
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.MessageModel;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.RabbitMQ;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Services.ServicesExtension;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.AliLog;
using DianGuanJiaApp.Utility.Model.AliLog;
using Microsoft.Win32;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DianGuanJiaApp.Message.Consumer.Services;
using DianGuanJiaApp.Trace.ViewModels.Models;
using ReturnedModel = DianGuanJiaApp.ViewModels.Models.ReturnedModel;
using ReturnedStatus = DianGuanJiaApp.ViewModels.Models.ReturnedStatus;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Services.Services.DataDuplication;
using System.Net.Sockets;
using System.Security.Policy;
using DianGuanJiaApp.Utility.Web;
using System.Text;
using RabbitMQ.Client.Framing.Impl;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Data.FxModel;
using DianGuanJiaApp.Winform.Base;
using System.Security.Cryptography;
using DianGuanJiaApp.Data.Repository.BaseProduct;
using DianGuanJiaApp.Services.BaseProduct;
using System.Xml.Linq;
using DianGuanJiaApp.Data.Entity.BaseProduct;
using DianGuanJiaApp.Services.Services.ProfitStatistics;
using DianGuanJiaApp.Data.Entity.ProfitStatistics;
using System.Reflection;
using System.Threading;
using System.Linq.Expressions;
using DianGuanJiaApp.Services.Services.SupplierProduct;
using DianGuanJiaApp.Utility.JD.JCQ;
using DianGuanJiaApp.Message.Consumer.Model;
using System.Net;
using System.IO;
using System.Threading;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
//using DianGuanJiaApp.Winform.Base;

namespace DianGuanJiaApp.Message.Consumer
{
    /// <summary>
    /// 同步订单窗体
    /// </summary>
    public partial class MessageConsumer : BaseForm
    {
        /// <summary>
        /// 程序的业务名称
        /// </summary>
        protected override string FormName { get => "【分单】" + this.Text; }
        /// <summary>
        /// 程序运行的最小个数。用于心跳检测
        /// </summary>
        protected override int RunMinQuantity { get => 0; }

        #region 初始化

        public MessageConsumer()
        {
            InitializeComponent();
            if (DebugModeChk.Checked)
                OpenDebugMode();
            App_Start.DapperMapping.SetEntityMapping();
            System.Net.ServicePointManager.DefaultConnectionLimit = 512;
            if (CustomerConfig.MessageConsumerDefaultThreadCount > 0)
                textThreadCount.Text = CustomerConfig.MessageConsumerDefaultThreadCount.ToString();
            //startSyncTime.Text = DateTime.Now.AddMonths(-3).ToString("yyyy-MM-dd");
            //Redis 初始化
            RedisConfigService.Initialization();
        }

        #endregion

        #region 服务层相关变量

        private ShopService _shopService = new ShopService();

        private TouTiaoMessageProcessor _touTiaoMessageProcessor = new TouTiaoMessageProcessor();

        private FxTouTiaoMessageProcessor _fxTouTiaoMessageProcessor = new FxTouTiaoMessageProcessor();

        private FxHotPrdouctMessageProcessor _fxHotPrdouctMessageProcessor = new FxHotPrdouctMessageProcessor();
        private FxTaoBaoMaiCaiMessageProcessor _fxTaoBaoMaiCaiMessageProcessor = new FxTaoBaoMaiCaiMessageProcessor();
        #endregion

        #region 开始任务

        /// <summary>
        /// 任务是否停止
        /// </summary>
        private static bool isStoped = false;

        /// <summary>
        /// 更新同步状态
        /// </summary>
        /// <param name="allCount"></param>
        /// <param name="syncCount"></param>
        delegate void UpdateUI(int allCount);

        private List<MessageDescription> _messageWorkerDescriptions = new List<MessageDescription>();
        private int yzOrderMessageCount = 0;
        private int yzProductMessageCount = 0;
        private int aliOrderMessageCount = 0;
        private int aliQingOrderMessageCount = 0;
        private int aliProductMessageCount = 0;
        private int aliC2mOrderMessageCount = 0;
        private int aliC2mProductMessageCount = 0;
        private int kuaiShouOrderMessageCount = 0;
        private int kuaiShouOrderAddressChangeMessageCount = 0;
        private int kuaiShouSupplierOrderMessageCount = 0;
        private int kuaiShouProductMessageCount = 0;
        private int touTiaoOrderMessageCount = 0;
        private int touTiaoQingOrderMessageCount = 0;
        private int touTiaoOrderTagMessageCount = 0;
        private int touTiaoProductMessageCount = 0;
        private int traceMessageCount = 0;
        private int tuanHaoHuoOrderMessageCount = 0;
        private int touTiaoNoSendRefundMessageCount = 0;
        private int fxDbExecuteLogMessageCount = 0;
        private int dbExecuteLogMessageCount = 0;
        private int wxvideoOrderExtMessageCount = 0;
        private int wxvideoOrderFxExtMessageCount = 0;
        private int xiaoHongShuOrderMessageCount = 0;
        private int xiaoHongShuProductMessageCount = 0;
        private int xiaoHongShuFxOrderMessageCount = 0;
        private int xiaoHongShuFxProductMessageCount = 0;
        private int updatePurchaseStatusMessageCount = 0;
        private int aliFxOrderMessageCount = 0;
        private int taobaoOrderMessageCount = 0;
        private int taobaoFxOrderMessageCount = 0;
        private int aliFxProductMessageCount = 0;
        private int aliBuyerFxOrderMessageCount = 0;
        private int aliFxAutoAfterSaleCount = 0;
        private int fxSendHistoryReturnCount = 0;
        private int aliFxBatchFundChargeCount = 0;
        private int updatePurchaseSettlementPriceCount = 0;
        private int aliFxRefundChargeCount = 0;
        private int fxBaseProductCount = 0;
        private int afterSaleOrderStockCount = 0;
        private int fxProfitStatisticsCount = 0;
        private int fxHotPrdouctCount = 0;
        private int fxHotPrdouctSummaryCount = 0;
        private int taoBaoMaiCaiOrderMessageCount = 0;
        private int heliangFxOrderMessageCount = 0;
        private int fxShopAuthCount = 0;
        private int fxListingCompleteCount = 0;
        private int biliBiliOrderMessageCount = 0;

        private DateTime _lastSendMessageTime = DateTime.Now.AddSeconds(-90);
        private Object _locker = new object();
        private void SetStatusText()
        {
            var text1 = $"1688待处理，订单消息：{this.aliOrderMessageCount} ，产品消息：{this.aliProductMessageCount}\r\n";
            var text2 = $"有赞待处理，订单消息：{this.yzOrderMessageCount} ，产品消息：{this.yzProductMessageCount}\r\n";
            var text3 = $"C2M待处理，订单消息：{this.aliC2mOrderMessageCount} ，产品消息：{this.aliC2mProductMessageCount}\r\n";
            var text4 = $"快手待处理，订单消息：{this.kuaiShouOrderMessageCount} ，产品消息：{this.kuaiShouProductMessageCount}，地址变更消息：{this.kuaiShouOrderAddressChangeMessageCount}\r\n";
            var text5 = $"头条待处理，订单消息：{this.touTiaoOrderMessageCount} ，标签消息：{this.touTiaoOrderTagMessageCount} ，产品消息：{this.touTiaoProductMessageCount}\r\n";
            var text6 = $"物流轨迹消息：{this.traceMessageCount}\r\n";
            var text7 = $"团好货待处理，订单消息：{this.tuanHaoHuoOrderMessageCount}\r\n";
            var text8 = $"分单数据库执行日志消息：{this.fxDbExecuteLogMessageCount}\r\n";
            var text9 = $"打单数据库执行日志消息：{this.dbExecuteLogMessageCount}\r\n";
            var text10 = $"快手厂商待处理，订单消息：{this.kuaiShouSupplierOrderMessageCount}\r\n";
            var text11 = $"微信视频号待处理，订单消息：{this.wxvideoOrderExtMessageCount}\r\n";
            var text12 = $"头条待处理，轻应用支付订单消息：{this.touTiaoQingOrderMessageCount}\r\n";
            var textTb = $"淘宝待处理，订单消息：{this.taobaoOrderMessageCount}，Fx订单消息：{this.taobaoFxOrderMessageCount}\r\n";
            var text13 = $"1688待处理，轻应用支付订单消息：{this.aliQingOrderMessageCount}\r\n";
            var text14 = $"1688更新采购单关系状态消息：{this.updatePurchaseStatusMessageCount}，买家消息：{this.aliBuyerFxOrderMessageCount}，自动回流：{this.fxSendHistoryReturnCount}\r\n";
            var text15 = $"1688分单待处理，订单消息：{this.aliFxOrderMessageCount} ，产品消息：{this.aliFxProductMessageCount}，自动售后：{this.aliFxAutoAfterSaleCount}，产品采购结算价：{this.updatePurchaseSettlementPriceCount}\r\n";
            var text16 = $"1688分单采购金，预充值支付：{this.aliFxBatchFundChargeCount} ，预充值退款：{this.aliFxRefundChargeCount}\r\n";
            var text17 = $"基础商品：{this.fxBaseProductCount}，售后入库：{this.afterSaleOrderStockCount},店铺授权：{fxShopAuthCount}，铺货任务：{fxListingCompleteCount} \r\n";
            var text18 = $"爆品分析：{this.fxHotPrdouctCount} , 汇总：{this.fxHotPrdouctSummaryCount} \r\n";
            var textPs = $"利润统计：{this.fxProfitStatisticsCount} \r\n";
            var text19 = $"淘宝买菜待处理，订单消息：{this.taoBaoMaiCaiOrderMessageCount}\r\n";
            var text20 = $"其他平台（禾量）：{this.heliangFxOrderMessageCount} \r\n";
            var text21 = $"哔哩哔哩待处理，订单消息：{this.biliBiliOrderMessageCount}\r\n";
            var text22 = $"微信视频号分单待处理，订单消息：{this.wxvideoOrderFxExtMessageCount}\r\n";

            var maxQueueCount = CustomerConfig.MaxMessageQueueWarningCount;
            if (true)
            {
                var seconds = (DateTime.Now - _lastSendMessageTime).TotalSeconds;
                if (seconds >= 30)
                {

                    try
                    {
                        if ((DateTime.Now - _lastSendMessageTime).TotalSeconds >= 30)
                        {
                            DianGuanJiaApp.Utility.Net.HttpUtility.PostToDingDing($"消息队列消息堆积超过{maxQueueCount}条，详细：\r\n{text1}{text2}{text3}{text4}{text5}{text7}{text8}{text10}{text11}{text12}{text13}{text14}{text15}{text16}{text17}{text18}{text19}{text20}{textPs}{text21}{text22}", new List<string> { "13065187972" });
                            lock (_locker)
                            {
                                _lastSendMessageTime = DateTime.Now;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"发送消息到钉钉时发生错误：{ex}");
                    }
                }
                if (seconds > 1)
                {
                    statusTextBox.Text = text1 + text2 + text3 + text4 + text5 + text6 + text7 + text8 + text9 + text10 + text11 + text12 + textTb + text13 + text14 + text15 + text16 + text17 + text18+ text19+ text20+ textPs+ text21+ text22;
                    _lastSendMessageTime = DateTime.Now;
                }
            }
        }
        /// <summary>
        /// 启动同步-按钮点击事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void startBtn_Click(object sender, EventArgs e)
        {
            Log.WriteLine("启动消息消费线程");
            isStoped = false;
            stopBtn.Enabled = true;
            var maxThreadCount = textThreadCount.Text.ToInt();
            if (maxThreadCount <= 0)
                maxThreadCount = 1;
            var needCreateCount = maxThreadCount - _messageWorkerDescriptions.Count() / 4;
            if (needCreateCount > 0)
            {
                if (CustomerConfig.CloudPlatformType.ToLower() == "jingdong")
                {
                    CreateNewMessageProcessorOfJingdong();
                }
                else
                {
                    for (int i = 0; i < needCreateCount; i++)
                    {
                        CreateNewMessageProcessorOfTouTiao(i);
                        //CreateLogisticMessageProcessor();
                    }
                    CreateNewMessageProcessorOfOther();
                }
            }
            else
            {
                //若是暂停状态，启动所有的线程
                _messageWorkerDescriptions.ForEach(desc =>
                {
                    desc.ResetEvent.Set();
                });
            }

            startBtn.Enabled = false;
            syncStatusLabel.Text = "消息处理中...";
        }

        private void ListenMessageV2(MessageDescription desc, Func<List<string>, int, bool> callback)
        {
            RabbitMQService.ListenMessageByMaintenanceTime(desc, (msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.touTiaoOrderMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return ProcessFxMessage(msgs, PlatformType.TouTiao.ToString());
            }, () =>
            {
                return new CommonSettingService().GetMaintenanceTime();
            });
        }

        private void CreateNewMessageProcessorOfTouTiao(int index)
        {
            //头条打印应用的消息
            var touTiaoOrderDesc = RabbitMQService.TouTiaoOrderMessageDescription;
            var touTiaoOrderTagDesc = RabbitMQService.TouTiaoOrderTagMessageDescription;
            var touTiaoNoSendRefundMessageDesc = RabbitMQService.TouTiaoNoSendRefundMessageDescription;
            //头条分单应用的消息
            var touTiaoFxOrderDesc = RabbitMQService.TouTiaoFxOrderMessageDescription;
            var touTiaoFxOrderTagDesc = RabbitMQService.TouTiaoFxOrderTagMessageDescription;

            //轻应用订单支付
            var touTiaoQingOrderDesc = RabbitMQService.TouTiaoQingOrderPayMessageDescription;

            //if (index % 2 == 0 || index % 3 == 0)
            //{

            //}
            #region 头条分单应用

            ListenMessageV2(touTiaoFxOrderDesc, (msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.touTiaoOrderMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return ProcessFxMessage(msgs, PlatformType.TouTiao.ToString());
            });
            _messageWorkerDescriptions.Add(touTiaoFxOrderDesc);

            ListenMessageV2(touTiaoFxOrderTagDesc, (msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.touTiaoOrderTagMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return ProcessFxMessage(msgs, PlatformType.TouTiao.ToString());
            });
            _messageWorkerDescriptions.Add(touTiaoFxOrderTagDesc);
            #endregion

            #region 头条打单应用

            ListenMessageV2(touTiaoNoSendRefundMessageDesc, (msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.touTiaoNoSendRefundMessageCount = a;
                    SetStatusText();
                }), leftCount);
                //抖店应用消息只能在抖店云内消费
                if (CustomerConfig.CloudPlatformType != "TouTiao")
                    return true;
                return ProcessMessage(msgs);
            });
            _messageWorkerDescriptions.Add(touTiaoNoSendRefundMessageDesc);

            ListenMessageV2(touTiaoOrderDesc, (msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.touTiaoOrderMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return ProcessMessage(msgs);
            });
            _messageWorkerDescriptions.Add(touTiaoOrderDesc);

            ListenMessageV2(touTiaoOrderTagDesc, (msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.touTiaoOrderTagMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return ProcessMessage(msgs);
            });
            _messageWorkerDescriptions.Add(touTiaoOrderTagDesc);

            ListenMessageV2(touTiaoQingOrderDesc, (msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.touTiaoQingOrderMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return ProcessFxMessage(msgs, "Alibaba");
            });
            _messageWorkerDescriptions.Add(touTiaoQingOrderDesc);
            #endregion

        }

        private void CreateNewMessageProcessorOfJingdong()
        {
            ThreadPool.QueueUserWorkItem(new WaitCallback(o =>
            {
                ProcessJingdongMessage();
            }));
        }

        private void ProcessJingdongMessage()
        {
            //处理京东的JCQ信息
            // 从配置文件读取信息
            var consumerGroupId = CustomerConfig.JcqConsumerGroupId;
            var topicName = CustomerConfig.JcqProductDarftInfoTopicName;
            // 拉取商品草稿变更消息
            JcqClientBuilder builder = new JcqClientBuilder();
            var client = builder.CreateDefault().Build();
            var errorCount = 0;
            while (true)
            {
                try
                {
                    var response = client.GetMessages(new Utility.JD.JCQ.Model.GetMessageJcqRequest()
                    {
                        Topic = topicName,
                        ConsumerGroupId = consumerGroupId,
                    });
                    errorCount = 0;
                    if (response == null || response.Result == null || response.Result.Messages == null || !response.Result.Messages.Any())
                    {
                        Thread.Sleep(100);
                        continue;
                    }

                    // 更新商品信息
                    var shopService = new ShopService();
                    var messageInfo = response.Result.Messages.First();
                    var body = messageInfo.GetMessage<ProductDraftInfo>();
                    if (body == null)
                    {
                        continue;
                    }
                    // jcqhttp协议消费京东云没有消费没有轨迹，我们自己记录日志
                    Log.WriteLine($"接收到消息:{messageInfo.MessageBody}");
                    if (body.AuditStatus != 1 && body.AuditStatus != 3)
                    {
                        //直接ack
                        doAck();
                        continue;
                    }
                    var shops = shopService.GetShopByVenderId(new List<string>() { body.VenderId }, CustomerConfig.CloudPlatformType);
                    if (shops == null || !shops.Any())
                    {
                        Log.WriteWarning($"没有找到店铺:{body.VenderId}");
                        doAck();
                        continue;
                    }
                    var shop = shops.First();
                    // 商品写入数据库
                    var userFxService = new UserFxService();
                    var userFxAndShopList = userFxService.GetUserFxAndShopsByShopIds(new List<string>() { shop.ShopId }, null, CustomerConfig.CloudPlatformType);

                    if (userFxAndShopList != null && userFxAndShopList.Any()&& userFxAndShopList.First().Item1!=null)
                    {
                        var fxUserId = userFxAndShopList.First().Item1.Id;
                        Log.WriteLine($"写入商品信息，店铺Id:{shop.ShopId},用户id：{fxUserId}");
                        var syncRes = BulkSyncFxProduct(userFxAndShopList, new List<PlatformMessageModel>() { new PlatformMessageModel() {
                                 MID=shop.ShopId,
                                 PID=body.ProductId.ToString()
                            }
                        });
                        if (!syncRes)
                        {
                            doAck(false);
                            continue;
                        }
                    }
                    else
                    {
                        Log.WriteWarning($"没有找到店铺和用户信息:{body.VenderId}");
                        doAck();
                        continue;
                    }

                    // ack
                    doAck();
                    void doAck(bool ackAction = true)
                    {
                        if (response != null && response.Result != null && !response.Result.AckIndex.IsNullOrEmpty())
                        {
                            var ackRes = client.AckMessage(new Utility.JD.JCQ.Model.AckMessageRequest()
                            {
                                AckIndex = response.Result.AckIndex,
                                ConsumerGroupId = consumerGroupId,
                                Topic = topicName,
                                AckAction = ackAction ? "SUCCESS" : "CONSUME_FAILED"
                            });
                        }
                    }
                }
                catch (WebException ex)
                {
                    errorCount++;
                    var response = ex.Response.GetResponseStream();
                    if (response != null)
                    {
                        StreamReader reader = new StreamReader(response);
                        var res = reader.ReadToEnd();
                        Log.WriteLine(res + "\r\n" + ex.Message);
                    }
                    Thread.Sleep(errorCount % 10 * 1000);
                }
                catch (Exception ex)
                {
                    errorCount++;
                    Log.WriteError(ex.Message);
                    Thread.Sleep(errorCount % 10 * 1000);
                }
                if (errorCount > 100)
                {
                    Log.WriteError("异常次数过多，退出处理");
                    return;
                }
            }
        }

        private void CreateNewMessageProcessorOfOther()
        {
            var aliOrderDesc = RabbitMQService.AlibabaOrderMessageDescription;
            var aliQingOrderDesc = RabbitMQService.AlibabaQingOrderMessageDescription;
            var aliProductDesc = RabbitMQService.AlibabaProductMessageDescription;
            var yzOrderDesc = RabbitMQService.YouZanOrderMessageDescription;
            var yzProductDesc = RabbitMQService.YouZanProductMessageDescription;
            // 淘工厂打单应用的消息
            var aliC2mOrderDesc = RabbitMQService.AlibabaC2MOrderMessageDescription;
            var aliC2mProductDesc = RabbitMQService.AlibabaC2MProductMessageDescription;
            // 淘工厂分单应用的消息
            var aliC2mFxOrderDesc = RabbitMQService.AlibabaC2MFxOrderMessageDescription;
            //快手打单应用的消息
            var kuaiShouOrderDesc = RabbitMQService.KuaiShouOrderMessageDescription;
            var kuaiShouProductDesc = RabbitMQService.KuaiShouProductMessageDescription;
            var kuaiShouOrderAddressDesc = RabbitMQService.KuaiShouOrderAddressChangeMessageDescription;
            //快手分单管理应用 打单发货类目的消息
            var kuaiShouFxOrderDesc = RabbitMQService.KuaiShouFxOrderMessageDescription;
            var kuaiShouFxProductDesc = RabbitMQService.KuaiShouFxProductMessageDescription;
            var kuaiShouFxOrderAddressDesc = RabbitMQService.KuaiShouFxOrderAddressChangeMessageDescription;
            //快手分单代发应用 厂商代发的消息
            var kuaiShouNewFxOrderDesc = RabbitMQService.KuaiShouNewFxOrderMessageDescription;
            var kuaiShouNewFxProductDesc = RabbitMQService.KuaiShouNewFxProductMessageDescription;
            //快手打单应用-厂商消息
            var kuaiShouSupllierOrderDesc = RabbitMQService.KuaiShouSupplierOrderMessageDescription;

            var tuanhaohuoOrderDesc = RabbitMQService.TuanHaoHuoOrderMessageDescription;
            //头条打印应用的消息
            var touTiaoProductDesc = RabbitMQService.TouTiaoProductMessageDescription;
            var touTiaoModifyReceiverDesc = RabbitMQService.TouTiaoModifyReceiverMessageDescription;
            //头条分单应用的消息
            var touTiaoFxProductDesc = RabbitMQService.TouTiaoFxProductMessageDescription;
            var touTiaoFxModifyReceiverDesc = RabbitMQService.TouTiaoFxModifyReceiverMessageDescription;
            //var touTiaoFxNoSendRefundMessageDesc = RabbitMQService.TouTiaoFxNoSendRefundMessageDescription;

            //分单应用“数据执行”日志
            var fxDbExecuteLogMessageDesc = RabbitMQService.FxDbExecuteLogMessageDescription;
            //打单应用“数据执行”日志
            var dbExecuteLogMessageDesc = RabbitMQService.DbExecuteLogMessageDescription;

            //微信视频号
            var wxvideoOrderExtDesc = RabbitMQService.WxVideoOrderextMessageDescription;
            var wxvideoOrderFxExtDesc = RabbitMQService.WxVideoOrderextFxMessageDescription;

            //小红书打单应用的消息
            var xiaoHongShuOrderDesc = RabbitMQService.XiaoHongShuOrderMessageDescription;
            var xiaoHongShuProductDesc = RabbitMQService.XiaoHongShuProductMessageDescription;

            //小红书分单应用的消息
            var xiaoHongShuFxOrderDesc = RabbitMQService.XiaoHongShuFxOrderMessageDescription;
            var xiaoHongShuFxProductDesc = RabbitMQService.XiaoHongShuFxProductMessageDescription;

            //1688更新采购单关系状态消息
            var updatePurchaseStatusDesc = RabbitMQService.UpdatePurchaseStatusMessageDescription;

            //1688-分单系统
            var aliFxOrderDesc = RabbitMQService.AlibabaFxOrderMessageDescription;
            var aliFxProductDesc = RabbitMQService.AlibabaFxProductMessageDescription;
            var aliBuyerFxOrderDesc = RabbitMQService.AlibabaBuyerFxOrderMessageDescription;
            var aliFxAutoAfterSaleDesc = RabbitMQService.AlibabaFxAutoAfterSaleMessageDescription;
            var fxSendHistoryReturnDesc = RabbitMQService.FxSendHistoryReturnMessageDescription;
            var updatePurchaseSettlementPriceDesc = RabbitMQService.UpdatePurchaseSettlementPriceMessageDescription;

            //1688-采购金
            var aliFxBatchFundChargeDesc = RabbitMQService.AlibabaFxBatchFundChargeMessageDescription;
            var aliFxRefundChargeDesc = RabbitMQService.AlibabaFxRefundChargeMessageDescription;

            //基础商品
            var fxBasePrdouctDesc = RabbitMQService.FxBaseProductMessageDescription;

            //分单-售后自动入库
            var afterSaleOrderStockDesc = RabbitMQService.AfterSaleOrderStockMessageDescription;
            
            //利润统计
            var fxProfitStatisticsDesc = RabbitMQService.FxProfitStatisticsMessageDescription;

            //店铺授权
            var fxShopAuthDesc = RabbitMQService.FxShopAuthMessageDescription;
            //铺货任务完成
            var fxListingCompleteDesc = RabbitMQService.FxListingCompleteDescription;

            //淘宝打单应用的消息
            var taobaoOrderDesc = RabbitMQService.TaobaoOrderMessageDescription;
            //淘宝分销系统订单的消息
            var taobaoFxOrderDesc = RabbitMQService.TaobaoFxOrderMessageDescription;
            //淘宝买菜应用消息
            var taobaomaicaiOrderMessageDesc = RabbitMQService.TaobaoMaiCaiOrderMessageDescription;
            //禾量分销系统订单的消息
            var heliangFxOrderDesc = RabbitMQService.HeliangOrderMessageDescription;
            //哔哩哔哩分销系统订单的消息
            var biliBiliFxOrderDesc = RabbitMQService.BiliBiliFxOrderMessageDescription;
            //爆品分析
            var fxHotPrdouctDesc = RabbitMQService.FxHotProductMessageDescription;
            var fxHotProductSummaryDesc = RabbitMQService.FxHotProductSummaryMessageDescription;

            ListenMessageV2(aliProductDesc, (Func<List<string>, int, bool>)((msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.aliProductMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return this.ProcessMessage(msgs);
            }));
            ListenMessageV2(yzProductDesc, (Func<List<string>, int, bool>)((msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.yzProductMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return this.ProcessMessage(msgs);
            }));
            ListenMessageV2(aliC2mProductDesc, (Func<List<string>, int, bool>)((msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.aliC2mProductMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return this.ProcessMessage(msgs);
            }));
            //快手-商品消息-打单应用
            ListenMessageV2(kuaiShouProductDesc, (Func<List<string>, int, bool>)((msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.kuaiShouProductMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return this.ProcessMessage(msgs);
            }));
            //快手-商品消息-分销管理应用
            ListenMessageV2(kuaiShouFxProductDesc, (Func<List<string>, int, bool>)((msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.kuaiShouProductMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return this.ProcessFxMessage(msgs, PlatformType.KuaiShou.ToString());
            }));
            //快手-商品消息-分销代发应用
            ListenMessageV2(kuaiShouNewFxProductDesc, (Func<List<string>, int, bool>)((msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.kuaiShouProductMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return this.ProcessFxMessage(msgs, PlatformType.KuaiShou.ToString());
            }));

            //头条-打单
            ListenMessageV2(touTiaoProductDesc, (Func<List<string>, int, bool>)((msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.touTiaoProductMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return this.ProcessMessage(msgs);
            }));
            ListenMessageV2(touTiaoModifyReceiverDesc, (msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.touTiaoOrderMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return ProcessMessage(msgs);
            });

            //头条-分单
            ListenMessageV2(touTiaoFxProductDesc, (Func<List<string>, int, bool>)((msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.touTiaoProductMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return this.ProcessFxMessage(msgs, PlatformType.TouTiao.ToString());
            }));
            ListenMessageV2(touTiaoFxModifyReceiverDesc, (msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.touTiaoOrderMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return ProcessFxMessage(msgs, PlatformType.TouTiao.ToString());
            });
            //ListenMessageV2(touTiaoFxNoSendRefundMessageDesc, (msgs, leftCount) =>
            //{
            //    this.Invoke(new UpdateUI(delegate (int a)
            //    {
            //        this.touTiaoNoSendRefundMessageCount = a;
            //        SetStatusText();
            //    }), leftCount);
            //    return ProcessFxMessage(msgs);
            //});

            //分单应用“数据库执行”日志
            ListenMessageV2(fxDbExecuteLogMessageDesc, (msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.fxDbExecuteLogMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return ProcessFxAliLogMessage(msgs);
            });

            //打单应用“数据库执行”日志
            ListenMessageV2(dbExecuteLogMessageDesc, (msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.dbExecuteLogMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return ProcessFxAliLogMessage(msgs);
            });

            //小红书-商品消息-打单应用
            ListenMessageV2(xiaoHongShuProductDesc, (Func<List<string>, int, bool>)((msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.xiaoHongShuProductMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return this.ProcessMessage(msgs);
            }));

            //小红书-商品消息-分单应用
            ListenMessageV2(xiaoHongShuFxProductDesc, (Func<List<string>, int, bool>)((msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.xiaoHongShuFxProductMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return this.ProcessFxMessage(msgs, PlatformType.XiaoHongShu.ToString());
            }));
            

            _messageWorkerDescriptions.Add(aliProductDesc);
            _messageWorkerDescriptions.Add(yzProductDesc);
            _messageWorkerDescriptions.Add(aliC2mProductDesc);
            _messageWorkerDescriptions.Add(kuaiShouProductDesc);
            _messageWorkerDescriptions.Add(kuaiShouOrderAddressDesc);
            _messageWorkerDescriptions.Add(kuaiShouFxOrderAddressDesc);
            _messageWorkerDescriptions.Add(kuaiShouFxProductDesc);

            _messageWorkerDescriptions.Add(touTiaoProductDesc);
            _messageWorkerDescriptions.Add(touTiaoModifyReceiverDesc);

            _messageWorkerDescriptions.Add(touTiaoFxProductDesc);
            _messageWorkerDescriptions.Add(touTiaoFxModifyReceiverDesc);
            _messageWorkerDescriptions.Add(fxDbExecuteLogMessageDesc);
            _messageWorkerDescriptions.Add(dbExecuteLogMessageDesc);
            //_messageWorkerDescriptions.Add(touTiaoFxNoSendRefundMessageDesc);

            _messageWorkerDescriptions.Add(xiaoHongShuProductDesc);
            _messageWorkerDescriptions.Add(xiaoHongShuFxProductDesc);


            for (int i = 0; i < 3; i++)
            {
                ListenMessageV2(aliOrderDesc, (msgs, leftCount) =>
                {
                    this.Invoke(new UpdateUI(delegate (int a)
                    {
                        this.aliOrderMessageCount = a;
                        SetStatusText();
                    }), leftCount);
                    return ProcessMessage(msgs);
                });
            }
            ListenMessageV2(aliQingOrderDesc, (msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.aliQingOrderMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return ProcessFxMessage(msgs, "Alibaba");
            });

            ListenMessageV2(yzOrderDesc, (msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.yzOrderMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return ProcessMessage(msgs);
            });

            // 淘工厂打单应用
            ListenMessageV2(aliC2mOrderDesc, (msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.aliC2mOrderMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return ProcessMessage(msgs);
            });
            
            // 淘工厂分单应用
            ListenMessageV2(aliC2mFxOrderDesc, (msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.aliC2mOrderMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return ProcessFxMessage(msgs, nameof(PlatformType.AlibabaC2M));
            });
            //快手-厂商订单消息-打单应用
            ListenMessageV2(kuaiShouSupllierOrderDesc, (msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.kuaiShouSupplierOrderMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return ProcessMessage(msgs);
            });
            //快手-订单消息-打单应用
            ListenMessageV2(kuaiShouOrderDesc, (msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.kuaiShouOrderMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return ProcessMessage(msgs);
            });
            //快手-订单消息-打单应用
            ListenMessageV2(kuaiShouOrderDesc, (msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.kuaiShouOrderMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return ProcessMessage(msgs);
            });
            //快手-订单消息-打单应用
            ListenMessageV2(kuaiShouOrderDesc, (msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.kuaiShouOrderMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return ProcessMessage(msgs);
            });
            //快手-订单地址变更消息-打单应用
            ListenMessageV2(kuaiShouOrderAddressDesc, (msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.kuaiShouOrderAddressChangeMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return ProcessMessage(msgs);
            });
            //快手-订单地址变更消息-分单应用
            ListenMessageV2(kuaiShouFxOrderAddressDesc, (msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.kuaiShouOrderAddressChangeMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return ProcessFxMessage(msgs, PlatformType.KuaiShou.ToString());
            });
            //快手-订单消息-分单管理应用
            ListenMessageV2(kuaiShouFxOrderDesc, (msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.kuaiShouOrderMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return ProcessFxMessage(msgs, PlatformType.KuaiShou.ToString());
            });
            //快手-订单消息-分单代发应用
            ListenMessageV2(kuaiShouNewFxOrderDesc, (msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.kuaiShouOrderMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return ProcessFxMessage(msgs, PlatformType.KuaiShou.ToString());
            });

            //快团团 订单消息
            ListenMessageV2(tuanhaohuoOrderDesc, (msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.tuanHaoHuoOrderMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return ProcessMessage(msgs);
            });
            ListenMessageV2(wxvideoOrderExtDesc, (msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.wxvideoOrderExtMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return ProcessMessage(msgs);
            });

            // 微信小店-订单消息-分单
            ListenMessageV2(wxvideoOrderFxExtDesc, (msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.wxvideoOrderFxExtMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return ProcessFxMessage(msgs, PlatformType.WxVideo.ToString());
            });
            
            //小红书-订单消息-打单应用
            ListenMessageV2(xiaoHongShuOrderDesc, (msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.xiaoHongShuOrderMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return ProcessMessage(msgs);
            });

            //小红书-订单消息-分单应用
            ListenMessageV2(xiaoHongShuFxOrderDesc, (msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.xiaoHongShuFxOrderMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return ProcessFxMessage(msgs, PlatformType.XiaoHongShu.ToString());
            });

            //淘宝-订单消息
            ListenMessageV2(taobaoOrderDesc, (msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.taobaoOrderMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return ProcessMessage(msgs);
            });

            //淘宝-分销系统订单消息
            ListenMessageV2(taobaoFxOrderDesc, (msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.taobaoFxOrderMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return ProcessFxMessage(msgs, PlatformType.Taobao.ToString());
            });


            //1688更新采购单关系状态消息（分单系统）
            ListenMessageV2(updatePurchaseStatusDesc, (Func<List<string>, int, bool>)((msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.updatePurchaseStatusMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return this.ProcessFxPurchaseMessage(msgs);
            }));
            ListenMessageV2(aliFxOrderDesc, (Func<List<string>, int, bool>)((msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.aliFxOrderMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return this.ProcessFxMessage(msgs, PlatformType.Alibaba.ToString());
            }));
            ListenMessageV2(aliFxProductDesc, (Func<List<string>, int, bool>)((msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.aliFxProductMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return this.ProcessFxMessage(msgs, PlatformType.Alibaba.ToString());
            }));
            ListenMessageV2(aliBuyerFxOrderDesc, (Func<List<string>, int, bool>)((msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.aliBuyerFxOrderMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return this.ProcessBuyerFxMessage(msgs, PlatformType.Alibaba.ToString());
            }));

            ListenMessageV2(aliFxAutoAfterSaleDesc, (Func<List<string>, int, bool>)((msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.aliFxAutoAfterSaleCount = a;
                    SetStatusText();
                }), leftCount);
                return this.ProcessFxAutoAfterSaleMessage(msgs);
            }));

            //自动回流处理
            ListenMessageV2(fxSendHistoryReturnDesc, (Func<List<string>, int, bool>)((msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.fxSendHistoryReturnCount = a;
                    SetStatusText();
                }), leftCount);
                return this.ProcessFxSendHistoryReturnMessage(msgs);
            }));

            //采购金-预充值支付
            ListenMessageV2(aliFxBatchFundChargeDesc, (Func<List<string>, int, bool>)((msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.aliFxBatchFundChargeCount = a;
                    SetStatusText();
                }), leftCount);
                return this.ProcessFxBatchFundChargeMessage(msgs);
            }));

            //采购金-预充值退款
            ListenMessageV2(aliFxBatchFundChargeDesc, (Func<List<string>, int, bool>)((msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.aliFxRefundChargeCount = a;
                    SetStatusText();
                }), leftCount);
                return this.ProcessFxBatchFundChargeMessage(msgs);
            }));


            //更新采购结算价
            ListenMessageV2(updatePurchaseSettlementPriceDesc, (Func<List<string>, int, bool>)((msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.updatePurchaseSettlementPriceCount = a;
                    SetStatusText();
                }), leftCount);
                return this.ProcessFxUpdatePurchaseSettlementPriceMessage(msgs);
            }));


            //基础商品
            ListenMessageV2(fxBasePrdouctDesc, (Func<List<string>, int, bool>)((msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.fxBaseProductCount = a;
                    SetStatusText();
                }), leftCount);
                return this.ProcessFxBaseProductMessage(msgs);
            }));
            //淘宝买菜订单消息
            ListenMessageV2(taobaomaicaiOrderMessageDesc, (Func<List<string>, int, bool>)((msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.taoBaoMaiCaiOrderMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return _fxTaoBaoMaiCaiMessageProcessor.ProcessTaoBaoMaiCaiMessage(msgs);
            }));
            //禾量-分销系统订单消息
            RabbitMQService.ListenMessage(heliangFxOrderDesc, (msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.heliangFxOrderMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return ProcessFxMessage(msgs, PlatformType.Other_Heliang.ToString());
            });
            //哔哩哔哩-分销系统订单消息
            RabbitMQService.ListenMessage(biliBiliFxOrderDesc, (msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.biliBiliOrderMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return ProcessFxMessage(msgs, PlatformType.BiliBili.ToString());
            });
            //利润统计
            RabbitMQService.ListenMessage(fxProfitStatisticsDesc, (Func<List<string>, int, bool>)((msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.fxProfitStatisticsCount = a;
                    SetStatusText();
                }), leftCount);
                return this.ProcessFxCloudMessage<ProfitStatisticsCloudMessage>(msgs, typeof(ProfitStatisticsCloudMessageService));
            }));

            //店铺授权
            RabbitMQService.ListenMessage(fxShopAuthDesc, (Func<List<string>, int, bool>)((msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.fxShopAuthCount = a;
                    SetStatusText();
                }), leftCount);
                return this.ProcessFxShopAuthMessage(msgs);
            }));

            for (int i = 0; i < 3; i++)
            {
                //分单-售后自动入库
                RabbitMQService.ListenMessage(afterSaleOrderStockDesc, (Func<List<string>, int, bool>)((msgs, leftCount) =>
                {
                    this.Invoke(new UpdateUI(delegate (int a)
                    {
                        this.afterSaleOrderStockCount = a;
                        SetStatusText();
                    }), leftCount);
                    return this.ProcessFxAfterSaleStockMessage(msgs);
                }));
            }

            //铺货任务完成
            RabbitMQService.ListenMessage(fxListingCompleteDesc, (Func<List<string>, int, bool>)((msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.fxListingCompleteCount = a;
                    SetStatusText();
                }), leftCount);
                return this.ProcessFxListingCompleteMessage(msgs);
            }));

            for (int i = 0; i < 2; i++)
            {
                //爆品分析
                RabbitMQService.ListenMessage(fxHotPrdouctDesc, (Func<List<string>, int, bool>)((msgs, leftCount) =>
                {
                    this.Invoke(new UpdateUI(delegate (int a)
                    {
                        this.fxHotPrdouctCount = a;
                        SetStatusText();
                    }), leftCount);
                    return _fxHotPrdouctMessageProcessor.FxProcessHotPrdouctMessage(msgs);
                }));

            }
            RabbitMQService.ListenMessage(fxHotProductSummaryDesc, (Func<List<string>, int, bool>)((msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.fxHotPrdouctSummaryCount = a;
                    SetStatusText();
                }), leftCount);
                return _fxHotPrdouctMessageProcessor.FxProcessHotPrdouctSummaryMessage(msgs);
            }));
            _messageWorkerDescriptions.Add(aliOrderDesc);

            _messageWorkerDescriptions.Add(aliQingOrderDesc);

            _messageWorkerDescriptions.Add(yzOrderDesc);

            _messageWorkerDescriptions.Add(aliC2mOrderDesc);
            
            _messageWorkerDescriptions.Add(aliC2mFxOrderDesc);

            _messageWorkerDescriptions.Add(kuaiShouOrderDesc);

            _messageWorkerDescriptions.Add(kuaiShouSupllierOrderDesc);

            _messageWorkerDescriptions.Add(tuanhaohuoOrderDesc);

            _messageWorkerDescriptions.Add(wxvideoOrderExtDesc);

            _messageWorkerDescriptions.Add(xiaoHongShuOrderDesc);

            _messageWorkerDescriptions.Add(xiaoHongShuFxOrderDesc);

            _messageWorkerDescriptions.Add(updatePurchaseStatusDesc);
            _messageWorkerDescriptions.Add(aliFxOrderDesc);
            _messageWorkerDescriptions.Add(taobaoOrderDesc);
            _messageWorkerDescriptions.Add(taobaoFxOrderDesc);
            _messageWorkerDescriptions.Add(aliFxProductDesc);
            _messageWorkerDescriptions.Add(aliBuyerFxOrderDesc);
            _messageWorkerDescriptions.Add(aliFxAutoAfterSaleDesc);
            _messageWorkerDescriptions.Add(fxBasePrdouctDesc);
            _messageWorkerDescriptions.Add(afterSaleOrderStockDesc);
            _messageWorkerDescriptions.Add(fxProfitStatisticsDesc);
            _messageWorkerDescriptions.Add(fxHotPrdouctDesc);
            _messageWorkerDescriptions.Add(taobaomaicaiOrderMessageDesc);
            _messageWorkerDescriptions.Add(biliBiliFxOrderDesc);
            _messageWorkerDescriptions.Add(fxShopAuthDesc);
            _messageWorkerDescriptions.Add(fxListingCompleteDesc);
        }


        private void CreateLogisticMessageProcessor()
        {
            var logisticTraceDesc = RabbitMQService.LogisticCenterPushTracesMessageDescription;

            ListenMessageV2(logisticTraceDesc, (Func<List<string>, int, bool>)((msgs, leftCount) =>
            {
                this.Invoke(new UpdateUI(delegate (int a)
                {
                    this.traceMessageCount = a;
                    SetStatusText();
                }), leftCount);
                return this.ProcessLogisticMessage(msgs);
            }));
            _messageWorkerDescriptions.Add(logisticTraceDesc);
        }

        /// <summary>
        /// 处理物流中心推送的消息
        /// </summary>
        /// <param name="msgs"></param>
        /// <returns></returns>
        private bool ProcessLogisticMessage(List<string> msgs)
        {
            var result = true;
            try
            {
                var models = msgs?.Distinct().Select(msg => msg.ToObject<PlatformMessageModel>()).ToList();
                if (models == null || !models.Any())
                    return true;
                //去重
                var dict = new Dictionary<string, PlatformMessageModel>();
                models.ForEach(m =>
                {
                    if (!dict.ContainsKey(m.Key))
                        dict.Add(m.Key, m);
                });
                models = dict.Values.ToList();
                foreach (var item in models)
                {
                    var requestData = item.Content.ToObject<List<LogisticCodeQueryResponse>>();
                    var shopId = requestData.Select(m => m.State).FirstOrDefault().ToInt();
                    var context = new SiteContext(shopId, new SiteContextConfig { NeedRelationShops = false, NeedShopExpireTime = false, NeedLoadFxUser = false });
                    (new OrderLogisticInfoService()).UpdateLogisticTraces(requestData);
                }
            }
            catch (Exception ex)
            {
                result = false;
                Log.WriteError($"更新物流信息出现异常：{ex}");
            }
            return result;
        }

        /// <summary>
        /// 停止同步-按钮点击事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void stopBtn_Click(object sender, EventArgs e)
        {
            Log.WriteLine("暂停消息处理");
            isStoped = true;
            startBtn.Enabled = true;
            _messageWorkerDescriptions.ForEach(desc =>
            {
                desc.ResetEvent.Reset();
            });
            stopBtn.Enabled = false;
            syncStatusLabel.Text = "已暂停";
        }

        /// <summary>
        /// 批量同步商品信息
        /// </summary>
        /// <param name="msgs"></param>
        /// <returns></returns>
        public bool ProcessMessage(List<string> msgs)
        {
            try
            {
                var models = msgs?.Distinct().Select(msg => msg.ToObject<PlatformMessageModel>()).ToList();
                if (models == null || !models.Any())
                    return true;
                //去重
                var dict = new Dictionary<string, PlatformMessageModel>();
                models.ForEach(m =>
                {
                    if (!dict.ContainsKey(m.Key))
                        dict.Add(m.Key, m);
                });
                models = dict.Values.ToList();
                var first = models.FirstOrDefault();
                var pt = first.PT;
                var msgType = string.IsNullOrEmpty(first.PID) ? "订单" : "产品";//产品ID为空则是订单消息
                var shopService = new ShopService();
                var mids = models.Select(m => m.MID).Distinct()?.Where(mid => !string.IsNullOrEmpty(mid))?.ToList();
                if (mids == null || !mids.Any())
                    return true;

                //抖店应用消息只能在抖店云内消费
                if (CustomerConfig.CloudPlatformType != "TouTiao" && pt == "TouTiao")
                    return true;

                var shops = shopService.GetShopWithDbConfig(mids, pt);
                //Log.WriteLine($"处理{first.PT}{msgType}消息,ID：{string.Join(",", models.Select(x => x.PID + x.TID))} 店铺ID:{shops?.FirstOrDefault()?.Id} ShopId:{first.MID}");
                if (shops == null || shops.Any() == false)
                    return true;

                //判断消息对应的店铺数据库所在云平台是否和当前消费程序配置的云平台一致，不一致，消息丢弃
                var discardMessageModel = new List<PlatformMessageModel>(); //待废弃的消息
                foreach (var item in models)
                {
                    var shop = shops.FirstOrDefault(f => f.ShopId == item.MID);
                    if (shop == null) continue;
                    if (shop.DbConfig?.DbServer?.Location != CustomerConfig.CloudPlatformType)
                    {
                        discardMessageModel.Add(item); //加入废弃的消息集合
                                                       //记入日志
                        Log.WriteLine($"消息【{item.Key}】所属的店铺【{shop.Id},{shop.NickName},{shop.PlatformType}】数据库在【{shop.DbConfig?.DbServer?.Location}】，与当前消费程序所在云平台【{CustomerConfig.CloudPlatformType}】不一致，消息已丢弃", "DiscardedMessageLog.txt");
                    }
                }

                //丢弃消息
                if (discardMessageModel.Any())
                {
                    foreach (var item in discardMessageModel)
                    {
                        models.Remove(item);
                    }
                }

                var rst = true;
                //2023-03-07 头条灰度3用户的消息处理额外处理，没问题后，头条的消息都走这个独立的逻辑
                if (first.PT == PlatformType.TouTiao.ToString() && IsEnableNewConsumeLogic(shops.First().Version)) // == "3"
                {
                    //头条消息走新的消费逻辑的次数
                    try { RedisHelper.IncrBy($"TouTiaoMsgNewProcess:{DateTime.Now.ToString("yyyyMMdd")}"); } catch { }
                    rst = _touTiaoMessageProcessor.ProcessTouTiaoMessage(models, shops);
                    if (rst) //处理失败，还是走之前的逻辑处理
                        return rst;
                }

                if (first.PT == nameof(PlatformType.AlibabaC2M) && string.IsNullOrEmpty(first.AfterSaleId) == false)
                {
                    // 由于淘工厂目前只有打单应用，所以在打单消费处，收到了售后消息，走分单的逻辑先处理，再走打单的逻辑
                    var userFxService = new UserFxService();
                    var userFxAndShopList = userFxService.GetUserFxAndShopsByShopIds(mids, null, nameof(PlatformType.AlibabaC2M));  
                    BulkSyncFxOrder(userFxAndShopList, models, true);
                }
                if (first.State == "111")
                {
                    //头条修改地址消息额外处理
                    return (new TouTiaoOrderService()).ProcessModifyReceiverMessage(models, shops);
                }
                else if (first.State == "10003")
                {

                    //新疆中转 可能去标 ，去标后可以与普通订单合并 需走订单同步逻辑，更新相关合单关联计算-ExtField2
                    var logistics_transitModels = models.Where(x => x.Content.IndexOf("logistics_transit") > -1).ToList();
                    if (logistics_transitModels != null && logistics_transitModels.Any())
                    {
                        BulkSyncOrder(shops, logistics_transitModels, pt);
                    }
                    //头条订单标签消息处理
                    return (new TouTiaoOrderService()).ProcessAddOrderTagMessage(models, shops);
                }
                else if (first.State == "200" && first.AfterSaleType == "2" && !string.IsNullOrEmpty(first.AfterSaleId))
                {
                    //头条发货小助手消息额外处理
                    return (new TouTiaoOrderService()).SendCancelSendGoods(models, shops);
                }
                else if (msgType == "订单")
                    return BulkSyncOrder(shops, models, pt);
                else
                    return BulkSyncProduct(shops, models, pt);
            }
            catch (Exception ex)
            {
                Log.WriteError($"处理消息时发生错误，消息内容：{msgs?.ToJson()}，错误信息：{ex}");
            }
            return true;
        }

        /// <summary>
        /// 1688采购单订单支付成功，接受到成功支付消息后更改相应的状态
        /// </summary>
        /// <param name="platformMessageModels"></param>
        /// <returns></returns>
        private bool ProcessAlibabaQingPayStatus(List<PlatformMessageModel> platformMessageModels)
        {
            if (platformMessageModels.IsNullOrEmptyList())
            {
                return true;
            }

            foreach (var model in platformMessageModels)
            {
                var fxUserId = model.Content.ToInt();
                var pids = model.TID.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries).ToList();

                try
                {
                    SiteContext siteContext = new SiteContext(new UserFx() { Id = fxUserId }, new SiteContextConfig() { NeedRelationShops = false, NeedShopExpireTime = false, NeedLoadFxUser = false });
                    var purchaseService = new PurchaseOrderRelationService();
                    Log.Debug(() => $"ProcessAlibabaQingPayStatus,fxUserId={fxUserId},{purchaseService.baseRepository.DbConnection.Database},purchasePlatformOrderIds={pids.ToJson()}", $"1688-QingPay-{DateTime.Now.ToString("yyyyMMdd")}.txt");
                    purchaseService.UpdatePurchasePayOrderStatus(pids, fxUserId, true);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"1688采购单订单支付消息消费失败,fxUserId:{fxUserId},pids:{pids?.ToJson()},data:{platformMessageModels.ToJson()}\r\n:ex:{ex}", $"1688-QingPay-{DateTime.Now.ToString("yyyyMMdd")}.txt");
                }

            }

            return true;
        }

        /// <summary>
        /// 是否启用新的消息消费逻辑
        /// </summary>
        /// <returns></returns>
        private bool IsEnableNewConsumeLogic(string shopVersion)
        {
            //读取配置
            var key = "/System/Dadan/NewConsumeLogic";
            var enableNewConsumeLogicVersion = MemoryCacheHelper.GetCacheItem<string>(key);
            if (string.IsNullOrWhiteSpace(enableNewConsumeLogicVersion))
            {
                var _commonSettingService = new CommonSettingService();
                enableNewConsumeLogicVersion = _commonSettingService.GetString(key, 0);
                if (string.IsNullOrWhiteSpace(enableNewConsumeLogicVersion))
                    enableNewConsumeLogicVersion = "3"; //默认灰度3用户
                MemoryCacheHelper.Set(key, enableNewConsumeLogicVersion, 5 * 60); //缓存5分钟
            }
            return enableNewConsumeLogicVersion == "*" || enableNewConsumeLogicVersion.Contains(shopVersion);
        }

        /// <summary>
        /// 是否把消息转发到主客
        /// </summary>
        /// <returns></returns>
        private bool IsTransmitMessageToZk()
        {
            var isTransmit = false;
            try
            {
                //读取配置
                var key = "/System/ZhukePuhuo/IsTransmitMessageToZk";
                var transmitMessageToZkValue = MemoryCacheHelper.GetCacheItem<string>(key);
                if (string.IsNullOrWhiteSpace(transmitMessageToZkValue))
                {
                    var _commonSettingService = new CommonSettingService();
                    transmitMessageToZkValue = _commonSettingService.GetString(key, 0);
                    if (string.IsNullOrWhiteSpace(transmitMessageToZkValue))
                        transmitMessageToZkValue = "0";//获取不到，默认是不转发。赋值0，避免留空时，一直查询配置库
                    MemoryCacheHelper.Set(key, transmitMessageToZkValue, 5 * 60); //缓存5分钟
                }
                if (!string.IsNullOrEmpty(transmitMessageToZkValue) && transmitMessageToZkValue == "1")
                    isTransmit = true;
            }
            catch (Exception e)
            {
                Log.WriteError($"消费程序获取是否转发到铺货时报错：{e.Message}");
            }

            return isTransmit;
        }

        /// <summary>
        /// 链路监控收集-打单-消息校验
        /// </summary>
        /// <param name="syncOrderService"></param>
        /// <param name="platformMessages"></param>
        /// <param name="shop"></param>
        /// <param name="traceStatus"></param>
        /// <param name="message"></param>
        public void TraceDataCollect(SyncOrderService syncOrderService, List<PlatformMessageModel> platformMessages,
            Shop shop, string traceStatus, string message = "")
        {
            foreach (var platformMessage in platformMessages)
            {
                if (!string.IsNullOrEmpty(platformMessage.TID) && !platformMessage.TID.StartsWith("C") && !string.IsNullOrEmpty(platformMessage.BatchId))
                {
                    syncOrderService.TraceDataCollect(new TraceDataModel<TraceDataMetaDataModel>
                    {
                        BatchId = platformMessage.BatchId,
                        ShopId = shop.Id,
                        BusinessId = platformMessage.TID,
                        CloudPlatformType = CustomerConfig.CloudPlatformType,
                        PlatformType = shop.PlatformType,
                        OperationType = TraceOperationType.OrderMessagePush.ToString(),
                        TraceType = TraceType.MessageVerification.ToString(),
                        CreateTime = DateTime.Now,
                        TraceStatus = traceStatus,
                        Message = message,
                        MetaData = new TraceDataMetaDataModel()
                    });
                }
            }
        }
        /// <summary>
        /// 批量同步订单
        /// </summary>
        /// <param name="msgs"></param>
        /// <returns></returns>
        private bool BulkSyncOrder(List<Shop> shops, List<PlatformMessageModel> models, string pt)
        {
            var orders = new ConcurrentBag<Order>();
            var gs = models.GroupBy(m => m.MID).ToList();
            gs.ForEach(g =>
            {
                var syncedOrders = new List<Order>();

                var shop = shops.FirstOrDefault(s => s.ShopId == g.Key);
                var isNeedProcess = shop != null && shop.IsAuthExpired == false && shop.AuthTime != null && shop.LastSyncMessage?.Contains("授权过期") == false &&
                    (shop.LastSyncTime > DateTime.Now.AddDays(-7) ||
                        (shop.LastSyncTime == null && shop.CreateTime > DateTime.Now.AddDays(-3)));
                var sc = new SiteContext(shop, new SiteContextConfig { NeedRelationShops = false, NeedShopExpireTime = false, NeedLoadFxUser = false });
                var _syncOrderService = new SyncOrderService();
                var temps = g.ToList();
                if (isNeedProcess)
                {
                    var service = PlatformFactory.GetPlatformService(shop);
                    //链路日志-消息校验-成功
                    TraceDataCollect(_syncOrderService, temps, shop, TraceStatus.Success);
                    //var asMessages = new List<PlatformMessageModel>();//需另外处理售后的消息
                    foreach (var temp in temps)
                    {
                        if (!string.IsNullOrEmpty(temp.TID) && !temp.TID.StartsWith("C"))
                        {
                            try
                            {
                                Order apiOrder = null;
                                if (shop.PlatformType == PlatformType.KuaiShouSupplier.ToString())
                                {
                                    var order = _syncOrderService.SyncSingleOrderOld(temp.TID, shop, out apiOrder, temp.UserCode, batchId: temp.BatchId);
                                }
                                else
                                {
                                    var order = _syncOrderService.SyncSingleOrderOld(temp.TID, shop, out apiOrder, batchId: temp.BatchId);
                                }
                                if (apiOrder != null)
                                {
                                    syncedOrders.Add(apiOrder);
                                    if (apiOrder.ProductItemCount == 0)
                                        Log.WriteWarning($"店铺：【{shop.Id}】的订单：【{temp.TID}】.ProductItemCount=0,订单详情：{apiOrder.ToJson()}");
                                }
                            }
                            catch (LogicException lex)
                            {
                                Log.WriteError($"同步单个订单时发生逻辑错误，订单ID:{temp.TID}：{lex.Message}");
                                //更新店铺同步状态
                                if (lex.Message.Contains("授权过期"))
                                    _shopService.UpdateLastSyncMessage(shop.Id, lex.Message);
                            }
                            catch (Exception ex)
                            {
                                Log.WriteError($"同步单个订单时发生错误，订单ID:{temp.TID}，{temp.MID}，{shop.Id}：{ex}");
                            }
                        }
                        //if (IsAfterSaleOrder(temp))
                        //    asMessages.Add(temp);
                    }
                    //处理售后单-针对分单系统
                    //HandleAfterSaleOrderFx(asMessages);
                }
                else
                {
                    //链路日志-消息校验-失败
                    var message = $"店铺信息无效：ShopID:{g.Key}，CreateTime：{shop?.CreateTime} ,LastSyncTime:{shop?.LastSyncTime} ,IsAuthExpired:{shop?.IsAuthExpired}";
                    TraceDataCollect(_syncOrderService, temps, shop, TraceStatus.Fail, message);
                    //if (shop != null && shop.PlatformType != PlatformType.KuaiShou.ToString())
                    //   Log.WriteWarning($"店铺信息无效：ShopID:{g.Key}，CreateTime：{shop?.CreateTime} ,LastSyncTime:{shop?.LastSyncTime} ,IsAuthExpired:{shop?.IsAuthExpired}");
                }
                //if (syncedOrders.Any() == false || shop?.PlatformType == PlatformType.TouTiao.ToString() || shop?.PlatformType == PlatformType.KuaiShou.ToString())
                //    return;

                //2023-10-07 zouyi 目前对接消息的平台 只有1688和有赞 是和分单共用的应用，所以这里只需要判断是否是这两个平台，是这两个平台才进行转发
                if (syncedOrders.Any() == false || (shop?.PlatformType != PlatformType.Alibaba.ToString() && shop?.PlatformType != PlatformType.YouZan.ToString()))
                    return;

                try
                {
                    //var sc = new SiteContext(shop, new SiteContextConfig { NeedRelationShops = false, NeedShopExpireTime = false });
                    //判断店铺是否被商家关联，如果被分销关联，则需要将订单更新到商家端
                    (new OrderService()).FxOrderMessageProcess(syncedOrders);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"订单信息推送到商家报错：ShopID:{g.Key}，{ex.Message + ex.StackTrace}");
                }

                //2023-09-26,注释掉。目前铺货系统的快手系统没有使用分单的应用。避免转发引起的问题，需要注释掉。----麦
                //try
                //{
                //    //判断订单所属的店铺是否被加入主客铺货系统，加入主客铺货系统，则需要把订单消息转发到订单铺货的订单队列
                //    var isExistsZhuKeSystem = (new ShopService()).ShopExistsZhuKeSystem(shop.Id);
                //    if (isExistsZhuKeSystem)
                //    {
                //        //快手订单消息转发到住客铺货快手订单消息
                //        var kuaiShouZkOrderDesc = RabbitMQService.KuaiShouZkOrderMessageDescription;
                //        //发送到消息队列
                //        foreach (var msgModel in models)
                //        {
                //            var isSuccess = RabbitMQService.SendMessage(msgModel, kuaiShouZkOrderDesc);
                //            if (isSuccess == false)
                //            {
                //                Log.WriteWarning($"快手订单消息转发到主客铺货的订单队列失败,队列写入返回false，消息内容：{models.ToJson()}");
                //            }
                //        }
                //    }
                //}
                //catch (Exception ex)
                //{
                //    Log.WriteWarning($"快手订单消息转发到主客铺货的订单队列失败，消息内容：{models.ToJson()}，错误详情：{ex}");
                //}

            });

            RemoveKeysFromRedis(models);
            return true;
        }

        /// <summary>
        /// 批量同步产品
        /// </summary>
        /// <param name="shops"></param>
        /// <param name="models"></param>
        /// <param name="pt"></param>
        /// <returns></returns>
        private bool BulkSyncProduct(List<Shop> shops, List<PlatformMessageModel> models, string pt)
        {
            var conn = Data.CustomerConfigExt.GetConnectString(shops.First());
            var _syncProductService = new SyncProductService(conn);
            var gs = models.GroupBy(m => m.MID).ToList();
            gs.ForEach(g =>
            {
                var shop = shops.FirstOrDefault(s => s.ShopId == g.Key);
                if (shop != null && shop.IsAuthExpired == false && shop.AuthTime != null && shop.AuthTime > DateTime.Now.AddDays(-7) &&
                    (shop.LastSyncTime > DateTime.Now.AddDays(-7) ||
                        (shop.LastSyncTime == null && shop.CreateTime > DateTime.Now.AddDays(-1)))
                        )
                {
                    var service = PlatformFactory.GetPlatformService(shop);
                    var temps = g.ToList();
                    var products = new ConcurrentBag<Product>();
                    Parallel.ForEach(temps, new ParallelOptions { MaxDegreeOfParallelism = 10 }, temp =>
                    {
                        if (!string.IsNullOrEmpty(temp.PID))
                        {
                            var pids = temp.PID.Split(',');
                            foreach (var pid in pids)
                            {
                                var tempProduct = _syncProductService.SyncSingleProduct(service, pid);
                                if (tempProduct != null)
                                    products.Add(tempProduct);
                            }
                        }
                    });
                    try
                    {
                        _syncProductService.productService.BulkMerger(products.ToList(), shop);

                        //判断产品所属的店铺是否有关联商家，有的话则需要把产品分发到商家端
                        //new ProductFxService(conn).FxProductMessageProcess(products.ToList());

                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"消息产品推送到商家报错，消息内容：{models.ToJson()}，错误详情：{ex}");
                    }

                    try
                    {
                        //判断订单所属的店铺是否被加入主客铺货系统，加入主客铺货系统，则需要把消息转发到铺货的队列
                        var isExistsZhuKeSystem = (new ShopService()).ShopExistsZhuKeSystem(shop.Id);
                        if (isExistsZhuKeSystem)
                        {
                            //快手订单消息转发到住客铺货快手订单消息
                            var kuaiShouZkProductDesc = RabbitMQService.KuaiShouZkProductMessageDescription;
                            //发送到消息队列
                            foreach (var msgModel in models)
                            {
                                var isSuccess = RabbitMQService.SendMessage(msgModel, kuaiShouZkProductDesc);
                                if (isSuccess == false)
                                {
                                    Log.WriteWarning($"快手产品消息转发到主客铺货的产品消息队列失败,队列写入返回false，消息内容：{models.ToJson()}");
                                }
                            }
                        }

                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"快手产品消息转发到主客铺货的产品消息队列失败，消息内容：{models.ToJson()}，错误详情：{ex}");
                    }


                }
                //else
                //    Log.WriteWarning($"无法查询到店铺信息：ShopID:{g.Key}");
            });
            RemoveKeysFromRedis(models);
            return true;
        }

        /// <summary>
        /// 批量同步商品/订单信息（分单系统）
        /// </summary>
        /// <param name="msgs"></param>
        /// <returns></returns>
        public bool ProcessFxMessage(List<string> msgs, string platformType)
        {
            //抖店应用消息只能在抖店云内消费
            if (CustomerConfig.CloudPlatformType != "TouTiao" && platformType == "TouTiao")
                return true;
            try
            {
                var models = msgs?.Distinct().Select(msg => msg.ToObject<PlatformMessageModel>()).ToList();
                if (models == null || !models.Any())
                    return true;
                //去重
                var dict = new Dictionary<string, PlatformMessageModel>();
                models.ForEach(m =>
                {
                    if (!dict.ContainsKey(m.Key))
                        dict.Add(m.Key, m);
                });
                models = dict.Values.ToList();
                var first = models.FirstOrDefault();
                var pt = first.PT;
                var msgType = string.IsNullOrEmpty(first.PID) ? "订单" : "产品";//产品ID为空则是订单消息
                var _userFxService = new UserFxService();
                var mids = models.Select(m => m.MID).Distinct()?.Where(mid => !string.IsNullOrEmpty(mid))?.ToList();
                if (mids == null || !mids.Any())
                    return true;

                if (first.State == "AlibabaqQngPay")
                {
                    try
                    {
                        ProcessAlibabaQingPayStatus(models);
                        Log.Debug(() => $"ProcessAlibabaQingPayStatus消费完成，models:{models?.ToJson()}", $"1688-QingPay-{DateTime.Now.ToString("yyyyMMdd")}.txt");
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"ProcessAlibabaQingPayStatus发生错误，models:{models?.ToJson()},ex:{ex}", $"1688-QingPay-{DateTime.Now.ToString("yyyyMMdd")}.txt");
                    }
                    return true;
                }

                //var shops = shopService.GetShopWithDbConfig(mids, pt);
                var userFxAndShopList = _userFxService.GetUserFxAndShopsByShopIds(mids, null, platformType); //根据店铺的平台ID查询出店铺所属的商家用户
                                                                                                             //Log.WriteLine($"处理{first.PT}{msgType}消息,ID：{string.Join(",", models.Select(x => x.PID + x.TID))} 店铺ID:{shops?.FirstOrDefault()?.Id} ShopId:{first.MID}");
                if (userFxAndShopList == null || userFxAndShopList.Any() == false)
                    return true;


                #region 抖店消息过滤处理
                //抖店消息：订单部分发货=108，卖家发货消息=102，订单取消消息=106，交易完成消息=103
                //只取被开启预付的用户消息类型
                var needFilterStates = new List<string> { "102" };
                if (platformType == PlatformType.TouTiao.ToString() && msgType == "订单" && models.Any(a => needFilterStates.Contains(a.State)))
                {
                    //被开启预付的用户
                    var fxUserIds = userFxAndShopList.Select(a => a.Item1.Id).Distinct().ToList();
                    var openedPrePayFxUserIds = new SupplierUserService().GetOpenedPrePayFxUserIdFromCache(fxUserIds);
                    var openedMids = userFxAndShopList.Where(a => openedPrePayFxUserIds.Contains(a.Item1.Id)).Select(a => a.Item2.ShopId).Distinct().ToList();

                    models = models.Where(a => (needFilterStates.Contains(a.State) == false) || (needFilterStates.Contains(a.State) && openedMids.Contains(a.MID))).ToList();
                    if (models.Any() == false)
                        return true;
                }
                #endregion

                var rst = true;

                var ptShopIds = userFxAndShopList.Where(a => a.Item2.Version == "3").Select(a => a.Item2.ShopId).ToList();
                var newModels = models.Where(a => ptShopIds.Contains(a.MID)).ToList();//走新模式
                var oldModels = models.Where(a => !ptShopIds.Contains(a.MID)).ToList();//走原来模式

                if (first.PT == PlatformType.TouTiao.ToString() && newModels.Any())
                {
                    //头条消息走新的消费逻辑的次数
                    try { RedisHelper.IncrBy($"FxTouTiaoMsgNewProcess:{DateTime.Now.ToString("yyyyMMdd")}"); } catch { }
                    rst = _fxTouTiaoMessageProcessor.FxProcessTouTiaoMessage(newModels, userFxAndShopList);
                    if (!rst) //处理失败，还是走之前的逻辑处理
                        oldModels = models;
                }
                else
                    oldModels = models;

                if (oldModels == null || !oldModels.Any())
                    return true;

                if (first.State == "111")
                {
                    return (new TouTiaoOrderService()).ProcessFxAppModifyReceiverMessage(oldModels, userFxAndShopList);
                }
                else if (first.State == "10003")
                {

                    //新疆中转 可能去标 ，去标后可以与普通订单合并 需走订单同步逻辑，更新相关合单关联计算-ExtField2
                    var logistics_transitModels = oldModels.Where(x => x.Content.IndexOf("logistics_transit") > -1).ToList();
                    if (logistics_transitModels != null && logistics_transitModels.Any())
                    {
                        BulkSyncFxOrder(userFxAndShopList, logistics_transitModels);
                    }
                    //头条订单标签消息处理
                    return (new TouTiaoOrderService()).ProcessAddFxOrderTagMessage(oldModels, userFxAndShopList);
                }
                else if (first.State == "200" && first.AfterSaleType == "2" && !string.IsNullOrEmpty(first.AfterSaleId))
                {
                    return (new TouTiaoOrderService()).SendFxCancelSendGoods(oldModels, userFxAndShopList);
                }
                else if (first.PT == nameof(PlatformType.AlibabaC2M) && string.IsNullOrEmpty(first.AfterSaleId) == false)
                    return BulkSyncFxOrder(userFxAndShopList, oldModels, true);
                else if (msgType == "订单")
                    return BulkSyncFxOrder(userFxAndShopList, oldModels);
                else
                    return BulkSyncFxProduct(userFxAndShopList, oldModels);
            }
            catch (Exception ex)
            {
                Log.WriteError($"处理消息时发生错误，消息内容：{msgs?.ToJson()}，错误信息：{ex}");
            }
            return true;
        }

        /// <summary>
        /// 处理采购单信息（分单系统）
        /// </summary>
        /// <param name="msgs"></param>
        /// <returns></returns>
        public bool ProcessFxPurchaseMessage(List<string> msgs)
        {
            try
            {
                var models = msgs?.Distinct().Select(msg => msg.ToObject<UpdatePurchaseStatusModel>()).ToList();
                if (models == null || !models.Any())
                    return true;

                Log.WriteLine($"ProcessFxPurchaseMessage消费开始，models:{models?.ToJson()}", $"1688-update-purchase-{DateTime.Now.ToString("yyyyMMdd")}.txt");

                //去重
                var dict = new Dictionary<string, UpdatePurchaseStatusModel>();
                models.ForEach(m =>
                {
                    if (!dict.ContainsKey(m.PlatformOrderId))
                        dict.Add(m.PlatformOrderId, m);
                });
                models = dict.Values.ToList();
                var _userFxService = new UserFxService();
                var fxUserIds = models.Select(a => a.UserId).Distinct().ToList();
                var userFxs = _userFxService.GetsByIds(fxUserIds);
                //按用户维度逐个处理
                userFxs?.ForEach(userFx =>
                {
                    var sc = new SiteContext(userFx, new SiteContextConfig { NeedRelationShops = false, NeedShopExpireTime = false });
                    var curModels = models.Where(a => a.UserId == userFx.Id).ToList();
                    var porService = new PurchaseOrderRelationService();
                    Log.Debug(() => $"当前用户={userFx.Id}，默认所在库：{porService.baseRepository.DbConnection.Database}");
                    porService.UpdatePurchaseStatus(curModels);
                });

                Log.WriteLine($"ProcessFxPurchaseMessage消费完成，models:{models?.ToJson()}", $"1688-update-purchase-{DateTime.Now.ToString("yyyyMMdd")}.txt");

            }
            catch (Exception ex)
            {
                Log.WriteError($"处理采购单信息消息时发生错误，消息内容：{msgs?.ToJson()}，错误信息：{ex}", $"1688-update-purchase-error-{DateTime.Now.ToString("yyyyMMdd")}.txt");
            }
            return true;
        }

        /// <summary>
        /// 处理买家消息（分单系统）
        /// </summary>
        /// <param name="msgs"></param>
        /// <param name="platformType"></param>
        /// <returns></returns>
        public bool ProcessBuyerFxMessage(List<string> msgs, string platformType)
        {
            try
            {
                Log.WriteLine($"ProcessBuyerFxMessage.Step1，msgs:{msgs?.ToJson()}", $"1688-buyer-{DateTime.Now.ToString("yyyyMMdd")}.txt");

                var models = msgs?.Distinct().Select(msg => msg.ToObject<PlatformMessageModel>()).ToList();
                if (models == null || !models.Any())
                    return true;
                //去重
                var dict = new Dictionary<string, PlatformMessageModel>();
                models.ForEach(m =>
                {
                    if (!dict.ContainsKey(m.Key))
                        dict.Add(m.Key, m);
                });
                models = dict.Values.ToList();
                var first = models.FirstOrDefault();
                var pt = first.PT;
                var msgType = string.IsNullOrEmpty(first.PID) ? "订单" : "产品";//产品ID为空则是订单消息

                var mids = models.Select(m => m.MID).Distinct()?.Where(mid => !string.IsNullOrEmpty(mid))?.ToList();
                if (mids == null || !mids.Any())
                    return true;

                Log.Debug(() => $"ProcessBuyerFxMessage.Step2，models:{models?.ToJson(true)}", $"1688-buyer-{DateTime.Now.ToString("yyyyMMdd")}.txt");

                if (msgType == "订单")
                {
                    var _userFxService = new UserFxService();
                    var _buyerRelationService = new FxAlibabaBuyerShopRelationService();
                    var buyerRelations = _buyerRelationService.GetListByMemberIds(mids);
                    if (buyerRelations == null || buyerRelations.Any() == false)
                        return true;

                    var buyerFxUserIds = buyerRelations.Select(a => a.FxUserId).Distinct().ToList();
                    var buyerUserFxs = _userFxService.GetsByIds(buyerFxUserIds);

                    var allList = new List<PurchaseOrderRelation>();

                    //1.查询PurchaseOrderRelation
                    //按买家维度逐个处理
                    buyerUserFxs.ForEach(buyer =>
                    {
                        var memberId = buyerRelations.First(a => a.FxUserId == buyer.Id).ShopId;
                        var sc = new SiteContext(buyer, new SiteContextConfig { NeedRelationShops = false, NeedShopExpireTime = false });

                        var pids = models.Where(a => a.MID == memberId).Select(a => a.TID).Distinct().ToList();

                        var poRelationService = new PurchaseOrderRelationService();
                        var poRelations = poRelationService.GetByPids(pids, status: 1);

                        if (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString() && poRelations.Count() < pids.Count())
                        {
                            //1.查出其他精选库
                            var dbList = new DbConfigRepository().GetFxAllCloudPlatformBusinessDbConfigsByAlibaba();
                            var otherAlibabaDbs = dbList.Where(a =>
                                    a.DbNameConfig.DbName != poRelationService.baseRepository.DbConnection.Database)
                                .ToList();

                            //2.其他精选库查询
                            otherAlibabaDbs.ForEach(oDb =>
                            {
                                var curRelations = new PurchaseOrderRelationService(oDb.ConnectionString).GetByPids(pids, status: 1);
                                if (curRelations != null && curRelations.Any())
                                    allList.AddRange(curRelations);

                                Log.WriteLine($"ProcessBuyerFxMessage.其他精选库查询，curRelations:{curRelations?.ToJson(true)}，db={oDb.DbNameConfig?.DbName}", $"1688-buyer-{DateTime.Now.ToString("yyyyMMdd")}.txt");
                            });
                        }


                        Log.Debug(() => $"ProcessBuyerFxMessage.GetByPids，poRelations:{poRelations?.ToJson(true)}，db={new PurchaseOrderRelationService().baseRepository?.DbConnection?.Database}", $"1688-buyer-{DateTime.Now.ToString("yyyyMMdd")}.txt");
                        if (poRelations != null && poRelations.Any())
                            allList.AddRange(poRelations);
                    });


                    if (allList.Any())
                    {
                        var shopIds = allList.Select(a => a.PurchaseOrderShopId).Distinct().ToList();
                        var shops = _shopService.GetShopsAndShopExtension(shopIds);
                        var supplierModels = new List<PlatformMessageModel>();

                        allList.ForEach(o =>
                        {
                            var shop = shops.FirstOrDefault(a => a.Id == o.PurchaseOrderShopId);
                            if (shop != null)
                            {
                                supplierModels.Add(new PlatformMessageModel
                                {
                                    PT = platformType,
                                    TID = o.PurchasePlatformOrderId,
                                    MID = shop.ShopId,
                                    Key = $"1688-{o.PurchasePlatformOrderId}",
                                    State = "ORDER_BUYER"
                                });
                            }
                        });

                        //2.1 精选云：直接同步订单
                        //按厂家+店铺分组处理
                        if (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString())
                        {

                            var supplierMids = supplierModels.Select(a => a.MID).Distinct().ToList();
                            var supplierUserFxAndShopList = _userFxService.GetUserFxAndShopsByShopIds(supplierMids, null);

                            Log.Debug(() => $"ProcessBuyerFxMessage直接消费，supplierModels:{supplierModels?.ToJson(true)}，supplierMids:{supplierMids?.ToJson()}", $"1688-buyer-{DateTime.Now.ToString("yyyyMMdd")}.txt");

                            if (supplierUserFxAndShopList == null || supplierUserFxAndShopList.Any() == false)
                                return true;

                            //同步订单
                            BulkSyncFxOrder(supplierUserFxAndShopList, supplierModels);
                        }
                        else
                        {
                            //2.2 其他云：Post消息到专门的接收地址
                            var url = CustomerConfig.DgjMessageReceiveUrl;
                            if (string.IsNullOrEmpty(url))
                                return true;
                            var wu = new WebUtils();
                            supplierModels.ForEach(model =>
                            {
                                try
                                {
                                    var requestBody = Encoding.UTF8.GetBytes(model.ToJson());
                                    string content = wu.DoPostByRequestStream(url, requestBody);

                                    Log.Debug(() => $"ProcessBuyerFxMessage转发{url}，model:{model.ToJson(true)}", $"1688-buyer-{DateTime.Now.ToString("yyyyMMdd")}.txt");
                                }
                                catch (Exception ex)
                                {
                                    Log.WriteError($"ProcessBuyerFxMessage发送到{url}时发生错误，消息内容：{msgs?.ToJson()}，错误信息：{ex}", $"1688-buyer-error-{DateTime.Now.ToString("yyyyMMdd")}.txt");
                                }
                            });
                        }
                    }

                    Log.Debug(() => $"ProcessBuyerFxMessage消费完成，models:{models?.ToJson(true)}", $"1688-buyer-{DateTime.Now.ToString("yyyyMMdd")}.txt");
                }

            }
            catch (Exception ex)
            {
                Log.WriteError($"ProcessBuyerFxMessage时发生错误，消息内容：{msgs?.ToJson(true)}，错误信息：{ex}", $"1688-buyer-error-{DateTime.Now.ToString("yyyyMMdd")}.txt");
            }
            return true;
        }

        /// <summary>
        /// 处理自动售后消息（分单系统）
        /// </summary>
        /// <param name="msgs"></param>
        /// <returns></returns>
        public bool ProcessFxAutoAfterSaleMessage(List<string> msgs)
        {
            try
            {
                var logFileName = $"1688-autoaftersale-{DateTime.Now.ToString("yyyyMMdd")}.txt";
                Log.WriteLine($"ProcessFxAutoAfterSaleMessage.Step1，msgs:{msgs?.ToJson()}", logFileName);

                //MID存的是UpFxUserId
                //PID存的是RefundActionRecordCode
                var models = msgs?.Distinct().Select(msg => msg.ToObject<PlatformMessageModel>()).ToList();
                if (models == null || !models.Any())
                    return true;

                //去重
                var dict = new Dictionary<string, PlatformMessageModel>();
                models.ForEach(m =>
                {
                    if (!dict.ContainsKey(m.PID))
                        dict.Add(m.PID, m);
                });

                models = dict.Values.ToList();

                //厂家用户Id
                var supplierFxUserIds = models.Select(m => m.MID.ToInt()).Distinct()?.Where(mid => mid > 0)?.ToList();
                if (supplierFxUserIds == null || !supplierFxUserIds.Any())
                    return true;

                Log.Debug(() => $"ProcessFxAutoAfterSaleMessage.Step2，models:{models?.ToJson(true)}", logFileName);
                var _userFxService = new UserFxService();
                var supplierUserFxs = _userFxService.GetsByIds(supplierFxUserIds);

                //按用户维度逐个处理
                supplierUserFxs?.ForEach(fxUser =>
                {
                    var sc = new SiteContext(fxUser, new SiteContextConfig { NeedRelationShops = false, NeedShopExpireTime = false });

                    var codes = models.Where(a => a.MID == fxUser.Id.ToString()).Select(a => a.PID).Distinct().ToList();

                    Log.Debug(() => $"ExeRefundFromMessage，models:{codes?.ToJson(true)}", logFileName);
                    var asarService = new AfterSaleActionRecordService();
                    asarService.ExeRefundFromMessage(codes);
                });

            }
            catch (Exception ex)
            {
                Log.WriteError($"ProcessFxAutoAfterSaleMessage时发生错误，消息内容：{msgs?.ToJson(true)}，错误信息：{ex}", $"1688-autoaftersale-error-{DateTime.Now.ToString("yyyyMMdd")}.txt");
            }
            return true;
        }

        /// <summary>
        /// 处理自动回流消息（分单系统）
        /// </summary>
        /// <param name="msgs"></param>
        /// <returns></returns>
        public bool ProcessFxSendHistoryReturnMessage(List<string> msgs)
        {
            try
            {
                var logFileName = $"1688-sendhistoryreturn-{DateTime.Now.ToString("yyyyMMdd")}.txt";
                Log.WriteLine($"ProcessFxSendHistoryReturnMessage.Step1，msgs:{msgs?.ToJson()}", logFileName);

                //PT存的是目标云平台：Alibaba或TouTiao
                //MID存的是SendFxUserId
                //PID存的是RefundActionRecordCode
                //CID存的是DbName
                //AfterSaleType存的是SourcePlatformType
                var models = msgs?.Distinct().Select(msg => msg.ToObject<PlatformMessageModel>()).ToList();
                if (models == null || !models.Any())
                    return true;

                //去重
                var dict = new Dictionary<string, PlatformMessageModel>();
                models.ForEach(m =>
                {
                    if (!dict.ContainsKey(m.PID))
                        dict.Add(m.PID, m);
                });

                models = dict.Values.ToList();

                var purchaseOrderSendService = new PurchaseOrderSendService();
                models.ForEach(m =>
                {
                    var loSendMsg = new LogicOrderSendMessageModel
                    {
                        SendUserId = m.MID.ToInt(),
                        ReturnRecordCode = m.PID,
                        DbName = m.CID
                    };
                    var rst = purchaseOrderSendService.Send(loSendMsg, true);
                    if (rst.IsSucc == false)
                    {
                        //消息消费失败，写入rabbit mq
                        FailToRabbitMq(loSendMsg);
                    }
                });

            }
            catch (Exception ex)
            {
                Log.WriteError($"ProcessFxSendHistoryReturnMessage时发生错误，消息内容：{msgs?.ToJson(true)}，错误信息：{ex}", $"1688-sendhistoryreturn-error-{DateTime.Now.ToString("yyyyMMdd")}.txt");
            }
            return true;
        }


        /// <summary>
        /// 更新采购结算价（分单系统）
        /// </summary>
        /// <param name="msgs"></param>
        /// <returns></returns>
        public bool ProcessFxUpdatePurchaseSettlementPriceMessage(List<string> msgs)
        {
            try
            {
                var models = msgs?.Distinct().Select(msg => msg.ToObject<UpdatePurchaseSettlementPriceModel>()).ToList();
                if (models == null || !models.Any())
                    return true;

               // var logFileName = $"1688-UpdatePurchaseSettlementPrice-{DateTime.Now.ToString("yyyyMMdd")}.txt";
               // Log.WriteLine($"ProcessFxUpdatePurchaseSettlementPriceMessage消费开始，models:{models?.ToJson()}", logFileName);

                //去重
                var dict = new Dictionary<string, UpdatePurchaseSettlementPriceModel>();
                models.ForEach(m =>
                {
                    if (!dict.ContainsKey(m.PlatformId))
                        dict.Add(m.PlatformId, m);
                });
                models = dict.Values.ToList();
                var _userFxService = new UserFxService();
                var fxUserIds = models.Select(a => a.UserId).Distinct().ToList();
                var userFxs = _userFxService.GetsByIds(fxUserIds);
                //按用户维度逐个处理
                userFxs?.ForEach(userFx =>
                {
                    var sc = new SiteContext(userFx, new SiteContextConfig { NeedRelationShops = false, NeedShopExpireTime = false });
                    var curModels = models.Where(a => a.UserId == userFx.Id).ToList();
                    var productFxService = new ProductFxService();
                   // Log.WriteLine($"当前用户={userFx.Id}，默认所在库：{productFxService.baseRepository.DbConnection.Database}", logFileName);

                    var fxProducts = productFxService.GetProductsForPurchaseSettlementPrice(models, out var tupleSet, out var shopPlatformType);

                    productFxService.UpdatePurchaseSettlementPrice(models, fxProducts, tupleSet, shopPlatformType, "FormMessage");
                });

               // Log.WriteLine($"ProcessFxUpdatePurchaseSettlementPriceMessage消费完成，models:{models?.ToJson()}", logFileName);

            }
            catch (Exception ex)
            {
                Log.WriteError($"处理更新采购结算价消息时发生错误，消息内容：{msgs?.ToJson()}，错误信息：{ex}", $"1688-UpdatePurchaseSettlementPrice-error-{DateTime.Now.ToString("yyyyMMdd")}.txt");
            }
            return true;
        }


        /// <summary>
        /// 基础商品消息处理（分单系统）
        /// </summary>
        /// <param name="msgs"></param>
        /// <returns></returns>
        public bool ProcessFxBaseProductMessage(List<string> msgs)
        {
            try
            {
                var models = msgs?.Distinct().Select(msg => msg.ToObject<MessageRecord>()).ToList();
                if (models == null || !models.Any())
                    return true;

                var logFileName = $"baseproduct-{DateTime.Now.ToString("yyyyMMdd")}.txt";
                Log.Debug(() => $"ProcessFxBaseProductMessage，models:{models?.ToJson()}", logFileName);

                var _userFxService = new UserFxService();
                var fxUserIds = models.Select(a => a.FxUserId).Distinct().ToList();
                var userFxs = _userFxService.GetsByIds(fxUserIds);

                var failMessages = new List<MessageRecord>();
                var curCpt = CustomerConfig.CloudPlatformType;
                var messageRecordService = new MessageRecordService();

                //按用户维度逐个处理
                userFxs?.ForEach(userFx =>
                {
                    var curModels = models.Where(a => a.FxUserId == userFx.Id).ToList();

                    //拼多多或精选，按库执行
                    if (curCpt == CloudPlatformType.Pinduoduo.ToString() || curCpt == CloudPlatformType.Alibaba.ToString())
                    {
                        //按DbName处理各消息
                        var dbNames = curModels.Where(a => a.DbName.IsNotNullOrEmpty()).Select(a => a.DbName).Distinct().ToList();
                        if (dbNames != null && dbNames.Any())
                        {
                            dbNames.ForEach(dbName =>
                            {
                                var sc = new SiteContext(userFx, dbName, new SiteContextConfig { NeedRelationShops = false, NeedShopExpireTime = false });
                                var subModels = curModels.Where(a => a.DbName == dbName).ToList();
                                Log.Debug(() => $"当前用户={userFx.Id}，所在库：{dbName}", logFileName);
                                var curFailMessages = messageRecordService.ProcessFxBaseProductMessage(subModels, "FormMessage");

                                if (curFailMessages != null && curFailMessages.Any())
                                {
                                    failMessages.AddRange(curFailMessages);
                                }
                            });
                        }
                        //处理DbName为空的消息
                        var subModels2 = curModels.Where(a => a.DbName.IsNullOrEmpty()).ToList();
                        if (subModels2 != null && subModels2.Any())
                        {
                            var sc2 = new SiteContext(userFx, new SiteContextConfig { NeedRelationShops = false, NeedShopExpireTime = false });
                            Log.Debug(() => $"当前用户2={userFx.Id}", logFileName);
                            var curFailMessages2 = messageRecordService.ProcessFxBaseProductMessage(subModels2, "FormMessage");
                            if (curFailMessages2 != null && curFailMessages2.Any())
                            {
                                failMessages.AddRange(curFailMessages2);
                            }
                        }
                    }
                    else
                    {
                        //DbName不参与初始上下文
                        var sc = new SiteContext(userFx, new SiteContextConfig { NeedRelationShops = false, NeedShopExpireTime = false });
                        Log.Debug(() => $"当前用户={userFx.Id}", logFileName);
                        var curFailMessages = messageRecordService.ProcessFxBaseProductMessage(curModels, "FormMessage");
                        if (curFailMessages != null && curFailMessages.Any())
                        {
                            failMessages.AddRange(curFailMessages);
                        }
                    }

                });

                Log.Debug(() => $"ProcessFxBaseProductMessage消费完成，models:{models?.ToJson()}，失败：{failMessages.ToJson()}", logFileName);

                //失败的写入延迟队列

                //需要写入延迟队列的消息类型（目前只针对BaseOfPtSkuRelation消息）
                var needMsgTypes = new List<string>() { BaseProductMsgType.BaseOfPtSkuRelation };
                if (failMessages.Any(a => needMsgTypes.Contains(a.MsgType)))
                {
                    failMessages = failMessages.Where(a => needMsgTypes.Contains(a.MsgType)).ToList();
                    FailToRabbitMqForBusinessMessage(failMessages);
                }

            }
            catch (Exception ex)
            {
                Log.WriteError($"ProcessFxBaseProductMessage时发生错误，消息内容：{msgs?.ToJson()}，错误信息：{ex}", $"baseproduct-error-{DateTime.Now.ToString("yyyyMMdd")}.txt");
            }
            return true;
        }

        /// <summary>
        /// 店铺授权消息处理（分单系统-铺货应用新店铺-同步店铺类目相关）
        /// </summary>
        /// <param name="msgs"></param>
        /// <returns></returns>
        public bool ProcessFxShopAuthMessage(List<string> msgs)
        {
            try
            {
                var models = msgs?.Distinct().Select(msg => msg.ToObject<ShopAuthMessageModel>()).ToList();
                if (models == null || !models.Any())
                    return true;

                var logFileName = $"shopauth-{DateTime.Now.ToString("yyyyMMdd")}.txt";
                Log.WriteLine($"ProcessFxShopAuthMessage，models:{models?.ToJson()}", logFileName);

                var _fusService = new FxUserShopService();
                var shopIds = models.Select(a => a.ShopId).Distinct().ToList();
                var fxUserShops = _fusService.GetUserIdByShopId(shopIds, "Id,ShopId,FxUserId,PlatformType");
                if (fxUserShops == null || fxUserShops.Any() == false)
                    return true;

                var _userFxService = new UserFxService();
                var fxUserIds = fxUserShops.Select(a => a.FxUserId).Distinct().ToList();
                var userFxs = _userFxService.GetsByIds(fxUserIds);
                if (userFxs == null || userFxs.Any() == false)
                    return true;

                var categoryService = new PlatformCategorySupplierService();

                //按用户维度逐个处理
                userFxs.ForEach(userFx =>
                {
                    var curShopIds = fxUserShops.Where(a => a.FxUserId == userFx.Id).Select(a => a.ShopId).ToList();
                    var curModels = models.Where(a => curShopIds.Contains(a.ShopId)).ToList();
                    var sc = new SiteContext(userFx, new SiteContextConfig { NeedRelationShops = false, NeedShopExpireTime = false });

                    Log.WriteLine($"当前用户={userFx.Id}", logFileName);

                    ThreadPool.QueueUserWorkItem(state =>
                    {
                        //处理同步指定店铺类目相关
                        curModels.ForEach(model =>
                        {
                            try
                            {
                                categoryService.SyncFnDdCateGory(model.ShopId);

                            }
                            catch (Exception e)
                            {
                                Log.WriteError($"ProcessFxShopAuthMessage消费时异常，异常原因：{e.Message}，FxUserId={userFx.Id}，model={model.ToJson(true)}，堆栈信息：{e.StackTrace}", $"shopauth-error-{DateTime.Now.ToString("yyyyMMdd")}.txt");
                            }
                        });
                    });

                });

                Log.WriteLine($"ProcessFxShopAuthMessage消费完成，models:{models?.ToJson()}", logFileName);

            }
            catch (Exception ex)
            {
                Log.WriteError($"ProcessFxShopAuthMessage时发生错误，消息内容：{msgs?.ToJson()}，错误信息：{ex}", $"shopauth-error-{DateTime.Now.ToString("yyyyMMdd")}.txt");
            }
            return true;
        }

        /// <summary>
        /// 铺货任务完成后触发业务逻辑处理
        /// </summary>
        /// <param name="msgs"></param>
        /// <returns></returns>
        public bool ProcessFxListingCompleteMessage(List<string> msgs)
        {
            try
            {
                var models = msgs?.Distinct().Select(msg => msg.ToObject<ListingCompleteMessageModel>()).ToList();
                if (models == null || !models.Any())
                    return true;

                var logFileName = $"ProcessListingTaskBusiness-{DateTime.Now.ToString("yyyyMMdd")}.txt";
                Log.WriteLine($"ProcessFxListingCompleteMessage，models:{models?.ToJson()}", logFileName);

                var _userFxService = new UserFxService();
                var fxUserIds = models.Select(a => a.FxUserId).Distinct().ToList();
                var userFxs = _userFxService.GetsByIds(fxUserIds);
                if (userFxs == null || userFxs.Any() == false)
                    return true;

                //按用户维度逐个处理
                userFxs.ForEach(userFx =>
                {
                    var curModels = models.Where(a => a.FxUserId == userFx.Id).ToList();
                    var sc = new SiteContext(userFx, new SiteContextConfig { NeedRelationShops = false, NeedShopExpireTime = false });

                    Log.WriteLine($"当前用户={userFx.Id}", logFileName);

                    var listingService = new ListingTaskRecordsService();

                    ThreadPool.QueueUserWorkItem(state =>
                    {
                        //处理同步指定店铺类目相关
                        curModels.ForEach(model =>
                        {
                            try
                            {
                                listingService.ProcessListingTaskBusiness(model.ListingTaskCode, model.FxUserId);
                            }
                            catch (Exception e)
                            {
                                Log.WriteError($"ProcessFxListingCompleteMessage消费时异常，异常原因：{e.Message}，FxUserId={userFx.Id}，model={model.ToJson(true)}，堆栈信息：{e.StackTrace}", $"ProcessListingTaskBusiness-error-{DateTime.Now.ToString("yyyyMMdd")}.txt");
                            }
                        });
                    });

                });

                Log.WriteLine($"ProcessFxListingCompleteMessage消费完成，models:{models?.ToJson()}", logFileName);

            }
            catch (Exception ex)
            {
                Log.WriteError($"ProcessFxListingCompleteMessage时发生错误，消息内容：{msgs?.ToJson()}，错误信息：{ex}", $"ProcessListingTaskBusiness-error-{DateTime.Now.ToString("yyyyMMdd")}.txt");
            }
            return true;
        }

        /// <summary>
        /// 云消息处理
        /// </summary>
        /// <param name="msgs"></param>
        /// <param name="serviceType">云服务子类</param>
        /// <returns></returns>
        public bool ProcessFxCloudMessage<T>(List<string> msgs, Type serviceType) where T : CloudMessage, new()
        {
            try
            {
                var logFileName = CloudMessageBaseService<T>.LocalLogName;
                // 是否为云基础服务的子类
                if (!serviceType.IsSubclassOf(typeof(CloudMessageBaseService<T>)))
                {
                    Log.WriteError($"{serviceType.Name}不是CloudMessageBaseService的子类", logFileName);
                    throw new Exception($"{serviceType.Name}不是CloudMessageBaseService的子类");
                }
                var models = msgs?.Distinct().Select(msg => msg.ToObject<T>()).ToList();
                if (models == null || !models.Any())
                    return true;

                var cmService = (CloudMessageBaseService<T>)Activator.CreateInstance(serviceType);
     


                var _userFxService = new UserFxService();
                var fxUserIds = models.Select(a => a.FxUserId).Where(i => i>0).Distinct().ToList();
                var userFxs = _userFxService.GetsByIds(fxUserIds);

                var failMessages = new List<T>();

                Log.WriteLine($"{serviceType.Name}处理云消息，models:{models?.ToJson()}", logFileName);

                //按用户维度逐个处理
                userFxs?.ForEach(userFx =>
                {
                    var curModels = models.Where(a => a.FxUserId == userFx.Id).ToList();
                    if (typeof(T) == typeof(ProfitStatisticsCloudMessage)) //利润统计消息
                    {
                        var childService = cmService as ProfitStatisticsCloudMessageService;
                        var childModels = curModels as List<ProfitStatisticsCloudMessage>;
                        var curResults = DoProcessFxProfitStatisticsCloudMessage(childService, childModels, userFx); 
                        if (curResults != null) 
                            failMessages.AddRange(curResults as List<T> ?? new List<T>());
                    }
                });

                Log.WriteLine($"{serviceType.Name}云消息消费完成，models:{models?.ToJson()}，失败：{failMessages.ToJson()}", logFileName);

                //失败的写入延迟队列

                //需要写入延迟队列的消息类型（CloudMsgType.RetryProcess == true）
                var retryMsgTypes = serviceType
                                    .GetMethods(BindingFlags.Public | BindingFlags.Instance | BindingFlags.DeclaredOnly | BindingFlags.InvokeMethod)
                                    .SelectMany(m => m.GetCustomAttributes(typeof(CloudMsgType), false))
                                    .Where(a => ((CloudMsgType)a)?.RetryProcess ?? false)
                                    .Select(a => ((CloudMsgType)a).MsgType)
                                    .ToList();

                if (retryMsgTypes.IsNotNullAndAny())
                {
                    failMessages = failMessages.Where(a => retryMsgTypes.Contains(a.MsgType)).ToList();
                    var delayDescriptions = new MessageDescription[] { };
                    MessageDescription deadLetterDescription = null;
                    if (typeof(T) == typeof(ProfitStatisticsCloudMessage)) //利润统计消息
                    {
                        delayDescriptions = new MessageDescription[]
                        {
                            RabbitMQService.FxProfitStatisticsMessageDescription01,
                            RabbitMQService.FxProfitStatisticsMessageDescription02,
                            RabbitMQService.FxProfitStatisticsMessageDescription03,
                            RabbitMQService.FxProfitStatisticsMessageDescription04,
                            RabbitMQService.FxProfitStatisticsMessageDescription05

                        };
                        deadLetterDescription = RabbitMQService.FxProfitStatisticsDeadLetterMessageDescription;
                    }

                    if (delayDescriptions != null && deadLetterDescription != null)
                        CloudMsgFailToRabbitMq(failMessages, delayDescriptions, deadLetterDescription);
                }

            }
            catch (Exception ex)
            {
                Log.WriteError($"ProcessFxCloudMessage-{typeof(T).Name}时发生异常，消息内容：{msgs?.ToJson()}，错误信息：{ex}", $"{typeof(T).Name}-error-{DateTime.Now.ToString("yyyyMMdd")}.txt");
            }
            return true;
        }

        /// <summary>
        /// 处理利润统计的消息
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="service"></param>
        /// <param name="curModels"></param>
        /// <param name="userFx"></param>
        /// <returns></returns>
        private List<ProfitStatisticsCloudMessage> DoProcessFxProfitStatisticsCloudMessage(ProfitStatisticsCloudMessageService service, List<ProfitStatisticsCloudMessage> curModels, UserFx userFx)
        {
            var curCpt = CustomerConfig.CloudPlatformType;
            //curCpt = "TouTiao";
            var failedMessages = new List<ProfitStatisticsCloudMessage>();
            switch (curCpt)
            {
                case "Pinduoduo":
                    DoPinduoduo();
                    break;
                case "Alibaba":
                    DoAlibaba();
                    break;
                case "TouTiao":
                    DoTouTiao();
                    break;
                case "Jingdong":
                    DoJingdong();
                    break;
            }
            void DoPinduoduo()
            {
                throw new NotImplementedException("利润统计模块方法未实现拼多多消息处理");
            }
            void DoAlibaba()
            {
                throw new NotImplementedException("利润统计模块方法未实现精选消息处理");
            }
            void DoTouTiao()
            {
                //DbName不参与初始上下文
                var sc = new SiteContext(userFx, new SiteContextConfig { NeedRelationShops = false, NeedShopExpireTime = false });
                LogUser();
                failedMessages = service.Process(curModels, "FormMessage");
            }
            void DoJingdong()
            {
                throw new NotImplementedException("利润统计模块方法未实现京东消息处理");
            }
            void LogUser()
            {
                Log.WriteLine($"利润统计模块消息处理构建站点上下文，当前用户={userFx.Id}", ProfitStatisticsCloudMessageService.LocalLogName);
            }

            return failedMessages;
        }

        /// <summary>
        /// 处理采购金预充值支付消息（分单系统）
        /// </summary>
        /// <param name="msgs"></param>
        /// <returns></returns>
        public bool ProcessFxBatchFundChargeMessage(List<string> msgs)
        {
            try
            {
                var logFileName = $"1688-wangshangpay-{DateTime.Now.ToString("yyyyMMdd")}.txt";
                Log.WriteLine($"ProcessFxBatchFundChargeMessage.Step1，msgs:{msgs?.ToJson()}", logFileName);

                //PID：存的是ApplyNo
                //MID：存的是买家Id
                //CID：存的是厂家/卖家Id

                var models = msgs?.Distinct().Select(msg => msg.ToObject<PlatformMessageModel>()).ToList();
                if (models == null || !models.Any())
                    return true;

                //去重
                var dict = new Dictionary<string, PlatformMessageModel>();
                models.ForEach(m =>
                {
                    if (!dict.ContainsKey(m.PID))
                        dict.Add(m.PID, m);
                });

                models = dict.Values.ToList();

                var buyerFxUserIds = models.Select(a => a.MID.ToInt()).Distinct().ToList();
                var _userFxService = new UserFxService();
                var buyerUserFxs = _userFxService.GetsByIds(buyerFxUserIds);
                var psService = new PaymentStatementService();

                //按用户维度逐个处理
                buyerUserFxs.ForEach(fxUser =>
                {
                    var sc = new SiteContext(fxUser, new SiteContextConfig { NeedRelationShops = false, NeedShopExpireTime = false });
                    models.Where(a => a.MID == fxUser.Id.ToString()).ToList().ForEach(m =>
                    {
                        psService.ProcessPayResult(m.PID);
                    });
                });


            }
            catch (Exception ex)
            {
                Log.WriteError($"ProcessFxBatchFundChargeMessage时发生错误，消息内容：{msgs?.ToJson(true)}，错误信息：{ex}", $"1688-wangshangpay-error-{DateTime.Now.ToString("yyyyMMdd")}.txt");
            }
            return true;
        }
        /// <summary>
        /// 处理采购金预充值退款消息（分单系统）
        /// </summary>
        /// <param name="msgs"></param>
        /// <returns></returns>
        public bool ProcessFxRefundChargeMessage(List<string> msgs)
        {
            try
            {
                var logFileName = $"1688-wangshangpay-refund-{DateTime.Now.ToString("yyyyMMdd")}.txt";
                Log.WriteLine($"ProcessFxRefundChargeMessage.Step1，msgs:{msgs?.ToJson()}", logFileName);

                //PID：存的是ApplyNo
                //TID：存的是RefundNo
                //MID：存的是买家Id
                //CID：存的是厂家/卖家Id

                var models = msgs?.Distinct().Select(msg => msg.ToObject<PlatformMessageModel>()).ToList();
                if (models == null || !models.Any())
                    return true;

                //去重
                var dict = new Dictionary<string, PlatformMessageModel>();
                models.ForEach(m =>
                {
                    if (!dict.ContainsKey(m.PID))
                        dict.Add(m.PID, m);
                });

                models = dict.Values.ToList();

                var psService = new PaymentStatementService();
                models.ForEach(m =>
                {
                    psService.ProcessRefundResult(m.PID, m.TID);
                });

            }
            catch (Exception ex)
            {
                Log.WriteError($"ProcessFxRefundChargeMessage时发生错误，消息内容：{msgs?.ToJson(true)}，错误信息：{ex}", $"1688-wangshangpay-refund-error-{DateTime.Now.ToString("yyyyMMdd")}.txt");
            }
            return true;
        }
        /// <summary>
        /// 处理售后自动入库（分单系统）
        /// </summary>
        /// <param name="msgs"></param>
        /// <returns></returns>
        public bool ProcessFxAfterSaleStockMessage(List<string> msgs)
        {
            try
            {
                var logFileName = $"1688-aftersale-stock-{DateTime.Now.ToString("yyyyMMdd")}.txt";
                Log.Debug(() => $"ProcessFxAfterSaleStockMessage.Step1，msgs:{msgs?.ToJson()}", logFileName);


                var models = msgs?.Distinct().Select(msg => msg.ToObject<AfterSaleOrderStockMessageModel>()).ToList();
                if (models == null || !models.Any())
                    return true;

                //去重
                var dict = new Dictionary<string, AfterSaleOrderStockMessageModel>();
                models.ForEach(m =>
                {
                    if (!dict.ContainsKey(m.AfterSaleCode))
                        dict.Add(m.AfterSaleCode, m);
                });

                models = dict.Values.ToList();
                var _userFxService = new UserFxService();
                var fxUserIds = models.Select(a => a.FxUserId).Distinct().ToList();
                var userFxs = _userFxService.GetsByIds(fxUserIds);

                //按用户维度逐个处理
                userFxs?.ForEach(userFx =>
                {
                    try
                    {
                        var sc = new SiteContext(userFx, new SiteContextConfig { NeedRelationShops = false, NeedShopExpireTime = false });
                        var curModels = models.Where(a => a.FxUserId == userFx.Id).ToList();

                        var wareHouseService = new WareHouseService();
                        wareHouseService.ProcessStockInFromMessage(curModels);
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"ProcessFxAfterSaleStockMessage时发生错误，当前用户Id={userFx.Id}，当前db={new AfterSaleOrderRepository().DbConnection?.Database}，curModels={models.Where(a => a.FxUserId == userFx.Id)?.ToJson(true)}，错误信息：{ex}", $"1688-aftersale-stock-error-{DateTime.Now.ToString("yyyyMMdd")}.txt");
                    }
                });

            }
            catch (Exception ex)
            {
                Log.WriteError($"ProcessFxAfterSaleStockMessage时发生错误，消息内容：{msgs?.ToJson(true)}，错误信息：{ex}", $"1688-aftersale-stock-error-{DateTime.Now.ToString("yyyyMMdd")}.txt");
            }
            return true;
        }

        /// <summary>
        /// 消息消费失败，写入RabbitMq 延迟队列，做后续的重试
        /// </summary>
        public void FailToRabbitMq(LogicOrderSendMessageModel messageModel)
        {
            var rst = false; //是否成功写入队列

            //以防队列写不进去，重试3次
            for (int i = 0; i < 3; i++)
            {
                if (rst)
                    break;

                try
                {
                    //根据消息设置的消费失败时写入的队列属性，判断当前消息写入哪个延迟队列
                    if (messageModel.FailToQueue == 0 || messageModel.FailToQueue == 1)
                    {
                        //为空或者01时，写入01延迟队列，并指定下次失败时，写入02队列
                        messageModel.FailToQueue = 2;
                        rst = RabbitMQService.SendMessage(messageModel, RabbitMQService.PurchaseOrderSendDelayMessageDescription01);
                    }
                    else if (messageModel.FailToQueue == 2)
                    {
                        //写入02队列，并设定，下次失败时进入03队列
                        messageModel.FailToQueue = 3;
                        rst = RabbitMQService.SendMessage(messageModel, RabbitMQService.PurchaseOrderSendDelayMessageDescription02);
                    }
                    else if (messageModel.FailToQueue == 3)
                    {
                        //写入03队列，并设定下次失败时进入04队列
                        messageModel.FailToQueue = 4;
                        rst = RabbitMQService.SendMessage(messageModel, RabbitMQService.PurchaseOrderSendDelayMessageDescription03);
                    }
                    else if (messageModel.FailToQueue == 4)
                    {
                        //写入04队列，并设定下次失败时进入05队列
                        messageModel.FailToQueue = 5;
                        rst = RabbitMQService.SendMessage(messageModel, RabbitMQService.PurchaseOrderSendDelayMessageDescription04);
                    }
                    else if (messageModel.FailToQueue == 5)
                    {
                        //写入05队列，并设定下次失败时进入死信队列
                        messageModel.FailToQueue = 6;
                        rst = RabbitMQService.SendMessage(messageModel, RabbitMQService.PurchaseOrderSendDelayMessageDescription05);
                    }
                    else
                    {
                        //写入死信队列
                        rst = RabbitMQService.SendMessage(messageModel, RabbitMQService.PurchaseOrderSendDeadLetterMessageDescription);
                    }
                }
                catch (Exception ex)
                {
                    Log.WriteError($"自动回流消费失败时写入延迟队列失败：{ex}");
                }
            }

            //最后还是失败
            if (rst == false)
            {
                //do something ...
            }
        }


        /// <summary>
        /// 消息消费失败，写入RabbitMq 延迟队列，做后续的重试
        /// </summary>
        public void FailToRabbitMqForBusinessMessage(List<MessageRecord> messages)
        {

            messages.ForEach(messageModel =>
            {

                var rst = false; //是否成功写入队列

                //以防队列写不进去，重试3次
                for (int i = 0; i < 3; i++)
                {
                    if (rst)
                        break;

                    try
                    {
                        //根据消息设置的消费失败时写入的队列属性，判断当前消息写入哪个延迟队列
                        if (messageModel.FailToQueue == 0 || messageModel.FailToQueue == 1)
                        {
                            //为空或者01时，写入01延迟队列，并指定下次失败时，写入02队列
                            messageModel.FailToQueue = 2;
                            rst = RabbitMQService.SendMessage(messageModel, RabbitMQService.FxBaseProductDelayMessageDescription01);
                        }
                        else if (messageModel.FailToQueue == 2)
                        {
                            //写入02队列，并设定，下次失败时进入03队列
                            messageModel.FailToQueue = 3;
                            rst = RabbitMQService.SendMessage(messageModel, RabbitMQService.FxBaseProductDelayMessageDescription02);
                        }
                        else if (messageModel.FailToQueue == 3)
                        {
                            //写入03队列，并设定下次失败时进入04队列
                            messageModel.FailToQueue = 4;
                            rst = RabbitMQService.SendMessage(messageModel, RabbitMQService.FxBaseProductDelayMessageDescription03);
                        }
                        else if (messageModel.FailToQueue == 4)
                        {
                            //写入04队列，并设定下次失败时进入05队列
                            messageModel.FailToQueue = 5;
                            rst = RabbitMQService.SendMessage(messageModel, RabbitMQService.FxBaseProductDelayMessageDescription04);
                        }
                        else if (messageModel.FailToQueue == 5)
                        {
                            //写入05队列，并设定下次失败时进入死信队列
                            messageModel.FailToQueue = 6;
                            rst = RabbitMQService.SendMessage(messageModel, RabbitMQService.FxBaseProductDelayMessageDescription05);
                        }
                        else
                        {
                            //写入死信队列
                            rst = RabbitMQService.SendMessage(messageModel, RabbitMQService.FxBaseProductDeadLetterMessageDescription);
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"基础商品消息消费失败时写入延迟队列失败：{ex}");
                    }
                }

                //最后还是失败
                if (rst == false)
                {
                    //do something ...
                }

            });
        }

        /// <summary>
        /// 消息消费失败，写入RabbitMq 延迟队列，做后续的重试
        /// </summary>
        public void CloudMsgFailToRabbitMq<T>(List<T> messages, MessageDescription[] delayDescriptions, MessageDescription deadLetterDescription) where T : CloudMessage
        {
            messages.ForEach(message =>
            {
                var rst = false;
                for (int i = 0; i < 3; i++)
                {
                    if (rst)
                        break;
                    try
                    {

                        if (message.FailToQueue >= 0 && message.FailToQueue < delayDescriptions.Length)
                        {
                            var index = message.FailToQueue;
                            if (message.FailToQueue < 2)
                                index = 1;

                            message.FailToQueue++;


                            rst = RabbitMQService.SendMessage(message, delayDescriptions[index-1]);

                        }
                        else if (deadLetterDescription != null)
                        {
                            rst = RabbitMQService.SendMessage(message, deadLetterDescription);
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"{typeof(T).Name}消息消费失败时写入延迟队列失败：{ex}");
                    }

                }
                if (!rst)
                {
                    // 处理最后仍然失败的情况
                    if (typeof(T) == typeof(ProfitStatisticsCloudMessage))
                    {
                        // do something ... 
                    }
                }
            });

        }

        /// <summary>
        /// 处理推送给阿里云日志的消息（分单系统）
        /// </summary>
        /// <param name="msgs"></param>
        /// <returns></returns>
        public bool ProcessFxAliLogMessage(List<string> msgs)
        {
            try
            {
                var models = msgs?.Distinct().Select(msg => msg.ToObject<DbExecuteLogMsgModel>()).ToList();
                if (models == null || !models.Any())
                    return true;

                var logservice = new AliLogService();
                foreach (var model in models)
                {
                    var data = new List<IAliPutLogs>();
                    data.AddRange(model.Data);
                    var aliLog = new AliPutLogs<IAliPutLogs>
                    {
                        Project = model.Project,
                        Logstore = model.Logstore,
                        Topic = model.Topic,
                        Data = data
                    };
                    logservice.PutLog(aliLog);
                }

            }
            catch (Exception ex)
            {
                Log.WriteError($"处理消息时发生错误，消息内容：{msgs?.ToJson()}，错误信息：{ex}");
            }
            return true;
        }

        public ReturnedModel Verification(Tuple<UserFx, Shop> tuple, string shopId)
        {
            var returned = new ReturnedModel();
            //针对非手机号的，忽略消息
            if (!tuple.Item1.Mobile.IsMobile())
            {
                var message = $"未绑定手机号，忽略此消息：ShopID:{shopId}，userFx.Mobile：{tuple.Item1.Mobile}";
                Log.WriteWarning(message);

                returned.Success = false;
                returned.Message = message;
                returned.Status = ReturnedStatus.Fail;
                return returned;
            }

            var shop = tuple.Item2;
            if (shop.OrderSyncStatus != null && shop.OrderSyncStatus.ShopIsExpired)
            {
                var message = string.Empty;

                if (shop.PlatformType != PlatformType.KuaiShou.ToString())
                {
                    message =
                        $"店铺信息无效：ShopID:{shopId}，CreateTime：{shop?.CreateTime} ,LastSyncTime:{shop?.LastSyncTime} ,IsAuthExpired:{shop?.IsAuthExpired}";
                    Log.WriteWarning(message);
                }
                returned.Success = false;
                returned.Message = message;
                returned.Status = ReturnedStatus.Fail;
                return returned;
            }

            return returned;
        }

        /// <summary>
        /// 链路监控收集
        /// </summary>
        /// <param name="syncOrderService"></param>
        /// <param name="platformMessages"></param>
        /// <param name="shop"></param>
        /// <param name="traceStatus"></param>
        /// <param name="message"></param>
        public void TraceDataCollect(SyncFxOrderService syncOrderService, List<PlatformMessageModel> platformMessages,
            Shop shop, string traceStatus, string message = "")
        {
            foreach (var platformMessage in platformMessages)
            {
                if (!string.IsNullOrEmpty(platformMessage.TID) && !platformMessage.TID.StartsWith("C"))
                {
                    syncOrderService.TraceDataCollect(new TraceDataModel<TraceDataMetaDataModel>
                    {
                        BatchId = platformMessage.BatchId,
                        ShopId = shop.Id,
                        BusinessId = platformMessage.TID,
                        CloudPlatformType = CustomerConfig.CloudPlatformType,
                        PlatformType = shop.PlatformType,
                        OperationType = TraceOperationType.OrderMessagePush.ToString(),
                        TraceType = TraceType.MessageVerification.ToString(),
                        CreateTime = DateTime.Now,
                        TraceStatus = traceStatus,
                        Message = message,
                        MetaData = new TraceDataMetaDataModel()
                    });
                }
            }
        }

        /// <summary>
        /// 批量同步订单/售后订单
        /// </summary>
        /// <param name="userFx_PlatformShop_SystemShop"></param>
        /// <param name="models"></param>
        /// <param name="isOnlyAfterSale">是否只有售后单消息，为true时：不需要处理主客系统，不需要移除Redis</param>
        /// <returns></returns>
        private bool BulkSyncFxOrder(List<Tuple<UserFx, Shop>> userFx_PlatformShop_SystemShop, List<PlatformMessageModel> models, bool isOnlyAfterSale = false)
        {
            var gs = models.GroupBy(m => m.MID).ToList();
            gs.ForEach(g =>
            {
                var tuple = userFx_PlatformShop_SystemShop.FirstOrDefault(s => s.Item2?.ShopId == g.Key);
                if (tuple == null || tuple.Item1 == null || tuple.Item2 == null) return;
                //店铺没过期就处理消息
                var userFx = tuple.Item1;
                var sc = new SiteContext(userFx, new SiteContextConfig { NeedRelationShops = false, NeedShopExpireTime = false });
                var syncFxOrderService = new SyncFxOrderService(userFx.Id);
                var shop = tuple.Item2;
                //消息
                var platformMessages = g.ToList();
                //针对非手机号的，忽略消息
                var returned = Verification(tuple, g.Key);
                if (!returned.Success)
                {
                    TraceDataCollect(syncFxOrderService, platformMessages, shop, TraceStatus.Success);
                }
                else
                {
                    TraceDataCollect(syncFxOrderService, platformMessages, shop, TraceStatus.Fail, returned.Message);
                }

                var orderTagOrderService = new OrderTagService();
                //Log.WriteLine($"ProcessBuyerFxMessage.Step11，models:{models?.ToJson(true)}，db={orderTagOrderService.baseRepository?.DbConnection?.Database}", $"1688-buyer-{DateTime.Now.ToString("yyyyMMdd")}.txt");
                //同步订单，售后单
                foreach (var platformMessage in platformMessages)
                {
                    //售后处理，同步售后单时会同步订单
                    if (isOnlyAfterSale || IsAfterSaleOrder(platformMessage))
                    {
                        try
                        {
                            if (shop.PlatformType == PlatformType.TouTiao.ToString2())
                            {
                                Log.Debug(() => $"【消息】抖店同步售后订单1 afterSaleId={platformMessage.AfterSaleId}", "AfterSaleAddres.txt");
                            }

                            var afterSaleOrders = syncFxOrderService.SyncAfterSaleSingleOrder(platformMessage.AfterSaleId, shop, true);
                        }
                        catch (Exception ex)
                        {
                            Log.WriteWarning($"同步单个售后订单时发生错误，售后订单ID:{platformMessage.AfterSaleId}：{ex}");
                        }

                        //更新退款状态
                        if (platformMessage.PT == PlatformType.TouTiao.ToString() && IsNeedUpdateOrderRefundStatus(platformMessage))
                        {
                            _fxTouTiaoMessageProcessor.ProcessRefundMessage(platformMessage, tuple);
                        }

                        if (platformMessage.PT == PlatformType.Alibaba.ToString())
                        {
                            var order = syncFxOrderService.SyncSingleOrder(platformMessage.TID, shop, batchId: platformMessage.BatchId);

                            Log.WriteLine($"售后单消息里SyncSingleOrder完成，TID:{platformMessage.TID}，order={order?.PlatformOrderId}", $"1688-SyncSingleOrder-{DateTime.Now.ToString("yyyyMMdd")}.txt");
                        }
                    }
                    else if (!string.IsNullOrEmpty(platformMessage.TID) && !platformMessage.TID.StartsWith("C"))
                    {
                        //快手-订单发货时效变更消息
                        if (platformMessage.ASS == "ks_deliveryeffect")
                        {
                            if (string.IsNullOrEmpty(platformMessage.Content))
                                platformMessage.Content = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                            orderTagOrderService.BulkInsert(new List<OrderTags>
                            {
                                new OrderTags
                                {
                                    OiCode = platformMessage.TID,
                                    Platform = platformMessage.PT,
                                    TagType = TagType.Order.ToString(),
                                    Tag = OrderTag.remind_shipment.ToString(),
                                    TagValue = platformMessage.Content, //催发时间
                                    Status = 0
                                }
                            });
                        }
                        else
                        {
                            try
                            {
                                //Log.WriteLine($"ProcessBuyerFxMessage.SyncSingleOrder开始，TID:{platformMessage.TID}", $"1688-buyer-{DateTime.Now.ToString("yyyyMMdd")}.txt");

                                var order = syncFxOrderService.SyncSingleOrder(platformMessage.TID, shop, batchId: platformMessage.BatchId);
                              
                                //Log.WriteLine($"SyncSingleOrder完成，TID:{platformMessage.TID}，order={order?.ToJson(true)}", $"SyncSingleOrder-{DateTime.Now.ToString("yyyyMMdd")}.txt");
                            }
                            catch (Exception ex)
                            {
                                Log.WriteWarning($"同步单个订单时发生错误，订单ID:{platformMessage.TID}，{platformMessage.MID}，{shop.Id}：{ex}");
                            }
                        }
                    }
                }

                try
                {
                    if (!isOnlyAfterSale)
                    {
                        //2023-12-04,新增配置，只要开启才会转发到铺货
                        if (IsTransmitMessageToZk())
                        {
                            //判断订单所属的店铺是否被加入主客铺货系统，加入主客铺货系统，则需要把订单消息转发到订单铺货的订单队列
                            var isExistsZhuKeSystem = (new ShopService()).ShopExistsZhuKeSystem(shop.Id);
                            if (isExistsZhuKeSystem)
                            {
                                //头条分单应用转发到住客铺货头条订单消息
                                var touTiaoZkOrderDesc = RabbitMQService.TouTiaoZkOrderMessageDescription;
                                //发送到消息队列
                                foreach (var msgModel in models)
                                {
                                    var isSuccess = RabbitMQService.SendMessage(msgModel, touTiaoZkOrderDesc);
                                    if (isSuccess == false)
                                    {
                                        Log.WriteWarning($"头条分单应用订单消息转发到订单铺货的订单队列失败,队列写入返回false，消息内容：{models.ToJson()}");
                                    }
                                }
                            }
                        }
                    }

                }
                catch (Exception ex)
                {
                    Log.WriteWarning($"头条分单应用订单消息转发到订单铺货的订单队列失败，消息内容：{models.ToJson()}，错误详情：{ex}");
                }
            });
            if (!isOnlyAfterSale)
                RemoveKeysFromRedis(models);
            return true;
        }
        /// <summary>
        /// 是否需要更新订单退款状态
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        private bool IsNeedUpdateOrderRefundStatus(PlatformMessageModel model)
        {
            var states = new[] { "200", "201", "202", "204", "205", "206", "207", "208" };
            return states.Contains(model.State);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userFx_PlatformShop_SystemShop"></param>
        /// <param name="models"></param>
        /// <returns></returns>
        private bool BulkSyncFxProduct(List<Tuple<UserFx, Shop>> userFx_PlatformShop_SystemShop, List<PlatformMessageModel> models)
        {
            var gs = models.GroupBy(m => m.MID).ToList();
            var _userFxService = new UserFxService();
            gs.ForEach(g =>
            {
                var tuple = userFx_PlatformShop_SystemShop.FirstOrDefault(s => s.Item2?.ShopId == g.Key);
                if (tuple == null || tuple.Item1 == null || tuple.Item2 == null) return;

                //针对非手机号的，忽略消息
                if (!tuple.Item1.Mobile.IsMobile())
                {
                    Log.WriteWarning($"未绑定手机号，忽略此消息：ShopID:{g.Key}，userFx.Mobile：{tuple.Item1.Mobile}");
                    return;
                }

                var shop = tuple.Item2;
                if (shop.OrderSyncStatus.ShopIsExpired)
                {
                    if (shop.PlatformType != PlatformType.KuaiShou.ToString())
                        Log.WriteWarning($"店铺信息无效：ShopID:{g.Key}，CreateTime：{shop?.CreateTime} ,LastSyncTime:{shop?.LastSyncTime} ,IsAuthExpired:{shop?.IsAuthExpired}");
                    return;
                }
                //店铺没过期就处理消息
                var userFx = tuple.Item1;
                var sc = new SiteContext(userFx, new SiteContextConfig { NeedRelationShops = false, NeedShopExpireTime = false });
                var _syncProductService = new SyncFxProductService(userFx.Id);
                var service = PlatformFactory.GetPlatformService(shop);
                var temps = g.ToList();
                var products = new ConcurrentBag<Product>();
                Parallel.ForEach(temps, new ParallelOptions { MaxDegreeOfParallelism = 10 }, temp =>
                {
                    if (!string.IsNullOrEmpty(temp.PID))
                    {
                        var pids = temp.PID.Split(',');
                        foreach (var pid in pids)
                        {
                            var tempProduct = _syncProductService.SyncSingleProduct(service, pid);
                            if (tempProduct != null)
                                products.Add(tempProduct);
                        }
                    }
                });
                try
                {
                    //平台商品，转换成ProductFx的表
                    //var systemShopId = _userFxService.GetSystemShopIdByFxUserId(tuple.Item1.Id);
                    var productFxList = _syncProductService.productService.TransferModelToProductFx(products.ToList(), tuple.Item1.Id, shop.PlatformType);
                    _syncProductService.productService.BulkMerger(productFxList, shop.Id, isBindSupplier: true); // [20250220] 批量同步商品/订单信息（分单系统）消息队列，同步订单和商品
                }
                catch (Exception ex)
                {
                    Log.WriteWarning($"处理消息完成后保存产品时发生错误，消息内容：{models.ToJson()}，错误详情：{ex}");
                }
            });
            RemoveKeysFromRedis(models);
            return true;
        }

        private void RemoveKeysFromRedis(List<PlatformMessageModel> models)
        {
            return;
            ////移除Redis缓存的key
            //models.ForEach(m =>
            //{
            //    if (!string.IsNullOrEmpty(m.Key))
            //    {
            //        try
            //        {
            //            RedisService.RemoveKey(m.Key);
            //        }
            //        catch (Exception ex)
            //        {
            //            Log.WriteError($"从Redis上移除Key：{m.Key}发生错误：{ex}");
            //        }
            //    }
            //});
        }

        /// <summary>
        /// 是否是售后单消息且需处理的平台
        /// </summary>
        /// <param name="temp"></param>
        /// <returns></returns>
        private bool IsAfterSaleOrder(PlatformMessageModel temp)
        {
            if (string.IsNullOrEmpty(temp.AfterSaleId))
                return false;
            //头条
            //211=卖家收到买家换货包裹，确认换货并二次发货消息
            //208=买家修改售后申请消息
            //207=售后关闭消息
            //206=退款成功消息
            //203=买家退货给卖家消息
            //200=买家发起售后申请消息
            var state = temp.State.ToInt();
            if (state >= 200 && state < 300)
                return true;

            //支持售后的店铺平台，为空表示所有
            var needAfterSalePlatfrom = CustomerConfig.GetNeedAfterSalePlatform();
            if (needAfterSalePlatfrom == null || needAfterSalePlatfrom.Count() == 0)
                return true;

            if (needAfterSalePlatfrom.Contains(temp.PT))
                return true;

            return false;
        }

        /// <summary>
        /// 售后单消息处理-针对分单系统
        /// </summary>
        /// <param name="asMessages"></param>
        /// <returns></returns>
        private bool HandleAfterSaleOrderFx(List<PlatformMessageModel> asMessages)
        {
            if (asMessages == null || !asMessages.Any())
                return true;
            try
            {
                var mids = asMessages.Select(m => m.MID).Distinct()?.Where(mid => !string.IsNullOrEmpty(mid))?.ToList();
                if (mids == null || !mids.Any())
                    return true;
                var _userFxService = new UserFxService();
                var userFxAndShopList = _userFxService.GetUserFxAndShopsByShopIds(mids, null); //根据店铺的平台ID查询出店铺所属的商家用户
                if (userFxAndShopList == null || userFxAndShopList.Any() == false)
                    return true;
                return BulkSyncFxOrder(userFxAndShopList, asMessages, isOnlyAfterSale: true);
            }
            catch (Exception ex)
            {
                Log.WriteError($"售后单消息处理发生错误，消息内容：{asMessages.ToJson()}，错误详情：{ex}");
            }
            return true;
        }
        #endregion

        #region 调试模式 添加日志监听

        delegate void SetLogDelegate(string text);
        public void SetLog(string text)
        {
            if (DebugModeChk.Checked)
            {
                if (this.LogList.TextLength > 1024 * 10)
                    this.LogList.Clear();
                this.LogList.AppendText(text);
                //if(LogAuotScrollEnabled.Checked)
                LogList.ScrollToCaret();
            }
        }
        private bool IsLogEventListened;
        private void OpenDebugMode()
        {
            //开启读取日志的线程
            if (DebugModeChk.Checked && !IsLogEventListened)
            {
                var d = new SetLogDelegate(SetLog);
                Log.OnLogMessage += new Log.OnLogMessageDelegate(s =>
                {
                    if (!DebugModeChk.Checked)
                        return "";
                    this.Invoke(d, s);
                    return s;
                });
                IsLogEventListened = true;
            }
        }

        private void DebugModeChk_CheckedChanged(object sender, EventArgs e)
        {
            OpenDebugMode();
        }

        #endregion

        #region 窗口相关
        /// <summary>
        /// 阻止关闭
        /// </summary>
        /// <param name="m"></param>
        protected override void DefWndProc(ref System.Windows.Forms.Message m)
        {
            const int WM_SYSCOMMAND = 0x0112;

            const int SC_CLOSE = 0xF060;
            if (m.Msg == WM_SYSCOMMAND && (int)m.WParam == SC_CLOSE)
            {
                this.WindowState = FormWindowState.Minimized;
                ShowInTaskbar = true;
                ShowIcon = true;
                return;
            }
            base.DefWndProc(ref m);
        }

        private void 退出ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show("确定退出吗？", "退出", MessageBoxButtons.OKCancel, MessageBoxIcon.Question);
            //MessageBox.Show("")
            if (result == DialogResult.OK)
            {
                //彻底退出，清理所有后台线程。
                System.Environment.Exit(0);
            }
            else
            {
                return;
            }
        }
        private void notifyIcon1_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            this.WindowState = FormWindowState.Normal;
            ShowInTaskbar = true;
            ShowIcon = true;
        }
        private void SyncOrder_FormClosing(object sender, FormClosingEventArgs e)
        {
            HideProgram();
        }

        /// <summary>
        /// 隐藏程序到托盘
        /// </summary>
        private void HideProgram()
        {
            this.WindowState = FormWindowState.Minimized;
            ShowInTaskbar = true;
            ShowIcon = true;
        }
        #endregion

        #region 注册自动启动

        /// <summary>
        /// 判断是否开机启动
        /// </summary>
        /// <param name="strAppPath">应用程序路径</param>
        /// <param name="strAppName">应用程序名称</param>
        /// <returns></returns>
        public static bool IsAutoRun(string strAppPath, string strAppName)
        {
            try
            {
                var reg = Registry.LocalMachine;
                var software = reg.OpenSubKey(@"SOFTWARE");
                var run = reg.OpenSubKey(@"SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run\");
                var key = run.GetValue(strAppName)?.ToString();
                software.Close();
                run.Close();
                if (!string.IsNullOrEmpty(key) && strAppPath.ToLower() == key.ToLower())
                    return true;
            }
            catch (Exception ex)
            {
                Log.WriteError($"检测是否注册开机启动时失败：{ex}");
            }
            return false;
        }


        private void AutoRun()
        {
            try
            {
                //程序运行位置
                var path = Application.ExecutablePath;
                if (!IsAutoRun(path, this.Name))
                {
                    //对应于HKEY_LOCAL_MACHINE主键
                    var localKey = Registry.LocalMachine;
                    //开机自动运行
                    var localPath = localKey.CreateSubKey("SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run");
                    localPath.SetValue(this.Name, path);
                    localPath.Close();
                    localKey.Close();
                    Log.WriteLine("注册开机启动成功");
                }
                else
                {
                    Log.WriteLine("已注册开机启动");
                }
            }
            catch (Exception ex)
            {
                Log.WriteError("请以管理员身份运行，否则电脑重启后无法自动启动");
            }
            finally
            {
                startBtn_Click(null, null);
            }
        }

        private void SyncOrder_Load(object sender, EventArgs e)
        {
            AutoRun();
        }

        #endregion

    }
}
