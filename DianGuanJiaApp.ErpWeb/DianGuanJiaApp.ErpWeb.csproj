<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.Default.props" Condition="Exists('$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.Default.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{8B8F2526-E5FA-4BBF-9BB0-7EA802F2EEF7}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>DianGuanJiaApp.ErpWeb</RootNamespace>
    <AssemblyName>DianGuanJiaApp.ErpWeb</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <MvcBuildViews>false</MvcBuildViews>
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress />
    <IISExpressSSLPort>44386</IISExpressSSLPort>
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <TargetFrameworkProfile />
    <TypeScriptToolsVersion>1.8</TypeScriptToolsVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>bin\DianGuanJiaApp.ErpWeb.xml</DocumentationFile>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="CSRedisCore, Version=3.8.670.0, Culture=neutral, PublicKeyToken=9aa6a3079358d437, processorArchitecture=MSIL">
      <HintPath>..\packages\CSRedisCore.3.8.670\lib\net45\CSRedisCore.dll</HintPath>
    </Reference>
    <Reference Include="Google.Protobuf, Version=3.22.5.0, Culture=neutral, PublicKeyToken=a7d26565bac4d604, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Protobuf.3.22.5\lib\net45\Google.Protobuf.dll</HintPath>
    </Reference>
    <Reference Include="Grpc.Core, Version=*******, Culture=neutral, PublicKeyToken=d754f35622e28bad, processorArchitecture=MSIL">
      <HintPath>..\packages\Grpc.Core.2.44.0\lib\net45\Grpc.Core.dll</HintPath>
    </Reference>
    <Reference Include="Grpc.Core.Api, Version=*******, Culture=neutral, PublicKeyToken=d754f35622e28bad, processorArchitecture=MSIL">
      <HintPath>..\packages\Grpc.Core.Api.2.44.0\lib\net45\Grpc.Core.Api.dll</HintPath>
    </Reference>
    <Reference Include="ICSharpCode.SharpZipLib, Version=0.86.0.518, Culture=neutral, PublicKeyToken=1b03e6acf1164f73, processorArchitecture=MSIL">
      <HintPath>..\packages\SharpZipLib.0.86.0\lib\20\ICSharpCode.SharpZipLib.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Http.Features, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Http.Features.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Http.Features.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.9.0.0\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Authorization.Policy, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Authorization.Policy.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Authorization.Policy.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Hosting.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Hosting.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Hosting.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Hosting.Server.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Hosting.Server.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Hosting.Server.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Http, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Http.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Http.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Http.Extensions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Http.Extensions.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Http.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Mvc.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Mvc.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Mvc.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Mvc.Core, Version=2.2.5.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Mvc.Core.2.2.5\lib\netstandard2.0\Microsoft.AspNetCore.Mvc.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.ResponseCaching.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.ResponseCaching.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.ResponseCaching.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Routing, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Routing.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Routing.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Routing.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Routing.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Routing.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.WebUtilities, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.WebUtilities.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.WebUtilities.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=3.6.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.3.6.0\lib\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.Extensions.Configuration, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.9.0.0\lib\net462\Microsoft.Extensions.Configuration.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.Abstractions.9.0.0\lib\net462\Microsoft.Extensions.Configuration.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.Binder, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.Binder.9.0.0\lib\net462\Microsoft.Extensions.Configuration.Binder.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.9.0.0\lib\net462\Microsoft.Extensions.DependencyInjection.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.Abstractions.9.0.0\lib\net462\Microsoft.Extensions.DependencyInjection.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Diagnostics.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Diagnostics.Abstractions.9.0.0\lib\net462\Microsoft.Extensions.Diagnostics.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.9.0.0\lib\net462\Microsoft.Extensions.Logging.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.Abstractions.9.0.0\lib\net462\Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging.Configuration, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.Configuration.9.0.0\lib\net462\Microsoft.Extensions.Logging.Configuration.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Options, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Options.9.0.0\lib\net462\Microsoft.Extensions.Options.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Options.ConfigurationExtensions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Options.ConfigurationExtensions.9.0.0\lib\net462\Microsoft.Extensions.Options.ConfigurationExtensions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Primitives, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Primitives.9.0.0\lib\net462\Microsoft.Extensions.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="MongoDB.Bson, Version=2.7.3.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\MongoDB.Bson.2.7.3\lib\net45\MongoDB.Bson.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=1*******, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json.Bson, Version=*******, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.Bson.1.0.2\lib\net45\Newtonsoft.Json.Bson.dll</HintPath>
    </Reference>
    <Reference Include="NPOI, Version=2.4.0.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.4.0\lib\net40\NPOI.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OOXML, Version=2.4.0.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.4.0\lib\net40\NPOI.OOXML.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OpenXml4Net, Version=2.4.0.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.4.0\lib\net40\NPOI.OpenXml4Net.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OpenXmlFormats, Version=2.4.0.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.4.0\lib\net40\NPOI.OpenXmlFormats.dll</HintPath>
    </Reference>
    <Reference Include="OpenTelemetry, Version=0.0.0.0, Culture=neutral, PublicKeyToken=7bd6737fe5b67e3c, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Build\OpenTelemetryPackage\OpenTelemetry.dll</HintPath>
    </Reference>
    <Reference Include="OpenTelemetry.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=7bd6737fe5b67e3c, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Build\OpenTelemetryPackage\OpenTelemetry.Api.dll</HintPath>
    </Reference>
    <Reference Include="OpenTelemetry.Api.ProviderBuilderExtensions, Version=0.0.0.0, Culture=neutral, PublicKeyToken=7bd6737fe5b67e3c, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Build\OpenTelemetryPackage\OpenTelemetry.Api.ProviderBuilderExtensions.dll</HintPath>
    </Reference>
    <Reference Include="OpenTelemetry.Exporter.OpenTelemetryProtocol, Version=0.0.0.0, Culture=neutral, PublicKeyToken=7bd6737fe5b67e3c, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Build\OpenTelemetryPackage\OpenTelemetry.Exporter.OpenTelemetryProtocol.dll</HintPath>
    </Reference>
    <Reference Include="OpenTelemetry.Instrumentation.AspNet, Version=0.0.0.0, Culture=neutral, PublicKeyToken=7bd6737fe5b67e3c, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Build\OpenTelemetryPackage\OpenTelemetry.Instrumentation.AspNet.dll</HintPath>
    </Reference>
    <Reference Include="OpenTelemetry.Instrumentation.AspNet.TelemetryHttpModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=7bd6737fe5b67e3c, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Build\OpenTelemetryPackage\OpenTelemetry.Instrumentation.AspNet.TelemetryHttpModule.dll</HintPath>
    </Reference>
    <Reference Include="OpenTelemetry.Instrumentation.Http, Version=0.0.0.0, Culture=neutral, PublicKeyToken=7bd6737fe5b67e3c, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Build\OpenTelemetryPackage\OpenTelemetry.Instrumentation.Http.dll</HintPath>
    </Reference>
    <Reference Include="OpenTelemetry.Instrumentation.SqlClient, Version=0.0.0.0, Culture=neutral, PublicKeyToken=7bd6737fe5b67e3c, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Build\OpenTelemetryPackage\OpenTelemetry.Instrumentation.SqlClient.dll</HintPath>
    </Reference>
    <Reference Include="Swashbuckle.Core, Version=*******, Culture=neutral, PublicKeyToken=cd1bb07a5ac7c7bc, processorArchitecture=MSIL">
      <HintPath>..\packages\Swashbuckle.Core.5.6.0\lib\net40\Swashbuckle.Core.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ComponentModel.Annotations.4.5.0\lib\net461\System.ComponentModel.Annotations.dll</HintPath>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.DiagnosticSource.9.0.0\lib\net462\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing" />
    <Reference Include="System.IO.Pipelines, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Pipelines.5.0.2\lib\net461\System.IO.Pipelines.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.Formatting, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.6.0.0\lib\net45\System.Net.Http.Formatting.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.InteropServices.RuntimeInformation.4.3.0\lib\net45\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Text.Encodings.Web, Version=8.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.8.0.0\lib\net462\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net461\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Abstractions" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Http, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Core.5.3.0\lib\net45\System.Web.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.WebHost, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.WebHost.5.3.0\lib\net45\System.Web.Http.WebHost.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Routing" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="Microsoft.Web.Infrastructure, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.Web.Infrastructure.*******\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http">
    </Reference>
    <Reference Include="System.Net.Http.WebRequest">
    </Reference>
    <Reference Include="System.Web.Helpers, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=5.2.7.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.7\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Optimization">
      <HintPath>..\packages\Microsoft.AspNet.Web.Optimization.1.1.3\lib\net40\System.Web.Optimization.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.7\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="Volcengine.TOS, Version=2.1.2.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Volcengine.TOS.SDK.2.1.2\lib\net45\Volcengine.TOS.dll</HintPath>
    </Reference>
    <Reference Include="WebActivatorEx, Version=*******, Culture=neutral, PublicKeyToken=7b26dc2a43f6a0d4, processorArchitecture=MSIL">
      <HintPath>..\packages\WebActivatorEx.2.0\lib\net40\WebActivatorEx.dll</HintPath>
    </Reference>
    <Reference Include="WebGrease">
      <Private>True</Private>
      <HintPath>..\packages\WebGrease.1.6.0\lib\WebGrease.dll</HintPath>
    </Reference>
    <Reference Include="Antlr3.Runtime">
      <Private>True</Private>
      <HintPath>..\packages\Antlr.3.5.0.2\lib\Antlr3.Runtime.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ApiControllers\CombinedProductChangeLogController.cs" />
    <Compile Include="ApiControllers\InstantRetailController.cs" />
    <Compile Include="ApiControllers\StockController.cs" />
    <Compile Include="ApiControllers\BaseProductController.cs" />
    <Compile Include="ApiControllers\BaseApiController.cs" />
    <Compile Include="ApiControllers\ApiDemoController.cs" />
    <Compile Include="ApiControllers\ProfitStatisticsController.cs" />
    <Compile Include="ApiControllers\FreightTemplateController.cs" />
    <Compile Include="ApiControllers\Collect\ClaimProductController.cs" />
    <Compile Include="ApiControllers\Collect\CollectApiController.cs" />
    <Compile Include="ApiControllers\Collect\GlobalProductController.cs" />
    <Compile Include="ApiControllers\ListingProduct\PtProductInfoController.cs" />
    <Compile Include="ApiControllers\ListingProduct\ListingController.cs" />
    <Compile Include="ApiControllers\CommonController.cs" />
    <Compile Include="ApiControllers\MemberLevelController.cs" />
    <Compile Include="ApiControllers\ListingProduct\PtProductInfoDraftController.cs" />
    <Compile Include="ApiControllers\ProductSkuHistoryController.cs" />
    <Compile Include="ApiControllers\PhApiBaseController.cs" />
    <Compile Include="ApiControllers\PhGlobalProductController.cs" />
    <Compile Include="ApiControllers\StationController.cs" />
    <Compile Include="ApiControllers\SupplierProductController.cs" />
    <Compile Include="ApiControllers\ListingProduct\ListingTemplateGroupController.cs" />
    <Compile Include="ApiControllers\SystemController.cs" />
    <Compile Include="Api\ApiBaseController.cs" />
    <Compile Include="Api\BaseProductApiController.cs" />
    <Compile Include="Api\ChangePrePayApiController.cs" />
    <Compile Include="Api\CheckRuleApiController.cs" />
    <Compile Include="Api\ImageFileController.cs" />
    <Compile Include="Api\OtlpApiController.cs" />
    <Compile Include="Api\ProductApiController.cs" />
    <Compile Include="ApiControllers\PhClaimProductController.cs" />
    <Compile Include="Api\WebApiDemo.cs" />
    <Compile Include="Api\PartnerApiController.cs" />
    <Compile Include="Api\CommonApiController.cs" />
    <Compile Include="Api\QuickSearchApiController.cs" />
    <Compile Include="Api\PrintHistoryApiController.cs" />
    <Compile Include="Api\UpdateReceiverInfoApiController.cs" />
    <Compile Include="Api\SellerInfoApiController.cs" />
    <Compile Include="Api\OrderSyncApiController.cs" />
    <Compile Include="Api\WebHookTransApiController.cs" />
    <Compile Include="App_Start\AntiCrawlerFilterAttribute.cs" />
    <Compile Include="App_Start\FxAuthorize.cs" />
    <Compile Include="App_Start\ApiExceptionFilter.cs" />
    <Compile Include="App_Start\FxMigrateLockFilter.cs" />
    <Compile Include="App_Start\DefaultApiAuthorizationFilter.cs" />
    <Compile Include="App_Start\FxWhiteUserFilter.cs" />
    <Compile Include="App_Start\FxWhiteUserFilterApi.cs" />
    <Compile Include="App_Start\LargeJsonValueProviderFactory.cs" />
    <Compile Include="App_Start\PageDepthControlFilter.cs" />
    <Compile Include="App_Start\WebApiConfig.cs" />
    <Compile Include="App_Start\SwaggerConfig.cs" />
    <Compile Include="App_Start\TemplateModuleBundleConfig.cs" />
    <Compile Include="App_Start\BundleConfig.cs" />
    <Compile Include="App_Start\LogForOperatorFilter.cs" />
    <Compile Include="App_Start\FilterConfig.cs" />
    <Compile Include="App_Start\RouteConfig.cs" />
    <Compile Include="Controllers\ProfitStatisiticsController.cs" />
    <Compile Include="Controllers\BaseProductController.cs" />
    <Compile Include="Controllers\CollectController.cs" />
    <Compile Include="Controllers\ExportTaskController.cs" />
    <Compile Include="Controllers\BusinessSettingsController.cs" />
    <Compile Include="Controllers\FundsManagement.cs" />
    <Compile Include="Controllers\GlobalProductController.cs" />
    <Compile Include="Controllers\CrossBorderController.cs" />
    <Compile Include="Controllers\ShopVideoController.cs" />
    <Compile Include="Controllers\SiteMessageController.cs" />
    <Compile Include="Controllers\AbnormalProductChangeController.cs" />
    <Compile Include="Controllers\OrderLifeCycleToolController.cs" />
    <Compile Include="Controllers\FreightTemplateController.cs" />
    <Compile Include="Controllers\TkOrderPrintController.cs" />
    <Compile Include="Controllers\PaymentStatementController.cs" />
    <Compile Include="Controllers\AbnormalOrderController.cs" />
    <Compile Include="Controllers\PurchaseOrderController.cs" />
    <Compile Include="Controllers\DistributionProductController.cs" />
    <Compile Include="Controllers\ScanPrintController.cs" />
    <Compile Include="Controllers\ShippingFeeTemplateController.cs" />
    <Compile Include="Controllers\StationController.cs" />
    <Compile Include="Controllers\SupplySet1688Controller.cs" />
    <Compile Include="Controllers\FFAccountController.cs" />
    <Compile Include="Controllers\FxCachingController.cs" />
    <Compile Include="Controllers\MobileApiController.cs" />
    <Compile Include="Controllers\FxAccountController.cs" />
    <Compile Include="Controllers\SendOrderController.cs" />
    <Compile Include="Controllers\MessageController.cs" />
    <Compile Include="Controllers\AccountListController.cs" />
    <Compile Include="Controllers\BuyerHashCodeUpdateController.cs" />
    <Compile Include="Controllers\CommonController.cs" />
    <Compile Include="Controllers\ExpressBillController.cs" />
    <Compile Include="Controllers\FinancialSettlementController.cs" />
    <Compile Include="Controllers\GeneralizeIndexController.cs" />
    <Compile Include="Controllers\AfterSaleController.cs" />
    <Compile Include="Controllers\PhoneQrCodeController.cs" />
    <Compile Include="Controllers\PurchasesController.cs" />
    <Compile Include="Controllers\SetInfoController.cs" />
    <Compile Include="Controllers\SharedWaybillAccountCheckController.cs" />
    <Compile Include="Controllers\SendGoodTemplateController.cs" />
    <Compile Include="Controllers\ShareWayBillAccountController.cs" />
    <Compile Include="Controllers\StockControlController.cs" />
    <Compile Include="Controllers\LogHistoryController.cs" />
    <Compile Include="Controllers\PrintConfigController.cs" />
    <Compile Include="Controllers\PrintHistoryController.cs" />
    <Compile Include="Controllers\SellerInfoController.cs" />
    <Compile Include="Controllers\SendHistoryController.cs" />
    <Compile Include="Controllers\SubAccountController.cs" />
    <Compile Include="Controllers\SystemController.cs" />
    <Compile Include="Controllers\ProductController.cs" />
    <Compile Include="Controllers\HomeController.cs" />
    <Compile Include="Controllers\AuthController.cs" />
    <Compile Include="Controllers\BaseController.cs" />
    <Compile Include="Controllers\NewOrderController.cs" />
    <Compile Include="Controllers\TemplateSetController.cs" />
    <Compile Include="Controllers\PartnerController.cs" />
    <Compile Include="Controllers\SendHistoryReturnRecordController.cs" />
    <Compile Include="Controllers\TempTestController.cs" />
    <Compile Include="Controllers\TkPrintProcessStateRecordController.cs" />
    <Compile Include="Controllers\WaybillCodeListController.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Models\BindSkuSubmitViewModel.cs" />
    <Compile Include="Models\InOutSkuSubmitViewModel.cs" />
    <Compile Include="Models\PageDepthModel.cs" />
    <Compile Include="Models\ProductSkuListViewModel.cs" />
    <Compile Include="Models\ProductGetOneViewModel.cs" />
    <Compile Include="Models\SelectListItemViewModel.cs" />
    <Compile Include="Models\StockControlLoadProductSkuListViewModel.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="bin\DianGuanJiaApp.ErpWeb.xml" />
    <Content Include="Content\css\abnormalProduct\abnormal-product-content.css" />
    <Content Include="Content\css\aftersale\aftersale.css" />
    <Content Include="Content\css\antdCommon.css" />
    <Content Include="Content\css\calenderTime.css" />
    <Content Include="Content\css\DistributionProduct\DistributionDetails.css" />
    <Content Include="Content\css\DistributionProduct\SelectionDistribution.css" />
    <Content Include="Content\css\distributionSet\distributionSet.css" />
    <Content Include="Content\css\exportExcelIndex\exportExcelIndex.css" />
    <Content Include="Content\css\FinancialSettlement\AccountBasisDailog.css" />
    <Content Include="Content\css\FreightTemplate\NewShippingFeeSet.css" />
    <Content Include="Content\css\FreightTemplate\SupplierSetBy1688.css" />
    <Content Include="Content\css\generalizeIndex\ApplicationIndex.css" />
    <Content Include="Content\css\generalizeIndex\generalizeIndex.css" />
    <Content Include="Content\css\generalizeIndex\MyStationCard.css" />
    <Content Include="Content\css\concatLayout.css" />
    <Content Include="Content\css\newLayout.css" />
    <Content Include="Content\css\common-old.css" />
    <Content Include="Content\css\common.css" />
    <Content Include="Content\css\daterangepicker\calenderTime.css" />
    <Content Include="Content\css\daterangepicker\daterangepicker.css" />
    <Content Include="Content\css\editemplatecom.css" />
    <Content Include="Content\css\fonts\iconfont.css" />
    <Content Include="Content\css\fonts\iconfont.js" />
    <Content Include="Content\css\fonts\iconfont.svg" />
    <Content Include="Content\css\orderlist\allOrder.css" />
    <Content Include="Content\css\orderlist\offlineOrder.css" />
    <Content Include="Content\css\orderlist\ordercommon.css" />
    <Content Include="Content\css\orderlist\scanPrint.css" />
    <Content Include="Content\css\orderlist\waitorder.css" />
    <Content Include="Content\css\orderlist\sendfail.css" />
    <Content Include="Content\css\phoneQr\Invite.css" />
    <Content Include="Content\css\phoneQr\setBase.css" />
    <Content Include="Content\css\product\BaseOfPtSkuRelationEqualProducts.css" />
    <Content Include="Content\css\product\GlobalProduct.css" />
    <Content Include="Content\css\product\Collectproduct.css" />
    <Content Include="Content\css\product\CommonProductCollection.css" />
    <Content Include="Content\css\product\PrepareDistribution.css" />
    <Content Include="Content\css\product\CreateBasePlatformProduct.css" />
    <Content Include="Content\css\product\BusinessCardCreateProduct.css" />
    <Content Include="Content\css\product\createBaseProduct.css" />
    <Content Include="Content\css\product\NewproductBasics.css" />
    <Content Include="Content\css\product\productBasics.css" />
    <Content Include="Content\css\product\product-index.css" />
    <Content Include="Content\css\product\BaseOfPtProducts.css" />
    <Content Include="Content\css\product\ShopBindSupplier.css" />
    <Content Include="Content\css\product\ProductCollectionBox.css" />
    <Content Include="Content\css\ProfitStatisticsView\Index.css" />
    <Content Include="Content\css\purchases\purchasePrint.css" />
    <Content Include="Content\css\purchases\purchases.css" />
    <Content Include="Content\css\searchOrderWrap.css" />
    <Content Include="Content\css\selectboxRemarksFlag.css" />
    <Content Include="Content\css\selectbox2.css" />
    <Content Include="Content\css\SendGoodTemplate\AllPagesStyle.css" />
    <Content Include="Content\css\SendGoodTemplate\main.css" />
    <Content Include="Content\css\SendGoodTemplate\SendGoodTemplate.css" />
    <Content Include="Content\css\SendGoodTemplate\setSendTemplate.css" />
    <Content Include="Content\css\editPswdLayout.css" />
    <Content Include="Content\css\skinCommon.css" />
    <Content Include="Content\css\skinCommon.css" />
    <Content Include="Content\css\StockControl\WarehouseProduct.css" />
    <Content Include="Content\css\StockControl\StockControl.css" />
    <Content Include="Content\css\system\qualification.css" />
    <Content Include="Content\css\wuDesign\wu-platform.css" />
    <Content Include="Content\css\wuDesign\wu-skin.css" />
    <Content Include="Content\css\wuDesign\wu-skin2.css" />
    <Content Include="Content\files\sendfail.wav" />
    <Content Include="Content\files\sendsuccess.wav" />
    <Content Include="Content\files\aftersale.wav" />
    <Content Include="Content\files\fail.wav" />
    <Content Include="Content\files\sended.wav" />
    <Content Include="Content\files\printed.wav" />
    <Content Include="Content\files\finishfile.wav" />
    <Content Include="Content\images\001.jpg" />
    <Content Include="Content\images\01.jpg" />
    <Content Include="Content\images\1.jpg" />
    <Content Include="Content\images\1.png" />
    <Content Include="Content\images\13.png" />
    <Content Include="Content\images\1688login.jpg" />
    <Content Include="Content\images\2.jpg" />
    <Content Include="Content\images\2022-12-05contact.png" />
    <Content Include="Content\images\2022-12-06-contact.gif" />
    <Content Include="Content\images\2022-contact-right.png" />
    <Content Include="Content\images\222.jpg" />
    <Content Include="Content\images\3.jpg" />
    <Content Include="Content\images\333.png" />
    <Content Include="Content\images\360-1.JPG" />
    <Content Include="Content\images\360-2.JPG" />
    <Content Include="Content\images\360-3.JPG" />
    <Content Include="Content\images\4.jpg" />
    <Content Include="Content\images\404-page_2024_11_05.png" />
    <Content Include="Content\images\aa20180926174948.png" />
    <Content Include="Content\images\abnormal-product-tip-icon.png" />
    <Content Include="Content\images\addong.gif" />
    <Content Include="Content\images\addWebcat.png" />
    <Content Include="Content\images\ads-01.png" />
    <Content Include="Content\images\agent_shilitu.jpg" />
    <Content Include="Content\images\alibaba.png" />
    <Content Include="Content\images\allicons.png" />
    <Content Include="Content\images\allicons2.png" />
    <Content Include="Content\images\appear-PC01.png" />
    <Content Include="Content\images\appear-PC02.png" />
    <Content Include="Content\images\automaticExplanation.png" />
    <Content Include="Content\images\avatarUrl.png" />
    <Content Include="Content\images\banner-01.png" />
    <Content Include="Content\images\contact_me_qr_05.png" />
    <Content Include="Content\images\dailogPic\douyindianzimiandan.png" />
    <Content Include="Content\images\dailogPic\gg640.jpg" />
    <Content Include="Content\images\dailogPic\platformOrderError-01.png" />
    <Content Include="Content\images\dailogPic\platformOrderError-02.png" />
    <Content Include="Content\images\dailogPic\taobaoMaiCaiDailog-01.png" />
    <Content Include="Content\images\dailogPic\taobaoMaiCaiDailog-02.png" />
    <Content Include="Content\images\data-status-2025-2-13.png" />
    <Content Include="Content\images\dianju800.gif" />
    <Content Include="Content\images\dingshe-icon.png" />
    <Content Include="Content\images\distribution\back.png" />
    <Content Include="Content\images\distribution\BaseProduct-course240906.jpg" />
    <Content Include="Content\images\distribution\empty0710.png" />
    <Content Include="Content\images\distribution\headerBack20240626.png" />
    <Content Include="Content\images\distribution\headerBackLeft20240626.png" />
    <Content Include="Content\images\distribution\headerBackRight20240626.png" />
    <Content Include="Content\images\distribution\icon-zhanweitu240820.png" />
    <Content Include="Content\images\distribution\usagetutorial240821.png" />
    <Content Include="Content\images\distribution\use-icon1.png" />
    <Content Include="Content\images\distribution\use-icon2.png" />
    <Content Include="Content\images\distribution\use-icon3.png" />
    <Content Include="Content\images\distribution\vip-icon1.png" />
    <Content Include="Content\images\distribution\vip-icon2.png" />
    <Content Include="Content\images\distribution\vip-icon3.png" />
    <Content Include="Content\images\distribution\vip-icon4.png" />
    <Content Include="Content\images\distribution\vip-icon5.png" />
    <Content Include="Content\images\distribution\vip-icon6.png" />
    <Content Include="Content\images\edit-kefu.gif" />
    <Content Include="Content\images\edit.49fa.gif" />
    <Content Include="Content\images\electronic-waybill-print-tip-bg.png" />
    <Content Include="Content\images\expenditureWallet.png" />
    <Content Include="Content\images\exportExcelIndex-icon.png" />
    <Content Include="Content\images\expressIcon-new.png" />
    <Content Include="Content\images\fd-2023-5-4-8.png" />
    <Content Include="Content\images\feedback-icon.png" />
    <Content Include="Content\images\fullPic-01.png" />
    <Content Include="Content\images\fxapp-platform.png" />
    <Content Include="Content\images\fxdf-platform.png" />
    <Content Include="Content\images\generalizeIndex-icons.png" />
    <Content Include="Content\images\goodapp-platform.png" />
    <Content Include="Content\images\haoyouduohelpstep.png" />
    <Content Include="Content\images\helianghelpstep.png" />
    <Content Include="Content\images\hot-icon-2023-4-21.png" />
    <Content Include="Content\images\incomeWallet.png" />
    <Content Include="Content\images\indexicons.png" />
    <Content Include="Content\images\indexNew-icons02.png" />
    <Content Include="Content\images\index_btn2.png" />
    <Content Include="Content\images\all_parther.png" />
    <Content Include="Content\images\an1.gif" />
    <Content Include="Content\images\an2.gif" />
    <Content Include="Content\images\an56.jpg" />
    <Content Include="Content\images\an56.png" />
    <Content Include="Content\images\anli.png" />
    <Content Include="Content\images\announcement.png" />
    <Content Include="Content\images\auth001.png" />
    <Content Include="Content\images\authinfo01.png" />
    <Content Include="Content\images\authinfo02.png" />
    <Content Include="Content\images\backgound_icon.png" />
    <Content Include="Content\images\baidu-1.JPG" />
    <Content Include="Content\images\baidu-2.JPG" />
    <Content Include="Content\images\baidu-3.JPG" />
    <Content Include="Content\images\banner-ad-conterIcon.png" />
    <Content Include="Content\images\banner-ad-leftIcon.png" />
    <Content Include="Content\images\Barcode.png" />
    <Content Include="Content\images\beihuodan-pic01.png" />
    <Content Include="Content\images\bg_btn_getorder.gif" />
    <Content Include="Content\images\bg_btn_tempsave.gif" />
    <Content Include="Content\images\bg_deliveryname.gif" />
    <Content Include="Content\images\bg_h2.gif" />
    <Content Include="Content\images\bg_h2_sblock.gif" />
    <Content Include="Content\images\bg_li_normal.gif" />
    <Content Include="Content\images\bg_li_star.gif" />
    <Content Include="Content\images\bg_operation_buttons.gif" />
    <Content Include="Content\images\bg_root.gif" />
    <Content Include="Content\images\bg_sprite_count_tab.gif" />
    <Content Include="Content\images\border.png" />
    <Content Include="Content\images\border_shawBottom.png" />
    <Content Include="Content\images\btns.png" />
    <Content Include="Content\images\btns927.png" />
    <Content Include="Content\images\btn_close_s.gif" />
    <Content Include="Content\images\btn_operation.png" />
    <Content Include="Content\images\btn_resize.gif" />
    <Content Include="Content\images\btn_tempmenu_close.gif" />
    <Content Include="Content\images\btn_tempmenu_open.gif" />
    <Content Include="Content\images\bt_gb.gif" />
    <Content Include="Content\images\bt_save.gif" />
    <Content Include="Content\images\button.gif" />
    <Content Include="Content\images\calendar_arrows.png" />
    <Content Include="Content\images\chrome_s.png" />
    <Content Include="Content\images\combo_arrow.png" />
    <Content Include="Content\images\contact.png" />
    <Content Include="Content\images\contact_me_qr_02.png" />
    <Content Include="Content\images\container03_tabale_setBtn.png" />
    <Content Include="Content\images\controlOffset.png" />
    <Content Include="Content\images\danpai1.jpg" />
    <Content Include="Content\images\datebox_arrow.png" />
    <Content Include="Content\images\deledialogTwo.png" />
    <Content Include="Content\images\deles-icon.png" />
    <Content Include="Content\images\dgjalogo.png" />
    <Content Include="Content\images\dgjlogo.png" />
    <Content Include="Content\images\dialog00.png" />
    <Content Include="Content\images\dialog01.png" />
    <Content Include="Content\images\dialog02.png" />
    <Content Include="Content\images\dialog03.png" />
    <Content Include="Content\images\dialog_windows_dele.png" />
    <Content Include="Content\images\dianhau-icon.png" />
    <Content Include="Content\images\email_icon.png" />
    <Content Include="Content\images\erliandanTemplate.png" />
    <Content Include="Content\images\erweima.png" />
    <Content Include="Content\images\ganthao.gif" />
    <Content Include="Content\images\genavigation.png" />
    <Content Include="Content\images\greenhand.png" />
    <Content Include="Content\images\guidance-set.png" />
    <Content Include="Content\images\guidance-set02.png" />
    <Content Include="Content\images\guidance-set03.png" />
    <Content Include="Content\images\headerActiveBackground.png" />
    <Content Include="Content\images\headerbg.png" />
    <Content Include="Content\images\icon-hot.png" />
    <Content Include="Content\images\icon.png" />
    <Content Include="Content\images\icon_close.png" />
    <Content Include="Content\images\image001.png" />
    <Content Include="Content\images\index_btn.png" />
    <Content Include="Content\images\index_btn2.png" />
    <Content Include="Content\images\index_fxapp-platform.png" />
    <Content Include="Content\images\index_hi_icon.png" />
    <Content Include="Content\images\index_laying_course.png" />
    <Content Include="Content\images\index_product_course.png" />
    <Content Include="Content\images\juhaomaihelpstep.png" />
    <Content Include="Content\images\kdexload.gif" />
    <Content Include="Content\images\kuanjin-icons.png" />
    <Content Include="Content\images\kuoda-icon-02.png" />
    <Content Include="Content\images\kuodajing.png" />
    <Content Include="Content\images\lbbeihuodan.png" />
    <Content Include="Content\images\leftandright.png" />
    <Content Include="Content\images\leftAndRight02.png" />
    <Content Include="Content\images\leftAndRights.png" />
    <Content Include="Content\images\leftNavSmall.png" />
    <Content Include="Content\images\level-bannerBacnk-01.png" />
    <Content Include="Content\images\level-icon.png" />
    <Content Include="Content\images\level-icons.png" />
    <Content Include="Content\images\limited-time-free-1025.png" />
    <Content Include="Content\images\liPics01.png" />
    <Content Include="Content\images\loading-2.gif" />
    <Content Include="Content\images\loading.gif" />
    <Content Include="Content\images\loading_small.gif" />
    <Content Include="Content\images\lock.png" />
    <Content Include="Content\images\login-icon01.png" />
    <Content Include="Content\images\logo - 副本.jpg" />
    <Content Include="Content\images\logo-2022-3-25-02.png" />
    <Content Include="Content\images\logo-2022-3-25-2.png" />
    <Content Include="Content\images\logo-2022-3-25.png" />
    <Content Include="Content\images\logo.jpg" />
    <Content Include="Content\images\logo2023-3-3-02.png" />
    <Content Include="Content\images\logo2023-3-3.png" />
    <Content Include="Content\images\makeShop.jpg" />
    <Content Include="Content\images\morePic2.png" />
    <Content Include="Content\images\moreShow01.gif" />
    <Content Include="Content\images\moreShow02.gif" />
    <Content Include="Content\images\mx-1.JPG" />
    <Content Include="Content\images\mx-2.JPG" />
    <Content Include="Content\images\mx-3.JPG" />
    <Content Include="Content\images\nahuo-icon.png" />
    <Content Include="Content\images\nahuo-lie-01_03.png" />
    <Content Include="Content\images\nahuolabelBackground.png" />
    <Content Include="Content\images\new01.gif" />
    <Content Include="Content\images\new02.gif" />
    <Content Include="Content\images\new03.gif" />
    <Content Include="Content\images\new04.gif" />
    <Content Include="Content\images\newConcatIcon.png" />
    <Content Include="Content\images\newGreenhand-02.png" />
    <Content Include="Content\images\newGreenhand-icons.png" />
    <Content Include="Content\images\newLeftNav_moreNav.png" />
    <Content Include="Content\images\newNoviceIntro\product\product-newStep01.png" />
    <Content Include="Content\images\newNoviceIntro\product\product-newStep02.png" />
    <Content Include="Content\images\newNoviceIntro\product\product-show.png" />
    <Content Include="Content\images\newNoviceIntro\product\product-step_01.png" />
    <Content Include="Content\images\newNoviceIntro\product\product-step_02.png" />
    <Content Include="Content\images\newNoviceIntro\purchases\newPurchases-1.png" />
    <Content Include="Content\images\newNoviceIntro\purchases\newPurchases-2.png" />
    <Content Include="Content\images\newNoviceIntro\purchases\newPurchases-3.png" />
    <Content Include="Content\images\newNoviceIntro\purchases\newPurchases-4.png" />
    <Content Include="Content\images\newNoviceIntro\purchases\newPurchases-5.png" />
    <Content Include="Content\images\newNoviceIntro\purchases\setPurchasesSet-1.png" />
    <Content Include="Content\images\newNoviceIntro\purchases\setPurchasesSet-2.png" />
    <Content Include="Content\images\newNoviceIntro\purchases\setPurchasesSet-3.png" />
    <Content Include="Content\images\newNoviceIntro\purchases\setPurchasesSet-4.png" />
    <Content Include="Content\images\newNoviceIntro\purchases\setPurchasesSet-5.png" />
    <Content Include="Content\images\newPurchases-5.png" />
    <Content Include="Content\images\newzhinan-background.png" />
    <Content Include="Content\images\new_icon_2024_11_20.png" />
    <Content Include="Content\images\new_msg.gif" />
    <Content Include="Content\images\nextStep.png" />
    <Content Include="Content\images\noData-icon.png" />
    <Content Include="Content\images\nopic.gif" />
    <Content Include="Content\images\noviceIntro-img.png" />
    <Content Include="Content\images\noviceIntroPic\allxufei-icon-2023-1-9.png" />
    <Content Include="Content\images\noviceIntroPic\2023-1-30-duoduohelp.png" />
    <Content Include="Content\images\noviceIntroPic\baseProduct0626.jpg" />
    <Content Include="Content\images\noviceIntroPic\cgj2023-12-22-1200px100px.png" />
    <Content Include="Content\images\noviceIntroPic\cgj2023-12-22-1200px180px.png" />
    <Content Include="Content\images\noviceIntroPic\cgj2023-12-22-800px60px.png" />
    <Content Include="Content\images\noviceIntroPic\chooseYun-img.png" />
    <Content Include="Content\images\noviceIntroPic\douyin-gaizao-icons-2.png" />
    <Content Include="Content\images\noviceIntroPic\douyin-gaizao-icons.png" />
    <Content Include="Content\images\noviceIntroPic\anquanWarn-2022-10-26-01.png" />
    <Content Include="Content\images\noviceIntroPic\anquanWarn-2022-10-26-02.png" />
    <Content Include="Content\images\noviceIntroPic\generalizeIndex-product-help.png" />
    <Content Include="Content\images\noviceIntroPic\kuajingShowHelps-step-03.png" />
    <Content Include="Content\images\noviceIntroPic\new-help-2024-11-11.png" />
    <Content Include="Content\images\noviceIntroPic\new-help-2024-11-12.png" />
    <Content Include="Content\images\noviceIntroPic\relationProduct0626.jpg" />
    <Content Include="Content\images\noviceIntroPic\kj-steps-icon01.png" />
    <Content Include="Content\images\noviceIntroPic\kj-steps-icon02.png" />
    <Content Include="Content\images\noviceIntroPic\kj-steps-icon03.png" />
    <Content Include="Content\images\noviceIntroPic\kj-steps-icon04.png" />
    <Content Include="Content\images\noviceIntroPic\kuajingShowHelps-step-02.png" />
    <Content Include="Content\images\noviceIntroPic\kuajingShowHelps-step.png" />
    <Content Include="Content\images\noviceIntroPic\sendfail-help-2023-2-10.png" />
    <Content Include="Content\images\noviceIntroPic\ssq-2023-1-9.png" />
    <Content Include="Content\images\noviceIntroPic\index-helpimg2023.png" />
    <Content Include="Content\images\noviceIntroPic\ssqhtz-2023-1-9-01.png" />
    <Content Include="Content\images\noviceIntroPic\ssqhtz-2023-1-9-02.png" />
    <Content Include="Content\images\noviceIntroPic\ssqhtz-2023-1-9-03.png" />
    <Content Include="Content\images\noviceIntroPic\ssqhtz-2023-1-9.png" />
    <Content Include="Content\images\noviceIntroPic\tutorial_2024_06_19.png" />
    <Content Include="Content\images\noviceIntroPic\wangshangpay_buyee_2024_01_03_1200_100.png" />
    <Content Include="Content\images\noviceIntroPic\wangshangpay_buyer_2024_01_03_1200_100.png" />
    <Content Include="Content\images\noviceIntroPic\waybill-icon.gif" />
    <Content Include="Content\images\noviceIntroPic\wuliuyujin-1.png" />
    <Content Include="Content\images\noviceIntroPic\wuliuyujin-2-1.png" />
    <Content Include="Content\images\noviceIntroPic\wuliuyujin-2-2.png" />
    <Content Include="Content\images\noviceIntroPic\wuliuyujin-2-3.png" />
    <Content Include="Content\images\noviceIntroPic\wuliuyujin-2-4.png" />
    <Content Include="Content\images\noviceIntroPic\wuliuyujin-2.png" />
    <Content Include="Content\images\noviceIntroPic\wuliuyujin-3.png" />
    <Content Include="Content\images\noviceIntroPic\yufahuo-1.png" />
    <Content Include="Content\images\noviceIntroPic\yufahuo-2.png" />
    <Content Include="Content\images\Number-1.png" />
    <Content Include="Content\images\Number-2.png" />
    <Content Include="Content\images\Number-3.png" />
    <Content Include="Content\images\onecode.png" />
    <Content Include="Content\images\onetomore.png" />
    <Content Include="Content\images\open1688fx2023-8-29.png" />
    <Content Include="Content\images\order-bg.png" />
    <Content Include="Content\images\ordergoods_noimage.jpg" />
    <Content Include="Content\images\orderNumber.png" />
    <Content Include="Content\images\other.png" />
    <Content Include="Content\images\pay_02.png" />
    <Content Include="Content\images\pdd.jpg" />
    <Content Include="Content\images\pdd_logo_v2.png" />
    <Content Include="Content\images\phoneLogo.png" />
    <Content Include="Content\images\piccshow.png" />
    <Content Include="Content\images\picShow925.gif" />
    <Content Include="Content\images\picShowonload.gif" />
    <Content Include="Content\images\pinduoduo.png" />
    <Content Include="Content\images\pinduoduodj.png" />
    <Content Include="Content\images\pinduoduo_ys.png" />
    <Content Include="Content\images\pingduoduojy-icons.png" />
    <Content Include="Content\images\pingtai-icon.png" />
    <Content Include="Content\images\pintaiIcon.png" />
    <Content Include="Content\images\pl.jpg" />
    <Content Include="Content\images\platform-icons-2025-2-14.png" />
    <Content Include="Content\images\platform.png" />
    <Content Include="Content\images\printapp-platform.png" />
    <Content Include="Content\images\print_quantity_new_select_tip.png" />
    <Content Include="Content\images\privacy.png" />
    <Content Include="Content\images\product-01.PNG" />
    <Content Include="Content\images\productCollect\play-icon.png" />
    <Content Include="Content\images\productCollect\google-icon.png" />
    <Content Include="Content\images\productCollect\lazada-icon.png" />
    <Content Include="Content\images\productCollect\shopee-icon.png" />
    <Content Include="Content\images\productCollect\tikTok-icon.png" />
    <Content Include="Content\images\produst01.png" />
    <Content Include="Content\images\produst02.png" />
    <Content Include="Content\images\produst03.png" />
    <Content Include="Content\images\produst04.png" />
    <Content Include="Content\images\profit-statistics-rule-instruction-icon.jpg" />
    <Content Include="Content\images\profit-statistics-tip-banner.png" />
    <Content Include="Content\images\ProudctImg.jpg" />
    <Content Include="Content\images\puhuo-icon-1.png" />
    <Content Include="Content\images\pur-table-operate.png" />
    <Content Include="Content\images\pyydon-index-top.png" />
    <Content Include="Content\images\QQ-1.JPG" />
    <Content Include="Content\images\QQ-2.JPG" />
    <Content Include="Content\images\QQ-3.JPG" />
    <Content Include="Content\images\qqKefu-cion.png" />
    <Content Include="Content\images\qqkehu.gif" />
    <Content Include="Content\images\qqkehu02.gif" />
    <Content Include="Content\images\QRcode.png" />
    <Content Include="Content\images\qrkmmexport.png" />
    <Content Include="Content\images\quyeweixiner01.png" />
    <Content Include="Content\images\quyeweixiner02.png" />
    <Content Include="Content\images\ReceiverSet.png" />
    <Content Include="Content\images\registerInto.gif" />
    <Content Include="Content\images\remark_icon.png" />
    <Content Include="Content\images\renew\Alibaba-xufei.png" />
    <Content Include="Content\images\renew\AlibabaC2M-xufei.png" />
    <Content Include="Content\images\renew\AlibabaFx-xufei.png" />
    <Content Include="Content\images\renew\DuXiaoDian-xufei.png" />
    <Content Include="Content\images\renew\Jingdong-xufei.png" />
    <Content Include="Content\images\renew\JingdongNew-xufei.png" />
    <Content Include="Content\images\renew\KuaiShou-xufei.png" />
    <Content Include="Content\images\renew\KuaiShouNew-xufei.png" />
    <Content Include="Content\images\renew\Pinduoduo-xufei.png" />
    <Content Include="Content\images\renew\Suning-xufei.png" />
    <Content Include="Content\images\renew\Taobao-xufei.png" />
    <Content Include="Content\images\renew\togglePinduoduo-xufei.png" />
    <Content Include="Content\images\renew\TouTiao-distribution-xufei.jpg" />
    <Content Include="Content\images\renew\TouTiao-xufei.png" />
    <Content Include="Content\images\renew\TouTiaoNew-xufei.png" />
    <Content Include="Content\images\renew\toutiaoXufeiTip.gif" />
    <Content Include="Content\images\renew\TouTiaoOld-xufei.png" />
    <Content Include="Content\images\renew\WeiMeng-xufei.png" />
    <Content Include="Content\images\renew\WxVideo_new.png" />
    <Content Include="Content\images\renew\WxVideo_old.png" />
    <Content Include="Content\images\scanPrintNoData.png" />
    <Content Include="Content\images\step-icon.png" />
    <Content Include="Content\images\renew\WxVideo-xufei-2.png" />
    <Content Include="Content\images\svg\aliDistributionIntroduce_01.png" />
    <Content Include="Content\images\shuiyinbackg.png" />
    <Content Include="Content\images\svg\aliDistributionIntroduce_02.png" />
    <Content Include="Content\images\svg\aliDistributionIntroduce_03.png" />
    <Content Include="Content\images\svg\aliDistributionIntroduce_04.png" />
    <Content Include="Content\images\svg\aliDistributionIntroduce_05.png" />
    <Content Include="Content\images\svg\aliDistributionIntroduce_06.png" />
    <Content Include="Content\images\svg\aliIntroduce-icons-2024-1-24-01.png" />
    <Content Include="Content\images\svg\aliIntroduce-icons-2024-1-24-02.png" />
    <Content Include="Content\images\svg\aliIntroduce-icons-2024-1-24-021.png" />
    <Content Include="Content\images\svg\aliIntroduce-icons-2024-1-24-03.png" />
    <Content Include="Content\images\svg\aliIntroduce-icons-2024-1-24-04-11.png" />
    <Content Include="Content\images\svg\aliIntroduce-icons-2024-1-24-04-111.png" />
    <Content Include="Content\images\svg\aliIntroduce-icons-2024-1-24-04.png" />
    <Content Include="Content\images\svg\aliIntroduce-icons-2024-1-24-05.png" />
    <Content Include="Content\images\svg\aliIntroduce-icons-2024-1-24-06.png" />
    <Content Include="Content\images\svg\aliIntroduce-icons-2024-1-24-07.png" />
    <Content Include="Content\images\svg\guide-icon.png" />
    <Content Include="Content\images\svg\icon-2024-4-17.png" />
    <Content Include="Content\images\svg\logo-2025-3-25-01.png" />
    <Content Include="Content\images\svg\logo-2025-3-25-02.png" />
    <Content Include="Content\images\svg\logo-2025-6-25-01.jpg" />
    <Content Include="Content\images\svg\logo-2025-6-25-01.png" />
    <Content Include="Content\images\svg\n-choosepintai-2024-7-11.png" />
    <Content Include="Content\images\svg\n-pintai-icons.png" />
    <Content Include="Content\images\svg\n-pintai-icons2x.png" />
    <Content Include="Content\images\svg\payOrderSteps-icon.png" />
    <Content Include="Content\images\temAddress2023-12-19.png" />
    <Content Include="Content\images\touTiaoSaleShopPlatform.png" />
    <Content Include="Content\images\warn-title-bg.png" />
    <Content Include="Content\images\weidian-platform.png" />
    <Content Include="Content\images\weixinxsphelpstep-v2.png" />
    <Content Include="Content\images\renew\WxVideo-v2-xufei.png" />
    <Content Include="Content\images\renew\WxVideo-xufei.png" />
    <Content Include="Content\images\renew\WxXiaoShangDian-xufei.png" />
    <Content Include="Content\images\renew\YouZan-xufei.png" />
    <Content Include="Content\images\select-icon.png" />
    <Content Include="Content\images\set-icon02.png" />
    <Content Include="Content\images\SetSendList_container02_btn.png" />
    <Content Include="Content\images\SetSendList_title.png" />
    <Content Include="Content\images\shop-type.png" />
    <Content Include="Content\images\showmore.png" />
    <Content Include="Content\images\showOrHideP.png" />
    <Content Include="Content\images\shuangpai1.jpg" />
    <Content Include="Content\images\smallPic.png" />
    <Content Include="Content\images\sogou-1.JPG" />
    <Content Include="Content\images\sogou-2.JPG" />
    <Content Include="Content\images\sogou-3.JPG" />
    <Content Include="Content\images\spinner_arrows.png" />
    <Content Include="Content\images\suotou.png" />
    <Content Include="Content\images\svg\perIcon.png" />
    <Content Include="Content\images\svg\recycle-icon.png" />
    <Content Include="Content\images\svg\unbin-icon.svg" />
    <Content Include="Content\images\sxh001.png" />
    <Content Include="Content\images\sxs001.png" />
    <Content Include="Content\images\syncOrder.png" />
    <Content Include="Content\images\tabale_content_dele.png" />
    <Content Include="Content\images\tabale_content_mo.png" />
    <Content Include="Content\images\tabale_content_print.png" />
    <Content Include="Content\images\table_style_1.png" />
    <Content Include="Content\images\table_style_2.png" />
    <Content Include="Content\images\table_style_3.png" />
    <Content Include="Content\images\table_style_4.png" />
    <Content Include="Content\images\table_style_5.png" />
    <Content Include="Content\images\taobao.png" />
    <Content Include="Content\images\taocan-left.png" />
    <Content Include="Content\images\taocan-right.png" />
    <Content Include="Content\images\tbauthinfo.jpg" />
    <Content Include="Content\images\tbauthinfo2.jpg" />
    <Content Include="Content\images\template_img_icon_01.jpg" />
    <Content Include="Content\images\template_img_icon_02.jpg" />
    <Content Include="Content\images\tfans.jpg" />
    <Content Include="Content\images\tianmao.png" />
    <Content Include="Content\images\tiaoxin60043.png" />
    <Content Include="Content\images\timeLeftRight.png" />
    <Content Include="Content\images\topAd.png" />
    <Content Include="Content\images\toubackground27.png" />
    <Content Include="Content\images\toubackground28.png" />
    <Content Include="Content\images\toutiao-platform.png" />
    <Content Include="Content\images\trBackground.png" />
    <Content Include="Content\images\trBackground2.png" />
    <Content Include="Content\images\UC-1.JPG" />
    <Content Include="Content\images\UC-2.JPG" />
    <Content Include="Content\images\UC-3.JPG" />
    <Content Include="Content\images\upgrade-background01.png" />
    <Content Include="Content\images\upgrade-background01_03.png" />
    <Content Include="Content\images\upgrade-background01_04.png" />
    <Content Include="Content\images\upgrade-background02.png" />
    <Content Include="Content\images\upgrade-background03_03.png" />
    <Content Include="Content\images\versions-icon.png" />
    <Content Include="Content\images\wanwan-01.png" />
    <Content Include="Content\images\wanwanicon.png" />
    <Content Include="Content\images\wanzhan_error.png" />
    <Content Include="Content\images\wareHouse-icon01.png" />
    <Content Include="Content\images\waybillcode.png" />
    <Content Include="Content\images\webOperationSystem01.png" />
    <Content Include="Content\images\webOperationSystem02.png" />
    <Content Include="Content\images\weixinhelpstep.png" />
    <Content Include="Content\images\weixinxsphelpstep.png" />
    <Content Include="Content\images\welcome-icon.png" />
    <Content Include="Content\images\wenhao.png" />
    <Content Include="Content\images\whilebg.png" />
    <Content Include="Content\images\wszfBanner-02.png" />
    <Content Include="Content\images\wt.jpg" />
    <Content Include="Content\images\wuliuyujin-icon02 .png" />
    <Content Include="Content\images\xxh001.png" />
    <Content Include="Content\images\xxs001.png" />
    <Content Include="Content\images\yantuqrcode.png" />
    <Content Include="Content\images\yiliandanTemplate.png" />
    <Content Include="Content\images\yinleftNav.png" />
    <Content Include="Content\images\youzan-platform.png" />
    <Content Include="Content\images\youzhanhelpstep.png" />
    <Content Include="Content\images\yufahuo-2.png" />
    <Content Include="Content\css\orderlist\orderList.css" />
    <Content Include="ErrorHtml\404Error.html" />
    <Content Include="favicon.ico" />
    <Content Include="Global.asax" />
    <Content Include="ILLink\ILLink.Descriptors.LibraryBuild.xml" />
    <Content Include="Scripts\AfterSale\ManualAfterSale.js" />
    <Content Include="Scripts\AfterSale\AfterSaleModule.js" />
    <Content Include="Scripts\ajaxfileupload.js" />
    <Content Include="Scripts\allPlatformFun.js" />
    <Content Include="Scripts\aliyunCaptchaCommonFn.js" />
    <Content Include="Scripts\animate.js" />
    <Content Include="Scripts\antd\antd.min.css" />
    <Content Include="Scripts\arms-tool.js" />
    <Content Include="Scripts\antd\antd.min.js" />
    <Content Include="Scripts\categoryMeu\index说明.html" />
    <Content Include="Scripts\categoryMeu\wu_categoryMeu.css" />
    <Content Include="Scripts\categoryMeu\wu_categoryMeu.js" />
    <Content Include="Scripts\collectbox\EditCollectProduct.js" />
    <Content Include="Scripts\collectbox\EditGlobalProduct.js" />
    <Content Include="Scripts\countdownTimer\countdownTimer.js" />
    <Content Include="Scripts\dragModule.js" />
    <Content Include="Scripts\exportExcelIndex\rightContentRender.js" />
    <Content Include="Scripts\exportExcelIndex\exportExcelIndex.js" />
    <Content Include="Scripts\ExpressBill\WaybillCodeCheckModule.js" />
    <Content Include="Scripts\ExpressPrinter.js" />
    <Content Include="Scripts\daterangepicker\calenderTime.js" />
    <Content Include="Scripts\daterangepicker\daterangepicker.js" />
    <Content Include="Scripts\daterangepicker\jquery.min.js" />
    <Content Include="Scripts\daterangepicker\moment.js" />
    <Content Include="Scripts\daterangepicker\moment.min.js" />
    <Content Include="Scripts\FengqiaoPrinter.js" />
    <Content Include="Scripts\FinancialSettlement\BillCenter.js" />
    <Content Include="Scripts\FinancialSettlement\PriceSettingCommon.js" />
    <Content Include="Scripts\FinancialSettlement\MyShops.js" />
    <Content Include="Scripts\FinancialSettlement\SkuRecycleAgent.js" />
    <Content Include="Scripts\FinancialSettlement\SkuRecycleSupplier.js" />
    <Content Include="Scripts\FinancialSettlement\BillManagement.js" />
    <Content Include="Scripts\FinancialSettlement\OutAccountProduct.js" />
    <Content Include="Scripts\FinancialSettlement\PriceSettingAgent.js" />
    <Content Include="Scripts\FinancialSettlement\PriceSettingClound.js" />
    <Content Include="Scripts\FinancialSettlement\PriceSettingSupplier.js" />
    <Content Include="Scripts\FreightTemplate\AddressTemplateAddOrEdmit.js" />
    <Content Include="Scripts\FreightTemplate\ShippingFeeSet1688.js" />
    <Content Include="Scripts\FreightTemplate\ShippingFeeTemplateSet.js" />
    <Content Include="Scripts\FundsManagement\fundsManagement.js" />
    <Content Include="Scripts\GeneralizeIndex\ApplicationIndexModule.js" />
    <Content Include="Scripts\GeneralizeIndex\MyStationCard.js" />
    <Content Include="Scripts\GeneralizeIndex\DemandModule.js" />
    <Content Include="Scripts\GeneralizeIndex\GeneralizeIndexModule.js" />
    <Content Include="Scripts\guidelayer\guideDailogLayer.js" />
    <Content Include="Scripts\guidelayer\guidelayer.css" />
    <Content Include="Scripts\guidelayer\guideSteplayer.js" />
    <Content Include="Scripts\initMyKu.js" />
    <Content Include="Scripts\JingDongPrinter.js" />
    <Content Include="Scripts\jquery.cookie.js" />
    <Content Include="Scripts\jsrender\jsrender.js" />
    <Content Include="Scripts\jsrender\jsrender.min.js" />
    <Content Include="Scripts\KuaiShouPrinter.js" />
    <Content Include="Scripts\layer\layer.js" />
    <Content Include="Scripts\layer\mobile\layer.js" />
    <Content Include="Scripts\layer\mobile\need\layer.css" />
    <Content Include="Scripts\layer\skin\of\style.css" />
    <Content Include="Scripts\layer\theme\default\icon-ext.png" />
    <Content Include="Scripts\layer\theme\default\icon.png" />
    <Content Include="Scripts\layer\theme\default\layer.css" />
    <Content Include="Scripts\layer\theme\default\loading-0.gif" />
    <Content Include="Scripts\layer\theme\default\loading-1.gif" />
    <Content Include="Scripts\layer\theme\default\loading-2.gif" />
    <Content Include="Scripts\concatlayout.js" />
    <Content Include="Scripts\layoutNew.js" />
    <Content Include="Scripts\layout.js" />
    <Content Include="Scripts\laypage\laypage.css" />
    <Content Include="Scripts\laypage\laypage.js" />
    <Content Include="Scripts\LoadOpenTelemetry.js" />
    <Content Include="Scripts\moment\moment.min.js" />
    <Content Include="Scripts\moment\zh-cn.js" />
    <Content Include="Scripts\my-moreSelect\commonSearch.css" />
    <Content Include="Scripts\my-moreSelect\my-moreSelect.js" />
    <Content Include="Scripts\my-moreSelect\select-icon.png" />
    <Content Include="Scripts\NewXiaoHongShuPrinter.js" />
    <Content Include="Scripts\noviceIntro\noviceIntro.css" />
    <Content Include="Scripts\noviceIntro\noviceIntro.js" />
    <Content Include="Scripts\noviceIntro\noviceIntro_step.png" />
    <Content Include="Scripts\noviceIntro\noviceIntro_step_right.png" />
    <Content Include="Scripts\orderlist\AddTemplateInOrderListModule.js" />
    <Content Include="Scripts\orderlist\CustomerValidModule.js" />
    <Content Include="Scripts\orderlist\ExportOrderModule.js" />
    <Content Include="Scripts\orderlist\KeywordFilterRepalceModule.js" />
    <Content Include="Scripts\Order\ScanPrint.js" />
    <Content Include="Scripts\Order\OrderFailModule.js" />
    <Content Include="Scripts\Order\OrderPayModule.js" />
    <Content Include="Scripts\Order\SendFailModule.js" />
    <Content Include="Scripts\Order\CustomerValidModule.js" />
    <Content Include="Scripts\Order\ErpOrderTableBuilder.js" />
    <Content Include="Scripts\Order\ExportOrderModule.js" />
    <Content Include="Scripts\Order\OrderListModule.js" />
    <Content Include="Scripts\jquery-1.12.4.js" />
    <Content Include="Scripts\jquery-1.12.4.min.js" />
    <Content Include="Scripts\layui\css\layui.css" />
    <Content Include="Scripts\layui\css\layui.mobile.css" />
    <Content Include="Scripts\layui\css\modules\code.css" />
    <Content Include="Scripts\layui\css\modules\laydate\default\laydate.css" />
    <Content Include="Scripts\layui\css\modules\layer\default\icon-ext.png" />
    <Content Include="Scripts\layui\css\modules\layer\default\icon.png" />
    <Content Include="Scripts\layui\css\modules\layer\default\layer.css" />
    <Content Include="Scripts\layui\css\modules\layer\default\loading-0.gif" />
    <Content Include="Scripts\layui\css\modules\layer\default\loading-1.gif" />
    <Content Include="Scripts\layui\css\modules\layer\default\loading-2.gif" />
    <Content Include="Scripts\layui\font\iconfont.svg" />
    <Content Include="Scripts\layui\images\face\0.gif" />
    <Content Include="Scripts\layui\images\face\1.gif" />
    <Content Include="Scripts\layui\images\face\10.gif" />
    <Content Include="Scripts\layui\images\face\11.gif" />
    <Content Include="Scripts\layui\images\face\12.gif" />
    <Content Include="Scripts\layui\images\face\13.gif" />
    <Content Include="Scripts\layui\images\face\14.gif" />
    <Content Include="Scripts\layui\images\face\15.gif" />
    <Content Include="Scripts\layui\images\face\16.gif" />
    <Content Include="Scripts\layui\images\face\17.gif" />
    <Content Include="Scripts\layui\images\face\18.gif" />
    <Content Include="Scripts\layui\images\face\19.gif" />
    <Content Include="Scripts\layui\images\face\2.gif" />
    <Content Include="Scripts\layui\images\face\20.gif" />
    <Content Include="Scripts\layui\images\face\21.gif" />
    <Content Include="Scripts\layui\images\face\22.gif" />
    <Content Include="Scripts\layui\images\face\23.gif" />
    <Content Include="Scripts\layui\images\face\24.gif" />
    <Content Include="Scripts\layui\images\face\25.gif" />
    <Content Include="Scripts\layui\images\face\26.gif" />
    <Content Include="Scripts\layui\images\face\27.gif" />
    <Content Include="Scripts\layui\images\face\28.gif" />
    <Content Include="Scripts\layui\images\face\29.gif" />
    <Content Include="Scripts\layui\images\face\3.gif" />
    <Content Include="Scripts\layui\images\face\30.gif" />
    <Content Include="Scripts\layui\images\face\31.gif" />
    <Content Include="Scripts\layui\images\face\32.gif" />
    <Content Include="Scripts\layui\images\face\33.gif" />
    <Content Include="Scripts\layui\images\face\34.gif" />
    <Content Include="Scripts\layui\images\face\35.gif" />
    <Content Include="Scripts\layui\images\face\36.gif" />
    <Content Include="Scripts\layui\images\face\37.gif" />
    <Content Include="Scripts\layui\images\face\38.gif" />
    <Content Include="Scripts\layui\images\face\39.gif" />
    <Content Include="Scripts\layui\images\face\4.gif" />
    <Content Include="Scripts\layui\images\face\40.gif" />
    <Content Include="Scripts\layui\images\face\41.gif" />
    <Content Include="Scripts\layui\images\face\42.gif" />
    <Content Include="Scripts\layui\images\face\43.gif" />
    <Content Include="Scripts\layui\images\face\44.gif" />
    <Content Include="Scripts\layui\images\face\45.gif" />
    <Content Include="Scripts\layui\images\face\46.gif" />
    <Content Include="Scripts\layui\images\face\47.gif" />
    <Content Include="Scripts\layui\images\face\48.gif" />
    <Content Include="Scripts\layui\images\face\49.gif" />
    <Content Include="Scripts\layui\images\face\5.gif" />
    <Content Include="Scripts\layui\images\face\50.gif" />
    <Content Include="Scripts\layui\images\face\51.gif" />
    <Content Include="Scripts\layui\images\face\52.gif" />
    <Content Include="Scripts\layui\images\face\53.gif" />
    <Content Include="Scripts\layui\images\face\54.gif" />
    <Content Include="Scripts\layui\images\face\55.gif" />
    <Content Include="Scripts\layui\images\face\56.gif" />
    <Content Include="Scripts\layui\images\face\57.gif" />
    <Content Include="Scripts\layui\images\face\58.gif" />
    <Content Include="Scripts\layui\images\face\59.gif" />
    <Content Include="Scripts\layui\images\face\6.gif" />
    <Content Include="Scripts\layui\images\face\60.gif" />
    <Content Include="Scripts\layui\images\face\61.gif" />
    <Content Include="Scripts\layui\images\face\62.gif" />
    <Content Include="Scripts\layui\images\face\63.gif" />
    <Content Include="Scripts\layui\images\face\64.gif" />
    <Content Include="Scripts\layui\images\face\65.gif" />
    <Content Include="Scripts\layui\images\face\66.gif" />
    <Content Include="Scripts\layui\images\face\67.gif" />
    <Content Include="Scripts\layui\images\face\68.gif" />
    <Content Include="Scripts\layui\images\face\69.gif" />
    <Content Include="Scripts\layui\images\face\7.gif" />
    <Content Include="Scripts\layui\images\face\70.gif" />
    <Content Include="Scripts\layui\images\face\71.gif" />
    <Content Include="Scripts\layui\images\face\8.gif" />
    <Content Include="Scripts\layui\images\face\9.gif" />
    <Content Include="Scripts\layui\layui.all.js" />
    <Content Include="Scripts\layui\layui.js" />
    <Content Include="Scripts\layui\lay\modules\carousel.js" />
    <Content Include="Scripts\layui\lay\modules\code.js" />
    <Content Include="Scripts\layui\lay\modules\colorpicker.js" />
    <Content Include="Scripts\layui\lay\modules\element.js" />
    <Content Include="Scripts\layui\lay\modules\flow.js" />
    <Content Include="Scripts\layui\lay\modules\form.js" />
    <Content Include="Scripts\layui\lay\modules\jquery.js" />
    <Content Include="Scripts\layui\lay\modules\laydate.js" />
    <Content Include="Scripts\layui\lay\modules\layedit.js" />
    <Content Include="Scripts\layui\lay\modules\layer.js" />
    <Content Include="Scripts\layui\lay\modules\laypage.js" />
    <Content Include="Scripts\layui\lay\modules\laytpl.js" />
    <Content Include="Scripts\layui\lay\modules\mobile.js" />
    <Content Include="Scripts\layui\lay\modules\rate.js" />
    <Content Include="Scripts\layui\lay\modules\slider.js" />
    <Content Include="Scripts\layui\lay\modules\table.js" />
    <Content Include="Scripts\layui\lay\modules\transfer.js" />
    <Content Include="Scripts\layui\lay\modules\tree.js" />
    <Content Include="Scripts\layui\lay\modules\upload.js" />
    <Content Include="Scripts\layui\lay\modules\util.js" />
    <Content Include="Scripts\newCalender\css\newcalenderTime.css" />
    <Content Include="Scripts\newCalender\js\newcalenderTime.js" />
    <Content Include="Scripts\Order\OfflineOrderModule.js" />
    <Content Include="Scripts\Order\TkOrderPrintModule.js" />
    <Content Include="Scripts\Order\WaitOrderModule.js" />
    <Content Include="Scripts\Partner\MyAgentModule.js" />
    <Content Include="Scripts\Partner\MySupplierModule.js" />
    <Content Include="Scripts\Partner\CrossBorderModule.js" />
    <Content Include="Scripts\Partner\PartnerModule.js" />
    <Content Include="Scripts\Partner\sRegisterCommonModule.js" />
    <Content Include="Scripts\AddressSpliter.js" />
    <Content Include="Scripts\CaiNiaoPrinter.js" />
    <Content Include="Scripts\CommonModule.js" />
    <Content Include="Scripts\LodopPrinter.js" />
    <Content Include="Scripts\orderlist\PrintContentFormatSetModule.js" />
    <Content Include="Scripts\PinduoduoPrinter.js" />
    <Content Include="Scripts\PrintHistory\PrintHistorySecondModule.js" />
    <Content Include="Scripts\PrintHistory\PrintHistoryModule.js" />
    <Content Include="Scripts\progress\ProgressModule.js" />
    <Content Include="Scripts\Proudctlist\DistributionbaseListModule.js" />
    <Content Include="Scripts\Proudctlist\DistributionbaseSetModule.js" />
    <Content Include="Scripts\Proudctlist\PrepareDistribution.js" />
    <Content Include="Scripts\Proudctlist\CreateBasePlatformProduct.js" />
    <Content Include="Scripts\Proudctlist\BusinessCardCreateProduct.js" />
    <Content Include="Scripts\Proudctlist\CreateBaseProduct.js" />
    <Content Include="Scripts\Proudctlist\OfflineProductModule.js" />
    <Content Include="Scripts\Proudctlist\BindSupplierModule.js" />
    <Content Include="Scripts\Proudctlist\BaseOfPtSkuRelationDetail.js" />
    <Content Include="Scripts\Proudctlist\ProductBasicsModule.js" />
    <Content Include="Scripts\Proudctlist\RecycleProductModule.js" />
    <Content Include="Scripts\Proudctlist\ProudctlistModule.js" />
    <Content Include="Scripts\Purchase\NahuoLabelModule.js" />
    <Content Include="Scripts\Purchase\newpurchases.js" />
    <Content Include="Scripts\Purchase\PurchaseModule.js" />
    <Content Include="Scripts\Purchase\purchasesSet.js" />
    <Content Include="Scripts\qrcode.js" />
    <Content Include="Scripts\qrcode.min.js" />
    <Content Include="Scripts\react\babel.min.js" />
    <Content Include="Scripts\react\react-dom.production.min.js" />
    <Content Include="Scripts\react\react.production.min.js" />
    <Content Include="Scripts\SCPPrint.js" />
    <Content Include="Scripts\selectbox\funSelectbox.js" />
    <Content Include="Scripts\selectbox\selectbox.js" />
    <Content Include="Scripts\selectbox\selectboxLeaveMessageFlag.js" />
    <Content Include="Scripts\selectbox\cascadeSelectbox.js" />
    <Content Include="Scripts\selectbox\selectboxRemarksFlag.js" />
    <Content Include="Scripts\selectbox\funSelectbox.js" />
    <Content Include="Scripts\selectbox\selectbox2.js" />
    <Content Include="Scripts\SellerInfoSet\SellerInfoManagerModule.js" />
    <Content Include="Scripts\SendGoodTemplate.js" />
    <Content Include="Scripts\SendHistory\SendHistoryModule.js" />
    <Content Include="Scripts\SendLogistic.js" />
    <Content Include="Scripts\select2\select2-spinner.gif" />
    <Content Include="Scripts\select2\select2.css" />
    <Content Include="Scripts\select2\select2.js" />
    <Content Include="Scripts\select2\select2.png" />
    <Content Include="Scripts\select2\select2_locale_zh-CN.js" />
    <Content Include="Scripts\sendTemplate\artDialog.js" />
    <Content Include="Scripts\sendTemplate\artDialog.plugins.min.js" />
    <Content Include="Scripts\sendTemplate\dialog.js" />
    <Content Include="Scripts\sendTemplate\jquery.slimscroll.min.js" />
    <Content Include="Scripts\sendTemplate\json2.js" />
    <Content Include="Scripts\sendTemplate\qrcode.min.js" />
    <Content Include="Scripts\sendTemplate\setSendList.js" />
    <Content Include="Scripts\sendTemplate\setSendTemplate.js" />
    <Content Include="Scripts\sendTemplate\setSendTemplate2.js" />
    <Content Include="Scripts\sendTemplate\Uploader.js" />
    <Content Include="Scripts\SendOrder\SendOrderModule.js" />
    <Content Include="Scripts\SetInfo\ReachCompareModule.js" />
    <Content Include="Scripts\Sortable.min.js" />
    <Content Include="Scripts\supplySet1688\ShippingFeeSetModule.js" />
    <Content Include="Scripts\supplySet1688\SkuBindModule.js" />
    <Content Include="Scripts\supplySet1688\SkuUnBindModule.js" />
    <Content Include="Scripts\threeAreaInfoModule.js" />
    <Content Include="Scripts\SetInfo\ExpressReachModule.js" />
    <Content Include="Scripts\SharedWaybillAccountCheck\sharedWaybillAccountCheckModule.js" />
    <Content Include="Scripts\ShareWayBillAccount\shareWayBillAccountModule.js" />
    <Content Include="Scripts\StockControl\AddProductByShopModule.js" />
    <Content Include="Scripts\StockControl\AddProductModule.js" />
    <Content Include="Scripts\StockControl\ChangeDetailModule.js" />
    <Content Include="Scripts\StockControl\EditProductModule.js" />
    <Content Include="Scripts\StockControl\CombinationProductModule.js" />
    <Content Include="Scripts\StockControl\SkuUnBind.js" />
    <Content Include="Scripts\StockControl\StockDetailModule.js" />
    <Content Include="Scripts\StockControl\SkuBind.js" />
    <Content Include="Scripts\StockControl\StoreManagementModule.js" />
    <Content Include="Scripts\StockControl\WareHouseProductModule.js" />
    <Content Include="Scripts\systemSet\DistributeSetModule.js" />
    <Content Include="Scripts\systemSet\BasicsetModule.js" />
    <Content Include="Scripts\TemplateSet\AddTemplateModule.js" />
    <Content Include="Scripts\TemplateSet\EditSiteTemplateModule.js" />
    <Content Include="Scripts\TemplateSet\EditTemplateModule.js" />
    <Content Include="Scripts\TemplateSet\EditTraditionTemplateModule.js" />
    <Content Include="Scripts\TemplateSet\EditWaybillTemplateModule.js" />
    <Content Include="Scripts\TemplateSet\TemplateListModule.js" />
    <Content Include="Scripts\TemplateSet\TemplateSetCommonModule.js" />
    <Content Include="Scripts\TkPrintProcessState\TkPrintProcessStateModule.js" />
    <Content Include="Scripts\tinymce\js\tinymce\icons\default\icons.min.js" />
    <Content Include="Scripts\tinymce\js\tinymce\langs\zh_CN.js" />
    <Content Include="Scripts\tinymce\js\tinymce\models\dom\model.min.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\accordion\plugin.min.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\advlist\plugin.min.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\anchor\plugin.min.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\autolink\plugin.min.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\autoresize\plugin.min.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\autosave\plugin.min.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\charmap\plugin.min.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\codesample\plugin.min.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\code\plugin.min.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\directionality\plugin.min.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\emoticons\js\emojiimages.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\emoticons\js\emojiimages.min.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\emoticons\js\emojis.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\emoticons\js\emojis.min.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\emoticons\plugin.min.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\fullscreen\plugin.min.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\help\js\i18n\keynav\ar.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\help\js\i18n\keynav\bg_BG.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\help\js\i18n\keynav\ca.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\help\js\i18n\keynav\cs.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\help\js\i18n\keynav\da.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\help\js\i18n\keynav\de.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\help\js\i18n\keynav\el.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\help\js\i18n\keynav\en.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\help\js\i18n\keynav\es.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\help\js\i18n\keynav\eu.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\help\js\i18n\keynav\fa.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\help\js\i18n\keynav\fi.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\help\js\i18n\keynav\fr_FR.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\help\js\i18n\keynav\he_IL.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\help\js\i18n\keynav\hi.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\help\js\i18n\keynav\hr.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\help\js\i18n\keynav\hu_HU.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\help\js\i18n\keynav\id.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\help\js\i18n\keynav\it.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\help\js\i18n\keynav\ja.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\help\js\i18n\keynav\kk.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\help\js\i18n\keynav\ko_KR.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\help\js\i18n\keynav\ms.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\help\js\i18n\keynav\nb_NO.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\help\js\i18n\keynav\nl.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\help\js\i18n\keynav\pl.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\help\js\i18n\keynav\pt_BR.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\help\js\i18n\keynav\pt_PT.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\help\js\i18n\keynav\ro.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\help\js\i18n\keynav\ru.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\help\js\i18n\keynav\sk.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\help\js\i18n\keynav\sl_SI.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\help\js\i18n\keynav\sv_SE.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\help\js\i18n\keynav\th_TH.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\help\js\i18n\keynav\tr.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\help\js\i18n\keynav\uk.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\help\js\i18n\keynav\vi.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\help\js\i18n\keynav\zh_CN.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\help\js\i18n\keynav\zh_TW.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\help\plugin.min.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\image\plugin.min.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\importcss\plugin.min.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\insertdatetime\plugin.min.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\link\plugin.min.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\lists\plugin.min.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\media\plugin.min.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\nonbreaking\plugin.min.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\pagebreak\plugin.min.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\preview\plugin.min.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\quickbars\plugin.min.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\save\plugin.min.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\searchreplace\plugin.min.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\table\plugin.min.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\visualblocks\plugin.min.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\visualchars\plugin.min.js" />
    <Content Include="Scripts\tinymce\js\tinymce\plugins\wordcount\plugin.min.js" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\content\dark\content.js" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\content\dark\content.min.css" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\content\default\content.js" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\content\default\content.min.css" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\content\document\content.js" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\content\document\content.min.css" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\content\tinymce-5-dark\content.js" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\content\tinymce-5-dark\content.min.css" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\content\tinymce-5\content.js" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\content\tinymce-5\content.min.css" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\content\writer\content.js" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\content\writer\content.min.css" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\ui\oxide-dark\content.inline.js" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\ui\oxide-dark\content.inline.min.css" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\ui\oxide-dark\content.js" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\ui\oxide-dark\content.min.css" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\ui\oxide-dark\skin.js" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\ui\oxide-dark\skin.min.css" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\ui\oxide-dark\skin.shadowdom.js" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\ui\oxide-dark\skin.shadowdom.min.css" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\ui\oxide\content.inline.js" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\ui\oxide\content.inline.min.css" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\ui\oxide\content.js" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\ui\oxide\content.min.css" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\ui\oxide\skin.js" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\ui\oxide\skin.min.css" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\ui\oxide\skin.shadowdom.js" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\ui\oxide\skin.shadowdom.min.css" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\ui\tinymce-5-dark\content.inline.js" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\ui\tinymce-5-dark\content.inline.min.css" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\ui\tinymce-5-dark\content.js" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\ui\tinymce-5-dark\content.min.css" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\ui\tinymce-5-dark\skin.js" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\ui\tinymce-5-dark\skin.min.css" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\ui\tinymce-5-dark\skin.shadowdom.js" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\ui\tinymce-5-dark\skin.shadowdom.min.css" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\ui\tinymce-5\content.inline.js" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\ui\tinymce-5\content.inline.min.css" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\ui\tinymce-5\content.js" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\ui\tinymce-5\content.min.css" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\ui\tinymce-5\skin.js" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\ui\tinymce-5\skin.min.css" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\ui\tinymce-5\skin.shadowdom.js" />
    <Content Include="Scripts\tinymce\js\tinymce\skins\ui\tinymce-5\skin.shadowdom.min.css" />
    <Content Include="Scripts\tinymce\js\tinymce\themes\silver\theme.min.js" />
    <Content Include="Scripts\tinymce\js\tinymce\tinymce.min.js" />
    <Content Include="Scripts\TouTiaoPrinter.js" />
    <Content Include="Scripts\upPicFileModule.js" />
    <Content Include="Scripts\Warehousing.js" />
    <Content Include="Scripts\WaybillCodeList\WaybillCodeListModule.js" />
    <Content Include="Scripts\wuDesign\wuFormModule.js" />
    <Content Include="Scripts\WxVideoPrinter.js" />
    <Content Include="Scripts\XiaoHongShuPrinter.js" />
    <Content Include="Views\Components\CombinationProductDialog.cshtml" />
    <Content Include="Views\BaseProduct\BaseProductOperationLog.cshtml" />
    <Content Include="Views\Components\BaseProductComponets.cshtml" />
    <Content Include="Views\BaseProduct\DistributionSet_partial.cshtml" />
    <Content Include="Views\BaseProduct\ExceptionLog.cshtml" />
    <Content Include="Views\BaseProduct\DistributionSettings.cshtml" />
    <Content Include="Views\BaseProduct\DistributionSet_list.cshtml" />
    <Content Include="NLog.config" />
    <Content Include="Views\Collect\CollectView.cshtml" />
    <Content Include="Content\css\wuDesign\unit.less" />
    <Content Include="Content\css\wuDesign\wu-skin.less" />
    <Content Include="Content\css\wuDesign\wu-platform.less" />
    <Content Include="Content\css\wuDesign\wu-skin2.less" />
    <Content Include="Scripts\guidelayer\说明.md" />
    <Content Include="Files\Templates\导入编码模板.xlsx" />
    <None Include="Views\Components\CommonDrawer\CommonDrawer.md" />
    <Content Include="Views\FinancialSettlement\MyShops.cshtml" />
    <Content Include="Web.config" />
    <Content Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Views\System\DistributionGradeSet.cshtml" />
    <Content Include="Views\Product\SetSettlementPriceTemplate.cshtml" />
    <Content Include="Views\TkPrintProcessStateRecord\Index.cshtml" />
    <Content Include="Views\FinancialSettlement\BillCenter.cshtml" />
    <Content Include="Views\ShopVideo\Index.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Files\TempFiles\" />
    <Folder Include="Views\ExpressTemplate\" />
    <Folder Include="Views\OpenTelemetryApi\" />
    <Folder Include="Views\ProductApi\" />
    <Folder Include="Views\PtProduct\" />
    <Folder Include="Views\Puhuo\" />
    <Folder Include="Views\ScanPrint\" />
    <Folder Include="Views\TemplateType\" />
    <Folder Include="Views\TestApi\" />
    <Folder Include="Views\UserListingSetting\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Config\AppSettings.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="Config\ConnectionStrings.config" />
    <Content Include="Content\css\fonts\iconfont.eot" />
    <Content Include="Content\css\fonts\iconfont.json" />
    <Content Include="Content\css\fonts\iconfont.ttf" />
    <Content Include="Content\css\fonts\iconfont.woff" />
    <Content Include="Content\css\fonts\iconfont.woff2" />
    <Content Include="Content\images\allicons2.png.orig" />
    <Content Include="font\iconfont.eot" />
    <Content Include="font\iconfont.json" />
    <Content Include="font\iconfont.ttf" />
    <Content Include="font\iconfont.woff" />
    <Content Include="font\iconfont.woff2" />
    <Content Include="Files\Templates\快递对账模板.xlsx" />
    <Content Include="Files\ExpressReach\省市区模板.xlsx" />
    <Content Include="Files\ImportOfflineOrder\批量导入模板.xls" />
    <Content Include="Content\productData.json" />
    <Content Include="Content\xyProductList.json" />
    <Content Include="Files\JsonFiles\ObjectBucket.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Files\ImportBaseProduct\批量导入模板.xls" />
    <Content Include="Files\FinancialSettlement\SKUID导入模板.xls" />
    <Content Include="Files\FinancialSettlement\SKU编码导入模板.xls" />
    <Content Include="Files\ProfitStatistics\导入其他成本模板.xlsx" />
    <None Include="packages.config" />
    <Content Include="Scripts\newCalender\js\组件说明.md" />
    <Content Include="Scripts\layui\font\iconfont.eot" />
    <Content Include="Scripts\layui\font\iconfont.ttf" />
    <Content Include="Scripts\layui\font\iconfont.woff" />
    <Content Include="Scripts\layui\font\iconfont.woff2" />
    <None Include="Properties\PublishProfiles\erpWeb.pubxml" />
    <Content Include="Scripts\noviceIntro\说明.md" />
    <Content Include="Scripts\my-moreSelect\说明.md" />
    <Content Include="Scripts\countdownTimer\new_file.md" />
    <Content Include="Views\AccountList\Index.cshtml" />
    <Content Include="Views\AccountList\Index.cshtml.orig" />
    <Content Include="Views\AfterSale\Index.cshtml" />
    <Content Include="Views\Auth\Index.cshtml" />
    <Content Include="Views\Auth\Login.cshtml" />
    <Content Include="Views\Auth\Statistic.cshtml" />
    <Content Include="Views\BaseProduct\BaseProduct.cshtml" />
    <Content Include="Views\BaseProduct\BaseProductSkuRelationBindIndex.cshtml" />
    <Content Include="Views\BaseProduct\BaseProductSkuRelationDetailIndex.cshtml" />
    <Content Include="Views\Common\AliDistributionSetSteps.cshtml" />
    <Content Include="Views\Common\ChoseProvincePartialView.cshtml" />
    <Content Include="Views\Common\ImportFreePrint.cshtml" />
    <Content Include="Views\Common\ImportFreePrint_OldVersion.cshtml" />
    <Content Include="Views\Common\KS_ImportFreePrint.cshtml" />
    <Content Include="Views\Common\leftNav.cshtml" />
    <Content Include="Views\Common\leftNav.cshtml.orig" />
    <Content Include="Views\Common\moreFunLeftNav.cshtml" />
    <Content Include="Views\Common\moreFunLeftNav.cshtml.orig" />
    <Content Include="Views\Common\Page.cshtml" />
    <Content Include="Views\Common\Page.cshtml.orig" />
    <Content Include="Views\Common\PartialPrintTemplate.cshtml" />
    <Content Include="Views\Common\PartialPrintTemplate.cshtml.orig" />
    <Content Include="Views\Common\PrintCommonDiv.cshtml" />
    <Content Include="Views\Common\PrintPreViewPartialView.cshtml" />
    <Content Include="Views\Common\printSetNav.cshtml" />
    <Content Include="Views\Common\SupplySet1688Nav.cshtml" />
    <Content Include="Views\Common\TestPage.cshtml" />
    <Content Include="Views\Common\threeAreaInfo.cshtml" />
    <Content Include="Views\DistributionProduct\AfterSaleList.cshtml" />
    <Content Include="Views\DistributionProduct\AgentProductList.cshtml" />
    <Content Include="Views\DistributionProduct\BackflowList.cshtml" />
    <Content Include="Views\DistributionProduct\ListByAgent.cshtml" />
    <Content Include="Views\DistributionProduct\ListBySupplier.cshtml" />
    <Content Include="Views\DistributionProduct\RelationAgentProductList.cshtml" />
    <Content Include="Views\DistributionProduct\SkuBind.cshtml" />
    <Content Include="Views\DistributionProduct\SkuUnBind.cshtml" />
    <Content Include="Views\ExpressBill\Index.cshtml" />
    <Content Include="Views\FinancialSettlement\BillManagement.cshtml" />
    <Content Include="Views\FinancialSettlement\BillManagement.cshtml.orig" />
    <Content Include="Views\FinancialSettlement\Index.cshtml" />
    <Content Include="Views\FinancialSettlement\OutAccountProduct.cshtml" />
    <Content Include="Views\FinancialSettlement\PriceSetting.cshtml" />
    <Content Include="Views\FinancialSettlement\PriceSettingClound.cshtml" />
    <Content Include="Views\FinancialSettlement\PriceSettingProduct.cshtml" />
    <Content Include="Views\FinancialSettlement\PriceSetting_Agent.cshtml" />
    <Content Include="Views\FinancialSettlement\PriceSetting_Supplier.cshtml" />
    <Content Include="Views\FinancialSettlement\SkuRecycle.cshtml" />
    <Content Include="Views\FinancialSettlement\SkuRecycleAgent.cshtml" />
    <Content Include="Views\FinancialSettlement\SkuRecycleSupplier.cshtml" />
    <Content Include="Views\FundsManagement\FundsManagementNav.cshtml" />
    <Content Include="Views\FundsManagement\Index.cshtml" />
    <Content Include="Views\FundsManagement\ShowTransactionDetailAgent.cshtml" />
    <Content Include="Views\FundsManagement\ShowTransactionDetailSupplier.cshtml" />
    <Content Include="Views\FundsManagement\TransactionDetailAgent.cshtml" />
    <Content Include="Views\FundsManagement\TransactionDetailSupplier.cshtml" />
    <Content Include="Views\GeneralizeIndex\AliDistributionIntroduce.cshtml" />
    <Content Include="Views\GeneralizeIndex\Index.cshtml" />
    <Content Include="Views\GeneralizeIndex\Index.cshtml.orig" />
    <Content Include="Views\GeneralizeIndex\LevelExplain.cshtml" />
    <Content Include="Views\LogHistory\Index.cshtml" />
    <Content Include="Views\NewOrder\AbnormalOrder.cshtml" />
    <Content Include="Views\NewOrder\AliIncludeOrder.cshtml" />
    <Content Include="Views\NewOrder\AliIncludePayOrder.cshtml" />
    <Content Include="Views\NewOrder\AllOrder.cshtml" />
    <Content Include="Views\NewOrder\Index.cshtml" />
    <Content Include="Views\NewOrder\OfflineOrder.cshtml" />
    <Content Include="Views\NewOrder\OrderFail.cshtml" />
    <Content Include="Views\NewOrder\PartialOrderTemplate.cshtml" />
    <Content Include="Views\NewOrder\PasswordFreePayTemplate.cshtml" />
    <Content Include="Views\NewOrder\PayDialogTemplate.cshtml" />
    <Content Include="Views\NewOrder\PaymentFailureLog.cshtml" />
    <Content Include="Views\NewOrder\SearchCondition.cshtml" />
    <Content Include="Views\NewOrder\SendFail.cshtml" />
    <Content Include="Views\NewOrder\WaitOrder.cshtml" />
    <Content Include="Views\Order\ConfigurationTemplateView.cshtml" />
    <Content Include="Views\Order\PrintContentInfoPartialView.cshtml" />
    <Content Include="Views\Order\PrintContentSetting.cshtml" />
    <Content Include="Views\Partner\Index.cshtml" />
    <Content Include="Views\Partner\Index.cshtml.orig" />
    <Content Include="Views\Partner\MyAgent.cshtml" />
    <Content Include="Views\Partner\MyAgent.cshtml.orig" />
    <Content Include="Views\Partner\MySupplier.cshtml" />
    <Content Include="Views\Partner\MySupplier.cshtml.orig" />
    <Content Include="Views\Partner\registerIntoContent.cshtml" />
    <Content Include="Views\PhoneQrCode\Index.cshtml" />
    <Content Include="Views\PrintConfig\Index.cshtml" />
    <Content Include="Views\PrintHistory\Index.cshtml" />
    <Content Include="Views\PrintHistory\SecondSendList.cshtml" />
    <Content Include="Views\BaseProduct\BaseOfPtSkuRelation_detail.cshtml" />
    <Content Include="Views\BaseProduct\BaseOfPtSkuRelation_detail_products.cshtml" />
    <Content Include="Views\BaseProduct\BaseOfPtSkuRelation_equal.cshtml" />
    <Content Include="Views\BaseProduct\BaseOfPtSkuRelation_equal_products.cshtml" />
    <Content Include="Views\Product\BindSupplierTemplate.cshtml" />
    <Content Include="Views\Product\Index.cshtml" />
    <Content Include="Views\Product\OfflineProduct.cshtml" />
    <Content Include="Views\Product\ProductBasics.cshtml" />
    <Content Include="Views\Product\Recycle.cshtml" />
    <Content Include="Views\Purchases\Index.cshtml" />
    <Content Include="Views\Purchases\KS_SearchCondition.cshtml" />
    <Content Include="Views\Purchases\newPurchases.cshtml" />
    <Content Include="Views\Purchases\PurchasesSet.cshtml" />
    <Content Include="Views\Purchases\RenderPurchaseList.cshtml" />
    <Content Include="Views\Purchases\SearchCondition.cshtml" />
    <Content Include="Views\Purchases\SearchCondition_OldVersion.cshtml" />
    <Content Include="Views\Purchases\Test.cshtml" />
    <Content Include="Views\SellerInfo\AddSellerInfo.cshtml" />
    <Content Include="Views\SellerInfo\Index.cshtml" />
    <Content Include="Views\SendGoodTemplate\Edit.cshtml" />
    <Content Include="Views\SendGoodTemplate\Index.cshtml" />
    <Content Include="Views\SendGoodTemplate\OldVersion.cshtml" />
    <Content Include="Views\SendHistoryReturnRecord\List.cshtml" />
    <Content Include="Views\SendHistory\Index.cshtml" />
    <Content Include="Views\SendOrder\Index.cshtml" />
    <Content Include="Views\SendOrder\Index.cshtml.orig" />
    <Content Include="Views\SetInfo\ExpressReach.cshtml" />
    <Content Include="Views\SetInfo\FakeSite.cshtml" />
    <Content Include="Views\SetInfo\Index.cshtml" />
    <Content Include="Views\SetInfo\ManyCodeSendSet.cshtml" />
    <Content Include="Views\SharedWaybillAccountCheck\Index.cshtml" />
    <Content Include="Views\Shared\500.cshtml" />
    <Content Include="Views\Shared\aliDistributionBanner.cshtml" />
    <Content Include="Views\Shared\contact.cshtml" />
    <Content Include="Views\Shared\Error.cshtml" />
    <Content Include="Views\Shared\_CloudPlatformLayout.cshtml" />
    <Content Include="Views\ExportTask\Index.cshtml" />
    <Content Include="Views\System\Qualification.cshtml" />
    <Content Include="Views\System\SystemNav.cshtml" />
    <Content Include="Views\System\Evaluates.cshtml" />
    <Content Include="Views\System\EdmitQualification.cshtml" />
    <Content Include="Views\Partner\CheckQualification.cshtml" />
    <Content Include="Views\Partner\SubmitEvaluates.cshtml" />
    <Content Include="Views\Shared\_Layout.cshtml" />
    <Content Include="Views\FinancialSettlement\quickOutAccount.cshtml" />
    <Content Include="Views\FinancialSettlement\CostPriceSettingProduct.cshtml" />
    <Content Include="Views\Shared\_NewLayout.cshtml" />
    <Content Include="Views\ShareWayBillAccount\Index.cshtml" />
    <Content Include="Views\ShareWayBillAccount\Index.cshtml.orig" />
    <Content Include="Views\StockControl\AddProduct.cshtml" />
    <Content Include="Views\StockControl\AddProductByShop.cshtml" />
    <Content Include="Views\StockControl\Bind.cshtml" />
    <Content Include="Views\StockControl\ChangeDetail.cshtml" />
    <Content Include="Views\StockControl\EditProduct.cshtml" />
    <Content Include="Views\StockControl\Index.cshtml" />
    <Content Include="Views\StockControl\StockDetail.cshtml" />
    <Content Include="Views\StockControl\StockDetail.cshtml.orig" />
    <Content Include="Views\StockControl\StoreManagement.cshtml" />
    <Content Include="Views\StockControl\UnBind.cshtml" />
    <Content Include="Views\StockControl\WareHouseProduct.cshtml" />
    <Content Include="Views\SupplySet1688\AfterSaleManagement.cshtml" />
    <Content Include="Views\SupplySet1688\DistributionOrderManagement.cshtml" />
    <Content Include="Views\SupplySet1688\DistributionProductList.cshtml" />
    <Content Include="Views\SupplySet1688\DistributionProductSearch.cshtml" />
    <Content Include="Views\SupplySet1688\DistributionProductSet.cshtml" />
    <Content Include="Views\SupplySet1688\DistributorMapProduct.cshtml" />
    <Content Include="Views\SupplySet1688\DistributorSet.cshtml" />
    <Content Include="Views\SupplySet1688\Index.cshtml" />
    <Content Include="Views\SupplySet1688\ShippingFeeSet.cshtml" />
    <Content Include="Views\SupplySet1688\SupplierSetBy1688.cshtml" />
    <Content Include="Views\System\BindWxUserIndex.cshtml" />
    <Content Include="Views\System\DistributeSet.cshtml" />
    <Content Include="Views\System\DistributeSet.cshtml.orig" />
    <Content Include="Views\System\Index.cshtml" />
    <Content Include="Views\System\WxCorrelationSuccess.cshtml" />
    <Content Include="Views\TemplateSet\AddTemplate.cshtml" />
    <Content Include="Views\TemplateSet\EditSiteTemplate.cshtml" />
    <Content Include="Views\TemplateSet\EditTraditionTemplate.cshtml" />
    <Content Include="Views\TemplateSet\EditWaybillTemplate.cshtml" />
    <Content Include="Views\TemplateSet\Index.cshtml" />
    <Content Include="Views\TemplateSet\Index.cshtml.orig" />
    <Content Include="Views\WaybillCodeList\Index.cshtml" />
    <Content Include="Views\Web.config" />
    <Content Include="Views\_ViewStart.cshtml" />
    <Content Include="Views\Product\CreateBaseProduct.cshtml" />
    <Content Include="Views\ExportTask\rightContentRender.cshtml" />
    <Content Include="Views\GeneralizeIndex\demandContent.cshtml" />
    <Content Include="Views\NewOrder\OrderLifeCycleTool.cshtml" />
    <Content Include="Views\FinancialSettlement\CostPriceSettingProductWrap.cshtml" />
    <Content Include="Views\NewOrder\ScanPrint.cshtml" />
    <Content Include="Views\Common\comSearchOrder.cshtml" />
    <Content Include="Views\Common\Warehousing.cshtml" />
    <Content Include="Views\AfterSale\ManualAfterSale.cshtml" />
    <Content Include="Views\AfterSale\CreateManualAfterSale.cshtml" />
    <Content Include="Views\BaseProduct\CreateBaseProduct.cshtml" />
    <Content Include="Views\BaseProduct\BaseOfPtProducts.cshtml" />
    <Content Include="Views\Common\cmCloudPlatform.cshtml" />
    <Content Include="Views\ProfitStatisticsView\Index.cshtml" />
    <Content Include="Views\ProfitStatisticsView\ProfitStatement.cshtml" />
    <Content Include="Views\ProfitStatisticsView\ProfitStatementIndex.cshtml" />
    <Content Include="Views\ProfitStatisticsView\ProfitStatistics.cshtml" />
    <Content Include="Views\Partner\CrossBorder.cshtml" />
    <Content Include="Views\Common\TkPartialTemplate.cshtml" />
    <Content Include="Views\Components\Popover.cshtml" />
    <Content Include="Views\DistributionProduct\SelectionDistribution.cshtml" />
    <Content Include="Views\DistributionProduct\DistributionDetails.cshtml" />
    <Content Include="Views\BaseProduct\BusinessCardCreateProduct.cshtml" />
    <Content Include="Views\Shared\empty.cshtml" />
    <Content Include="Views\BaseProduct\CreateBasePlatformProduct.cshtml" />
    <Content Include="Views\BaseProduct\DistributionLog.cshtml" />
    <Content Include="Views\DistributionProduct\UserCallingCardProduct.cshtml" />
    <Content Include="Views\BaseProduct\PrepareDistribution.cshtml" />
    <Content Include="Views\Station\StationQuickAdd.cshtml" />
    <Content Include="Views\GeneralizeIndex\MyStationCard.cshtml" />
    <Content Include="Views\GeneralizeIndex\ApplicationIndex.cshtml" />
    <Content Include="Views\FreightTemplate\NewShippingFeeSet.cshtml" />
    <Content Include="Views\AbnormalProductChange\Index.cshtml" />
    <Content Include="Views\AbnormalProductChange\AbnormalProductContent.cshtml" />
    <Content Include="Views\Components\CommonDrawer\ComonDrawer.cshtml" />
    <Content Include="Views\BaseProduct\BaseOfPtSkuRelationEqualProducts.cshtml" />
    <Content Include="Views\BaseProduct\BaseOfPtSkuUnrelatedList.cshtml" />
    <Content Include="Views\BaseProduct\NewBaseProduct.cshtml" />
    <Content Include="Views\Collect\CollectProduct.cshtml" />
    <Content Include="Views\Collect\CollectIndex.cshtml" />
    <Content Include="Views\GlobalProduct\Index.cshtml" />
    <Content Include="Views\Collect\EditCollectProduct.cshtml" />
    <Content Include="Scripts\tinymce\CHANGELOG.md" />
    <Content Include="Scripts\tinymce\js\tinymce\langs\README.md" />
    <Content Include="Scripts\tinymce\js\tinymce\license.md" />
    <Content Include="Views\Collect\EditGlobalProduct.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\AutoComment\DianGuanJiaApp.RabbitMQ\DianGuanJiaApp.RabbitMQ.csproj">
      <Project>{90dc55be-abba-4686-8119-a18e012f540e}</Project>
      <Name>DianGuanJiaApp.RabbitMQ</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Data\DianGuanJiaApp.Data.csproj">
      <Project>{6c8b9657-fbaa-46c8-8433-1b2f908e8398}</Project>
      <Name>DianGuanJiaApp.Data</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Services\DianGuanJiaApp.Services.csproj">
      <Project>{abee0a3a-3b86-4631-add0-3bdd17a0b1fc}</Project>
      <Name>DianGuanJiaApp.Services</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.SiteMessage\DianGuanJiaApp.SiteMessage.csproj">
      <Project>{776BDADB-85E8-4C5B-9B21-3C9235A36A79}</Project>
      <Name>DianGuanJiaApp.SiteMessage</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Utility\DianGuanJiaApp.Utility.csproj">
      <Project>{a0ead1ef-8b94-4487-ad73-f981cb45e58f}</Project>
      <Name>DianGuanJiaApp.Utility</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.ViewModels\DianGuanJiaApp.ViewModels.csproj">
      <Project>{549A33CF-F7FB-49EF-B8BD-1AAE56317663}</Project>
      <Name>DianGuanJiaApp.ViewModels</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Warehouse\DianGuanJiaApp.Warehouse.csproj">
      <Project>{a5ad179b-04e3-44e4-a90c-6260199de89a}</Project>
      <Name>DianGuanJiaApp.Warehouse</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Web\DianGuanJiaApp.Web.csproj">
      <Project>{9eff6008-ab13-4524-97e5-efd416e72ce9}</Project>
      <Name>DianGuanJiaApp.Web</Name>
    </ProjectReference>
    <ProjectReference Include="..\jos-net-open-api-sdk-2.0\jos-sdk-net.csproj">
      <Project>{cf7757d6-4f03-4bca-948d-d1e0b81b491c}</Project>
      <Name>jos-sdk-net</Name>
    </ProjectReference>
    <ProjectReference Include="..\ych-sdk\ych-sdk.csproj">
      <Project>{aaf61c3f-5729-4e3c-bc8c-eea8067e0305}</Project>
      <Name>ych-sdk</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{4A0DDDB5-7A95-4FBF-97CC-616D07737A77}" />
  </ItemGroup>
  <ItemGroup>
    <TypeScriptCompile Include="Scripts\tinymce\js\tinymce\tinymce.d.ts" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.targets" Condition="Exists('$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.targets')" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <Target Name="MvcBuildViews" AfterTargets="AfterBuild" Condition="'$(MvcBuildViews)'=='true'">
    <AspNetCompiler VirtualPath="temp" PhysicalPath="$(WebProjectOutputDir)" />
  </Target>
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>55885</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:44333</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Import Project="..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.3.6.0\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets" Condition="Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.3.6.0\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>这台计算机上缺少此项目引用的 NuGet 程序包。使用“NuGet 程序包还原”可下载这些程序包。有关更多信息，请参见 http://go.microsoft.com/fwlink/?LinkID=322105。缺少的文件是 {0}。</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.3.6.0\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.3.6.0\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets'))" />
    <Error Condition="!Exists('..\packages\Microsoft.NETFramework.ReferenceAssemblies.net461.1.0.3\build\Microsoft.NETFramework.ReferenceAssemblies.net461.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.NETFramework.ReferenceAssemblies.net461.1.0.3\build\Microsoft.NETFramework.ReferenceAssemblies.net461.targets'))" />
  </Target>
  <Import Project="..\packages\Microsoft.NETFramework.ReferenceAssemblies.net461.1.0.3\build\Microsoft.NETFramework.ReferenceAssemblies.net461.targets" Condition="Exists('..\packages\Microsoft.NETFramework.ReferenceAssemblies.net461.1.0.3\build\Microsoft.NETFramework.ReferenceAssemblies.net461.targets')" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target> -->
</Project>