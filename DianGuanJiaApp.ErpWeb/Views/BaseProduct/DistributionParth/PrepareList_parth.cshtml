
<div class="n-main" id="New_setPrepareDistribution_list">
    <div class="n-layui-mywrap" style="width: 100%;padding:0;">
        <ul class="n-tabNav" id="changeDistributionbaseListNav">
            <li class="n-tabNav-item active">已完善平台资料(<i id="NeedUpdateCountY"></i>)</li>
            <li class="n-tabNav-item">未完善平台资料 (<i id="NeedUpdateCountN"></i>)</li>
        </ul>
        <div class="distributionsListWrap" id="distributionsListTableWrap">
            <table class="n-table edmitTable activeEdmitTable active">
                <thead>
                    <tr id="distributionsList_tr">
                        <th>
                            <div class="th-header">
                                <span id="DistributionbaseListAll_check" class="n-newCheckbox" onclick="DistributionbaseListAllCheck.bind(this)()"></span>
                                <span>商品信息</span>
                            </div>
                        </th>
                        <th class="ishide_pdd" style="width:212px;max-width:212px;" >
                            售价
                            @*<i class="iconfont icon-a-help-circle1x popoverCommon"><span class="popoverCommon-warn" style="width:240px;">xxxxxxxxxxxxxxxxxxx</span></i>*@

                        </th>
                        <th class="isshow_table_pdd" style="width:160px;max-width:180px;display:none;">拼单价</th>
                        <th class="isshow_table_pdd" style="width:160px;max-width:180px;display:none;">
                        单买价/参考价
                        <i class="iconfont icon-a-help-circle1x popoverCommon"><span class="popoverCommon-warn" style="width:240px;">单买价：至少比拼单价高1元 <br>参考价：参考价即吊牌价、市场价、需高于商品单买价，并且不得超过单买价的5倍</span></i>
                        </th>

                        <th style="width:120px;max-width:120px;">库存</th>
                        <th class="leftOperate" style="width:80px;max-width:80px;">操作</th>
                    </tr>
                </thead>
                <tbody id="DistributionbaseListY_data"></tbody>
            </table>
            <div class="n-alert n-alert-03" style="margin:16px;display:none;" id="DistributionbaseListN_tar"><i class="iconfont icon-gantan"></i>未完善平台资料的商品无法铺货，请联系供货商或自主完善平台资料。</div>
            <table class="n-table edmitTable activeEdmitTable active" style="display:none">
                <thead>
                    <tr>
                        <th>
                            <div class="th-header">
                                <span>商品信息</span>
                            </div>
                        </th>
                        <th class="leftOperate" style="width:80px;max-width:80px;">操作</th>
                    </tr>
                </thead>
                <tbody id="DistributionbaseListN_data"></tbody>
            </table>

        </div>
    </div>
</div>



<div class="createBase-footer" style="justify-content:flex-start;">
    <span class="n-mButton n-sActive" style="margin-right:10px;" onclick="GoLastBatchDistribution.bind(this)()">上一步</span>
    <span id="batchDistributionbtn" class="n-mButton stop" style="margin-right:10px;margin-left:0;" onclick="SubmitBatchDistribution.bind(this)()">
        <i id="batchDistributionbtn_title">开始铺货</i>
        <span class="n-tooltip n-leftdown th-tooltip" id="batchDistributionbtn_warn" style="position: absolute;">请选择要铺货的商品</span>
    </span>
</div>



<script id="DistributionbaseListY_temp" type="text/x-jsrender">
    {{for data}}
    <tr data-index="{{:#getIndex()}}" class="chx-item{{:#getIndex()}} {{:IsZeroForSalePrice?'ZeroForSalePriceClass stopTr':""}} {{:isCheck?'ActiveTr':''}} {{:ShopCount>0?"hasWarnTd":""}}">
        <td>
            <div class="productWrap flex">
                <span onclick="DistributionbaseListSingleCheck('{{:#getIndex()}}')" class="n-newCheckbox {{:isCheck?"activeF":""}}" name="productCheckbox"></span>
                <div class="productShow">
                    <img src="{{:~transformImgSrc(MainImageUrl)}}" class="productShow-img">
                    <ul class="product-title">
                        <li class="f14 product-title-li">
                            <span class="c09">{{:Subject}}<i class="iconfont hover icon-a-edit1x mL4" onclick="DistributionbaseListColumnEdit('Subject','{{:UniqueCode}}')"></i></span>
                            <span><i class="n-dColor">{{:SubjectStringLeng}}</i>/{{:SubjectLengData[1]}}</span>
                        </li>
                        <li class="c06" style="margin-top: 4px;">商品类目：{{:forMatCategoryName}}</li>
                    </ul>
                </div>
            </div>
        </td>
        <td>
            <ul class="product-price">
                {{if IsZeroForSalePrice}}
                <li class="product-price-li n-sColor ZeroForSaleWrap"><i class="iconfont icon-gantan"></i>{{:SalePrice}}<i class="iconfont c09 hover icon-a-edit1x mL4" onclick="DistributionbaseListColumnEdit('Price','{{:UniqueCode}}')"></i></li>
                {{else}}
                <li class="product-price-li">{{:SalePrice}}<i class="iconfont hover icon-a-edit1x mL4" onclick="DistributionbaseListColumnEdit('Price','{{:UniqueCode}}')"></i></li>
                {{/if}}
                <li class="product-price-li">采购价：{{:SettlePrice}}</li>
                <li class="product-price-li distributePrice-li">分销价：{{:DistributePrice}}</li>
            </ul>
        </td>
        <td style="vertical-align: baseline;display:none;" class="isshow_table_pdd">
            <ul class="product-price">
                {{if IsZeroForSinglePrice}}
                <li class="product-price-li n-sColor ZeroForSaleWrap"><i class="iconfont icon-gantan"></i>{{:SinglePrice}}<i class="iconfont c09 hover icon-a-edit1x mL4" onclick="DistributionbaseListColumnEdit('SinglePrice','{{:UniqueCode}}')"></i></li>
                {{else}}
                <li class="product-price-li">{{:SinglePrice}}<i class="iconfont hover icon-a-edit1x mL4" onclick="DistributionbaseListColumnEdit('SinglePrice','{{:UniqueCode}}')"></i></li>
                {{/if}}
                {{if IsZeroForReferencePrice}}
                <li class="product-price-li n-sColor ZeroForSaleWrap" style="display:flex;align-items:center;">
                    <i class="iconfont icon-gantan"></i>
                    <div class="priceInputWrap n-inputWrap warnInput">
                        <input class="layui-input n-layui-input" style="padding-left: 18px;" type="text" name="ReferencePrice" value="{{:~replaceHtml(ReferencePrice)}}" onfocus="PrepareDistributionModule.focusReSkuForm.bind(this)('ReferencePrice')" onchange="DistributionbaseListColumnEdit.bind(this)('ReferencePrice','{{:UniqueCode}}')" placeholder="0.00" />
                        <i class="priceIcon">￥</i>
                    </div>
                </li>
                {{else}}
                <li>
                    <div class="priceInputWrap n-inputWrap">
                        <input class="layui-input n-layui-input" style="padding-left: 18px;" type="text" name="ReferencePrice" value="{{:~replaceHtml(ReferencePrice)}}" onfocus="PrepareDistributionModule.focusReSkuForm.bind(this)('ReferencePrice')" onchange="DistributionbaseListColumnEdit.bind(this)('ReferencePrice','{{:UniqueCode}}')" placeholder="0.00" />
                        <i class="priceIcon">￥</i>
                    </div>
                </li>
                {{/if}}
            </ul>
        </td>
        
        <td style="vertical-align: baseline;">
            <span class="c09 f14">{{:StockCount}}<i class="iconfont hover icon-a-edit1x mL4" onclick="DistributionbaseListColumnEdit('Stock','{{:UniqueCode}}')"></i></span>
        </td>
        <td class="leftOperate">
            <span class="n-dColor f14 hover" onclick="EdmitDistributionItem('{{:UniqueCode}}','{{:Subject}}')">编辑</span>
        </td>
    </tr>
    {{if ShopCount>0}}
    <tr data-index="{{:#getIndex()}}" class="chx-warn chx-item{{:#getIndex()}} {{:IsZeroForSalePrice?'ZeroForSalePriceClass stopTr':""}} {{:isCheck?'ActiveTr':''}}">
        <td colspan="20" class="n-fColor">
            <div class="flex n-tColor f12" style="padding-left:88px;">
                <span class="iconfont icon-a-error-circle-filled1x mR4"></span>
                <span>有<i class="n-dColor popoverCommon">{{:ShopCount}}个<span class="popoverCommon-warn" style="width:240px;">{{:ShopNames?ShopNames:"暂无数据！"}}</span></i>店铺无类目发布权限或未缴纳保证金<a class="n-dColor" href="https://fxg.jinritemai.com/ffa/gov/qualification/industry-edit" target="_blank">前往处理</a>，处理后请<i class="n-dColor hover" onclick="refGetPtInfoDrafts()">刷新</i>以获取最新店铺信息</span>
            </div>

        </td>
    </tr>
    {{/if}}
    {{/for}}
    {{if data.length==0}}
    <tr>
        <td colspan="20" class="tdNodata">
            <div class="tableNoDataShow">
                <img src="/Content/images/noData-icon.png"><span class="tableNoDataShow-title">暂无数据！</span>
            </div>
        </td>
    </tr>
    {{/if}}
</script>

<script id="DistributionbaseListN_temp" type="text/x-jsrender">
    {{for data}}
    <tr>
        <td>
            <div class="productWrap flex">
                <div class="productShow">
                    <img src="{{:~transformImgSrc(MainImageUrl)}}" class="productShow-img">
                    <ul class="product-title">
                        <li class="f14 product-title-li">
                            <span class="c09">{{:Subject}}</span>
                        </li>
                        <li class="c06 settlePricee-li" style="margin-top: 4px;">采购价：{{:SettlePrice}}</li>
                        <li class="c06 distributePrice-li" style="margin-top: 4px;">分销价：{{:DistributePrice}}</li>
                    </ul>
                </div>
            </div>
        </td>

        <td class="leftOperate">
            <span class="n-dColor f14 hover" onclick="EdmitDistributionItem('{{:UniqueCode}}','{{:Subject}}')">编辑</span>
        </td>
    </tr>
    {{/for}}
    {{if data.length==0}}
    <tr>
        <td colspan="20" class="tdNodata">
            <div class="tableNoDataShow">
                <img src="/Content/images/noData-icon.png"><span class="tableNoDataShow-title">暂无数据！</span>
            </div>
        </td>
    </tr>
    {{/if}}
</script>

<script type="text/javascript">

        commonModule.CloudPlatformType = '@(Html.Raw(ViewBag.CloudPlatformType ?? ""))';
        $.views.helpers({
            transformImgSrc: function (src) {
                var imgSrc = "/Content/images/nopic.gif";
                if (src) {
                    if (src.indexOf("http") == 0) {
                        imgSrc = src;
                    } else {
                        if (src != "//.") {
                            commonModule.CloudPlatformType = "Alibaba";
                            var token = $("#token_input").val() ? $("#token_input").val() : '';
                            imgSrc = src + '&platform=' + commonModule.CloudPlatformType + '&token=' + token;
                        } else {
                            imgSrc = "/Content/images/nopic.gif"
                        }
                    }
                }
                return imgSrc;
            },
            replaceHtml: function (str) {
                return str.replace('￥', "");
            }
        });

</script>