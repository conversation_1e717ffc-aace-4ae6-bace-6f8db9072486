
<div class="n-alert n-alert-03" style=" margin: 16px 16px 0 16px;position:relative;">
    <i class="iconfont icon-gantan"></i>目前仅支持抖音和拼多多店铺，该功能需要权限，添加店铺时，请先订购应用并授权，即可开始铺货。
</div>
<ul class="choosePlatform" id="choosePlatformAddShop"></ul>
<div class="chooseShopBtns">
    <span class="n-mButton" onclick="gotToAddShopFun()">添加店铺</span>
</div>
<div class="chooseShopTableWrap">
    <table class="chooseShopTable n-table edmitTable activeEdmitTable">
        <thead>
            <tr>
                <th>
                    <div class="th-header">
                        <span class="n-newCheckbox" id="allShopsCheck" onclick="changeAllShopsCheck.bind(this)()"></span>
                        <span>店铺名称</span>
                    </div>
                </th>
                <th>服务到期时间</th>
                @*<th>最新授权时间</th>*@
                <th>授权状态</th>
                <th class="leftOperate">操作</th>
            </tr>
        </thead>
        <tbody id="tbody_shop_data"></tbody>
    </table>
</div>
<div class="createBase-footer" style="justify-content:flex-start;">
    <span class="n-mButton stop" style="margin-right:10px;margin-left:0;" id="nextDistributionBtn" onclick="nextDistribution.bind(this)()">下一步</span>
    <span class="n-mButton n-sActive" style="margin-right:10px;"
          onclick="DistributionCommonModule.goBack()">取消</span>
</div>

<!-- 列表模样 -->
<script id="shops_data_tr" type="text/x-jsrender">
    {{for shops}}
    <tr class="{{:IsCheck?'active':''}}" onclick="singleShopsCheck.bind(this)('{{:Id}}')">
        <td>
            <div class="td-body">
                {{if IsExpire && PlatformPayUrl != ""}}
                <span class="n-newCheckbox stop"></span>
                {{else}}
                {{if Status == 1}}
                <span class="n-newCheckbox {{:IsCheck?'activeF':''}}"></span>
                {{else}}
                <span class="n-newCheckbox stop"></span>
                {{/if}}
                {{/if}}
                <span class="n-smallIcon {{:PlatformType}}"></span>
                <span class="c09 f14">{{:NickName}}</span>
            </div>
        </td>
        <td>
            {{if !IsBlank || Status !=0}}
            {{:AuthTime}}
            {{/if}}
        </td>
        @*<td>
                {{if !IsBlank || Status !=0 }}
                {{:CreateTime}}
                {{/if}}
            </td>*@
        <td>




            {{if IsExpire && PlatformPayUrl != ""}}
            {{if IsBlank}}
            <span class="n-tarTxt n-tarTxt02">暂未订购</span>
            {{else}}
            <span class="n-tarTxt n-tarTxt02">服务到期</span>
            {{/if}}
            {{else}}
            {{if Status == 1}}
            <span class="n-tarTxt n-tarTxt04">授权正常</span>
            {{else Status == 0 || IsBlank}}
            <span class="n-tarTxt n-tarTxt02">暂未订购</span>
            {{else Status == 3}}
            <span class="n-tarTxt n-tarTxt02">授权到期</span>
            {{/if}}
            {{/if}}

        </td>
        <td class="leftOperate" >


            {{if IsBlank}}
            <span class="n-dColor hover" onclick="latformRenewUrlFun('{{:PlatformPayUrl2}}','{{:PlatformType}}','{{:NickName}}')">订购</span>


            {{else}}


            {{if Status == 1}}
            <span class="n-dColor hover" onclick="latformRenewUrlFun('{{:PlatformPayUrl2}}','{{:PlatformType}}','{{:NickName}}')">续订</span>
            {{else Status == 3}}

            {{if IsExpire==false && PlatformPayUrl == ""}}
            <span class="n-dColor hover" onclick="latformRenewUrlFun('{{:AuthUrl}}')">重新授权</span>
            {{/if}}


            <span class="n-dColor hover" onclick="latformRenewUrlFun('{{:PlatformPayUrl2}}','{{:PlatformType}}','{{:NickName}}')">续订</span>


            {{else Status == 5}}
            <span class="n-dColor hover" onclick="latformRenewUrlFun('{{:PlatformPayUrl2}}','{{:PlatformType}}','{{:NickName}}')">订购</span>
            {{else Status==0}}
            <span class="n-dColor hover" onclick="latformRenewUrlFun('{{:PlatformPayUrl2}}','{{:PlatformType}}','{{:NickName}}')">续订</span>
            {{/if}}



            {{/if}}


        </td>
    </tr>
    {{/for}}

    {{if shops.length==0}}
    <tr>
        <td colspan="20">

            <div class="n-tableNoDataShow">
                <span class="iconfont icon-wushuju1"></span>
                <span class="tableNoDataShow-title">暂无授权店铺！</span>
            </div>
        </td>
    </tr>
    {{/if}}

</script>


