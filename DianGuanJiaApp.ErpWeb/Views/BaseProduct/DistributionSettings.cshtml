<style>
    #distributionSettingsWarp {
        font-size:14px;
    }
    #distributionSettingsWarp .setpriceWrap-item-left-up .setpriceWrap-item-left-up-title {
        font-size: 14px;
        line-height: 20px;
        color: rgba(0, 0, 0, 0.9);
    }
    
    #distributionSettingsWarp .setpriceWrap .setpriceWrap-item {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        padding: 14px 16px;
        background: rgba(0, 0, 0, 0.04);
        border-radius: 4px;
        margin-bottom: 8px;
    }
    #distributionSettingsWarp .setpriceWrap-item.n-center {
        padding-left: 0;
        background-color: #fff;
        justify-content: flex-start;
        padding-bottom: 0;
        margin-bottom: 0;
    }
    #distributionSettingsWarp .setpriceWrap-item-left-up-title {
        color: rgba(0, 0, 0, 0.9);
        font-size: 14px;
    }
    #distributionSettingsWarp .sumIcon {
        font-size: 14px;
        padding: 0 4px;
    }
    #distributionSettingsWarp .layui-content-ul {
        padding-top: 16px;
    }
    #distributionSettingsWarp .layui-content-ul > li span.layui-content-ul-title {
        margin-bottom: 0;
    }
    #distributionSettingsWarp .layui-content-ul > li:last-child {
        margin-bottom: 0;
    }
    #distributionSettingsWarp .setTips {
        width: 100%;
        height: 32px;
        border-radius: 4px;
        padding: 8px;
        color: rgba(0, 0, 0, 0.6);
        font-size: 12px;
        font-weight: normal;
        line-height: 16px;
        /* 品牌色/Brand1 */
        background: rgba(8, 136, 255, 0.1);
        box-sizing: border-box;
        /* 品牌色/Brand2 */
        border: 1px dashed rgba(8, 136, 255, 0.2);
    }
</style>

<div id="distributionSettingsWarp">
    
    
</div>
<script id="distributionSetPrice" type="text/x-jsrender">
    <div id="dialogSetPrice" style="display:none">
        <div class="setTips">
            例：当前售价为10.00元，则新售价为：10 × 1 + 0 = 10.00元
        </div>
        <div style="justify-content:space-between;display:flex;">
            <ul class="layui-content-ul modular">
                <li>
                    <div>
                        <span class="layui-content-ul-title">改价规则</span>
                    </div>
                    <ul class="setpriceWrap">
                        <li class="setpriceWrap-item n-center">
                            <span class="n-newRadio mR8 PriceUpdateType {{:queryQaram.PriceUpdateType == 1 ? 'active':''}}" onclick="DistributionSettingsModule.changeBatchListingRaio.bind(this)('PriceUpdateType',1)"></span>
                            <div class="setpriceWrap-item-left-up-title" style="display:flex;align-items:center;">
                                公式改价：新售价<s class="sumIcon">=</s>
                                <div class="n-mySelect n-mySelect-property n-single catePropsItem hasActive n-myCommonSelect n-myCommonSelect-PriceType" style="width:100px;">
                                    <div class="n-mySelect-title">
                                        <div class="n-mySelect-title-left">
                                            @*<span class="n-mySelect-title-placeholder">请选择</span>*@
                                            <span class="n-mySelect-title-chooseItem">当前售价</span>
                                        </div>
                                        <span class="iconfont icon-a-chevron-down1x"></span>
                                    </div>
                                    <div class="n-mySelect-showContent">
                                        <ul class="n-mySelect-showContent-ul">
                                            <li class="n-mySelect-showContent-ul-li {{:queryQaram.PriceType == 0 ? 'activeItem':''}}" data-idx="0" data-value="0" data-name="当前售价" onclick="DistributionSettingsModule.changeBatchSelect.bind(this)('PriceType', '0')">当前售价</li>
                                            <li class="n-mySelect-showContent-ul-li {{:queryQaram.PriceType == 1 ? 'activeItem':''}}" data-idx="1" data-value="1" data-name="采购价" onclick="DistributionSettingsModule.changeBatchSelect.bind(this)('PriceType', '1')">采购价</li>
                                            {{if isFxUser}}
                                            <li class="n-mySelect-showContent-ul-li {{:queryQaram.PriceType == 2 ? 'activeItem':''}}" data-idx="2" data-value="2" data-name="分销价" onclick="DistributionSettingsModule.changeBatchSelect.bind(this)('PriceType', '2')">分销价</li>
                                            {{/if}}
                                        </ul>
                                    </div>
                                </div>
                                <s class="sumIcon">×</s>
                                <div class="n-inputWrap">
                                    <input type="text" class="layui-input n-layui-input n-init inputPricePercentage" style="width:80px;" value="{{:queryQaram.PricePercentage}}" onchange="DistributionSettingsModule.changeBatchInput.bind(this)('PricePercentage')">
                                    <span class="input-num">%</span>
                                </div>
                                <s class="sumIcon">+</s>
                                <div class="n-inputWrap">
                                    <input type="text" class="layui-input n-layui-input n-input-price inputPriceIncrement" style="width:100px;" placeholder="请输入"  value="{{:queryQaram.PriceIncrement}}" onchange="DistributionSettingsModule.changeBatchInput.bind(this)('PriceIncrement')">
                                    <span class="input-num">元</span>
                                </div>
                            </div>
                        </li>
                        <li class="setpriceWrap-item n-center">
                            <span class="n-newRadio mR8 MinuteOfArc PriceUpdateType {{:queryQaram.PriceUpdateType == 0 ? 'active':''}}" onclick="DistributionSettingsModule.changeBatchListingRaio.bind(this)('PriceUpdateType',0)"></span>
                            <span class="f14 c09 ">统一改价：新售价</span><s class="sumIcon">=</s>
                            <div class="n-inputWrap">
                                <input type="text" class="layui-input n-layui-input n-input-price inputPriceCommon" style="width:100px;" placeholder="请输入" value="{{:queryQaram.PriceCommon}}" onchange="DistributionSettingsModule.changeBatchInput.bind(this)('PriceCommon')">
                                <span class="input-num">元</span>
                            </div>
                        </li>
                    </ul>

                </li>
                <li>
                    <div>
                        <span class="layui-content-ul-title">角分处理</span>
                    </div>
                    <ul class="setpriceWrap">
                        <li class="setpriceWrap-item n-center">
                            <span class="n-newRadio mR8 PriceUnitType {{:queryQaram.PriceUnitType == 0 ? 'active':''}}" onclick="DistributionSettingsModule.changeBatchListingRaio.bind(this)('PriceUnitType',0)"></span>
                            <span class="f14 c09">保留角和分</span>
                        </li>
                        <li class="setpriceWrap-item n-center">
                            <span class="n-newRadio mR8 PriceUnitType {{:queryQaram.PriceUnitType == 1 ? 'active':''}}" onclick="DistributionSettingsModule.changeBatchListingRaio.bind(this)('PriceUnitType',1)"></span>
                            <span class="f14 c09 mR4">统一改为</span>
                            <div class="n-inputWrap">
                                <input type="text" class="layui-input n-layui-input inputPriceCornerFen" style="width:100px;" placeholder="请输入" value="{{:queryQaram.PriceCornerFen}}" onchange="DistributionSettingsModule.changeBatchInput.bind(this)('PriceCornerFen')">
                            </div>
                            <i class="iconfont icon-a-help-circle1x popoverCommon"><span class="popoverCommon-warn" style="width:240px;">例如：商品计算后的价格为64.53元，选择此项并值输入99，则价格会调整为64.99元</span></i>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</script>

<script id="distributionSetStock" type="text/x-jsrender">
    <div id="dialogSetStock" style="display:none">
        <div style="justify-content:space-between;display:flex;">
            <ul class="layui-content-ul modular" style="padding-top:0px;">
                <li>
                    <div>
                        <span class="layui-content-ul-title">库存规则</span>
                    </div>
                    <ul class="setpriceWrap">
                        <li class="setpriceWrap-item n-center">
                            <span class="n-newRadio mR8 StockType {{:queryQaram.StockType == 0 ? 'active':''}}" onclick="DistributionSettingsModule.changeBatchListingRaio.bind(this)('StockType',0)"></span>
                            <span class="f14 c09">和源库存一致</span>
                        </li>
                        <li class="setpriceWrap-item n-center">
                            <span class="n-newRadio mR8 StockType {{:queryQaram.StockType == 1 ? 'active':''}}" onclick="DistributionSettingsModule.changeBatchListingRaio.bind(this)('StockType',1)"></span>
                            <span class="f14 c09 mR4">在现有库存基础上</span>
                            <div class="n-mySelect n-mySelect-property n-single catePropsItem hasActive n-myCommonSelect n-myCommonSelect-StockChange mR4" style="width:100px;">
                                <div class="n-mySelect-title">
                                    <div class="n-mySelect-title-left">
                                        @*<span class="n-mySelect-title-placeholder">请选择</span>*@
                                        {{if queryQaram.StockChange == 0}}
                                        <span class="n-mySelect-title-chooseItem">增加 +</span>
                                        {{else}}
                                        <span class="n-mySelect-title-chooseItem">减少 -</span>
                                        {{/if}}
                                    </div>
                                    <span class="iconfont icon-a-chevron-down1x"></span>
                                </div>
                                <div class="n-mySelect-showContent" style="animation:none;position:fixed;top:unset;width:100px;">
                                    <ul class="n-mySelect-showContent-ul">
                                        <li class="n-mySelect-showContent-ul-li {{:queryQaram.StockChange == 0 ? 'activeItem':''}}" data-value="0" onclick="DistributionSettingsModule.changeBatchSelect.bind(this)('StockChange',0)">增加 +</li>
                                        <li class="n-mySelect-showContent-ul-li {{:queryQaram.StockChange == 1 ? 'activeItem':''}}" data-value="1" onclick="DistributionSettingsModule.changeBatchSelect.bind(this)('StockChange', 1)">减少 -</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="n-inputWrap">
                                <input type="text" class="layui-input n-layui-input n-input-price inputStockChangeCount" style="width:100px;" placeholder="请输入" value="{{:queryQaram.StockChangeCount}}" onchange="DistributionSettingsModule.changeBatchInput.bind(this)('StockChangeCount')">
                                <span class="input-num">件</span>
                            </div>
                        </li>
                        <li class="setpriceWrap-item n-center">
                            <span class="n-newRadio mR8 StockType {{:queryQaram.StockType == 2 ? 'active':''}}" onclick="DistributionSettingsModule.changeBatchListingRaio.bind(this)('StockType',2)"></span>
                            <span class="f14 c09 mR4">统一改为</span>
                            <div class="n-inputWrap">
                                <input type="text" class="layui-input n-layui-input n-init inputStockCommonCount" style="width:100px;" placeholder="请输入" value="{{:queryQaram.StockCommonCount}}" onchange="DistributionSettingsModule.changeBatchInput.bind(this)('StockCommonCount')">
                            </div>
                            
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</script>
<script>
    var sourceType = 'Price';  //Stock库存，Price(单个价格),PriceBatch(多选价格)
    var distributionSetBatch = false; // 是否多选
    var isDistributionSetBatch = false;  // 是否列表编辑(多商品铺货列表)
    var sourceSetDom = null;  // 来源dom
    var queryQaram = {
        "UniqueCode": "",
        "PriceType": 0,  //价格类型：0 售价、1 采购价、2 分销价 ,
        PriceTypeData: [
            { name: '当前售价', value: 0, },
            { name: '采购价', value: 1, },
            { name: '分销价', value: 2, },
        ],
        "CalculatePrices": [
            {
               "Price": 10,
              "SkuUid": "",
              "PriceType": 0
            }
        ],
        "PriceUpdateType": 1,//价格修改方式：0 统一修改、1 公式修改 ,
        "PricePercentage": 100, //价格百分比（公式修改）
        "PriceIncrement": '', //价格增量（公式修改） ,
        "PriceCommon": '',   //价格统一值（统一修改） ,
        "PriceUnitType": 0,  //价格单位：0 保留角分 1 统一修改 ,
        "PriceCornerFen": '',  //价格角分自定义(两位数)
    }
    var queryQaramStock = {
        "UniqueCode": "",
        "StockType": 0, //库存修改类型：0 源库存一致、1 原库存变化、2 统一修改 = ['0', '1', '2'],
        "StockChange": 0, //库存变化：0 增加、1 减少 ,
        StockCountList:[
            {
              "StockCount": 10,
              "SkuUid": "",
              OriginalStockCount: 0, //源库存
            }
        ],  //
        "StockChangeCount": '',  //库存变化量 ,
        "StockCommonCount": '', //库存变化量(统一修改)
    }
   
    
    var DistributionSettingsModule = (function (module, $, layer) {
        module.changeBatchListingRaio = function (field, val) {
            //$('#dialogSetPrice input').closest(".n-inputWrap").removeClass("warnInput");
            //$('#dialogSetStock input').closest(".n-inputWrap").removeClass("warnInput");
            $("." + field).removeClass("active");
            $(this).addClass("active");
            if (sourceType === 'Stock') {
                queryQaramStock[field] = val;
                return
            }
            queryQaram[field] = val;
        }
        function initCommonSelectEvent() {
            $(".n-myCommonSelect .n-mySelect-title").off();
            $(".n-myCommonSelect .n-mySelect-title").on("click", function () {
                event.stopPropagation();
                $(".n-myCommonSelect").removeClass("active");
                $(this).closest(".n-myCommonSelect").addClass("active");
                $(this).closest(".n-myCommonSelect").find(".n-mySelect-showContent-ul-li").css({ display: 'flex' });
            })
        }
        $(function () {
            // 点击document
            $(document).on("click", function () {
                $(".n-myCommonSelect").removeClass("active");
            })
            
        })
       
        
        module.changeBatchInput = function (field) {
            var val = $(this).val().trim();
            if (val != "") {
                var re = "";
                if ($(this).hasClass('n-input-price')) { //匹配价格
                    re = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/; //金额正则
                } else if ($(this).hasClass('n-init')) {
                    re = /^[1-9]\d*$/; //整数正则
                } else {
                    re = /^[0-9]+(\.[0-9]+)?$/;
                }
                if (re.test(val) === false) {
                    $(this).closest(".n-inputWrap").addClass("warnInput");
                    $(this).val("");
                    return;
                }
                if (field === 'PriceCornerFen' && val.length > 2) {
                    $(this).closest(".n-inputWrap").addClass("warnInput");
                    $(this).val("");
                    return
                }
                if (sourceType === 'Stock') {
                    queryQaramStock[field] = parseFloat(val);
                    return
                }
                
                $(this).closest(".n-inputWrap").removeClass("warnInput");
            } else {
                var checkFlag = false;
                if (field === 'PriceIncrement' && queryQaram.PriceUpdateType == 1) {
                    checkFlag = true;
                }
                if (field === 'PriceCommon' && queryQaram.PriceUpdateType == 0) {
                    checkFlag = true;
                }
                if (field === 'PriceCornerFen' && queryQaram.PriceUnitType == 1) {
                    checkFlag = true;
                }
                if (field === 'StockChangeCount' && queryQaramStock.StockType == 1 ) {
                    checkFlag = true;
                }
                if (field === 'StockCommonCount' && queryQaramStock.StockType == 2) {
                    checkFlag = true;
                }
                if (checkFlag) {
                     $(this).closest(".n-inputWrap").addClass("warnInput");
                     $(this).val("");
                     commonModule.w_alert({
                         type: 2,
                         content: '请输入!'
                     });
                }
                if (sourceType === 'Stock') {
                    queryQaramStock[field] = val;
                    return
                }
            }
            queryQaram[field] = val;
        }
        module.changeBatchSelect = function (field, val) {
            $('.n-myCommonSelect-' + field + ' .n-mySelect-title-chooseItem').html($(this).text())
            $('.n-myCommonSelect-' + field + ' .n-mySelect-showContent-ul-li').removeClass('activeItem');
            $(this).addClass('activeItem')
            if (sourceType === 'Stock') {
                queryQaramStock[field] = val;

                return
            }
            queryQaram[field] = val;
        }
        module.onDialogSetPrice = function () {
            var tplt = $.templates("#distributionSetPrice");
            var html = tplt.render({
                queryQaram: queryQaram,
                isFxUser: isFxUser,
            });
            $('#distributionSettingsWarp').html(html);
            var title = '批量编辑售价';
            var loadIndex = layer.open({
                type: 1,
                title: title, //不显示标题
                content: $('#dialogSetPrice'),
                skin: 'n-skin',
                shade: 0,
                shadeClose: true, // 点击遮罩关闭层
                area: ['560px', 'auto'], //宽高
                btn: ['取消', '确定'],
                btn2: function (index) {
                    $('#dialogSetPrice input').closest(".n-inputWrap").removeClass("warnInput");
                    var checkFlagQueryQaram = false;
                    // 公式改价
                    if (queryQaram.PriceUpdateType == 1) {
                        if (queryQaram.PriceIncrement === '') {
                            checkFlagQueryQaram = true;
                            $('input.inputPriceIncrement').closest(".n-inputWrap").addClass("warnInput");
                        }
                    } else {
                        if (queryQaram.PriceCommon === '') {
                            checkFlagQueryQaram = true;
                            $('input.inputPriceCommon').closest(".n-inputWrap").addClass("warnInput");
                        }
                    }
                    if (queryQaram.PriceUnitType == 1) {
                        if (queryQaram.PriceCornerFen === '') {
                            checkFlagQueryQaram = true;
                            $('input.inputPriceCornerFen').closest(".n-inputWrap").addClass("warnInput");
                        }
                    }
                    if (checkFlagQueryQaram) {
                         commonModule.w_alert({
                             type: 2,
                             content: '请正确填写售价设置!'
                         });
                         return false
                    }
                    if ((distributionSetBatch && sourceType == 'PriceBatch') || isDistributionSetBatch) {
                        onSaveSubmitSpu(loadIndex);
                    } else {
                        onSaveSubmitSku(loadIndex);
                    }  
                },
                success: function () {
                    initCommonSelectEvent()
                },
                cancel: function (index) {
                    layer.close(loadIndex);
                    onClear();
                },
                end: function () {
                    onClear();
                },
            });
        }
        function onSaveSubmitSpu (loadIndex) {
            delete queryQaram.CalculatePrices;
            delete queryQaram.PriceTypeData;
            commonModule.Ajax({
		        url: '/api/PtProductInfoDraft/UpdatePtInfoDraftPrice',
		        type:'POST',
		        showMasker: false,
                data: queryQaram,
		        success: function (rsp) {
			        if (rsp.Success) {
				        layer.close(loadIndex);
                        
                        DistributionbaseSetModule.GetPtInfoDrafts(PtProductUniqueCodeArray);
                        onClear();
                        //sourceSetDom = null;
                        
			        } else {
				        layer.msg(rsp.Message || rsp.Data || '失败');
			        }
		        }
	        })
        }
        function onSaveSubmitSku (loadIndex) {
            delete queryQaram.PriceTypeData
            queryQaram.CalculatePrices.forEach(function (item, i) {
                if (queryQaram.PriceType == 1) {
                    item.Price = item.SettlePrice;
                }
                if (queryQaram.PriceType == 2) {
                    item.Price = item.DistributePrice;
                }
            })
            commonModule.Ajax({
				    url: '/api/PtProductInfoDraft/CalculateDraftPrice',
				    type:'POST',
				    showMasker: false,
                    data: queryQaram,
				    success: function (rsp) {
					    if (rsp.Success) {
						    layer.close(loadIndex);
                            var values = rsp.Data || [];
                             // 单个价格
                            if (sourceSetDom && !distributionSetBatch) {
                                sourceSetDom.val(values[0].Price).trigger('change');
                            } else {
                                queryQaram.CalculatePrices = values;
                                PrepareDistributionModule.sureBatchSkusDailog('SalePrice',values)
                            }
                            onClear();
					    } else {
						    layer.msg(rsp.Message || rsp.Data || '失败');
					    }
				    }
			    })
        }
       
        module.onDialogSetStock = function() {
            var tplt = $.templates("#distributionSetStock");
            var html = tplt.render({
                queryQaram: queryQaramStock,
            });
            $('#distributionSettingsWarp').html(html);
            var title = '批量设置库存';
            var loadIndex = layer.open({
                type: 1,
                title: title, //不显示标题
                content: $('#dialogSetStock'),
                skin: 'n-skin',
                shadeClose: true, // 点击遮罩关闭层
                area: ['560px', 'auto'], //宽高
                shade:0,
                btn: ['取消', '确定'],
                btn2: function (index) {
                    $('#dialogSetStock input').closest(".n-inputWrap").removeClass("warnInput");
                    if (queryQaramStock.StockType == 1 && queryQaramStock.StockChangeCount === '') {
                        $('input.inputStockChangeCount').closest(".n-inputWrap").addClass("warnInput");
                        commonModule.w_alert({
                            type: 2,
                            content: '请正确填写库存设置!'
                        });
                        return false
                    }
                    if (queryQaramStock.StockType == 2 && queryQaramStock.StockCommonCount === '') {
                        $('input.inputStockCommonCount').closest(".n-inputWrap").addClass("warnInput");
                         commonModule.w_alert({
                             type: 2,
                             content: '请正确填写库存设置!'
                         });
                        return false
                    }
                    
                    if (!isDistributionSetBatch) {
                        onSaveSubmitStockSku(loadIndex);
                        layer.close(loadIndex)
                    } else {
                        onSaveSubmitStockSpu(loadIndex);
                    }  
                    //layer.close(loadIndex)
                },
                success: function () {
                    initCommonSelectEvent()
                },
                cancel: function (index) {
                    layer.close(loadIndex);
                },
                end: function () {
                    onClear();
                },
            });
        }
        function onSaveSubmitStockSpu(loadIndex) {
            delete queryQaramStock.StockCountList;
             commonModule.Ajax({
                url: '/api/PtProductInfoDraft/UpdatePtInfoDraftStock',
		        type:'POST',
		        showMasker: false,
                data: queryQaramStock,
		        success: function (rsp) {
			        if (rsp.Success) {
				        layer.close(loadIndex);
                        DistributionbaseSetModule.GetPtInfoDrafts(PtProductUniqueCodeArray);
                        onClear();
			        } else {
				        layer.msg(rsp.Message || rsp.Data || '失败');
			        }
		        }
	        })
        }
        function onSaveSubmitStockSku (loadIndex) {
            var values = [];
            for (var i = 0; i < queryQaramStock.StockCountList.length; i++) {
                var item = queryQaramStock.StockCountList[i];
                item.StockCount = parseFloat(item.StockCount) 
        
                switch(queryQaramStock.StockType){
                    case 0:
                        val = item.OriginalStockCount;
                        break;
                    case 1:
                        if (queryQaramStock.StockChange == 0) {
                            val = item.StockCount + queryQaramStock.StockChangeCount;
                        } else {
                            val = item.StockCount - queryQaramStock.StockChangeCount;
                            val = val > 0 ? val : 0
                        }
                        break;
                    case 2:
                        val = queryQaramStock.StockCommonCount;
                        break;
                    default:
	                    break;
                }
                item.value = val
                values.push(item);
                
            }
            // 单个价格
            if (sourceSetDom && !distributionSetBatch) {
                sourceSetDom.val(values[0].value).trigger('change');
                sourceSetDom = null;
            } else {
                queryQaramStock.StockCountList = values;
                PrepareDistributionModule.sureBatchSkusDailog('StockCount',values)
            }
            onClear();
        }
        function onClear() {
            distributionSetBatch = false;
            isDistributionSetBatch = false;
            queryQaram = {
                "UniqueCode": "",
                "PriceType": 0,  //价格类型：0 售价、1 采购价、2 分销价 ,
                PriceTypeData: [
                    { name: '当前售价', value: 0, },
                    { name: '采购价', value: 1, },
                    { name: '分销价', value: 2, },
                ],
                "CalculatePrices": [
                    {
                        "Price": 10,
                        "SkuUid": "",
                        "PriceType": 0
                    }
                ],
                "PriceUpdateType": 1,//价格修改方式：0 统一修改、1 公式修改 ,
                "PricePercentage": 100, //价格百分比（公式修改）
                "PriceIncrement": '', //价格增量（公式修改） ,
                "PriceCommon": '',   //价格统一值（统一修改） ,
                "PriceUnitType": 0,  //价格单位：0 保留角分 1 统一修改 ,
                "PriceCornerFen": '',  //价格角分自定义(两位数)
            };
            queryQaramStock = {
                "UniqueCode": "",
                "StockType": 0, //库存修改类型：0 源库存一致、1 原库存变化、2 统一修改 = ['0', '1', '2'],
                "StockChange": 0, //库存变化：0 增加、1 减少 ,
                StockCountList: [
                    {
                        "StockCount": 10,
                        "SkuUid": "",
                        OriginalStockCount: 0, //源库存
                    }
                ],  //
                "StockChangeCount": '',  //库存变化量 ,
                "StockCommonCount": '', //库存变化量(统一修改)
            }

        }
        //onDialogSetPrice()
        return module;
    })(DistributionSettingsModule || {}, jQuery, layer);
</script>