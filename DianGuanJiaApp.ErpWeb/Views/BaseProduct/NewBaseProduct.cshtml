@{
    ViewBag.Title = "基础商品";
    ViewBag.MenuId = "ProductBasics";
    ViewBag.MenuActive = "ProductManagement";
}
@section Header {
    <link rel="stylesheet" href="~/Content/css/product/NewproductBasics.css?v=@ViewBag.SystemVersion" />
    <style type="text/css">
        #reactApp {
            padding: 0 24px;
            margin-bottom: 70px;
            min-width: 1200px;
        }
        .layui-tab-brief > .layui-tab-title .layui-this{
            font-weight: bold;
        }
        .stockup_table_content .n-newCheckbox {
            width: 15px;
        }
        .layui-form-item .n-mButton.n-pActive {
            border: 1px solid rgba(8, 136, 255, 0.1);
        } 

        .layui-form-item .n-mButton {
            margin-right: 8px;
        }

        .layui-input-inline-label .layui-input {
            border: none;
            width: 100%;
            height: 30px;
            line-height: 30px;
            padding-left: 0;
        }

        .n-layui-form .layui-input,
        .layui-select {
            color: #666;
        }

        .flex {
            display: flex;
            align-items: center;
        }

        .layui-input-inline-label .label {
            padding: 0px 8px 0 4px;
            word-break: keep-all;
            color: rgba(0, 0, 0, 0.6);
        }

        @@media screen and (min-width: 992px) {
            .layui-col-lg3 {
                width: 25%;
            }
        }

        @@media screen and (min-width: 1200px) {
            .layui-col-lg3 {
                width: 20%;
            }
        }

        .layui-laypage {
            padding-right: 0;
        }

        .query-form {
            padding: 0 16px;
        }

        .layui-col-lg3 .n-focus {
            display: flex;
            align-items: center;
        }

        .n-layui-input.n-focus .layui-input {
            border: none;
            width: 100%;
            height: 30px;
            line-height: 30px;
            padding-left: 0;
        }

        .n-layui-input.n-focus .label {
            padding: 0px 8px 0 4px;
            word-break: keep-all;
            color: rgba(0, 0, 0, 0.6);
        }

        .n-layui-input.n-focu.active {
            border: 1px solid #0888FF !important;
            box-shadow: 0px 0px 0px 2px rgb(8 136 255 / 20%) !important;
        }

        .n-breadcrumb {
            justify-content: space-between;
        }
        .abnormal-search{
            padding: 16px;
        }
        .n-myPage{
            padding: 0 16px;
        }
        .textColor {
            font-size: 14px;
            line-height: 20px;
            color: rgba(0, 0, 0, 0.9);
        }
        .textTitle {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.9) !important;
            font-weight: 600;
        }
        #logoTable_paging {
            padding-bottom: 60px;
        }
        #iframe_main_Log {
            width: 100%;
            height: 100%;
            border: none;
        }
        #createFullMaskName .popoverCommon-warn {
            line-height: 1;
        }
        #createFullMaskName .popoverCommon-warn:after{
            display: block;
            content: "";
            position: absolute;
            bottom: -6px;
            top: auto;
            left: 22px;
            width: 0;
            height: 0;
            border-left: 7px solid transparent;
            border-right: 7px solid transparent;
            border-top: 7px solid #fff;
            border-bottom: none;
        }
        .hide2 {
            display: none !important;
        }
        .hideOverflow {
            overflow:hidden!important;
        }
        .n-skin .layui-layer-title {
            font-weight: bold;
        }
        .level-sell-rule-content {
            border-radius: 4px;
            box-sizing: border-box;
            padding: 12px;
            border: 1px solid rgba(0, 0, 0, 0.09);
        }
        .level-sell-rule-footer {
            border-top: 1px solid rgba(0, 0, 0, 0.14);
            box-sizing: border-box;
            padding: 12px 16px;
        }
        .level-sell-rule-box {
            height: 36px;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.04);
            padding: 8px;
            box-sizing: border-box;
            display: flex;
            justify-content: space-between;
            margin-left: 16px;
        }
        .mT16 {
            margin-top: 16px;
        }
        .mT8 {
            margin-top: 8px;
        }
        .mL8 {
            margin-left: 8px;
        }
        .mL12 {
            margin-left: 12px;
        }
        .childSkusBox {
            display: flex;
            max-height: 78px; 
            overflow: hidden;
        }
        .childSkusBox.isMore {
            max-height: 100%;
        }
        #pagingCombinedProductChangeLog {
            padding-bottom: 16px;
        }
        .tr-vertical-align-baseline {
            vertical-align: baseline;
        }
        .iconfont.icon-a-copy1x:hover {
            color:#0888FF;
        }
        #combinationPopover .iconfont.icon-a-copy1x {
            color: #fff;
            display: inline-block;
        }
        
        #combinationPopover .combinationPopover-body-ul-li:hover .iconfont.icon-a-copy1x{
            display: inline-block;
            color: rgba(0, 0, 0, 0.4);
        }
        #combinationPopover .combinationPopover-body-ul-li .iconfont.icon-a-copy1x:hover {
            color:#0888FF;
        }
        .n-inputWrap .input-num {
            right: 0px;
            top: auto;
            font-size: 14px;
            bottom: 0;
            padding-bottom: 4px;
            padding-right: 10px;
            z-index: 1;
        }
        .n-layui-input.n-layui-textarea {
            padding: 6px 8px;
            box-sizing: border-box;
        }
        .base-commodity-all .n-layui-input {
            border-radius: 6px;
        }
        .base-commodity-all .selectWrap.n-wuSelect .selectMore {
            border-radius: 6px;
        }
        .base-commodity-all .layui-form-item .n-mButton.n-pActive {
            border: 1px solid #0888ff;
        }
        .ellipsis2 {
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2; /* 限制为两行 */
            overflow: hidden;
            text-overflow: ellipsis;
            /* 可选：调整行高和容器高度以确保精确显示两行 */
            line-height: 1.2em;
            max-height: 2.4em;
        }
        #dialogDeleteSku2 .tips {
            margin: 0;
        }
    </style>
}

<div className="layui-mywrap">

    <div id="reactApp"></div>
    @{
        Html.RenderPartial("~/Views/Components/CommonDrawer/ComonDrawer.cshtml");
    }
    @{
        Html.RenderPartial("~/Views/Components/Popover.cshtml");
    }
    @{
        Html.RenderPartial("~/Views/Components/CombinationProductDialog.cshtml");
    }
    @{
        Html.RenderPartial("~/Views/Components/BaseProductComponets.cshtml");
    }
    @{
        Html.RenderPartial("~/Views/BaseProduct/ExceptionLog.cshtml");
    }
</div>

@section scripts {
    <script src="~/Scripts/ajaxfileupload.js"></script>
    <script type="text/javascript">
        commonModule.CloudPlatformType = '@(Html.Raw(ViewBag.CloudPlatformType ?? ""))';
        var Suppliers = @(Html.Raw(ViewBag.Suppliers ?? "[]"));
        var PageSize = @(Html.Raw(ViewBag.PageSize ?? ""));
        commonModule.WarnStockNum = "@ViewBag.WarnStockNum";
        var isRemark = false; // 是否是备注
        // var loadingIndex = layer.load(2, {time: 1*1000});
        //绑定厂家
        var initSupplierSelectBox = function (ele, isRadio) {

            var selectboxArr = [];
            for (var i = 0; i < Suppliers.length; i++) {
                var obj = {};
                obj.Value = Suppliers[i].FxUserId;
                obj.Text = Suppliers[i].UserName;
                if (Suppliers[i].Status != undefined && Suppliers[i].Status == 1) {
                    if (Suppliers[i].IsTop != undefined && Suppliers[i].IsTop) {
                        obj.Text = '<i className="iconfont icon-zhuangtai zhidingIcon"></i>' + Suppliers[i].UserName;
                    }
                    //只取IsFilter=false
                    selectboxArr.push(obj);
                }
            }
            var selectInit = {
                eles: '#' + ele,
                emptyTitle: '请选择厂家', //设置没有选择属性时，出现的标题
                data: selectboxArr,
                searchType: 1, //1出现搜索框，不设置不出现搜索框
                showWidth: '250px', //显示下拉的宽
                isRadio: isRadio, //有设置，下拉框改为单选筛选
                allSelect: false,
                selectData: []  //初始化数据
            };
            var selectBox = new selectBoxModule2();
            selectBox.initData(selectInit);
        }

    </script>
    <script src="~/Scripts/react/react.production.min.js"></script>
    <script src="~/Scripts/react/react-dom.production.min.js"></script>
    <script src="~/Scripts/react/babel.min.js"></script>
    <script type="text/babel">
    var NewBaseProductsThis = null;
        // 1.创建类式组件
        class NewBaseProducts extends React.Component {
            constructor() {
                super()
                this.inputRef = React.createRef();  // 创建ref用于引用input元素
                this.state = {
                    baseProductNavs: [  //视角导航
                        { title: '商品视角', name: 'productStatus', isActive: true },
                        { title: '规格视角', name: 'skuStatus', isActive: false },
                        { title: '库存视角', name: 'stockStatus', isActive: false },
                    ],
                    activeNavsName: 'productStatus',//默认选择视角
                    activeNavsTitle: '商品视角',
                    searchData: [   //查询条件  status区分不同视角 productStatus：商品视角  skuStatus：规格视角   stockStatus：库存视角
                        { title: '商品名称', name: 'SpuSubject', type: 'input', sort: 1, value: '', status: ['productStatus', 'skuStatus', 'stockStatus'], isSelect: false },
                        { title: '商品简称', name: 'SpuShortTitle', type: 'input', sort: 2, value: '', status: ['productStatus', 'skuStatus', 'stockStatus'], isSelect: false },
                        
                        { title: '规格名称', name: 'SkuSubject', type: 'input', sort: 4, value: '', status: ['productStatus', 'skuStatus', 'stockStatus'], isSelect: false },
                        { title: '规格简称', name: 'SkuShortTitle', type: 'input', sort: 5, value: '', status: ['productStatus', 'skuStatus', 'stockStatus'], isSelect: false },
                        { title: '组合货品', name: 'IsCombination', type: 'select', sort: 6, value: '', status: ['productStatus', 'skuStatus', 'stockStatus'], isSelect: false },
                        
                        { title: '预警商品', name: 'IsWarn', type: 'select', sort: 10, value: '', status: ['stockStatus'], isSelect: false },
                        { title: '供货方式', name: 'SkuSupplyType', type: 'select', sort: 11, value: '', status: ['productStatus', 'skuStatus'], isSelect: false },
                        { title: '供货厂家', name: 'SelectSupplierId', type: 'select', sort: 12, value: '', status: ['productStatus', 'skuStatus'], isSelect: false },
                        { title: '商品编码', name: 'SpuCode', type: 'input', sort: 13, value: '', status: ['productStatus','skuStatus','stockStatus'], isSelect: false },
                        { title: 'SKUID', name: 'SkuUid', type: 'input', sort: 14, value: '', status: ['skuStatus'], isSelect: false },
                        { title: 'SKU编码', name: 'SkuCode', type: 'input', sort: 15, value: '', status: ['productStatus', 'skuStatus', 'stockStatus'], isSelect: false },
                        
                    ],
                    Suppliers: Suppliers, //供货厂家数据
                    isProductLogoDrawer:false,  //抽屉式-日志
                    productRelLogList: [], //操作日志列表
                    queryLogQarams: { //查询日志参数
                        PageIndex: 1,
                        PageSize: 50,
                        SkuCode: '',
                        SkuId: '',
                        SpuCode: '',
                    },
                    dialogVisible: false, // 组合货品弹框
                    ChildSku: [], //组合货品 - 子货品信息
                    guideLayerData: { //引导蒙层
						step: 1,
						title: '商品视角',
						text: '支持编辑商品、设置供货方式、上架到小站、上架到店铺等。',
						total: 3,
						top: 0,
						left: 0,
                        right: 0,
						isShow: false,
					},
                    guideClearFields: {
                        title: '字段清空设置',
						text: '支持设置规格解除关联后，店铺商品中的相关信息是否清空。',
						total: 1,
						top: 0,
						left: 0,
                        right: 0,
						isShow: false,
                    },
                    guideNavBar: { // 引导蒙层导航
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        width: 0,
                        height: 0,
                    },
                    list: [],
                    allCheckSku: false, //全选 allCheckSku
                    checkSkuList: [], //选中的sku
                    theadEditStatus: false, //表头编辑状态
                    queryQaram:{
						// // 同款类型：0 自营同款、1 下游分销商同款
						// Type: '',
						// // 基础商品skuid
						// BaseProductSkuUid: '',
						// // 基础商品id
						// BaseProductUid: '',
						PageIndex:1,
						PageSize:10,
					},

                    // 顶部tab
                    // 平台
                    platformType: 'TouTiao',
                    tabList: [
                        { id: 4, name: '抖店', value: 'TouTiao',checked: true, },
                        { id: 2, name: '拼多多', value: 'Pinduoduo' },
                        { id: 3, name: '京东', value: 'JingDong' },
                        { id: 1, name: '精选平台', value: 'Alibaba' },
                        { id: 5, name: '我的小站', value: 'Site' },
                    ],
                    // 平台分区列表
                    platformArea: [],
                    // 操作日志src
                    ifarmSrc: '',
                    iframeHeight: 0,
                    // 弹窗参数
					dialogData: {
                        isShowLevelSellPriceRule: false, // 等级分销价规则设置
                        DistributePriceSetting: 1,
						isSave: false,
						//已选sku数量
						skuCount: 0,
						label: '',
						inputValue: '',
                        inputRemarkValue: '',
						main:null,
						supply: '',
                        selectFields:[
							{
								id: 0,
								name: '全选',
								value: 'all',
								checked: false,
							},
							{
								id: 1,
								name: '采购价',
								value: 'IsUseSettlePrice',
								tips: '使用最新采购价对厂家进行财务结算。',
								checked: false,
							},
							{
								id: 2,
								name: '成本价',
								value: 'IsUseCostPrice',
								tips: '使用最新成本价用于自营店铺统计总成本。',
								checked: false,
							},
							{
								id: 3,
								name: '分销价',
								value: 'IsUseDistributePrice',
								tips: '使用最新分销价对下游分销品进行财务结算。',
								checked: false,
							},
							// {
							// 	id: 4,
							// 	name: '库存',
							// 	value: 'IsAllUseWarehouse',
							// 	tips: '一键开启所有分销品使用该库存进行打单发货扣减。',
							// 	checked: false,
							// },
							{
								id: 5,
								name: '供货方式',
								value: 'IsUseSupplierFxUser',
								tips: '使用最新供货方式对所有关联分销品的订单推送。',
								checked: false,
							},
							{
                                id: 6,
                                name: '简称',
                                value: 'IsUseShortName',
                                tips: '默认已关联所有的分销品使用统一简称。',
                                checked: true,
                                disabled: true,
                                absoluteChecked: true,
                            },
						],
						// 是否同步更新
                        isAutoChecked: false,
					},
                    edmitIframeData:[
						{
							title:'基础资料',
							url:'',
							isActive:true,
							type:'Base'
						},
						{
							title:'抖音资料',
							url:'',
							isActive:false,
							type:'TouTiao'
						}
					],
					isReUrl:false,
                    timer: null,
					timerCount: 0,
					// 同步进度
					AutoRelationProces: {
						"TaskCode": "",
						"SuccessCount": 0,
						"FailCount": 0,   // 异常数量
						"Process": -1, // 进度
					},
                    relationProductData:{},
					updateFieldsChecked: false, // 更新字段弹窗
                    inventoryText: '', // 库存
                    // 快速添加弹窗
					isShowQuickAdd: false,
					// 关联同款弹窗
					isShowRelationProduct:false,
                    isShowExceptionLog: false, // 异常日志弹窗
                    isHasAbnormal: false, // 是否有异常
                    popoverMsg : '', // 弹窗提示
                    // tab气泡提示 IsWaitSyncBaseProduct 需更新 NeedUpdate需完善
                    isBubbleTip: false,
                    inOutInputEditStatus: true, // 是否正在编辑
                    levelSellPriceLayerIndex: null, // 等级分销价弹窗index
                    combinationPopover: {
                        isShow: false,
                        left: 0,
                        top: 0,
                        ProSubject: '', // 标题
                        ChildSku: [], // 组合子货品列表
                    },
                    inventoryPopover: {
                        isShow: false,
                        left: 0,
                        top: 0,
                        direction: 'top',
                        ProSubject: '', // 标题
                    },
                    productChangeLogList: [], // 商品变更日志
                    queryQaramProductChangeLog:{
                        PageIndex: 1,
                        PageSize: 50,
                    },
                    isAllCheckEditInList: false, // 库存视角是否全选编辑（入库）
                    isAllCheckEditOutList: false, // 库存视角是否全选编辑（出库）
                }
            }
            componentDidMount() {
                const that = this;
                this.renderSearchData();
                this.CheckIsHasAbnormal();
                this.onGetBaseProductSetting();
                let _operateType = commonModule.getQueryVariable('operateType');
                if (_operateType == "BaseProductSkuRelationBindIndex") {
                    that.changeStatusNavs('skuStatus');
                }
                window.addEventListener('message',function(e){
                    if(e.data.hidePopUp){
                        that.setState({
                            isShowQuickAdd: false,
                            isShowRelationProduct:false,
                            isShowExceptionLog: false,
                        })
                    }
                    // 刷新列表
                    if(e.data.type == "update"){
				        that.activeNavsRequest(true)
                    }
                    if(e.data.type == "bindProduct"){
                        that.activeNavsRequest(true);
                    }
                    if (e.data.type === "update") {
                        that.activeNavsRequest(true);
                    }
                    if(e.data.isShowScreen){
                        $(".createProductIframe").addClass('activeScreen');
                    }else{
                        $(".createProductIframe").removeClass('activeScreen');
                    }

                    if(e.data.operateType && e.data.operateType=="cretaeProduct"){   //ifarme创建商品和编辑商品完成 传过来的
                        if (e.data.createFrom == "edmit") { //编辑商品完成,不关闭页面，更新抖音资料
                            var iframe = document.getElementById('createProductIframe1');
                            var iframeWindow = iframe.contentWindow;
                            iframeWindow.postMessage('onload', '*');
                            var baseproductuid = e.data.baseproductuid || '';
                            const {list} = that.state;
                            var IsTouTiao = true; //抖音商品是否完善
                            if (baseproductuid) {
                                list.forEach((item, index) => {
                                    if (item.UidStr == baseproductuid) {
                                        item.PlatformInfoTags.forEach((item1, index1) => {
                                            if (item1.PlatformType == 'TouTiao') {
                                                IsTouTiao = item1.IsHighlight;
                                            }
                                        })
                                    }
                                })
                            }
                            if (IsTouTiao) return false;
                        }
                        commonModule.w_alert({ type: 4, content: e.data.content});
                        $("#createFullMaskName").removeClass('active');
                        $("#businessCardCreateProductIframeWrap").removeClass('active');
                        $('body').removeClass('hideOverflow');
                        $('body').css({overflow:'auto'});
                        @* setTimeout(function(){
                            window.location.href=window.location.href;
                        },1000) *@
                        that.activeNavsRequest(false);
                        that.setState({isReUrl:true});

                    }

                    if(e.data.operateType && e.data.operateType=="closeCretaeProduct"){
                        var isShow=e.data.isShow || false;
                        if (!e.data.isBubbleTip) {
                            $('body').css({overflow:'auto'});
                            $('body').removeClass('hideOverflow');
                            $("#createFullMaskName").removeClass('active');
                        }

                        that.setState({
                            isBubbleTip: isShow ? e.data.isBubbleTip || '': "", //是否需要弹窗提示
                        },()=>{
                             $('#n_tabNav_pt .popoverCommon-warn').show();
                            setTimeout(function(){
                                $('#n_tabNav_pt .popoverCommon-warn').hide();
                            },3000)
                        })

                    }

                    if(e.data.operateType && e.data.operateType=="tarEdmitDouyunUrl"){
                        var status="edmit";
                        var url=commonModule.rewriteUrl('/BaseProduct/CreateBasePlatformProduct?CreateFrom='+status+'&baseproductuid='+e.data.edmitBaseproductuid);
                        that.createOrEdmitProduct(status,url);
                    }

                    if(e.data.operateType && e.data.operateType=="tarEdmitBaseProductUrl"){
                        var status="edmit";
                        var url=commonModule.rewriteUrl('/BaseProduct/CreateBaseProduct?CreateFrom='+status+'&baseproductuid='+e.data.edmitBaseproductuid+'&changeTime='+new Date().getTime());
                        that.createOrEdmitProduct(status,url);
                    }

                    if(e.data.operateType && e.data.operateType=="goToBaseProductEdmitFun"){
                        var edmitIframeData=that.state.edmitIframeData;
                        edmitIframeData.forEach(function(item,i){
                            item.isActive=false;
                            if(i==0){
                                item.isActive=true;
                            }
                        })
                        that.setState({edmitIframeData});
                    }
                    if (e.data.operateType && e.data.operateType == "closePrepareDistribution") {   //ifarme关闭铺货 传过来的
                        $("#PrepareDistributionIframeWrap").removeClass('active');
                        $('body').css({ overflow: 'auto' });
                        var times = e.data.times || null;
                        if (times) {
                            commonModule.w_alert({ type: 5, skin: 'goBackBatchSetSkin', times: 3000, content: '已取消本次铺货<span class="n-dColor mL12 hover" style="margin-left:12px;" onclick="commonModule.newTarDistribution()">恢复</span>' });
                        }
                    }

                    if (e.data.operateType && e.data.operateType == "StartDistribution") {   //铺货提交后
                        $("#PrepareDistributionIframeWrap").removeClass('active');
                        $('body').css({ overflow: 'auto' });
                        var resultData = JSON.stringify(e.data.resultData);
                        commonModule.alertDistribution(resultData);
                    }
                    if (e.data.operateType && e.data.operateType == "BatchStartDistribution") {   //ifarme关闭批量铺货 传过来的
                        $("#PrepareDistributionIframeWrap").removeClass('active');
                        $('body').css({ overflow: 'auto' });
                        var resultData = JSON.stringify(e.data.resultData);
                        commonModule.alertBatchDistribution(resultData);
                    }

                    if (e.data.operateType && e.data.operateType == "closeCretaeProduct") {   //ifarme创建商品和编辑商品完成 传过来的
                        $("#businessCardCreateProductIframeWrap").removeClass('active');
                        $('body').css({ overflow: 'auto' });
                    }
                });
                // 监听文档点击事件
                document.addEventListener('click', (event) => this.handleClickOutside(event));
                let operateType = commonModule.getQueryVariable("operateType") || '';
                if (operateType == "WareHouseProduct") {
                    that.changeStatusNavs('stockStatus');
                }
                
                //this.initGuideLayer();
                //this.initGuideClearFieldsLayer();
                /* console.log("commonModule.WarnStockNum", commonModule.WarnStockNum);
                if (commonModule.WarnStockNum) {
                    $("#operate_wran_open").addClass("hide");
                    $("#operate_wran_close").removeClass("hide");
                    $(".isShowWarn").removeClass("hide2");
                }else {
                    $(".isShowWarn").addClass("hide2");
                } */
            }
            componentWillUnmount() {
                // 清除事件监听
                document.removeEventListener('click', (event) => this.handleClickOutside(event));
            }
            CheckIsHasAbnormal(){  //是否存在异常数据
				let that=this;
				commonModule.Ajax({
					url: '/BaseProduct/CheckIsHasAbnormal',
					loadingMessage: "加载中",
					showMasker: false,
					data: {},
					success: function (rsp) {
						if (rsp.Success) {
							if(rsp.Data==1){
								that.setState({isHasAbnormal:true})
							}

						} else {
							layer.msg(rsp.Message || '失败')
						}
					}
				})
			}

            renderSearchData() { //渲染查询条件
                let activeNavsName = this.state.activeNavsName;
                let searchData = this.state.searchData;
                let Suppliers = this.state.Suppliers;
                let queryQaram = this.state.queryQaram;

                searchData.forEach((item) => {
                    item.isSelect = false;
                    item.status.forEach((cItem) => {
                        if (activeNavsName == cItem) {
                            item.isSelect = true;
                        }
                    })
                })
                // 使用函数式 setState 更新状态
                this.setState(prevState => ({
                    searchData: prevState.searchData
                }), () => {

                    let SkuSupplyTypes = [
                        { Value: '', Text: '全部' },
                        { Value: '0', Text: '自营' },
                        { Value: '1', Text: '厂家' },
                        { Value: '2', Text: '未设置' }
                    ];

                    var SkuSupplyTypesOptions = {
                        eles: '#SkuSupplyType',
                        name: 'SkuSupplyType',
                        data: SkuSupplyTypes,
                        leftTitle: '供货方式'
                    };
                    this.initRenderSelect(SkuSupplyTypesOptions);


                    let StockWarningData = [
                        { Value: '', Text: '全部' },
                        { Value: '1', Text: '预警' },
                        { Value: '0', Text: '非预警' },
                    ];

                    var StockWarningOptions = {
                        eles: '#IsWarn',
                        name: 'IsWarn',
                        data: StockWarningData,
                        leftTitle: '库存预警'
                    };
                    this.initRenderSelect(StockWarningOptions);



                    let changeSuppliers = [];
                    Suppliers.forEach((item) => {
                        let obj = {};
                        obj.Value = item.FxUserId,
                            obj.Text = item.UserName;
                        changeSuppliers.push(obj);
                    })

                    var SelectSupplierIdInit = {
                        eles: '#SelectSupplierId',
                        name: 'SelectSupplierId',
                        data: changeSuppliers,
                        searchType: 1, //1出现搜索框，不设置不出现搜索框
                        leftTitle: '供货厂家'
                    };
                    this.initRenderSelect(SelectSupplierIdInit);



                    let CombinationProductData = [
                        { Value: '', Text: '全部' },
                        { Value: '1', Text: '组合商品' },
                        { Value: '0', Text: '非组合商品' },
                    ];

                    var CombinationProductDataOptions = {
                        eles: '#IsCombination',
                        name: 'IsCombination',
                        data: CombinationProductData,
                        leftTitle: '组合货品'
                    };
                    this.initRenderSelect(CombinationProductDataOptions);

                    const params = this.getSearchParams();
                    // 回调中获取更新后的状态
                    this.setState({ queryQaram: params }, () => {
                        if (commonModule.WarnStockNum) {
                            $("#operate_wran_open").addClass("hide");
                            $("#operate_wran_close").removeClass("hide");
                            $(".isShowWarn").removeClass("hide2");
                        }else {
                            $(".isShowWarn").addClass("hide2");
                        }
                        this.activeNavsRequest(true);

                    });
                });
            }

            initRenderSelect(options) {
                let searchData = this.state.searchData;
                options.skin = "n-wuSelect";
                options.isRadio = true;
                options.newShowMoreicon = true;
                options.selectCallBack = function (resultData) {

                    let selectValue = "";
                    resultData.forEach((item) => {
                        selectValue = selectValue + item.Value + ",";
                    })

                    selectValue = selectValue.substr(0, selectValue.length - 1)
                    searchData.forEach((item) => {
                        if (item.name == options.name) {
                            item.value = selectValue;
                        }
                    })
                }
                let SelectSupplierBox = new selectBoxModule2();
                SelectSupplierBox.initData(options);
            }
            // 全选同组规格
            onCheckProSubject(item) {
                let { list } = this.state;
                const checkSkuList = [];
                // 半选
                let partialSelect = false;
				let allCheckSku = false;
                let checkNum = 0;
                const productLen = list.length;
                // 处理勾选/取消勾选状态
                list.forEach((iData) => {
                    if (item.BaseProductUidStr === iData.BaseProductUidStr) {
                        iData.isCheck = !iData.isCheck;
                        iData.isAllCheckSubject = iData.isCheck;
                        iData.isPartCheckSubject = false;
                    }
                    if (iData.isCheck) {
                        checkNum++;
                        checkSkuList.push(iData);
                    }
                });
                if (checkNum === productLen) {
                    allCheckSku = true;
                    partialSelect = false;
                }
                if (checkNum > 0 && checkNum !== productLen) {
                    allCheckSku = false;
                    partialSelect = true;
                }
				this.setState({ list: list, checkSkuList, allCheckSku, partialSelect });
            }
            // 选择规格（展开的商品）
			onCheckSku(item, index, type) {
				let {list ,allCheckSku} = this.state;
                let checkSkuList = [];
                // 半选
                let partialSelect = false;
				allCheckSku = true;
                let isCheckSku = !item.isCheck;
				list.forEach((iData, i) => {
                    if (type === 'spu') {
                        if (item.BaseProductUidStr == iData.BaseProductUidStr) {
                            iData.isCheck = isCheckSku;
                        }
                    }
                    if (index === i) {
                        iData.isCheck = isCheckSku;
                    }
                    if (!iData.isCheck) {
                        allCheckSku = false;
                    } else {
                        checkSkuList.push(iData);
                        partialSelect = true;
                    }
                    item.isPartCheckSubject = false;
                    item.isAllCheckSubject = false;
				});
                // 使用 reduce 分组并修改属性
                let sameSkuGroups = list.reduce((result, item) => {
                    // 查找是否已经有相同 BaseProductUidStr 的项
                    let existing = result.find(x => x.BaseProductUidStr === item.BaseProductUidStr);
                    if (existing) {
                        existing.checkedSkuList.push(item.isCheck);
                    } else {
                        // 如果没有找到，创建一个新的对象并初始化 checkedSkuList 数组
                        result.push({
                            BaseProductUidStr: item.BaseProductUidStr,
                            checkedSkuList: [item.isCheck] // 用数组来存储当前组的 isCheck 值
                        });
                    }
                    return result;
                }, []);
                // 根据 checkedSkuList 数组的值来设置 isAllCheckSubject 和 isPartCheckSubject
                sameSkuGroups.forEach(group => {
                    list.forEach(item => {
                        if (item.BaseProductUidStr === group.BaseProductUidStr) {
                            if (group.checkedSkuList.every(isCheck => isCheck === true)) {
                                item.isAllCheckSubject = true;
                                item.isPartCheckSubject = false;
                            } else if (group.checkedSkuList.some(isCheck => isCheck === true)) {
                                item.isAllCheckSubject = false;
                                item.isPartCheckSubject = true;
                            } else {
                                item.isAllCheckSubject = false;
                                item.isPartCheckSubject = false;
                            }
                        }
                    });
                });
				this.setState({ list: list, allCheckSku, checkSkuList, partialSelect });
			}
            // 库存选择规格（展开的商品）
			onCheckWareHouseSku(Id) {
				let {list ,allCheckSku} = this.state;
                let checkSkuList = [];
                // 半选
                let partialSelect = false;
				allCheckSku = true;
				list.forEach((iData, i) => {
                    iData.isAllCheck = true;
                     iData.isPartCheck = false;
                    if (iData.WareHouseSkus && iData.WareHouseSkus.length > 0) {
                        iData.WareHouseSkus.forEach((jData, j) => {
                            if (jData.Id === Id) {
                                jData.isCheck = !jData.isCheck;
                            }
                            if (!jData.isCheck) {
                                allCheckSku = false;
                                iData.isAllCheck = false;
                            } else {
                                checkSkuList.push(jData);
                                partialSelect = true;
                                iData.isPartCheck = true; // 部分选中
                            }
                        })
                    }
				})
				this.setState({ list: list,allCheckSku,checkSkuList,partialSelect})
			}
            // 库存选择规格（展开的商品）
			onCheckWareHouse(item,index) {
				let {list ,allCheckSku} = this.state;
                let checkSkuList = [];
                // 半选
                let partialSelect = false;
				allCheckSku = true;
				list.forEach((iData, i) => {
                    if (iData.Id === item.Id) {
                        iData.isCheck = !item.isCheck;
                        item.isAllCheck = !item.isAllCheck;
                        iData.isPartCheck = item.isAllCheck || false;
                    }
                    if (iData.WareHouseSkus && iData.WareHouseSkus.length > 0) {
                        iData.WareHouseSkus.forEach((jData, j) => {
                            jData.isCheck = iData.isCheck;
                            if (!jData.isCheck) {
                                allCheckSku = false;
                            } else {
                                checkSkuList.push(jData);
                                partialSelect = true;

                            }
                        })
                    }
				})
				this.setState({ list: list,allCheckSku,checkSkuList,partialSelect})
			}

            // 库存选择规格(展开更多子货品)
            onShowChildSkusMore(Id) {
                let {list } = this.state;
				list.forEach((iData, i) => {
                    if (iData.WareHouseSkus && iData.WareHouseSkus.length > 0) {
                        iData.WareHouseSkus.forEach((jData, j) => {
                            if (jData.Id === Id) {
                                jData.isMore = !jData.isMore;
                            }
                        })
                    }
				})
				this.setState({ list: list})
            }

			// 全选规格
			onCheckAllSku() {
				let { list, allCheckSku, theadEditStatus, activeNavsName } = this.state;
				allCheckSku = !allCheckSku;
                let checkSkuList = [];
				list.forEach((iData, i) => {
					iData.isCheck = allCheckSku;
                    iData.isAllCheckSubject = allCheckSku;
                    iData.isPartCheckSubject = false;
                    iData.isAllCheck = allCheckSku;
                    iData.isPartCheck = false;
                    if (iData.isCheck) {
					    checkSkuList.push(iData);
					}
                    if (activeNavsName == "stockStatus") {
                        if (allCheckSku) {
                            checkSkuList.push(...iData.WareHouseSkus); // 使用扩展运算符将WareHouseSkus的数据推入数组
                        }
                        iData.WareHouseSkus.forEach((jData, j) => {
                            jData.isCheck = allCheckSku;
                        });
                        //console.log("checkSkuList", checkSkuList);
                    }
				});
				theadEditStatus = allCheckSku;
				this.setState({ list: list, allCheckSku,theadEditStatus,checkSkuList,partialSelect: false })
			}
            // 编辑入库和出库
            onChangeInOutCountInput(type) {
                const { list } = this.state;
                let {isAllCheckEditInList, isAllCheckEditOutList}= this.state;
                if (type === 'InList') {
                isAllCheckEditInList = !isAllCheckEditInList;
                } else if (type === 'OutList') {
                    isAllCheckEditOutList = !isAllCheckEditOutList;
                }
                list.forEach(item => {
                    if (type === 'InList') {
                        item.WareHouseSkus.forEach(jItem => {
                            jItem.isAllCheckEditInList = isAllCheckEditInList;
                            jItem.isShowInListInput = isAllCheckEditInList;
                        });
                    } else {
                        item.WareHouseSkus.forEach(jItem => {
                            jItem.isAllCheckEditOutList = isAllCheckEditOutList;
                            jItem.isShowOutListInput = isAllCheckEditOutList;
                        });
                    }
				});
                this.setState({ list: list, isAllCheckEditInList, isAllCheckEditOutList });
            }
            // 批量按钮
			onBtnBatch (keysName,itemData) {
                let { checkSkuList,dialogData, activeNavsName } = this.state;
				var that =this;
				var msg = '';
                this.setState({
					popoverMsg: ''
				})
                if (itemData) {
                    checkSkuList =[itemData];
                }
				if (checkSkuList.length == 0) {
					msg = "请在列表中选择 1 个以上的商品";
					this.setState({
						popoverMsg: msg
					})
					return false
				}
				// 供货方式
				if (keysName == 'supply') {
                    dialogData.type = activeNavsName === 'productStatus'? 'spu' : 'sku';
				    this.setState({ dialogData });
					this.onColumnSupply()
				}
				// 上架到小站
				if (keysName == 'site') {
					let filterChckList = [];
					let msgFlag = true;
					// 过滤已上架商品
					checkSkuList.forEach((item,i)=>{
						if (item.isCheck){
                            msgFlag = false;
						}
						if (item.PlatformInfoTags && item.PlatformInfoTags.length) {
							item.PlatformInfoTags.forEach((jData,j)=>{
								if (jData.PlatformType == "PuHuo" && !jData.IsHighlight) {
									filterChckList.push(item);
								}
							})
						}
					})
					if (filterChckList.length == 0) {
						layer.msg('该商品已上架小站');
						return;
					}
					// if (msgFlag) {
					// 	layer.msg('默认按该商品的SPU的维度上架');
					// 	setTimeout(()=>{
					// 		that.onBatchProduct(filterChckList)
					// 	},3000)
					// 	return;
					// }
					this.onBatchProduct(filterChckList)
				}
				// 上传到店铺
				if (keysName == 'shop') {
					if (checkSkuList.length > 50) {
						msg = '批量铺货不得超过 50 条';
					}else{
                        /*if (checkSkuList.length == 1){
                            var checkListObj=checkSkuList[0];
                             var FxUserId=checkListObj.ProductSupplierUserId;
                            var UidStr = checkListObj.UidStr;
                            var FromType = 0;
                            var parame = {
                                FromType
                            }
                            commonModule.tarDistribution('edmit', UidStr,parame);
                        } else {
                            this.showTarDistribution();
                        }*/
                       $('.popoverCommon-warn').css("cssText","display:none !important");
                        //$('.popoverCommon-warn').css("display","none");
                       this.showTarDistribution(itemData);
                       setTimeout(function(){
                           $('.popoverCommon-warn').css("display","none");
                       },300)
					}

				}
				// 关联同款
				if (keysName == 'relation') {
					this.onDialogAutoRelationBind()
				}

				this.setState({
					popoverMsg: msg
				})
			}

            showTarDistribution(item,index){
                const {checkSkuList}= this.state;
                let popoverMsg = '';
                if (!item && !checkSkuList.length){
                    layer.msg('请选择商品');
                    return;
                }
                if (checkSkuList.length > 50 ) {
                    popoverMsg = '批量铺货不得超过 50 条';
                    this.setState({ popoverMsg });
                    return
                }

                var edmitBaseproductuid = []; //编辑商品
                if (!item){
                    checkSkuList.map((item,index)=>{
                        if (item.UidStr){
                            FxUserId = item.SupplierFxUserId || '';
                            UidStr = item.UidStr;
                            edmitBaseproductuid.push(item.UidStr);
                        }
                    })
                    localStorage.setItem('edmitBaseproductuid',JSON.stringify(edmitBaseproductuid));
                    var FromType = 0;
                    let obj = {FromType}
                    commonModule.tarDistribution('edmit', '',obj);
                } else {
                    var FxUserId = item.SupplierFxUserId || '';
                    var UidStr = item.UidStr || '';
                    edmitBaseproductuid.push(UidStr);
                    localStorage.setItem('edmitBaseproductuid',JSON.stringify(edmitBaseproductuid));
                    var FromType = 0;
                    let obj = {FxUserId,FromType}
                    commonModule.tarDistribution('edmit', '',obj);
                }
            }

            // 获取已选sku
			onCheckSkuList() {
				let { list,activeNavsName } = this.state;

				var checkSkuList = [];
				list.forEach((iData, i) => {
                    if (iData.isCheck) {
                        checkSkuList.push(iData);
                    }
                    // if (activeNavsName === 'skuStatus'){
                    //     if (iData.isCheck){
                    //         checkSkuList.push(iData);
                    //     }
                    // } else {
                    //     if (iData.isCheck && iData.Skus && iData.Skus.length > 0) {
                    //         iData.Skus.forEach((jData, j) => {
                    //             if (jData.isCheck) {
                    //                 checkSkuList.push(jData);
                    //             }
                    //         })
                    //     }
                    // }
				})
				return checkSkuList;
			}
			// 供货方式
			onColumnSupply(skuList) {
				var that = this;
				var checkSkuList = skuList || this.onCheckSkuList();
				var dialogData = this.state.dialogData;
				var skuCount = 0;
                if (skuList && skuList.length && skuList[0].Skus) {
                    skuList[0].Skus.forEach((iData,i)=>{
                        if (iData.isCheck) {
                            ++skuCount;
                        }
                    })
                }
				dialogData.skuCount = skuList ? skuCount : checkSkuList.length;
				dialogData.main = layer.open({
					type: 1,
					title: '编辑供货方式', //不显示标题
					content: $('#dialogSupply'),
					skin: 'n-skin dialogSupply-skin',
					success:function(){
						$("input[name=supply]").each(function(index, el) {
							el.checked= false;
						})
               			layui.form.render();
						initSupplierSelectBox("supplier_name_select",true);
					},
					area: '400px', //宽高
					cancel: function(){
						that.onDialogClose();
 				    },
				});
				dialogData.isSave = false;
				this.setState({ dialogData,checkSkuList });
			}
			// 供货方式列表项
			onColumnSpuSupply (item, subIndex,type){
				var list = this.state.list;
				var dialogData = this.state.dialogData;
				var checkSkuList = [];
				if (type == 'spu'){
					item.isCheck = true;
					item.Skus.forEach((jData, j) => {
						jData.isCheck = true;
					})
					checkSkuList.push(item);
				}
				if (type == 'sku'){
					list[subIndex].isCheck = true;
					// checkSkuList.push(item);
					var _item = JSON.parse(JSON.stringify(item));
					// _item.Skus.forEach((iData,i)=>{
					// 	iData.isCheck = false;
					// 	if (i == subIndex) {
					// 		iData.isCheck = true
					// 	}
					// })
					checkSkuList.push(_item,list)
				}
				dialogData.type = type;
				this.setState({ dialogData });
				this.onColumnSupply(checkSkuList);
			}
            // 规格视角 - 批量删除
            onBtnBatchDelete(type, itemData, itemIndex){
                const {activeNavsName} = this.state;
                var that = this;
				var checkSkuList = [];
                if (type === 'single') {
                    checkSkuList = itemData.Skus;
                } else {
                    let _checkSkuList = this.onCheckSkuList();
                    if (type === 'batch') {
                        _checkSkuList.forEach((item, index) => {
                            checkSkuList = [...checkSkuList,...item.Skus]
                        })
                    } else {
                        checkSkuList = _checkSkuList
                    }
                }
                if (!checkSkuList.length) {
                    commonModule.w_alert({ type: 3, content: activeNavsName === 'productStatus' ? '请先选中商品' : '请先选中规格' });
                    return false;
                }
				var dialogData = this.state.dialogData;
                dialogData.skuCount = checkSkuList.length;

                var BaseProductUids = [];
                var BaseProductSkuUids = [];
				checkSkuList.forEach((item, i) => {
					if (item.isCheck || type === 'single') {
						BaseProductUids.push(item.BaseProductUidStr)
                        BaseProductSkuUids.push(item.UidStr)
                        item.type = type || '';
					}
				})
                let newBaseProductUids = [...new Set(BaseProductUids)];

                var data = {
                    "BaseProductUids": newBaseProductUids,
                    "BaseProductSkuUids": BaseProductSkuUids,
                    "IsBaseProduct": activeNavsName === 'productStatus' ? true : false,
                    "IsRestoreShortTitle": false,
                    "IsRestoreCostPrice": false,
                    "IsRestoreSettlePrice": false,
                    "IsRestoreDistributePrice": false,
                    "IsRestoreSupplier": false,
                    "IsAllUseWarehouse": true,
				}
                dialogData.selectFields.forEach((iData, i) => {
					if(i) {
						data[iData.value] = iData.checked;
					}
				})
				
                dialogData.isShowDeleteCombineProduts = false;
                
                dialogData.isShowDeleteCombineProdutSkus = false;
               
                dialogData.selectFields = [
                    {
                        id: 0,
                        name: '全选',
                        value: 'all',
                        checked: false,
                    },
                    {
                        id: 1,
                        name: '采购价',
                        value: 'IsRestoreSettlePrice',
                        tips: '清空对当前代发商品设置的厂家结算价。',
                        checked: false,
                    },
                    {
                        id: 2,
                        name: '成本价',
                        value: 'IsRestoreCostPrice',
                        tips: '清空当前自营店铺商品当前设置的成本价。',
                        checked: false,
                    },
                    {
                        id: 3,
                        name: '分销价',
                        value: 'IsRestoreDistributePrice',
                        tips: '清空对当前下游推送的商品设置的商家结算价。',
                        checked: false,
                    },
                    {
                        id: 5,
                        name: '供货方式',
                        value: 'IsRestoreSupplier',
                        tips: '清空当前商品设置的供货方式。',
                        checked: false,
                    },
                    {
                        id: 6,
                        name: '简称',
                        value: 'IsRestoreShortTitle',
                        tips: '默认清空当前商品已使用的简称。',
                        checked: true,
                        disabled: true,
                        absoluteChecked: true,
                    },
                ];
				dialogData.isSave = true;
                commonModule.Ajax({
					url: '/api/BaseProduct/BathcDeleteBaseProductCheck',
					loadingMessage: "加载中",
					showMasker: false,
					data: data,
					success: function (rsp) {
						if (rsp.Success) {
                            
                            dialogData.main = layer.open({
                                type: 1,
                                title: activeNavsName === 'productStatus' ? '是否删除该商品？' : '是否删除该规格？', //不显示标题
                                content: $('#dialogDeleteSku'),
                                skin: 'n-skin dialogSupply-skin',
                                success:function(){
                                    that.initGuideClearFieldsLayer();
                                },
                                area: 'auto', //宽高
                                maxWidth: 560,
                                // maxHeight: 500,
                                cancel: function(){
                                    that.onDialogClose();
                                },
                            });
                            
                            rsp.Data.DeleteCombineProduts && rsp.Data.DeleteCombineProduts.map((item)=> {
                                if (item.WareHouseProductImageUrl ) {
                                    item.WareHouseProductImageUrl = commonModule.newTransformImgSrc(item.WareHouseProductImageUrl)
                                }
                            })
                            rsp.Data.DeleteCombineProdutSkus && rsp.Data.DeleteCombineProdutSkus.map((item)=> {
                                if (item.ImageUrl) {
                                    item.ImageUrl = commonModule.newTransformImgSrc(item.ImageUrl)
                                }
                            })
                            let newData = {...dialogData, ...rsp.Data};
                            that.setState({ dialogData:newData,checkSkuList });
						} else {
							layer.msg(rsp.Message)
						}
					}
				})
				
            }
            // 规格视角 - 批量删除 - 保存
            onDeleteSkuSave () {
                const that = this;
                const { dialogData,checkSkuList,activeNavsName } = this.state;
                dialogData.isShowDialogDeleteSkuField = false;
                dialogData.main = layer.close(dialogData.main);
                var BaseProductUids = [];
                var BaseProductSkuUids = [];
				checkSkuList.forEach((item, i) => {
					if (item.BaseProductUidStr && (item.isCheck || item.type === 'single')) {
						BaseProductUids.push(item.BaseProductUidStr)
                        BaseProductSkuUids.push(item.UidStr)
					}
                    if (!item.BaseProductUidStr && item.Skus && item.Skus.length>0) {
                        item.Skus.forEach((sitem) => {
                            BaseProductUids.push(sitem.BaseProductUidStr);
                            BaseProductSkuUids.push(sitem.UidStr);
                        })
                    }
				})
                let newBaseProductUids = [...new Set(BaseProductUids)];
                var data = {
                    "BaseProductUids": newBaseProductUids,
                    "BaseProductSkuUids": BaseProductSkuUids,
                    "IsBaseProduct": activeNavsName === 'productStatus' ? true : false,
                    "IsRestoreShortTitle": false,
                    "IsRestoreCostPrice": false,
                    "IsRestoreSettlePrice": false,
                    "IsRestoreDistributePrice": false,
                    "IsRestoreSupplier": false,
                    "IsAllUseWarehouse": true,
				}
                dialogData.selectFields.forEach((iData, i) => {
					if(i) {
						data[iData.value] = iData.checked;
					}
				})
                
				commonModule.Ajax({
					url:  activeNavsName === 'productStatus' ?  '/api/BaseProduct/BatchDeleteBaseProducts':'/api/BaseProduct/BatchDeleteBaseProductSkus',
					loadingMessage: "加载中",
					showMasker: false,
					data: data,
					success: function (rsp) {
						if (rsp.Success) {
                            that.activeNavsRequest(true);
                            that.onDialogClose();
                            dialogData.main = layer.open({
                                type: 1,
                                title: '删除结果', //不显示标题
                                content: $('#dialogDeleteResult'),
                                skin: 'n-skin dialogSupply-skin',
                                success:function(){
                                    
                                },
                                area: ['560px','auto'], //宽高
                                maxWidth: 560,
                                // maxHeight: 500,
                                cancel: function(){
                                    that.onDialogClose();
                                },
                            });
							that.setState({ checkSkuList:[],dialogData:{...dialogData, ...rsp.Data} });
						} else {
							layer.msg(rsp.Message)
						}
					}
				})
            }
            // 规格视角 - 批量删除 - 字段保存弹窗
            onDialogDeleteSkuFieldSave () {
                const that = this;
                const { dialogData } = this.state;
                dialogData.isShowDialogDeleteSkuField = true;
                
                commonModule.Ajax({
					url: '/api/BaseProduct/GetDeleteSetting',
					loadingMessage: "加载中",
					showMasker: false,
                    type: "GET",
					success: function (rsp) {
						if (rsp.Success) {
                            dialogData.main2 = layer.open({
                                type: 1,
                                title: '字段清空设置', //不显示标题
                                content: $('#dialogDeleteSku2'),
                                skin: 'n-skin dialogSupply-skin',
                                success:function(){
                                    
                                },
                                area: 'auto', //宽高
                                maxWidth: 560,
                                // maxHeight: 500,
                                cancel: function(){
                                    that.onDialogDeleteSkuFieldSaveClose();
                                },
                            });
                            dialogData.selectFields[0].checked = true;
                            for (const key in rsp.Data) {
                                const element = rsp.Data[key];
                                const result = dialogData.selectFields.find(elem => elem.value === key);
                                result.checked = element;
                                if (!element) {
                                    dialogData.selectFields[0].checked = false;
                                }
                            }
                            that.setState({
                                dialogData: dialogData,
                            })
                        }
                    }
                })
                
            }
            onDialogDeleteSkuFieldSaveClose (type) {
                const { dialogData } = this.state;
                if (!dialogData.main2) return
                layer.close(dialogData.main2);
                dialogData.isShowDialogDeleteSkuField = false;
                if (type == 'save') {
                    let data = {};
                    dialogData.selectFields.forEach((iData, i) => {
                        if(i) {
                            data[iData.value] = iData.checked;
                        }
                    })
                    commonModule.Ajax({
                        url: '/api/BaseProduct/SetDeleteSetting',
                        loadingMessage: "加载中",
                        showMasker: false,
                        data:data,
                        type: "POST",
                        success: function (rsp) {
                            if (rsp.Success) {
                                
                            }
                        }
                    });
                }
                this.setState({ dialogData });
            }
			onChangeSupply (value){
				var dialogData = this.state.dialogData;
				dialogData.supply = value;
				dialogData.isSave = value || value == 0 ? true : false;
				this.setState({ dialogData });
			}
			// 绑定厂家校验
			onSupplySaveCheck () {
				const that = this;
				var list = this.state.list;
				var dialogData = this.state.dialogData;
				var SupplierFxUserId = '';
				// 是否存在不同供货类型
				var isDifferent = false;
				var checkSkuList = [];
				var BaseProductUids = [];
				list.forEach((item, i) => {
					if (item.isCheck) {
						checkSkuList.push(item);
						BaseProductUids.push(item.UidStr)
					}
                    if (item.Skus && item.Skus.length > 0) {
                        item.Skus.forEach((iData, i) => {
                            if (iData.isCheck) {
                                if(SupplierFxUserId != "" && SupplierFxUserId == iData.SupplierFxUserId && item.Skus.length > 1) {
                                    isDifferent = true;
                                }
                                SupplierFxUserId = iData.SupplierFxUserId;
                            }
                        })
                    }

				})
				// spu维度未选中，供货类型一致
				if (dialogData.type == 'sku' || BaseProductUids.length == 0) {
					that.onSupplySave();
					return false
				}
				// 厂家id
				var supplier = $('#supplier_name_select').attr('data-values');
				var data = {
					"SupplierId": supplier,
					"IsSelf": true,
					"BaseProductUids": BaseProductUids
				}
				commonModule.Ajax({
					url: '/api/BaseProduct/CheckBaseProductBindSupplier',
					loadingMessage: "加载中",
					showMasker: false,
					data: data,
					success: function (rsp) {
						if (rsp.Success) {
							if (rsp.Data.IsDifferent) {
								if(dialogData.main) {
								dialogData.main = layer.close(dialogData.main);
								}
								dialogData.main = layer.open({
									type: 1,
									title: '多个SKU变更提醒', //不显示标题
									content: $('#dialogSupplyCheck'),
									skin: 'n-skin dialogSupply-skin',
									success:function(){

									},
									area: '400px', //宽高
									cancel: function(){
										that.onDialogClose();
									},
								});
								dialogData.isSave = true;
								dialogData.type = 'spu'
								that.setState({ dialogData,checkSkuList });
							}else {
								that.onSupplySave();
							}

						} else {
							layer.msg(rsp.Message)
						}
					}
				})

			}
			// 绑定厂家
			onSupplySave (BindType) {
				var that = this;
                const {activeNavsName} = this.state;
				var dialogData = this.state.dialogData;
				var checkSkuList = this.state.checkSkuList;
				var list = this.state.list;
				// 厂家id
				var supplier = $('#supplier_name_select').attr('data-values');
				var obj = {};
				var NoCheckedProductUidStr = '';
				var NoCheckedSkuUidStr = '';
				var ProductSelectedUids = [];
				var BindMethod = 'Sku';
				if (checkSkuList.length && (dialogData.type == 'spu'||dialogData.type == 'sku')) {
                    if (dialogData.type == 'sku') {

                        checkSkuList.forEach((jData, j) => {
                            if (jData.isCheck) {
                                var _uid = jData.UidStr;
                                var _baseproductuid = jData.BaseProductUidStr;
                                if (!obj[_baseproductuid]) {
                                    var arr = [];
                                    arr.push(_uid)
                                    Object.defineProperty(obj, [_baseproductuid], {
                                        value: arr,
                                        enumerable: true,
                                        configurable: true,
                                        writable: true
                                    });
                                } else {
                                    obj[_baseproductuid].push(_uid);
                                }
                            }
                            for (var i = 0; i < list.length; i++) {
                                    let item = list[i];
                                    if (jData.BaseProductUidStr == item.BaseProductUidStr) {
                                        if (!item.Checked) {
                                            NoCheckedProductUidStr += item.BaseProductUidStr+','
                                            NoCheckedSkuUidStr += item.UidStr+','
                                        }
                                    }
                            }
                            if (dialogData.type== 'sku') {
                                ProductSelectedUids.push(jData.UidStr)
                            }
                        })
                            // var _uid = jData.UidStr;
                            // var _baseproductuid = jData.BaseProductUidStr;
                            // if (!obj[_baseproductuid]) {
                            //     var arr = [];
                            //     arr.push(_uid)
                            //     Object.defineProperty(obj, [_baseproductuid], {
                            //         value: arr,
                            //         enumerable: true,
                            //         configurable: true,
                            //         writable: true
                            //     });
                            // } else {
                            //     obj[_baseproductuid].push(_uid);
                            // }

                    } else{
                        checkSkuList.forEach((iData, i) => {
                            if (iData.isCheck) {
                                BindMethod = 'Product';
                                ProductSelectedUids.push(iData.UidStr);
                            }
                            iData.Skus && iData.Skus.forEach((jData, j) => {
                                if (jData.isCheck) {
                                    var _uid = jData.UidStr;
                                    var _baseproductuid = jData.BaseProductUidStr;
                                    if (!obj[_baseproductuid]) {
                                        var arr = [];
                                        arr.push(_uid)
                                        Object.defineProperty(obj, [_baseproductuid], {
                                            value: arr,
                                            enumerable: true,
                                            configurable: true,
                                            writable: true
                                        });
                                    } else {
                                        obj[_baseproductuid].push(_uid);
                                    }
                                    if (dialogData.type== 'sku') {
                                        ProductSelectedUids.push(jData.UidStr)
                                    }
                                } else {
                                    NoCheckedProductUidStr += jData.BaseProductUidStr+','
                                    NoCheckedSkuUidStr += jData.UidStr+','
                                }
                            })
                        })
                    }


				} else {
					let newList= [];
					list.forEach((item, i) => {
                        if (item.isCheck) {
                            newList.push(item);
                        }
					})
					newList.forEach((item, i) => {
						if (item.isCheck) {
							ProductSelectedUids.push(item.UidStr);
							BindMethod = 'Product'
						}
						item.Skus.forEach((iData, i) => {
							if (iData.isCheck) {
								var _uid = iData.UidStr;
								var _baseproductuid = iData.BaseProductUidStr;
								if (!obj[_baseproductuid]) {
									var arr = [];
									arr.push(_uid)
									Object.defineProperty(obj, [_baseproductuid], {
										value: arr,
										enumerable: true,
										configurable: true,
										writable: true
									});
								} else {
									obj[_baseproductuid].push(_uid);
								}
							} else {
								NoCheckedProductUidStr += iData.BaseProductUidStr+','
								NoCheckedSkuUidStr += iData.UidStr+','
							}
						})
					})
				}
				// 清除后面逗号
				NoCheckedProductUidStr = NoCheckedProductUidStr.substring(0, NoCheckedProductUidStr.length - 1);
				NoCheckedSkuUidStr =NoCheckedSkuUidStr.substring(0, NoCheckedSkuUidStr.length - 1);
				var reqModel = {
					SkuUidStrDic: obj,
					//SkuUidStrDic: obj,
					BaseProductConfigModels: [
						{
							//Config: type == self ? selfConfig : supplierConfig, // 绑定规则 同理商品列表页面的绑定厂家传参
							ConfigType: 0, //绑定类型：0：默认，1：地址，2：下单时间
							SupplierId: supplier, // 供应商Id
							IsDelete: dialogData.supply == 0 ? true:false, // 是否删除 设为自营时传入true

						}
					],
					BindProductType: 'all',
					// 未勾选的规格Uid，多个逗号分隔
					NoCheckedSkuUidStr: NoCheckedSkuUidStr,
					// 未勾选的商品Uid，多个逗号分隔
					NoCheckedProductUidStr: NoCheckedProductUidStr,
					isSelf: dialogData.supply == 0 ? true:false,
					// 绑定方式 Sku、Product
					BindMethod: BindMethod,
					// 应用于商品SUP维度绑定：0 默认覆盖，1 跳过已绑定
				 	BindType: BindType || 0,
				 	// 应用于商品SUP维度绑定(选中)
				 	ProductSelectedUids: BindMethod == 'Product' ? ProductSelectedUids : [],
				}
				// if (ProductSelectedUids.length) {
				// 	// 应用于商品SUP维度绑定：0 默认覆盖，1 跳过已绑定
				// 	reqModel.BindType= BindType || 0;
				// 	// 应用于商品SUP维度绑定(选中)
				// 	reqModel.ProductSelectedUids= ProductSelectedUids;
				// }
                //console.log("reqModel",reqModel);

				commonModule.Ajax({
					url: '/BaseProduct/TriggerBaseProductBindSupplier',
					loadingMessage: "绑定中",
					showMasker: false,
					data: reqModel,
					success: function (rsp) {
						if (rsp.Success) {
							layer.msg(rsp.Data || '成功', {
                                icon: 1,
                                time: 1000
                            }, function () {

                            });
							if (checkSkuList.length && (dialogData.type == 'spu'||dialogData.type == 'sku')){
								that.activeNavsRequest(false, 'spu');
							} else {
                                that.activeNavsRequest(true);
							}
							that.onDialogClose();

						} else {
							layer.msg(rsp.Message)
						}
					}
				})
			}
            // 上架到小站
            onBatchProduct(checkList) {
				const that = this;
				let uidList = [];
				if (checkList.length == 0) {
					layer.msg('请先选择要操作的商品');
					return;
				}
				checkList.forEach((item,i)=>{
					uidList.push(item.UidStr);
				})
				let data = {
					BaseProductUidList: uidList,
				}
				commonModule.Ajax({
					url: '/api/SupplierProduct/PutOn',
					loadingMessage: "请求中",
					showMasker: false,
					contentType: 'application/json',
					data: JSON.stringify(data),
					success: function (rsp) {
						if (rsp.Success) {
							layer.msg( rsp.Data || '');
							setTimeout(function () {
                                that.activeNavsRequest(false);
							},1000)
						} else {
							layer.msg(rsp.Message || rsp.Data || '失败');
						}
					}
				})
			}

            // 自动关联前置弹窗
			onDialogAutoRelationBind() {
				var checkSkuList = this.onCheckSkuList();
				var dialogData = this.state.dialogData;
				if(checkSkuList.length == 0) {
					layer.msg('请选择需要自动映射编码的商品数据', { icon: 2 });
					return false;
				}
				var that =this;
				dialogData.skuCount = checkSkuList.length;
				dialogData.main = layer.open({
					type: 1,
					title: '自动关联商品',
					content: $('#dialogAutoRelationBind'),
					skin: 'n-skin',
					area: ['600px', 'auto'], //宽高
					success:function(){
						$('.explain-img').attr('src',"/Content/Images/automaticExplanation.png")
					},
					cancel: function(){
						that.onDialogClose();
 				    },
				});
				dialogData.label ='';
                dialogData.isSave = true;
				this.setState({ checkSkuList,dialogData });
			}
			onDialogAutoRelationBindNext () {
				var updateFieldsChecked = this.state.updateFieldsChecked;
				var dialogData = this.state.dialogData;
				if (updateFieldsChecked) {
					if(dialogData.main) {
						dialogData.main = layer.close(dialogData.main);
					}
					this.setState({ dialogData },()=>{
						this.onAutoRelationBind()
					});

					return false
				}
				this.onBindProductSave()
			}
			// 自动关联
			onAutoRelationBind () {
				var that=this;

				var dialogData = this.state.dialogData;
				var checkSkuList = this.onCheckSkuList();
				dialogData.skuCount = checkSkuList.length;
				dialogData.main = layer.open({
					type: 1,
					title: '请选择更新字段',
					content: $('#dialogBindProduct'),
					skin: 'n-skin',
					area: ['600px', 'auto'], //宽高
					success:function(){
						$("#goBackskinBtn").text("确定");
						$("#goBackskinBtn_stop").text("确定");
						var dialogData=that.state.dialogData;
						dialogData.selectFields.forEach((item,i)=>{
							item.checked=false;
						});
						that.setState({dialogData});
					},
					cancel: function(){
						that.onDialogClose();
 				    },
				});
				dialogData.label ='';
                dialogData.isSave = true;
				this.setState({ dialogData,checkSkuList });


			}
			// 自动关联 - 更新字段
			onDialogHandleCheckbox (item,index) {
				var dialogData = this.state.dialogData;
				dialogData.isSave = true;
				var _checked = item.checked;
                // 默认全选
                dialogData.selectFields[0].checked = true;
				dialogData.selectFields.forEach((iData, i) => {
					if (item.value == 'all') {
						iData.checked = !_checked;
					} else if (item.value != 'all' && index == i){
						iData.checked = !iData.checked;
					}

					if (iData.checked) { // 有选中保存按钮可按
						dialogData.isSave = true;
					}
					if (!iData.checked && i ) { // 有字段没选全选不选中
						dialogData.selectFields[0].checked = false;
					}
					if (iData.absoluteChecked) {
                        iData.checked = true;
                    }
				})
				this.setState({ dialogData });
			}
			// 自动关联 - 选择更新字段提交
			onBindProductSave () {
				var that = this;
				var dialogData = this.state.dialogData;
				var list = this.state.list;
				if (dialogData.type == 'abnormalRelationUnbind') {
					this.getAbnormalRelationUnbind()
					return false;
				}
				// 已选关联同款商品
				var checkEqualProducts = this.state.checkSkuList;
				var productSkuDetail = checkEqualProducts[0];
				if (checkEqualProducts.length == 0) {
					layer.msg('请选择关联同款商品',{zIndex:100000000000})
					return false;
				}
				var SkuList = [];
				var SkuCodeList = [];
				checkEqualProducts.forEach(function (item) {
					var obj = {
						IsUseWarehouse: item.IsUseWarehouse,
						IsDisabledUseWarehouse: item.IsDisabledUseWarehouse,
						ProductSkuCode: item.SkuCode,
						ProductCode: item.ProductCode,
					}
					SkuList.push(obj);
					SkuCodeList.push(item.SkuCode);
				})
				var skuUidStrDicObj = {};
				var NoCheckedProductUidStr = '';
				var NoCheckedSkuUidStr = '';
				let newList= [];
				list.forEach((item, i) => {
					let flag = false;
                    if (item.Skus && item.Skus.length > 0) {
                        item.Skus.forEach((iData, i) => {
                            if (iData.isCheck) {
                                flag = true;
                            }
                        })
                    } else {
                        if (item.isCheck) {
                            flag = true;
                        }
                    }

					if (flag) {
						newList.push(item);
					}
				})
				let obj = {};
				newList.forEach((item, i) => {
                    if (item.Skus && item.Skus.length > 0) {
                        item.Skus.forEach((iData, i) => {

                            if (iData.isCheck) {
                                var _uid = iData.UidStr;
                                var _baseproductuid = iData.BaseProductUidStr;
                                if (!skuUidStrDicObj[_baseproductuid]) {
                                    var arr = [];
                                    arr.push(_uid)
                                    Object.defineProperty(skuUidStrDicObj, [_baseproductuid], {
                                        value: arr,
                                        enumerable: true,
                                        configurable: true,
                                        writable: true
                                    });
                                } else {
                                    skuUidStrDicObj[_baseproductuid].push(_uid);
                                }
                            } else {
                                NoCheckedProductUidStr += iData.BaseProductUidStr+','
                                NoCheckedSkuUidStr += iData.UidStr+','
                            }
                        })
                    } else {
                        let iData = item;
                        if (iData.isCheck) {
                            var _uid = iData.UidStr;
                            var _baseproductuid = iData.BaseProductUidStr;
                            if (!skuUidStrDicObj[_baseproductuid]) {
                                var arr = [];
                                arr.push(_uid)
                                Object.defineProperty(skuUidStrDicObj, [_baseproductuid], {
                                    value: arr,
                                    enumerable: true,
                                    configurable: true,
                                    writable: true
                                });
                            } else {
                                skuUidStrDicObj[_baseproductuid].push(_uid);
                            }
                        } else {
                            NoCheckedProductUidStr += iData.BaseProductUidStr+','
                            NoCheckedSkuUidStr += iData.UidStr+','
                        }
                    }
				})

				// 清除后面逗号
				NoCheckedProductUidStr = NoCheckedProductUidStr.substring(0, NoCheckedProductUidStr.length - 1)
				NoCheckedSkuUidStr =NoCheckedSkuUidStr.substring(0, NoCheckedSkuUidStr.length - 1)
				var data = {
					BaseProductSkuUid: productSkuDetail.UidStr,
					BaseProductUid: productSkuDetail.BaseProductUidStr,
					SkuCode: productSkuDetail.SkuCode || '',
					IsAllUseWarehouse: false,
					IsUseDistributePrice: false,
					IsUseSupplierFxUser: false,
					IsUseSettlePrice: false,
					IsUseCostPrice: false,
					SkuList: SkuList,
					SettlePrice: productSkuDetail.SettlePrice ||"",
					DistributePrice: productSkuDetail.DistributePrice || "",
					CostPrice: productSkuDetail.CostPrice || "",
					ShortTitle: productSkuDetail.ShortTitle || "",
					SupplierFxUserId: productSkuDetail.SupplierFxUserId || "",
					BaseProductSkuSupplierConfigs: productSkuDetail.BaseProductSkuSupplierConfigs || "",
					SkuUidStrDic: skuUidStrDicObj,
					IsUseSkuShortTitle: true,
					NoCheckedSkuUidStr: NoCheckedSkuUidStr,
					NoCheckedProductUidStr: NoCheckedProductUidStr,
				}
				dialogData.selectFields.forEach((iData, i) => {
					if(i) {
						data[iData.value] = iData.checked;
					}
				})
				commonModule.Ajax({
					type: "POST",
					url: "/BaseProduct/AutoRelationBind",
					loadingMessage: "关联中",
					showMasker: false,
					data: data,
					success: function (rsp) {
						if (rsp.Success|| (rsp.Data &&rsp.Data.Process >= 100)) {
							layer.msg(rsp.Data,{zIndex:100000000000});
							that.onDialogClose();
							that.onClear();
							that.getAutoRelationProcess();

						} else {
							layer.msg(rsp.Message || '失败',{zIndex:100000000000})
						}
					}
				})
			}

			// 同步进度
	        getAutoRelationProcess () {
		        var that = this;
		        var timerCount = that.state.timerCount;
		        var CurrShop = commonModule.CurrShop;

		        ++timerCount;

		        commonModule.Ajax({
			        type: "POST",
			        url: "/BaseProduct/GetAutoRelationProcess",
			        showMasker: false,
			        data: {
				        fxShopId: CurrShop.Id,
			        },
			        success: function (rsp) {
				        if (rsp.Success) {
					        var rspData = rsp.Data;
					        if (rspData == -1 || (rsp.Data && rsp.Data.Process >= 100)) {
						        that.onDialogClose();
						        that.setState({ timerCount: 0 });
						        if (rsp.Data && rsp.Data.Process >= 100) {
							        that.setState({ AutoRelationProces: rspData });
							        that.onDialogAutoRelation();
						        }
					        } else {
						        // 更新进度
						        that.setState({ AutoRelationProces: rspData }, () => {
							        setTimeout(that.getAutoRelationProcess.bind(that), 1500);
						        });
					        }
				        }
			        },
			        error: function () {
				        setTimeout(that.getAutoRelationProcess.bind(that), 1500);
			        }
		        });

		        that.setState({
			        timerCount: timerCount
		        });
	        }
			// 同步弹窗
			onDialogAutoRelation () {
				var that = this;
				var dialogData = this.state.dialogData;
				dialogData.main = layer.open({
					type: 1,
					title: '关联完成', //不显示标题
					content: $('#dialogAutoBindProduct'),
					skin: 'n-skin',
					area: '400px', //宽高
					cancel: function(){
						that.onDialogClose();
 				    },
				});
				dialogData.isSave = true;
				dialogData.type = 'autoRelation';
				this.setState({
					dialogData: dialogData,
				})
			}
			// 中止关联
			onAbortAssociation () {
				var that = this;
				var CurrShop = commonModule.CurrShop;
				var timer = this.state.timer;
				var timerCount = that.state.timerCount;
				commonModule.Ajax({
					type: "POST",
					url: "/BaseProduct/BreakTask",
					loadingMessage: "正在中止",
					showMasker: false,
					data: {
						fxShopId: CurrShop.Id,
					},
					success: function (rsp) {
						if (rsp.Success) {
							layer.msg(rsp.Message ||"已成功中止任务")
							if(timer) {
								clearInterval(timer);
							}

							that.onDialogClose();
							that.setState({timer,timerCount:0,AutoRelationProces:{}})
						} else {
							layer.msg(rsp.Message || '失败')
						}
					}
				})
			}
            // 复制Spu编码
            onCopySpuCode(selectId) {
                commonModule.CopyText('#' + selectId, '复制成功');
            }
            // 快速编辑价格
            onColumnPrice(keys,item,index,type) {
                var checkSkuList = [];
				var dialogData = this.state.dialogData;
				var skuCount = 0;
                let { list } = this.state;
                if (item) {
					dialogData.type = type;
					if (type == 'spu'){
						item.Skus.forEach((jData, j) => {
							jData.isCheck = true;
						})
						checkSkuList.push(item)
						skuCount = item.SkuCount || item.Skus.length;
					}

					if (type == 'sku'){
						skuCount=1;
						var _item = JSON.parse(JSON.stringify(item));
						list.forEach((iData,i)=>{
							if (i == index) {
								iData.isCheck = true
							}
						})
						checkSkuList.push(_item)
					}
                } else {

                    list.forEach((iData, i) => {
                        if (iData.Skus && iData.Skus.length > 0) {
                            iData.Skus.forEach((jData, j) => {
                                if (jData.isCheck) {
                                    checkSkuList.push(jData)
                                    skuCount = jData.SkuCount || jData.Skus.length;
                                }
                            })
                            dialogData.type = 'sku';
                        }else {
                            if (iData.isCheck) {
                                checkSkuList.push(iData);
                            }
                            dialogData.type = 'spu';
                        }
                    })
                }
                var title = '编辑采购价';
                dialogData.isShowLevelSellPriceRule = false;
                switch (keys) {
                    case 'CostPrice':
                        title = '编辑成本价'
                        break;
                    case 'DistributePrice':
                        title = '编辑默认分销价';
                        dialogData.isShowLevelSellPriceRule = true;
                        break;
					case 'ShortTitle':
						title = '编辑简称';
						dialogData.inputType = 'text';
						dialogData.isAutoChecked = true;
                        break;
                    default:
                        title = '编辑采购价';
                        break;
                }
                dialogData.checkedKeys = keys;
                dialogData.skuCount = type ? skuCount :checkSkuList.length;
                if (dialogData.skuCount === 0) {
                    commonModule.w_alert({ type: 3, content: '请勾选需要修改的规格' });
                    return false;
                }
				var that =this;
                dialogData.main = layer.open({
                    type: 1,
                    title: title, // 标题
                    content: $('#dialogPrice'),
                    skin: 'n-skin',
                    area: '600px', // 宽高
                    success:function(){
                    },
					cancel: function(){
						that.onDialogClose();
 				    },
                });
                this.setState({ dialogData,checkSkuList:checkSkuList });
            }
			// 快速编辑价格弹窗 - input改变
			onDialogHandleChange(event,keys) {
				var dialogData = this.state.dialogData;
				var val = event.target.value || '';
				if (val < 0 && dialogData.inputType != 'text') {
					layer.msg("请输入正确的金额");
					return false;
				}
                // 出入库输入框校验
                if (keys == 'inputRemarkValue' && (dialogData.keysName == 'InList' || dialogData.keysName == 'OutList')) {
                    if (val.length > 100) {
                        val = val.slice(0, 100);
                    }
                    dialogData.inputRemarkValue = val;
                }
                if (keys != 'inputRemarkValue') {
                    dialogData.inputValue = val;
                }
				dialogData.isSave = val ? true : false;
				this.setState({ dialogData });
			}
			onDialogCheckbox (isCheck) {
                var dialogData = this.state.dialogData;
                dialogData.isAutoChecked = !dialogData.isAutoChecked;
                this.setState({ dialogData });
            }
            getByteLen(val) {
                var len = 0;
                if (!val) return
                for (var i = 0; i < val.length; i++) {
                    if (val[i].match(/[^\x00-\xff]/ig) != null) //全角
                        len += 2;
                    else
                        len += 1;
                }
                return len;
            }
			// 快速编辑价格弹窗提交
            onDialogSubmit(){
                var that = this;
				var list = this.state.list;
                var checkSkuList = this.state.checkSkuList;
                var dialogData = this.state.dialogData;
                var queryQaram = this.state.queryQaram;
                var platformType = this.state.platformType;
				// 修改简称
				if (dialogData.checkedKeys == 'ShortTitle') {
					if (dialogData.type == 'sku' ) {
						this.onDialogSubmitSku()
					}
					if (dialogData.type == 'spu' ) {
                        @*if (!dialogData.inputValue || this.getByteLen(dialogData.inputValue) > 20) {
                            layer.msg('请输入1-20个字符（10个汉字）')
                            return false;
                        }*@
                        if (!dialogData.inputValue || dialogData.inputValue.length > 512) {
                            layer.msg('请输入1-512个字符')
                            return false;
                        }
						this.onDialogSubmitSpu()
					}
					return false
				}

				var obj = {};
				var NoCheckedProductUidStr = '';
				var NoCheckedSkuUidStr = '';
				if (checkSkuList.length) {
                    if (dialogData.type == 'sku') {

                        checkSkuList.forEach((jData, j) => {
                                var _uid = jData.UidStr;
                                var _baseproductuid = jData.BaseProductUidStr;
                                if (!obj[_baseproductuid]) {
                                    var arr = [];
                                    arr.push(_uid)
                                    Object.defineProperty(obj, [_baseproductuid], {
                                        value: arr,
                                        enumerable: true,
                                        configurable: true,
                                        writable: true
                                    });
                                } else {
                                    obj[_baseproductuid].push(_uid);
                                }
                            for (var i = 0; i < list.length; i++) {
                                    let item = list[i];
                                    if (jData.BaseProductUidStr == item.BaseProductUidStr) {
                                        if (!item.Checked) {
                                            NoCheckedProductUidStr += item.BaseProductUidStr+','
                                            NoCheckedSkuUidStr += item.UidStr+','
                                        }
                                    }
                            }

                        })
                    } else{
                        checkSkuList.forEach((jData, j) => {
                        list.forEach((item, i) => {
                            if (jData.BaseProductUidStr == item.BaseProductUidStr) {
                                var _uid = jData.UidStr;
                                var _baseproductuid = jData.BaseProductUidStr;
                                if (item.isCheck) {
                                    if (!obj[_baseproductuid]) {
                                        var arr = [];
                                        arr.push(_uid)
                                        Object.defineProperty(obj, [_baseproductuid], {
                                            value: arr,
                                            enumerable: true,
                                            configurable: true,
                                            writable: true
                                        });
                                    } else {
                                        obj[_baseproductuid].push(_uid);
                                    }
                                } else {
                                    NoCheckedProductUidStr += item.BaseProductUidStr+','
								    NoCheckedSkuUidStr += item.UidStr+','
                                }
                            }
                        })

					})
                    }
				} else {
					let newList= [];
					list.forEach((item, i) => {
						let flag = false;
						item.Skus.forEach((iData, i) => {
							if (iData.isCheck) {
								flag = true;
							}
						})
						if (flag) {
							newList.push(item);
						}
					})
					newList.forEach((item, i) => {
						item.Skus.forEach((iData, i) => {
							if (iData.isCheck) {
								var _uid = iData.UidStr;
								var _baseproductuid = iData.BaseProductUidStr;
								if (!obj[_baseproductuid]) {
									var arr = [];
									arr.push(_uid)
									Object.defineProperty(obj, [_baseproductuid], {
										value: arr,
										enumerable: true,
										configurable: true,
										writable: true
									});
								} else {
									obj[_baseproductuid].push(_uid);
								}
							} else {
								NoCheckedProductUidStr += iData.BaseProductUidStr+','
								NoCheckedSkuUidStr += iData.UidStr+','
							}
						})
					})

				}
				// 清除后面逗号
				NoCheckedProductUidStr = NoCheckedProductUidStr.substring(0, NoCheckedProductUidStr.length - 1);
				NoCheckedSkuUidStr = NoCheckedSkuUidStr.substring(0, NoCheckedSkuUidStr.length - 1);
				var data = {
					SkuUidStrDic: obj,
					NoCheckedProductUidStr,
					NoCheckedSkuUidStr,
				}
				data[dialogData.checkedKeys] = dialogData.inputValue;
				data['IsUpdatePtSku'+dialogData.checkedKeys] = dialogData.isAutoChecked;
                commonModule.Ajax({
                    url: '/BaseProduct/SaveEdit',
                    data: {model:data},
                    async: true,
                    loading: true,
                    type: 'POST',
                    success: function (rsp) {
                        if (rsp.Success) {

							layer.msg('设置成功', {
                                icon: 1,
                                time: 1000
                            }, function () {
                                //that.LoadList(true);
                            });
							if (checkSkuList.length) {
								that.activeNavsRequest(false,'spu');
                                that.setState({
                                    checkSkuList: checkSkuList,
                                });
							} else {
								that.activeNavsRequest(true);
							}

                            that.onDialogClose();
                        } else {
                            layer.msg(rsp.Message || '失败')
                        }
                    },
                    error: function () {
                        layer.msg("网络错误，请稍后重试");
                    }
                })
            }
			onDialogSubmitSpu () {
				var that = this;
				var list = this.state.list;
                var checkSkuList = this.state.checkSkuList;
                var dialogData = this.state.dialogData;
                var queryQaram = this.state.queryQaram;

				let productSkuDetail = checkSkuList[0];
				var data = {
					ShortTitle: productSkuDetail.ShortTitle || '',
					BaseProductUid: productSkuDetail.UidStr || '',
					BaseProductSkuSupplierConfig: productSkuDetail.BaseProductSkuSupplierConfigs||'',
				}
				data[dialogData.checkedKeys] = dialogData.inputValue;
				data['IsUpdatePtSku'+dialogData.checkedKeys] = dialogData.isAutoChecked;
                commonModule.Ajax({
                    url: '/BaseProduct/SaveEditBaseProduct',
                    data: {model:data},
                    async: true,
                    loading: true,
                    type: 'POST',
                    success: function (rsp) {
                        if (rsp.Success) {
							layer.msg('设置成功', {
                                icon: 1,
                                time: 1000
                            }, function () {
                                //that.LoadList(true);
                            });
							if (checkSkuList.length) {
								that.activeNavsRequest()
							} else {
								that.activeNavsRequest(true);
							}

                            that.onDialogClose();
                        } else {
                            layer.msg(rsp.Message || '失败')
                        }
                    },
                    error: function () {
                        layer.msg("网络错误，请稍后重试");
                    }
                })
			}
			onDialogSubmitSku () {
				var that = this;
				var list = this.state.list;
                var checkSkuList = this.state.checkSkuList;
                var dialogData = this.state.dialogData;
                var queryQaram = this.state.queryQaram;
				let productSkuDetail = {};
				var obj = {};
				checkSkuList.forEach((iData, i) => {
                    productSkuDetail = iData;
                    var _uid = iData.UidStr;
                    var _baseproductuid = iData.BaseProductUidStr;
                    if (!obj[_baseproductuid]) {
                        var arr = [];
                        arr.push(_uid)
                        Object.defineProperty(obj, [_baseproductuid], {
                            value: arr,
                            enumerable: true,
                            configurable: true,
                            writable: true
                        });
                    } else {
                        obj[_baseproductuid].push(_uid);
                    }
				})

				var data = {
					SkuUidStrDic: obj,
					ShortTitle: productSkuDetail.ShortTitle || '',
					BaseProductSkuSupplierConfig: productSkuDetail.BaseProductSkuSupplierConfigs||'',
					CostPrice: productSkuDetail.CostPrice||'',
					DistributePrice: productSkuDetail.DistributePrice||'',
					SettlePrice: productSkuDetail.SettlePrice||'',
				}
				data[dialogData.checkedKeys] = dialogData.inputValue;
				data['IsUpdatePtSku'+dialogData.checkedKeys] = dialogData.isAutoChecked;
                commonModule.Ajax({
                    url: '/BaseProduct/SaveEditBaseProductSku',
                    data: {model:data},
                    async: true,
                    loading: true,
                    type: 'POST',
                    success: function (rsp) {
                        if (rsp.Success) {
							layer.msg('设置成功', {
                                icon: 1,
                                time: 1000
                            }, function () {
                                //that.LoadList(true);
                            });
							if (checkSkuList.length) {
								that.activeNavsRequest()
							} else {
								that.activeNavsRequest(true);
							}

                            that.onDialogClose();
                        } else {
                            layer.msg(rsp.Message || '失败')
                        }
                    },
                    error: function () {
                        layer.msg("网络错误，请稍后重试");
                    }
                })
			}
            // 清空选中状态
			onClear () {
				var list = this.state.list;
				list.length && list.forEach((item, i) => {
					item.isCheck = false;
                    if (item.Skus && item.Skus.length) {
                        item.Skus.forEach((iData, i) => {
                            iData.isCheck = false;
                        })
                    }
					if (item.WareHouseSkus && item.WareHouseSkus.length) {
                        item.WareHouseSkus.forEach((iData, i) => {
                            iData.isCheck = false;
                        })
                    }
				})
				this.setState({
					list: list,
                    checkSkuList: [],
					allCheckSku: false,
                    partialSelect: false,
				})
			}
            // 弹窗关闭
			onDialogClose() {
				var dialogData = this.state.dialogData;
				var list = this.state.list;
				var fileInfo = this.state.fileInfo;
				var AutoRelationProces = this.state.AutoRelationProces;
				if(dialogData.main) {
					dialogData.main = layer.close(dialogData.main);
				}
				if (dialogData.type == 'autoRelation') {
					AutoRelationProces = {}
				}
				dialogData.inputValue = '';
                dialogData.supply = '';
                dialogData.checkedKeys = '';
                dialogData.skuCount = 0;
                dialogData.isSave = false;
				dialogData.type = '';
				dialogData.inputType = '';
                dialogData.keysName = '';
                dialogData.inputRemarkValue = '';
                dialogData.selectFields = [
                    {
                        id: 0,
                        name: '全选',
                        value: 'all',
                        checked: false,
                    },
                    {
                        id: 1,
                        name: '采购价',
                        value: 'IsUseSettlePrice',
                        tips: '使用最新采购价对厂家进行财务结算。',
                        checked: false,
                    },
                    {
                        id: 2,
                        name: '成本价',
                        value: 'IsUseCostPrice',
                        tips: '使用最新成本价用于自营店铺统计总成本。',
                        checked: false,
                    },
                    {
                        id: 3,
                        name: '分销价',
                        value: 'IsUseDistributePrice',
                        tips: '使用最新分销价对下游分销品进行财务结算。',
                        checked: false,
                    },
                    {
                        id: 5,
                        name: '供货方式',
                        value: 'IsUseSupplierFxUser',
                        tips: '使用最新供货方式对所有关联分销品的订单推送。',
                        checked: false,
                    },
                    {
                        id: 6,
                        name: '简称',
                        value: 'IsUseShortName',
                        tips: '默认已关联所有的分销品使用统一简称。',
                        checked: true,
                        disabled: true,
                        absoluteChecked: true,
                    },
                ],
				dialogData.selectFields && dialogData.selectFields.forEach((iData, i) => {
					iData.isCheck = false;
				})
				if (fileInfo&&fileInfo.name) {
					fileInfo = null
				}
                /* if (this.state.activeNavsName == 'productStatus') {
                    list.forEach((item, i) => {
                        item.isCheck = false;
                    })
                    this.setState({
                        allCheckSku: false,
                        partialSelect: false,
                        checkSkuList: []

                    })
                } */
               
                list.forEach((item, i) => {
                    item.isCheck = false;
                    if (item.isAllCheckSubject) {
                        item.isAllCheckSubject = false;
                    }
                    if (item.isPartCheckSubject) {
                        item.isPartCheckSubject = false;
                    }
                    item.ChangeCount = 0;
                })
				this.setState({ dialogData,list,fileInfo,AutoRelationProces, allCheckSku: false, partialSelect: false, checkSkuList:[] });
			}
            // 修改导航状态 - 视角切换
            changeStatusNavs(name,notEmptyName) {
                const that =this;
                let baseProductNavs = this.state.baseProductNavs;
                let activeNavsName = '';
                let activeNavsTitle = '';
                baseProductNavs.forEach((item) => {
                    item.isActive = false;
                    if (item.name == name) {
                        item.isActive = true;
                        activeNavsName = item.name;
                        activeNavsTitle = item.title;
                    }
                })
                // if (name == 'productStatus') {
                let searchData = this.state.searchData;
                searchData.forEach((item, i) => {
                    if (notEmptyName && notEmptyName.length > 0) {
                        notEmptyName.some(jtem => jtem == item.name) ? item.value = item.value : item.value = "";
                    } else {
                        item.value = "";
                    }
                });
                // }
                this.setState({ baseProductNavs, activeNavsName, activeNavsTitle, list:[], checkSkuList:[] }, () => {
                    this.renderSearchData();
                });
            }
            // 查询规格
            onColumnSku(SpuCode) {

                const { searchData } = this.state;

                const updatedSearchData = searchData.map(item => {
                    if (item.name === 'SpuCode') {
                        return { ...item, value: SpuCode };
                    }
                    return item;
                });

                this.setState({ searchData: updatedSearchData }, () => {
                    this.changeStatusNavs('skuStatus',['SpuCode']);
                });

			}
            loadData(isPaging, type) {
                let {queryQaram, isLoading, activeNavsName, checkSkuList} = this.state;
				let that = this;
				if (isPaging) {
                    queryQaram.PageIndex = 1;
					this.setState({hideNoDataStatus:true,allCheckSku:false,partialSelect:false});
                }
				commonModule.Ajax({
					url: '/api/BaseProduct/GetOnlyBaseProducts',
					loadingMessage: "加载中",
					showMasker: false,
				    loading: true,
					async: true,
					data: queryQaram,
					success: function (rsp) {
						if (rsp.Success) {
							var list = rsp.Data.Rows || [];
                            // 引导蒙层
                            let isShowGuideLayer = localStorage.getItem('isShowGuideLayer') || '0';
                            if ( isShowGuideLayer == '0') {
                                that.initGuideLayer();
                            }
                            list.forEach(function (item,i) {
								item.isCheck = false;
								item.Images = commonModule.newTransformImgSrc(item.Images);
								item.MainImageUrl = commonModule.newTransformImgSrc(item.MainImageUrl);
								// item.MainImageUrl = 'http://cbu01.alicdn.com/img/ibank/O1CN01Tqqzms1Bs2xET0VN8_!!0-0-cib.80x80.jpg';
                                if (item.Skus && item.Skus.length > 0) {
									item.Skus.forEach(function (jData, j) {
										jData.isCheck = true;
									})
								}
                                if (type == 'spu') {
                                    for (let j = 0; j < checkSkuList.length; j++) {
                                        let iData = checkSkuList[j];
                                        if (item.Id == iData.Id) {
                                            item.isCheck = true;
                                            list[i] = item;
                                        }
                                    }
                                }
                            })
                            if (type != 'spu') {
                                that.onClear();
                            }
                            that.setState({ list: list })
							layui.laypage.render({
								elem: 'paging',
								count: rsp.Data.Total,
								limit: queryQaram.PageSize,
								curr: queryQaram.PageIndex,
								limits: [50, 100, 200, 300, 400, 500],
								layout: ['count', 'prev', 'next', 'limit', 'skip'],
                                theme: 'n-page',
								jump: function (obj, first) {
									if (!first) {
										queryQaram.PageIndex = obj.curr;
										queryQaram.PageSize = obj.limit;
										that.setState({ queryQaram: queryQaram }, function () {
                                            that.onClear();
											that.activeNavsRequest(false);
										})
									}
								}
							});

                            if (rsp.Data.Total <= queryQaram.PageSize) {
                                $('#paging .layui-laypage-skip').hide();
                            } else {
                                $('#paging .layui-laypage-skip').show();
                            }

						} else {
							layer.msg(rsp.Message || '失败');
						}
					}
				})
            }
            // 规格列表
            loadSkuData(isPaging,type) {
                let {queryQaram,isLoading,checkSkuList} = this.state;
				let that = this;
				if (isPaging) {
                    queryQaram.PageIndex = 1;
					this.setState({hideNoDataStatus:true,allCheckSku:false,partialSelect:false});
                }
				commonModule.Ajax({
					url: '/api/BaseProduct/GetOnlySkus',
					loadingMessage: "加载中...",
					showMasker: false,
				    loading: true,
					async: true,
					data: queryQaram,
					success: function (rsp) {
						if (rsp.Success) {
							let list = rsp.Data.Rows || [];
                            list.forEach(function (item,i) {
								item.isCheck = false;
								item.ImageUrl = commonModule.newTransformImgSrc(item.ImageUrl);
                                item.isShowInput = false;
                                item.isAllCheckSubject = false;
                                item.isPartCheckSubject = false;
                                item.checkedSkuList = [];
                                item.AttributeValue = that.onColumnSkuFormat(item.Attributes)
                                if (item.Skus && item.Skus.length > 0) {
									item.Skus.forEach(function (jData, j) {
										jData.isCheck = true;
									})
								}
                                if (type == 'spu') {
                                    for (let j = 0; j < checkSkuList.length; j++) {
                                        let iData = checkSkuList[j];
                                        if (item.Id == iData.Id) {
                                            item.isCheck = true;
                                            list[i] = item;
                                        }
                                    }
                                }
                                if (item.ChildSku && item.ChildSku.length > 0) {
                                    item.ChildSku.forEach((jData, j) => {
                                        if (jData.ImageUrl) {
                                            jData.ImageUrl = commonModule.newTransformImgSrc(jData.ImageUrl);
                                        }
                                    })
                                }
                            })
                            if (type != 'spu') {
                                that.onClear();
                            }
                            // 使用 reduce 来组合相同 SpuCode 的项
                            const CombinedSkuData = Object.values(list.reduce((acc, item) => {
                                // 如果这个 SpuCode 已经存在，直接推入到对应的 SkuItems 数组中
                                if (!acc[item.SpuCode]) {
                                    acc[item.SpuCode] = {
                                        IsCombineSpu: item.IsCombineSpu,
                                        ProSubject: item.ProSubject,
                                        SpuCode: item.SpuCode,
                                        isAllCheckSubject: false, // 是否全部选中
                                        isPartCheckSubject: false, // 是否部分选中  组合=====
                                        SkuItems: [],
                                        checkedSkuCount: 0,
                                    };
                                }
                                acc[item.SpuCode].SkuItems.push(item);
                                return acc;
                            }, {}));
                            //console.log("规格视角列表数据：", CombinedSkuData);
                            that.setState({ list: list });
							layui.laypage.render({
								elem: 'paging',
								count: rsp.Data.Total,
								limit: queryQaram.PageSize,
								curr: queryQaram.PageIndex,
								limits: [50, 100, 200, 300, 400, 500],
								layout: ['count', 'prev', 'next', 'limit', 'skip'],
                                theme: 'n-page',
								jump: function (obj, first) {
									if (!first) {
										queryQaram.PageIndex = obj.curr;
										queryQaram.PageSize = obj.limit;
										that.setState({ queryQaram: queryQaram }, function () {
                                            that.onClear();
											that.activeNavsRequest(false);
										})
									}
								}
							});
                            if (rsp.Data.Total <= queryQaram.PageSize) {
                                $('#paging .layui-laypage-skip').hide();
                            } else {
                                $('#paging .layui-laypage-skip').show();
                            }
						} else {
							layer.msg(rsp.Message || '失败');
						}
					}
				})
            }
            // 获取库存列表
            loadStockData(isPaging,type) {
                // this.LoadList(isPaging)
                // return
                let {queryQaram,isLoading,checkSkuList} = this.state;
				var that = this;

				if (isPaging) {
                    queryQaram.PageIndex = 1;
					this.setState({hideNoDataStatus:true,allCheckSku:false,partialSelect:false});
                }
                queryQaram.skuName = queryQaram.SkuSubject;
                queryQaram.skuCargoNumber = queryQaram.SkuCode;
				commonModule.Ajax({
					url: '/StockControl/LoadProductSkuList',
					loadingMessage: "加载中",
					showMasker: false,
				    loading: true,
					async: true,
					data: queryQaram,
					success: function (rsp) {
						if (rsp.Success) {
							//rsp.Data.Rows ||
							var list = rsp.Data.List || [];
                            list.forEach(function (item,i) {
								item.isCheck = false;
                                item.isAllCheck = false;
                                item.isPartCheck = false;
								item.ImageUrl = commonModule.newTransformImgSrc(item.ImageUrl);
                                if (item.WareHouseSkus && item.WareHouseSkus.length > 0) {
									item.WareHouseSkus.forEach(function (jData, j) {
                                        jData.isAllCheckEditOutList = false;
                                        jData.isAllCheckEditInList = false;
										jData.isCheck = false;
                                        jData.isMore = false;
                                        jData.ImageUrl = commonModule.newTransformImgSrc(jData.ImageUrl);
                                        jData.isShowInput = false;
                                        jData.isShowOutListInput = false; // 出库输入框
                                        jData.isShowInListInput = false; // 入库输入框
                                        jData.isAddRemark = false;  // 是否备注
                                        jData.ChangeCount = '';
                                        if (jData.ChildSkus && jData.ChildSkus.length > 0) {
											jData.ChildSkus.forEach(function(kData, k){
                                                kData.ImageUrl = commonModule.newTransformImgSrc(kData.ImageUrl);
                                                kData.SkuCargoNumber = jData.SkuCargoNumber;
                                            });
                                        }
									});
								}
                                if (type == 'spu') {
                                    for (let j = 0; j < checkSkuList.length; j++) {
                                        let iData = checkSkuList[j];
                                        if (iData.WareHouseSkus && iData.WareHouseSkus.length > 0) {
                                            iData.WareHouseSkus.forEach(function (jData, j) {
                                                if (item.Id == jData.Id) {
                                                    jData.isCheck = true;
                                                    list[i] = jData;
                                                }
                                            })
                                        }

                                    }
                                }
                                if (item.ChildSku && item.ChildSku.length > 0) {
                                    item.ChildSku.forEach((jData, j) => {
                                        if (jData.ImageUrl) {
                                            jData.ImageUrl = commonModule.newTransformImgSrc(jData.ImageUrl);
                                        }
                                    })
                                }
                            })
                             if (type != 'spu') {
                                that.onClear();
                            }
                            that.setState({ list: list })
							layui.laypage.render({
								elem: 'paging',
								count: rsp.Data.Total,
								limit: queryQaram.PageSize,
								curr: queryQaram.PageIndex,
								limits: [50, 100, 200, 300, 400, 500],
								layout: ['count', 'prev', 'next', 'limit', 'skip'],
                                theme: 'n-page',
								jump: function (obj, first) {
									if (!first) {
										queryQaram.PageIndex = obj.curr;
										queryQaram.PageSize = obj.limit;
										that.setState({ queryQaram: queryQaram }, function () {
                                            that.onClear();
											that.activeNavsRequest(false);
										})
									}
								}
							});
                            if (rsp.Data.Total <= queryQaram.PageSize) {
                                $('#paging .layui-laypage-skip').hide();
                            } else {
                                $('#paging .layui-laypage-skip').show();
                            }
						} else {
							layer.msg(rsp.Message || '失败');
						}
					}
				})
            }
            // 获取库存列表
            refreshLoadStockData(itemId) {
                // this.LoadList(isPaging)
                // return
                let {queryQaram,isLoading,list} = this.state;
				var that = this;
                queryQaram.skuName = queryQaram.SkuSubject;
                queryQaram.skuCargoNumber = queryQaram.SkuCode;
				commonModule.Ajax({
					url: '/StockControl/LoadProductSkuList',
					loadingMessage: "加载中",
					showMasker: false,
				    loading: true,
					async: true,
					data: queryQaram,
					success: function (rsp) {
						if (rsp.Success) {
							//rsp.Data.Rows ||
							var _list = rsp.Data.List || [];
                            var itemData = null;
                            var isBreak = false;
                            for (let i = 0; i < _list.length; i++) {
                                let item = _list[i];
                                if (item.WareHouseSkus && item.WareHouseSkus.length > 0) {
                                    item.WareHouseSkus.forEach(function (jData, j) {
                                        if (jData.Id === itemId) {
                                            jData.isCheck = false;
                                            jData.isMore = false;
                                            jData.ImageUrl = commonModule.newTransformImgSrc(jData.ImageUrl);
                                            jData.isShowInput = false;
                                            jData.isShowOutListInput = false; // 出库输入框
                                            jData.isShowInListInput = false; // 入库输入框
                                            jData.isAddRemark = false;  // 是否备注
                                            jData.ChangeCount = '';
                                            if (jData.ChildSkus && jData.ChildSkus.length > 0) {
                                                jData.ChildSkus.forEach(function(kData, k){
                                                    kData.ImageUrl = commonModule.newTransformImgSrc(kData.ImageUrl);
                                                    kData.SkuCargoNumber = jData.SkuCargoNumber;
                                                      //kData.SkuProperty = kData.SkuProperty.replace(/;无规格$/, "");
                                                });
                                            }
                                            itemData = jData;
                                            isBreak = true;
                                        }
                                    });
                                }
                                if (isBreak) break;
                            }
                            for (let i = 0; i < list.length; i++) {
                                let item = list[i];
                                if (item.WareHouseSkus && item.WareHouseSkus.length > 0) {
                                    item.WareHouseSkus.forEach(function (jData, j) {
                                        if (jData.Id === itemId) {
                                            item.WareHouseSkus[j] = itemData;
                                        }
                                    });
                                }
                            }
                            that.setState({
                                list: list
                            })
                        }
                    }
                })
            }

            changeSeach(e, name) {

                let searchData = this.state.searchData;
                let value = e.target.value.trim();

                searchData.forEach((item, i) => {
                    if (item.name == name) {
                        item.value = value;
                    }
                })
                this.setState({ searchData });
            }

            searchLoadList() {
                let { searchData,queryQaram } = this.state;
                const params = this.getSearchParams();
                this.setState({ queryQaram: params }, function () {
                    this.activeNavsRequest(true);
                })
            }
            // activeNavsName 选中的对应请求
            activeNavsRequest(isTrue,type){
                let activeNavsName = this.state.activeNavsName;
                $('.newOperation').hide();
                switch (activeNavsName) {
                    case 'productStatus'://商品视角
                        this.loadData(isTrue,type);
                        break;
                    case 'skuStatus'://规格视角
                        this.loadSkuData(isTrue,type);
                        break;
                    case 'stockStatus': //库存视角
                        this.loadStockData(isTrue,type);
                        $('.newOperation').show();
                        break;
                    default:
                        this.loadData(isTrue,type);
                        break;
                }
            }
            // 获取对应的查询参数
            getSearchParams(){
                let { searchData,activeNavsName } = this.state;
                let isGoSkuStatus = false;
                const selectedItems = searchData
                    .filter(item => item.isSelect) // 筛选 isSelect 为 true 的项
                    .reduce((acc, item) => {
                        if ((item.name === "SkuSubject" || item.name === "SkuShortTitle" || item.name === "SkuCode") && item.value !== "") {
                            isGoSkuStatus = true;
                        }
                        acc[item.name] = item.value; // 赋值
                        return acc;
                    }, {});
                //  - 用户在“规格名称”、“规格简称”或“SKU编码”输入框中输入内容，点击“搜索”按钮，系统进行模糊匹配搜索。
                // - 页面自动跳转到“规格视角”。
                if (isGoSkuStatus && activeNavsName === 'productStatus') {
                    for (const key in selectedItems) {
                        const value = selectedItems[key];
                        if (!(key == 'SkuSubject' || key == 'SkuShortTitle' || key == 'SkuCode')) {
                            selectedItems[key] = ""
                        }
                    }
                    this.changeStatusNavs('skuStatus',['SkuSubject','SkuShortTitle','SkuCode']);
                }
                return { PageIndex: 1, PageSize: 50, ...selectedItems };

            }

            reset(notEmptyName) {

                let searchData = this.state.searchData;
                searchData.forEach((item, i) => {
                    if (!notEmptyName && item.name != notEmptyName) {
                        item.value = "";
                    }
                });
                this.renderSearchData();

            }

			cancelDrawerModul(type){

				this.setState({[type]:false});
			}

            submitDrawer(){

            }

            // 打开操作日志弹框
            showOperateLogo(){
                let _this = this;
                // this.getBaseProductRelLogs(false);
				this.setState({ isProductLogoDrawer: true },function(){
                    $('#changePartner>li:eq(0)').trigger("click");
                    _this.oninitIframeHeight()
                });
            }
            // 等级分销价
            showLevelSellPriceDialog() {
                const that = this;
                that.onGetBaseProductSetting();
                // 保存弹窗的 index
                const index = layer.open({
                    type: 1,
                    title: '等级分销价生效规则设置', // 标题
                    content: $('#dialogLevelSellRules'),
                    area: ['720px', 'auto'], // 宽高
                    skin: 'n-skin',
                    success: function () {},
                });
                that.setState({ levelSellPriceLayerIndex: index });
            }
            // 等级分销价规则设置-保存
            savePriceRuleSetting() {
                const that = this;
				const dialogData = this.state.dialogData;
				const data = {
					OrderCombine: dialogData.isSwitch,
					PrintBeforeShippingWarning: dialogData.PrintBeforeShippingWarning || false,
					StockPreparationReminder: dialogData.StockPreparationReminder || false,
					AbnormalOrderTagReminder: dialogData.AbnormalOrderTagReminder || false,
					ExportBeforeReminder: dialogData.ExportBeforeReminder || false,
                    DistributePriceSetting: dialogData.DistributePriceSetting
				}
				commonModule.SaveCommonSetting("/ErpWeb/FenDan/BaseProductSetting", JSON.stringify(data), function (rsp) {
					if (rsp.Success) {
                        commonModule.w_alert({ type: 4, content: '设置成功' });
						that.closePriceRuleSetting();
						that.onGetBaseProductSetting();
					}
				});
            }
            // 等级分销价规则设置-取消
            closePriceRuleSetting() {
                const { levelSellPriceLayerIndex } = this.state;
                if (levelSellPriceLayerIndex) {
                    layer.close(levelSellPriceLayerIndex); // 关闭弹窗
                    this.setState({ levelSellPriceLayerIndex: null }); // 重置 index
                }
            }
            // 前往设置等级分销价规则
            linkToDistributeSetPage() {
                window.open(commonModule.rewriteUrl("/System/DistributeSet?type=levelSet"), '_blank');
            }
            // 获取操作日志
            getBaseProductRelLogs(isTrue,isSearch){
                let that = this;
                let { queryLogQarams } = this.state;
                let reqModel = {};

				if(isSearch){
					queryLogQarams.PageIndex = 1;
				}
                queryLogQarams.SkuCode = $('input[name=logoSkuCode]').val() || '';
                queryLogQarams.SkuId = $('input[name=logoSkuUid]').val() || '';
                queryLogQarams.SpuCode = $('input[name=logoSpuCode]').val() || '';

				commonModule.Ajax({
					url: '/api/BaseProduct/GetBaseProductRelLogs',
					loadingMessage: "加载中",
					showMasker: false,
					data: queryLogQarams,
					success: function (rsp) {
						if (rsp.Success) {
							if(rsp.Data){
								var Rows = rsp.Data.Rows || [];
								that.setState({ productRelLogList: Rows });
								var total = rsp.Data.Total;
								if(!isTrue){
									layui.laypage.render({
											elem: 'logoTable_paging',
											count: total,
											limit: queryLogQarams.PageSize,
											curr: queryLogQarams.PageIndex,
											limits: [50, 100, 200, 300, 400, 500],
											layout: ['count', 'prev', 'next', 'limit', 'skip'],
                                            theme: 'n-page',
											jump: function (obj, first) {
													if (!first) {
                                                        let queryQarams = {
                                                            PageIndex: obj.curr,
                                                            PageSize: obj.limit
                                                        }
														that.setState({ queryLogQarams: queryQarams }, function () {
															that.getBaseProductRelLogs(true,false);
														})
												}
											}
									});
								}
							}

						} else {
							layer.msg(rsp.Message || '失败')
						}
					}
				})
            }

            // 重置查询参数
            onReset() {
                let { queryLogQarams } = this.state;
                queryLogQarams.PageIndex = 1;
                $('input[name=logoSkuCode]').val('');
                $('input[name=logoSkuUid]').val('');
                $('input[name=logoSpuCode]').val('');
                this.setState({ queryLogQarams });
            }

            // 打开组合货品弹框
            showCombinationProductDialog() {
                this.setState({ dialogVisible: true })
            }
            // 关闭组合货品弹框回调
            handleReceiveData(visible,type,data) {
                var that = this;
                this.setState({ dialogVisible: visible,ChildSku:[] },function(){
                    if(type == 'save'){
                        this.activeNavsRequest(true);
                        if (data) {
                            localStorage.setItem('CreateModelByCombine',JSON.stringify(data));
                        }
                        var status="create";
                        var url=commonModule.rewriteUrlToMainDomain('/BaseProduct/CreateBaseProduct?CreateFrom='+status+'&changeTime='+new Date().getTime());
                        that.createOrEdmitProduct(status,url,"创建组合商品")
                        //that.createOrEdmitProduct(status,url);
                    }
                    if (type == 'refresh'){
                        this.activeNavsRequest(true);
                    }
                })
            }
            // 初始化蒙层
            initGuideLayer(){
                var domMockId = document.getElementById('baseProductNavsDom');
			    var { top, left,right,width, height } = domMockId.getBoundingClientRect();
                const that = this;
                // 兼容旧新手指引
                if (localStorage.getItem('isShowGuideLayer') != null) {
                    that.setState({
                        guideLayerData: {...that.state.guideLayerData, right:right + 68, top: top, isShow: true},
                        guideNavBar: {...that.state.guideNavBar, top, left, right, width: width - 16, height}
                    });
                    return
                }
                commonModule.LoadCommonSetting('baseProduct/guideLayer', true, function (rsp) { //是否展示此广告
                    let isShow = rsp.Data == '1' ? false : true;
                    that.setState({
                        guideLayerData: {...that.state.guideLayerData, right:right + 68, top: top, isShow: isShow},
                        guideNavBar: {...that.state.guideNavBar, top, left, right, width: width - 16, height}
                    });
                })
                
                //console.log("domMockId",domMockId.getBoundingClientRect());
            }
            // 引导蒙层提示文案
			onGuideLayer(step) {

				let guideLayerData = this.state.guideLayerData;
				if (step == 2) {
					guideLayerData.title = '规格视角';
					guideLayerData.text = '支持批量改价、编码关联同款、设置供货方式、查看同款明细等。';
				}
				if (step == 3) {
					guideLayerData.title = '库存视角';
					guideLayerData.text = '支持编辑入库、出库、设置库存预警等。';
				}
                guideLayerData.step = step;

                if(step > guideLayerData.total){
                    guideLayerData.isShow = false;
                    localStorage.setItem('isShowGuideLayer', '1');
                    commonModule.SaveCommonSetting('baseProduct/guideLayer', "1", function (rsp) {
                    }); //关闭
                }

				this.setState({
					guideLayerData: guideLayerData
				})
			}
            initGuideClearFieldsLayer() {
                const that = this;
                var domMockId = document.getElementById('guideClearFieldsDom');
                var { top, left,right,width, height } = domMockId.getBoundingClientRect();
                // console.log(top, left)
                commonModule.LoadCommonSetting('baseProduct/guideClearFieldsLayer', true, function (rsp) { //是否展示此广告
                    let isShow = rsp.Data == '1' ? false : true;
                    that.setState({
                        guideClearFields: {...that.state.guideClearFields, right:right + 68, top: top, isShow: isShow,step: 1},
                        guideNavBar: {...that.state.guideNavBar, top, left: left, right, width: 120, height: 48,}
                    });
                })
            }
            // 引导蒙层提示文案
			onGuideClearFieldsLayer(step) {

				let guideClearFields = this.state.guideClearFields;
				
                guideClearFields.step = 1;

                if(step > guideClearFields.total){
                    guideClearFields.isShow = false;
                    commonModule.SaveCommonSetting('baseProduct/guideClearFieldsLayer', "1", function (rsp) {
                    }); //关闭
                }

				this.setState({
					guideClearFields: guideClearFields
				})
			}
            // 供货方式
            onSupplierFormat(item,index,type) {
                type = type || 'spu';
				//  /// 供货方式 0: 未设置， 1：自营、2：多厂家、3:自营+厂家供货、4：自营+多厂家
				var htmlDom;
				if (item.SupplyMethod == 0) {
					htmlDom = <span className="n-tarTxt n-tarTxt05 hover" onClick={()=>this.onColumnSpuSupply(item, index, type)}><span className="flex">未设置<i className="iconfont icon-a-edit1x common-product-icon common-product-icon2"></i></span></span>
				} else if (item.SupplyMethod == 1) {
					htmlDom = <span className="n-tarTxt n-tarTxt05 hover" onClick={()=>this.onColumnSpuSupply(item, index, type)}><span className="flex">自营<i className="iconfont icon-a-edit1x common-product-icon common-product-icon2"></i></span></span>
				} else if (item.SupplyMethod == 2) {
		            if (item.SupplierName){
						htmlDom = <div onClick={()=>this.onColumnSpuSupply(item, index, type)} className="">
						 <span className="n-tarTxt n-tarTxt04 hover"><span className="flex">厂家<i className="iconfont icon-a-edit1x common-product-icon common-product-icon2"></i></span></span>
                         <div className="grid" style={{marginTop: '2px'}}>
                            <span className="c06 fz12 ellipsis grid-column" title={item.SupplierName}>{item.SupplierName}</span>
                         </div>

						</div>
					} else{
						htmlDom = <div onClick={()=>this.onColumnSpuSupply(item, index, type)} className="">
						 <span className="n-tarTxt n-tarTxt04 hover ellipsis"><span className="flex">厂家<i className="iconfont icon-a-edit1x common-product-icon common-product-icon2"></i></span></span>
						</div>
					}
				} else {
					if (item.SupplierName){
						htmlDom = <div onClick={()=>this.onColumnSpuSupply(item, index, type)}><span className="n-tarTxt n-tarTxt04 hover" ><span className="flex">厂家<i className="iconfont icon-a-edit1x common-product-icon common-product-icon2"></i></span></span>
						<span className="n-tarTxt n-tarTxt05 hover"><span className="flex">自营<i className="iconfont icon-a-edit1x common-product-icon common-product-icon2"></i></span></span>
                        <div className="c06 fz12" style={{marginTop: '2px'}} title={item.SupplierName}>{item.SupplierName}</div>

						</div>
					} else{
						htmlDom = <div onClick={()=>this.onColumnSpuSupply(item, index, type)}><span className="n-tarTxt n-tarTxt04 hover" ><span className="flex">厂家<i className="iconfont icon-a-edit1x common-product-icon common-product-icon2"></i></span></span>
						<span className="n-tarTxt n-tarTxt05 hover"><span className="flex">自营<i className="iconfont icon-a-edit1x common-product-icon common-product-icon2"></i></span></span>
						</div>
					}
				}
				return htmlDom
			}
            // 平台切换
			onTabChange(item,type) {
				let tabList = this.state.tabList;
				if (item.value != 'Site') {
					this.onCloudPlatform(item.value,type)
				} else {
                    this.getBaseProductRelLogs(false,true);
                }
                this.setState({ platformType: item.value, platformArea: []})
			}
			// 平台分区切换
			onPlatformAreaChange (item,type){
				let platformArea = this.state.platformArea;
				let platformType = this.state.platformType;
				let queryLogQarams = {};
                queryLogQarams.SkuCode = $('input[name=logoSkuCode]').val() || '';
                queryLogQarams.SkuId = $('input[name=logoSkuUid]').val() || '';
                queryLogQarams.SpuCode = $('input[name=logoSpuCode]').val() || '';

				platformArea.forEach((iData, i) => {
					if (iData.DbName == item.DbName) {
						iData.checked = true;
					} else {
						iData.checked = false;
					}
				})
				let src = '';
				let tempsrc = "/BaseProduct/BaseProductOperationLog?token=@(Request.QueryString["token"])&pt=" + platformType +"&dbname="+item.DbName+"&SkuCode="+queryLogQarams.SkuCode+"&SkuId="+queryLogQarams.SkuId+"&SpuCode="+queryLogQarams.SpuCode;
				platformType = platformType.toLowerCase() || "";
				if (platformType == "pinduoduo") {
					src = "@DianGuanJiaApp.Utility.CustomerConfig.PinduoduoFenFaSystemUrl" + tempsrc;
				} else {
					src = tempsrc
				}
				//console.log("platformType==",platformType,src)
				this.setState({ platformArea: platformArea, ifarmSrc: src })
			}
			// 切换平台
			onCloudPlatform(pt,type) {
				var bindSkuCode = $('#bindSkuCode-hiddeninput').val();

				pt = pt.toLowerCase() || "";
				var src = '';
				var _dbname = "@(Request.QueryString["dbname"])" || '';
                let queryLogQarams = {};
                queryLogQarams.SkuCode = $('input[name=logoSkuCode]').val() || '';
                queryLogQarams.SkuId = $('input[name=logoSkuUid]').val() || '';
                queryLogQarams.SpuCode = $('input[name=logoSpuCode]').val() || '';

				var tempsrc = "/BaseProduct/BaseProductOperationLog?token=@(Request.QueryString["token"])&pt=" + pt +"&SkuCode="+queryLogQarams.SkuCode+"&SkuId="+queryLogQarams.SkuId+"&SpuCode="+queryLogQarams.SpuCode;

				if ( pt == "kuaituantuan") {
					src = "@DianGuanJiaApp.Utility.CustomerConfig.PinduoduoFenFaSystemUrl" + tempsrc+"&dbname=@(Request.QueryString["dbname"])";
				}
				else if (pt == "jingdong") {
					src = "@DianGuanJiaApp.Utility.CustomerConfig.JingdongFenFaSystemUrl" + tempsrc+"&dbname=@(Request.QueryString["dbname"])";
				}
				else if (pt == "toutiao") {
					src = "@DianGuanJiaApp.Utility.CustomerConfig.ToutiaoFenFaSystemUrl" + tempsrc+"&dbname=@(Request.QueryString["dbname"])";
				}
				else {
					src = tempsrc;
				}
				if (pt == "alibaba" || pt == "pinduoduo") {
					//显示或隐藏分区
					if (pt == "pinduoduo") {
						src = "@DianGuanJiaApp.Utility.CustomerConfig.PinduoduoFenFaSystemUrl" + tempsrc;
					}
                    this.getDbAreas(type, src,pt,_dbname);

				} else {
                    this.setState({
                        platformArea: [],
                        ifarmSrc: src
                    })
				}

			}
			//获取数据分区信息
			getDbAreas(type, defaultSrc,pt,_dbname) {
				var platform = pt || "alibaba";
				var that = this;
				commonModule.Ajax({
					type: "POST",
					url: "/Common/GetDbAreas",
					data: { cloudPlatformType: platform },
					success: function (rsp) {
						if ( rsp == undefined || rsp == null || rsp.Data == undefined || !rsp.Success ) {
                            that.setState({ platformArea: [], ifarmSrc: defaultSrc + "&dbname="+_dbname })
							return;
						}
						var dbareaobj = JSON.parse(rsp.Data.DbAreas);
						if (dbareaobj && dbareaobj.length > 1) {
							var isDefault = true;
							dbareaobj.forEach(function (item, index) {
								if (_dbname && item.DbName == _dbname) {
									item.checked = true;
									isDefault = false;
								} else {
									item.checked = false;
								}
							})
							if (isDefault) {
								dbareaobj[0].checked = true;
								_dbname = _dbname || dbareaobj[0].DbName;
							}
                            that.setState({ platformArea: dbareaobj, ifarmSrc: defaultSrc + "&dbname="+_dbname })
						}
						else {

							that.setState({ platformArea: [], ifarmSrc: defaultSrc + "&dbname="+_dbname })
						}
						that.oninitIframeHeight();

					},
				});
			}
			// 初始化iframe高度
			oninitIframeHeight () {
				var _top = $('#iframe_main_Log').offset().top ;
				var _height = $(window).height() - _top + 20
				this.setState({iframeHeight:_height > 500 ? _height : 500 })
			}

            changeIframeNav(index){

				let edmitIframeData=this.state.edmitIframeData;
				let isReUrl=this.state.isReUrl;

				edmitIframeData.forEach((item,i)=>{
					item.isActive=false;
					if(index==i){
						item.isActive=true;
					}
					if(isReUrl && i==1){
						item.url=item.url+"&isReUrl=true";
						this.setState({isReUrl:false});
					}
				})
				this.setState({edmitIframeData});

			}
            // 单个商品编辑状态
			onColumnEdit(item, index,creatStatus,type) {

				let { list, edmitIframeData} = this.state;
				if(type){
					edmitIframeData.forEach(function(item,i){
						item.isActive=false;
						if(item.type==type){
							item.isActive=true;
						}
					})
				}else{
					edmitIframeData.forEach(function(item,i){
						item.isActive=false;
						if(i==0){
							item.isActive=true;
						}
					})
				}

				list[index].isEdit = !list[index].isEdit;

				var status=creatStatus=="edmit"?creatStatus:"copy";
                let IsCombination= item.IsCombination;
                let title= '';
                if (item.IsCombination){
                    if (creatStatus=="edmit"){
                        title= '编辑组合商品';
                    }
                }
				edmitIframeData[0].url=commonModule.rewriteUrlToMainDomain('/BaseProduct/CreateBaseProduct?CreateFrom='+status+'&baseproductuid='+item.UidStr+"&changeTime="+new Date().getTime());
				edmitIframeData[1].url=commonModule.rewriteUrlToMainDomain('/BaseProduct/CreateBasePlatformProduct?CreateFrom=edmit&baseproductuid='+item.UidStr+"&changeTime="+new Date().getTime());
				this.setState({ list: list,edmitIframeData:edmitIframeData },()=>{
                    this.createOrEdmitProduct(status,'',title);
                })
                $("body").addClass("hideOverflow");

			}
            onColumnBusinessCardEdit(item, index,creatStatus,type) {
				var UidStr=item.UidStr;
				$('body').find("#businessCardCreateProductIframeWrap").remove();
				var html = "";
				html += '<div class="new-full-mask" id="businessCardCreateProductIframeWrap">';
				html += '<div class="full-mask-back" onclick="closeFullMaskCreateProduct()"></div>';
				html += '<div class="full-mask-content-wrapper full-mask-right" style="width: 1088px !important;height:100vh;">';
				html += '<iframe id="businessCardCreateProductIframe" style="width: 100%; height: 100%;" frameborder="0"></iframe>';
				html += '</div>';
				html += '</div>';
				$('body').append(html);
				var url = commonModule.rewriteUrlToMainDomain('/BaseProduct/BusinessCardCreateProduct') + '&CreateFrom=edmit&baseproductuid=' + UidStr+'&isBaseProUid=true';
				$("#businessCardCreateProductIframe").attr({ src: url });
				$("#businessCardCreateProductIframeWrap").addClass("active");
				$('body').css({ overflow: 'hidden' });
			}
            // 删除结果
            showDeleteDataResult(data) {
                const content = `
                    <div>
                        <div class="n-font4">
                            <span>删除成功：<i class="n-fColor">${data.SuccessCount}</i></span>
                            <span style="margin-left: 33px;">删除失败：<i class="n-sColor">${data.FailedCount}</i></span>
                        </div>
                        ${Object.keys(data.FailedDict).length > 0 ? `
                            <div class="delete-results-container n-font6">
                                ${Object.keys(data.FailedDict).map(key => {
                                    const value = data.FailedDict[key];
                                    if (Array.isArray(value)) {
                                        const newList = value.slice(0, 2);
                                        // 如果 value 是数组，遍历数组显示
                                        return `
                                            <div class="delete-results-item">
                                                <div>失败原因：${key}</div>
                                                <div style="margin-top: 4px;">
                                                    <span id="deleteSpuCode" style="display: none;">${value.join(',')}</span>
                                                    <span class="c06">商品编码：<span>${newList.join(',')}</span>${value.length > 1 ? '等' : ''}</span>
                                                    <span class="n-dColor hover" onclick="commonModule.CopyText('#deleteSpuCode')">一键复制</span>
                                                </div>
                                            </div>
                                        `;
                                    } else if (value === null) {
                                        // 如果 value 是 null，直接显示
                                        return `
                                            <div class="delete-results-item">
                                                <div>失败原因：${key}</div>
                                            </div>
                                        `;
                                    }
                                    return '';  // 确保没有返回空值
                                }).join('')}
                            </div>
                        ` : ''}
                    </div>
                `;
			    layer.open({
				    type: 1,
				    title: '删除结果', // 标题
				    content: content,
				    offset: '200px',
				    area: '560px', // 宽高
				    skin: 'n-skin delete-result-skin',
				    success: function() {},
				    btn: ['关闭'],
			    });
            }
            // 删除列表项
			onColumnDelete (type, item, index) {
				const that = this;
                const { list, checkSkuList } = this.state;
                //console.log("checkSkuList", checkSkuList);
				let data = {};
                let message = ''; // 删除提示语
                if (type === 'batch') {
                    if (!checkSkuList.length) {
                        commonModule.w_alert({ type: 3, content: '请选择要删除的商品？' });
                        return false;
                    }
                    message = '是否删除选中的商品？';
                    const UidStrList = checkSkuList.map(item => item.UidStr);
                    data = {
                        uidList: UidStrList,
                        fxUserId: checkSkuList[0].FxUserId,
                    };
                } else {
                    data = {
                        uidList: [item.UidStr],
                        fxUserId: item.FxUserId,
                    };
                    message = '是否删除该商品？';
                }

                layer.open({
				    type: 1,
				    title: '提示', // 标题
				    content: message,
				    area: '400px', // 宽高
				    skin: 'n-skin',
				    success: function() {
                        $(".n-skin .layui-layer-content").css({
                            padding: '16px',
                            fontSize: '14px'
                        });
                    },
				    btn: ['取消', '确定'],
                    btn1: function(index1) {
                        layer.close(index1);
                    },
                    btn2: function(index1) {
                        commonModule.Ajax({
				            // url: '/BaseProduct/BaseProductRemove',
                            url:'/api/BaseProduct/BatchDeleteBaseProducts',
				            data: data,
				            async: true,
				            loading: true,
				            type: 'POST',
				            success: function (rsp) {
					            if (rsp.Success) {
                                    const data = rsp.Data;
						            layer.close(index1);
						            @* const _list = JSON.parse(JSON.stringify(list));
						            _list.splice(index, 1);
                                    that.setState({ list:_list }); *@
                                    that.activeNavsRequest(false);
                                    // commonModule.w_alert({ type: 4, content: '删除成功' });
                                    that.showDeleteDataResult(data);
					            } else {
                                    commonModule.w_alert({ type: 2, content: rsp.Message || '失败' });
					            }
				            },
				            error: function () {
					            layer.msg("网络错误，请稍后重试");
				            }
                        });
                    },
			    });
			}
            createOrEdmitProduct(type,url,title){

				@* var title='创建商品';
				if(type=='create'){
					url="/BaseProduct/CreateBaseProduct?token=@(Request.QueryString["token"])&dbname=@(Request.QueryString["dbname"])";
				}
				if(type=='edmit'){
					title='编辑商品';
				}
				$("#createFullMaskName .full-mask-header c09").text(title);
				$("#createProductIframe").attr({src:url}); *@

				if(type=='create'){
					var url="/BaseProduct/CreateBaseProduct?token=@(Request.QueryString["token"])&dbname=@(Request.QueryString["dbname"])&changeTime="+new Date().getTime();
					var edmitIframeData=this.state.edmitIframeData;
					edmitIframeData.forEach(function(item,i){
						item.isActive=false;
						if(i==0){
							item.url=url;
							item.isActive=true;
						}
					})

					this.setState({
						edmitIframeData
					},()=>{
						$("#n_tabNav_pt").hide();
						$("#headerTitle").text(title || '创建基础商品');

					})
				} else if(type=="edmit"){
					$("#n_tabNav_pt").css({display:'flex'});
					$('body').css({overflow:'hidden'});
					$("#headerTitle").text(title || '编辑商品');

				}else if(type=="copy"){
					$("#n_tabNav_pt").hide();
					$("#headerTitle").text(title || '创建商品');
				}
				$("#createFullMaskName").addClass('active');
				$('body').css({overflow:'hidden'});
			}
            closeCreateProduct(){
				//$("#createFullMaskName").removeClass("active");
				$('body').css({overflow:'auto'});

				$("#createProductIframeWrap iframe").each(function(index,item){
					if($(item).hasClass('active')){
						var iframe = document.getElementById('createProductIframe'+index);
						var iframeWindow = iframe.contentWindow;
						iframeWindow.postMessage('closeCreateProduct', '*');
					}
				});
                $("body").removeClass("hideOverflow");

			}
            switchoverInput(itemData, itemIndex, inventoryText) {
                let { list } = this.state;
                list.map((item,i)=>{
                    if (item.WareHouseSkus && item.WareHouseSkus.length > 0) {
                        item.WareHouseSkus.map((jData,j)=>{
                            if (itemData.Id == jData.Id) {
                                jData.isShowInput = !jData.isShowInput;
                                if (inventoryText === 'InList') {
                                    jData.isShowInListInput = !jData.isShowInListInput;
                                }
                                if (inventoryText === 'OutList') {
                                    jData.isShowOutListInput = !jData.isShowOutListInput;
                                }
                            }
                        })
                    }
                })
                const that = this;
                this.setState({ list, inventoryText },function(){});
            }

            // 点击空白区域时退出编辑状态
            handleClickOutside(event) {
                //console.log('点击了空白区域');
                // const { list, inOutInputEditStatus } = this.state;
                // this.setState({ inOutInputEditStatus: !inOutInputEditStatus });
            }
            // 出入库input失去焦点
            handleBlur(itemData, itemIndex, event, type) {

                let { list,checkSkuList, inventoryText,dialogData } = this.state;
                var val = event.target.value || '';
                inventoryText = type || inventoryText;
                this.setState({ list });
                if (!val) {
                    if (type === 'InList') {
                        itemData.isShowInListInput = false;
                    }
                    if (type === 'OutList') {
                        itemData.isShowOutListInput = false;
                    }
                    return false;
                }
                let r = /^\+?[1-9][0-9]*$/;
                if (val && !r.test(val)) {
                    layer.msg("请输入整数");
                    return false;
                }
                if (inventoryText == 'OutList') {
                    if (val > itemData.StockCount) {
                        layer.msg("出库数量不能大于库存数量");
                        return false;
                    }
                }

                list.map((item,i)=>{
                    if (item.WareHouseSkus && item.WareHouseSkus.length > 0) {
                        item.WareHouseSkus.map((jData,j)=>{
                            if (itemData.Id == jData.Id) {
                                jData.ChangeCount = val;
                                itemData = jData;
                            }
                        })
                    }
                })

                this.setState({ list,checkSkuList,inventoryText},()=>{
                    // 如果点击了备注按钮，则弹出备注框
                    setTimeout(() => {
                        this.state.list.map((item,i)=>{
                            if (item.WareHouseSkus && item.WareHouseSkus.length > 0) {
                                item.WareHouseSkus.map((jData,j)=>{

                                    if (itemData.Id == jData.Id) {
                                        dialogData.itemData = jData;
                                        if (type === 'InList') {
                                            jData.isShowInListInput = !jData.isShowInListInput;
                                        }
                                        if (type === 'OutList') {
                                            jData.isShowOutListInput = !jData.isShowOutListInput;
                                        }
                                    }
                                })
                            }

                        })

                        if (!isRemark) {
                            this.getInOutSkuSubmit(itemData);
                        } else {
                            this.setState({ dialogData })
                        }

                    },200)
                });
            }
            // 出入库弹窗
            onShowInOutSkuModal(keys) {
                var that =this;
                var checkSkuList = [];
				var dialogData = this.state.dialogData;
				var skuCount = 0;
                let inventoryText = keys;
                let { list } = this.state;
                list.forEach((iData, i) => {
                    if (iData.WareHouseSkus && iData.WareHouseSkus.length > 0) {
                        iData.WareHouseSkus.map((jData,j)=>{
                            if (jData.isCheck) {
                                checkSkuList.push(jData);
                            }
                        })
                    }
                })
                if (checkSkuList.length === 0) {
                    layer.msg('请选择SKU');
                    return;
                }
                var title = inventoryText === 'InList' ? '统一增加库存数量': '统一减少库存数量';
                dialogData.skuCount = checkSkuList.length;
                dialogData.keysName = keys;
                dialogData.main = layer.open({
                    type: 1,
                    title: title, //不显示标题
                    content: $('#dialogInOutSku'),
                    skin: 'n-skin',
                    area: ['500px', 'auto'], //宽高
                    success:function(){
                    },
					cancel: function(){
						that.onHideInOutSkuModal();
 				    },
                });
                this.setState({ dialogData,checkSkuList:checkSkuList,inventoryText });
            }
            onHideInOutSkuModal() {
                const {dialogData} = this.state;
                if(dialogData.main) {
                    dialogData.main = layer.close(dialogData.main);
                }
                dialogData.inputValue = '';
                dialogData.inputRemarkValue = '';
                this.setState({dialogData});
            }

            getInOutSkuSubmit (itemData){
                const {inventoryText,list,checkSkuList,dialogData} = this.state;
                const that = this;
                let InList = [];
                let OutList = [];
                let flag = true;
                if (itemData) {
                    if (inventoryText === 'InList') {
                        InList.push({WareHouseSkuCode:itemData.Id,ChangeCount:itemData.ChangeCount});
                    } else {
                        OutList.push({WareHouseSkuCode:itemData.Id,ChangeCount:itemData.ChangeCount});
                    }
                } else {
                    if (checkSkuList.length){
                        let r = /^\+?[1-9][0-9]*$/;
                        if (dialogData.inputValue && !r.test(dialogData.inputValue)) {
                            layer.msg("请输入整数");
                            return false;
                        }
                        checkSkuList.map((item,i)=>{
                            if (dialogData.inputValue && inventoryText === 'InList'){
                                InList.push({
                                    WareHouseSkuCode: item.Id,
                                    ChangeCount:dialogData.inputValue,

                                    ChangeType: 'BatchIn'
                                })
                            }
                            if (dialogData.inputValue && inventoryText === 'OutList'){
                                OutList.push({
                                    WareHouseSkuCode: item.Id,
                                    ChangeCount:dialogData.inputValue,
                                    ChangeType: 'BatchOut'
                                })
                                if (item.StockCount - dialogData.inputValue < 0) {
                                    flag = false;
                                }
                            }
                        })
                    } else {
                        list.map((item,i)=>{
                            if (item.WareHouseSkus && item.WareHouseSkus.length > 0) {
                                item.WareHouseSkus.map((jData,j)=>{
                                    if (jData.ChangeCount && inventoryText === 'InList'){

                                        InList.push({
                                            WareHouseSkuCode: jData.Id,
                                            ChangeCount:jData.ChangeCount,
                                            ChangeType: 'BatchIn',
                                        })
                                    }
                                    if (jData.ChangeCount && inventoryText === 'OutList' && jData.StockCount - dialogData.inputValue >= 0){
                                        OutList.push({
                                            WareHouseSkuCode: jData.Id,
                                            ChangeCount:jData.ChangeCount,
                                            ChangeType: 'BatchOut',
                                        })
                                        if (jData.StockCount - dialogData.inputValue < 0) {
                                            flag = false;
                                        }
                                    }
                                })
                            }
                        })
                    }
                }

                if (!flag){
                    layer.msg('数量不能大于剩余库存数量');
                    return;
                }
                dialogData.isSave = false; //防止重复提交
                this.setState({
                    dialogData,
                })
                commonModule.Ajax({
					type: "POST",
					url: "/StockControl/InOutSkuSubmit",
                    loading: true,
					data: {
                        requestModel: {
                            InList:InList,
                            OutList: OutList,
                            OperateRemark: dialogData.inputRemarkValue || '',
                        }
                    },
					success: function (rsp) {
						if ( rsp.Success ) {
                            layer.msg('保存成功');
                            if (itemData && itemData.Id) {
                                that.onClear();
                                that.refreshLoadStockData(itemData.Id);
                            } else {
                                that.onDialogClose();
                                that.onClear();
                                that.activeNavsRequest();
                                that.setState({ checkSkuList: [] });
                            }

                        } else {
                            layer.msg(rsp.Message || '操作失败');
                            dialogData.isSave = true; //防止重复提交
                            that.setState({
                                dialogData,
                            })
                        }
                    }
                });
            }

            // 出入库备注
            onRemarkModal(itemData, index, keys){
                let keysName = this.state.dialogData.keysName;
                // 校验输入框的编辑状态是否改变
                let isValChange = keysName != keys ? true : false;

                isRemark = true;
                var that =this;
                setTimeout(() => {
                    var checkSkuList = [];
                    var dialogData = this.state.dialogData;
                    var skuCount = 0;
                    let inventoryText = keys;
                    let { list } = this.state;
                    if (itemData) {
                        itemData.isAddRemark = true;
                        dialogData.itemIndex = index;
                        dialogData.inputValue = isValChange ? itemData.ChangeCount : '';
                        skuCount = 1;
                        dialogData.itemData = itemData; // 当前选中的数据
                    } else {
                        list.forEach((iData, i) => {
                            if (iData.WareHouseSkus && iData.WareHouseSkus.length > 0) {
                                iData.WareHouseSkus.map((jData,j)=>{
                                    if (jData.isCheck) {
                                        checkSkuList.push(jData);
                                    }
                                })
                            }
                        })
                    }

                    var title = inventoryText === 'InList' ? '入库备注': '出库备注';
                    dialogData.skuCount = skuCount || checkSkuList.length;
                    dialogData.keysName = keys;
                    dialogData.inputRemarkValue = '';
                    //console.log("dialogData====",dialogData)
                    this.setState({ dialogData,checkSkuList:checkSkuList,inventoryText,list });
                    dialogData.main = layer.open({
                        type: 1,
                        title: title, //不显示标题
                        content: $('#dialogInOutRemark'),
                        skin: 'n-skin',
                        area: ['500px', 'auto'], //宽高
                        success:function(){
                        },
                        cancel: function(){
                            that.onDialogClose();
                        },
                        end: function(){
                            that.onDialogClose();
                        }
                    });


                },100);

            }
            // 出入库input失去焦点(备注弹窗)
            handleRemarkBlur( event, type) {
                let { dialogData } = this.state;
                var val = event.target.value || '';
                let flag=true;
                if (!val) {
                    return false;
                }
                let r = /^\+?[1-9][0-9]*$/;
                if (val && !r.test(val)) {
                    layer.msg("请输入整数");
                    return false;
                }
                if (dialogData.keysName == 'OutList') {
                    let stockCount = dialogData.itemData ? dialogData.itemData.StockCount : dialogData.inputValue;
                    if (val > stockCount) {
                        layer.msg("出库数量不能大于库存数量");
                        return false;
                    }
                }

                dialogData.itemData.ChangeCount = val;
            }
            // 出入库备注提交
            getInOutRemarkSubmit (){
                const {inventoryText,list,checkSkuList,dialogData} = this.state;
                const that = this;
                let InList = [];
                let OutList = [];
                let flag = true;
                let itemData = dialogData.itemData;
                if (dialogData.itemData) {
                    if (inventoryText === 'InList') {
                        InList.push({WareHouseSkuCode:itemData.Id,ChangeCount:itemData.ChangeCount,OperateRemark:dialogData.inputRemarkValue});
                    } else {
                        if (itemData.ChangeCount > itemData.StockCount || itemData.ChangeCount <= 0) {
                            flag = false;
                        }
                        OutList.push({WareHouseSkuCode:itemData.Id,ChangeCount:itemData.ChangeCount,OperateRemark:dialogData.inputRemarkValue});
                    }
                } else {
                    if (checkSkuList.length){

                        checkSkuList.map((item,i)=>{
                            if (dialogData.inputValue && inventoryText === 'InList'){
                                InList.push({
                                    WareHouseSkuCode: item.Id,
                                    ChangeCount:dialogData.inputValue,
                                    ChangeType: 'BatchIn'
                                })
                            }
                            if (dialogData.inputValue && inventoryText === 'OutList'){
                                OutList.push({
                                    WareHouseSkuCode: item.Id,
                                    ChangeCount:dialogData.inputValue,
                                    ChangeType: 'BatchOut'
                                })
                                if (item.StockCount - dialogData.inputValue < 0) {
                                    flag = false;
                                }
                            }
                        })
                    } else {
                        list.map((item,i)=>{
                            if (item.WareHouseSkus && item.WareHouseSkus.length > 0) {
                                item.WareHouseSkus.map((jData,j)=>{
                                    if (jData.ChangeCount && inventoryText === 'InList'){

                                        InList.push({
                                            WareHouseSkuCode: jData.Id,
                                            ChangeCount:jData.ChangeCount,
                                            ChangeType: 'BatchIn'
                                        })
                                    }
                                    if (jData.ChangeCount && inventoryText === 'OutList' && jData.StockCount - dialogData.inputValue >= 0){
                                        OutList.push({
                                            WareHouseSkuCode: jData.Id,
                                            ChangeCount:jData.ChangeCount,
                                            ChangeType: 'BatchOut'
                                        })
                                        if (jData.StockCount - dialogData.inputValue < 0) {
                                            flag = false;
                                        }
                                    }
                                })
                            }
                        })
                    }
                }
                if (!flag){
                    layer.msg('数量不能大于剩余库存数量');
                    return;
                }
                commonModule.Ajax({
					type: "POST",
					url: "/StockControl/InOutSkuSubmit",
					data: {
                        requestModel: {
                            InList:InList,
                            OutList: OutList,
                            OperateRemark: dialogData.inputRemarkValue || '',
                        }
                    },
                    loading: true,
					success: function (rsp) {
						if ( rsp.Success ) {
                            layer.msg('保存成功');
                            isRemark = false;
                            if (itemData && itemData.Id) {
                                // list.map((item,i)=>{
                                //     if (item.WareHouseSkus && item.WareHouseSkus.length > 0) {
                                //         item.WareHouseSkus.map((jData,j)=>{
                                //             jData.StockCount = jData.ChangeCount;
                                //             if (Id == jData.Id) {
                                //                 jData.isShowInput = !jData.isShowInput;
                                //             }
                                //         })
                                //     }
                                // })
                                // that.setState({ list });
                                that.onClear();
                                that.refreshLoadStockData(itemData.Id);//刷新库存
                               // that.loadStockData(false,'spu');
                                that.onDialogClose();
                            } else {
                                that.onDialogClose();
                                that.onClear();
                                that.activeNavsRequest();
                                that.setState({ checkSkuList: [] });
                            }

                        } else {
                            layer.msg(rsp.Message || '操作失败');
                        }
                    }
                });
            }
            opanStockControl() {
                window.top.open(commonModule.rewriteUrl("/StockControl/StoreManagement"));
            }
            // 开启库存预警
            openWarehouseWarn() {
                let _this = this;
                var html = '<div class="warehouseWarnWrap  wu-f14" style="color: #3D3D3D">当剩余库存小于等于<div class="wu-inputWrap wu-form-mid wu-mL4 wu-mR4" style="width: 100px"><input class="wu-input"  type="text" id="WarnStockNum" value="' + commonModule.WarnStockNum + '" maxlength="6" /></div> 时，预警提示。</div>';
                var warehouseWarnLayer = layer.open({
                    type: 1,
                    title: "设置剩余库存低于提醒的数量", //不显示标题
                    content: html,
                    area: ["400px"],
                    skin: 'wu-dailog',
                    btn: ['保存设置', '取消设置'],
                    yes: function () {

                        var v = $("#WarnStockNum").val();
                        if (v == "" || isNaN(v) || parseInt(v) < 0) {
                            layer.msg('请输入大于等于0的整数');
                            return;
                        }
                        v = parseInt(v);

                        var key = "ErpWeb/StockDetail/WarnStockNum";
                        // commonModule.WarnStockNum = v.toString();
                        $("#operate_wran_open").addClass("hide");
                        $("#operate_wran_close").removeClass("hide");
                        $(".isShowWarn").removeClass("hide2");
                        commonModule.Ajax({
                            url: "/System/SaveCommonSetting",//同时更新阿里配置和拼多多配置
                            type: "POST",
                            data: { settingKey: key, settingValue: v },
                            success: function (rsp) {
                                if (!rsp.Success) {
                                    layer.msg(rsp.Message, { icon: 2 });
                                    return;
                                }
                                commonModule.WarnStockNum = v.toString();
                                $("#operate_wran_open").addClass("hide");
                                $("#operate_wran_close").removeClass("hide");
                                $(".isShowWarn").removeClass("hide2");

                                _this.activeNavsRequest(true);
                            }
                        });
                        layer.close(warehouseWarnLayer);
                    }
                });
            }

            // 关闭库存预警
            closeWarehouseWarn() {
                let _this = this;
                var closeWarehouseWarnLayer = layer.confirm('<div class="wu-flex"><i class="iconfont icon-a-error-circle-filled1x wu-mR8" style="color: rgba(8, 136, 255, 1); font-size: 20px;"></i><div><div class="wu-c09 wu-mB8" style="font-size:14px;">确定要关闭库存预警吗？</div><div  class="wu-c06 wu-f12" style="line-height: 12px;" >关闭库存预警后，预警条件将失效</div></div></div>', {
                    area: ["320px"], //宽高
                    // icon: 3,
                    // title: "确定",
                    title: false,  
                    btn: ['确认关闭', '取消关闭'], //可以无限个按钮
                    skin: 'wu-dailog',
                    btn2: function (index, layero) {

                    }
                }, function (index, layero) {

                    var v = "";
                    var key = "ErpWeb/StockDetail/WarnStockNum";
                    commonModule.Ajax({
                        url: "/System/SaveCommonSetting",//同时更新阿里配置和拼多多配置
                        type: "POST",
                        data: { settingKey: key, settingValue: v },
                        success: function (rsp) {
                            if (!rsp.Success) {
                                layer.msg(rsp.Message, { icon: 2 });
                                return;
                            }
                            commonModule.WarnStockNum = v;
                            _this.closeWareSelect();
                            _this.activeNavsRequest(true);

                            //按钮【按钮一】的回调
                            $("#operate_wran_open").removeClass("hide");
                            $("#operate_wran_close").addClass("hide");
                            $(".isShowWarn").addClass("hide2");
                            layui.form.render("select");
                        }
                    });

                    layer.close(closeWarehouseWarnLayer);

                });
            }
            // 清空预警下拉框
            closeWareSelect() {
                let _this = this;
                let { searchData,queryQaram } = this.state;
                let list = searchData.map(function(item) {
                    if (item.name === 'IsWarn') {
                        return { ...item, value: "" };
                    }
                    return item;
                });

                queryQaram.IsWarn = "";
                _this.setState({ searchData: list, queryQaram });

                let StockWarningData = [
                    { Value: '', Text: '全部' },
                    { Value: '1', Text: '预警' },
                    { Value: '0', Text: '非预警' },
                ];
                let StockWarningOptions = {
                    eles: '#IsWarn',
                    name: 'IsWarn',
                    data: StockWarningData,
                    leftTitle: '库存预警'
                };
                _this.initRenderSelect(StockWarningOptions);
            }
 			// 价格格式化
            onPriceFormat(value) {
                if ( !value && value !== 0) {
                    return '-';
                } else {
                    return '￥'+ value
                }
            }
            // 规格格式化
			onColumnSkuFormat(AttributeValue) {
				var skuCombination= '';
				var skuName = '';
				var Attributes = JSON.parse(AttributeValue);
				if (Attributes.length) {
					for (var i = 0; i < Attributes.length; i++) {
						var attributesItem = Attributes[i];
						@*skuCombination += attributesItem.k + ',' + attributesItem.v;*@
                        if (attributesItem.v !="无规格") {
		                    skuName += attributesItem.v + ";";
	                    }
                        
	                    // if (i != Attributes.length-1) {
	                    // }
					}
					skuName = skuName.substring(0, skuName.length - 1)
				}
				return skuName
			}
            // 添加关联商品（右侧弹出）
			onAddProduct () {
				this.setState({
					isShowQuickAdd: true,
				 });
			}
			// 关联同款（右侧弹出）
			onRelationProduct (item,type) {
                //取消关联同款
                if (type == 'unbind') {
                    item.popUpJump = type;
                }
				this.setState({
					isShowRelationProduct: true,
					relationProductData: item,
				});
			}
            // 
            hideBindProductPopUp () {
				this.setState({isShowQuickAdd:false,isShowRelationProduct:false,isShowExceptionLog:false});
                this.activeNavsRequest();
                document.body.style.overflow = 'auto';
			}
            goTarUrl (url) {
				event.stopPropagation();
				var dbname = $("#dbname_input").val();
				var _url = url + '&dbname=' + dbname;
				window.open(commonModule.rewriteUrl(_url),'_parent')
			}
			goTarUrl2 (url) {
				event.stopPropagation();
				var dbname = $("#dbname_input").val();
				var _url = url + '?dbname=' + dbname;
				window.open(commonModule.rewriteUrl(_url),'_parent')
			}
            onSetting () {
				var dialogData = this.state.dialogData;
				const that = this;
				dialogData.main = layer.open({
					type: 1,
					title: '基础商品显示设置',
					content: $('#dialogSetting'),
					skin: 'n-skin',
					area: ['560px', 'auto'], //宽高
					success:function(){
					},
					cancel: function(index, layero){
						that.onDialogClose();
					},
                    end: function() {
                        that.onGetBaseProductSetting();
                    }
				});
				dialogData.isSave = true;
				this.setState({ dialogData });
			}
            onGetBaseProductSetting() {
				const that = this;
				const {dialogData} = this.state;

				commonModule.Ajax({
					url: '/api/BaseProduct/GetBaseProductSetting',
					type:'GET',
					showMasker: false,
					success: function (rsp) {
						if (rsp.Success) {
							const _data = rsp.Data;
							dialogData.AbnormalOrderTagReminder = _data.AbnormalOrderTagReminder;
							dialogData.ExportBeforeReminder = _data.ExportBeforeReminder;
							dialogData.isSwitch = _data.OrderCombine;
							dialogData.PrintBeforeShippingWarning = _data.PrintBeforeShippingWarning;
							dialogData.StockPreparationReminder = _data.StockPreparationReminder;
                            dialogData.DistributePriceSetting = _data.DistributePriceSetting;
							that.setState({ dialogData });
						} else {
							layer.msg(rsp.Message || rsp.Data || '失败');
						}
					}
				})
			}
            onSwitch () {
				var dialogData = this.state.dialogData;
				dialogData.isSwitch = !dialogData.isSwitch;
				this.setState({ dialogData });
			}
            onRemindCheck (keysName) {
				var dialogData = this.state.dialogData;
				dialogData[keysName] = !dialogData[keysName];
				this.setState({ dialogData });
			}
            // 等级分销价生效规则设置
            handleLevelRuleSetChange(value) {
               var dialogData = this.state.dialogData;
               dialogData.DistributePriceSetting = value;
               this.setState({ dialogData });
            }
            SetBaseProductSetting(checkList) {
				const that = this;
				var dialogData = this.state.dialogData;
				var data = {
					OrderCombine: dialogData.isSwitch,
					PrintBeforeShippingWarning: dialogData.PrintBeforeShippingWarning || false,
					StockPreparationReminder : dialogData.StockPreparationReminder || false,
					AbnormalOrderTagReminder : dialogData.AbnormalOrderTagReminder || false,
					ExportBeforeReminder : dialogData.ExportBeforeReminder || false,
				}
				commonModule.SaveCommonSetting("/ErpWeb/FenDan/BaseProductSetting", JSON.stringify(data), function (rsp) {
					if (rsp.Success) {
						layer.msg( rsp.Data || '保存成功');
						that.onDialogClose();
						that.onGetBaseProductSetting();
					}
				});

				// commonModule.Ajax({
				// 	url: '/api/BaseProduct/SetBaseProductSetting',
				// 	showMasker: false,
				// 	loadingMessage: "保存中",
				// 	contentType: 'application/json',
				// 	data: JSON.stringify(data),
				// 	success: function (rsp) {
				// 		if (rsp.Success) {
				// 			layer.msg( rsp.Data || '');
				// 			that.onDialogClose();
				// 		} else {
				// 			layer.msg(rsp.Message || rsp.Data || '失败');
				// 		}
				// 	}
				// })
			}

            rewriteUrlToMainDomain(url) {
                window.open(commonModule.rewriteUrlToMainDomain(url),'_parent')
            }
            onShowExceptionLog() {
                this.setState({isShowExceptionLog:true})
                document.body.style.overflow = 'hidden';//启用滚动条
            }
            onMouseOver (e,item, index) {
                this.popoverRef = ReactDOM.findDOMNode(e.target);
                if (this.popoverRef === null) return;
                const {combinationPopover} = this.state;
                // 库存视角组合货品信息
                if (item.WareHouseSkus && item.WareHouseSkus.length > 0) {
                    item.ChildSku = item.WareHouseSkus[0].ChildSkus;
                    item.ProSubject = item.ProductSubject
                }
                if (combinationPopover.isShow || (item.ChildSku && item.ChildSku.length == 0)) return;
                const that = this;
                // 气泡dom
                const popoverDom = this.popoverRef.getBoundingClientRect();
                const scrollTop = document.body.scrollTop || document.documentElement.scrollTop;
                // 减去68是气泡能鼠标移出时隐藏
                combinationPopover.left = popoverDom.left + popoverDom.width + 4 - 68;
                combinationPopover.top = popoverDom.top + scrollTop;
                combinationPopover.isShow = true;

                combinationPopover.ProSubject = item.ProSubject;
                combinationPopover.ChildSku = item.ChildSku;
                combinationPopover.ChildSku[0].UidStr = item.UidStr;
                this.setState({combinationPopover})
                var popoverCont = document.getElementById('combinationPopover');
                popoverCont.addEventListener('mouseleave', onMouseleave);
                function onMouseleave (e) {
                    popoverCont.removeEventListener('mouseleave', onMouseleave);
                    combinationPopover.isShow = false;
                    that.setState({combinationPopover})
                }
            }

            // 编辑组合货品
            onEditCombination (ChildSku) {
                const {combinationPopover} = this.state;
                combinationPopover.isShow = false;
                layer.closeAll();
                this.setState({ dialogVisible: true,ChildSku,combinationPopover })
            }

            // 打开更新日志
            openChangLog (productCode) {
                const that = this;
                layer.open({
                    type: 1,
                    title: '组合品变更日志',
                    area: ['560px', '500px'],
                    content: $('#ChangLogDialog'),
                    skin: 'n-skin',
                    success: function (layero, index) {

                    }
                })
                const {queryQaramProductChangeLog} = this.state;
                queryQaramProductChangeLog.productCode = productCode;
                this.setState({queryQaramProductChangeLog,productChangeLogList:[]},()=>{
                    this.getCombinedProductChangeLog(true);
                });
            }
            // 打开库存弹窗
            onMouseOverInventory (e,item, index,keysName) {


                if (this.popoverRef) {
                    this.popoverRef.removeAttribute("style");
                }
                this.popoverRef = ReactDOM.findDOMNode(e.target);
                if (this.popoverRef === null) return;
                const {inventoryPopover} = this.state;
                const that = this;
                this.popoverRef.style.color = '#0888FF';
                // 气泡dom
                const popoverDom = this.popoverRef.getBoundingClientRect();
                const scrollTop = document.body.scrollTop || document.documentElement.scrollTop;
                const innerHeight = window.innerHeight;

                let domHeight = 254;
                // 减去68是气泡能鼠标移出时隐藏
                inventoryPopover.left = popoverDom.left - 272/2 + popoverDom.width /2 ;
                inventoryPopover.top = popoverDom.top + popoverDom.height + 8+ scrollTop - 24;
                inventoryPopover.direction = 'top';
                // 如果超出屏幕高度
                if (inventoryPopover.top + domHeight > innerHeight) {
                    inventoryPopover.top = popoverDom.top - domHeight + scrollTop -8;
                    inventoryPopover.direction = 'bottom';
                }
                inventoryPopover.isShow = true;
                inventoryPopover.isSave = false;
                inventoryPopover.itemData = item;
                inventoryPopover.keysName = keysName;
                this.setState({inventoryPopover})

                document.addEventListener('click', this.onMouseleavePopove);
                NewBaseProductsThis = that;
            }
            onMouseleavePopove (e) {
                var popoverCont = document.getElementById('inventoryPopover');
                if ( popoverCont.contains(e.target)) return;
                NewBaseProductsThis.onPopoverHandleClose()
            }


            onPopoverHandleChange(event,keys) {
				var inventoryPopover = this.state.inventoryPopover;
				var val = event.target.value || '';
				if (val < 0 && inventoryPopover.inputType != 'text') {
					layer.msg("请输入正确的金额");
					return false;
				}

                // 出入库输入框校验
                if (keys == 'inventoryNum' && (inventoryPopover.keysName == 'InList' || inventoryPopover.keysName == 'OutList')) {
                    inventoryPopover.inventoryNum = val;
                    inventoryPopover.isSave = val ? true : false;
                }
                if (keys == 'inputRemarkValue') {
                    if (val.length > 100) {
                        val = val.slice(0, 100);
                    }
                    inventoryPopover.inputRemarkValue = val;
                }
				this.setState({ inventoryPopover });
			}

            onPopoverHandleClose() {
                const {inventoryPopover} = this.state;
                if (this.popoverRef) {
                    this.popoverRef.removeAttribute("style");
                }
                inventoryPopover.isShow = false;
                inventoryPopover.keysName = '';
                inventoryPopover.inventoryNum = '';
                inventoryPopover.inputRemarkValue = '';
                inventoryPopover.itemData = '';
                // this.onMouseOverInventory('close')
                document.removeEventListener('click', this.onMouseleavePopove);
                this.setState({ inventoryPopover });
            }
            onPopoverHandleSave() {
                const {inventoryText,list,checkSkuList,inventoryPopover} = this.state;
                const that = this;
                let InList = [];
                let OutList = [];
                let flag = true;
                let itemData = inventoryPopover.itemData;
                let reg = /^\d+$/;
                if (!reg.test(inventoryPopover.inventoryNum)) {
                    layer.msg("请输入正确的数量");
                    return;
                }
                if (inventoryPopover.itemData) {
                    if (inventoryPopover.keysName === 'InList') {
                        InList.push({WareHouseSkuCode:itemData.Id,ChangeCount:inventoryPopover.inventoryNum,OperateRemark:inventoryPopover.inputRemarkValue});
                    } else {
                        if (inventoryPopover.inventoryNum > itemData.StockCount || inventoryPopover.inventoryNum <= 0) {
                            flag = false;
                        }
                        OutList.push({WareHouseSkuCode:itemData.Id,ChangeCount:inventoryPopover.inventoryNum,OperateRemark:inventoryPopover.inputRemarkValue});
                    }
                }
                if (!flag){
                    layer.msg('数量不能大于剩余库存数量');
                    return;
                }
                commonModule.Ajax({
					type: "POST",
					url: "/StockControl/InOutSkuSubmit",
					data: {
                        requestModel: {
                            InList:InList,
                            OutList: OutList,
                            OperateRemark: inventoryPopover.inputRemarkValue || '',
                        }
                    },
                    loading: true,
					success: function (rsp) {
						if ( rsp.Success ) {
                            layer.msg('保存成功');
                            isRemark = false;
                            if (itemData && itemData.Id) {

                                that.onClear();
                                that.refreshLoadStockData(itemData.Id);//刷新库存
                               // that.loadStockData(false,'spu');
                                that.onPopoverHandleClose();
                            }

                        } else {
                            layer.msg(rsp.Message || '操作失败');
                        }
                    }
                });
            }
            getCombinedProductChangeLog (isPaging) {
                const that = this;
                const {queryQaramProductChangeLog} = this.state;

                if (isPaging) {
                    queryQaramProductChangeLog.PageIndex = 1;
                }
				commonModule.Ajax({
					url: '/api/CombinedProductChangeLog/GetByProductCode',
					type:'POST',
                    data: queryQaramProductChangeLog,
					showMasker: false,
					success: function (rsp) {
						if (rsp.Success) {
                            let list = rsp.Data.Rows || [];
                            list.forEach((item,index)=>{
                                item.ChangeDetailList = item.ChangeDetail.split('\r\n');
                            })
							that.setState({
								productChangeLogList: list,
								queryQaramProductChangeLog: {
									...queryQaramProductChangeLog,
									Total: rsp.Data.Total,
								}
							}),
							layui.laypage.render({
								elem: 'pagingCombinedProductChangeLog',
								count: rsp.Data.Total,
								limit: queryQaramProductChangeLog.PageSize,
								curr: queryQaramProductChangeLog.PageIndex,
								limits: [50, 100, 200, 300, 400, 500],
								layout: ['count', 'prev', 'next', 'limit', 'skip'],
                                theme: 'n-page',
								jump: function (obj, first) {
									if (!first) {
										queryQaramProductChangeLog.PageIndex = obj.curr;
										queryQaramProductChangeLog.PageSize = obj.limit;
										that.setState({ queryQaramProductChangeLog: queryQaramProductChangeLog }, function () {
											that.getCombinedProductChangeLog(false);
										})
									}
								}
							});
						} else {
							layer.msg(rsp.Message || rsp.Data || '失败');
						}
					}
				})
            }
            // 过滤文本
            onReplaceText (str) {
                return str.replace(/;无规格$/, "");
            }
            handledialogDataToggle(keysName,value) {
                const {dialogData} = this.state;
                dialogData[keysName] = !value;
                this.setState({ dialogData: dialogData })
            }
            render() {
                const { baseProductNavs, activeNavsName, searchData, activeNavsTitle, guideLayerData,guideNavBar,list,tabList,platformArea,platformType,
                iframeHeight,allCheckSku,checkSkuList,partialSelect,dialogData,AutoRelationProces, updateFieldsChecked,
                inventoryText ,isShowQuickAdd, isShowRelationProduct, isHasAbnormal,isShowExceptionLog,popoverMsg,
                combinationPopover,inOutInputEditStatus,inventoryPopover,isShowUnbindRelationProduct,guideClearFields } = this.state;
                return (
                    <div className="base-commodity-all">
                        <div id="combinationPopover" className="combinationPopover" style={{display: combinationPopover.isShow ? 'block' : 'none',left: combinationPopover.left+'px',top: combinationPopover.top+'px'}} >
                            <div className="combinationPopover-content">
                            <div className="combinationPopover-hidebox"></div>
                                <div className="combinationPopover-title">
                                    <span>子货品出库明细</span>
                                    <span className="combinationPopover-edit hover" onClick={(e)=>{e.stopPropagation(); this.onEditCombination(combinationPopover.ChildSku)}}>编辑</span>
                                </div>
                                <div className="combinationPopover-body">
                                    <div className="combinationPopover-body-ul">
                                        {
                                            combinationPopover.ProSubject && combinationPopover.ProSubject?
                                            <div className="combinationPopover-body-li-title ellipsis">{combinationPopover.ProSubject}</div>
                                            :null
                                        }

                                        {
                                            combinationPopover.ChildSku && combinationPopover.ChildSku.map((item, index) => {
                                                return <div className="combinationPopover-body-li flex">
                                                        <img src={item.ImageUrl ? item.ImageUrl : '/Content/images/nopic.gif'} className="productShow-img" />
                                                            <ul>
                                                                <li className="flex fs14 grid"><span className="c09 ellipsis grid-column">{this.onReplaceText(item.Name)}</span><span className="colorDC8 grid-column-2">x{item.Count}</span></li>
                                                                <li className="c04 fz12 ellipsis combinationPopover-body-ul-li">SKU编码：
                                                                    <span id={"ChildSku"+item.CargoNumber}>{item.CargoNumber}</span>
                                                                    <i className="iconfont icon-a-copy1x hover" style={{fontSize: "14px"}} onClick={(e)=>{e.stopPropagation();this.onCopySpuCode("ChildSku"+item.CargoNumber)}}></i>
                                                                </li>
                                                            </ul>
                                                        </div>
                                            })
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div id="inventoryPopover" className={inventoryPopover.direction === 'bottom' ? 'inventoryPopover inventoryPopover-bottom' : 'inventoryPopover inventoryPopover-top'}
                        style={{display: inventoryPopover.isShow ? 'block' : 'none',
                        left: inventoryPopover.left+'px',
                        top: inventoryPopover.top+'px',
                        paddingBottom: inventoryPopover.direction === 'bottom' ? '24px' : '0px',
                        paddingTop: inventoryPopover.direction === 'top' ? '24px' : '0px',
                        }}
                        >
                            <div className="inventoryPopover-content" onClick={(e)=>{e.stopPropagation();}}>
                            <div className="inventoryPopover-hidebox"></div>
                                <div className="inventoryPopover-body">
                                   <ul>
                                        <li>
                                            <span>{inventoryPopover.keysName == 'InList' ? '增加库存数量' : '减少库存数量'}</span>
                                            <input type="text" id="inventoryPopover-input" className="layui-input n-layui-input" value={inventoryPopover.inventoryNum} onChange={(event)=>this.onPopoverHandleChange(event,'inventoryNum')} placeholder="请输入数量" ref={this.inputRef}/>
                                        </li>
                                        <li>
                                            <span>备注</span>
                                            <div class="n-inputWrap">
                                                <textarea type="text" className="layui-input n-layui-input n-layui-textarea" onChange={(event)=>this.onPopoverHandleChange(event,'inputRemarkValue')} style={{height: "72px"}} placeholder="请输入备注内容，展示在“库存变更记录”页面" value={inventoryPopover.inputRemarkValue} maxlength="100"></textarea>
                                                <span class="input-num"><i class="titleLength">{inventoryPopover.inputRemarkValue ? inventoryPopover.inputRemarkValue.length : 0}</i>/100</span>
                                            </div>

                                        </li>
                                   </ul>
                                   <div className="inventoryPopover-btn">
                                        <div class="n-mButton n-sActive mR8" onClick={()=> this.onPopoverHandleClose()}>取消</div>
                                        {
                                            inventoryPopover.isSave ? <div class="n-mButton" onClick={()=>this.onPopoverHandleSave()}>保存</div>:
                                            <div class="n-mButton stop">保存</div>
                                        }
                                   </div>
                                </div>
                            </div>
                        </div>

                        {
							isShowQuickAdd ?
							<BaseProductQuickAdd hideBindProductPopUp={()=>this.hideBindProductPopUp()}/>
							:null
						}
						{
							isShowRelationProduct ?
							<BaseProductRelation data={this.state.relationProductData} hideBindProductPopUp={()=>this.hideBindProductPopUp()}/>
							:null
						}
                        
                        {
							isShowExceptionLog ?
							<ExceptionLogComponent hideBindProductPopUp={()=>this.hideBindProductPopUp()}/>
							:null
						}
                        @*引导蒙层*@
                        <div className="guideLayerDom" style={guideLayerData.isShow ? {display: 'block'}:{display:'none'}}>
                            <div id="guideNavBar" style={{left:guideNavBar.left+'px',top:guideNavBar.top+'px',right: guideNavBar.right+'px', width:guideNavBar.width+'px'}}>
                                <ul className="n-box-nav">
                                    {
                                        baseProductNavs.map((item, index) => {
                                            return (
                                                <li className={guideLayerData.step == index + 1 ? "n-box-nav-li active" : "n-box-nav-li"}>{item.title}</li>
                                            )
                                        })
                                    }
                                </ul>
                                <div className="line-box">
                                    <div className="great-circle">
                                        <span className="small-circle"></span>
                                    </div>
                                    <div className="line"></div>
                                </div>
                            </div>
                            <div className="guideLayer-container" style={{display: 'block',left:guideLayerData.right+'px',top:guideLayerData.top+'px'}}>
                                <div className="guideLayer-title" >{guideLayerData.title}</div>
                                <div className="guideLayer-content" >
                                    {guideLayerData.text}
                                </div>
                                <div className="guideLayer-btn" >
                                    <div className="n-mButton" style={(guideLayerData.step) == guideLayerData.total ? {display: 'block'}:{display:'none'}} onClick={()=> this.onGuideLayer(guideLayerData.step + 1)}>我知道了({guideLayerData.step}/{guideLayerData.total})</div>
                                    <div className="n-mButton" style={(guideLayerData.step) !=guideLayerData.total ? {display: 'block'}:{display:'none'}} onClick={()=> this.onGuideLayer(guideLayerData.step + 1)}>下一步({guideLayerData.step}/{guideLayerData.total})</div>
                                </div>
                            </div>
                        </div>
                        @*引导蒙层 - 字段清空*@
                        <div className="guideLayerDom guideLayerDom2" style={guideClearFields.isShow ? {display: 'block'}:{display:'none'}}>
                            <div id="guideClearFields" style={{left:guideNavBar.left+'px',top:guideNavBar.top+'px',right: guideNavBar.right+'px', width:guideNavBar.width+'px'}}>
                                <div className="guideClearFields-content">
                                     <div className="n-dColor " ><i className="iconfont icon-a-setting1x1"></i>字段清空设置</div>
                                </div>
                                <div className="line-box">
                                    <div className="great-circle">
                                        <span className="small-circle"></span>
                                    </div>
                                    <div className="line"></div>
                                </div>
                            </div>
                            <div className="guideLayer-container" style={{display: 'block',left:guideClearFields.right+'px',top:guideClearFields.top+'px'}}>
                                <div className="guideLayer-title" >{guideClearFields.title}</div>
                                <div className="guideLayer-content" >
                                    {guideClearFields.text}
                                </div>
                                <div className="guideLayer-btn" >
                                    <div className="n-mButton" style={(guideClearFields.step) == guideClearFields.total ? {display: 'block'}:{display:'none'}} onClick={()=> this.onGuideClearFieldsLayer(guideClearFields.step + 1)}>我知道了</div>
                                    <div className="n-mButton" style={(guideClearFields.step) !=guideClearFields.total ? {display: 'block'}:{display:'none'}} onClick={()=> this.onGuideClearFieldsLayer(guideClearFields.step + 1)}>下一步({guideClearFields.step}/{guideClearFields.total})</div>
                                </div>
                            </div>
                        </div>
                        <div className="n-breadcrumb" style={{width:'100%'}}>
                            <span className="n-breadcrumb-title">
                                <i className="c04">基础商品 /</i><i className="c09" style={{fontWeight:'600'}}>{activeNavsTitle}</i>
                            </span>
                            <div class="headerBtns flex">
                                <span className="n-mButton n-sActive mR8 n-BatchButton n-downBatchButton">更多
                                    <i class="iconfont icon-a-chevron-up1x"></i>
                                    <ul class="n-BatchButton-ul">
                                        <li onClick={()=>this.onSetting()}>设置</li>
                                        <li onClick={()=>this.showOperateLogo()}>操作日志</li>
                                        <li onClick={()=>{window.open('https://m0ij9216j9.feishu.cn/drive/folder/ZJS0fQs44l3OmPdKHjacEb0JnIR', '_blank')}}>使用教程</li>
                                        <li onClick={()=>this.showLevelSellPriceDialog()}>等级分销价</li>
                                    </ul>
                                </span>
                                <div class="n-mButton n-sActive mR8" onClick={()=> this.showCombinationProductDialog()}>创建组合货品</div>
                                <div class="n-mButton n-sActive mR8" onClick={()=>this.createOrEdmitProduct('create','')}>创建基础商品</div>
                                <div class="n-mButton" onClick={()=>this.onAddProduct()}><i class="iconfont icon-a-magic1x"></i>快速添加</div>
                            </div>
                        </div>

                        {
							isHasAbnormal?
							<div class="n-alert n-alert-02 productLibrary-warn fz14">
								<i class="iconfont icon-gantan"></i>
								<span>发现关联异常，请立即处理。</span>
								<span class="hover n-dColor fz12" onClick={()=>{this.onShowExceptionLog()}}>查看异常<s class="iconfont icon-a-chevron-right1x"></s></span>
								<span class="icon-a-close1x iconfont" style={{ fontSize: '16px'}} onClick={()=>{this.setState({isHasAbnormal:false})}}></span>
							</div>:null
						}
                        <div className="n-main" style={{width: "100%"}}>
                            <div className="n-layui-mywrap" style={{ padding: "16px 0" }}>
                                <div className="query-form" style={{position: 'relative'}}>
                                    <form className="layui-form layui-form-pane n-layui-form " action="">

                                        <div className="layui-form-item layui-row layui-col-space8" style={{ marginBottom: '12px',minHeight: '66px' }}>
                                            <div className="layui-col-md4 layui-col-lg3" id="baseProductNavsDom">
                                                <div className="layui-input-inline-label n-flex" >
                                                    <ul className="n-box-nav">
                                                        {
                                                            baseProductNavs.map((item, i) => {
                                                                return (
                                                                    <li onClick={() => this.changeStatusNavs(item.name)} className={item.isActive ? "n-box-nav-li active" : "n-box-nav-li"}>{item.title}</li>
                                                                )
                                                            })
                                                        }
                                                    </ul>
                                                </div>
                                            </div>
                                            {
                                                searchData.map((item) => {
                                                    return (
                                                        item.isSelect ?
                                                            item.type == 'input' ?
                                                                <div className="layui-col-md4 layui-col-lg3">
                                                                    <div className={item.value != "" ? "n-layui-input n-focus active" : "n-layui-input n-focus"}>
                                                                        {
                                                                            item.name != 'SkuUid' ?
                                                                            <span className='label'>{item.title}</span>:
                                                                            <span className='label flex'>
                                                                            {item.title}
                                                                            <i className="commonWenHao02 popoverCommon hovericon iconfont icon-a-help-circle1x">
                                                                                <span className="popoverCommon-warn" style={{ width: '180px', left: '-12px'}}>SKUID为店铺商品中规格编码</span>
                                                                            </i>
                                                                            </span>
                                                                        }
                                                                        
                                                                        <input value={item.value} type="text" onChange={(e) => this.changeSeach(e, item.name)} name={item.name} className="layui-input" placeholder="请输入" />
                                                                    </div>
                                                                </div>
                                                                : item.type == 'select' ?
                                                                    <div className={item.name == "IsWarn" ? "layui-col-md4 layui-col-lg3 isShowWarn" : "layui-col-md4 layui-col-lg3"} style={{ display: 'flex' }}>
                                                                        <div class="selectWrap" id={item.name}></div>
                                                                    </div>
                                                                    : null : null
                                                    )
                                                })

                                            }
                                            <div className="layui-input-inline flex" style={{ marginBottom: '0px' }}>
                                                <span className="n-mButton n-pActive wu-btn wu-btn-mid wu-primary wu-two" onClick={() => this.searchLoadList()}>查询</span>
                                                <span className="n-mButton n-sActive wu-btn wu-btn-mid wu-primary" onClick={() => this.reset()}>重置</span>
                                            </div>
                                        </div>
                                    </form>
                                    
                                </div>
                                <div class="layui-mytable-operate newOperation" style={{ display: 'flex'}}>
                                    <div class="layui-mytable-operate-wran" style={{marginRight: '16px'}}>
                                        <span class="n-dColor hover fz14" onClick={() => this.opanStockControl()}>
                                            <i class="iconfont icon-a-setting1x1 fz14" style={{marginRight: '3px'}}></i>库存设置
                                        </span>
                                    </div>
                                    <div class="layui-mytable-operate-wran" id="operate_wran_open">
                                        <span class="n-dColor hover fz14" onClick={() => this.openWarehouseWarn()}>
                                            <i class="iconfont icon-forewarning fz14" style={{marginRight: '3px'}}></i>开启库存预警
                                        </span>
                                    </div>
                                    <div class="layui-mytable-operate-wran hide" id="operate_wran_close">
                                        <span class="hover fz14"style={{ paddingRight: '8px', color: '#DC8715'}} onClick={() => this.closeWarehouseWarn()}>
                                            <i class="iconfont icon-forewarning fz14" style={{ marginRight: '3px' }} ></i>关闭库存预警
                                        </span>
                                        <span class="n-dColor hover fz14" style={{ marginLeft: '8px' }} onClick={() => this.openWarehouseWarn()}>编辑</span>
                                    </div>
                                </div>
                                <div className={ activeNavsName == "skuStatus" ? "layui-mytable custom-scrollbar" : "layui-mytable"}>
                                    {
                                        activeNavsName == "productStatus" ?
                                            <table className="n-table stockup_table_content new_product_table">
                                                <thead>
                                                    <tr>
                                                        <th style={{ minWidth: '300px' }} className="pL16">
                                                            <div className="c09" style={{ display: "flex", alignItems: "center" }}>
                                                                <span className={ allCheckSku ? "n-newCheckbox activeF": partialSelect ? "n-newCheckbox activeP":"n-newCheckbox"} onClick={() => { this.onCheckAllSku() }}></span>商品信息
                                                            </div>
                                                        </th>
                                                        <th style={{ minWidth: '180px' }}>
                                                            <span className="c09">简称</span>
                                                        </th>
                                                        <th style={{ minWidth: '180px' }}>
                                                            <span className="c09">供货方式</span>
                                                        </th>
                                                        <th style={{ minWidth: '140px',textAlign: 'right' }}>
                                                            <span className="c09">包含规格</span>
                                                        </th>
                                                        <th style={{ minWidth: '140px' }}>
                                                            <span className="c09">分销资料</span>
                                                        </th>
                                                        <th style={{ minWidth: '140px' }}>
                                                            <span className="c09">操作</span>
                                                        </th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {
                                                        this.state.list.length > 0 ? this.state.list.map((item, index) => {
                                                            var listItem = []
                                                            listItem[0] = <tr key={item.Id} className='pL16 common-product-tr'>
                                                                <td >
                                                                    <div className="productWrap flex" >
                                                                        <span className={item.isCheck ? "n-newCheckbox activeF":"n-newCheckbox"}  name="productCheckbox" onClick={(e)=>{e.stopPropagation();this.onCheckSku(item, index)}}></span>
                                                                        <div className="productShow flex">
                                                                            {
                                                                                item.MainImageUrl ?  <img src={item.MainImageUrl} className="productShow-img" /> : <img src="/Content/images/nopic.gif" className="productShow-img" />
                                                                            }
                                                                            @*悬浮放大暂时用不到，先隐藏*@
                                                                            @*{
                                                                                item.MainImageUrl ?<div className="image-container">
                                                                                                        <img src={item.MainImageUrl} className="productShow-img" />
                                                                                                        <div className="zoom-lens">
                                                                                                            <div className="big-img">
                                                                                                                <img src={item.MainImageUrl} />
                                                                                                            </div>
                                                                                                            <div className="goodsName">【爆款新品】秋季男士新款舒适低帮透气百搭男士休闲鞋一脚蹬男鞋</div>
                                                                                                            <div className="specification-box">
                                                                                                                <span className="fz12 c06">规格属性：</span>
                                                                                                                <span className="specification-text fz12 c06" style={{ marginRight: '2px' }}>白蓝色</span>
                                                                                                                <span className="specification-text fz12 c06">39</span>
                                                                                                            </div>
                                                                                                        </div>
                                                                                                    </div>
                                                                                                :<img src="/Content/images/nopic.gif" className="productShow-img" />
                                                                            }*@
                                                                            <ul className="product-title">
                                                                                <li title={item.Subject}>
                                                                                    {
                                                                                        item.IsCombination ? <span className="text">组合</span> : null
                                                                                    }
                                                                                    <span className="fz12">{item.Subject}</span>
                                                                                </li>
                                                                                <li style={{ marginTop: '4px' }} className="c04 fz12 flex">商品编码：<span id={'productStatus_SpuCode'+item.Id}>{item.SpuCode}</span><i className="iconfont icon-a-copy1x common-product-icon" onClick={(e)=>{e.stopPropagation();this.onCopySpuCode('productStatus_SpuCode'+item.Id)}}></i></li>
                                                                            </ul>
                                                                        </div>
                                                                    </div>
                                                                </td>
                                                                <td style={{ textAlign: 'left' }} onClick={()=>this.onColumnPrice('ShortTitle',item,index,'spu')}>
                                                                    <span className="fz14 c09 flex">{item.ShortTitle || '-'}<i className="iconfont icon-a-edit1x common-product-icon"></i></span>
                                                                </td>
                                                                <td style={{ textAlign: 'left' }} onClick={(e)=>{e.stopPropagation();}}>
                                                                    {this.onSupplierFormat(item,index,'spu')}
                                                                </td>


                                                                <td style={{ textAlign: 'right' }} onClick={(e)=>{e.stopPropagation();this.onColumnSku(item.SpuCode)}}>
                                                                    <div className="fz14" style={{color: '#0888FF'}}>{item.SkuCount || 0}</div>
                                                                </td>
                                                                <td style={{ textAlign: 'left' }}>
                                                                    <div className="column-platformList flex">
                                                                        {
                                                                            item.PlatformInfoTags.length>0 && item.PlatformInfoTags.map((citem,i)=>{
                                                                                let listDom=null;

                                                                                if(citem.PlatformType == 'PuHuo') {
                                                                                    listDom=<MyPopover
                                                                                            placement="bottom"
                                                                                            content={citem.IsHighlight ? '小站商品' : '小站商品: 未完善'}
                                                                                            trigger='hover'
                                                                                        >
                                                                                        {
                                                                                            citem.IsHighlight ?
                                                                                            <span style={{marginLeft: '0px'}} className="hover wu-pintaiIcon wu-small Dgj wu-mR4" onClick={()=>this.onColumnBusinessCardEdit(item,index,'edmit','Base')}></span>
                                                                                            :<span style={{marginLeft: '0px'}} className="hover wu-pintaiIcon wu-small Dgj opacity02 wu-mR4" ></span>
                                                                                        }
                                                                                        </MyPopover>
                                                                                }
                                                                                if(citem.PlatformType == 'TouTiao' || citem.PlatformType == 'Toutiao') {
                                                                                        listDom=<MyPopover
                                                                                            placement="bottom"
                                                                                            content={citem.IsHighlight ? '抖音商品' : '抖音商品: 未完善'}
                                                                                            trigger='hover'
                                                                                        >
                                                                                        {
                                                                                            citem.IsHighlight ?
                                                                                            <span style={{marginLeft: '0px'}} className="hover wu-pintaiIcon wu-small TouTiao" onClick={()=>this.onColumnEdit(item,index,'edmit','TouTiao')}></span>
                                                                                            :<span style={{marginLeft: '0px'}} className="hover wu-pintaiIcon wu-small TouTiao opacity02" onClick={()=>this.onColumnEdit(item,index,'edmit','TouTiao')}></span>
                                                                                        }
                                                                                        </MyPopover>
                                                                                }
                                                                                return listDom
                                                                            })

                                                                        }
                                                                    </div>
                                                                </td>
                                                                <td style={{ textAlign: 'left' }} className='pR16'>
                                                                    <span className="n-dColor hover fz14" onClick={(e)=>{e.stopPropagation();this.onColumnEdit(item,index,'edmit')}}>编辑</span>
                                                                    <div className="popoverCommon popoverMore">
                                                                        <span className="n-dColor hover popoverCommon" style={{ marginLeft: '10px'}}>
                                                                        更多
                                                                        <i className="iconfont icon-down icon-more"></i>
                                                                        </span>
                                                                        <div className="popoverCommon-warn">
                                                                            <span class="n-dColor hover" onClick={(e)=>{e.stopPropagation();this.onBtnBatch('site', item)}}>上架到小站</span>
                                                                            <span class="n-dColor hover" onClick={(e)=>{e.stopPropagation();this.onBtnBatch('shop',item)}}>上架到店铺</span>
                                                                            {
                                                                                item.IsCombination ? null : <span class="n-dColor hover" onClick={(e)=>{e.stopPropagation();this.onColumnEdit(item,index,'copy')}}>复制</span>
                                                                             }
                                                                            <span class="n-sColor hover " onClick={(e)=>{e.stopPropagation();this.onBtnBatchDelete('single', item, index)}}>删除</span>
                                                                        </div>
                                                                    </div>

                                                                </td>
                                                            </tr>
                                                            return listItem
                                                        }) :
                                                            <tr>
                                                                <td colSpan="20" className="tdNodata" style={{ padding: "24px 16px" }}>
                                                                    <div className="n-tableNoDataShow">
                                                                        <span className="iconfont icon-wushuju1"></span>
                                                                        <span className="tableNoDataShow-title">未找到符合条件的内容</span>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                    }

                                                </tbody>
                                            </table>
                                            : activeNavsName == "skuStatus" ?
                                            <table className="n-table stockup_table_content new_product_table">
                                                <thead>
                                                    <tr>
                                                        <th style={{ minWidth: '280px' }} className="pL16">
                                                            <div className="c09" style={{ display: "flex", alignItems: "center" }}>
                                                                <span className={ allCheckSku ? "n-newCheckbox activeF": partialSelect ? "n-newCheckbox activeP":"n-newCheckbox"} onClick={()=>{this.onCheckAllSku()}}></span>规格信息
                                                            </div>
                                                        </th>
                                                        <th style={{ minWidth: '120px' }}>
                                                            <span className="c09">简称</span>
                                                        </th>
                                                        <th style={{ minWidth: '80px',textAlign: 'right' }}>
                                                            <span className="c09">同款数</span>
                                                        </th>
                                                        <th style={{ minWidth: '100px' }}>
                                                            <div className="t-r flex" style={{ justifyContent: 'flex-end'}}>
                                                                <span className="c09">采购价</span>
                                                                <i className="commonWenHao02 popoverCommon hovericon iconfont icon-a-help-circle1x">

                                                                    <span className="popoverCommon-warn" style={{ width: '260px', top: '20px', left: '-10px' }}>采购价应用于财务结算对厂家设置的规格单品结算价</span>
                                                                </i>
                                                            </div>
                                                        </th>
                                                        <th style={{ minWidth: '100px' }}>
                                                            <div className="t-r flex" style={{ justifyContent: 'flex-end'}}>
                                                                <span className="c09">成本价</span>
                                                                <i className="commonWenHao02 popoverCommon hovericon iconfont icon-a-help-circle1x">

                                                                    <span className="popoverCommon-warn" style={{ width: '260px', top: '20px', left: '-10px' }}>成本价应用于店铺对账时使用的规格单品成本价</span>
                                                                </i>
                                                            </div>
                                                        </th>
                                                        <th style={{ minWidth: '100px' }}>
                                                            <div className="t-r flex" style={{ justifyContent: 'flex-end'}}>
                                                                <span className="c09">默认分销价</span>
                                                                <i className="commonWenHao02 popoverCommon hovericon iconfont icon-a-help-circle1x">

                                                                    <span className="popoverCommon-warn" style={{ width: '260px', top: '20px', left: '-10px' }}>对商家结算价=默认分销价换算等级分销价规则之后的最终结算价</span>
                                                                </i>
                                                            </div>
                                                        </th>
                                                        <th style={{ minWidth: '140px' }}>
                                                            <span className="c09">供货方式</span>
                                                        </th>

                                                        <th style={{ width: '128px',    backgroundColor: '#F5F5F5' }}>
                                                            <span className="c09">操作</span>
                                                        </th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {
                                                        this.state.list.length > 0 ? this.state.list.map((item, index) => {
                                                            var listItem = []
                                                            listItem[0] = <tr className='common-product-tr' style={{display: (index && item.BaseProductUidStr != list[index-1].BaseProductUidStr) || index === 0 ? 'table-row' : 'none'}}>
                                                                <td colSpan="20" style={{ background: '#fff', padding: '0px'}}>
                                                                    <div className="combination_goods_title">
                                                                        <span
                                                                           className={item.isAllCheckSubject? "n-newCheckbox activeF" : item.isPartCheckSubject? "n-newCheckbox activeP" : "n-newCheckbox"}
                                                                           onClick={(e)=>{e.stopPropagation();this.onCheckSku(item, index, 'spu')}}
                                                                        ></span>
                                                                        {
                                                                            item.IsCombineSpu ? <span className="text" onMouseEnter={(e)=>{e.stopPropagation();this.onMouseOver(e,item, index)}}>组合</span> : null
                                                                        }
                                                                        <span className="combination_goods_name combination_copy_box" title={item.ProSubject}>
                                                                            <span id={"ProSubject_"+item.Id}>{item.ProSubject}</span>
                                                                            <i className="iconfont icon-a-copy1x common-product-icon" onClick={(e)=>{e.stopPropagation();this.onCopySpuCode("ProSubject_"+item.Id)}}></i>
                                                                        </span>
                                                                        {
                                                                            item.ProShortTitle ? 
                                                                            <span className="mL12 combination_copy_box" >商品简称：
                                                                            <span id={"ProShortTitle_"+item.Id}>{item.ProShortTitle}</span>
                                                                            <i className="iconfont icon-a-copy1x common-product-icon" onClick={(e)=>{e.stopPropagation();this.onCopySpuCode("ProShortTitle_"+item.Id)}}></i>
                                                                            </span>
                                                                            : null
                                                                        }
                                                                        {
                                                                            item.SpuCode ? 
                                                                            <span className="mL12 combination_copy_box">商品编码：
                                                                            <span id={"SpuCode_"+item.Id}>{item.SpuCode}</span>
                                                                            <i className="iconfont icon-a-copy1x common-product-icon" onClick={(e)=>{e.stopPropagation();this.onCopySpuCode("SpuCode_"+item.Id)}}></i>
                                                                            </span>
                                                                            : null
                                                                        }
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            listItem[1] = <tr key={item.Id} className='pL16 common-product-tr'>
                                                                        <td>
                                                                            <div className="productWrap flex">
                                                                                <span className={ item.isCheck ? "n-newCheckbox activeF":"n-newCheckbox"} name="productCheckbox" onClick={(e)=>{e.stopPropagation();this.onCheckSku(item, index)}}></span>
                                                                                <div className="productShow flex">
                                                                                    {
                                                                                        item.ImageUrl ?<img src={item.ImageUrl} className="productShow-img" />:<img src="/Content/images/nopic.gif" className="productShow-img" />
                                                                                    }

                                                                                    <ul className="product-title">
                                                                                        <li className="fz12" title={item.AttributeValue}>
                                                                                            <span>{item.AttributeValue}</span>
                                                                                        </li>
                                                                                        <li style={{ marginTop: '4px' }} className="c04 fz12 flex">SKU编码：<span id={'skuStatus_SkuCode'+item.Id}>{item.SkuCode}</span><i className="iconfont icon-a-copy1x common-product-icon" onClick={(e)=>{e.stopPropagation();this.onCopySpuCode('skuStatus_SkuCode'+item.Id)}}></i></li>
                                                                                    </ul>
                                                                                </div>
                                                                            </div>
                                                                        </td>
                                                                        <td className="hover" style={{ textAlign: 'left' }}>
                                                                            <div className="flex" onClick={(e)=>{e.stopPropagation();this.onColumnPrice('ShortTitle',item,index,'sku')}}>
                                                                                <span className="fz14 c09">{item.ShortTitle || '-'}</span><i className="iconfont icon-a-edit1x hovericon" ></i>
                                                                            </div>
                                                                        </td>
                                                                        <td style={{ textAlign: 'right' }} >
                                                                            <div className="fz14 n-dColor" onClick={()=>this.goTarUrl('/BaseProduct/BaseProductSkuRelationBindIndex?baseProductSkuUid='+item.UidStr+'&baseProductUid='+item.BaseProductUidStr+'&relationCount='+item.RelationCount)}>{item.RelationCount}</div>
                                                                        </td>
                                                                        <td className="hover">
                                                                            <div className='flex' onClick={(e)=>{e.stopPropagation();this.onColumnPrice('SettlePrice',item,index,'sku')}} style={{justifyContent:"flex-end",textAlign: 'right',wordBreak: 'break-word'}}>
                                                                                <span className="fz14 c09">{this.onPriceFormat(item.SettlePrice)}</span><i className="iconfont icon-a-edit1x hovericon" ></i>
                                                                            </div>
                                                                        </td>

                                                                        <td className="hover">
                                                                            <div className='flex' onClick={(e)=>{e.stopPropagation();this.onColumnPrice('CostPrice',item,index,'sku')}} style={{justifyContent:"flex-end",textAlign: 'right',wordBreak: 'break-word'}}>
                                                                                <span className="fz14 c09">{this.onPriceFormat(item.CostPrice)}</span><i className="iconfont icon-a-edit1x hovericon" ></i>
                                                                            </div>
                                                                        </td>

                                                                        <td className="hover">
                                                                            <div className='flex' onClick={(e)=>{e.stopPropagation();this.onColumnPrice('DistributePrice',item,index,'sku')}} style={{justifyContent:"flex-end",textAlign: 'right',wordBreak: 'break-word'}}>
                                                                                <span className="fz14 c09">{this.onPriceFormat(item.DistributePrice)}</span><i className="iconfont icon-a-edit1x hovericon" ></i>
                                                                            </div>
                                                                        </td>
                                                                        <td className="hover" style={{ textAlign: 'left' }} onClick={(e)=>{e.stopPropagation();}}>
                                                                            @*className={item.SupplierFxUserId >0 ? "grid":'' }*@
                                                                            <div onClick={()=>this.onColumnSpuSupply(item, index, 'sku')}>
                                                                            {
                                                                                item.SkuSupplierUserId == 0 ?
                                                                                <span className="n-tarTxt n-tarTxt05 hover"><span className='flex'>未设置<i className="iconfont icon-a-edit1x common-product-icon common-product-icon2"></i></span></span>
                                                                                :
                                                                                item.SkuSupplierUserId==item.FxUserId ?
                                                                                    <span className="n-tarTxt n-tarTxt05"><span className='flex'>自营<i className="iconfont icon-a-edit1x common-product-icon common-product-icon2"></i></span></span>
                                                                                    :
                                                                                    <div>
                                                                                        <span className="n-tarTxt n-tarTxt04"><span className='flex'>厂家<i className="iconfont icon-a-edit1x common-product-icon common-product-icon2"></i></span></span>
                                                                                        <span className="ellipsis grid-column c06 fz12 flex" title={item.SupplierName}>{item.SupplierName}</span>
                                                                                    </div>
                                                                            }
                                                                            </div>
                                                                        </td>
                                                                        <td style={{ textAlign: 'left',whiteSpace: 'nowrap' }} className='pR16'>
                                                                            <span className="n-dColor hover fz14" style={{ marginRight: '12px' }} onClick={()=>this.onRelationProduct(item)}>关联同款</span>
                                                                            <span className="n-dColor hover fz14" style={{ marginRight: '12px' }} onClick={()=>this.goTarUrl('/BaseProduct/BaseProductSkuRelationBindIndex?baseProductSkuUid='+item.UidStr+'&baseProductUid='+item.BaseProductUidStr+'&relationCount='+item.RelationCount)}>同款明细</span>
                                                                            {
                                                                                item.RelationCount ?
                                                                                <span className="n-sColor hover fz14"  onClick={()=>this.onRelationProduct(item,'unbind')}>解绑同款</span>
                                                                                : null
                                                                            }
                                                                            
                                                                        </td>
                                                                    </tr>

                                                            return listItem
                                                        }) :
                                                            <tr>
                                                                <td colSpan="20" className="tdNodata" style={{ padding: "24px 16px" }}>
                                                                    <div className="n-tableNoDataShow">
                                                                        <span className="iconfont icon-wushuju1"></span>
                                                                        <span className="tableNoDataShow-title">未找到符合条件的内容</span>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                    }

                                                </tbody>
                                            </table>
                                            :activeNavsName == "stockStatus" ?
                                            <table className="n-table stockup_table_content new_product_table">
                                                <thead>
                                                    <tr>
                                                        <th style={{ minWidth: '280px' }} className="pL16" >
                                                            <div className="c09" style={{ display: "flex", alignItems: "center" }}>
                                                                <span className={ allCheckSku ? "n-newCheckbox activeF": partialSelect ? "n-newCheckbox activeP":"n-newCheckbox"} onClick={() => { this.onCheckAllSku() }}></span>规格信息
                                                            </div>
                                                        </th>
                                                        <th style={{ minWidth: '180px' }}>
                                                            <span className="c09">简称</span>
                                                        </th>
                                                        <th style={{ minWidth: '160px',width: '160px' }}>
                                                            <div className="t-r flex" style={{ justifyContent: 'flex-start'}}>
                                                                <span className="c09">子货品出库明细</span>
                                                                <i className="commonWenHao02 popoverCommon hovericon iconfont icon-a-help-circle1x">
                                                                    <span className="popoverCommon-warn" style={{ width: '220px', top: '20px', left: '-10px' }}>组合货品中单个子规格具体扣减数目</span>
                                                                </i>
                                                            </div>
                                                        </th>
                                                        <th style={{ minWidth: '130px',width: '130px' }}>
                                                            <div className="t-r flex" style={{ justifyContent: 'flex-end' }}>
                                                                <span className="c09">子货品可组合数量</span>
                                                                <i className="commonWenHao02 popoverCommon hovericon iconfont icon-a-help-circle1x">
                                                                    <span className="popoverCommon-warn" style={{ width: '250px', top: '20px', left: '-10px' }}>最终可组合数量等于子规格中最小值的数量</span>
                                                                </i>
                                                            </div>
                                                        </th>
                                                        <th style={{ minWidth: '104px',textAlign: 'right' }}>
                                                            <span className="c09">入库数量</span>
                                                        </th>
                                                        <th style={{ minWidth: '104px',textAlign: 'right' }}>
                                                            <span className="c09">出库数量</span>
                                                        </th>
                                                        <th style={{ minWidth: '104px',textAlign: 'right' }}>
                                                            <span className="c09">剩余库存</span>
                                                        </th>
                                                        <th style={{ minWidth: '80px' }}>
                                                            <span className="c09">操作</span>
                                                        </th>
                                                    </tr>
                                                </thead>
                                                <tbody >

                                                    {
                                                        this.state.list.length > 0 ? this.state.list.map((item, index) => {
                                                            var listItem = []
                                                            listItem[0] = <tr className='common-product-tr'>
                                                                <td colSpan="20" style={{ background: '#fff', padding: '0px' }}>

                                                                    <div className="combination_goods_title">
                                                                        <span
                                                                            className={item.isAllCheck? "n-newCheckbox activeF" : item.isPartCheck? "n-newCheckbox activeP" : "n-newCheckbox"}
                                                                            onClick={(e)=>{e.stopPropagation();this.onCheckWareHouse(item, index)}}
                                                                        ></span>
                                                                        {
                                                                            item.ItemType == 'ZH' ? <span className="text" onMouseEnter={(e)=>{e.stopPropagation();this.onMouseOver(e,item, index)}}>组合</span>: null
                                                                        }
                                                                       
                                                                        <span className="combination_goods_name combination_copy_box" title={item.ProductSubject}>
                                                                            <span id={"ProductSubject_"+item.Id}>{item.ProductSubject}</span>
                                                                            <i className="iconfont icon-a-copy1x common-product-icon" onClick={(e)=>{e.stopPropagation();this.onCopySpuCode("ProductSubject_"+item.Id)}}></i>
                                                                        </span>
                                                                        {
                                                                            item.ProductShortName ? 
                                                                            <span className="mL12 combination_copy_box" >商品简称：
                                                                            <span id={"ProductShortName_"+item.Id}>{item.ProductShortName}</span>
                                                                            <i className="iconfont icon-a-copy1x common-product-icon" onClick={(e)=>{e.stopPropagation();this.onCopySpuCode("ProductShortName_"+item.Id)}}></i>
                                                                            </span>
                                                                            : null
                                                                        }
                                                                        {
                                                                            item.ProductCargoNumber ? 
                                                                            <span className="mL12 combination_copy_box">商品编码：
                                                                            <span id={"ProductCargoNumber_"+item.Id}>{item.ProductCargoNumber}</span>
                                                                            <i className="iconfont icon-a-copy1x common-product-icon" onClick={(e)=>{e.stopPropagation();this.onCopySpuCode("ProductCargoNumber_"+item.Id)}}></i>
                                                                            </span>
                                                                            : null
                                                                        }
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            listItem[1] = item.WareHouseSkus && item.WareHouseSkus.length > 0 ? item.WareHouseSkus.map((jData, j) => {
                                                                return (
                                                                    <tr key={jData.Id} className='pL16 common-product-tr'>
                                                                        <td >
                                                                            <div className="productWrap flex" >
                                                                                <span className={ jData.isCheck ? "n-newCheckbox activeF":"n-newCheckbox"} name="productCheckbox" onClick={(e)=>{e.stopPropagation();this.onCheckWareHouseSku(jData.Id)}} ></span>
                                                                                <div className="productShow flex">
                                                                                    {
                                                                                        jData.ImageUrl ?<img src={jData.ImageUrl} className="productShow-img" />:<img src="/Content/images/nopic.gif" className="productShow-img" />
                                                                                    }

                                                                                    <ul className="product-title">
                                                                                        <li className="fz14 ellipsis" title={this.onReplaceText(jData.SkuProperty)}>
                                                                                            <span>{this.onReplaceText(jData.SkuProperty)}</span>
                                                                                        </li>
                                                                                        <li style={{ marginTop: '4px' }} className="c04 fz12 flex">SKU编码：<span id={'stockStatus_SkuCode'+jData.Id}>{jData.SkuCargoNumber}</span><i className="iconfont icon-a-copy1x common-product-icon" onClick={(e)=>{e.stopPropagation();this.onCopySpuCode('stockStatus_SkuCode'+jData.Id)}}></i></li>
                                                                                    </ul>
                                                                                </div>
                                                                            </div>
                                                                        </td>
                                                                        <td >
                                                                            <span className="fz14 c09">{jData.ShortName || '-'}</span>
                                                                        </td>
                                                                        <td className="hover">
                                                                           <div className={jData.isMore ? "childSkusBox isMore" : "childSkusBox"}>
                                                                                <ul className="flex-column">
                                                                                    {
                                                                                        jData.ChildSkus && jData.ChildSkus.length ? jData.ChildSkus.map((ChildSkuItem, ChildSkuIndex) => {
                                                                                            return  <li className="flex fs12 grid"><span className="c09 ellipsis grid-column">{this.onReplaceText(ChildSkuItem.Name)}</span><span className="colorDC8 grid-column-2">x{ChildSkuItem.Count}</span></li>
                                                                                        }):'-'
                                                                                    }
                                                                                </ul>

                                                                            </div>
                                                                            {
                                                                                jData.ChildSkus && jData.ChildSkus.length ?
                                                                                <span className="dColor hover mR4" onClick={(e)=>{e.stopPropagation(); this.onEditCombination(jData.ChildSkus)}}>编辑</span>:null
                                                                            }
                                                                            {
                                                                                jData.ChildSkus && jData.ChildSkus.length>4 ?
                                                                                jData.isMore ? <span className="dColor hover" onClick={(e)=>{e.stopPropagation();this.onShowChildSkusMore(jData.Id)}}>收起 </span>
                                                                                : <span className="dColor hover" onClick={(e)=>{e.stopPropagation();this.onShowChildSkusMore(jData.Id)}}>查看更多</span>
                                                                                :null
                                                                            }

                                                                        </td>
                                                                        <td className="hover" style={{ textAlign: 'right' }}>
                                                                           <span className="fz14 c09">{jData.ComposeChildStockCount || '-'}</span>
                                                                        </td>
                                                                        <td className="hover">
                                                                            <div className="flex" style={{ justifyContent: 'flex-end' }}>
                                                                                {
                                                                                    jData.isShowInListInput?
                                                                                    <div className="flex">
                                                                                        <div className="flex fz14">
                                                                                             <span className="c09">{jData.SumStockInCount}</span>
                                                                                             <span className="c04" style={{ margin: '0 2px' }}>+</span>
                                                                                        </div>
                                                                                        <div className="flex" style={{ position: 'relative', width: '100px' }}>
                                                                                            <input
                                                                                                type="text"
                                                                                                className="layui-input n-layui-input"
                                                                                                style={{ paddingRight: '44px' }} placeholder="请输入"
                                                                                                onBlur={() =>{this.handleBlur(jData, index, event, 'InList') }}
                                                                                             />
                                                                                            <span class="input-num n-dColor hover fz14" onClick={(e)=>{e.stopPropagation(); this.onRemarkModal(jData, index, 'InList')}}>
                                                                                                  备注
                                                                                              </span>
                                                                                        </div>
                                                                                    </div>
                                                                                    :
                                                                                    <span>{jData.SumStockInCount || '-'}
                                                                                            <span className="popoverCommon-warn popoverCommon-warn03" style={{ width: '272px', top: '20px', left: '-10px',height: '254px'}}>最终可组合数量等于子规格中最小值的数量</span>
                                                                                            <i className="iconfont icon-a-edit1x c04 hover common-product-icon" onClick={(e)=>{e.stopPropagation();this.onMouseOverInventory(e,jData, index, 'InList')}}></i>
                                                                                    </span>

                                                                                    // onClick={}}
                                                                                }
                                                                            </div>
                                                                        </td>
                                                                        <td className="hover">
                                                                              <div className="flex" style={{ justifyContent: 'flex-end' }}>
                                                                                   {
                                                                                      jData.isShowOutListInput?
                                                                                      <div className="flex">
                                                                                          <div className="flex fz14">
                                                                                               <span className="c09">{Math.abs(jData.SumStockOutCount)}</span>
                                                                                               <span className="c04" style={{ margin: '0 2px' }}>+</span>
                                                                                          </div>
                                                                                          <div className="flex" style={{ position: 'relative', width: '100px' }}>
                                                                                              <input type="text" className="layui-input n-layui-input"
                                                                                              style={{ paddingRight: '44px' }} placeholder="请输入"
                                                                                              onBlur={() =>{this.handleBlur(jData, index, event, 'OutList')}} />
                                                                                              <span class="input-num n-dColor hover fz14" onClick={(e)=>{e.stopPropagation(); this.onRemarkModal(jData, index, 'OutList')}}>
                                                                                                  备注
                                                                                              </span>
                                                                                          </div>
                                                                                      </div>
                                                                                      :
                                                                                      <span>{jData.SumStockOutCount ? Math.abs(jData.SumStockOutCount) : '-'}

                                                                                      <i className="iconfont icon-a-edit1x c04 hover common-product-icon" onClick={(e)=>{e.stopPropagation();this.onMouseOverInventory(e,jData, index, 'OutList')}}></i>
                                                                                      </span>
                                                                                   }
                                                                              </div>
                                                                        </td>
                                                                        <td className="">
                                                                            <div className='flex' style={{justifyContent:"flex-end",textAlign: 'right',wordBreak: 'break-word'}}>
                                                                                {
                                                                                    jData.IsWarn == 1 ?
                                                                                    <span className="fz14" style={{color: '#EA572E'}}>{jData.StockCount}
                                                                                        <i class="iconfont icon-forewarning fz14" style={{marginRight: '3px'}}></i>
                                                                                    </span>
                                                                                    :<span className="fz14 c09">{jData.StockCount}</span>
                                                                                }
                                                                            </div>
                                                                        </td>

                                                                        <td style={{ textAlign: 'left' }} className='pR16'>
                                                                            <span className="flex n-dColor hover fz14" onClick={(e)=>{this.rewriteUrlToMainDomain('/StockControl/ChangeDetail')}}>库存明细</span>
                                                                            {
                                                                                item.ItemType == 'ZH' && jData.ChildSkus && jData.ChildSkus.length ? <span className="flex n-dColor hover fz14" onClick={(e)=>{this.openChangLog(item.Id)}}>变更日志</span>: null
                                                                            }

                                                                        </td>
                                                                    </tr>
                                                                )

                                                            }):null
                                                            return listItem
                                                        }) :
                                                            <tr>
                                                                <td colSpan="20" className="tdNodata" style={{ padding: "24px 16px" }}>
                                                                    <div className="n-tableNoDataShow">
                                                                        <span className="iconfont icon-wushuju1"></span>
                                                                        <span className="tableNoDataShow-title">未找到符合条件的内容</span>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                    }

                                                </tbody>
                                            </table>
                                            :null
                                    }
                                </div>
                            </div>
                        </div>
                        <div className="n-footerWrap" style={{width: "100%"}}>
                            <div class="n-footerWrap-left">
                                <div style={{display: "flex", alignItems: "center"}} onClick={()=>{this.onCheckAllSku()}}>
                                    <span className={ allCheckSku ? "n-newCheckbox activeF": partialSelect ? "n-newCheckbox activeP":"n-newCheckbox"}></span>
                                    <span className="c09">单页全选</span>
                                    <span className="c09 pL8">已选<i className="n-dColor pL4 pR4">{ checkSkuList.length }</i>条</span>
                                </div>
                                {
                                    activeNavsName == "productStatus" ?
                                        <div className="mL24">
                                            <span className="n-mButton mR8 n-BatchButton" onClick={()=>this.onBtnBatch('supply')}>设置供货方式</span>
                                            <MyPopover
                                                placement="top"
                                                content={popoverMsg}
                                                trigger='click'
                                                position='fixed'
                                                click={()=>this.onBtnBatch('site')}
                                            >
                                            <span className="n-mButton n-sActive mR8" >上架到小站</span>
                                            </MyPopover>
                                            <MyPopover
                                                placement="top"
                                                content={popoverMsg}
                                                trigger='click'
                                                position='fixed'
                                                click={()=>this.onBtnBatch('shop')}
                                            >
                                            <div className="n-mButton n-sActive mR8" id="guideMock2Dom">上架到店铺<div id="guideMock2" className="n-mButton n-sActive"  style={{display:'none'}}> 上架到店铺</div></div>
                                            </MyPopover>
                                            <span className="n-mButton n-sActive" onClick={()=>{this.onBtnBatchDelete('batch', null, null)}}>删除</span>
                                        </div> :
                                        activeNavsName == "skuStatus" ?
                                            <div className="mL24">
                                                <span className="n-mButton mR8 n-BatchButton">批量改价
                                                    <i class="iconfont icon-a-chevron-up1x"></i>
                                                    <ul class="n-BatchButton-ul">
                                                        <li onClick={()=>this.onColumnPrice('SettlePrice')}>采购价</li>
                                                        <li onClick={()=>this.onColumnPrice('CostPrice')}>成本价</li>
                                                        <li onClick={()=>this.onColumnPrice('DistributePrice')}>分销价</li>
                                                    </ul>
                                                </span>
                                                {
                                                    AutoRelationProces && AutoRelationProces.Process >= 0 && AutoRelationProces.Process < 100?
                                                    <span className="n-mButton n-sActive mR8">自动关联中 {AutoRelationProces.Process}%</span>
                                                    : <span className="n-mButton n-sActive mR8" onClick={()=>this.onBtnBatch('relation')}>编码关联同款</span>
                                                }
                                                <span className="n-mButton n-sActive mR8" onClick={()=>this.onBtnBatch('supply')}>设置供货方式</span>
                                                <span className="n-mButton n-sActive" onClick={()=>this.onBtnBatchDelete()}>批量删除</span>
                                            </div> :
                                            activeNavsName == "stockStatus" ?
                                                <div className="mL24">
                                                    <span className="n-mButton n-sActive mR8" onClick={()=>this.onShowInOutSkuModal('InList')}>统一入库</span>
                                                    <span className="n-mButton n-sActive" onClick={()=>this.onShowInOutSkuModal('OutList')}>统一出库</span>
                                                </div> : null
                                }
                            </div>
                            <div class="n-footerWrap-right">

                                <div class="layui-myPage" id="paging"></div>

                            </div>
                        </div>
                        <div id="dialogSupply" style={{ display: 'none'}}>
							<div class="dialog-wrap">
								<div className="title">{ dialogData.label }</div>
								<div className="dialog-wrap-cont">
									<label className="flex" for="">
										<input type="radio" name="supply" value='0' checked={dialogData.supply == '0' ? true : false} onChange={()=>this.onChangeSupply(0)} title="自营供货" /><span className="radioSpan">自营供货</span>
									</label>
									<label className="flex" for="">
										<input type="radio" name="supply" value='1' checked={dialogData.supply == '1' ? true : false} onChange={()=>this.onChangeSupply(1)}  title="厂家供货" /><span className="radioSpan">厂家供货</span>
										<div id="supplier_name_select" class="Supplier selectWrap" name="supplier-name-select" style={dialogData.supply == '1' ? {display:'block'}: { display: 'none' }}></div>
									</label>
								</div>
							</div>
							<div class="dialogBtns flex">
                                <div></div>
								<div class="btnBox flex">
									<div class="n-mButton n-sActive" onClick={()=>this.onDialogClose()}>取消</div>
									{
										dialogData.isSave ? <div class="n-mButton" onClick={()=>this.onSupplySaveCheck()}>保存</div> : <div class="n-mButton stop" >保存</div>
									}
								</div>
							</div>
						</div>
						<div id="dialogSupplyCheck" style={{ display: 'none'}}>
							<div class="dialog-wrap">
								<div className="dialog-wrap-cont">
									您勾选的商品中存在多个SKU绑定不同的厂家，请确认这些SKU是否跟随商品一起更改到最新供货方式？
								</div>
							</div>
							<div class="dialogBtns flex">
								<div></div>
								<div class="btnBox flex">
									<div class="n-mButton n-sActive" onClick={()=>this.onSupplySave(1)}>跳过已绑定厂家的SKU变更</div>
									{
										dialogData.isSave ? <div class="n-mButton" onClick={()=>this.onSupplySave(0)}>确认变更</div> : <div class="n-mButton stop" >确认变更</div>
									}
								</div>
							</div>
						</div>

                        <div id="dialogBindProduct" style={{ display: 'none'}}>
							<div class="dialog-wrap">
								<div className="title">{dialogData.label}</div>
								<div className="dialog-wrap-cont">
									<div className="table-field">
										{
											dialogData.selectFields.map((item,index)=>{
												var listItem = <div class="item-column-checkbox flex">
													<div className="flex item-column-checkbox-input">
														<input type="checkbox" disabled={item.absoluteChecked ||item.disabled} checked={item.absoluteChecked ||item.checked} onChange={(event)=>this.onDialogHandleCheckbox(item,index)}/>
													</div>
													<div>
														<div className="item-column-checkbox-title">{ item.name }</div>
														{
															item.tips ?
															<div className="flex tips">
																<i class="iconfont icon-a-info-circle1x"></i>
																<span>{item.tips}</span>
															</div>
															: ''
														}
													</div>
												</div>
												return listItem
											})
										}
									</div>
								</div>
							</div>
							<div class="dialogBtns flex">
								<div>对 <span class="count">{ dialogData.skuCount }</span> 个规格生效</div>
								<div class="btnBox flex">
									<div class="n-mButton n-sActive" onClick={()=>this.onDialogClose()}>取消</div>
									{
										dialogData.isSave ? <div id="goBackskinBtn" class="n-mButton goBackskinBtn" onClick={()=>this.onBindProductSave()}>确定</div> : <div id="goBackskinBtn_stop" class="n-mButton goBackskinBtn stop" >确定</div>
									}
								</div>
							</div>
						</div>

						<div id="dialogAutoRelationBind" style={{ display: 'none'}}>
							<div class="dialog-wrap">
								<div className="dialog-wrap-cont">
									<div className="explain flex">
										<img className="explain-img" src="/Content/Images/automaticExplanation.png" className="img"/>
										<div>
											<p className="n-font3">高效关联同款商品</p>
											<p style={{color: "rgba(0, 0, 0, 0.6)"}}>系统将识别店铺商品中相同的 SKU 编码，进行自动关联。</p>
										</div>
									</div>
								</div>
							</div>
							<div class="dialogBtns flex">
								<div></div>
								<div class="btnBox flex">
									<input type="checkbox" checked={updateFieldsChecked} onChange={(event)=>this.setState({updateFieldsChecked:!updateFieldsChecked})} />
                                    <span>更新商品信息</span>
									<div class="n-mButton n-sActive" onClick={()=>this.onDialogClose()}>取消</div>
									{
										dialogData.isSave ? <div id="goBackskinBtn" class="n-mButton goBackskinBtn" onClick={()=>this.onDialogAutoRelationBindNext()}>开始关联</div> : <div id="goBackskinBtn_stop" class="n-mButton goBackskinBtn stop" >开始关联</div>
									}
								</div>
							</div>
						</div>

						<div id="dialogAutoBindProduct" style={{ display: 'none'}}>
							<div class="dialog-wrap">
								<div className="dialog-wrap-cont flex">
									<div className="dialog-wrap-cont-statusbox flex">
										<span className="count">{AutoRelationProces.SuccessCount || 0}</span>
										<span>关联成功</span>
									</div>
									<div className="dialog-wrap-cont-statusbox flex">
										<span className="count" style={{color: "#EA572E"}}>{AutoRelationProces.FailCount || 0}</span>
										<span>关联异常</span>
									</div>
								</div>
							</div>
							<div class="dialogBtns flex">
								<div></div>
								<div class="btnBox flex">
									<div class="n-mButton n-sActive" onClick={()=>this.onDialogClose()}>关闭</div>
									<div class="n-mButton" style={{background: '#EA572E',display: 'none'}} onClick={()=>this.onAbortAssociation()}>中止关联</div>
								</div>
							</div>
						</div>
						<div id="dialogPrice" style={{ display: 'none'}}>
							<div class="dialog-wrap">
								<div className="title">{ dialogData.label }</div>
								<div className="dialog-wrap-cont">
									{
                                        dialogData.inputType == 'text' ?
                                        <div class="item-column-input flex">
                                            <input type="text" value={dialogData.inputValue} placeholder="请输入" onChange={(event)=>this.onDialogHandleChange(event)} />
                                        </div>
                                        :<div class="item-column-input flex">
                                            <span>￥</span>
                                            <input type="number" value={dialogData.inputValue} placeholder="请输入" onChange={(event)=>this.onDialogHandleChange(event)} />
                                        </div>
                                    }
									{
										dialogData.checkedKeys == 'ShortTitle' ? <div></div>
										:<div className="flex" style={{ marginTop: '8px' }}>
                                            <span className="flex hover" onClick={() => this.onDialogCheckbox(dialogData.isAutoChecked)}>
                                                <span className={ dialogData.isAutoChecked ? "n-newCheckbox activeF": "n-newCheckbox"}></span>
                                                <span>同步更新到已关联商品</span>
                                            </span>
										</div>
									}
                                    {
                                        dialogData.isShowLevelSellPriceRule && dialogData.isAutoChecked? <div className="level-sell-rule-box n-font5 mT8">
                                           <span className="c06">
                                                {
                                                   dialogData.DistributePriceSetting === 0? '覆盖历史已存在结算价数据，全部按最新分销规则更新全部下游商家结算价' : '仅针对新关联的下游商品生效，历史下游商家结算价不覆盖更新'
                                                }
                                           </span>
                                           <span className="flex n-dColor hover" onClick={()=>this.showLevelSellPriceDialog()}><i class="iconfont icon-a-setting1x1" style={{ marginRight: '4px'}}></i>设置</span>
                                        </div> : null
                                    }
								</div>
							</div>
							<div class="dialogBtns flex">
								{
									dialogData.checkedKeys == 'ShortTitle' ? <div></div>
									:<div>对 <span class="count">{ dialogData.skuCount }</span> 个规格生效</div>
								}
								<div class="btnBox flex">
									<div class="n-mButton n-sActive" onClick={()=>this.onDialogClose()}>取消</div>
									{
										dialogData.isSave ? <div class="n-mButton" onClick={()=>this.onDialogSubmit()}>保存</div> : <div class="n-mButton stop" >保存</div>
									}
								</div>
							</div>
						</div>
                        <div id="dialogInOutSku" style={{ display: 'none'}}>
							<div class="dialog-wrap">
								<div className="title" style={{marginBottom: '8px'}}>{dialogData.keysName == 'InList' ? '统一增加库存数量' : '统一减少库存数量'}</div>
								<div className="dialog-wrap-cont" style={{marginBottom: '16px'}}>
                                    <div class="item-column-input flex">
                                        <input class="n-layui-input" type="text" Name="changeNum" value={dialogData.inputValue} ref={(input) => (this.input = input)} onChange={(event)=>this.onDialogHandleChange(event)} placeholder="请输入数量" />
                                    </div>
								</div>

								<div className="title" style={{marginBottom: '8px'}}>备注</div>
								<div className="dialog-wrap-cont">
                                    <div class="n-inputWrap">
                                        <input class="n-layui-input" style={{paddingRight: '60px'}} type="text" value={dialogData.inputRemarkValue} onChange={(event)=>this.onDialogHandleChange(event, 'inputRemarkValue')} name="OperateRemark"  placeholder="请输入备注内容，显示在“库存变更记录”页面" maxlength="100"/>
                                        <span class="input-num"><i class="titleLength">{dialogData.inputRemarkValue? dialogData.inputRemarkValue.length : 0}</i>/100</span>
                                    </div>
								</div>
							</div>
							<div class="dialogBtns flex">
								{
									dialogData.skuCount ? <div>对 <span class="count">{ dialogData.skuCount }</span> 个规格生效</div> : <div></div>
								}
								<div class="btnBox flex">
									<div class="n-mButton n-sActive" onClick={()=>this.onHideInOutSkuModal()}>取消</div>
									{
										dialogData.isSave ? <div class="n-mButton" onClick={()=>this.getInOutSkuSubmit()}>保存</div> : <div class="n-mButton stop" >保存</div>
									}
								</div>
							</div>
						</div>
                        <div id="dialogInOutRemark" style={{ display: 'none'}}>
							<div class="dialog-wrap">
                                <div className="title" style={{marginBottom: '8px'}}>{dialogData.keysName == 'InList' ? '增加库存数量' : '减少库存数量'}</div>
								<div className="dialog-wrap-cont" style={{marginBottom: '16px'}}>
                                    <div class="n-inputWrap">
                                        <input class="n-layui-input" type="text" placeholder="请输入数量" ref={(input) => (this.input = input)} onChange={(event)=>this.onDialogHandleChange(event,'inputValue')} value={dialogData.inputValue} onBlur={()=>{this.handleRemarkBlur( event,'ChangeCount')}}/>
                                    </div>
								</div>
								<div className="title" style={{marginBottom: '8px'}}>备注</div>
								<div className="dialog-wrap-cont">
                                    <div class="n-inputWrap">
                                        <input class="n-layui-input"  style={{paddingRight: '60px'}}  type="text" value={dialogData.inputRemarkValue} onChange={(event)=>this.onDialogHandleChange(event,'inputRemarkValue')} name="OperateRemark"  placeholder="请输入备注内容，显示在“库存变更记录”页面" maxlength="100"/>
                                        <span class="input-num"><i class="titleLength">{dialogData.inputRemarkValue? dialogData.inputRemarkValue.length : 0}</i>/100</span>
                                    </div>
								</div>
							</div>
							<div class="dialogBtns flex">
								<div></div>
								<div class="btnBox flex">
									<div class="n-mButton n-sActive" onClick={()=>this.onDialogClose()}>取消</div>
									{
										dialogData.isSave ? <div class="n-mButton" onClick={()=>this.getInOutRemarkSubmit()}>保存</div> : <div class="n-mButton stop" >保存</div>
									}
								</div>
							</div>
						</div>
                        <div id="dialogLevelSellRules" style={{ display: 'none'}}>
                            <div style={{ padding: '12px 16px 32px', boxSizing: 'border-box'}}>
                                <div class="n-alert n-alert-01" style={{ display: 'flex'}}>
                                    <i class="iconfont icon-gantan"></i>
                                    <div>
                                        <div>小站商品结算价：适用于小站商品对外展示不同分销价，下游商家铺货成功后按不同分销价结算；</div>
                                        <div>关联下游同款结算价：基础商品关联下游同款时，默认按分销等级换算当前基本分销价的最终结算价格。</div>
                                    </div>
                                </div>
                                <div className="n-font5 c06 mT16">修改默认分销价后，等级分销价</div>
                                <div className="level-sell-rule-content mT8">
                                    <label className="flex hover">
                                        <input
                                            type="radio"
                                            name="DistributePriceSetting"
                                            value="0"
                                            checked={dialogData.DistributePriceSetting == 0}
                                            onChange={()=>this.handleLevelRuleSetChange(0)}
                                        />
                                        <span className="n-font5 mL8">覆盖历史已存在结算价数据，全部按最新分销规则更新全部下游商家结算价</span>
                                    </label>
                                    <label className="flex hover mT16">
                                        <input
                                            type="radio"
                                            name="DistributePriceSetting"
                                            value="1"
                                            checked={dialogData.DistributePriceSetting == 1}
                                            onChange={()=>this.handleLevelRuleSetChange(1)}
                                        />
                                        <span className="n-font5 mL8">仅针对新关联的下游商品生效，历史下游商家结算价不覆盖更新</span>
                                    </label>
                                </div>
                            </div>
                            <div className="flex level-sell-rule-footer" style={{ justifyContent: 'space-between' }}>
                                <span className="n-font5 hover" style={{ color: '#0888FF' }} onClick={()=>this.linkToDistributeSetPage()}>前往设置等级分销价规则</span>
                                <div>
                                    <span className="n-mButton n-sActive" onClick={()=>this.closePriceRuleSetting()}>取消</span>
                                    <span className="n-mButton mL12" onClick={()=>this.savePriceRuleSetting()}>保存</span>
                                </div>
                            </div>
                        </div>
                        <div id="dialogSetting" style={{ display: 'none'}}>
							<div class="dialog-wrap">
								<div className="dialog-wrap-cont">
									<div className="dialog-wrap-cont-label mB8">显示设置</div>
									<div className="dialog-wrap-cont-switchBox flex">
										<div>系统订单/商品信息替换显示为基础商品信息</div>
										<div className={'hover layui-unselect layui-form-switch ' + (dialogData.isSwitch ? 'layui-form-onswitch' : '')} lay-skin="_switch"  onClick={()=>this.onSwitch()}><em></em><i></i></div>
									</div>
									<div className="dialog-wrap-cont-label mB8">关联提醒设置</div>
									<div class="n-alert n-alert-03 mB8">
											<i class="iconfont icon-gantan"></i>
											<span>为确保数据统计准确，当店铺商品未绑定基础商品时，会有关联提醒。</span>
										</div>
									<div className="dialog-wrap-cont-checkbox flex" style={{marginBottom:'-3px'}}>
										<div className="dialog-wrap-cont-checkbox-item mR8 flex hover" onClick={()=>{this.onRemindCheck('PrintBeforeShippingWarning')}}>
											<span className={dialogData.PrintBeforeShippingWarning ? 'n-newCheckbox c-chx activeF mR8': 'n-newCheckbox c-chx mR8'} ></span>
											<span>打单发货前拦截提醒</span>
										</div>
										<div className="dialog-wrap-cont-checkbox-item flex hover" onClick={()=>{this.onRemindCheck('AbnormalOrderTagReminder')}}>
											<span className={dialogData.AbnormalOrderTagReminder ? 'n-newCheckbox c-chx activeF mR8': 'n-newCheckbox c-chx mR8'} ></span>
											<span>订单标记异常提醒</span>
										</div>
										<div className="dialog-wrap-cont-checkbox-item mB8 flex hover" style={{display: 'none'}} onClick={()=>{this.onRemindCheck('StockPreparationReminder')}}>
											<span className={dialogData.StockPreparationReminder ? 'n-newCheckbox c-chx activeF mR8': 'n-newCheckbox c-chx mR8'} ></span>
											<span>备货单使用提醒</span>
										</div>

										<div className="dialog-wrap-cont-checkbox-item flex hover" style={{display: 'none'}} onClick={()=>{this.onRemindCheck('ExportBeforeReminder')}}>
											<span className={dialogData.ExportBeforeReminder ? 'n-newCheckbox c-chx activeF mR8': 'n-newCheckbox c-chx mR8'} ></span>
											<span>导出前提醒</span>
										</div>
									</div>

								</div>
							</div>
                            <div class="dialogBtns flex">
								<div></div>
								<div class="btnBox flex">
									<div class="n-mButton n-sActive" onClick={()=>{this.onDialogClose();this.onGetBaseProductSetting()}}>取消</div>
									{
										dialogData.isSave ? <div class="n-mButton" onClick={()=>this.SetBaseProductSetting()}>确认</div> : <div class="n-mButton stop" >确认</div>
									}
								</div>
							</div>
                        </div>
                        <div id="dialogDeleteSku" className="dialogDeleteSku" style={{ display: 'none'}}>
							<div className="dialog-wrap" style={{padding: '0px 0px 56px'}}>
                                <div style={{maxHeight: '400px',overflowY: 'auto',padding: '16px'}}>
                                    <div className="title">{ dialogData.label || '' }</div>
                                    <div className="dialog-wrap-cont">
                                        <div className="dialog-wrap-cont-item flex">
                                            <ul className="deleteTips">
                                                <li> 永久删除该{activeNavsName == "productStatus" ? "商品" : "规格"}信息。</li>
                                                <li>解除关联店铺商品的关联（店铺商品信息不变）。</li>
                                                <li> 同步删除关联的小站商品。</li>
                                            </ul>
                                        </div>
                                        
                                        { 
                                            dialogData.DeleteCombineProduts && dialogData.DeleteCombineProduts.length > 0 ?
                                        
                                                    <div className={dialogData.isShowDeleteCombineProduts ? 'dialog-toggleItem expanded' : 'dialog-toggleItem'}>
                                                    <div className="dialog-toggleItem-header">
                                                        <div>移除规格后，<span className="n-dColor">{dialogData.DeleteCombineProduts.length}</span> 个组合商品将被永久删除</div> 
                                                        {
                                                            dialogData.isShowDeleteCombineProduts ?
                                                            <div className="n-dColor hover" onClick={(event)=>this.handledialogDataToggle("isShowDeleteCombineProduts",dialogData.isShowDeleteCombineProduts)}>收起 <i className="iconfont icon-a-chevron-down1x"></i></div>
                                                            :<div className="n-dColor hover" onClick={(event)=>this.handledialogDataToggle("isShowDeleteCombineProduts",dialogData.isShowDeleteCombineProduts)}>查看详情 <i className="iconfont icon-a-chevron-down1x"></i></div>
                                                        }
                                                    </div>
                                                    <div className="dialog-toggleItem-body">
                                                        {
                                                            dialogData.DeleteCombineProduts.map((item,index)=>{
                                                                return (
                                                                    <div className="dialog-toggleItem-body-item flex">
                                                                        <img className="dialog-toggleItem-body-item-img" src={item.WareHouseProductImageUrl || '/Content/images/nopic.gif'} />
                                                                        <ul className="flex-column">
                                                                            <li className="dialog-toggleItem-body-item-title">
                                                                            <span className="tag">组合</span>
                                                                            {item.WareHouseProductName }
                                                                            </li>
                                                                            <li className="dialog-toggleItem-body-item-conte ellipsis c04 fz12">{item.WareHouseProductCargoNumber }</li>
                                                                        </ul>
                                                                    </div>
                                                                )
                                                            })
                                                        }
                                                    </div>
                                                </div>
                                            : null
                                        }
                                        { 
                                            dialogData.DeleteCombineProdutSkus && dialogData.DeleteCombineProdutSkus.length > 0 ?
                                                    <div className={dialogData.isShowDeleteCombineProdutSkus ? 'dialog-toggleItem expanded' : 'dialog-toggleItem'}>
                                                    <div className="dialog-toggleItem-header">
                                                        <div>移除 <span className="n-dColor">{dialogData.DeleteCombineProdutSkus.length}</span> 个组合商品中的规格</div> 
                                                        {
                                                            dialogData.isShowDeleteCombineProdutSkus ?
                                                            <div className="n-dColor hover" onClick={(event)=>this.handledialogDataToggle("isShowDeleteCombineProdutSkus",dialogData.isShowDeleteCombineProdutSkus)}>收起 <i className="iconfont icon-a-chevron-down1x"></i></div>
                                                            :<div className="n-dColor hover" onClick={(event)=>this.handledialogDataToggle("isShowDeleteCombineProdutSkus",dialogData.isShowDeleteCombineProdutSkus)}>查看详情 <i className="iconfont icon-a-chevron-down1x"></i></div>
                                                        }
                                                    </div>
                                                    <div className="dialog-toggleItem-body">
                                                        {
                                                            dialogData.DeleteCombineProdutSkus.map((item,index)=>{
                                                                return (
                                                                   <div className="dialog-toggleItem-body-item flex">
                                                                        <img className="dialog-toggleItem-body-item-img" src={item.ImageUrl || '/Content/images/nopic.gif'} />
                                                                        <ul className="flex-column">
                                                                            <li className="dialog-toggleItem-body-item-title ellipsis">
                                                                            <span className="tag">组合</span>
                                                                            {item.SkuName }
                                                                            </li>
                                                                            <li className="dialog-toggleItem-body-item-conte ellipsis c04 fz12">{item.WareHouseProductCargoNumber }</li>
                                                                        </ul>
                                                                    </div>
                                                                )
                                                            })
                                                        }
                                                    </div>
                                                </div>
                                            : null
                                        }
                                    </div>  
                                </div>
							</div>
							<div className="dialogBtns flex">
                                <div className="n-dColor hover flex" style={{lineHeight: '20px'}} id="guideClearFieldsDom" onClick={(event)=>this.onDialogDeleteSkuFieldSave()}>
                                <i className="iconfont icon-a-setting1x1 mR4"></i>字段清空设置
                                </div>
								<div className="btnBox flex">
									<div className="n-mButton n-sActive" onClick={()=>this.onDialogClose()}>取消</div>
									{
										dialogData.isSave ? <div className="n-mButton" onClick={()=>this.onDeleteSkuSave()}>仍要删除</div> : <div className="n-mButton stop" >仍要删除</div>
									}
								</div>
							</div>
						</div>

                        <div id="dialogDeleteSku2"  className="dialogDeleteSku" style={{ display: 'none'}}>
							<div className="dialog-wrap" style={{padding: '0px 0px 56px'}}>
                                <div style={{maxHeight: '500px',overflowY: 'auto',padding: '16px'}}>
                                    <div className="title">{ dialogData.label }</div>
                                    <div className="dialog-wrap-cont">
                                        <div className="dialog-wrap-cont-item flex">
                                            <div className="wu-pB16">规格解除关联后，店铺商品中的一下字段可选择清空：</div>
                                        </div>
                                        <div className="table-field" style={{boxSizing: 'border-box',width: '100%'}}>
                                            {
                                                dialogData.selectFields.map((item,index)=>{
                                                    var listItem = <div class="item-column-checkbox flex">
                                                        <div className="flex item-column-checkbox-input">
                                                            <div 
                                                            className={item.absoluteChecked ||item.disabled ? "wu-my-checkboxWrap checked disabled" : item.absoluteChecked ||item.checked ? "wu-my-checkboxWrap checked" : "wu-my-checkboxWrap"}
                                                            onClick={(event)=>this.onDialogHandleCheckbox(item,index)}
                                                            >
                                                                <span className="wu-my-checkbox"></span>
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <div className="item-column-checkbox-title">{ item.name }</div>
                                                            {
                                                                item.tips ?
                                                                <div className="flex tips">
                                                                    <i class="iconfont icon-a-info-circle1x"></i>
                                                                    <span>{item.tips}</span>
                                                                </div>
                                                                : ''
                                                            }
                                                        </div>
                                                    </div>
                                                    return listItem
                                                })
                                            }
                                        </div>
                                    </div>
                                </div>
							</div>
							<div className="dialogBtns flex">
                                <div className="n-dColor hover"></div>
								<div className="btnBox flex">
									<div className="n-mButton n-sActive" onClick={(event)=>this.onDialogDeleteSkuFieldSaveClose()}>取消</div>
									<div className="n-mButton" onClick={(event)=>this.onDialogDeleteSkuFieldSaveClose('save')}>保存</div>
								</div>
							</div>
						</div>

                         <div id="dialogDeleteResult" style={{ display: 'none'}}>
							<div className="dialog-wrap" style={{padding: '0px 0px 56px'}}>
                                <div style={{maxHeight: '400px',overflowY: 'auto',padding: '16px'}}>
                                    <div className="title">{ dialogData.label }</div>
                                    <div className="dialog-wrap-cont">
                                        <div className="result-box">
                                            <div className="result-box-content-item">
                                                <div className="result-box-content-item-title">删除成功：<span className="n-fColor">{dialogData.SuccessCount}</span></div>
                                                <div className="result-box-content-item-value" >已解除 [{dialogData.DeleteUnBindRelationsCount}] 个同款规格关联，小站商品已同步删除。</div>
                                            </div>
                                            {
                                                dialogData.FailCount && dialogData.FailCount > 0 ?
                                                <div className="result-box-content-item">
                                                    <div className="result-box-content-item-title">删除失败：<span className="n-sColor" style={{color: '#EA572E'}}>{dialogData.FailCount }</span></div>
                                                    <div className="result-box-content-item-cont">
                                                        <div className="result-box-content-item-cont-title" style={{display: dialogData.FailMsg ? "block" : "none"}}>失败原因：{dialogData.FailMsg}</div>
                                                        {
                                                            activeNavsName == "skuStatus" && dialogData.FailSkuCodes && dialogData.FailSkuCodes.length ?
                                                            <div className="result-box-content-item-cont-text">SKU编码：
                                                            <span id="FailSkuCodes" >{dialogData.FailSkuCodes.join(',')}</span>等
                                                            <span className="n-dColor hover" onClick={()=>onCopySpuCode('FailSkuCodes')}>一键复制</span>
                                                            </div>: null
                                                        }
                                                        {
                                                            activeNavsName == "productStatus" && dialogData.FailProductCodes && dialogData.FailProductCodes.length ?
                                                            <div className="result-box-content-item-cont-text">SKU编码：
                                                            <span id="FailProductCodes" >{dialogData.FailProductCodes.join(',')}</span>等
                                                            <span className="n-dColor hover" onClick={()=>onCopySpuCode('FailProductCodes')}>一键复制</span>
                                                            </div>: null
                                                        }
                                                    </div>
                                                </div>
                                                : null
                                            }
                                        </div> 
                                    </div>
                                </div>
							</div>
							<div className="dialogBtns flex" style={{borderRadius: "0 0 10px 10px"}}>
                                <div className="n-dColor hover"></div>
								<div className="btnBox flex">
									<div className="n-mButton n-sActive" onClick={(event)=>this.onDialogClose()}>关闭</div>
								</div>
							</div>
						</div>
                        
                        <ComonDrawer showDrawer={this.state.isProductLogoDrawer} title={'基础商品操作日志'} width={'910px'} cancelBtnTitle="关闭日志" showCancelBtn={true} shadeClose={true} cancelDrawer={() => this.cancelDrawerModul("isProductLogoDrawer")}>
                            <div className="layui-tab layui-tab-brief " style={{ margin: '0'}}>
                                <ul className="layui-tab-title" id="changePartner">
                                    {
                                        tabList.length && tabList.map((item, index) => {
                                            return (
                                                <li  className={item.checked ? "layui-this" : ''}
                                                    data-type={item.value} onClick={() => this.onTabChange(item,'equal')}
                                                    key={item.id}
                                                >
                                                    {item.name}
                                                </li>

                                            )
                                        })
                                    }
                                </ul>
                            </div>
                            {
                                platformArea.length ?
                                    <div className="platform-area">
                                        <ul className="flex">
                                            {
                                                platformArea.length && platformArea.map((item, index) => {
                                                    return (
                                                        <li className={item.checked ? "hover active" : 'hover'}
                                                            data-type={item.value} onClick={() => this.onPlatformAreaChange(item)}
                                                            key={item.DbName}
                                                        >
                                                            {item.NickName}
                                                        </li>
                                                    )
                                                })
                                            }
                                        </ul>
                                    </div>:null
                            }
                            {
                                platformType  != "Site"
                                    ?<iframe id="iframe_main_Log" style={{height: iframeHeight+'px'}} src={this.state.ifarmSrc}></iframe>
                                    :<div>
                                        <div class="abnormal-search" style={{display:"flex"}}>
                                            <div class="layui-input-inline layui-input-inline-label flex n-layui-input" style={{marginRight: "8px"}}>
                                                <span class="label">SKU编码</span>
                                                <input type="text" name="logoSkuCode" class="layui-input " autocomplete="off" placeholder="请输入SKU编码" />
                                            </div>
                                            <div class="layui-input-inline layui-input-inline-label flex n-layui-input" style={{marginRight: "8px"}}>
                                                <span class="label">SKUID</span>
                                                <input type="text" name="logoSkuUid" class="layui-input " autocomplete="off" placeholder="请输入SKUID" />
                                            </div>
                                            <div class="layui-input-inline layui-input-inline-label flex n-layui-input" style={{marginRight: "8px"}}>
                                                <span class="label">SPU编码</span>
                                                <input type="text" name="logoSpuCode" class="layui-input " autocomplete="off"  placeholder="请输入SPU编码" />
                                            </div>
                                            <div class="layui-input-inline flex">
                                                <span class="n-mButton n-pActive" lay-submit="true" onClick={()=>this.getBaseProductRelLogs(false,true)}>查询</span>
                                                <span class="n-mButton n-sActive" lay-submit="true" style={{marginLeft: "8px"}} onClick={()=>this.onReset()}>重置</span>
                                            </div>
                                        </div>
                                        <div class="full-mask-main-tableWrap">

                                            <div class="n-productDailog-table">
                                                <table class="n-table">
                                                    <thead>
                                                        <tr>
                                                            <th className="textTitle" style={{width:'120px'}}>操作账号</th>
                                                            <th className="textTitle" style={{width:'108px'}}>日志类型</th>
                                                            <th className="textTitle" style={{width:'160px'}}>商品信息</th>
                                                            <th className="textTitle" style={{minWidth:'240px'}}>变更内容</th>
                                                            <th className="textTitle" style={{width:'140px'}}>变更时间</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        {
                                                            this.state.productRelLogList && this.state.productRelLogList.length > 0 ? this.state.productRelLogList.map((item,i)=>{
                                                                return(
                                                                    <tr>
                                                                        <td>
                                                                            <span className="textColor">{ item.Mobile }</span>
                                                                        </td>
                                                                        <td>
                                                                            <span className="textColor">{ item.RelationTypeText }</span>
                                                                        </td>
                                                                        <td>
                                                                            {
                                                                                item.RelationType == 2
                                                                                ? <span className="textColor">SPU编码{ item.SpuCode }</span>
                                                                                : <span className="textColor">店铺SKUID：{ item.SkuIdStr }</span>
                                                                            }
                                                                        </td>
                                                                        <td>
                                                                            <div className="textTitle">关联基础规格</div>
                                                                            <div className="textColor">SKU编码：{ item.SkuCode }</div>
                                                                            <div className="textColor">更新内容：{ item.ChangeDetailInfo }</div>
                                                                        </td>
                                                                        <td>
                                                                            <span className="textColor">{ item.CreateTime }</span>
                                                                        </td>
                                                                    </tr>
                                                                )
                                                            }): <tr>
                                                                <td colspan="15" class="tdNodata" style={{ padding: "24px 16px" }}>
                                                                    <div className="n-tableNoDataShow">
                                                                        <span className="iconfont icon-wushuju1"></span>
                                                                        <span className="tableNoDataShow-title">未找到符合条件的内容</span>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        }
                                                    </tbody>
                                                </table>
                                            </div>
                                            <div className="layui-myPage n-myPage" id="logoTable_paging"></div>
                                        </div>
                                    </div>
                            }

						</ComonDrawer>

                        @*组合货品弹窗*@
                        <CombinationProductDialog dialogVisible={ this.state.dialogVisible } ChildSku={this.state.ChildSku} style={ this.state.dialogVisible ? 'display:block' : 'display:none'} onSendData={(visible,type,data) => this.handleReceiveData(visible,type,data)} />

                        <div id="ChangLogDialog" style={{display:"none"}}>
                             <div class="full-mask-main-tableWrap">

                                <div class="n-productDailog-table" style={{marginBottom: '16px'}}>
                                    <table class="n-table">
                                        <thead>
                                            <tr>
                                                <th className="textTitle" style={{width:'36px'}}>序号</th>
                                                <th className="textTitle" style={{width:'140px'}}>变更时间</th>
                                                <th className="textTitle" style={{width:'300px'}}>变更明细</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {
                                                this.state.productChangeLogList && this.state.productChangeLogList.length ?this.state.productChangeLogList.map((item,index) => {
                                                    return (
                                                        <tr>
                                                            <td className="tr-vertical-align-baseline"><span className="textColor">{index+1}</span></td>
                                                            <td className="tr-vertical-align-baseline"><span className="textColor">{item.CreateTime}</span></td>
                                                            <td>
                                                                <ul className="flex-column">
                                                                    <li className="textColor">{item.ChangeTypeView}</li>
                                                                    <li className="fs12 c04">
                                                                        {
                                                                            item.ChangeDetailList && item.ChangeDetailList.length ?item.ChangeDetailList.map((item1) => {
                                                                                return (
                                                                                    <div className="mT2">{item1}</div>
                                                                                )
                                                                            }): <span>-</span>
                                                                        }
                                                                    </li>
                                                                </ul>
                                                            </td>
                                                        </tr>
                                                    )
                                                })
                                                :<tr>
                                                    <td colSpan="20" className="tdNodata" style={{ padding: "24px 16px" }}>
                                                        <div className="n-tableNoDataShow">
                                                            <span className="iconfont icon-wushuju1"></span>
                                                            <span className="tableNoDataShow-title">未找到符合条件的内容</span>
                                                        </div>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                                <div className="layui-myPage n-myPage" id="pagingCombinedProductChangeLog"></div>
                                <div></div>
                            </div>
                        </div>
                        <div class="new-full-mask iframeMask" id="createFullMaskName">
							<div class="full-mask-back" ></div>
							<div class="full-mask-content-wrapper full-mask-right baseProductfull-mask-right" style={{width:'1088px'}}>
								<div class="full-mask-header" style={{width:'888px'}}>
									<div class="full-mask-title c09" id="headerTitle">创建商品</div>
									<i class="iconfont icon-chuyidong" onClick={()=>this.closeCreateProduct()}></i>
								</div>
								<ul class="n-tabNav" id="n_tabNav_pt" style={{width:'888px'}}>
									{
										this.state.edmitIframeData.map((item,index)=>{
											return(
												<li onClick={()=>this.changeIframeNav(index)} class={item.isActive?'n-tabNav-item active':'n-tabNav-item'}>
                                                    {item.title}
                                                    {
                                                        item.type == 'TouTiao' && this.state.isBubbleTip ?
                                                        <i className="commonWenHao02 popoverCommon hovericon iconfont icon-gantan" style={{color: '#EA572E', fontSize: '14px', marginLeft: '5px'}}>
                                                            <span className="popoverCommon-warn" style={{ width: '42px', top: 'unset', left: '-22px', bottom:'26px',color:'rgba(0, 0, 0, 0.9)', textAlign: 'center'}}>{this.state.isBubbleTip == 'IsWaitSyncBaseProduct' ? '需更新':'需完善'}</span>
                                                        </i>
                                                        :null
                                                    }
                                                </li>

											)
										})
									}
								</ul>
								<div class="full-mask-content" id="createProductIframeWrap">
									{
										this.state.edmitIframeData.map((item,index)=>{
											return(
												<iframe id={"createProductIframe"+index} class={item.isActive?'createProductIframe active':'createProductIframe'} src={item.url?item.url:''} frameborder="0"></iframe>
											)
										})
									}
								</div>
							</div>
						</div>
                    </div>
                )
            }
        }

        // 2.渲染组件到页面
        ReactDOM.render(<NewBaseProducts />, document.getElementById('reactApp'))
    </script>
}