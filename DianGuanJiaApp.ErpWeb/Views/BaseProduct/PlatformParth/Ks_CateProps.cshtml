<script id="ks_data_list_cateProps" type="text/x-jsrender">
    {{if CateProps && CateProps.length>0}}
    {{for CateProps}}
    {{if FieldType!="multiValueMeasure"}}
    <div class="catePropsItemWrap {{:Rule.IsRequired?'required':Rule.IsImportant?'importanted':'noRequired'}}">
        {{if Rule.IsRequired}}
        <span class="n-sColor requiredIcon">*</span>
        {{/if}}
        {{if FieldType=="brandSearch" }}
        <div class="n-mySelect n-single catePropsItem {{:FieldType}} {{:Value!=null && Value!=""?'hasActive':''}}">
            <div class="n-mySelect-title">
                <div class="n-mySelect-title-left">
                    <span class="n-mySelect-title-left-title">{{:Name}}</span>
                    <span class="n-mySelect-title-placeholder">请选择</span>
                    <span class="n-mySelect-title-chooseItem">{{:Value}}</span>
                </div>
                <span class="iconfont icon-a-chevron-down1x"></span>
            </div>
            <div class="n-mySelect-showContent">
                <div class="n-mySelect-search" onclick="event.stopPropagation()">
                    <input id="searchInputEle" type="text" class="n-input searchInput" placeholder="请输入搜索的品牌" >
                    <span class="iconfont icon-a-search1x1" onclick="searchBrand.bind(this)('{{:Id}}')"></span>
                </div>
                <ul class="n-mySelect-showContent-ul">
                    {{if Options.length>0}}
                    {{for Options ~Name=Name ~Value=Value?Value:''}}
                    <li class="n-mySelect-showContent-ul-li  {{:~Value.indexOf(#data)!=-1?'activeItem':''}}" onclick="selectCateProps.bind(this)('{{:~Name}}')">{{:#data}}</li>
                    {{/for}}
                    {{else}}
                    <li class="n-mySelect-showContent-ul-li noData">暂无数据</li>
                    {{/if}}

                    @*<li class="n-mySelect-showContent-ul-li n-dColor" onclick="createBrandFun()"><i class="iconfont icon-a-add1x"></i><span>新建品牌</span></li>*@
                </ul>
            </div>
        </div>
        {{else FieldType=="input"}}
        <div class="n-mySelect n-single catePropsItem {{:Value!=null && Value!=""?'hasActive':''}}">
            <div class="n-mySelect-title">
                <div class="n-mySelect-title-left">
                    <span class="n-mySelect-title-left-title">{{:Name}}</span>
                    <input class="n-mySelect-input" type="text" name="name" value="{{:Value!=null?Value:''}}" oninput="inputCateProps.bind(this)('{{:Name}}','{{:Rule.DataType}}')" />
                </div>
            </div>
        </div>
        {{else FieldType=="dateTimeRange"}}
        <div class="n-mySelect n-single catePropsItem">
            <div class="n-mySelect-title">
                <div class="n-mySelect-title-left">
                    <span class="n-mySelect-title-left-title">{{:Name}}</span>
                    @*<input class="n-mySelect-input" type="text" name="name" />*@

                    <input type="text" class="layui-input cate-dateTime" id="dateTimeRange_{{:Id}}" placeholder="请选择时间">
                </div>
            </div>
        </div>
        {{else FieldType=="dateTime"}}
        <div class="n-mySelect n-single catePropsItem">
            <div class="n-mySelect-title">
                <div class="n-mySelect-title-left">
                    <span class="n-mySelect-title-left-title">{{:Name}}</span>
                    @*<input class="n-mySelect-input" type="text" name="name" />*@

                    <input type="text" class="layui-input cate-dateTime" id="cate_dateTime_{{:Id}}" placeholder="请选择时间">
                </div>
            </div>
        </div>
        {{else FieldType=="measure"}}

        <div class="measureWrap" style="display: flex; ">

            <div class="n-mySelect n-single catePropsItem {{:MeasureTemplates.LeftValue!=null?'hasActive':''}}" style="width:160px;">
                <div class="n-mySelect-title">
                    <div class="n-mySelect-title-left">
                        <span class="n-mySelect-title-left-title">{{:Name}}</span>
                        <input class="n-mySelect-input" type="text" name="name" value="{{:MeasureTemplates.LeftValue!=null?MeasureTemplates.LeftValue:''}}" oninput="inputCateProps.bind(this)('{{:Name}}','measure')" />
                    </div>
                </div>
            </div>

            <div class="n-mySelect n-single catePropsItem {{:MeasureTemplates.RightValue!=null?'hasActive':''}}" style="margin-left: 4px;">
                <div class="n-mySelect-title">
                    <div class="n-mySelect-title-left">
                        <span class="n-mySelect-title-placeholder">请选择</span>
                        <span class="n-mySelect-title-chooseItem">{{:MeasureTemplates.RightValue}}</span>
                    </div>
                    <span class="iconfont icon-a-chevron-down1x"></span>
                </div>
                <div class="n-mySelect-showContent">
                    <div class="n-mySelect-search" onclick="event.stopPropagation()">
                        <input type="text" class="n-input searchInput" placeholder="请输入搜索内容" oninput="CreateBasePlatformProductModule.searchCateProps.bind(this)()">
                        <span class="iconfont icon-a-search1x1"></span>
                    </div>
                    <ul class="n-mySelect-showContent-ul">
                        {{if MeasureTemplates.RightOptions.length>0}}
                        {{for MeasureTemplates.RightOptions ~Name=Name ~ChooseValue=MeasureTemplates.RightValue?MeasureTemplates.RightValue:''}}
                        <li class="n-mySelect-showContent-ul-li {{:~ChooseValue.indexOf(Value)==0?'activeItem':''}}" onclick="selectCateProps.bind(this)('{{:~Name}}','measure')">{{:Value?Value:'无'}}</li>
                        {{/for}}
                        {{else}}
                        <li class="n-mySelect-showContent-ul-li noData">暂无数据</li>
                        {{/if}}
                    </ul>
                </div>
            </div>
        </div>
        {{else FieldType=="checkbox" || FieldType=="checkboxlist"}}
        <div class="n-mySelect n-single catePropsItem {{:Value!=null && Value!=""?'hasActive':''}}">
            <div class="n-mySelect-title" onclick="searchCatePropsList.bind(this)('{{:Id}}')">
                <div class="n-mySelect-title-left">
                    <span class="n-mySelect-title-left-title">{{:Name}}</span>
                    <span class="n-mySelect-title-placeholder">请选择</span>
                    <span class="n-mySelect-title-chooseItem">{{:Value}}</span>
                </div>
                <span class="iconfont icon-a-chevron-down1x"></span>
            </div>
            <div class="n-mySelect-showContent">
                <div class="n-mySelect-search" onclick="event.stopPropagation()">
                    <input type="text" class="n-input searchInput" placeholder="请输入搜索内容" oninput="CreateBasePlatformProductModule.searchCateProps.bind(this)()">
                    <span class="iconfont icon-a-search1x1"></span>
                </div>
                <ul class="n-mySelect-showContent-ul">
                    {{if Options.length>0}}
                    {{for Options ~Name=Name ~Value=Value?Value:'' ~Id=Id}}
                    <li class="checkbox n-mySelect-showContent-ul-li {{:~checkboxActiveFun(~Value,#data)?'activeItem':''}}" onclick="selectCateProps.bind(this)('{{:~Name}}', 'checkbox','{{:#getIndex()}}','{{:~Id}}')">{{:#data}}</li>
                    {{/for}}
                    {{else}}
                    <li class="n-mySelect-showContent-ul-li noData">暂无数据</li>
                    {{/if}}
                </ul>
                {{if Rule.DiyType}}
                <div class="addAttribute" onclick="event.stopPropagation()">
                    <div class="addAttribute_addbtn n-dColor hover"><i class="iconfont icon-a-add1x"></i><span>新建自定义值</span> </div>
                    <div class="addAttribute_box flex">
                        <input class=" layui-input n-layui-input" data-name="{{:Name}}" style="width:120px;" placeholder="请输入规格值" type="text" value="" />
                        <i class="iconfont icon-a-check1x addBtn hover" style="color: #5ad095;"></i>
                        <i class="iconfont icon-a-close1x closeBtn hover" style="color: #ff9da5;"></i>
                    </div>
                </div>
                {{/if}}
            </div>
        </div>
        {{else FieldType=="Image"}}
            <div class="catePropsItemWrap-box">
                <div class="catePropsItemWrap-label">{{:Name}}</div>
                <div class="newProductPicShow-itembox">
                    {{if !Value}}
                    <div class="n-productInfo-img-add" data-id="{{:Id}}" id="addCatePropsItemPic_{{:Id}}" onclick="event.stopPropagation()">
                        <i class="iconfont icon-a-image-add1x1 upPic-icon"></i>
                    </div>
                    {{else}}
                    <div class="newProductPicShow-item newProductPicShowCateProps-item" >
                        <img src="{{:Value}}" />
                        <span class="iconfont icon-icon_shanchu-" onclick="CreateBasePlatformProductModule.delCatePropsProductPic('{{:Id}}')"></span>
                    </div>
                    {{/if}}
                </div>
            </div>
        {{else}}
        <div class="n-mySelect n-single catePropsItem {{:Value!=null && Value!=""?'hasActive':''}}">
            <div class="n-mySelect-title">
                <div class="n-mySelect-title-left">
                    <span class="n-mySelect-title-left-title">{{:Name}}</span>
                    <span class="n-mySelect-title-placeholder">请选择</span>
                    <span class="n-mySelect-title-chooseItem">{{:Value}}</span>
                </div>
                <span class="iconfont icon-a-chevron-down1x"></span>
            </div>
            <div class="n-mySelect-showContent">
                <div class="n-mySelect-search" onclick="event.stopPropagation()">
                    <input type="text" class="n-input searchInput" placeholder="请输入搜索内容" oninput="CreateBasePlatformProductModule.searchCateProps.bind(this)()">
                    <span class="iconfont icon-a-search1x1"></span>
                </div>
                <ul class="n-mySelect-showContent-ul">
                    {{if Options.length>0}}
                    {{for Options ~Name=Name ~Value=Value?Value:''}}
                    <li class="n-mySelect-showContent-ul-li {{:~Value.indexOf(#data)==0?'activeItem':''}}" onclick="selectCateProps.bind(this)('{{:~Name}}')">{{:#data}}</li>
                    {{/for}}
                    {{else}}
                    <li class="n-mySelect-showContent-ul-li noData">暂无数据</li>
                    {{/if}}
                </ul>
            </div>
        </div>
        {{/if}}


        {{if Rule.IsRequired}}
        <div class="input-warnTitle">该项为必填项，请完善</div>
        {{/if}}
    </div>

    {{else}}

    @*面料材质属性*@

    {{for MeasureTemplates.MultiValueMeasureList ~FieldType=FieldType ~Name=Name ~Options=Options ~Rule=Rule ~len=MeasureTemplates.MultiValueMeasureList.length }}
    <div class="catePropsItemWrap {{:~Rule.IsRequired?'required':~Rule.IsImportant?'importanted':'noRequired'}} {{:~FieldType}}">
        {{if ~Rule.IsRequired}}
        <span class="n-sColor requiredIcon">*</span>
        {{/if}}

        <div class="multiValueMeasureWrap">
            <div class="n-mySelect n-single catePropsItem {{:Name ? 'hasActive':''}}">

                <div class="n-mySelect-title">
                    <div class="n-mySelect-title-left">
                        <span class="n-mySelect-title-left-title">{{:~Name}}</span>
                        <span class="n-mySelect-title-placeholder">请选择</span>
                        <span class="n-mySelect-title-chooseItem" data-name="{{:Name}}">{{:Name}}</span>
                    </div>
                    <span class="iconfont icon-a-chevron-down1x"></span>
                </div>
                <div class="n-mySelect-showContent">
                    <div class="n-mySelect-search" onclick="event.stopPropagation()">
                        <input type="text" class="n-input searchInput" placeholder="请输入搜索内容" oninput="CreateBasePlatformProductModule.searchCateProps.bind(this)()">
                        <span class="iconfont icon-a-search1x1"></span>
                    </div>
                    <ul class="n-mySelect-showContent-ul">
                        {{if ~Options.length>0}}
                        {{for ~Options ~index=#getIndex()}}
                        <li class="n-mySelect-showContent-ul-li" data-name="{{:#data}}" onclick="selectCateProps.bind(this)('{{:~Name}}','multiValueMeasure','{{:~index}}')">{{:#data}}</li>
                        {{/for}}
                        {{else}}
                        <li class="n-mySelect-showContent-ul-li noData">暂无数据</li>
                        {{/if}}
                    </ul>
                </div>
            </div>
            <div class="n-mySelect n-single catePropsItem {{:Value ? 'hasActive':''}}" style="max-width: 64px; margin-left: 4px;">
                <div class="n-mySelect-title" style="padding-left: 0;">
                    <div class="n-mySelect-title-left">
                        <input class="n-mySelect-input" type="text" name="name" value="{{:Value}}" oninput="inputCateProps.bind(this)('{{:~Name}}','multiValueMeasure','{{:#getIndex()}}')" />
                        <span class="n-mySelect-title-left-perIcon">%</span>
                    </div>
                </div>
            </div>
            {{if ~len>1}}
            <span class="iconfont icon-a-delete1x" onclick="delCateProps.bind(this)('{{:~Name}}','{{:#getIndex()}}')"></span>
            {{/if}}
        </div>
        <div class="input-warnTitle">该项为必填项，请完善</div>

    </div>
    {{/for}}
    {{/if}}
    {{/for}}
    {{/if}}
</script>

