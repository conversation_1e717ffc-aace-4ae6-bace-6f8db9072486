@{
	ViewBag.Title = "铺货日志";
	ViewBag.MenuId = "DistributionLog";
	ViewBag.MenuActive = "ProductManagement";
}

@section Header {
	<!-- <link rel="stylesheet" href="~/Content/css/product/BaseOfPtProducts.css" /> -->
	<link rel="stylesheet" href="~/Scripts/antd/antd.min.css" />
	<link rel="stylesheet" href="~/Content/css/product/CommonProductCollection.css" />
	<style type="text/css">
		body {
			background-color: #edf0f4;
		}
        .ellipsis2{
            display: -webkit-box;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            -webkit-line-clamp: 2; /* 控制显示的行数 */
        }
        .ellipsis {
            overflow: hidden;
            /* 超出部分隐藏 */
            text-overflow: ellipsis;
            /* 超出一行显示省略号 */
            white-space: nowrap;
            /* 文本不换行 */
        }

		.distributionLog-container {
			padding: 24px;
			box-sizing: border-box;
			position: relative;
		}

		.distributionLog-title {
			font-size: 20px;
			font-weight: 500;
			line-height: 28px;
			color: rgba(0, 0, 0, 0.9);
		}

		.distributionLog-content {
			background: #FFFFFF;
			border-radius: 8px;
			margin-top: 24px;
		}

		.tabs-wrapper {
			height: 44px;
			display: flex;
		}

		.tabs-wrapper .n-tabNav .n-tabNav-item {
			margin-left: 8px;
		}

		.tabs-wrapper .n-tabNav .n-tabNav-item:first-child {
			margin-left: 16px;
		}

		.table-th-text {
			font-size: 14px;
			font-weight: 500;
			line-height: 20px;
			color: rgba(0, 0, 0, 0.6);
		}

		.productShow-name {
			margin-left: 8px;
		}

		.productShow-img {
			width: 56px;
			height: 56px;
			border-radius: 3px;
		}

		.stockup_table_content thead tr th {
			border: none;
			color: rgba(0, 0, 0, 0.6);
		}

		.stockup_table_content tbody tr td:first-child {
			padding-left: 16px;
		}

		.stockup_table_content tbody tr td {
			transition: all 1s ease 0s;
			border: none;
			color: rgba(0, 0, 0, 0.9);
		}

		.stockup_table_content tbody tr {
			border-bottom: 1px solid #e6e6e6;
		}

		.layui-myPage {
			padding-right: 12px;
		}

		.stockup_table_content thead tr {
			border-top: 1px solid #e6e6e6;
			border-bottom: 1px solid #e6e6e6;
			background: #f5f5f5;
		}

		.stockup_table_content {
			border-left: none;
		}

		.flex {
			display: flex;
			align-items: center;
		}

		.form-wrapper {
			padding: 8px 16px;
			box-sizing: border-box;
		}

		.form-wrapper .layui-input-inline-label {
			height: 32px;
			line-height: 32px;
			/* border-radius: 4px; */
			border-radius: 6px;
			box-sizing: border-box;
			border: 1px solid rgba(0, 0, 0, 0.14);
			padding-left: 0px;
		}

		.form-wrapper .layui-input-inline-label .label {
			padding: 0px 8px;
			word-break: keep-all;
			color: rgba(0, 0, 0, 0.6);
		}

		.form-wrapper .layui-input-inline-label .layui-input {
			border: none;
			width: 100%;
			height: 30px;
			line-height: 30px;
			padding-left: 0;
		}

		.form-wrapper .layui-input-inline-label .layui-select {
			border: none;
			width: 100%;
			height: 30px;
			line-height: 30px;
			padding-left: 0;
			-webkit-appearance: auto;
		}

		.form-wrapper .layui-input-inline-label .layui-form-select {
			width: 100%;
		}

		.form-wrapper .layui-form-item .n-mButton {
			margin-right: 8px;
		}

		.form-wrapper .layui-form-item .n-mButton.n-pActive {
			/* border: 1px solid rgba(8, 136, 255, 0.1); */
			border: 1px solid rgba(8, 136, 255, 0.8);
		}

		.form-wrapper .layui-form-item {
			margin-bottom: 0;
			height: 32px;
		}

		.layui-content-li-icons {
			display: flex;
			align-items: center;
		}

		.layui-content-li-icons .layui-content-li-icons-item {
			display: inline-block;
			width: 20px;
			height: 20px;
			border-radius: 50%;
		}

		.btn-text {
			cursor: pointer;
			font-size: 14px;
			line-height: 20px;
		}

		.tip-common-icon {
			width: 14px;
			height: 14px;
			border-radius: 50%;
			overflow: hidden;
			display: flex;
			align-items: center;
			justify-content: center;
			flex-shrink: 0;
		}

		.tip-success-icon {
			background: #73AC1F;
			color: #FFFFFF;
		}

		.tip-error-icon {
			background: #EA572E;
			color: #FFFFFF;
		}

		.iconfont-size {
			font-size: 12px !important;
		}

		.common-m-l-8 {
			margin-left: 8px !important;
		}

		.common-m-t-16 {
			margin-top: 16px;
		}

		.common-m-b-8 {
			margin-bottom: 8px;
		}

		.tooltip-wrap {
			position: absolute;
			margin-left: 80px;
			margin-top: 2px;
			width: 310px;
			z-index: 8;
			background: #FFFFFF;
		}

		.tooltip-wrap .n-tooltip {
			padding: 16px 16px 8px 16px !important;
		}

		.order-tips-wrapper {
			width: 320px;
			background: #FFFFFF;
			z-index: 130;
			position: fixed;
			border-radius: 8px;
			right: 24px;
			top: 70px;
			box-shadow: 0px 6px 30px 5px rgba(0, 0, 0, 0.05), 0px 16px 24px 2px rgba(0, 0, 0, 0.04), 0px 8px 10px -5px rgba(0, 0, 0, 0.08);
		}

		.order-tips-wrapper-header {
			height: 44px;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: space-between;
			padding: 12px 16px;
			box-sizing: border-box;
		}

		.order-tips-wrapper-header .icon-a-notification-filled1x {
			color: #0888FF;
			font-size: 20px;
			margin-right: 4px;
		}

		.order-tips-wrapper-header .icon-a-close1x {
			color: rgba(0, 0, 0, 0.6);
			font-size: 20px;
			cursor: pointer;
		}

		.order-tips-wrapper-header-title {
			display: flex;
			align-items: center;
		}

		.order-tips-wrapper-content {
			padding: 0 16px 8px 44px;
			height: 68px;
			box-sizing: border-box;
		}

		.order-tips-wrapper-footer {
			padding: 12px 16px;
			height: 56px;
			display: flex;
			align-items: center;
			justify-content: right;
			box-sizing: border-box;
		}
		.color04{
			color: rgba(0, 0, 0, 0.4);
		}
		ul {
			list-style: none;
			padding: 0;
			margin: 0;
		}
		.Cascader-box .ant-select:not(.ant-select-customize-input) .ant-select-selector {
			background-color: transparent !important;
			border: none !important;
			width: 176px;
		}
		.ant-cascader-menus .ant-cascader-menu {
			/* width: 200px; */
			padding: 8px;
		}
		.ant-cascader-menus .ant-cascader-menu:last-child {
			border-right: none;
		}
		.custom-dropdown {
			border-radius: 4px;
			/* padding: 8px !important; */
			color: rgba(0, 0, 0, 0.6);
			/* 其他自定义样式 */
		}
		.ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled), .ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled):hover {
			font-weight: 400;
			background: rgba(8, 136, 255, 0.1);
			border-radius: 4px;
		}
		.ant-cascader-menu-item:hover {
			border-radius: 4px;
		}

		.shareShopList_data_tr_logo {
            background-image: url("/Content/images/kuanjin-icons.png");
            background-position: -16px -8px;
            background-color: unset;
        }
		.distributionLog-container .n-mButton {
			border-radius: 6px;
		}
		.distributionLog-container .form-wrapper .layui-form-item .n-mButton.n-pActive {
			background: unset;
		}
	</style>
}
	
<div class="my-body">
	<div id="DistributionLogApp"></div>
</div>

@section scripts {
	<script src="~/Scripts/react/react.production.min.js"></script>
	<script src="~/Scripts/react/react-dom.production.min.js"></script>
	<script src="~/Scripts/react/babel.min.js"></script>
	<script src="~/Scripts/antd/antd.min.js"></script>
    <script type="text/babel">

		commonModule.CloudPlatformType = '@(Html.Raw(ViewBag.CloudPlatformType ?? ""))';
		commonModule.userFlag = "@ViewBag.UserFlag"; // 获取当前用户的角色标记
		window.onload= function(){
		// 1. 创建类式组件
		class LogComponent extends React.Component {
			constructor() {
				super();
				this.state = {
					queryParams: {
						TargetShopId: null,
						ProductName: '',
						TaskStatus: '',
						PageIndex: 1,
						PageSize: 50,
					},
					list: [],
					tabList: [
						{ id: 1, label: '全部', status: '', count: '' },
						{ id: 2, label: '正在铺货', status: 'Doing', count: '' },
						{ id: 3, label: '铺货成功', status: 'Success', count: '' },
						{ id: 4, label: '铺货失败', status: 'Error', count: '' },
					],
					shopList: [], // 店铺数据
					showTipIndex: null,
					AbnormalTypes: {
						'CreateBaseProduct': '创建基础商品',
						'SyncShopProduct': '同步店铺商品异常',
						'BaseProductBindSupplier': '基础商品绑定厂家异常',
						'BaseOfPtSkuRelation': '基础商品关联店铺商品异常',
						'ProductBindSupplier': '店铺商品设置厂家异常',
					},
					AbnormalTypeBtnText: {
						'CreateBaseProduct': '创建商品',
						'SyncShopProduct': '重新同步',
						'BaseProductBindSupplier': '前往处理',
						'BaseOfPtSkuRelation': '前往处理',
						'ProductBindSupplier': '前往处理',
					},
					dbname: '',
					showOrderTips: false, // 显示订购提示
					subscribeNormalShopCount: 0, // 店铺未订购应用数量
					edmitIframeData: [], // 编辑商品数据
				}
			}
			// 获取店铺下拉数据
			getShopData(callback) {
				let that = this;
				commonModule.Ajax({
					url: "/api/Listing/GetShopList",
					type: "GET",
					showMasker: false,
					loading: true,
					async: true,
					success: function (rsp) {
						const data = rsp.Data;
						if (rsp.Success) {
							if (callback) {
								callback(data, rsp.Message);
							}
						} else {
							callback(data, rsp.Message);
						}
					},
					error: function (error) {
						if (callback) {
							callback(null);
						}
					}
				});
			}
			// 同步店铺商品异常
			syncAbnormalProduct(taskCode) {
				commonModule.Ajax({
					url: "/api/Listing/ReSync?taskCode=" + taskCode,
					type: "POST",
					showMasker: false,
					loading: true,
					async: true,
					success: function (rsp) {
						if (rsp.Success) {
							commonModule.w_alert({ type: 4, content: '同步成功' });
						}
					},
					error: function (error) {
						console.log(error);
					}
				});
			}
			// 切换铺货状态
			changeStatus(Status) {
				const { queryParams } = this.state;
				queryParams.TargetShopId = null;
				queryParams.ProductName = '';
				$('input[name="ProductName"]').val('');
				this.setState({
					queryParams: {
						...queryParams,
						TaskStatus: Status
					}
				}, () => {
					this.LoadList();
				});
			}
			// 重置数据
			resetData = () => {
				const { queryParams } = this.state;
				queryParams.TargetShopId = null;
				queryParams.ProductName = '';
				this.setState({ queryParams }, function () {
					this.LoadList(true);
				});
				this.handleHideTip();
			}
			// 异常处理
			goToProcess = (type, item) => {
				// 创建商品, 点击跳转到基础商品，新建商品页面
				if (type === "CreateBaseProduct") {
					let ProductName = encodeURIComponent(item.ProductName)
					window.open(commonModule.rewriteUrl("/BaseProduct/NewBaseProduct?ProductName=" + ProductName + "&operateType=CreateBaseProduct"), "_self");
				}
				// 重新同步, 同步店铺商品异常
				if (type === "SyncShopProduct") {
					this.syncAbnormalProduct(item.ListingTaskCode);
				}
				// 前往处理, 点击跳转基础商品，查询当前商品显示
				if (type === "BaseProductBindSupplier") {
					window.open(commonModule.rewriteUrl("/BaseProduct/NewBaseProduct?BaseProductUId=" + item.BaseProductUId), "_blank");
				}
				// 前往处理, 点击跳转基础商品，查询当前商品显示
				if (type === "BaseOfPtSkuRelation") {
					window.open(commonModule.rewriteUrl("/BaseProduct/NewBaseProduct?BaseProductUId=" + item.BaseProductUId), "_blank");
				}
				// 前往处理, 点击跳转店铺商品列表，查询当前店铺商品显示
				if (type === "ProductBindSupplier") {
					// 获取自己的分区dbname
					commonModule.Ajax({
						url: "/api/Listing/GetSelfDbName",
						type: "GET",
						success: function (rsp) {
							if (rsp.Success) {
								const dbname = rsp.Data;
								const url = "/Common/Page/Product-Index?type=all&dbname=" + dbname + "&CloudPlatform=" + item.CloudPlatform + "&ProductId=" + item.ProductId;
								window.open(commonModule.rewriteUrl(url), "_blank");
							}
						}
					});
				}
			}
			// 查看详情
			viewDetails = (id, event) => {
				console.log('查看详情');
				event.stopPropagation();
				this.setState({ showTipIndex: id });
			}
			handleHideTip = () => {
				this.setState({ showTipIndex: null });
			}
			// 查看商品
			viewProduct = (item) => {
				if (item.TargetPlatformType != "TikTok") {
					commonModule.Ajax({
						url: "/api/Listing/GetDetailUrl",
						type: "GET",
						data: {
							platform: item.TargetPlatformType,
							ProductId: item.ProductId
						},
						success: function (rsp) {
							if (rsp.Success) {
								const url = rsp.Data;
								window.open(commonModule.rewriteUrl(url), "_blank");
							}
						}
					});
				} else {
					let url = '';
					if (item.SubPlatformType) { // 站点
						url = 'https://seller.tiktokglobalshop.com/product/manage';

					} else { // 全球
						url = 'https://seller.tiktokglobalshop.com/product/global';
					}
					window.open(commonModule.rewriteUrl(url), "_blank");
				}
			}
			// 重新铺货
			restocking = (item) => {

				var UidStr = item.BaseProductUId;
				var FromCode = item.ListingTaskCode;
				var PlatformType = item.TargetPlatformType;
				var TargetShopId = item.TargetShopId;
				var TargetShopName = item.TargetShopName;

				var data = {
					FromCode, PlatformType
				}
				commonModule.tarDistribution('edmit', UidStr, data, TargetShopId, TargetShopName, 'distributionLog');
			}
			// 处理空白处点击事件
			handleOutsideClick = (e) => {
				// console.log('点击空白');
				// this.setState({ showTipIndex: null });
				// if (this.tipRef && !this.tipRef.contains(e.target)) {
				//   this.setState({ showTipIndex: null });
				// }
			}
			// 获取页面数据
			LoadList(isPaging = false) {
				let { queryParams } = this.state;
				var that = this;
				if (isPaging) {
					queryParams.PageIndex = 1;
				}
				commonModule.Ajax({
					url: "/api/Listing/GetListingTaskRecord",
					type: "POST",
					loadingMessage: "查询中",
					showMasker: false,
					data: { ...queryParams, TargetShopId: queryParams.TargetShopId ? queryParams.TargetShopId.join(',') :'' },
					success: function (rsp) {
						if (rsp.Success) {
							const list = rsp.Data.Rows || [];

							for (let i = 0; i < list.length; i++) {
								const obj = list[i];
								obj.ImageUrl = commonModule.newTransformImgSrc(obj.ImageUrl);
								if (obj.ErrorMessage) {
									obj.ErrorMessage = obj.ErrorMessage.replace('/ffa/content/shop_deposit', "https://fxg.jinritemai.com/ffa/content/shop_deposit");
									obj.ErrorMessage = obj.ErrorMessage.replace(/<a href/g, '<a class="n-dColor btn-text" href');
								}
							}
							layui.laypage.render({
								elem: 'paging',
								count: rsp.Data.Total,
								limit: queryParams.PageSize,
								curr: queryParams.PageIndex,
								limits: [50, 100, 200, 300, 400, 500],
								layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
								jump: function (obj, first) {
									if (!first) {
										queryParams.PageIndex = obj.curr;
										queryParams.PageSize = obj.limit;
										that.setState({ queryParams: queryParams }, function () {
											that.LoadList();
										})
									}
								}
							});
							that.setState({ list: list })
						} else {
							layer.msg(rsp.Message);
						}
					}
				})
			}
			// 获取双角色用户铺货平台的店铺订购普通应用的情况
			getSubscribeNormalAppShops() {
				const that = this;
				commonModule.Ajax({
					url: '/api/Common/GetSubscribeNormalAppShops',
					type: 'POST',
					showMasker: false,
					success: function (rsp) {
						if (rsp.Success) {
							var data = rsp.Data;
							var list = data.filter(function (item) {
								return !item.IsSubscribeNormalApp;
							});
							console.log("list", list);
							if (list.length > 0) {
								that.setState({ showOrderTips: true, subscribeNormalShopCount: list.length });
							}
						}
					}
				});
			}
			// 立即订购
			goOrdering = () => {
				const url = "https://fuwu.jinritemai.com/detail?btm_ppre=a0254.b6901.c8954.d4082_2&btm_pre=a0254.b9825.c7579.d6976_1&page_from=1gr0710iq_b9825&pre_show_id=f7cd2d5b-41d3-40c4-a248-a862d79465a3&searchKey=%E5%BA%97%E7%AE%A1%E5%AE%B6&service_id=24069";
				window.open(commonModule.rewriteUrl(url), "_blank");
			}
			// 知道了
			closeTips = () => {
				this.setState({ showOrderTips: false });
			}
			// 在组件挂载后
			componentDidMount() {
				document.addEventListener('click', this.handleOutsideClick, false);
				let that = this;
				this.LoadList();
				var userFlag = commonModule.userFlag;
				console.log("铺货日志userFlag======", userFlag);
				if (userFlag !== "only_listing") {
					that.getSubscribeNormalAppShops();
				}
				$(function () {
					that.getShopData(function (data, Message) {
						if (data) {
							that.setState({ shopList: data }, function () {
								layui.use('form', function () {
									var $ = layui.$;
									var form = layui.form;
									form.render();
									// 提交事件
									form.on('submit(productLibrary)', function (data) {
										var field = data.field; // 获取表单字段值
										console.log("field", field);
										var queryParams = that.state.queryParams;
										queryParams = Object.assign(queryParams, field)
										that.setState({ queryParams: queryParams }, function () {
											that.LoadList(true);
											that.handleHideTip();
										})
										return false; // 阻止默认form跳转
									});
								});
							});
						}
					});
				});
				window.addEventListener('message', function (e) {
					if (e.data.operateType && e.data.operateType == "closePrepareDistribution") {   //ifarme关闭铺货 传过来的
						$("#PrepareDistributionIframeWrap").removeClass('active');
						$('body').css({ overflow: 'auto' });
						var times = e.data.times || null;
						if (times) {
							commonModule.w_alert({ type: 5, skin: 'goBackBatchSetSkin', times: 3000, content: '已取消本次铺货<span class="n-dColor mL12 hover" style="margin-left:12px;"  onclick="commonModule.newTarDistribution()">恢复</span>' });
						}
					}
					if (e.data.operateType && e.data.operateType == "StartDistribution") {   //ifarme关闭铺货传过来的
						that.getSubscribeNormalAppShops(); // 店铺订购普通应用情况
						$("#PrepareDistributionIframeWrap").removeClass('active');
						$('body').css({ overflow: 'auto' });
						var resultData = JSON.stringify(e.data.resultData);
						commonModule.alertDistribution(resultData, true);
					}
					if (e.data.isShowScreen) {
						$("#PrepareDistributionIframe").addClass('activeScreen').addClass('createProductIframe');

					} else {
						$("#PrepareDistributionIframe").removeClass('activeScreen').removeClass('createProductIframe');
					}

				});
				this.onWindowMessage();
			}
			// 组件卸载时，移除事件监听器
			componentWillUnmount() {
				document.removeEventListener('click', this.handleOutsideClick, false);
			}
			onChange (value) {
				let { queryParams } = this.state;
				queryParams.TargetShopId = value
				this.setState({ queryParams });
			}
			// 商品编辑状态
			onColumnEdit(item, index) {
				console.log('商品编辑状态', item, index);

				let { edmitIframeData } = this.state;
				let shopData = []
				shopData.push(
					{
						shopId: item.ShopId || '',
						shopName: item.ShopName || '',
						isActive: true,
						url: commonModule.rewriteUrlToMainDomain('/collect/EditGlobalProduct?token=@(Request.QueryString["token"])&dbname=@(Request.QueryString["dbname"])' + '&CreateFrom=edmit&detailId=' + item.PlatformId + '&shopId=' + item.ShopId + '&pageType=2&UniqueCode=' + item.ListingTaskCode)
					}
				)
				edmitIframeData = JSON.parse(JSON.stringify(shopData))
				console.log(edmitIframeData, "edmitIframeData=========")
				this.setState({ edmitIframeData }, () => {
					$("#n_tabNav_pt").css({ display: 'flex' });
					$('body').css({ overflow: 'hidden' });
					$("#createFullMaskName").addClass('active');
					$('body').css({ overflow: 'hidden' });
					this.forceUpdate()
				})
			}
			// 选择编辑店铺
			changeIframeNav(index) {

				let { edmitIframeData } = this.state;
				let that = this
				layer.open({
					type: 1,
					title: '未保存，是否切换到其他店铺？', //不显示标题
					content: '<div style="font-size:14px;" class="c06">切换后，本次所有更改不会生效。</div>',
					area: '560px', //宽高
					btn: ['留下', '切换店铺'],
					shade: false,
					skin: 'n-skin goBackskin closeShop',
					yes: function (index) {
						layer.close(index);
					},
					btn2: function () {
						edmitIframeData.forEach((item, i) => {
							item.isActive = false;
							if (index == i) {
								var iframe = document.getElementById('createProductIframe' + index);
								var iframeWindow = iframe.contentWindow;
								iframeWindow.postMessage('updateIframeData', '*');
								item.isActive = true;
							}
						})
						that.setState({ edmitIframeData }, () => {
							that.forceUpdate()
						});
					}
				});




			}
        	// 删除编辑店铺
        	delShopEdit(index) {
				const that = this
				layer.open({
					type: 1,
					title: '未保存，是否退出店铺编辑？', //不显示标题
					content: '<div style="font-size:14px;" class="c06">退出后，本次所有更改不会生效。</div>',
					area: '560px', //宽高
					btn: ['留下', '退出页面'],
					shade: false,
					skin: 'n-skin goBackskin closeShop',
					yes: function (index) {
						layer.close(index);
					},
					btn2: function () {
						let { edmitIframeData } = that.state
						if (edmitIframeData && edmitIframeData.length > 1) {
							edmitIframeData.splice(index, 1)
							let bool = edmitIframeData.some((e) => { return e.isActive })
							if (!bool) {
								edmitIframeData[0].isActive = true
							}
							that.setState({ edmitIframeData }, () => {
								that.forceUpdate()
							})
						} else {
							$("#createFullMaskName").removeClass('active');
							$("#businessCardCreateProductIframeWrap").removeClass('active');
							$('body').css({ overflow: 'auto' });
							edmitIframeData = []
							that.setState({ edmitIframeData }, () => {
								that.forceUpdate()
							})
						}
					}
				});

			}

        	// 关闭编辑
			closeCreateProduct(type) {
				$('body').css({ overflow: 'auto' });
				$("#createProductIframeWrap iframe").each(function (index, item) {
					if ($(item).hasClass('active')) {
						var iframe = document.getElementById('createProductIframe' + index);
						var iframeWindow = iframe.contentWindow;
						iframeWindow.postMessage('closeCreateProduct', '*');
					}
				});
			}

			// 监听window消息
			onWindowMessage() {
				var that = this
				window.addEventListener('message', function (e) {
					if (e.data.hidePopUp) {
						that.setState({
							isShowQuickAdd: false,
							isShowRelationProduct: false
						})
					}
					if (e.data.type == "update") {
						that.LoadList(true)
					}
					if (e.data.isShowScreen) {
						$(".createProductIframe").addClass('activeScreen');
					} else {
						$(".createProductIframe").removeClass('activeScreen');
					}

					if (e.data.operateType && e.data.operateType == "cretaeProduct") {   //ifarme创建商品和编辑商品完成 传过来的
						commonModule.w_alert({ type: 4, content: e.data.content });
						$("#createFullMaskName").removeClass('active');
						$("#businessCardCreateProductIframeWrap").removeClass('active');

						$('body').css({ overflow: 'auto' });
						that.LoadList();
					}

					if (e.data.operateType && e.data.operateType == "tarEdmitDouyunUrl") {
						var status = "edmit";
						var url = commonModule.rewriteUrl('/collect/EditCollectProduct?CreateFrom=' + status + '&baseproductuid=' + e.data.edmitBaseproductuid);
						that.createOrEdmitProduct(status, url);
					}

					if (e.data.operateType && e.data.operateType == "closePrepareDistribution") {   //ifarme关闭铺货 传过来的
						$("#PrepareDistributionIframeWrap").removeClass('active');
						$('body').css({ overflow: 'auto' });
					}

					if (e.data.operateType && e.data.operateType == "StartDistribution") {   //铺货提交后
						$("#PrepareDistributionIframeWrap").removeClass('active');
						$('body').css({ overflow: 'auto' });
						var resultData = JSON.stringify(e.data.resultData);
						commonModule.alertDistribution(resultData);
					}

					if (e.data.operateType && e.data.operateType == "closeCretaeProduct") {   //ifarme创建商品和编辑商品完成 传过来的
						$("#createFullMaskName").removeClass('active');
						$("#businessCardCreateProductIframeWrap").removeClass('active');
						$('body').css({ overflow: 'auto' });
						that.setState({ edmitIframeData: [] })
					}
				})
			}
			render() {
				const { list, tabList, shopList, queryParams, showTipIndex, AbnormalTypes, AbnormalTypeBtnText, showOrderTips, subscribeNormalShopCount, edmitIframeData } = this.state;
				return (
					<div className="distributionLog-container">
						{
							showOrderTips ? (
								<div className="order-tips-wrapper">
									<div className="order-tips-wrapper-header">
										<span className="order-tips-wrapper-header-title"><i class="iconfont icon-a-edit1x icon-a-notification-filled1x"></i>
											<span className="n-font5">应用订购提示</span>
										</span>
										<i class="iconfont icon-a-edit1x icon-a-close1x" onClick={this.closeTips}></i>
									</div>
									<div className="order-tips-wrapper-content n-font5">
										有 <span className="n-sColor">{subscribeNormalShopCount}</span> 个店铺未订购【店管家_分销代发】应用，将导致铺货商品无法推单给厂家代发，请前往订购。
									</div>
									<div className="order-tips-wrapper-footer">
										<span class="n-mButton n-sActive" onClick={this.closeTips}>知道了</span>
										<span class="n-mButton" style={{ marginLeft: "12px" }} onClick={this.goOrdering}>立即订购</span>
									</div>
								</div>
							) : null
						}
						<div className="distributionLog-title">铺货日志</div>
						<div className="distributionLog-content">
							<div className="tabs-wrapper">
								<ul className="n-tabNav">
									{
										tabList.length && tabList.map((item, index) => {
											return (
												<li
													className={`n-tabNav-item ${item.status === queryParams.TaskStatus ? 'active' : ''}`}
													onClick={() => this.changeStatus(item.status)}
													key={item.id}
												>
													{item.label}
												</li>
											)
										})
									}
								</ul>
							</div>
							<div className="form-wrapper">
								<form className="layui-form layui-form-pane n-layui-form" action="">
									<div className="layui-form-item flex">
										@*<div className="n-layui-input n-layui-input-inline layui-input-inline-label flex" style={{ width: '240px' }}>
											<span className="label">铺货店铺</span>
											<select name="TargetShopId" lay-filter="TargetShopId" className="layui-select">
												<option value="">请选择</option>
												{
													shopList.length && shopList.map((item, index) => {
														return (
															<option value={item.ShopId}>{item.ShopName}</option>
														)
													})
												}
											</select>
										</div>*@
										<div className="n-layui-input layui-input-inline layui-input-inline-label flex common-m-l-8 Cascader-box" style={{ width: '240px' }}>
											<span className="label" style={{ paddingRight: '0' }}>铺货店铺</span>
											<antd.Cascader
												popupClassName="custom-dropdown"
												style={{ width:'176px' }}
												options={ shopList }
												allowClear={ false }
												value={ queryParams.TargetShopId }
												fieldNames={{ label: "ShopName", value: "ShopId", children: "children" }}
												onChange={(e) => this.onChange(e)} placeholder="请选择"
											/>
										</div>
										<div className="n-layui-input layui-input-inline layui-input-inline-label flex common-m-l-8" style={{ width: '240px' }}>
											<span className="label">商品名称</span>
											<input type="text" name="ProductName" autoComplete="off" className="layui-input" placeholder="请输入" />
										</div>
										<div className="layui-input-inline flex" style={{ width: 'auto' }}>
											<button className="n-mButton n-pActive  wu-btn wu-btn-mid wu-primary wu-two" lay-submit="true" lay-filter="productLibrary">查询</button>
											<button type="reset" className="n-mButton n-sActive  wu-btn wu-btn-mid wu-primary" onClick={this.resetData}>重置</button>
										</div>
									</div>
								</form>
							</div>
							<div className="layui-mytable" style={{ overflowX: 'auto' }}>
								<table className="stockup_table_content new-mytable">
									<thead>
										<tr className="table-th-text">
											<th style={{ paddingLeft: '16px', minWidth: '240px' }}>
												<span>商品</span>
											</th>
											<th style={{ minWidth: '150px' }}>
												<span>商品来源</span>
											</th>
											<th style={{ minWidth: '220px' }}>
												<span>铺货平台/店铺</span>
											</th>
											<th style={{ minWidth: '150px' }}>
												<span>铺货时间</span>
											</th>

											<th style={{ minWidth: '150px' }}>
												<span>铺货状态</span>
											</th>
											<th style={{ minWidth: '150px' }}>
												<span>商品状态</span>
											</th>
											<th style={{ minWidth: '150px' }}>
												<span>状态说明</span>
											</th>
											<th style={{ textAlign: "center", minWidth: '150px' }}>
												<span>操作</span>
											</th>
										</tr>
									</thead>
									<tbody >
										{
											list.length > 0 ? list.map((item, index) => {
												return (
													<tr key={item.index}>
														<td>
															<div className="productShow flex">
																{
																	item.ImageUrl ? <img src={item.ImageUrl} className="productShow-img" /> : <img src="/Content/images/nopic.gif" className="productShow-img" />
																}
																<div className="productShow-name ellipsis2 n-font5" title={item.ProductName}>{item.ProductName}</div>
															</div>
														</td>
														<td>
															{
																item.SupplierName ? <span class="n-tarTxt n-tarTxt04">{item.SupplierName}</span> : <span class="n-tarTxt n-tarTxt05">自营</span>
															}
														</td>
														<td>
															{
																<ul className="layui-content-li-icons">
																	<span className={`wu-pintaiIcon wu-small ${item.TargetPlatformType}`}></span>



																	<li className="n-font5" style={{ marginLeft: '4px' }}>
																		<div>{item.TargetShopName}</div>
																		{
																			item.TargetPlatformType == 'TikTok' ?<div style={{ color: 'rgba(0, 0, 0, 0.6)' }}>{item.SubPlatformType ? item.SubPlatformType : '全球'}</div> : null
																		}
																	</li>
																</ul>
															}
														</td>
														<td>
															<span className="n-font5">{new Date(item.CreateTime).Format()}</span>
														</td>
														<td>
															{
																item.TaskStatus === "Success" ? <span className="n-tarTxt n-tarTxt04">铺货成功</span> :
																	item.TaskStatus === "Doing" ? <span className="n-tarTxt n-tarTxt03">正在铺货</span> :
																		item.TaskStatus === "Error" || item.Status === "Error_Repeat" ? <span className="n-tarTxt n-tarTxt02">铺货失败</span> : null
																//item.TaskStatus === "Created" ? <span class="n-tarTxt n-tarTxt01">待铺货</span> : null
															}
														</td>
														<td className="n-font5">
															<div className="" style={{display: 'flex', flexDirection: 'column', alignItems: 'flex-start'}}>
																<span>{item.ListingProductStatusStr}</span>
																<span className="color04">{item.ProductId ? 'ID:' + item.ProductId : ''}</span>
															</div>
														</td>
														<td style={{ position: 'relative' }}>
															{
																(item.AbnormalList && item.AbnormalList.length > 1) && !item.ErrorMessage ?
																	<React.Fragment>
																		<span className="n-sColor btn-text">关联商品异常</span>
																		<span className="n-dColor btn-text common-m-l-8" onClick={(event) => this.viewDetails(index, event)}>
																			查看详情
																		</span>
																		{showTipIndex === index && (
																			<div className="tooltip-wrap">
																				<div ref={(node) => { this.tipRef = node; }} className="n-tooltip n-leftUp">
																					<div className="n-font5">这里是关联商品的目的说明</div>
																					<ul className="common-m-t-16">
																						{
																							item.AbnormalList && item.AbnormalList.map((d, index) => {
																								return (
																									<li
																										className="flex common-m-b-8"
																										key={index}
																									>
																										<div className="tip-common-icon tip-error-icon">
																											<i class="iconfont icon-chuyidong iconfont-size"></i>
																										</div>
																										<span className="n-font5 common-m-l-8">{AbnormalTypes[d.BusinessType]}</span>
																										<span className="n-dColor btn-text common-m-l-8" onClick={() => this.goToProcess(d.BusinessType, item)}>{AbnormalTypeBtnText[d.BusinessType]}</span>
																									</li>
																								)
																							})
																						}
																					</ul>
																				</div>
																			</div>
																		)}
																	</React.Fragment>
																	:
																	(item.AbnormalList && item.AbnormalList.length === 1) && !item.ErrorMessage ?
																		<React.Fragment>
																			<span className="n-sColor btn-text">{AbnormalTypes[item.AbnormalList[0].BusinessType]}</span>
																			<span className="n-dColor btn-text common-m-l-8" onClick={() => this.goToProcess(item.AbnormalList[0].BusinessType, item)}>{AbnormalTypeBtnText[item.AbnormalList[0].BusinessType]}</span>
																		</React.Fragment>
																		:
																		!item.AbnormalList && item.ErrorMessage ?
																			<React.Fragment>
																				<div className="n-font5" dangerouslySetInnerHTML={{__html: item.ErrorMessage}} />
																			</React.Fragment>
																			:
																			"-"
															}
														</td>
														<td style={{ width: "180px", textAlign: "center" }}>
															{
																item.TaskStatus === "Success" ? <span className="n-dColor btn-text" onClick={() => this.viewProduct(item)}>查看商品</span> :
																	item.TaskStatus === "Error" && item.TargetPlatformType != "TikTok"
																	? <span className="n-dColor btn-text" onClick={() => this.restocking(item)}>重新铺货</span>
																	: item.TaskStatus === "Error" && item.TargetPlatformType == "TikTok" ? <span className="n-dColor btn-text" onClick={() => this.onColumnEdit(item,index)}>重新铺货</span> : null
															}
														</td>
													</tr>
												)
											}) :
												<tr>
													<td colSpan="20" className="tdNodata" style={{ padding: "24px 16px" }}>
														<div className="n-tableNoDataShow">
															<span className="iconfont icon-wushuju1"></span>
															<span className="tableNoDataShow-title">未找到符合条件的内容</span>
														</div>
													</td>
												</tr>
										}

									</tbody>
								</table>

								<div className="layui-myPage" id="paging"></div>
							</div>

						</div>

						<div class="new-full-mask iframeMask" id="createFullMaskName">
							<div class="full-mask-back" ></div>
							<div class="full-mask-content-wrapper full-mask-right baseProductfull-mask-right" style={{ width: '1088px' }}>
								<div class="full-mask-header" style={{ width: '888px' }}>
									<div class="full-mask-title c09" id="headerTitle">编辑商品</div>
									<i class="iconfont icon-chuyidong" onClick={() => this.closeCreateProduct("classAll")}></i>
								</div>
								<ul class="n-tabNav" id="n_tabNav_pt" style={{ width: '888px', marginBottom: '0px' }}>
									{
										this.state.edmitIframeData && this.state.edmitIframeData > 1 && this.state.edmitIframeData.map((item, index) => {
											return (
												<li style={{ margin: "0px 8px" }} onClick={(e) => { e.stopPropagation(); this.changeIframeNav(index) }} class={item.isActive ? 'n-tabNav-item active' : 'n-tabNav-item'}><span>{item.shopName}</span><i onClick={(e) => { e.stopPropagation(); this.delShopEdit(index) }} className="iconfont icon-a-close1x" style={{ fontSize: '14px', marginLeft: '8px' }}></i></li>
											)
										})
									}
								</ul>
								<div class="full-mask-content" id="createProductIframeWrap">
									{
										this.state.edmitIframeData.map((item, index) => {
											return (
												<iframe id={"createProductIframe" + index} class={item.isActive ? 'createProductIframe active' : 'createProductIframe'} src={item.url} frameborder="0"></iframe>
											)
										})
									}
								</div>
							</div>
						</div>
					</div>
				)
			}
		}
		// 2. 渲染组件到页面
		ReactDOM.render(<LogComponent />, document.getElementById('DistributionLogApp'))
		}
    </script>
}