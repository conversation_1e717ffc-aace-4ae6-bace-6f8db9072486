@{
    ViewBag.Title = "我的店铺";
    ViewBag.MenuId = "Partner";
    ViewBag.MenuActive = "Authorization";
}
@section Header{
    <style type="text/css">
        .icon-tianjia {
            margin-right: 3px;
        }

        .layui-mysearch {
            margin: 15px 0;
            padding: 15px;
            background-color: #f8f8f8;
        }

        .layui-mywrap {
            padding-bottom: 15px;
        }

        .adialog-Shops {
            background-color: #fff;
            display: none;
        }

            .adialog-Shops > .adialog-Shops-title {
                font-size: 14px;
                color: #000;
            }



        .mysearch-partOne {
            margin-bottom: 0;
        }

        .stockup_table_content {
            width: auto;
        }

        .support-platform-content {
            padding: 15px;
            box-sizing: border-box;
        }

            .support-platform-content > div {
                margin-bottom: 5px;
            }

            .support-platform-content > input {
                height: 35px;
                width: 270px;
            }

        .support-platform-into {
            width: 350px;
            box-sizing: border-box;
            background-color: #fff;
            display: none;
        }

            .support-platform-into .support-platform-title {
                background-color: #F8F8F8;
                padding: 15px;
                position: relative;
            }

            .support-platform-into .icon-jia-copy1 {
                position: absolute;
                right: 15px;
                color: #2d2c3b;
                font-size: 18px;
                font-weight: 700;
            }

        .support-platform-wxhelpShow {
            display: none;
            width: 1200px;
            height: 700px;
            border: 2px solid #fff;
        }

        .layui-warnin {
            display: flex;
            border: 1px solid #f59c1a;
            margin-bottom: 15px;
            padding: 10px 15px;
        }

            .layui-warnin > ul > li {
                padding: 2px 0;
            }

        .iconfontGan {
            width: 20px;
            height: 20px;
            border: 2px solid #f7941f;
            color: #f7941f;
            display: inline-block;
            border-radius: 50%;
            font-weight: 700;
            text-align: center;
            font-size: 17px;
            margin-right: 10px;
            margin-top: 2px;
            line-height: 20px;
        }

        .layui-warnin-title {
            font-size: 17px;
            font-weight: 700;
            line-height: 20px;
            color: #04385d;
            margin-bottom: 5px;
        }

            .layui-warnin-title i {
                color: #ff511c;
                font-size: 20px;
                padding: 0 3px;
            }

        .span-warn {
            color: #ff511c;
            margin-left: 10px;
            font-size: 14px;
        }

        .adialog-Shops-warn {
            font-size: 18px;
            font-weight: 700;
            color: #ff6161;
            /* border: 1px solid #e2e2e2; */
            padding: 10px;
            background-color: #fff5f5;
            display: none;
            margin: 15px;
            line-height: 50px;
        }

        .dot-i {
            color: #ff0000;
            font-size: 14px;
            font-weight: 700;
            margin-right: 8px;
        }

        .edmitWrap {
            padding: 25px;
        }

            .edmitWrap i {
                color: #fe6f4f;
            }

            .edmitWrap input[type=text] {
                width: 380px;
                height: 30px;
                padding-left: 5px;
            }

        .adialog-Shops .stockup_table_content thead tr th, .adialog-Shops .stockup_table_content tbody tr td {
            background-color: #f8f8f8;
        }

        .adialog-Shops > .stockup_table_content > tbody > tr > td {
            height: 52px;
        }

            .adialog-Shops > .stockup_table_content > tbody > tr > td:hover {
                background-color: #f5fbff;
                position: relative;
            }

                .adialog-Shops > .stockup_table_content > tbody > tr > td:hover:before {
                    display: block;
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    content: "";
                    border: 1px solid #78c6ff;
                    top: 0;
                    left: 0;
                }

        .newVersionsLeftNav .layui-mywrap {
            margin-top: 65px !important;
        }

        .newVersionsLeftNav.hasTopBanner .layui-mywrap {
            margin-top: 15px !important;
        }

        .togglePlatform {
            width: 620px;
            padding: 20px;
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
        }

            .togglePlatform .togglePlatform-title {
                padding: 10px;
                display: flex;
                flex-direction: row;
                justify-content: center;
                align-items: center;
                font-weight: 700;
                font-size: 20px;
                color: #333;
                border-bottom: 1px solid #e2e2e2;
                padding-bottom: 30px;
                margin-bottom: 10px;
            }

            .togglePlatform .togglePlatform-content {
                display: flex;
                flex-direction: row;
                justify-content: space-around;
                align-items: center;
                margin-top: 10px;
                padding: 0 20px;
            }

                .togglePlatform .togglePlatform-content .togglePlatform-content-item {
                    padding: 15px;
                    flex-direction: column;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    cursor: pointer;
                    border: 1px solid #fff;
                    border-radius: 7px;
                }

                    .togglePlatform .togglePlatform-content .togglePlatform-content-item:hover {
                        box-shadow: 0 0 12px 1px #90d1ff;
                    }

                    .togglePlatform .togglePlatform-content .togglePlatform-content-item.active {
                        box-shadow: 0 0 12px 1px #90d1ff;
                    }

                        .togglePlatform .togglePlatform-content .togglePlatform-content-item.active .togglePlatform-content-item-title {
                            color: #f69906;
                        }

                    .togglePlatform .togglePlatform-content .togglePlatform-content-item .togglePlatform-content-item-img {
                        width: 140px;
                        height: 140px;
                        background-image: url('/Content/images/toutiao-platform.png');
                        background-position: 0 0;
                        display: inline-block;
                    }

                    .togglePlatform .togglePlatform-content .togglePlatform-content-item:nth-child(2) .togglePlatform-content-item-img {
                        background-position: -140px 0;
                    }

                    .togglePlatform .togglePlatform-content .togglePlatform-content-item .togglePlatform-content-item-title {
                        font-size: 16px;
                        margin-top: 20px;
                    }

        .sqBtn {
            cursor: pointer;
            /*            border: 1px solid #f7941f;
            padding: 2px 6px;*/
            color: #f7941f;
            border-radius: 3px;
        }

            .sqBtn:hover {
                opacity: 0.8;
            }

        .touTiaoOldUserAd02 {
            margin-bottom: 15px;
            width: 1200px;
            height: 60px;
            display: none;
        }

        .setUnBindDailog {
            display: flex;
            flex-direction: column;
            font-size: 16px;
            color: #888;
            align-items: center;
            padding: 15px;
            box-sizing: border-box;
            line-height: 25px;
        }

        .cancelUnBindDailog {
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            font-size: 16px;
            color: #888;
            padding: 15px;
            box-sizing: border-box;
            line-height: 30px;
        }

            .cancelUnBindDailog .my-layer-ico {
                margin-right: 10px;
            }

        .setTimeWrap {
            display: flex;
            flex-direction: row;
            align-items: center;
        }

            .setTimeWrap > .setTimeWrap-operate {
                display: flex;
                flex-direction: column;
                margin-left: 2px;
            }

                .setTimeWrap > .setTimeWrap-operate span:nth-child(1) {
                    width: 0;
                    height: 0;
                    border-left: 6px solid transparent;
                    border-right: 6px solid transparent;
                    border-bottom: 6px solid #888;
                    margin-bottom: 1px;
                    cursor: pointer;
                }

                    .setTimeWrap > .setTimeWrap-operate span:nth-child(1).active {
                        border-bottom: 6px solid #3aadff;
                    }

                .setTimeWrap > .setTimeWrap-operate span:nth-child(2) {
                    width: 0;
                    height: 0;
                    border-left: 6px solid transparent;
                    border-right: 6px solid transparent;
                    border-top: 6px solid #888;
                    cursor: pointer;
                }

                    .setTimeWrap > .setTimeWrap-operate span:nth-child(2).active {
                        border-top: 6px solid #3aadff;
                    }

        .support-platform-wxhelpShow {
            position: relative;
        }

            .support-platform-wxhelpShow .support-platform-more {
                position: absolute;
                top: 582px;
                left: 565px;
                font-size: 26px;
                font-weight: 700;
                color: #3aadff;
                cursor: pointer;
            }

                .support-platform-wxhelpShow .support-platform-more::after {
                    display: block;
                    content: "";
                    width: 50px;
                    height: 50px;
                    border-bottom: 5px solid #3aadff;
                    border-right: 5px solid #3aadff;
                    display: inline-block;
                    position: absolute;
                    bottom: -45px;
                    left: 15px;
                    transform: rotate(45deg);
                    animation: operateLasChildL 0.4s linear infinite forwards;
                }

        @@keyframes operateLasChildL {
            0% {
                bottom: -45px;
            }

            100% {
                bottom: -40px;
            }
        }

        body .SRcotainer {
            top: 220px;
            left: 180px;
            /*background-color: #ecf0f5;*/
            position: absolute;
            align-items: unset;
        }

        .layui-mysearchcontent {
            display: flex;
            flex-direction: row;
        }

            .layui-mysearchcontent .layui-form {
                margin-left: 15px;
            }

        .layui-mysearch-operate {
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
        }

            .layui-mysearch-operate .span-warn {
                margin: 10px 0;
            }

        .layui-mysearchcontent-a {
            position: absolute;
            right: 0;
        }

            .layui-mysearchcontent-a .layui-mysearchcontent-a-img {
                width: 180px;
            }

        .ssq-img {
            width: 350px;
            height: 50px;
        }

        .ssq-img {
            position: relative;
            top: 12px;
            left: 200px;
            cursor: pointer;
        }

        .layui-mysearchcontent-a-img02 {
            width: 250px;
            height: 45px;
            margin-right: 10px;
            cursor: pointer;
        }

        .span-warn {
            display: flex;
            flex-direction: column;
        }

        .span-warn-main {
            display: flex;
            flex-direction: column;
            padding: 5px;
            background-color: #f5f5f5;
            margin-top: 5px;
            font-size: 12px;
            color: #666;
            line-height: 20px;
        }

        .selectWrap .selectMore {
            height: 35px;
            background-color: #fff;
            line-height: 35px;
            cursor: pointer;
        }

        .selectWrap .showMoreicon {
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-top: 6px solid #c2c2c2;
            position: absolute;
            top: 7px;
            right: 8px;
            z-index: 100;
            top: 18px !important;
        }

        body .selectWrap-box {
            top: 40px;
        }

        .selectMore-choose-title {
            height: 35px;
        }

        .selectMore-ul > li > label input[type="checkbox"] {
            display: inline-block;
        }

        .adialog-Shops .stockup_table_content thead tr th, .adialog-Shops .stockup_table_content tbody tr td {
            border: unset;
        }

        #emergency_unbinding .registerInto-main {
            width: 420px;
            margin: 0 auto;
            overflow: hidden;
        }

            #emergency_unbinding .registerInto-main > .Code {
                display: inline-block;
                background-color: #fff;
                width: 205px;
                height: 45px;
                padding: 7px;
                box-sizing: border-box;
                border-bottom: 1px solid #eee;
            }

                #emergency_unbinding .registerInto-main > .Code > input {
                    width: 100px;
                    height: 28px;
                    border: none
                }

            #emergency_unbinding .registerInto-main input {
                font-size: 14px;
                font-family: 微软雅黑;
            }

            #emergency_unbinding .registerInto-main > label {
                display: flex;
                height: 45px;
                box-sizing: border-box;
                background-color: #fff;
                position: relative;
                border-bottom: 1px solid #eee;
                align-items: center;
                box-sizing: border-box;
                padding-left: 5px;
            }

                #emergency_unbinding .registerInto-main > label > .iconfont {
                    width: 15px;
                    height: 18px;
                    background-repeat: no-repeat;
                    display: inline-block;
                    margin-right: 15px;
                }

                #emergency_unbinding .registerInto-main > label > .icon-shouji1 {
                    background-position: 0 0;
                }

                #emergency_unbinding .registerInto-main > label > .icon-mima3 {
                    background-position: -15px 0;
                }

                #emergency_unbinding .registerInto-main > label > .icon-duanxin {
                    background-position: -30px 0;
                }

                #emergency_unbinding .registerInto-main > label > input {
                    border: none;
                    height: 30px;
                    width: 200px;
                    outline: none;
                }

                #emergency_unbinding .registerInto-main > label > #LoginCode {
                    color: #3cc3a2;
                    position: absolute;
                    top: 0;
                    right: 10px;
                    cursor: pointer;
                }

        .setEye {
            width: 22px;
            height: 11px;
            background-image: url(/Content/images/puhuo-icon-1.png);
            position: absolute;
            top: 18px;
            right: 10px;
            background-position: -32px -11px;
            cursor: pointer;
        }

            .setEye.close {
                background-position: -32px 0;
            }

        #emergency_unbinding .registerInto-button {
            height: 45px;
            width: 160px;
            border-radius: 5px;
            background-color: #3aadff;
            line-height: 40px;
            text-align: center;
            color: #fff;
            margin: 20px auto;
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }

            #emergency_unbinding .registerInto-button:hover {
                background-color: #79ceff;
            }

        #LoginCode1, #LoginCode2 {
            position: absolute;
            right: 10px;
            top: 13px;
        }

        input::placeholder {
            font-size: 12px;
        }

        .status-flex {
            display: flex;
            align-items: center;
        }

        #tb_support_platform tr td {
            position: relative;
        }

            #tb_support_platform tr td::after {
                position: absolute;
                content: "";
                display: block;
                width: 16px;
                background-color: #fff;
                height: 48px;
                top: 0;
                right: 0;
            }

        .delivery-mode-select-card {
            padding: 12px 16px;
            box-sizing: border-box;
            background: rgba(0, 0, 0, 0.04);
            border-radius: 8px;
        }

        .flex {
            display: flex;
            align-items: center;
        }

        .mT4 {
            margin-top: 4px;
        }

        .mT16 {
            margin-top: 16px;
        }

        .mL8 {
            margin-left: 8px;
        }

        .delivery-mode-select-wrapper {
            padding: 16px;
            box-sizing: border-box;
        }

        .delivery-mode-select-radio {
            border: 1px solid rgba(0, 0, 0, 0.09);
            padding: 16px;
            box-sizing: border-box;
            border-radius: 8px;
        }

            .delivery-mode-select-radio input[type=radio] {
                width: 15px;
                height: 15px;
            }

        .delivery-mode-select-footer {
            padding: 12px 16px;
            box-sizing: border-box;
            border-top: 1px solid rgba(0, 0, 0, 0.09);
        }

        #support_OtherPlatforms > ul > li > label select {
            width: 170px;
            border: 1px solid #e2e2e2;
            box-sizing: border-box;
            height: 35px;
        }

        #support_Other_JuHaoMai ul > li > label,
        #support_Other_JuHaoMai ul > li > label > span,
        #support_Other_HaoYouDuo ul > li > label,
        #support_Other_HaoYouDuo ul > li > label > span,
        #support_Other_Heliang ul > li > label > span {
            width: 70px;
            text-align: right;
            display: inline-block;
            padding-right: 10px;
        }

        #support_Other_JuHaoMai ul > li,
        #support_Other_JuHaoMai > ul > li > label input[type=text],
        #support_Other_HaoYouDuo ul > li,
        #support_Other_HaoYouDuo > ul > li > label input[type=text],
        .add-new-shop-dailog .layui-layer-content, .add-support-platform-dailog .layui-layer-content {
            padding: 0 !important;
            border-radius: 10px 10px 0 0;
        }

        .selectWrap.wu-select-skin .selectMore-choose > li .selectMore-choose-title {
            max-width: 85px;
        }

        .selectWrap.wu-select-skin .showMoreicon {
            z-index: 12;
        }

        .wu-search-wrap .wu-layui-select .layui-unselect input[type=text] {
            color: rgba(0,0,0,0.9);
        }

        .wu-search-wrap .wu-layui-select.wu-active .layui-unselect input[type=text] {
            color: rgba(0,0,0,0.9);
        }
        #AddNewShop .icon-tianjia {
            display:none;
        }
        #AddNewShop {
            box-sizing: border-box;
            outline: none;
            position: relative;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            white-space: nowrap;
            text-align: center;
            background-color: #0888ff !important;
            color: #fff;
            cursor: pointer;
            letter-spacing: 0em;
            line-height: normal;
            border: unset;
            height: 32px;
            font-size: 14px;
            padding: 6px 12px;
            border-radius: 6px;
        }
    </style>
}

<div style="min-width: 1200px;">
    @if (ViewBag.IsShowCrossBorder == true)
    {
        <div class="wu-container wu-toFull" style="position: sticky; z-index: 15; top: 50px;" id="new_and_old_version_navigation">
            <div class="layui-tab layui-tab-brief wu-layui-tab" lay-filter="test-hash">
                <ul id="ulShopType" class="layui-tab-title">
                    <li data-shopType="1" class="active layui-this">国内电商</li>
                    <li data-shopType="2"><a href="/partner/crossborder">跨境电商</a></li>
                </ul>
            </div>
        </div>
    }
    <div class="wu-p16">
        <div class="wu-color-n wu-background wu-p16 wu-8radius wu-search-wrap">
            <div class="wu-flex wu-yCenter">
                @{
                    //是否允许添加店铺（超出当前版本的店铺数量时需限制）
                    if (ViewBag.IsAddAvailable)
                    {
                        <button type="button" onclick="partnerModule.AddShopWarn()" id="AddNewShop" class="layui-btn layui-btn-normal layui-btn35" style="background-color: #f59c1a;"><i class="iconfont icon-tianjia"></i>添加新店铺</button>
                    }
                    else
                    {
                        <button type="button" onclick="commonModule.ProcessVersionErrorCode('NEED_UPGRADE_SHOP_COUNT')" title="" id="AddNewShop" class="layui-btn my-btn-commonStop layui-btn35 CheckVirtualRegMobile" style="background-color: hsl(0deg 0% 87%); "><i class="iconfont icon-tianjia"></i>添加店铺</button>
                    }
                }
                <form class="layui-form wu-flex wu-yCenter wu-mL8">
                    <div style="width: 180px;" class="wu-flex" id="domestic_shop_select">
                        <div id="sel_shops" class="selectWrap wu-form-mid wu-select-skin"></div>
                    </div>
                    <div class="wu-layui-select wu-form-mid wu-mL8" style="width: 180px;">
                        <select id="sel_status" lay-filter="active-select-filter">
                            <option value="">授权状态</option>
                        </select>
                    </div>
                    <div class="layui-inline">
                        <button type="button" onclick="partnerModule.Search()" class="wu-btn wu-btn-mid wu-primary wu-two wu-mL8">查询</button>
                    </div>
                </form>
            </div>
            <div class="wu-6radius wu-color-m wu-background wu-flex wu-pT12 wu-pB12 wu-pL16 wu-pR16 wu-mT16">
                <i class="iconfont icon-jiaocheng1 wu-color-a"></i>
                <div class="wu-mL4 wu-f12">
                    <div class="wu-color-e wu-weight500">店铺绑定指南</div>
                    <div class="wu-color-b wu-mT2">
                        请勿绑定合作商的店铺！一个店铺只能绑定一个账号！一个账号支持绑定多个店铺！
                    </div>
                    <div class="wu-color-m wu-mT2">
                        举个例子：
                    </div>
                    <div class="wu-color-m wu-mT2">
                        A店铺已绑定在168********的手机账号，那么A不能再绑定给其他手机账号，而168********的手机账号可以绑定A，B，C等多个店铺，绑定数量暂未限制。
                    </div>
                </div>
            </div>
        </div>
        <div class="wu-color-n wu-background wu-mT16 wu-8radius">
            <div class="wu-p16">
                <div class="wu-6radius wu-color-c wu-background wu-p12 wu-flex wu-mB16" id="layui_warnin" style="display: none">
                    <i class="iconfont icon-a-info-circle-filled1x wu-color-c"></i>
                    <ul class="wu-mL4">
                        <li class="wu-f14 wu-c09 wu-weight500">您有 <i id="warnin_mun" class="wu-color-b">0</i> 个店铺的服务即将到期或已到期，处理方式如下：</li>
                        <li class="wu-f12 wu-c09 wu-mT2">1.状态为【服务到期】点击[续费]续费成功后即可使用<a class="wu-color-a wu-operate wu-mL4" href="https://docs.qq.com/sheet/DQ29TVldGTHpXWGRk?tab=BB08J2&token=A5B9E4587C522F606AFBDE6866B1B91A&dbname=wdJM8OZZiF60rm73/6ttwOCsQNNRTq3m" target="_blank">点击查看所有平台订单链接</a></li>
                        <li class="wu-f12 wu-c09 wu-mT2">2.状态为【授权到期】点击[授权]即可使用</li>
                    </ul>
                </div>
                <div>
                    <div class="wu-tableWrap">
                        <table class="stockup_table_content wu-table-one">
                            <thead>
                                <tr>
                                    <th style="min-width:100px;">店铺名称</th>
                                    <th style="min-width:120px;">平台</th>
                                    <th style="min-width:150px;" id="show_white_user_element">功能权限</th>
                                    <th>订购应用名称</th>
                                    <th>状态</th>
                                    <th style="min-width:120px;">创建时间</th>
                                    <th style="min-width:120px;">
                                        <div class="setTimeWrap">
                                            <span>服务到期时间</span>
                                            <div class="setTimeWrap-operate">
                                                <span id="AuthAscArrow" onclick="partnerModule.orderbyColl(1)"></span>
                                                <span id="AuthDescArrow" onclick="partnerModule.orderbyColl(2)"></span>
                                            </div>
                                        </div>
                                    </th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="ShareShopList_body"></tbody>
                        </table>
                    </div>
                    <div class="layui-myPage" id="paging"></div>
                </div>
            </div>
        </div>
    </div>
</div>
@*<div class="layui-mysearch">
        <form class="layui-form">
            <div class="layui-inline mysearch-partOne">
                <div class="layui-input-inline" style="width: 160px;">
                    <select id="sel_shop">
                        <option value="">店铺名称</option>
                    </select>
                </div>
            </div>
            <div class="layui-inline mysearch-partOne">
                <div class="layui-input-inline" style="width: 160px;">
                    <select id="sel_status">
                        <option value="">授权状态</option>
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <button type="button" onclick="partnerModule.Search()" class="layui-btn layui-btn-normal layui-btn35 CheckVirtualRegMobile">查询</button>
            </div>
        </form>
    </div>*@
@*<img id="touTiaoOldUserAd02" class="touTiaoOldUserAd02" src="~/Content/images/noviceIntroPic/douyin-gaizao-icons-2.png"  />*@
<div class="support-platform-into" id="support_platform_wxinto">
    <div class="support-platform-title">
        <span id="support_platform_title" class="wu-f14 wu-c09">输入小程序ID</span>
        <span class="wu-color-a wu-operate wu-f14 wu-mL24" onclick="partnerModule.platformHelp('wx')">使用教程</span>
        <i class="iconfont icon-jia-copy1" onclick="partnerModule.closePlatformInto()"></i>
    </div>
    <div class="support-platform-content" id="support_platform_WxXiaoShangDian">
        <div class="wu-f14 wu-c06">例：wxf414f0****1845</div>
        <div class="wu-inputWrap wu-form-mid" style="width: 100%;">
            <input class="wu-input" type="text" name="shopId" placeholder="输入小程序ID" />
            <span class="iconfont icon-a-close-circle-filled1x" onclick="wuFormModule.clearInput(this)"></span>
        </div>
    </div>
</div>
@*微信视频号*@
<div class="support-platform-into" id="support_platform_wxvinto">
    <div class="support-platform-title">
        <span id="support_platform_title" class="wu-f14 wu-c09">输入小程序ID</span>
        <span class="wu-color-a wu-operate wu-f14 wu-mL24" onclick="partnerModule.platformHelp('wxv')">使用教程</span>
        <i class="iconfont icon-jia-copy1" onclick="partnerModule.closePlatformInto()"></i>
    </div>
    <div class="support-platform-content" id="support_platform_WxVideo">
        <div class="wu-f14 wu-c06">例：wxf414f0****1845</div>
        <div class="wu-inputWrap wu-form-mid" style="width: 100%;">
            <input class="wu-input" type="text" name="shopId" placeholder="输入小程序ID" />
            <span class="iconfont icon-a-close-circle-filled1x" onclick="wuFormModule.clearInput(this)"></span>
        </div>
    </div>
</div>

<div class="support-platform-into" id="support_platform_ownshop">
    <div class="support-platform-title">
        <span id="support_platform_title" class="wu-f14 wu-c09">输入店铺名称</span>
        <i class="iconfont icon-jia-copy1" onclick="partnerModule.closePlatformInto()"></i>
    </div>
    <div class="support-platform-content" id="support_platform_OwnShop">
        <div class="wu-f14 wu-c06">例：XXXX店</div>
        <div class="wu-inputWrap wu-form-mid" style="width: 100%;">
            <input class="wu-input" type="text" placeholder="请输入店铺名称" name="shopId" />
            <span class="iconfont icon-a-close-circle-filled1x" onclick="wuFormModule.clearInput(this)"></span>
        </div>
    </div>
</div>

<div class="support-platform-into" id="new_support_platform_wxvinto">
    <div class="support-platform-title">
        <span id="support_platform_title" class="wu-f14 wu-c09">输入小程序ID</span>
        <span class="wu-color-a wu-operate wu-f14 wu-mL24" onclick="partnerModule.platformHelp('newwxv')">使用教程</span>
        <i class="iconfont icon-jia-copy1" onclick="javascript:$('.new-platform-HelpBailog .layui-layer-btn1').trigger('click')"></i>
    </div>
    <div class="support-platform-content" id="new_support_platform_WxVideo">
        <div class="wu-f14 wu-c06">例：wxf414f0****1845</div>
        <div class="wu-inputWrap wu-form-mid" style="width: 100%;">
            <input class="wu-input" type="text" placeholder="请输入小程序ID" name="shopId" />
            <span class="iconfont icon-a-close-circle-filled1x" onclick="wuFormModule.clearInput(this)"></span>
        </div>
    </div>
</div>

<div class="support-platform-into" id="support_platform_yzinto">
    <div class="support-platform-title">
        <span id="support_platform_title" class="wu-f14 wu-c09">输入有赞kdt_id</span>
        <span class="wu-color-a wu-operate wu-f14 wu-mL24" onclick="partnerModule.platformHelp('yz')">使用教程</span>
        <i class="iconfont icon-jia-copy1" onclick="partnerModule.closePlatformInto()"></i>
    </div>
    <div class="support-platform-content" id="support_platform_YouZan">
        <div class="wu-f14 wu-c06">例：927**047</div>
        <div class="wu-inputWrap wu-form-mid" style="width: 100%;">
            <input class="wu-input" type="text" placeholder="请输入有赞kdt_id" name="shopId" />
            <span class="iconfont icon-a-close-circle-filled1x" onclick="wuFormModule.clearInput(this)"></span>
        </div>
    </div>
</div>

<div class="support-platform-into" id="support_otherplatforms">
    <div class="support-platform-title">
        <span id="support_platform_title" class="wu-f14 wu-c09">选择平台</span>
        <i class="iconfont icon-jia-copy1" onclick="partnerModule.closePlatformInto()"></i>
    </div>
    <div class="support-platform-content" id="support_OtherPlatforms">
        <ul>
            <li class="wu-flex wu-yCenter">
                <span>
                    <span>选择其他平台：</span>
                </span>
                <div class="wu-selectWrap wu-form-mid wu-flex-1">
                    <select class="wu-select" id="otherplatforms_type">
                        <option value="Other_Heliang">禾量平台</option>
                        <option value="Other_JuHaoMai">聚好麦平台</option>
                        <option value="Other_HaoYouDuo">好又多平台</option>
                    </select>
                    <i class="iconfont icon-a-chevron-down1x"></i>
                </div>
            </li>
        </ul>
    </div>
</div>

<div class="support-platform-into" id="support_other_heliang">
    <div class="support-platform-title">
        <span id="support_platform_title" class="wu-f14 wu-c09">禾量平台店铺授权</span>
        <span class="wu-color-a wu-operate wu-f14 wu-mL24" onclick="partnerModule.platformHelp('heliang')">使用教程</span>
        <i class="iconfont icon-jia-copy1" onclick="partnerModule.closeHeliangPlatformInto()"></i>
    </div>
    <div class="support-platform-content" id="support_Other_Heliang">
        <ul class="wu-f14 wu-c06">
            <li class="wu-flex wu-yCenter">
                <div style="width: 90px;text-align: right;"><i class="wu-color-b wu-mR2">*</i>店铺名称：</div>
                <div class="wu-inputWrap wu-form-mid wu-flex-1">
                    <input class="wu-input" type="text" name="shopName" placeholder="店铺>店铺信息>店铺名称" />
                    <span class="iconfont icon-a-close-circle-filled1x" onclick="wuFormModule.clearInput(this)"></span>
                </div>
            </li>
            <li class="wu-flex wu-yCenter wu-mT12">
                <div style="width: 90px;text-align: right;"><i class="wu-color-b wu-mR2">*</i>AppId：</div>
                <div class="wu-inputWrap wu-form-mid wu-flex-1">
                    <input class="wu-input" type="text" name="appId" placeholder="店铺>店铺信息>APPID" />
                    <span class="iconfont icon-a-close-circle-filled1x" onclick="wuFormModule.clearInput(this)"></span>
                </div>
            </li>
            <li class="wu-flex wu-yCenter wu-mT12">
                <div style="width: 90px;text-align: right;"><i class="wu-color-b wu-mR2">*</i>AppSecret：</div>
                <div class="wu-inputWrap wu-form-mid wu-flex-1">
                    <input class="wu-input" type="text" name="appSecret" placeholder="店铺>店铺信息>APPSECRET" />
                    <span class="iconfont icon-a-close-circle-filled1x" onclick="wuFormModule.clearInput(this)"></span>
                </div>
            </li>
        </ul>
    </div>
</div>

<div class="support-platform-into" id="support_other_juhaomai">
    <div class="support-platform-title">
        <span id="support_platform_title" class="wu-f14 wu-c09">聚好麦平台店铺授权</span>
        <span class="wu-color-a wu-operate wu-f14 wu-mL24" onclick="partnerModule.platformHelp('juhaomai')">使用教程</span>
        <i class="iconfont icon-jia-copy1" onclick="partnerModule.closeHeliangPlatformInto()"></i>
    </div>
    <div class="support-platform-content" id="support_Other_JuHaoMai">
        <ul class="wu-f14 wu-c06">
            <li class="wu-flex wu-yCenter">
                <div style="width: 90px;text-align: right;"><i class="wu-color-b wu-mR2">*</i>AppId：</div>
                <div class="wu-inputWrap wu-form-mid wu-flex-1">
                    <input class="wu-input" type="text" name="appId" placeholder="店铺>店铺信息>APPID" />
                    <span class="iconfont icon-a-close-circle-filled1x" onclick="wuFormModule.clearInput(this)"></span>
                </div>
            </li>
            <li class="wu-flex wu-yCenter wu-mT12">
                <div style="width: 90px;text-align: right;"><i class="wu-color-b wu-mR2">*</i>AppSecret：</div>
                <div class="wu-inputWrap wu-form-mid wu-flex-1">
                    <input class="wu-input" type="text" name="appSecret" placeholder="店铺>店铺信息>APPSECRET" />
                    <span class="iconfont icon-a-close-circle-filled1x" onclick="wuFormModule.clearInput(this)"></span>
                </div>
            </li>
        </ul>
    </div>
</div>

<div class="support-platform-into" id="support_other_haoyouduo">
    <div class="support-platform-title">
        <span id="support_platform_title" class="wu-f14 wu-c09">好又多平台店铺授权</span>
        <span class="wu-color-a wu-operate wu-f14 wu-mL24" onclick="partnerModule.platformHelp('haoyouduo')">使用教程</span>
        <i class="iconfont icon-jia-copy1" onclick="partnerModule.closeHeliangPlatformInto()"></i>
    </div>
    <div class="support-platform-content" id="support_Other_HaoYouDuo">
        <ul class="wu-f14 wu-c06">
            <li>
                <label class="wu-flex wu-yCenter" style="display:flex; alitem: center; width: 100%">
                    <div style="width: 90px;text-align: right;"><i class="wu-color-b wu-mR2">*</i>AppId：</div>
                    <div class="wu-inputWrap wu-form-mid" style="flex: 1;">
                        <input class="wu-input" type="text" name="appId" style="border-radius: 6px" placeholder="店铺>店铺信息>APPID" />
                        <span class="iconfont icon-a-close-circle-filled1x" onclick="wuFormModule.clearInput(this)"></span>
                    </div>
                </label>
            </li>
            <li class=" wu-mT12">
                <label class="wu-flex wu-yCenter" style="display:flex; alitem: center; width: 100%">
                    <div style="width: 90px;text-align: right;"><i class="wu-color-b wu-mR2">*</i>AppSecret：</div>
                    <div class="wu-inputWrap wu-form-mid" style="flex: 1;">
                        <input class="wu-input" type="text" name="appSecret" style="border-radius: 6px" placeholder="店铺>店铺信息>APPSECRET" />
                        <span class="iconfont icon-a-close-circle-filled1x" onclick="wuFormModule.clearInput(this)"></span>
                    </div>
                </label>
            </li>
        </ul>
    </div>
</div>

<div class="support-platform-into" id="support_platform_thhinto">
    <div class="support-platform-title">
        <span id="support_platform_title" class="wu-color-a wu-operate wu-f14" onclick="partnerModule.platformHelp('thh')">如何获取团好货的店铺ID和店铺名称？</span>
        <i class="iconfont icon-jia-copy1" onclick="partnerModule.closePlatformInto()"></i>
    </div>
    <div class="support-platform-content wu-flex wu-column" id="support_platform_TuanHaoHuo">
        <div class="wu-f14 wu-c09">输入店铺ID：</div>
        <div class="wu-inputWrap wu-form-mid wu-flex-1">
            <input class="wu-input" type="text" placeholder="请输入店铺ID" name="shopId" />
            <span class="iconfont icon-a-close-circle-filled1x" onclick="wuFormModule.clearInput(this)"></span>
        </div>
        <div class="wu-f14 wu-c09 wu-mT12">店铺名称：</div>
        <div class="wu-inputWrap wu-form-mid wu-flex-1">
            <input class="wu-input" type="text" placeholder="请输入店铺名称" name="shopName" />
            <span class="iconfont icon-a-close-circle-filled1x" onclick="wuFormModule.clearInput(this)"></span>
        </div>
    </div>
</div>

<div class="support-platform-into" id="support_platform_all" style="width:620px">

    <div class="togglePlatform">
        <div class="togglePlatform-title">请选择店铺已订购的应用类型</div>
        <ul class="togglePlatform-content" id="togglePlatform_content">
            @*<li class="togglePlatform-content-item"  data-status="old">
                    <span class="togglePlatform-content-item-img"></span>
                    <span class="togglePlatform-content-item-title">店管家铺货代发(旧)</span>
                </li>
                <li class="togglePlatform-content-item" data-status="new">
                    <span class="togglePlatform-content-item-img"></span>
                    <span class="togglePlatform-content-item-title">店管家分销货代发(新)</span>
                </li>*@
        </ul>
    </div>

</div>

<div class="support-platform-into" id="support_platform_alibaba" style="width:620px">
    <div class="togglePlatform">
        <div class="togglePlatform-title">请选择店铺已订购的应用类型</div>
        <ul class="togglePlatform-content" id="togglePlatform_content_alibaba">
            <li class="togglePlatform-content-item" data-status="old">
                <span class="togglePlatform-content-item-img"></span>
                <span class="togglePlatform-content-item-title">店管家打单</span>
            </li>
            <li class="togglePlatform-content-item" data-status="new">
                <span class="togglePlatform-content-item-img"></span>
                <span class="togglePlatform-content-item-title">店管家_分销代发</span>
            </li>
        </ul>
    </div>

</div>

<div class="support-platform-into" id="support_platform_kuaishou" style="width:620px">
    <div class="togglePlatform">
        <div class="togglePlatform-title">请选择店铺已订购的应用类型</div>
        <ul class="togglePlatform-content" id="togglePlatform_content_kuaishou">
            <li class="togglePlatform-content-item" data-status="old">
                <span class="togglePlatform-content-item-img"></span>
                <span class="togglePlatform-content-item-title">店管家打单</span>
            </li>
            <li class="togglePlatform-content-item" data-status="new">
                <span class="togglePlatform-content-item-img"></span>
                <span class="togglePlatform-content-item-title">店管家_分销代发</span>
            </li>
        </ul>
    </div>
</div>

<div class="support-platform-into" id="support_platform_jingdong" style="width:620px">
    <div class="togglePlatform">
        <div class="togglePlatform-title">请选择店铺已订购的应用类型</div>
        <ul class="togglePlatform-content" id="togglePlatform_content_jingdong">
            <li class="togglePlatform-content-item" data-status="old">
                <span class="togglePlatform-content-item-img"></span>
                <span class="togglePlatform-content-item-title">店管家打单</span>
            </li>
            <li class="togglePlatform-content-item" data-status="new">
                <span class="togglePlatform-content-item-img"></span>
                <span class="togglePlatform-content-item-title">店管家_分销代发</span>
            </li>
        </ul>
    </div>
</div>

<div class="support-platform-into" id="support_platform_WxVideoDailog" style="width:620px;">
    <div class="togglePlatform">
        <div class="togglePlatform-title">请选择店铺已订购的应用类型</div>
        <ul class="togglePlatform-content" id="togglePlatform_content_WxVideo">
            <li class="togglePlatform-content-item" data-status="old">
                <span class="togglePlatform-content-item-img" style="background-image: url(/Content/images/fxdf-platform.png);width:210px;height:140px"></span>
                <span class="togglePlatform-content-item-title">中恒分销代发_订单代发</span>
            </li>
            <li class="togglePlatform-content-item" data-status="new">
                <span class="togglePlatform-content-item-img"></span>
                <span class="togglePlatform-content-item-title">店管家_分销代发</span>
            </li>
        </ul>
    </div>
</div>

<div class="support-platform-wxhelpShow" id="support_platform_helpShow">
    <img id="platform_helpShow_img" src="~/Content/images/weixinhelpstep.png" />
    <span class="support-platform-more" id="support-platform-more" style="display:none" onclick="partnerModule.scrollHeight()">下一步</span>
</div>

@*紧急解绑*@
<div class="support-platform-into" id="emergency_unbinding" style="width:100%">
    <div class="layui-tab layui-tab-brief wu-layui-tab">
        <ul class="layui-tab-title">
            <li class="layui-this">账号密码验证</li>
            <li>验证码验证</li>
        </ul>
        <div class="layui-tab-content wu-pB24">
            <div class="layui-tab-item layui-show">
                <!-- 账号密码验证 -->
                <div class="registerInto-main" id="registerInto_main">
                    <label class="fristLabel">
                        <span class="iconfont icon-shouji1"></span>
                        <input type="text" id="loginTelePhone" maxlength="11" placeholder="输入手机号码">
                    </label>
                    <label class="labelPassword">
                        <span class="iconfont icon-mima3"></span>
                        <input type="password" id="loginPassword" placeholder="输入密码，由数字、字母或特殊符号组成">
                        <i class="setEye close" onclick="partnerModule.changeShowPassType('loginPassword',this)"></i>


                    </label>
                    <div class="Code">
                        <input type="text" id="loginValidCode" placeholder="请输入验证码">
                        <span class="loginCode" onclick="partnerModule.validCode()"> <img id="vCodeId" src="/FxAccount/ValidCode?t=1"></span>
                    </div>
                    @*<div class="registerInto-button" onclick="partnerModule.emergencyUnbinding(0)">
                            <span style="font-size:16px;">确认</span>
                        </div>*@
                    <div class="wu-flex wu-xCenter wu-mT20">
                        <div class="wu-btn wu-btn" style="width: 220px;" onclick="partnerModule.emergencyUnbinding(0)">确认</div>
                    </div>
                </div>
            </div>
            <div class="layui-tab-item">
                <!-- 验证码验证 -->
                <div class="registerInto-main commonregisterInto" id="intoLoginSelf">
                    <label id="fristLabel">
                        <span class="iconfont icon-shouji1"></span>
                        <input type="text" id="intoRegisterTelePhone" maxlength="11" placeholder="输入手机号码">
                    </label>
                    <label for="againRegisterValidCode">
                        <span class="iconfont icon-duanxin"></span>
                        <input placeholder="短信验证码" id="intoRegisterValidCode">
                        <span id="LoginCode1" style="margin-left:-40px;" class="wu-color-a wu-operate wu-f14" onclick="partnerModule.postMobileMessageCode('1')">发送验证码</span>
                    </label>
                    @*<div class="registerInto-button" onclick="partnerModule.emergencyUnbinding(1)">
                            <span style="font-size:16px;">确认</span>
                        </div>*@
                    <div class="wu-flex wu-xCenter wu-mT20">
                        <div class="wu-btn wu-btn" style="width: 220px;" onclick="partnerModule.emergencyUnbinding(1)">确认</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- 列表模样 -->
<script id="shareShopList_data_tr" type="text/x-jsrender">
    {{if shopData.length>0}}
    {{for shopData }}
    {{for children ~rowspan=rowSpan ~parentIndex=#getIndex() ~IsWhiteUser=IsWhiteUser}}
    <tr id="tr-fs-{{:Id}}" data-usershop-id="{{:Id}}">
        {{if #getIndex() == 0 }}
        <td rowspan="{{:~rowspan}}" class="NickName" shopid="{{:ShopId}}">{{:NickName}}</td>
        {{/if}}
        <td>{{:PlatformTypeName}}</td>
        {{if ~IsWhiteUser}}
        <td>
            {{if PlatformType === "TouTiao" && IsFxListingApp}}
            商品铺货权限
            {{else}}
            同步订单权限
            {{/if}}
        </td>
        {{/if}}
        <td>{{:AppName}}</td>
        <td>
            {{if IsHideOtherButton}}
            <span class="commonWenHao popoverCommon hover">?<span class="popoverCommon-warn" style="width:290px;">您的店铺订购了分销新&老双应用，为了保证授权正常已为您默认优先使用服务到期时间最长的应用</span></span>
            {{else}}
            {{if IsUnBind}}
            <span class="wu-badge wu-error">
                <i class="wu-badge-gan">！</i>
                <s class="wu-badge-title">申请解绑中</s>
            </span>
            {{else}}
            {{if IsExpire && PlatformPayUrl != ""}}
            <span class="wu-badge wu-error">
                <i class="wu-badge-gan">！</i>
                <s class="wu-badge-title">服务到期</s>
            </span>
            {{else}}
            {{if Status == 1}}
            <span class="wu-badge wu-success"><i class="wu-badge-dot"></i><s class="wu-badge-title">授权成功</s></span>
            @*{{else Status == 2}}
                <span class="status-flex"><i class="dot"></i>解除关联</span>*@
            {{else Status == 3}}
            <span class="wu-badge wu-error"><i class="wu-badge-dot"></i><s class="wu-badge-title">授权过期</s></span>
            {{else IsBlank && PlatformType === "TouTiao"}}
            <span class="status-flex hover n-dColor" onclick="partnerModule.OrderingTips.bind(this)({{:IsFxListingApp}}, '{{:PlatformType}}')">
                未授权
            </span>
            {{/if}}
            {{/if}}
            {{/if}}
            {{/if}}
        </td>
        <td>
            {{if !IsBlank}}
            <span>{{:CreateTime}}</span>
            {{/if}}
        </td>
        {{if PlatformPayUrl != null && PlatformPayUrl != ""}}
        <td>
            {{if !IsBlank}}
            <span class="td-fs-expireTime">{{:AuthTime}}</span> <span onclick="partnerModule.HandSyncExpireTime({{:Index}},this)" title="同步服务到期时间，如授权失效会从订购记录中更新过期时间"><i class="iconfont icon-tongbu dColor hover"></i></span>
            {{/if}}
            {{if IsRefundExpire}}
            <span class="tarTxt tarTxt03">手动退款</span>
            {{/if}}
        </td>
        {{else}}
        <td>-</td>
        {{/if}}
        {{if !IsBlank}}
        <td>
            <div class="wu-flex wu-yCenter">
                {{if IsHideOtherButton}}
                @*<span>-</span>*@
                {{else}}
                {{if PlatformType=="Virtual"}}
                <span class="EditShop hide wu-color-a wu-operate wu-f12 wu-mR12" onclick="partnerModule.edmitNickName({{:Id}},'{{:NickName}}')">编辑</span>
                {{else PlatformType =="Other_Heliang"}}
                <span class="wu-color-a wu-operate wu-f12 wu-mR12" onclick="partnerModule.edmitHeliang('{{:AppKey}}','{{:NickName}}')">编辑</span>
                {{else PlatformType =="Other_JuHaoMai"}}
                <span class="wu-color-a wu-operate wu-f12 wu-mR12" onclick="partnerModule.edmitJuHaoMai('{{:AppKey}}','{{:NickName}}')">编辑</span>
                {{else PlatformType =="Other_HaoYouDuo"}}
                <span class="wu-color-a wu-operate wu-f12 wu-mR12" onclick="partnerModule.edmitHaoYouDuo('{{:AppKey}}','{{:NickName}}')">编辑</span>
                {{else}}
                {{if (PlatformPayUrl != "" && PlatformType != "TouTiao") ||  (PlatformType=="TouTiao" && PlatformPayUrl2 != "") }}
                {{if PlatformType=="TouTiao"}}
                <span class="RenewalShop wu-color-a wu-operate wu-f12 wu-mR12" onclick="commonModule.latformRenewUrlTouTiao('{{:PlatformType}}','{{:NickName}}','{{:PlatformPayUrl2}}','{{:TouTiaoOldOrNew}}',{{:IsFxListingApp}})">续费</span>
                {{else PlatformType=="Alibaba" || PlatformType=="KuaiShou"}}
                <span class="RenewalShop wu-color-a wu-operate wu-f12 wu-mR12" onclick="commonModule.latformRenewUrl('{{:PlatformType}}','{{:NickName}}','{{:PlatformPayUrl}}','{{:TouTiaoOldOrNew}}')">续费</span>
                {{else PlatformType=="Pinduoduo" }}
                {{if AppKey == "b7e73126680b4a17892152ec0b277066" }}
                {{if PddIsUsePrintSystemApp && IsExpire == false}}
                {{else}}
                <span class="RenewalShop wu-color-a wu-operate wu-f12 wu-mR12" onclick="commonModule.latformRenewUrl('{{:PlatformType}}','{{:NickName}}','{{:PlatformPayUrl}}')">续费</span>
                {{/if}}
                {{else PddIsUsePrintSystemApp}}
                <span class="RenewalShop wu-color-a wu-operate wu-f12 wu-mR12" onclick="commonModule.latformRenewUrl('togglePinduoduo','{{:NickName}}','{{:PlatformPayUrl}}')">续费</span>
                {{/if}}
                {{else}}
                <span class="RenewalShop wu-color-a wu-operate wu-f12 wu-mR12" onclick="commonModule.latformRenewUrl('{{:PlatformType}}','{{:NickName}}','{{:PlatformPayUrl}}')">续费</span>
                {{/if}}
                {{/if}}
                {{if PlatformPayUrl == ""}}
                {{if Status == 3}}
                <span class="wu-color-a wu-operate wu-f12 wu-mR12" onclick="partnerModule.ReBinding({{:Index}},'{{:PlatformAuthUrl}}')">激活授权</span>
                {{else}}
                @*<span>-</span>*@
                {{/if}}
                {{else IsExpire == false}}
                {{if Status == 3}}
                {{if PlatformType=="TouTiao"}}
                <span class="wu-color-a wu-operate wu-f12 wu-mR12" onclick="partnerModule.ReBindingTouTiao('{{:AuthUrl}}')">激活授权</span>
                {{else}}
                <span class="wu-color-a wu-operate wu-f12 wu-mR12" onclick="partnerModule.ReBinding({{:Index}},'{{:PlatformAuthUrl}}')">激活授权</span>
                {{/if}}
                {{else}}
                <span> </span>
                {{/if}}
                {{/if}}
                {{/if}}

                {{if UnBindTaskState == -1 || UnBindTaskState == 2 ||  UnBindTaskState == 4}}
                <span class="UnbindShop hide wu-color-b wu-operate wu-f12 wu-mR12" onclick="partnerModule.SetUnBind({{:Id}},'{{:PlatformType}}', '{{:AppKey}}', '{{:PlatformTypeName}}')">申请解绑</span>
                {{else UnBindTaskState == 0}}
                {{if !IsHideUnBindButton}}
                <span class="wu-color-b wu-operate wu-f12 wu-mR12" onclick="partnerModule.CancelUnBind({{:Id}}, '{{:AppKey}}')">取消解绑</span>
                <span class="wu-color-b wu-operate wu-f12 wu-mR12" onclick="partnerModule.SetUrgentUnBind({{:Id }},'{{:PlatformTypeName}}', '{{:AppKey}}')">紧急解绑</span>
                {{else}}
                <span class="wu-color-a wu-operate wu-f12 wu-mR12" title="晚11点到早6点暂不支持取消解绑">取消解绑</span>
                {{/if}}
                {{else UnBindTaskState == 1}}
                <span class="wu-color-b wu-operate wu-f12" title="解绑中，无法操作">解绑中</span>
                {{/if}}
                {{/if}}
            </div>
        </td>
        {{else}}
        <td>-</td>
        {{/if}}
    </tr>
    {{/for}}
    {{/for}}
    {{else}}
    <tr>
        <td colspan="10" class="tdNodata">
            <div class="tableNoDataShow"><img src="/Content/images/noData-icon.png"><span class="tableNoDataShow-title">暂无数据</span></div>
        </td>
    </tr>
    {{/if}}

</script>

<div class="adialog-Shops-warn" id="adialog_Shops_warn">
    请勿绑定合作商的店铺！一个店铺只能绑定一个账号！一个账号支持绑定多个店铺！
</div>

<!-- 发货模式选择 -->
<div class="delivery-mode-select-container" style="display: none;">
    <div class="delivery-mode-select-wrapper">
        <div class="delivery-mode-select-card n-font6">
            <div class="c06">京东供销平台官方要求：</div>
            <div class="flex c06">
                ①每个订单只能上传一个快递单号到店铺后台；②不允许拆单发货，只能整单发货。
            </div>
        </div>
        <div class="n-font5 mT16" style="padding-left: 16px;">当您需要将订单中的商品推送给不同的厂家发货时，请选择</div>
        <div class="n-font5 mT16">
            <label class="delivery-mode-select-radio hover flex">
                <input type="radio" name="DeliveryMode" value="1" /><span class="mL8">快递单号不自动上传到店铺后台，需手动到后台发货</span>
            </label>
            <label class="delivery-mode-select-radio hover mT16" style="display: block;">
                <span class="flex"><input type="radio" name="DeliveryMode" value="2" /><span class="mL8">所有商品发货后，店铺后台订单才变为已发货，并上传任一商品快递单号</span></span>
                <span class="flex n-tColor mT4" style="padding-left: 24px;box-sizing:border-box;">可能有发货后仅退款的风险，避免经济损失，请慎重选择</span>
            </label>
        </div>
    </div>
    <div class="delivery-mode-select-footer flex" style="justify-content: space-between;">
        <span class="n-dColor hover" id="ShowDeliveryModeOperateLog">修改日志</span>
        <div class="flex">
            <span class="n-mButton n-sActive" id="CloseDeliveryModeSelect">取消</span>
            <span class="n-mButton" style="margin-left: 10px;" id="ConfirmDeliveryModeSelect">确认，立即授权</span>
        </div>
    </div>
</div>
<!-- 发货模式修改日志 -->
<script id="delivery_mode_operation_log" type="text/x-jsrender">
    <div style="max-height: 500px; overflow-y: auto;">
        <table class="n-table">
            <thead>
                <tr>
                    <th style="width: 140px;">操作时间</th>
                    <th style="min-width: 300px;">操作明细</th>
                </tr>
            </thead>
            <tbody>
                {{for data}}
                <tr>
                    <td>{{:CreateTime}}</td>
                    <td>
                        {{if Value === '1'}}
                        <span>快递单号不自动上传到店铺后台，需手动到后台发货</span>
                        {{else}}
                        <span>所有商品发货后，店铺后台订单才变为已发货，并上传任一商品快递单号</span>
                        {{/if}}
                    </td>
                </tr>
                {{/for}}
            </tbody>
        </table>
    </div>
</script>

@section OutBodyHtml{
        <div class="adialog-Shops new-support-platform" id="adialog_Shops_wrap">
        <div class="adialog-Shops-title">
            <span>选择平台</span>
                <a class="wu-f14 wu-color-a wu-operate wu-mL16" href="https://docs.qq.com/sheet/DQ29TVldGTHpXWGRk?tab=BB08J2" target="_blank">所有平台订购地址</a>
        </div>
            <table class="stockup_table_content wu-mL16 wu-mT16">
            <tbody id="tb_support_platform"></tbody>
        </table>
        @*<div class="adialog-Shops-warn">
                同一个店铺只能授权一个账号!!!<br />
                店铺一经授权，因订单数据维护，将无法进行解绑，请谨慎操作
            </div>*@
    </div>
}

<script>
    var defaultShopId = '@(Html.Raw(ViewBag.DefaultShopId ?? ""))';
    console.log("defaultShopId=", defaultShopId);
    var supportPlatforms = @(Html.Raw(ViewBag.SupportPlatform ?? "")) || [];
    console.log("supportPlatforms ==>", supportPlatforms);
    commonModule.AvailableCountInfo =  @(Html.Raw(ViewBag.AvailableCountInfo ?? "null")) || {}
    var IsTouTiaoOldUser = @(Html.Raw(ViewBag.IsTouTiaoOldUser ?? false)) || false;
    var HasTwoApp =  @(Html.Raw(ViewBag.HasTwoApp ?? false)) || false;
    console.log("HasTwoApp=>", HasTwoApp);
    commonModule.userFlag = "@ViewBag.UserFlag"; // 获取当前用户的角色标记
    if (IsTouTiaoOldUser) {
        $("#touTiaoOldUserAd02").css("display", "block");
    }
    var IsWhiteUserFlag = true;
    // 是否白名单用户
    function getIsWhiteUserData() {
        commonModule.ajax({
            type: 'GET',
            url: '/api/Common/GetIsWhiteUser',
            success: function (res) {
                if (res.Success) {
                    IsWhiteUserFlag = res.Data;
                    if (IsWhiteUserFlag) {
                        $("#show_white_user_element").show();
                    } else {
                        $("#show_white_user_element").hide();
                    }
                }
            }
        });
    }
    commonModule.OpenInfo1688 =  @(Html.Raw(ViewBag.OpenInfo1688 ?? "null")) || {};
    var IsWxVideoOldUser = "@ViewBag.IsWxVideoOldUser" == "true" ? true : false;
    IsWxVideoOldUser = true;
    // 部分按钮的展示权限
    var showPermDict = @(Html.Raw(ViewBag.ShowPermDict ?? "")) || {};
    commonModule.PartnerIndexShowPermDict = showPermDict;
    commonModule.HideNoPermDiv(showPermDict);

    $(function () {
        getIsWhiteUserData();
        if (IsTouTiaoOldUser) {
            $("#touTiaoOldUserAd01").show();
        }
        if (HasTwoApp) {
            $(".layui-mysearchcontent-a .layui-mysearchcontent-a-img02").show();
        }
        partnerModule.Init(true);
        wuFormModule.initMySelect("#domestic_shop_select");
        wuFormModule.initLayuiSelect('active-select-filter');

        var newAndOldVersionNavFlag = window.localStorage.getItem("isNewAndOldVersionNav");
        //if (newAndOldVersionNavFlag && newAndOldVersionNavFlag === 'true') {
        //    $("#new_and_old_version_navigation").css('top', '50px');
        //} else {
        //    $("#new_and_old_version_navigation").css('top', '60px');
        //}
    });
</script>

@Scripts.Render("~/bundles/partner")
