using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Entity.BaseProduct;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Model.BaseProduct;
using DianGuanJiaApp.Data.Model.SupplierProduct;
using DianGuanJiaApp.Models;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Services.BaseProduct;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Services.Services.SupplierProduct;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.Other;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using static NPOI.HSSF.Util.HSSFColor;

namespace DianGuanJiaApp.ErpWeb.ApiControllers.ListingProduct
{
    /// <summary>
    /// 多商品铺货草稿
    /// </summary>
    public class PtProductInfoDraftController : BaseApiController
    {
        /// <summary>
        ///  功能模块：多商品铺货、
        ///  功能点：多商品铺货草稿列表、
        ///  描述信息：多商品铺货信息获取、
        ///  接口开发人员：李云
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [LogForOperatorFilter("多商品铺货草稿列表")]
        public AjaxResult<PtInfoDraftResModel> GetPtInfoDrafts(PtInfoDraftReqModel model)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var ptInfoservice = new PtProductInfoService(fxUserId);
            var ptInfoDraftRes = new PtInfoDraftResModel();
            var ptInfoDraftItem = new List<PtInfoDraftItem>();
            ptInfoDraftRes.NeedUpdateCountY = 0;
            ptInfoDraftRes.NeedUpdateCountN = 0;
            ptInfoDraftRes.NeedUpdateY = new List<PtInfoDraftItem>();
            ptInfoDraftRes.NeedUpdateN = new List<PtInfoDraftItem>();

            var ptInfoDrafts = ptInfoservice.GetPtInfoDrafts(model.PtProductUniqueCode, fxUserId, model.PlatformType);

            if (ptInfoDrafts.Count > 0)
            {
                foreach (var info in ptInfoDrafts)
                {
                    var infoMode = ptInfoservice.TranPtModelFromPtEntity(info);
                    var draft = new PtInfoDraftItem();
                    var length = infoMode.Subject.IsNullOrEmpty() ? 0 : infoMode.Subject.Length;
                    var categorys = infoMode.CategoryInfoList.OrderBy(x => x.Level).ToList();
                    var categoryName = string.Join(",", categorys.Select(p => p.Name).ToList());
                    var categoryId = infoMode.CategoryId;

                    // a = infoMode.SubjectGetStringRealLen();

                    draft.UniqueCode = infoMode.UniqueCode;
                    draft.Subject = infoMode.Subject;
                    draft.SubjectLength = $"{length}/60";
                    draft.MainImageUrl = ImgHelper.ChangeImgUrl(infoMode?.ProductImages.FirstOrDefault()?.ImageUrl);
                    draft.CategoryName = categoryName;
                    draft.CategoryId = categoryId;
                    draft.NeedUpdate = infoMode.NeedUpdate;


                    // 库存 // 售价
                    var stockCount = infoMode.ProductInfoSkus.Where(p => !p.IsDefaultPadding).Sum(p => p.StockCount);
                    var salePriceMax = infoMode.ProductInfoSkus.Count == 0 ? 0 : infoMode.ProductInfoSkus.Where(p => !p.IsDefaultPadding).Max(p => p.SalePrice);
                    var salePriceMin = infoMode.ProductInfoSkus.Count == 0 ? 0 : infoMode.ProductInfoSkus.Where(p => !p.IsDefaultPadding).Min(p => p.SalePrice);
                    // 分销价格
                    var DistributePriceMax = infoMode.ProductInfoSkus.Count == 0 ? 0 : infoMode.ProductInfoSkus.Where(p => !p.IsDefaultPadding).Max(p => p.DistributePrice);
                    var DistributePriceMin = infoMode.ProductInfoSkus.Count == 0 ? 0 : infoMode.ProductInfoSkus.Where(p => !p.IsDefaultPadding).Min(p => p.DistributePrice);
                    // 采购价格
                    var SettlePriceMax = infoMode.ProductInfoSkus.Count == 0 ? 0 : infoMode.ProductInfoSkus.Where(p => !p.IsDefaultPadding).Max(p => p.SettlePrice);
                    var SettlePriceMin = infoMode.ProductInfoSkus.Count == 0 ? 0 : infoMode.ProductInfoSkus.Where(p => !p.IsDefaultPadding).Min(p => p.SettlePrice);


                    draft.StockCount = stockCount;
                    draft.SalePrice = salePriceMax == salePriceMin
                        ? $"￥{salePriceMax}" : $"￥{salePriceMin}-￥{salePriceMax}";
                    draft.DistributePrice = DistributePriceMax == DistributePriceMin
                        ? $"￥{DistributePriceMax}" : $"￥{DistributePriceMin}-￥{DistributePriceMax}";
                    draft.SettlePrice = SettlePriceMax == SettlePriceMin
                        ? $"￥{SettlePriceMax}" : $"￥{SettlePriceMin}-￥{SettlePriceMax}";
                    draft.ShopCount = 0;
                    draft.IsZeroForSalePrice = salePriceMax == 0 || salePriceMin == 0;
                    ptInfoDraftItem.Add(draft);
                }
                ptInfoDraftRes.NeedUpdateCountY = ptInfoDraftItem.Where(p => !p.NeedUpdate).ToList().Count();
                ptInfoDraftRes.NeedUpdateCountN = ptInfoDraftItem.Where(p => p.NeedUpdate).ToList().Count();
                ptInfoDraftRes.NeedUpdateY = ptInfoDraftItem.Where(p => !p.NeedUpdate).ToList();
                ptInfoDraftRes.NeedUpdateN = ptInfoDraftItem.Where(p => p.NeedUpdate).ToList();

                // 类目发布权限校验(已完善)
                if (model.ShopIds != null && model.ShopIds.Any() && !SiteContext.Current.AllShops.Any(a => model.ShopIds.Contains(a.Id)))
                    throw new LogicException("没有权限");

                var authReqModels = new List<CategoryCheckAuth>();
                var shops = new ShopService(PlatformAppScene.listing).GetShopByIds(model.ShopIds);
                foreach (var shop in shops)
                {
                    foreach (var info in ptInfoDraftRes.NeedUpdateY)
                    {
                        PlatformFactory.GetPlatformService(shop, scene: PlatformAppScene.listing);
                        var data = new CategoryCheckAuth();
                        data.ShopId = shop.Id;
                        data.Shop = shop;
                        data.CateId = info.CategoryId;
                        data.IsAuth = false;
                        data.UniqueCode = info.UniqueCode;
                        authReqModels.Add(data);
                    }
                }

                var authResModels = ptInfoservice.CheckShopCateListAuth(authReqModels);
                foreach (var item in ptInfoDraftRes.NeedUpdateY)
                {
                    var models = authResModels.Where(p => !p.IsAuth && p.UniqueCode == item.UniqueCode).ToList();
                    var ShopNames = models.Select(p => p.Shop.ShopName).ToList();
                    item.ShopCount = models.Count();
                    item.ShopNames = string.Join(",", ShopNames);
                }
            }
            return new AjaxResult<PtInfoDraftResModel>()
            {
                Success = true,
                Message = "成功！",
                Data = ptInfoDraftRes
            };
        }


        /// <summary>
        ///  功能模块：多商品铺货、
        ///  功能点：多商品铺货草稿列表、
        ///  描述信息：多商品铺货列表价格编辑、
        ///  接口开发人员：李云
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [LogForOperatorFilter("多商品铺货草稿价格编辑")]
        public AjaxResult<bool> UpdatePtInfoDraftPrice(UpdatePtProductInfoDraftModel model)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;

            if (model.UniqueCode.IsNullOrEmpty())
            {
                return new AjaxResult<bool>()
                {
                    Success = false,
                    Message = "草稿资料编码不能为空！",
                    Data = false
                };
            }

            if (model.PriceUnitType == 1)
            {
                if (model.PriceCornerFen.IsNullOrEmpty())
                {
                    return new AjaxResult<bool>()
                    {
                        Success = false,
                        Message = "自定义角分不能为空！",
                        Data = false
                    };
                }

                if (model.PriceCornerFen < 0)
                {
                    return new AjaxResult<bool>()
                    {
                        Success = false,
                        Message = "自定义角分不能未负数！",
                        Data = false
                    };
                }
            }

            if (model.PriceUpdateType == 0)
            {
                if (model.PriceCommon.IsNullOrEmpty())
                {
                    return new AjaxResult<bool>()
                    {
                        Success = false,
                        Message = "草稿资料价格不能为空！",
                        Data = false
                    };
                }

                if (model.PriceCommon < 0)
                {
                    return new AjaxResult<bool>()
                    {
                        Success = false,
                        Message = "草稿资料价格不能为负数！",
                        Data = false
                    };
                }
            }

            if (model.PriceUpdateType == 1)
            {
                if (model.PricePercentage.IsNullOrEmpty())
                {
                    return new AjaxResult<bool>()
                    {
                        Success = false,
                        Message = "草稿资料价格百分比不能为空！",
                        Data = false
                    };
                }

                if (model.PricePercentage <= 0)
                {
                    return new AjaxResult<bool>()
                    {
                        Success = false,
                        Message = "草稿资料价格百分比不能为负数或者0！",
                        Data = false
                    };
                }

                if (model.PriceIncrement.IsNullOrEmpty())
                {
                    return new AjaxResult<bool>()
                    {
                        Success = false,
                        Message = "草稿资料价格增量不能为空！",
                        Data = false
                    };
                }
            }
            var curPtProductInfoService = new PtProductInfoService(string.Empty, fxUserId);
            var curPtProductInfo =
                curPtProductInfoService.GetByCode(model.UniqueCode,fxUserId);
            if (curPtProductInfo.IsNullOrEmpty())
            {
                return new AjaxResult<bool>()
                {
                    Success = false,
                    Message = "草稿资料不存在！",
                    Data = false
                };
            }

            if (curPtProductInfo.Ext.IsNullOrEmpty())
            {
                return new AjaxResult<bool>()
                {
                    Success = false,
                    Message = "草稿资料不存在！",
                    Data = false
                };
            }

            if (curPtProductInfo.Ext.SkuJson.IsNullOrEmpty())
            {
                return new AjaxResult<bool>()
                {
                    Success = false,
                    Message = "草稿资料规格信息不存在！",
                    Data = false
                };
            }

            var res = curPtProductInfoService.UpdatePtInfoDraftPrice(curPtProductInfo, model);
            return new AjaxResult<bool>()
            {
                Success = res,
                Message = res ? "编辑成功！" : "编辑失败！",
                Data = res
            };
        }


        /// <summary>
        ///  功能模块：多商品铺货、
        ///  功能点：多商品铺货草稿列表、
        ///  描述信息：多商品铺货库存编辑、
        ///  接口开发人员：李云
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [LogForOperatorFilter("多商品铺货草稿库存编辑")]
        public AjaxResult<bool> UpdatePtInfoDraftStock(UpdatePtProductInfoDraftStockCountModel model)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;

            if (model.UniqueCode.IsNullOrEmpty())
            {
                return new AjaxResult<bool>()
                {
                    Success = false,
                    Message = "草稿资料编码不能为空！",
                    Data = false
                };
            }

            if (model.StockType == StockTypeEnum.SameChange)
            {
                return new AjaxResult<bool>()
                {
                    Success = true,
                    Message = "",
                    Data = true
                };
            }

            if (model.StockType == StockTypeEnum.CommonChange)
            {
                if (model.StockCommonCount.IsNullOrEmpty())
                {
                    return new AjaxResult<bool>()
                    {
                        Success = false,
                        Message = "草稿资料库存不能为空！",
                        Data = false
                    };
                }
                if (model.StockCommonCount < 0)
                {
                    return new AjaxResult<bool>()
                    {
                        Success = false,
                        Message = "草稿资料库存不能为负数！",
                        Data = false
                    };
                }
            }

            if (model.StockType == StockTypeEnum.OtherChange)
            {
                if (model.StockChangeCount.IsNullOrEmpty())
                {
                    return new AjaxResult<bool>()
                    {
                        Success = false,
                        Message = "草稿资料库存不能为空！",
                        Data = false
                    };
                }
                if (model.StockChangeCount < 0)
                {
                    return new AjaxResult<bool>()
                    {
                        Success = false,
                        Message = "草稿资料库存不能为负数！",
                        Data = false
                    };
                }
            }

            var curPtProductInfoService = new PtProductInfoService(string.Empty, fxUserId);
            var curPtProductInfo =
                curPtProductInfoService.GetByCode(model.UniqueCode, fxUserId);
            if (curPtProductInfo.IsNullOrEmpty())
            {
                return new AjaxResult<bool>()
                {
                    Success = false,
                    Message = "草稿资料不存在！",
                    Data = false
                };
            }
            var res = curPtProductInfoService.UpdatePtInfoDraftStock(curPtProductInfo, model);
            return new AjaxResult<bool>()
            {
                Success = res,
                Message = res ? "编辑成功！" : "编辑失败！",
                Data = res
            };
        }

        /// <summary>
        ///  功能模块：多商品铺货、
        ///  功能点：多商品铺货草稿列表、
        ///  描述信息：多商品铺货信息编辑、
        ///  接口开发人员：李云
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [LogForOperatorFilter("多商品铺货草稿信息编辑")]
        public AjaxResult<bool> UpdatePtInfoDraftSubject(UpdatePtProductInfoDraftTitleModel model)
        {
            if (model.UniqueCode.IsNullOrEmpty())
            {
                return new AjaxResult<bool>()
                {
                    Success = false,
                    Message = "草稿资料编码不能为空！",
                    Data = false
                };
            }
            if (model.Subject.IsNullOrEmpty())
            {
                return new AjaxResult<bool>()
                {
                    Success = false,
                    Message = "草稿资料标题不能为空！",
                    Data = false
                };
            }

            if (model.Subject.GetStringLen() < 16 || model.Subject.GetStringLen() >120)
            {
                return new AjaxResult<bool>()
                {
                    Success = false,
                    Message = "草稿资料标题长度在8-60个字！",
                    Data = false
                };
            }
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var service = new PtProductInfoService(string.Empty, fxUserId);
            var curPtProductInfo = service.GetByCode(model.UniqueCode, fxUserId);
            if (curPtProductInfo.IsNullOrEmpty())
            {
                return new AjaxResult<bool>()
                {
                    Success = false,
                    Message = "草稿资料不存在！",
                    Data = false
                };
            }
            curPtProductInfo.Subject = model.Subject;
            service.BatchUpdate(new List<PtProductInfo> { curPtProductInfo });
            return new AjaxResult<bool>()
            {
                Success = true,
                Message = "成功！",
                Data = true
            };
        }

        /// <summary>
        ///  功能模块：多商品铺货、
        ///  功能点：多商品铺货草稿、
        ///  描述信息：多商品铺货信息编辑、
        ///  接口开发人员：李云
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [LogForOperatorFilter("多商品铺货草稿信息详情")]
        public AjaxResult<PtProductInfoModel> GetPtInfoDraft(UpdatePtProductInfoDraftTitleModel model)
        {
            var res = new AjaxResult<PtProductInfoModel>();
            res.Success = true;
            res.Data = null;

            var uniqueCode = model.UniqueCode;
            if (string.IsNullOrEmpty(uniqueCode))
            {
                res.Success = false;
                res.Message = "铺货草稿编码不能为空！";
                res.Data = null;
                return res;
            }
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var service = new PtProductInfoService(string.Empty, fxUserId);
            var curPtProductInfo = service.GetByCode(uniqueCode);
            var curPtProducModel = service.TranPtModelFromPtEntity(curPtProductInfo);

            // 输出给前端时，按照笛卡尔积，系统会强制填充sku
            curPtProducModel.ProductInfoSkus = service.TranSkuFission(curPtProducModel.ProductInfoSkus);

            var content = new PlatformCategoryService().GetPlatformCategoryPublishRule(curPtProducModel.PlatfromType, curPtProducModel.CategoryId) ?? string.Empty;
            if (!string.IsNullOrWhiteSpace(content))
            {
                var rules = content.ToObject<ProductUpdateRuleInfoModel>();
                var specs = rules?.product_spec_rule?.required_spec_details;
                curPtProducModel.SellPropertyJson = specs.ToJson();
            }

            #region 图片显示转换
            List<string> descriptionList = new List<string>();
            curPtProducModel.DescriptionStr?.ForEach(x => { descriptionList.Add(ImgHelper.ChangeImgUrl(x)); });
            curPtProducModel.ProductImages?.ForEach(x => { x.ImageUrl = ImgHelper.ChangeImgUrl(x.ImageUrl); });
            curPtProducModel.ProductInfoSkus?.ForEach(x =>
            {
                var tranUrl = ImgHelper.ChangeImgUrl(x.ImageUrl);
                x.ImageUrl = tranUrl;
                if (x.Attribute != null)
                {
                    x.Attribute.ValueUrl = tranUrl;
                    x.Attribute.ValueUrlKey = tranUrl;
                }
            });
            curPtProducModel.DescriptionStr = descriptionList;
            curPtProducModel.AttributeTypes?.ForEach(x =>
            {
                x?.AttributeValues.ForEach(v =>
                {
                    v.ValueUrl = ImgHelper.ChangeImgUrl(v.ValueUrl);
                });
            });
            #endregion

            // 智能类目信息补充
            if (string.IsNullOrWhiteSpace(curPtProducModel.CategoryId))
            {
                service.GetAutoCategorysV2(curPtProducModel,2 ); // 多商品铺货-编辑回显
            }

            var userShopIds = SiteContext.Current.UserShops.Select(sh => sh.Id).ToList();
            if (!userShopIds.Any() || !model.ShopIds.Any(t => userShopIds.Contains(t)))
                throw new LogicException("铺货的店铺不属于当前的账号！");

            service.CateForecastRecord(curPtProducModel.Subject, curPtProducModel.PlatfromType, model.ShopIds); // 多商品铺货-编辑,只请求接口做记录

            if (curPtProducModel.CreateFrom == "SelfBaseProduct" || curPtProducModel.CreateFrom == "SelfSupplierProduct")
                curPtProducModel.IsSyncPtProductInfoButton = true;
            else
                curPtProducModel.IsSyncPtProductInfoButton = false;
            res.Data = curPtProducModel;
            res.Message = "成功！";
            return res;
        }

        /// <summary>
        ///  功能模块：多商品铺货、
        ///  功能点：多商品铺货草稿、
        ///  描述信息：多商品铺货信息编辑、
        ///  接口开发人员：李云
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [LogForOperatorFilter("多商品铺货草稿信息编辑")]
        public AjaxResult<bool> UpdatePtInfoDraft(PtProductInfoModel model)
        {
            if (model.UniqueCode.IsNullOrEmpty())
            {
                return new AjaxResult<bool>
                {
                    Success = false,
                    Message = "资料编码不能为空！",
                    Data = false
                };
            }
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var service = new PtProductInfoService(string.Empty, fxUserId);
            var res = service.UpdatePtInfoDraft(model, fxUserId);
            var isSync = model.IsSyncPtProductInfo.HasValue && model.IsSyncPtProductInfo.Value && res;
            if (isSync)
            {
                try
                {
                    // 同步到覆盖抖音资料
                    service.SyncPtProductInfo(model, fxUserId);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"铺货草稿同步保存平台资料失败：资料：{model.UniqueCode},异常：{ex.ToJsonExt()}");
                }

            }
            return new AjaxResult<bool>
            {
                Success = true,
                Message = res ? "编辑成功" : "编辑失败",
                Data = res
            };
        }

        /// <summary>
        ///  功能模块：多商品铺货、
        ///  功能点：铺货、
        ///  描述信息：多商品已完善资料铺货、
        ///  接口开发人员：李云
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult<BatchListingTaskRes> BatchSaveListings(BatchListingTaskModel model)
        {
            if (model.ListingShopIds.Count <= 0)
            {
                return new AjaxResult<BatchListingTaskRes>
                {
                    Success = false,
                    Message = "铺货店铺不能为空！",
                    Data = new BatchListingTaskRes()
                };
            }

            if (model.ListingPtProducts.Count <= 0)
            {
                return new AjaxResult<BatchListingTaskRes>
                {
                    Success = false,
                    Message = "铺货资料编码不能为空！",
                    Data = new BatchListingTaskRes()
                };
            }

            if (model.PlatformType.IsNullOrEmpty())
            {
                return new AjaxResult<BatchListingTaskRes>
                {
                    Success = false,
                    Message = "铺货平台不能为空！",
                    Data = new BatchListingTaskRes()
                };
            }

            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var ptProductInfoService = new PtProductInfoService(string.Empty, fxUserId);
            var listingPtProductUniqueCodes = model.ListingPtProducts.Select(p=>p.ListingPtProductUniqueCode).Distinct().ToList();
            var ptProductInfos = ptProductInfoService.GetPtInfoDrafts(listingPtProductUniqueCodes, fxUserId, model.PlatformType);
            if (ptProductInfos == null || ptProductInfos.Count <= 0)
            {
                return new AjaxResult<BatchListingTaskRes>
                {
                    Success = false,
                    Message = "未找到对应的铺货草稿！",
                    Data = new BatchListingTaskRes()
                };
            }

            var recommendIds = ptProductInfoService.CateForecastRecord(ptProductInfos[0].Subject, model.PlatformType, model.ListingShopIds); //多商品铺货-铺货,只请求接口做记录
            ptProductInfos.ForEach(ptinfo => ptinfo.Ext.RecommendIds = recommendIds?.recommend_id ?? "");

            var service = new ListingTaskRecordsService();
            var result = service.SaveFromPtProductInfos(ptProductInfos, model, fxUserId);
            return new AjaxResult<BatchListingTaskRes>
            {
                Success = result.Success,
                Message = result.Message,
                Data = result.Data,
            };
        }

        /// <summary>
        ///  功能模块：多商品铺货、
        ///  功能点：多商品铺货草稿价格转换、
        ///  描述信息：多商品铺货列表价格编辑、
        ///  接口开发人员：李云
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [LogForOperatorFilter("多商品铺货草稿价格编辑")]
        public AjaxResult<List<CalculatePrice>> CalculateDraftPrice(CalculatePtProductInfoDraftModel model)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;

            if (model.CalculatePrices.Count<=0)
            {
                return new AjaxResult<List<CalculatePrice>>()
                {
                    Success = false,
                    Message = "编辑数据SkuUid不能为空！",
                    Data = null
                };
            }

            if (model.PriceUnitType == 1)
            {
                if (model.PriceCornerFen.IsNullOrEmpty())
                {
                    return new AjaxResult<List<CalculatePrice>>()
                    {
                        Success = false,
                        Message = "自定义角分不能为空！",
                        Data = null
                    };
                }

                if (model.PriceCornerFen < 0)
                {
                    return new AjaxResult<List<CalculatePrice>>()
                    {
                        Success = false,
                        Message = "自定义角分不能未负数！",
                        Data = null
                    };
                }
            }

            if (model.PriceUpdateType == 0)
            {
                if (model.PriceCommon.IsNullOrEmpty())
                {
                    return new AjaxResult<List<CalculatePrice>>()
                    {
                        Success = false,
                        Message = "草稿资料价格不能为空！",
                        Data = null
                    };
                }

                if (model.PriceCommon < 0)
                {
                    return new AjaxResult<List<CalculatePrice>>()
                    {
                        Success = false,
                        Message = "草稿资料价格不能为负数！",
                        Data = null
                    };
                }
            }

            if (model.PriceUpdateType == 1)
            {
                if (model.PricePercentage.IsNullOrEmpty())
                {
                    return new AjaxResult<List<CalculatePrice>>()
                    {
                        Success = false,
                        Message = "草稿资料价格百分比不能为空！",
                        Data = null
                    };
                }

                if (model.PricePercentage <= 0)
                {
                    return new AjaxResult<List<CalculatePrice>>()
                    {
                        Success = false,
                        Message = "草稿资料价格百分比不能为负数或者0！",
                        Data = null
                    };
                }

                if (model.PriceIncrement.IsNullOrEmpty())
                {
                    return new AjaxResult<List<CalculatePrice>>()
                    {
                        Success = false,
                        Message = "草稿资料价格增量不能为空！",
                        Data = null
                    };
                }
            }
            
            // 价格计算
            var service = new PtProductInfoService(string.Empty, fxUserId);
            int? priceCornerFen = null;
            if (model.PriceUnitType == 1)
            {
                // 单位保留角分
                priceCornerFen = model.PriceCornerFen;
            }
            else
            {
                // 单位统一修改
                priceCornerFen = null;
            }
            if (model.CalculatePrices.Count > 0)
            {
                // 公式修改
                if (model.PriceUpdateType == 1)
                {
                    var pricePercentage = (decimal)(model.PricePercentage.Value);
                    var priceIncrement = model.PriceIncrement.Value;
                    foreach (var priceModel in model.CalculatePrices)
                    {
                        priceModel.PriceType = model.PriceType;
                        priceModel.Price = priceModel.Price * pricePercentage / 100 + priceIncrement;
                        priceModel.Price = service.PriceTranslate(priceModel.Price, priceCornerFen);
                    }
                }
                // 统一修改
                else
                {
                    var priceCommon = model.PriceCommon.Value;
                    foreach (var priceModel in model.CalculatePrices)
                    {
                        priceModel.PriceType = model.PriceType;
                        priceModel.Price = priceCommon;
                        priceModel.Price = service.PriceTranslate(priceModel.Price, priceCornerFen);
                    }
                }
            }
            var res = new CalculatePtProductInfoDraftRes();
            res.CalculatePrices = model.CalculatePrices;
            return new AjaxResult<List<CalculatePrice>>()
            {
                Success = true,
                Message = "计算成功！",
                Data = model.CalculatePrices
            };
        }

        /// <summary>
        /// 店铺类目检测接口
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [LogForOperatorFilter("店铺类目检测接口")]
        public AjaxResult<List<CategoryCheckAuth>> CheckShopCateAuth(PtInfoDraftReqModel model)
        {
            var shopService = new ShopService(PlatformAppScene.listing);
            var shops = shopService.GetShopByIds(model.ShopIds);
            var authReqModels = new List<CategoryCheckAuth>();
            var authResModels = new List<CategoryCheckAuth>();
            foreach (var shop in shops)
            {
                foreach (var id in model.CatIds)
                {
                    PlatformFactory.GetPlatformService(shop, scene: PlatformAppScene.listing);
                    var data = new CategoryCheckAuth();
                    data.ShopId = shop.Id;
                    data.Shop = shop;
                    data.CateId = id;
                    data.IsAuth = false;
                    data.UniqueCode = "";
                    authReqModels.Add(data);
                }
            }
            var platformCategorySupplierService = new PlatformCategorySupplierService();
            Parallel.ForEach(authReqModels, new ParallelOptions { MaxDegreeOfParallelism = 5 }, authReq =>
            {
                try
                {
                    var data = platformCategorySupplierService.CheckShopCateAuth(authReq);
                    authResModels.Add(data);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"检测发布规则异常：{ex.Message}，参数：{authReq.ToJson()}");
                }
            });
            return new AjaxResult<List<CategoryCheckAuth>>()
            {
                Success = false,
                Message = "成功！",
                Data = authResModels
            };
        }

        /// <summary>
        /// 清除类目检测缓存
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [LogForOperatorFilter("清除类目检测缓存")]
        public AjaxResult<bool> ClearShopCateAuth(PtInfoDraftReqModel model)
        {
            var shopService = new ShopService(PlatformAppScene.listing);
            var shops = shopService.GetShopByIds(model.ShopIds);
            var authReqModels = new List<CategoryCheckAuth>();
            foreach (var shop in shops)
            {
                foreach (var id in model.CatIds)
                {
                    PlatformFactory.GetPlatformService(shop, scene: PlatformAppScene.listing);
                    var data = new CategoryCheckAuth();
                    data.ShopId = shop.Id;
                    data.Shop = shop;
                    data.CateId = id;
                    data.IsAuth = false;
                    data.UniqueCode = "";
                    authReqModels.Add(data);
                }
            }
            var platformCategorySupplierService = new PlatformCategorySupplierService();
            foreach (var item in authReqModels)
            {
                try
                {
                    var data = platformCategorySupplierService.ClearShopCateAuth(item);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"清除缓存失败店铺：{item.ShopId}/类目：{item.CateId}：{ex.ToJson()}");
                }
            }
            
            return new AjaxResult<bool>()
            {
                Success = false,
                Message = "清除成功！",
                Data = true
            };
        }
    }
}
