using DianGuanJiaApp.Data.FxModel.CategoryProduct;
using DianGuanJiaApp.Data.FxModel.Listing;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Model.BaseProduct;
using DianGuanJiaApp.Models;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Services.BaseProduct;
using DianGuanJiaApp.Services.Model;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Services.Services.SupplierProduct;
using DianGuanJiaApp.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.MessageModel;
using DianGuanJiaApp.RabbitMQ;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Services.Services.SupplierProduct.ListingProduct;
using DianGuanJiaApp.Data.Entity.BaseProduct;
using DianGuanJiaApp.Data.FxModel;
using DianGuanJiaApp.Data.Model.CrossBorder;
using System.Diagnostics;
using static NPOI.HSSF.Util.HSSFColor;

namespace DianGuanJiaApp.ErpWeb.ApiControllers
{
    /// <summary>
    /// 铺货相关的控制器
    /// </summary>
    [FxWhiteUserFilterApi]
    public class ListingController : BaseApiController
    {
        private readonly PlatformCategoryService _platformCategorySvc = new PlatformCategoryService();

        /// <summary>
        ///  功能模块：货盘库、
        ///  功能点：下拉框查询、
        ///  描述信息：系统/平台类目查询接口
        ///  接口开发人员：张立聪
        /// </summary>
        /// <param name="req">入参</param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult<List<ListingCategory>> GetCategoryList(GetCategoryListReqModel req)
        {
            var errorMessage = EntryVerification();
            if (errorMessage != null) return FailedResult<List<ListingCategory>>(null, errorMessage);

            if (req.ShopId > 0)
            {
                int fxUserId = SiteContext.Current.CurrentFxUserId;//登录用户ID

                if (CustomerConfig.IsCrossBorderSite)
                {
                    ValidateCrossBorderShop(fxUserId, req.ShopId);

                }
                else
                {
                    // 越权校验
                    if (!SiteContext.Current.AllShops.Any(a => a.Id == req.ShopId))
                        throw new LogicException("违法操作");
                }
            }
            var list = _platformCategorySvc.GetCategoryList(req);
            return new AjaxResult<List<ListingCategory>>
            {
                Success = true,
                Data = list,
                Message = "获取成功"
            };
        }

        /// <summary>
        /// 跨境验权
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="shopId"></param>
        /// <returns></returns>
        private void ValidateCrossBorderShop(int fxUserId, int shopId)
        {
            FxUserShopQueryModel _reqModel = new FxUserShopQueryModel()
            {
                FxUserId = fxUserId,
                QueryPageType = 2,
                IsShowAccount = true,
            };
            var tuple = new FxUserForeignShopService().GetList(_reqModel);
            var crossBorderShops = tuple.Item2;
            if (crossBorderShops != null && !crossBorderShops.Any(x => x.ShopId == shopId))
                throw new LogicException("违法操作");

        }

        /// <summary>
        ///  功能模块：货盘库、
        ///  功能点：下拉框文本查询、
        ///  描述信息：系统/平台类目查询 根据Key 搜索 类目名称
        ///  接口开发人员：张立聪
        /// </summary>
        /// <param name="req">请求参数</param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult<List<ListingCategory>> GetCategoryByName(GetCategoryByNameResModel req)
        {
            var errorMessage = EntryVerification();
            if (errorMessage != null) return FailedResult<List<ListingCategory>>(null, errorMessage);

            if (req.ShopId > 0)
            {
                int fxUserId = SiteContext.Current.CurrentFxUserId;//登录用户ID

                if (CustomerConfig.IsCrossBorderSite)
                {
                    ValidateCrossBorderShop(fxUserId, req.ShopId);

                }
                else
                {
                    // 越权校验
                    if (!SiteContext.Current.AllShops.Any(a => a.Id == req.ShopId))
                        throw new LogicException("违法操作");
                }
            }

            // https://zk.dgjapp.com/ListingConfig/SearchCategories?pctoken=9A7F5992168E90A6470DD05586FFEA1B
            List<ListingCategory> list = _platformCategorySvc.GetCategoryByName(req);
            return new AjaxResult<List<ListingCategory>>
            {
                Success = true,
                Data = list,
                Message = "获取成功"
            };
        }

        /// <summary>
        ///  功能模块：货盘库、
        ///  功能点：获取抖店平牌
        ///  接口开发人员：张立聪
        /// </summary>
        /// <param name="req">请求参数</param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult<List<BrandModel>> GetBrand(GetBrandReqModel req)
        {
            var errorMessage = EntryVerification();
            if (errorMessage != null) return FailedResult<List<BrandModel>>(null, errorMessage);
            CheckDataPermission(req.ShopId);

            try
            {
                List<BrandModel> resultList = _platformCategorySvc.GetBrand2(req);
                return new AjaxResult<List<BrandModel>>
                {
                    Success = true,
                    Data = resultList,
                    Message = "获取成功"
                };
            }
            catch (Exception e)
            {
                Log.WriteError($"获取品牌失败: {e.Message} 堆栈：{e.StackTrace}");
                return FailedResult<List<BrandModel>>(null, "获取品牌失败，请稍会儿再试");
            }
        }

        /// <summary>
        /// 功能模块：货盘库、
        /// 功能点：类目预测</summary>
        /// 接口开发人员：张立聪<param name="req"></param>
        /// <returns></returns>
        /// <exception cref="LogicException"></exception>
        [HttpPost]
        public AjaxResult<ProductRecommendCategory> ProductCateForecast(ProductCateForecastReq req)
        {
            var errorMessage = EntryVerification();
            if (errorMessage != null) return FailedResult<ProductRecommendCategory>(null, errorMessage);
            CheckDataPermission(req.ShopId); //越权检查
            PtProductInfoModel ptProductInfoModel = new PtProductInfoModel { Subject = req.Subject, PlatfromType = req.PlatformType, ShopId = req.ShopId };
            new PtProductInfoService().GetAutoCategorysV2(ptProductInfoModel,3); // 接口调用
            return new AjaxResult<ProductRecommendCategory>
            {
                Success = true,
                Data = ptProductInfoModel.RecommendCategory,
                Message = "获取成功"
            };
        }

        /// <summary>
        ///  功能模块：货盘库、
        ///  功能点：查询类目属性、抖店的类目属性后面改成调 PtProductInfo/TranAttributeAndSku 这个接口去了
        ///  描述信息：根据类目Id查询类目属性
        ///  接口开发人员：张立聪、钟柱
        /// </summary>
        /// <param name="reqModel">入参</param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult<List<ListingProductEditResultModelByCateProps>> GetCategoryProp(GetCategoryPropReqModel reqModel)
        {
            var errorMessage = EntryVerification();
            if (errorMessage != null) return FailedResult<List<ListingProductEditResultModelByCateProps>>(null, errorMessage);

            var resultList = _platformCategorySvc.GetCategoryProp(reqModel);
            return new AjaxResult<List<ListingProductEditResultModelByCateProps>>
            {
                Success = true,
                Data = resultList,
                Message = "获取成功"
            };
        }

        [HttpGet]
        public AjaxResult<object> GetCategoryRule(string id)
        {
            var resultList = _platformCategorySvc.GetPlatformCategoryPublishRule("TouTiao", id);
            return new AjaxResult<object>
            {
                Success = true,
                Data = resultList,
                Message = "获取成功"
            };
        }

        [HttpGet]
        public AjaxResult<object> GetCategoryProp(string id)
        {
            var resultList = _platformCategorySvc.GetCategoryProp(id);
            return new AjaxResult<object>
            {
                Success = true,
                Data = resultList.ToJson(),
                Message = "获取成功"
            };
        }

        [HttpGet]
        public AjaxResult<object> GetCategoryDataComparison()
        {
            if (!CustomerConfig.IsDebug)
            {
                return FailedResult<object>(null, "非法请求");
            }

            var result = _platformCategorySvc.GetCategoryDataComparison();
            return new AjaxResult<object>
            {
                Success = true,
                Data = result.ToString(),
                Message = "获取成功"
            };
        }

        /// <summary>
        /// 铺货模块-铺货日志-获取铺货日志记录
        /// [肖茂翔]
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult<object> GetListingTaskRecord(ListingTaskRecordQuery query)
        {
            query.FxUserId = SiteContext.GetCurrentFxUserId();
            var records = new ListingTaskRecordsService().GetRecordPageList(query);
            return new AjaxResult<object>
            {
                Success = true,
                Message = "",
                Data = new
                {
                    Total = records.Item1,
                    Rows = records.Item2
                }
            };
        }

        /// <summary>
        /// TK的进入重新铺货界面先调用此接口获取详情数据
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult<GlobalProductSearchDetailRes> GetTkListingTaskRecordDetail(PhGlobalProductDetailModel query)
        {
            query.FxUserId = SiteContext.GetCurrentFxUserId();
            var detail = new ListingTaskRecordsService().GetTkListingTaskRecordDetail(query);
            return new AjaxResult<GlobalProductSearchDetailRes>
            {
                Success = detail.Item1,
                Message = detail.Item2,
                Data = detail.Item3
            };
        }

        /// <summary>
        /// 铺货模块-铺货日志-获取铺货日志店铺列表数据
        /// [肖茂翔]
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public AjaxResult<List<ListingShopModel>> GetShopList()
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var result = new ListingTaskRecordsService().GetShopList(fxUserId);
            return new AjaxResult<List<ListingShopModel>>
            {
                Success = true,
                Message = "",
                Data = result
            };
        }

        /// <summary>
        /// 功能：编辑铺货页面--开始铺货；
        /// 版本：2024-07-16 邱庆基 创建；
        /// 2024-07-23 邱庆基 返回数据增加BathNo字段；
        /// 多商品铺货之后，入口：铺货日志点击重新铺货
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult<ListingTaskStatusStat> Save(PtProductInfoModel model)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            //指定用户
            model.FxUserId = fxUserId;
            var service = new ListingTaskRecordsService();
            var ptmodel = new PtProductInfoService().TransferToPtProductInfo(model);
            var result = service.SaveFromPtProductInfo(ptmodel);
            return new AjaxResult<ListingTaskStatusStat>
            {
                Success = result.Success,
                Message = result.Message,
                Data = result.Data,
            };
        }

        /// <summary>
        /// 功能：Tk跨境的编辑铺货页面--重新铺货；
        /// 版本：2024-11-29 孔繁辰 创建；
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult<ListingTaskStatusStat> TkSave(GlobalProductUpdateModel model)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            //指定用户,这地方要做越权判断，只能编辑同一个用户下的铺货日志全球产品信息和店铺产品信息
            var service = new ListingTaskRecordsService();
            var result = service.TkUpdateListingProductInfo(model, fxUserId);
            return new AjaxResult<ListingTaskStatusStat>
            {
                Success = result.Success,
                Message = result.Message,
                Data = result.Data,
            };
        }

        /// <summary>
        /// 功能：铺货状态统计；
        /// 版本：2024-07-23 邱庆基 创建
        /// </summary>
        /// <param name="model">查询</param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult<ListingTaskStatusStat> StatusStat(ListingTaskStatusQuery model)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            model.FxUserId = fxUserId;
            var service = new ListingTaskRecordsService();
            var result = service.StatusStat(model);
            return new AjaxResult<ListingTaskStatusStat>
            {
                Success = true,
                Data = result,
            };
        }

        /// <summary>
        /// 获取平台铺货店铺(包含运费模板)
        /// </summary>
        /// <param name="platformType"></param>
        /// <returns></returns>
        [HttpGet]
        public AjaxResult<object> GetListingShopData(string platformType)
        {
            var userShops = SiteContext.CurrentNoThrow.AllShops.Where(x => x.PlatformType.Equals(platformType, System.StringComparison.CurrentCultureIgnoreCase)).ToList();

            Dictionary<int, List<PlatformDeliveryTemplate>> deliveryTemplatesDic = new Dictionary<int, List<PlatformDeliveryTemplate>>();
            userShops.ForEach(s =>
            {
                try
                {
                    ZhiDianNewPlatformService service = new ZhiDianNewPlatformService(s);
                    var deliveryTemplates = service.GetListingDeliveryTemplate();

                    if (deliveryTemplates != null && deliveryTemplates.Count > 0)
                    {
                        var platformDeliveryTemplates = deliveryTemplates.Select(x => new PlatformDeliveryTemplate
                        {
                            Id = x.Id,
                            DeliveryTemplateName = x.Name
                        }).ToList();
                        if (platformDeliveryTemplates != null && platformDeliveryTemplates.Count > 0)
                        {
                            if (!deliveryTemplatesDic.ContainsKey(s.Id))
                            {
                                deliveryTemplatesDic.Add(s.Id, platformDeliveryTemplates);
                            }
                            else
                            {
                                deliveryTemplatesDic[s.Id] = platformDeliveryTemplates;
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    Log.WriteError($"{nameof(GetListingShopData)}-获取店铺模板异常：{ex.Message}");
                }
            });
            return new AjaxResult<object>
            {
                Success = true,
                Message = "",
                Data = userShops.Select(x => new
                {
                    x.Id,
                    x.ShopName,
                    x.PlatformType,
                    x.ShopId,
                    x.CreateTime,
                    x.AuthTime,
                    ServiceEndTime = x.ExpireTime,
                    x.IsAuthExpired,
                    x.Status,
                    AllExpressTemplates = deliveryTemplatesDic.ContainsKey(x.Id) ? deliveryTemplatesDic[x.Id] : new List<PlatformDeliveryTemplate>()
                })
            };
        }
        /// <summary>
        /// 获取指定店铺模板
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult<List<ShopDeliveryTemplate>> ShopAllDeliveryTemplate(ShopDeliveryTemplateReq req)
        {
            var userShops = SiteContext.CurrentNoThrow.AllShops.Where(x => x.PlatformType.Equals(req.PlatformType, System.StringComparison.CurrentCultureIgnoreCase) && req.ShopIds.Contains(x.Id)).ToList();
            var templates = new Services.Services.SupplierProduct.ListingProduct.ListingTemplateGroupItemService().RefreshDeliveryTemplate(req.ShopIds);
            //Dictionary<int, List<ShopDeliveryTemplate>> deliveryTemplatesDic = new Dictionary<int, List<ShopDeliveryTemplate>>();
            List<ShopDeliveryTemplate> shopDeliveryTemplates = new List<ShopDeliveryTemplate>();
            userShops.ForEach(s =>
            {
                ShopDeliveryTemplate shopDeliveryTemplate = new ShopDeliveryTemplate()
                {
                    ShopId = s.Id,
                    ShopName = s.ShopName,
                };
                if (templates.ContainsKey(s.Id))
                {
                    shopDeliveryTemplate.AllExpressTemplates = templates[s.Id];
                }
                shopDeliveryTemplates.Add(shopDeliveryTemplate);
            });
            #region 测试代码，需要删除
            //if (CustomerConfig.IsDebug)
            //{
            //    shopDeliveryTemplates.ForEach(x => {

            //        if(x.AllExpressTemplates.Count == 0)
            //        {
            //            List<PlatformDeliveryTemplate> platformTemplates = new List<PlatformDeliveryTemplate>();
            //            Random random = new Random();
            //            for (int i = 0; i < 3; i++)
            //            {
            //                var temp = new PlatformDeliveryTemplate();
            //                temp.Id = random.Next(10000, 10005).ToString();
            //                temp.DeliveryTemplateName = $"debug模板-{temp.Id}";
            //                if (!platformTemplates.Any(p => p.Id == temp.Id))
            //                {
            //                    platformTemplates.Add(temp);
            //                }
            //            }
            //            x.AllExpressTemplates.AddRange (platformTemplates);
            //        }
            //    });
            //}
            #endregion
            return new AjaxResult<List<ShopDeliveryTemplate>>
            {
                Success = true,
                Message = "",
                Data = shopDeliveryTemplates
            };
        }

        /// <summary>
        /// 切换自己的分区，目前仅拼多多和精选适用
        /// [肖茂翔]
        /// </summary>
        /// <returns></returns>
        public AjaxResult<string> GetSelfDbName()
        {
            if (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString()
                || CustomerConfig.CloudPlatformType == CloudPlatformType.Pinduoduo.ToString())
            {
                var dbName = Request["dbname"] + "";
                if (!string.IsNullOrEmpty(dbName))
                {
                    try
                    {
                        dbName = DES.DecryptDES(dbName, CustomerConfig.LoginCookieEncryptKey);
                    }
                    catch (Exception)
                    {
                        // ignored
                    }
                }
                var selfDbName = SiteContext.Current.CurrentDbSettingConfig?.DbNameConfig?.DbName ?? "";
                if (dbName != selfDbName && !string.IsNullOrEmpty(selfDbName))
                {
                    var sc = new SiteContext(SiteContext.Current.CurrentFxUser, selfDbName);
                    selfDbName = DES.EncryptDES(selfDbName, CustomerConfig.LoginCookieEncryptKey);
                    return SuccessResult(selfDbName);
                }
            }

            return SuccessResult(string.Empty);
        }

        /// <summary>
        /// 铺货模块-铺货日志-重新同步
        /// [肖茂翔]
        /// </summary>
        /// <param name="taskCode">铺货任务Code</param>
        /// <returns></returns>
        public AjaxResult<string> ReSync(string taskCode)
        {
            if (taskCode.IsNullOrEmpty()) return FailedResult("任务Code不能为空");
            var fxUserId = SiteContext.GetCurrentFxUserId();
            var msgModel = new ListingCompleteMessageModel()
            {
                ListingTaskCode = taskCode,
                FxUserId = fxUserId,
                Time = DateTime.Now
            };

            RabbitMQService.SendMessage(msgModel, RabbitMQService.FxListingCompleteDescription);

            return SuccessResult("");
        }

        /// <summary>
        /// 铺货模块-铺货日志-获取店铺平台商品详情链接
        /// [肖茂翔]
        /// </summary>
        /// <param name="platform"></param>
        /// <param name="productId"></param>
        /// <returns></returns>
        [HttpGet]
        public AjaxResult<string> GetDetailUrl(string platform, string productId)
        {
            switch (platform)
            {
                case "TouTiao":
                    return SuccessResult($"https://fxg.jinritemai.com/ffa/g/create?product_id={productId}");
            }
            return FailedResult("");
        }


        /// <summary>
        /// 铺货模块-铺货设置-批量铺货设置，返回 UniqueCode 出来
        /// [张立聪]
        /// </summary>
        /// <returns>返回 UniqueCode </returns>
        [HttpPost]
        public AjaxResult<List<string>> BatchListingSet(BatchListingSetReq req)
        {
            var errorMessage = EntryVerification();
            if (errorMessage != null) return FailedResult<List<string>>(null, errorMessage);

            Stopwatch stopwatch = Stopwatch.StartNew();
            int saveResult = new UserListingSettingService().SaveOrUpdate(req.UserListingSetting, true);
            if (saveResult == 0)
            {
                return FailedResult<List<string>>(null, "保存失败，请稍后再试！");
            }
            stopwatch.Stop();
            Log.Debug(() => $"1.铺货设置保存 耗时：{stopwatch.ElapsedMilliseconds}ms");

            var ptsvc = new PtProductInfoService();
            List<PtProductInfoModel> data = ptsvc.BatchCreatePtInfoDrafts(req);

            stopwatch.Restart();
            List<string> codeList = new List<string>();
            foreach (var item in data)
            {
                var m = ptsvc.AddOrUpdate(item);
                if (m != null)
                {
                    codeList.Add(m.UniqueCode);
                }
            }

            stopwatch.Stop();
            Log.Debug(() => $"3.草稿入库 耗时：{stopwatch.ElapsedMilliseconds}ms");
            return SuccessResult(codeList);
        }


        /// <summary>
        /// 铺货模块-铺货设置-获取批量铺货设置
        /// [张立聪]
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult<BatchListingSettingValue> GetBatchListing(GetBatchListingReq req)
        {
            // var userListingSetting = new UserListingSettingService().Get(req.PlatformType)?.ListingSettingData?.BatchSettingValue;
            var userListingSetting = new UserListingSettingService().GetSettingModel(req.PlatformType);
            return SuccessResult(userListingSetting);
        }

        #region 数据初始化
        /// <summary>
        /// 铺货模块-重复铺货检测-测试
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult<string> TestCheckRepeatPtListing(PtProductInfo model)
        {
            var msg = IpWhiteCheck();
            if (msg != null) return FailedResult<string>(null, msg);

            var shopId = Request.QueryString.Get("shopId");
            var res = new ListingTaskRecordsService().CheckRepeatPtListingV2(int.Parse(shopId), model);
            return SuccessResult(res.ToString());
        }

        /// <summary>
        /// 类目-初始化
        /// </summary>
        /// <param name="id">类目Id</param>
        /// <returns></returns>
        [HttpGet]
        public AjaxResult<string> TestSyncFnDdCateGory(int id = 1681)
        {
            var msg = IpWhiteCheck();
            if (msg != null) return FailedResult<string>(null, msg);

            var sslist = new List<int>() { id };
            new Services.PlatformCategorySupplierService().SyncFnDdCateGory(id);
            return SuccessResult("ok");
        }

        /// <summary>
        /// 类目属性-初始化
        /// </summary>
        /// <param name="id">店铺Id</param>
        /// <param name="cateId">类目Id</param>
        /// <returns></returns>
        [HttpGet]
        public AjaxResult<string> TestSyncFnDdCateGoryProp(int id = 1370669, string cateId = "20196")
        {
            var msg = IpWhiteCheck();
            if (msg != null) return FailedResult<string>(null, msg);

            var targetShops = new ShopService(PlatformAppScene.listing).GetShopByIds(new List<int>() { id });
            var shop = targetShops.FirstOrDefault();
            PlatformFactory.GetPlatformService(shop, scene: PlatformAppScene.listing);

            new Services.PlatformCategorySupplierService().SyncFnDdCategoryProp(shop, new List<Data.PlatformCategory>()
            {
                new Data.PlatformCategory(){ CateId = cateId}
            });
            return SuccessResult("ok");
        }

        /// <summary>
        /// 商品发布规则-初始化
        /// </summary>
        /// <param name="id">店铺Id</param>
        /// <param name="cateId">类目Id</param>
        [HttpGet]
        public AjaxResult<string> TestSyncFnDdCategoryPublishRule(int id = 1370669, string cateId = "22994")
        {
            var msg = IpWhiteCheck();
            if (msg != null) return FailedResult<string>(null, msg);

            var targetShops = new ShopService(PlatformAppScene.listing).GetShopByIds(new List<int>() { id });
            var shop = targetShops.FirstOrDefault();
            PlatformFactory.GetPlatformService(shop, scene: PlatformAppScene.listing);

            var svc = new PlatformCategorySupplierService();
            svc.SyncFnDdCategoryPublishRule(shop, new List<Data.PlatformCategory>()
            {
                new Data.PlatformCategory(){ CateId = cateId}
            });
            return SuccessResult("ok");
        }

        /// <summary>
        /// 抖店sku导航属性-初始化
        /// </summary>
        /// <param name="id">店铺Id</param>
        /// <returns></returns>
        [HttpGet]
        public AjaxResult<string> LabourSyncNavigationCateProp(int id = 1370669)
        {
            var msg = IpWhiteCheck();
            if (msg != null) return FailedResult<string>(null, msg);

            var targetShops = new ShopService(PlatformAppScene.listing).GetShopByIds(new List<int>() { id });
            var shop = targetShops.FirstOrDefault();
            PlatformFactory.GetPlatformService(shop, scene: PlatformAppScene.listing);

            new Services.PlatformCategorySupplierService().SyncNavigationCateProp(shop);
            return SuccessResult("ok");
        }
        #endregion

    }
}