using DianGuanJiaApp.Data.Entity.SupplierProduct;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.FxModel.CategoryProduct;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Model.BaseProduct;
using DianGuanJiaApp.Models;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Services.BaseProduct;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Services.Services.SupplierProduct;
using DianGuanJiaApp.Services.Services.SupplierProduct.ListingProduct;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.Other;
using Swashbuckle.Swagger;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web.Mvc;
using DianGuanJiaApp.Services.Services;

namespace DianGuanJiaApp.ErpWeb.ApiControllers
{
    /// <summary>
    /// 平台资料
    /// </summary>
    public class PtProductInfoController : BaseApiController
    {
        /// <summary>
        /// 
        /// </summary>
        public PtProductInfoController()
        {

        }
        /// <summary>
        /// 获取指定平台资料（切换到平台资料编辑/铺货页面时会发起改请求）【钟柱】
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult<PtProductInfoModel> PtProductDatas(PtInfoReqModel model)//(string platformType, long productUid, string dbFlag)
        {
            model.Token = this.PageToken;
            // dbFlag supplierproduct=货盘，business=普通业务库，其他值=基础商品库
            if (model == null) throw new Utility.LogicException("所传参数异常！");

            // 需判断是否是当前用户的厂商
            if (model.FromType == 3)
            {
                SupplierUserService supplierUserService = new SupplierUserService();
                //var supplier = supplierUserService.GetByUser(SiteContext.GetCurrentFxUserId(),model.FxUserId);
                if (!supplierUserService.IsBindSupplierUser(SiteContext.GetCurrentFxUserId(), model.FxUserId))
                {
                    throw new Utility.LogicException("非当前用户厂商");
                }
            }
            else
            {
                model.FxUserId = SiteContext.GetCurrentFxUserId();
            }
            var result = new PtProductInfoModel();

            #region 铺货任务重新铺货
            string souserCode = model.FromCode;
            ListingTaskRecords listingTask = null;
            if (model.FromType == 4)
            {
                if (model.FromCode.IsNullOrEmpty())
                    throw new Utility.LogicException("任务code");
                var listingService = new ListingTaskRecordsService();
                var fxUserId = SiteContext.Current.CurrentFxUserId;
                listingTask = listingService.GetByCode(model.FromCode, fxUserId);
                if (listingTask == null)
                    throw new Utility.LogicException("未找到对应任务！");
                model.FromCode = listingTask.PtProductUniqueCode;
            }
            #endregion

            // 默认基础商品库
            var _service = new PtProductInfoService(string.Empty, SiteContext.GetCurrentFxUserId());
            result = _service.GetPtProductInfo(model);
            // 当前是铺货界面 2024-11-07 单品走多商品铺货逻辑，不需要此方法了
            //if (model.FromType != 1)
            //{
            //    result.UserListingSetting = new UserListingSettingService()?.Get(model.PlatformType)?.ListingSettingData;
            //}
            if (model.FromType == 4)
            {
                result.FromCode = souserCode;
                result.FromType = model.FromType;
                var packJson = listingTask?.Ext?.PackJson.ToObject<PlatformListingModel>();
                result.ListingShopIds = new List<int> { listingTask.ShopId};
                // 铺货设置
                if (packJson != null)
                {
                    result.UserListingSetting = packJson.PtProductInfo?.UserListingSetting;
                    if (result.UserListingSetting != null && result.UserListingSetting.BatchSettingValue!=null) 
                    {
                        // 批量铺货前端兼容
                        result.UserListingSetting.ListingSettingValue = result.UserListingSetting.BatchSettingValue;
                    }
                }
                //result.ProductInfoSkus = packJson.PtProductInfo.Ext.SkuJson.ToObject<List<PtProductInfoSkuModel>>();
            }
            
            // 等级分销价换算规则
            if (model.FromType == 3 || model.FromType == 4)
            {
                // 获取厂家的换算规则
                var supplierRuleModels = new MemberLevelService().GetSupplierRuleModel(new List<int> { model.FxUserId }, SiteContext.Current.CurrentFxUserId);
                if (supplierRuleModels?.Count > 0 && supplierRuleModels.TryGetValue(model.FxUserId, out var supplierRuleModel))
                {
                    result.ProductInfoSkus.ForEach(sku =>
                    {
                        var (newPrice, isChanged) = MemberLevelService.DistributePriceChange(sku.DistributePrice, supplierRuleModel.PriceRule, supplierRuleModel.FinalDistributePriceCorrectRule);
                        if (isChanged) sku.DistributePrice = newPrice ?? sku.DistributePrice;
                    });
                }
            } 

            #region 类目发布权限校验(铺货日志-重新铺货)
            if (model.FromType != 1 &&  !string.IsNullOrEmpty(result.CategoryId))
            {
                var shopIds = model.FromType == 4 ? new List<int> { listingTask.ShopId } : model.ShopIds;

                // 店铺Id，越权检查
                if (!SiteContext.Current.AllShops.Any(a => shopIds.Contains(a.Id)))
                    throw new LogicException("没有权限");

                var authReqModels = new List<CategoryCheckAuth>();
                var shops = new ShopService(PlatformAppScene.listing).GetShopByIds(shopIds);
                foreach (var shop in shops)
                {
                    PlatformFactory.GetPlatformService(shop, scene: PlatformAppScene.listing);
                    var data = new CategoryCheckAuth();
                    data.ShopId = shop.Id;
                    data.Shop = shop;
                    data.CateId = result.CategoryId;
                    data.IsAuth = false;
                    data.UniqueCode = result.UniqueCode;
                    authReqModels.Add(data);
                }

                var authResModels = _service.CheckShopCateListAuth(authReqModels);
                var models = authResModels.Where(p => !p.IsAuth && p.UniqueCode == result.UniqueCode).ToList();
                result.ShopCount = models.Count();
                result.ShopNames = string.Join(",", models.Select(p => p.Shop.ShopName).ToList());
            }
            #endregion

            return new AjaxResult<PtProductInfoModel>
            {
                Success = true,
                Message = "",
                Data = result
            };
        }
        /// <summary>
        /// 根据类目转换基础资料相关属性和规格 - 选择类目时进行触发 【钟柱】
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult<TranAttributeAndSkuResult> TranAttributeAndSku(TranAttributeAndSkuModel reqModel)
        {
            var _service = new PtProductInfoService(reqModel.DbFlag, SiteContext.GetCurrentFxUserId());
            var tranResult = _service.TranAttributeAndSku(reqModel.SourceSku, reqModel.SourcePropJson, reqModel.CategoryId, reqModel.PlatformType,reqModel.SkuModeType);
            return new AjaxResult<TranAttributeAndSkuResult>
            {
                Success = true,
                Message = "",
                Data = tranResult
            };
        }
        /// <summary>
        /// 保存及更新平台资料-【钟柱】
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult<string> SaveOrUpdate(PtProductInfoModel model)
        {
            var _service = new PtProductInfoService(string.Empty, SiteContext.GetCurrentFxUserId());
            bool isSuccess = _service.SaveOrUpdate(model);
            return new AjaxResult<string>
            {
                Success = true,
                Message = "",
                Data = isSuccess ? "保存成功" : "保存失败"
            };
        }

        /// <summary>
        /// 基础资料与平台资料规格不一致，需要以基础资料的规格为主，重新生成一份新的规格
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public AjaxResult<object> GetTranBaseProductSku(long productUid, string categoryId, string platformTtype)
        {
            var _service = new PtProductInfoService(string.Empty, SiteContext.GetCurrentFxUserId());
            var result = _service.TranAttributeAndSku(productUid, categoryId, platformTtype);
            return new AjaxResult<object>
            {
                Success = true,
                Message = "",
                Data = new
                {
                    result.AttributeTypes,
                    result.ProductInfoSkus,
                    result.SellPropertyJson,
                }
            };
        }
        /// <summary>
        /// 获取级联属性（仅限抖店）（因现抖店相关接口暂不支持级联属性，故此接口无用，可以保留待日后用）
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult<DyCascadeValue> CascadeValueData(DyCascadeValueReqModel req)
        {
            var _service = new PtProductInfoService("GG", SiteContext.GetCurrentFxUserId());

            DyCascadeValue cascadeValue = _service.GetCascadeValue(req);

            return new AjaxResult<DyCascadeValue> { Success = cascadeValue != null, Data = cascadeValue, Message = cascadeValue != null ? string.Empty : "获取级联属性失败" };
        }

    }
    /// <summary>
    /// 转换规格和属性的请求model
    /// </summary>
    public class TranAttributeAndSkuModel
    {
        /// <summary>
        /// 抖音类目Id
        /// </summary>
        public string CategoryId { get; set; }
        /// <summary>
        /// 基础资料商品Id
        /// </summary>
        public long ProductUid { get; set; }
        /// <summary>
        /// 平台
        /// </summary>
        public string PlatformType { get; set; }
        /// <summary>
        /// supplierproduct=货盘，business=普通业务库，其他值=基础商品库
        /// </summary>
        public string DbFlag { get; set; }

        /// <summary>
        /// 来源属性
        /// </summary>
        public string SourcePropJson { get; set; }
        /// <summary>
        /// 来源Sku
        /// </summary>
        public List<PtProductInfoSkuModel> SourceSku { get; set; }

        /// <summary>
        /// 平台规格模式=0（默认）、自定义规格模式=1
        /// </summary>
        public int SkuModeType { get; set; }
    }
}