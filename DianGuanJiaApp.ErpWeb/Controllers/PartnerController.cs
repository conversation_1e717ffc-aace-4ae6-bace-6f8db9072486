using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Extension;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.Mvc;
using System.Threading;
using DianGuanJiaApp.Models;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Services.Services;
using System.Threading.Tasks;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Controllers;
using DianGuanJiaApp.Data.Model.LogModel;
using DianGuanJiaApp.Services.Services.DataEventTracking;
using DianGuanJiaApp.Services.Services.SettingsService;
using DianGuanJiaApp.Services.ServicesExtension;
using DianGuanJiaApp.Data.FxModel;
using System.Runtime.Remoting.Metadata.W3cXsd2001;
using DianGuanJiaApp.Services.BaseProduct;
using DianGuanJiaApp.Data.MessageModel;
using DianGuanJiaApp.Services.Services.MessageQueue;

namespace DianGuanJiaApp.ErpWeb.Controllers
{
    /// <summary>
    /// 授权管理
    /// </summary>
    [SessionState(System.Web.SessionState.SessionStateBehavior.Disabled)]
    public partial class PartnerController : BaseController
    {
        private FxUserShopService _fxUserShopService = new FxUserShopService();
        private UserFxService _userFxService = new UserFxService();
        private ShopService _shopService = new ShopService();
        private SupplierUserService _supplierUserService = new SupplierUserService();
        private FxUserAddressService _fxUserAddressService = new FxUserAddressService();
        private PathFlowNodeService _pathFlowNodeService = new PathFlowNodeService();
        private ShopExtensionService _shopExtensionService = new ShopExtensionService();
        private SyncStatusService _syncStatusService = new SyncStatusService();
        private SyncTaskService _syncTaskService = new SyncTaskService();
        private UserService _userService = new UserService();
        private CommonQRCodeService _commonQRCodeService = new CommonQRCodeService();
        private CommonSettingService _commonSettingService = new CommonSettingService();
        private readonly CooperateStatusRecordService _cooperateStatusRecordService = new CooperateStatusRecordService();
        private readonly BusinessCardService _businessCardService = new BusinessCardService();
        private readonly BusinessCardRemarkService _businessCardRemarkService = new BusinessCardRemarkService();
        private readonly CooperateEvaluateService _cooperateEvaluateService = new CooperateEvaluateService();
        private readonly SupplierUserService _supplierService = new SupplierUserService();
        private readonly SendHistoryService _sendHistoryService = new SendHistoryService();
        // GET: Partner
        public ActionResult Index()
        {
            var token = Request.QueryString["token"];
            ViewBag.DefaultShopId = Request["shopId"].ToString2();
            ViewBag.SupportPlatform = CustomerConfig.GetFxUserPlatform(token).ToJson();
            var fxuserId = SiteContext.Current.CurrentFxUserId;
            //var checkResult = _fxUserShopService.IsFxUserIsHaveAvaliableCount(fxuserId, ServiceVersionTypeEnum.ShopUnit);
            //ViewBag.AvailableCountInfo = checkResult.ToJson();
            //ViewBag.IsAddAvailable = checkResult.IsAvailable;
            //if (checkResult.IsAvailable == false)
            //    ViewBag.SupportPlatform = "[]";

            var result = _userFxService.CheckFxUserHasTwoApp(SiteContext.Current.CurrentFxUserId);
            ViewBag.HasTwoApp = result > 1 ? "true" : "false";
            ViewBag.AvailableCountInfo = null;//checkResult.ToJson();
            ViewBag.IsAddAvailable = true;//checkResult.IsAvailable;
            #region 是否是老用户(需求:抖音老应用兼容新应用改造内容,ID1013015)
            ViewBag.IsTouTiaoOldUser = _commonSettingService.IsTipsDouYinNewApp() ? "true" : "false";
            ViewBag.IsWxVideoOldUser = _userFxService.CheckWxVideoUser(fxuserId) ? "true" : "false";
            #endregion

            //更新商家是否显示1688菜单的缓存
            FxCaching.ForeRefeshCache(FxCachingType.Show1688Menu, fxuserId.ToString2());
            //_commonSettingService.UpdateIsShow1688MenuCache(fxuserId);

            //1688新老应用开通信息
            ViewBag.OpenInfo1688 = (new
            {
                QingService.Instance.IsOpenQing,
                QingService.Instance.IsOpenAlibabaOld,
                QingService.Instance.IsOpen1688Shops
            }).ToJson();

            #region 向平台回传当前账号开通的面单情况
            //向平台回传当前账号开通的面单情况
            new OpenEbillSendTaskService().ExecuteCheckEbillOpenStatusAsync();
            #endregion

            // 页面按钮展示权限
            ViewBag.ShowPermDict = new Dictionary<string, bool>
            {
                {$"#{nameof(FxPermission.AddNewShop)}",SiteContext.HasPermission(FxPermission.AddNewShop)},
                {$".{nameof(FxPermission.RenewalShop)}",SiteContext.HasPermission(FxPermission.RenewalShop)},
                {$".{nameof(FxPermission.EditShop)}",SiteContext.HasPermission(FxPermission.EditShop)},
                {$".{nameof(FxPermission.UnbindShop)}",SiteContext.HasPermission(FxPermission.UnbindShop)},
            }.ToJson();

            return View();
        }

        #region 我的店铺
        /// <summary>
        /// 加载我的店铺列表
        /// 1.获取店铺绑定信息查询FxUserShop，P_SyncStatus.LastSyncMessage会根据平台跨云获取，保证准确性
        /// 2.获取ShopExtension授权信息表，可以配置是否预检查授权/到期时间，方法内会校验订购记录按服务到期排序或针对某个应用择优使用授权token
        /// 2.1 注意服务到期时间字段已经支持多个应用获取对应的时间，shop?.ShopExtension?.ExpireTime字段已经在授权站点上线了很长一段时间，多应用的大部分都应该有值
        /// 2.2 授权token择优排序：shop?.ShopExtension?.ExpireTime=》shop.ExpireTime =》fs.AuthTime ，如ShopExtension?.ExpireTime为空那就会依赖订购记录来排序选择，历史兼容问题不同平台有不同场景
        /// 3.某些平台页面要展示双应用，现在还依赖订购记录，双应用都有订购记录就会展示
        /// 4.最后判断FxUserShop的NickName/AuthTime/Satus有变化的话就需要及时更新数据库
        /// 5.[暂时注释]UpdateLastSyncMessageByShop更新同步表是在2步骤[预检查授权/到期时间]开启的情况下才更新
        /// </summary>
        /// <param name="_reqModel"></param>
        /// <returns></returns>
        public ActionResult LoadMyShopList(FxUserShopQueryModel _reqModel)
        {
            var shopId = Request["ShopId"].ToInt();
            if (shopId > 0 && _reqModel.ShopId == 0)
                _reqModel.ShopId = shopId;
            _reqModel.FxUserId = SiteContext.Current.CurrentFxUserId;
            _reqModel.QueryPageType = 1;

            // 手动同步弹窗查询
            if (_reqModel.Flag == 99)
            {
                _reqModel.PageIndex = 1;
                _reqModel.PageSize = 2000;
                _reqModel.Status = FxUserShopStatus.Binded;
            }

            var tutles = _fxUserShopService.GetList(_reqModel);
            var newResult = tutles.Item2;
            if (newResult.Any() == false)
                return SuccessResult(new { Total = 0, List = newResult });

            bool isCheckAuth = false;
            //数据库原始数据
            var oldnewResult = newResult.ToJson().ToObject<List<FxUserShop>>();
            //所有店铺
            var shopIds = newResult.Select(x => x.ShopId).Distinct().ToList();
            var shops = new ShopService("all").GetShopsAndShopExtensionFunc(shopIds, isCheckAuth, isProcessPddShopExtension: false);


            #region 更新店铺名称，暂时只支持视频号
            // 更新店铺名称
            if (shops.Any() && newResult.Any())
            {
                foreach (var fs in newResult.Where(w => w.PlatformType == PlatformType.WxVideo.ToString()))
                {
                    var shop = shops.FirstOrDefault(x => x.Id == fs.ShopId);
                    if (shop == null || shop.NickName == fs.NickName)
                    {
                        continue;
                    }

                    fs.NickName = shop.NickName;

                    if (string.IsNullOrWhiteSpace(fs.NickName))
                    {
                        fs.NickName = shop.ShopName;
                    }
                }
            }
            #endregion

            if (shops.Any() && newResult.Any())
            {
                //拼多多是否使用打单应用
                var isUsePrintSystemApp = _commonSettingService.PddIsUsePrintSystemApp();
                foreach (var fs in newResult)
                {
                    var shop = shops.FirstOrDefault(x => x.Id == fs.ShopId);
                    if (shop == null)
                        continue;

                    fs.PddIsUsePrintSystemApp = isUsePrintSystemApp;
                    fs.VenderId = shop.VenderId;
                    fs.AuthTime = shop?.ShopExtension?.ExpireTime ?? shop.ExpireTime ?? fs.AuthTime;

                    //如走接口校验，以接口返回的错误消息为准
                    if (isCheckAuth)
                        fs.LastSyncMessage = shop.LastSyncMessage;

                    // 手动退款
                    fs.IsRefundExpire = shop?.ShopExtension?.ExpireTime != null && shop?.ShopExtension?.RefundExpiredTime != null && shop?.ShopExtension?.ExpireTime <= shop?.ShopExtension?.RefundExpiredTime;
                    if (fs.IsRefundExpire)
                    {
                        fs.AppKey = shop?.ShopExtension?.AppKey;
                    }
                }
            }

            var serviceAppOrders = new List<ServiceAppOrder>();
            //查询订购记录，目前只有头条会用到订购记录来校验前端应该显示什么信息
            var ptShopsIds = shops.Where(s => s.PlatformType == PlatformType.TouTiao.ToString()).Select(x => x.ShopId).ToList();
            // 查询店铺订购记录截止时间
            if (ptShopsIds.Any())
            {
                var getShopIds = shops.Where(r => r.PlatformType == PlatformType.TouTiao.ToString()).Select(x => x.Id).Distinct().ToList();
                serviceAppOrders = _userService.GetLastEndOrdersByShopIds(getShopIds, ptShopsIds);
            }

            #region 附赠旧版应用的信息，对应需求【【1688分销】添加店铺授权展示逻辑】 https://www.tapd.cn/32787034/prong/stories/view/1132787034001017435
            var bundledResult = new List<FxUserShop>();
            //查询旧版阿里订购记录
            var alibabaptShops = shops?.Where(x => x.PlatformType == PlatformType.Alibaba.ToString()).ToList();
            if (alibabaptShops != null && alibabaptShops.Any())
            {
                var cuurTime = DateTime.Now;
                //使用旧应用就查询是否存在新应用订购记录，使用新应用则相反
                var oldalibabaShopsIds = alibabaptShops.Where(x => x.ShopExtension == null).Select(x => x.Id).ToList();
                var oldalibabaptShopsIds = alibabaptShops.Where(x => x.ShopExtension == null).Select(x => x.ShopId).ToList();
                var newAppOrders = _userService.GetLastEndOrdersByPtShopsIds(oldalibabaptShopsIds, PlatformType.Alibaba.ToString());
                var newalibabaShops = _shopExtensionService.GetRawShopExtensionByShopIds(oldalibabaShopsIds);

                var newalibabaShopsIds = alibabaptShops.Where(x => x.ShopExtension == null).Select(x => x.Id).ToList();
                var newalibabaptShopsIds = alibabaptShops.Where(x => x.ShopExtension != null).Select(x => x.ShopId).ToList();
                var oldAppOrderList = _userService.GetLastEndAppOrderListByPtShopsIds(newalibabaptShopsIds, PlatformType.Alibaba.ToString());
                var oldalibabaShops = _shopService.GetShopByIds(newalibabaShopsIds);

                alibabaptShops.ForEach(s =>
                {
                    if (s.ShopExtension != null)
                    {
                        var lastapp = AppServiceFactory.GetAppService(s.PlatformType).getAppModel(CustomerConfig.AlibabaAppKey);
                        var oldAppOrder = oldAppOrderList?.FirstOrDefault(a => a.MemberId == s.ShopId && a.PlatformType == s.PlatformType && a.AppKey == lastapp.Appkey);
                        var oldAppShop = oldalibabaShops?.FirstOrDefault(a => a.Id == s.Id);
                        if (oldAppOrder != null)
                        {
                            var oldBinShop = newResult.FirstOrDefault(o => o.ShopId == s.Id);
                            if (oldBinShop != null)
                            {
                                var copyBinShop = oldBinShop.ToJson().ToObject<FxUserShop>();
                                copyBinShop.AppKey = lastapp.Appkey.ToString2();
                                copyBinShop.AppName = lastapp.Name;
                                copyBinShop.AuthTime = oldAppShop?.ExpireTime ?? oldAppOrder.GmtServiceEnd;
                                copyBinShop.AuthUrl = lastapp?.AuthUrl;
                                copyBinShop.PlatformAuthUrl = lastapp?.AuthUrl;
                                //copyBinShop.Status = FxUserShopStatus.Binded;
                                bundledResult.Add(copyBinShop);
                            }
                        }
                    }
                    else
                    {
                        //有一种情况，订购记录在数据库了，但用户并没实际授权过分单系统，不能将附赠信息展示出来
                        var isOpenQing = _shopService.GetAppShopAuth(_reqModel.FxUserId, s.Id, new List<string> { CustomerConfig.AlibabaQingAppKey });
                        if (isOpenQing)
                        {
                            var lastapp = AppServiceFactory.GetAppService(s.PlatformType).getAppModel(CustomerConfig.AlibabaQingAppKey);
                            var newAppOrder = newAppOrders?.FirstOrDefault(a => a.PlatformShopId == s.ShopId && a.PlatformType == s.PlatformType && a.ServiceAppId == lastapp.Appkey);
                            var newAppShop = newalibabaShops?.FirstOrDefault(a => a.ShopId == s.Id && a.AppKey == lastapp.Appkey);
                            if (newAppOrder != null)
                            {
                                var newBinShop = newResult.FirstOrDefault(o => o.ShopId == s.Id);
                                if (newBinShop != null)
                                {
                                    var copyBinShop = newBinShop.ToJson().ToObject<FxUserShop>();
                                    copyBinShop.AppKey = lastapp.Appkey.ToString2();
                                    copyBinShop.AppName = lastapp.Name;
                                    copyBinShop.AuthTime = newAppShop?.ExpireTime ?? newAppOrder.ServiceEnd.Value;
                                    copyBinShop.AuthUrl = lastapp?.AuthUrl;
                                    copyBinShop.PlatformAuthUrl = lastapp?.AuthUrl;
                                    //copyBinShop.Status = FxUserShopStatus.Binded;
                                    copyBinShop.TouTiaoOldOrNew = "Fx";
                                    copyBinShop.PlatformPayUrl2 = lastapp.MarketUrl;
                                    bundledResult.Add(copyBinShop);
                                }
                            }
                        }
                    }
                });
            };
            //查询快手订购记录
            var kuaishouptShops = shops?.Where(x => (x.ShopExtension != null && x.PlatformType == PlatformType.KuaiShou.ToString())).ToList();
            if (kuaishouptShops != null && kuaishouptShops.Any())
            {
                var cuurTime = DateTime.Now;
                var ksshopIds = kuaishouptShops.Select(x => x.Id).ToList();
                var ksshopUids = kuaishouptShops.Select(x => x.Uid).ToList();
                var appOrders = _userService.GetLastEndOrdersWhere(ksshopIds, ksshopUids, PlatformType.KuaiShou.ToString());
                if (appOrders != null && appOrders.Any())
                    serviceAppOrders.AddRange(appOrders);
                var appShops = _shopExtensionService.GetRawShopExtensionByShopIds(ksshopIds);
                kuaishouptShops.ForEach(s =>
                {
                    var appkey = s.ShopExtension.AppKey;
                    if (appkey == CustomerConfig.KuaiShouNewFxAppKey)
                    {
                        var lastapp = AppServiceFactory.GetAppService(s.PlatformType).getAppModel(CustomerConfig.KuaiShouFxAppKey);
                        var oldAppOrder = appOrders?.FirstOrDefault(a => (a.PlatformShopId == s.Uid || a.ShopId == s.Id) && a.ServiceAppId == CustomerConfig.KuaiShouFxAppKey);
                        var oldAppShop = appShops?.FirstOrDefault(a => a.ShopId == s.Id && a.AppKey == CustomerConfig.KuaiShouFxAppKey);
                        if (oldAppOrder != null)
                        {
                            var newBinShop = newResult.FirstOrDefault(o => o.ShopId == s.Id);
                            if (newBinShop != null)
                            {
                                var copyBinShop = newBinShop.ToJson().ToObject<FxUserShop>();
                                copyBinShop.AppKey = lastapp.Appkey.ToString2();
                                copyBinShop.AppName = lastapp.Name;
                                copyBinShop.AuthTime = oldAppShop?.ExpireTime ?? oldAppOrder.ServiceEnd.Value;
                                copyBinShop.AuthUrl = lastapp?.AuthUrl;
                                copyBinShop.PlatformAuthUrl = lastapp?.AuthUrl;
                                copyBinShop.IsHideOtherButton = true;
                                //copyBinShop.Status = FxUserShopStatus.Binded;
                                copyBinShop.PlatformPayUrl2 = lastapp.MarketUrl;
                                bundledResult.Add(copyBinShop);
                            }
                        }
                    }
                    else if (appkey == CustomerConfig.KuaiShouFxAppKey)
                    {
                        var isOpenNew = _shopService.GetAppShopAuth(_reqModel.FxUserId, s.Id, new List<string> { CustomerConfig.KuaiShouNewFxAppKey });
                        if (isOpenNew)
                        {
                            var lastapp = AppServiceFactory.GetAppService(s.PlatformType).getAppModel(CustomerConfig.KuaiShouNewFxAppKey);
                            var newAppOrder = appOrders?.FirstOrDefault(a => (a.PlatformShopId == s.Uid || a.ShopId == s.Id) && a.ServiceAppId == CustomerConfig.KuaiShouNewFxAppKey);
                            var newAppShop = appShops?.FirstOrDefault(a => a.ShopId == s.Id && a.AppKey == CustomerConfig.KuaiShouNewFxAppKey);
                            if (newAppOrder != null)
                            {
                                var newBinShop = newResult.FirstOrDefault(o => o.ShopId == s.Id);
                                if (newBinShop != null)
                                {
                                    var copyBinShop = newBinShop.ToJson().ToObject<FxUserShop>();
                                    copyBinShop.AppKey = lastapp.Appkey.ToString2();
                                    copyBinShop.AppName = lastapp.Name;
                                    copyBinShop.AuthTime = newAppShop?.ExpireTime ?? newAppOrder.ServiceEnd.Value;
                                    copyBinShop.AuthUrl = lastapp?.AuthUrl;
                                    copyBinShop.PlatformAuthUrl = lastapp?.AuthUrl;
                                    copyBinShop.IsHideOtherButton = true;
                                    //copyBinShop.Status = FxUserShopStatus.Binded;
                                    copyBinShop.TouTiaoOldOrNew = "New";
                                    copyBinShop.PlatformPayUrl2 = lastapp.MarketUrl;
                                    bundledResult.Add(copyBinShop);
                                }
                            }
                        }
                    }
                });
            }

            //拼多多-处理双应用
            var pddptShops = shops?.Where(x => (x.PlatformType == PlatformType.Pinduoduo.ToString() || x.PlatformType == PlatformType.KuaiTuanTuan.ToString()) && x.SystemVersion.ToString2() == "ForFxSystem" && x.ShopExtension != null).ToList();

            if (pddptShops != null && pddptShops.Any())
            {
                pddptShops.ForEach(s =>
                {
                    if (s.ShopExtension != null)
                    {
                        var oldBinShop = newResult.FirstOrDefault(o => o.ShopId == s.Id);
                        if (oldBinShop != null)
                        {
                            var lastapp = AppServiceFactory.GetAppService(s.PlatformType).getAppModel(CustomerConfig.PinduoduoAppKey);
                            var copyBinShop = oldBinShop.ToJson().ToObject<FxUserShop>();
                            copyBinShop.AppKey = lastapp?.Appkey;
                            copyBinShop.AppName = lastapp?.Name;
                            copyBinShop.AuthTime = s.ExpireTime ?? copyBinShop.AuthTime;
                            copyBinShop.AuthUrl = lastapp?.AuthUrl;
                            copyBinShop.PlatformAuthUrl = lastapp?.AuthUrl;
                            bundledResult.Add(copyBinShop);
                        }

                    }
                });
            };

            //抖店-处理分单铺货应用2024.07.08
            if (ptShopsIds.Any())
            {
                var getShopIds = shops.Where(r => r.PlatformType == PlatformType.TouTiao.ToString()).Select(x => x.Id).Distinct().ToList();
                var listingShops = new ShopService().GetShopBySourceType(getShopIds, new List<string>()
                                {
                                    CustomerConfig.TouTiaoFxListingAppKey
                                }).Where(a => a.ShopExtension != null).ToList();
                shops.Where(s => s.PlatformType == PlatformType.TouTiao.ToString()).ToList().ForEach(s =>
                {
                    //订购记录存在分单铺货应用，追加铺货应用
                    if ((s.ShopExtension == null || (s.ShopExtension != null && s.ShopExtension.AppKey != CustomerConfig.TouTiaoFxListingAppKey)) && serviceAppOrders.Any(a => a.ServiceAppId == CustomerConfig.TouTiaoFxListingAppKey && a.ShopId == s.Id))
                    {
                        var existExtension = listingShops.FirstOrDefault(a => a.Id == s.Id)?.ShopExtension;
                        var oldBinShop = newResult.FirstOrDefault(o => o.ShopId == s.Id);
                        if (oldBinShop != null && existExtension != null)
                        {
                            var lastapp = AppServiceFactory.GetAppService(s.PlatformType).getAppModel(CustomerConfig.TouTiaoFxListingAppKey);
                            var copyBinShop = oldBinShop.ToJson().ToObject<FxUserShop>();
                            //var existExtension = listingShops.FirstOrDefault(a => a.Id == s.Id)?.ShopExtension;
                            var authTime = existExtension?.ExpireTime;
                            if (authTime == null)
                                authTime = s.ExpireTime;
                            if (authTime == null)
                                authTime = copyBinShop.AuthTime;

                            copyBinShop.AppKey = lastapp?.Appkey;
                            copyBinShop.AppName = lastapp?.Name;
                            copyBinShop.AuthTime = authTime.Value;
                            copyBinShop.AuthUrl = lastapp?.AuthUrl;
                            copyBinShop.PlatformAuthUrl = lastapp?.AuthUrl;
                            copyBinShop.PlatformPayUrl2 = lastapp?.MarketUrl;

                            //使用同步商品的状态
                            copyBinShop.LastSyncMessage = copyBinShop.LastSyncProductMessage;
                            copyBinShop.Status = copyBinShop.SyncProductStatus;

                            bundledResult.Add(copyBinShop);
                        }

                    }
                });
            }
            #endregion

            //京东-处理双应用
            var jingdongShops = shops.Where(x => x.PlatformType == PlatformType.Jingdong.ToString() && string.IsNullOrEmpty(x.SystemVersion) && x.ShopExtension != null).ToList();
            if (jingdongShops != null && jingdongShops.Any())
            {
                jingdongShops.ForEach(s =>
                {
                    var oldBinShop = newResult.FirstOrDefault(o => o.ShopId == s.Id);
                    if (oldBinShop != null)
                    {
                        //AppKey不为空代表新应用过期时间小于旧应用过期时间（内部方法优先使用订购时间较长的应用被赋值）
                        var appKey = string.IsNullOrEmpty(s.AppKey) ? CustomerConfig.JingDongAppKey : CustomerConfig.JingDongFxAppKey;
                        var lastapp = AppServiceFactory.GetAppService(s.PlatformType).getAppModel(appKey);
                        var copyBinShop = oldBinShop.ToJson().ToObject<FxUserShop>();
                        copyBinShop.AppKey = lastapp?.Appkey;
                        copyBinShop.AppName = lastapp?.Name;
                        copyBinShop.AuthTime = s.ExpireTime ?? copyBinShop.AuthTime;
                        copyBinShop.AuthUrl = lastapp?.AuthUrl;
                        copyBinShop.PlatformAuthUrl = lastapp?.AuthUrl;
                        bundledResult.Add(copyBinShop);
                    }
                });
            }
            
            //获取店铺解绑申请信息
            var unbindservice = new FxUnBindTaskService();
            var unbindtasks = unbindservice.GetShops(shopIds, _reqModel.FxUserId);
            Parallel.ForEach(newResult, new ParallelOptions { MaxDegreeOfParallelism = 10 }, (r) =>
            {
                try
                {
                    if (!string.IsNullOrEmpty(unbindservice.TimeCheck()))
                        r.IsHideUnBindButton = true;
                }
                catch { }
                _fxUserShopService.GetAppServiceModel(_reqModel.FxUserId, r, shops, serviceAppOrders);

                //针对铺货应用，使用同步商品的状态来判断是否授权过期
                if (string.IsNullOrEmpty(r.AppKey) == false && CustomerConfig.FxListingAppKeys.Contains(r.AppKey))
                {
                    r.LastSyncMessage = r.LastSyncProductMessage;
                    r.Status = r.SyncProductStatus;
                }

            });

            //原始数据比对处理后的数据 状态/时间/名称不同需要修改
            if (oldnewResult.Any() && newResult.Any())
            {
                var needUpdateShopInfo = new List<FxUserShop>();
                var needUpdateShopSyncInfo = new List<FxUserShop>();
                var oldResultDic = oldnewResult.GroupBy(x => x.ShopId).ToDictionary(x => x.Key, x => x.OrderByDescending(y => y.AuthTime).FirstOrDefault());
                var newResultDic = newResult.GroupBy(x => x.ShopId).ToDictionary(x => x.Key, x => x.OrderByDescending(y => y.AuthTime).FirstOrDefault());
                foreach (var oldd in oldResultDic)
                {
                    var newd = newResultDic[oldd.Key];
                    //1.修改绑定信息
                    if (oldd.Value.Status != newd.Status || oldd.Value.AuthTime != newd.AuthTime || oldd.Value.NickName != newd.NickName || oldd.Value.OldStatus != newd.Status)
					{
                        needUpdateShopInfo.Add(newd);
                    }
                    //2.更新同步表信息
                    needUpdateShopSyncInfo.Add(newd);
                }
                if (needUpdateShopInfo.Any() || needUpdateShopSyncInfo.Any())
                {
                    ThreadPool.QueueUserWorkItem(item =>
                    {
                        #region 修改店铺信息
                        try
                        {
                            if (needUpdateShopInfo.Any())
                                _fxUserShopService.UpdateFxUserShopInfo(needUpdateShopInfo);
                            //if (needUpdateShopSyncInfo.Any()) 
                            //    _syncStatusService.UpdateLastSyncMessageByShop(needUpdateShopSyncInfo);
                            //需要更新缓存
                            FxCaching.RefeshCache(FxCachingType.FxShopSelf, _reqModel.FxUserId);
                        }
                        catch (Exception ex)
                        {
                            Log.WriteError($"Partner/LoadMyShopList修改绑定的店铺信息，异常：{ex}");
                        }
                        #endregion
                    });
                }
            }

            //追加附赠的应用信息
            if (bundledResult.Any())
            {
                newResult.AddRange(bundledResult);

                if (_reqModel.AuthTimeOrderBy == 1)
                    newResult = newResult.OrderBy(x => x.AuthTime).ToList();
                else if (_reqModel.AuthTimeOrderBy == 2)
                    newResult = newResult.OrderByDescending(x => x.AuthTime).ToList();
                else
                    newResult = newResult.OrderByDescending(x => x.CreateTime).ToList();
            }
            var curShopIds = newResult.Select(a => a.ShopId).Distinct().ToList();

            // 获取铺货的店铺，以店铺为维度，看是否订购了铺货的应用
            if (_reqModel.Flag == 1)
            {
                #region 仅获取指定平台铺货的数据
                if (!CustomerConfig.FxListingSupportPlatformTypes.Contains(_reqModel.PlatformType)) throw new LogicException("暂不支持当前平台进行铺货");
                List<FxUserShop> listingFxUserShops = new List<FxUserShop>();
                curShopIds.ForEach(s =>
                {
                    var platformShops = newResult.Where(r => r.PlatformType.Equals(_reqModel.PlatformType, StringComparison.CurrentCultureIgnoreCase) && r.ShopId == s).ToList();
                    if (platformShops.Any())
                    {
                        var listingShop = platformShops.FirstOrDefault(p => p.IsFxListingApp);
                        // 看当前是否已经配置了铺货应用
                        if (listingShop != null)
                        {
                            listingFxUserShops.Add(listingShop);
                        }
                        else
                        {
                            var tempShop = platformShops.FirstOrDefault();
                            FxUserShop newShop = new FxUserShop
                            {
                                NickName = tempShop.NickName,
                                FxUserId = tempShop.FxUserId,
                                FxUserMobile = tempShop.FxUserMobile,
                                FxUserName = tempShop.FxUserName,
                                ShopId = s,
                                PlatformType = _reqModel.PlatformType,
                                IsBlank = true,
                            };
                            newShop.AppKey = CustomerConfig.GetFxListingAppKey(_reqModel.PlatformType);
                            var lastapp = AppServiceFactory.GetAppService(_reqModel.PlatformType).getAppModel(newShop.AppKey);
                            newShop.AppName = lastapp?.Name;
                            newShop.AuthUrl = lastapp?.AuthUrl;
                            newShop.PlatformAuthUrl = lastapp?.AuthUrl;
                            newShop.PlatformPayUrl2 = lastapp?.MarketUrl;
                            newShop.Status = 0;
                            //newShop.PlatformPayUrl2 = newShop.PlatformPayUrl;
                            listingFxUserShops.Add(newShop);
                        }
                    }
                });
                newResult = listingFxUserShops;
                #endregion
            }
            else
            {
                #region 铺货+普通，双应用模式展示处理，未订购另一种类型的补充一条空数据
                var userFlag = SiteContext.Current.CurrentFxUser.UserFlag.ToString2();
                var isWhiteUser = SiteContext.Current.IsWhiteUser;
                //白名单 且 (纯铺货  或 纯铺货转双应用) 的用户
                if (isWhiteUser && (userFlag.Contains("only_listing") || userFlag.Contains("from_listing")))
                {
                    var fxListingSupportPlatformTypes = CustomerConfig.FxListingSupportPlatformTypes;
                    //补充空行的数据<空行位置, 数据>
                    var dicFxUserShop = new Dictionary<int, FxUserShop>();
                    var blankShopIds = new List<int>();
                    var index = 0;
                    var addIndex = 0;
                    newResult.ForEach(s =>
                    {
                        index += 1;
                        if (fxListingSupportPlatformTypes.Contains(s.PlatformType))
                        {
                            //是否有铺货应用
                            var hasFxListingApp = newResult.Any(a => a.IsFxListingApp && a.ShopId == s.ShopId);
                            if (hasFxListingApp == false && blankShopIds.Any(a => a == s.ShopId) == false)
                            {
                                blankShopIds.Add(s.ShopId);
                                var blankFxUserShop = s.ToJson().ToObject<FxUserShop>();
                                blankFxUserShop.AppKey = CustomerConfig.GetFxListingAppKey(s.PlatformType);
                                var lastapp = AppServiceFactory.GetAppService(s.PlatformType).getAppModel(blankFxUserShop.AppKey);

                                blankFxUserShop.AppKey = lastapp?.Appkey;
                                blankFxUserShop.AppName = lastapp?.Name;
                                blankFxUserShop.AuthUrl = lastapp?.AuthUrl;
                                blankFxUserShop.PlatformAuthUrl = lastapp?.AuthUrl;
                                blankFxUserShop.PlatformPayUrl2 = lastapp?.MarketUrl;
                                blankFxUserShop.Status = 0;
                                blankFxUserShop.IsBlank = true;

                                dicFxUserShop.Add(index + addIndex, blankFxUserShop);
                                addIndex += 1;
                            }
                            else
                            {
                                //是否有常规应用
                                var hasNormalApp = newResult.Any(a => a.IsFxListingApp == false && a.ShopId == s.ShopId);
                                if (hasNormalApp == false && blankShopIds.Any(a => a == s.ShopId) == false)
                                {
                                    blankShopIds.Add(s.ShopId);
                                    var blankFxUserShop = s.ToJson().ToObject<FxUserShop>();
                                    blankFxUserShop.AppKey = CustomerConfig.GetFxNormalAppKey(s.PlatformType);
                                    var lastapp = AppServiceFactory.GetAppService(s.PlatformType).getAppModel(blankFxUserShop.AppKey);

                                    blankFxUserShop.AppKey = lastapp?.Appkey;
                                    blankFxUserShop.AppName = lastapp?.Name;
                                    blankFxUserShop.AuthUrl = lastapp?.AuthUrl;
                                    blankFxUserShop.PlatformAuthUrl = lastapp?.AuthUrl;
                                    blankFxUserShop.PlatformPayUrl2 = lastapp?.MarketUrl;
                                    blankFxUserShop.Status = 0;
                                    blankFxUserShop.IsBlank = true;

                                    dicFxUserShop.Add(index + addIndex - 1, blankFxUserShop);
                                    addIndex += 1;
                                }
                            }
                        }
                    });

                    if (dicFxUserShop.Any())
                    {
                        dicFxUserShop.ToList().ForEach(g =>
                        {
                            newResult.Insert(g.Key, g.Value);
                        });
                    }
                }
                #endregion
            }

            // 此处处理解绑任务相关标识，新增应用解绑，需区分
            newResult.ForEach(r =>
            {
                var unbindTask = unbindtasks?.OrderByDescending(x => x.UpdateTime ?? x.CreateTime).FirstOrDefault(x =>
                    x.ShopId == r.ShopId && x.FxUserId == SiteContext.Current.CurrentFxUserId &&
                    x.TaskType != UnBindTaskType.UnBindApplication);
                var unbindApplicationTask = unbindtasks?.FirstOrDefault(x =>
                    x.ShopId == r.ShopId && x.AppKey == r.AppKey && x.TaskType == UnBindTaskType.UnBindApplication);

                if (unbindApplicationTask != null)
                {
                    r.UnBindTaskState = unbindApplicationTask.TaskState.ToInt();
                    if (unbindApplicationTask.TaskState == UnBindTaskState.Ready &&
                        unbindApplicationTask.ExpectedTime < DateTime.Now)
                        r.UnBindTaskState = 1; // 紧急解绑
                }
                else if (unbindTask != null)
                {
                    r.UnBindTaskState = unbindTask.TaskState.ToInt();
                    var unbindTime = unbindTask.ExpectedTime;
                    if (unbindTask.TaskState == UnBindTaskState.Ready &&
                        unbindTime != DateTime.Today.AddHours(23) &&
                        unbindTime < DateTime.Now &&
                        unbindTime.Date == DateTime.Today)
                        r.UnBindTaskState = 1; // 紧急解绑
                }
                else
                {
                    r.UnBindTaskState = -1; // 申请解绑
                }
            });

            if (_reqModel.Flag == 99)
                return HandleManualSyncShopResult(newResult);

            return SuccessResult(new { Total = tutles.Item1, List = newResult, ShopIds = curShopIds });
        }


        /// <summary>
        /// 处理手动同步查询店铺的返回结果
        /// </summary>
        /// <param name="shops"></param>
        /// <returns></returns>
        private ActionResult HandleManualSyncShopResult(List<FxUserShop> shops)
        {
            if (shops.IsNullOrEmptyList())
                return SuccessResult();
            // 0. 排除线下单店铺、不支持订单号同步的店铺
            // 1. 只要授权成功的店铺
            // 2. 只要有同步权限的店铺
            // 3. 相同店铺只取其中一个
            var filterPlatforms = new List<string>() { PlatformType.Offline.ToString(), PlatformType.Virtual.ToString(), PlatformType.System.ToString() };
            shops = shops.Where(s => s.Status == FxUserShopStatus.Binded && !s.IsFxListingApp && !filterPlatforms.Contains(s.PlatformType)).ToList();
            shops = shops.GroupBy(s => s.ShopId).Select(g => g.OrderByDescending(gs => gs.AuthTime).FirstOrDefault()).OrderByDescending(s => s.CreateTime).ToList();


            #region 全局搜搜增加跨境店铺
            var _reqModel = new FxUserShopQueryModel { FxUserId = SiteContext.Current.CurrentFxUserId };
            List<FxUserShop> fxUserShops = new List<FxUserShop>();

            if (new CommonSettingService().IsShowCrossBorder())
                fxUserShops = _fxUserForeignShopService.GetList(_reqModel)?.Item2?.Select(f => f as FxUserShop)?.ToList() ?? new List<FxUserShop>();
            if (fxUserShops != null && fxUserShops.Any())
                shops.AddRange(fxUserShops);
            #endregion
            return SuccessResult(shops.Select(s => new { Id = s.ShopId, s.NickName, s.PlatformType, IsAuthExpired = false, IsServiceEnd = false }));
        }

        public ActionResult LoadShop()
        {
            var fxuserId = SiteContext.Current.CurrentFxUserId;
            var agentShops = _fxUserShopService.GetShopsByFxUserId(fxuserId, false);
            var shopLimit = new CommonSettingService().Get("/ErpWeb/BindShopLimit", 0)?.Value.ToInt() ?? 1000;
            return SuccessResult(new { shops = agentShops, limt = shopLimit });
        }

        private ActionResult AuthOtherPlatform(Shop _shop)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            //新增店铺
            var token = new AlibabaMemberToken()
            {
                MemberId = _shop.AppKey,
                Resource_Owner = _shop.ShopName
            };
            var shopId = _shop.Id;
            if (_shop.Id == 0)
            {
                //添加店铺
                var newShop = _shopService.AddShop(token, _shop.PlatformType);
                shopId = newShop.Id;
                //添加店铺扩展
                _shopExtensionService.AddShopExtension(new ShopExtension()
                {
                    ShopId = shopId,
                    AppKey = _shop.AppKey,
                    AppSecret = _shop.AppSecret,
                    CreateTime = DateTime.Now,
                    ExpireTime = DateTime.Now.AddDays(7),
                    LastRefreshTokenTime = DateTime.Now
                });
                //添加绑定关系
                _fxUserShopService.AddFxUserShop(new FxUserShop()
                {
                    FxUserId = fxUserId,
                    ShopId = shopId,
                    PlatformType = _shop.PlatformType,
                    NickName = _shop.NickName,
                    CreateTime = DateTime.Now,
                    AuthTime = _shop.ExpireTime ?? DateTime.Now,
                    Status = FxUserShopStatus.Binded
                });
            }
            else
            {
                var shop = _shopService.Get(shopId);
                if (shop == null)
                    return FalidResult("店铺信息未找到");
                //更新店铺appSecret
                _shopExtensionService.UpdateAppSecret(shopId, _shop.AppKey, _shop.AppSecret);
                if (shop.ShopName != _shop.ShopName)
                {
                    //更新店铺名称
                    _shopService.UpdateShopName(shopId, _shop.ShopName);
                    _fxUserShopService.UpdateNickName(new Dictionary<int, string>()
                    {
                        {shopId,_shop.ShopName } }
                    );
                }
            }

            //订单同步状态
            _syncStatusService.AddSyncStatus(new SyncStatus()
            {
                FxUserId = fxUserId,
                ShopId = shopId,
                SyncType = ShopSyncType.Order,
                CreateTime = DateTime.Now,
                Source = OwnerSource.FenDanSystem.ToString(),
            });
            //订单同步任务
            _syncTaskService.AddTask(new SyncTask()
            {
                FxUserId = fxUserId,
                ShopId = shopId,
                TaskType = SyncTaskType.Order,
                Status = SyncTaskStatus.Wait,
                CreateTime = DateTime.Now,
                Source = OwnerSource.FenDanSystem.ToString(),
            });
            //产品同步状态
            _syncStatusService.AddSyncStatus(new SyncStatus()
            {
                FxUserId = fxUserId,
                ShopId = shopId,
                SyncType = ShopSyncType.Product,
                CreateTime = DateTime.Now,
                Source = OwnerSource.FenDanSystem.ToString(),
            });
            //售后同步状态
            _syncStatusService.AddSyncStatus(new SyncStatus()
            {
                FxUserId = fxUserId,
                ShopId = shopId,
                SyncType = ShopSyncType.AfterSale,
                CreateTime = DateTime.Now,
                Source = OwnerSource.FenDanSystem.ToString(),
            });
            //同步售后任务
            _syncTaskService.AddTask(new SyncTask()
            {
                FxUserId = fxUserId,
                ShopId = shopId,
                TaskType = SyncTaskType.AfterSale,
                Status = SyncTaskStatus.Wait,
                CreateTime = DateTime.Now,
                Source = OwnerSource.FenDanSystem.ToString(),
            });
            return SuccessResult("成功");
        }

        [LogForOperatorFilter("添加其他平台店铺")]
        [FxMigrateLockFilter()]
        public ActionResult AddShopByOtherPlatforms(string shopName, string appId, string appSecret, PlatformType pt, bool isBind)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;

            //1.判断是否被绑定
            var fxUserShop = _fxUserShopService.GetFxUserShop(appId, pt.ToString2());
            if (fxUserShop != null && fxUserShop.FxUserId != fxUserId)
            {
                var showName = string.IsNullOrWhiteSpace(fxUserShop.FxUserName) ? fxUserShop.FxUserMobile : fxUserShop.FxUserName;
                return FalidResult($"店铺已经被商家【{showName}】关联");
            }
            if (isBind && fxUserShop != null)
            {
                return FalidResult($"店铺已授权，请勿重复授权");
            }
            var checkResult = _fxUserShopService.IsFxUserIsHaveAvaliableCount(fxUserId, ServiceVersionTypeEnum.ShopUnit);
            if (checkResult.IsAvailable == false)
                return FalidResult($"您当前版本的绑定店铺数量已达上限 （{checkResult.AvailableCount}）个，请联系客服升级套餐后继续使用！", "NEED_UPGRADE_SHOP_COUNT", checkResult);
            var shop = new Shop()
            {
                Id = fxUserShop == null ? 0 : fxUserShop.ShopId,
                ShopName = shopName,
                NickName = shopName,
                AppKey = appId,
                AppSecret = appSecret,
                PlatformType = pt.ToString2(),
                ShopExtension = new ShopExtension()
                {
                    AppKey = appId,
                    AppSecret = appSecret
                }
            };
            //只修改店铺名称
            if (string.IsNullOrWhiteSpace(appSecret) && shop.Id > 0)
            {
                //更新店铺名称
                _shopService.UpdateShopName(shop.Id, shopName);
                _fxUserShopService.UpdateNickName(new Dictionary<int, string>()
                    {
                        {shop.Id, shopName } }
                );
                return SuccessResult("成功");
            }
            //校验是否可以授权成功
            var service = PlatformFactory.GetPlatformService(shop);
            if (!service.Ping())
            {
                return FalidResult($"授权失败，请重新检查填写内容是否正确！");
            }
            if (pt == PlatformType.Other_JuHaoMai || pt == PlatformType.Other_HaoYouDuo)
            {
                //聚好麦通过接口获取店铺名称
                var ptShop = service.SyncShopInfo();
                shop.ShopName = ptShop.ShopName;
                shop.NickName = ptShop.ShopName;
            }
            return AuthOtherPlatform(shop);
        }

        /// <summary>
        /// 根据平台,平台店铺Id绑定店铺
        /// </summary>
        /// <param name="pt"></param>
        /// <param name="shopId"></param>
        /// <param name="isNewWxShop">是否新应用</param>
        /// <returns></returns>
        [LogForOperatorFilter("添加店铺")]
        [FxMigrateLockFilter()]
        public ActionResult AddShop(string shopId, PlatformType pt, bool isNewWxShop = false)
        {
            if (pt == PlatformType.OwnShop)
            {
                return AddOwnShop(shopId);
            }
            var fxuserId = SiteContext.Current.CurrentFxUserId;
            Shop shop;
            if (pt == PlatformType.WxXiaoShangDian || pt == PlatformType.WxVideo)
            {
                string appKeyStr = CustomerConfig.Fx_WxXiaoShangDianAppId;
                if (IsNewCorpReview) appKeyStr = CustomerConfig.Fx_WxComponentNewAppId;
                if (isNewWxShop) appKeyStr = CustomerConfig.Fx_WxShopNewAppId;

                shop = _shopService.GetShopAndShopExtension(shopId, pt.ToString(), appKeyStr);
            }
            else shop = _shopService.GetShopAndShopExtension(shopId, pt.ToString());

            //只有分单应用和打单应用分开的平台，ShopExtension才会有数据
            var fendanAppAlonePts = (new CommonSettingService()).Get<List<string>>("/System/Setting/FenDan/FenDanAppAlonePt", 0);
            if (fendanAppAlonePts != null && fendanAppAlonePts.Contains(pt.ToString()))
            {
                if (shop?.ShopExtension == null)
                {
                    //检查订购记录有没有
                    if (pt == PlatformType.WxXiaoShangDian || pt == PlatformType.WxVideo)
                    {
                        var type = isNewWxShop ? PlatformAppTypeEnum.FenDanLatest : PlatformAppTypeEnum.FenDanNew;
                        var payMsg = new WxAppIdTokenService().GetModel(shopId, type.ToInt());
                        if (payMsg == null && isNewWxShop == false)
                            payMsg = new WxAppIdTokenService().GetModel(shopId, PlatformAppTypeEnum.FenDan.ToInt());
                        if (payMsg == null)
                            return FalidResult("请前往应用市场购买【分销代发】服务", "NOTPAY");

                        bool isVideo = false; //有记录开始初始化店铺
                        bool isNewCorp = false; //是否新主体
                        string appKeyStr = CustomerConfig.Fx_WxXiaoShangDianAppId;
                        string appSecretStr = CustomerConfig.Fx_WxXiaoShangDianAppSecret;
                        //1.区分出小商店/视频号
                        if (string.IsNullOrWhiteSpace(payMsg.Extend3) == false)
                        {
                            //旧主体分单服务
                            if (payMsg.Extend3.Contains("2548661915485487104"))
                            {
                                isVideo = true;
                            }
                            //新主体分单 测试服务2927990604503072768，正式服务2928429036274614274
                            else if (payMsg.Extend3.Contains("2927990604503072768") || payMsg.Extend3.Contains("2928429036274614274"))
                            {
                                isVideo = true;
                                isNewCorp = true;
                                appKeyStr = CustomerConfig.Fx_WxComponentNewAppId;
                                appSecretStr = CustomerConfig.Fx_WxComponentNewAppSecret;
                            }
                            // 新主体新应用，测试服务3695130241828765701，正式服务暂无
                            else if (payMsg.Extend3.Contains("3695130241828765701"))
                            {
                                isVideo = true;
                                isNewCorp = true;
                                appKeyStr = CustomerConfig.Fx_WxShopNewAppId;
                                appSecretStr = CustomerConfig.Fx_WxShopNewAppSecret;
                            }
                        }
                        else
                        {
                            return FalidResult("已经购买当前系统并没有点“去使用”", "NOTAUTH");
                        }
                        //消息来源是那个平台
                        PlatformType msgPt = isVideo ? PlatformType.WxVideo : PlatformType.WxXiaoShangDian;

                        //初始化店铺
                        var newShop = TryCreateShopInit(() =>
                        {
                            var testShop = new Shop
                            {
                                ShopId = payMsg.AppId,
                                NickName = payMsg.AppId,
                                ShopName = payMsg.AppId,
                                AccessToken = payMsg.AccessToken,
                                RefreshToken = payMsg.AppSecret,
                                PlatformType = msgPt.ToString(),
                                AuthTime = DateTime.Now,
                                CreateTime = DateTime.Now,
                                VenderId = isNewCorp ? "V2" : null,
                                ShopExtension = new ShopExtension()
                                {
                                    AppKey = appKeyStr,
                                    AppSecret = appSecretStr,
                                    AccessToken = payMsg.AccessToken,
                                    RefreshToken = payMsg.AppSecret,
                                    LastRefreshTokenTime = DateTime.Now
                                }
                            };
                            return testShop;
                        }, msgPt);

                        //再查一次数据库 获取最新的店铺信息
                        shop = _shopService.GetShopAndShopExtension(newShop.Id, appKeyStr);

                        //上面的逻辑都失败了, 还是让用户手动点“去使用”初始化
                        if (shop == null || shop?.ShopExtension == null)
                            return FalidResult("初始化失败，请前往服务市场手动点击【去使用】，再回到系统绑定店铺", "NOTPAY");
                    }
                    else
                    {
                        return FalidResult("请前往应用市场购买【分销代发】服务", "NOTPAY");
                    }
                }
            }
            else
            {
                if (shop == null)
                {
                    return FalidResult("店铺未授权", "NOTAUTH");
                }
            }

            //1.判断是否被绑定
            var shoplist = _fxUserShopService.GetList(shopId: shop.Id);
            if (shoplist.Any(x => x.FxUserId != fxuserId))
            {
                var fxshop = shoplist.FirstOrDefault();
                var showName = string.IsNullOrWhiteSpace(fxshop.FxUserName) ? fxshop.FxUserMobile : fxshop.FxUserName;
                // 该店铺是否存在45天内的底单记录
                if (pt == PlatformType.WxVideo)
                {
                    // 该店铺是否存在45天内的底单记录，仅处理视频号平台
                    var isExist = new WaybillCodeService().IsExistWaybillCodeByShopId(fxshop.ShopId, CloudPlatformType.Alibaba.ToString());
                    var errorCode = !isExist ? "CAN_UNBIND" : "NOT_UNBIND";
                    // 立即绑定才需要设置Redis
                    if (!isExist)
                    {
                        // 新增Redis，防止越权
                        var redisKeyByShop = $"DianGuanJiaApp:FenDan:AuthFailed:{fxuserId}:{fxshop.ShopId}";
                        RedisHelper.Set(redisKeyByShop, true, 60 * 5);
                    }
                    return FalidResult($"{showName}", errorCode, fxshop.ShopId); 
                }
                
                return FalidResult($"店铺已经被商家【{showName}】关联");
            }
            //if (shoplist.Any(x => x.FxUserId == fxuserId))
            //{
            //    return FalidResult($"您已经绑定了该店铺，不用重复绑定。");
            //}
            var checkResult = _fxUserShopService.IsFxUserIsHaveAvaliableCount(fxuserId, ServiceVersionTypeEnum.ShopUnit);
            if (checkResult.IsAvailable == false)
                return FalidResult($"您当前版本的绑定店铺数量已达上限 （{checkResult.AvailableCount}）个，请联系客服升级套餐后继续使用！", "NEED_UPGRADE_SHOP_COUNT", checkResult);
            try
            {
                //3.绑定关系
                _fxUserShopService.AddFxUserShop(new FxUserShop()
                {
                    FxUserId = fxuserId,
                    ShopId = shop.Id,
                    PlatformType = shop.PlatformType,
                    NickName = shop.NickName,
                    CreateTime = DateTime.Now,
                    AuthTime = shop.ExpireTime ?? DateTime.Now,
                    Status = FxUserShopStatus.Binded
                });

                //4.保存店铺同步状态数据
                //订单同步状态
                _syncStatusService.AddSyncStatus(new SyncStatus()
                {
                    FxUserId = fxuserId,
                    ShopId = shop.Id,
                    SyncType = ShopSyncType.Order,
                    CreateTime = DateTime.Now,
                    Source = OwnerSource.FenDanSystem.ToString()
                });

                //产品同步状态
                _syncStatusService.AddSyncStatus(new SyncStatus()
                {
                    FxUserId = fxuserId,
                    ShopId = shop.Id,
                    SyncType = ShopSyncType.Product,
                    CreateTime = DateTime.Now,
                    Source = OwnerSource.FenDanSystem.ToString()
                });

                //5.同步任务
                _syncTaskService.AddTask(new SyncTask()
                {
                    FxUserId = fxuserId,
                    ShopId = shop.Id,
                    TaskType = SyncTaskType.Order,
                    Status = SyncTaskStatus.Wait,
                    CreateTime = DateTime.Now,
                    Source = OwnerSource.FenDanSystem.ToString()
                });


                //6.1售后同步状态
                _syncStatusService.AddSyncStatus(new SyncStatus()
                {
                    FxUserId = fxuserId,
                    ShopId = shop.Id,
                    SyncType = ShopSyncType.AfterSale,
                    CreateTime = DateTime.Now,
                    Source = OwnerSource.FenDanSystem.ToString()
                });

                //6.2售后同步任务
                _syncTaskService.AddTask(new SyncTask()
                {
                    FxUserId = fxuserId,
                    ShopId = shop.Id,
                    TaskType = SyncTaskType.AfterSale,
                    Status = SyncTaskStatus.Wait,
                    CreateTime = DateTime.Now,
                    Source = OwnerSource.FenDanSystem.ToString()
                });

                if (shop.PlatformType == PlatformType.WxVideo.ToString())
                {
                    //把当前店铺当作电子面单账号
                    new CainiaoAuthOwnerService().IfShopIsSupportWaybillThenSave(shop, SiteContext.Current.CurrentLoginShop.Id, shop?.ShopExtension?.AppKey);
                }

                return SuccessResult("成功");
            }
            catch (Exception ex)
            {
                Log.WriteError($"{pt.GetEnumDescription()}店铺ID：{shopId},和商家Id：{fxuserId},绑定关系失败，错误消息：{ex}");
                return FalidResult("添加店铺失败，请联系客服");
            }
        }

        /// <summary>
        /// 添加自有店铺
        /// </summary>
        /// <param name="shopName"></param>
        /// <returns></returns>
        private ActionResult AddOwnShop(string shopName)
        {
            var fxuserId = SiteContext.Current.CurrentFxUserId;
            var shopId = (shopName + fxuserId).ToShortMd5();
            //1.判断是否被绑定
            var shop = _shopService.GetByShopId(shopId, PlatformType.OwnShop.ToString());
            if (shop != null)
            {
                return FalidResult($"已存在店铺【{shopName}】");
            }
            var checkResult = _fxUserShopService.IsFxUserIsHaveAvaliableCount(fxuserId, ServiceVersionTypeEnum.ShopUnit);
            if (checkResult.IsAvailable == false)
                return FalidResult($"您当前版本的绑定店铺数量已达上限 （{checkResult.AvailableCount}）个，请联系客服升级套餐后继续使用！", "NEED_UPGRADE_SHOP_COUNT", checkResult);
            try
            {
                var token = new AlibabaMemberToken()
                {
                    MemberId = shopId,
                    Resource_Owner = shopName,
                };
                //添加店铺
                shop = _shopService.AddShop(token, PlatformType.OwnShop);
                //添加店铺扩展
                _shopExtensionService.AddShopExtension(new ShopExtension()
                {
                    ShopId = shop.Id,
                    AppKey = string.Empty,
                    AppSecret = string.Empty,
                    CreateTime = DateTime.Now,
                    LastRefreshTokenTime = DateTime.Now
                });
                //添加绑定关系
                _fxUserShopService.AddFxUserShop(new FxUserShop()
                {
                    FxUserId = fxuserId,
                    ShopId = shop.Id,
                    PlatformType = shop.PlatformType,
                    NickName = shop.NickName,
                    CreateTime = DateTime.Now,
                    AuthTime = shop.ExpireTime ?? DateTime.Now,
                    Status = FxUserShopStatus.Binded
                });
                //添加同步记录
                _syncStatusService.AddSyncStatus(new SyncStatus()
                {
                    FxUserId = fxuserId,
                    ShopId = shop.Id,
                    SyncType = ShopSyncType.Order,
                    CreateTime = DateTime.Now,
                    Source = OwnerSource.FenDanSystem.ToString(),
                    LastSyncStatus = "Finished",
                    FullSyncStatus = "Finished",
                    LastSyncTime = DateTime.Now,
                    LastSyncMessage = "同步完成",
                    FullSyncCompleteTime = DateTime.Now,
                });
                return SuccessResult("成功");
            }
            catch (Exception ex)
            {
                Log.WriteError($"添加自有商城失败:{shopName}，错误消息：{ex}");
                return FalidResult("添加店铺失败，请联系客服");
            }
        }

        private Shop TryCreateShopInit(Func<Shop> func, PlatformType pt)
        {
            Shop _shop = null;
            try
            {
                var oldshop = func();
                var platformService = PlatformFactory.GetPlatformService(oldshop);
                var shop = platformService.SyncShopInfo();
                //保存店铺
                _shop = _shopService.AddShop(new AlibabaMemberToken()
                {
                    Access_Token = shop.AccessToken,
                    Refresh_Token = shop.RefreshToken,
                    Resource_Owner = shop.NickName,
                    MemberId = shop.ShopId,
                    UserId = shop.ShopName,
                }, pt, isFxAppAuth: true);

                _shopExtensionService.AddShopExtension(new ShopExtension()
                {
                    ShopId = _shop.Id,
                    AppKey = oldshop.ShopExtension.AppKey,
                    AppSecret = oldshop.ShopExtension.AppSecret,
                    SignSecret = oldshop.ShopExtension.SignSecret,
                    AccessToken = oldshop.ShopExtension.AccessToken,
                    RefreshToken = oldshop.ShopExtension.RefreshToken,
                    LastRefreshTokenTime = oldshop.ShopExtension.LastRefreshTokenTime,
                    Source = OwnerSource.FenDanSystem.ToString(),
                });
            }
            catch (Exception ex)
            {
                Log.WriteError($"TryCreateShopInit 创建店铺失败：{ex}");
            }
            return _shop;
        }

        public ActionResult LoadMySupplierQrCode()
        {
            var exceedTime = DateTime.Now.AddDays(1);//这个过期时间后续可做配置


            var code = GetQrCode("MySupplier", exceedTime);

            return SuccessResult(new { QrCode = code, ExceedTime = exceedTime });
        }

        /// <summary>
        /// 是否存在指定平台的店铺
        /// </summary>
        /// <returns></returns>
        public ActionResult HasShop(string pt)
        {
            var fxuserId = SiteContext.Current.CurrentFxUserId;
            var result = _fxUserShopService.IsHasShop(fxuserId, new List<string>() { pt });
            return SuccessResult(result);
        }
        #endregion

        #region 我的厂家
        public ActionResult MySupplier()
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var supplierUsers = _supplierUserService.GetSupplierUserNames(fxUserId);
            var suppliers = supplierUsers.Select(x => new { FxUserId = x.Key, UserName = x.Value, Status = 1 }).ToList();


            #region 不显示非合作关系
            var shopId = SiteContext.Current.CurrentShopId;
            var keyOrderDisplaySetting = CommonSettingService.OrderDisplaySetting;
            List<string> keys = new List<string>() {
                 "/FenFa/System/Config/IsShowCancelUser",
                 "/FenFa/System/Config/IsSalePricePublic",
                 "/FenFa/System/Config/IsAgentSendAddress",
                "/FenFa/System/Config/IsShopNamePublic",
                "/FenFa/System/Config/StockOutType",
                "/FenFa/System/Config/StockOutZHType",
                "/ErpWeb/OutAccount/Agent/UnSetPriceDailog",
                "/ErpWeb/OutAccount/Supplier/UnSetPriceDailog",
               keyOrderDisplaySetting
            };
            var result = _commonSettingService.GetSets(keys, shopId);

            //没有设置则用默认
            if (result.Any(x => x.Key == "/FenFa/System/Config/IsAgentSendAddress") == false)
            {
                var defaultIsAgentSendAddress = _commonSettingService.Get("/FenFa/System/Config/IsAgentSendAddress", 0);
                if (defaultIsAgentSendAddress != null)
                    result.Add(defaultIsAgentSendAddress);
            }
            if (result.Any(x => x.Key == "/ErpWeb/OutAccount/Agent/UnSetPriceDailog") == false)
            {
                result.Add(new CommonSetting { Key = "/ErpWeb/OutAccount/Agent/UnSetPriceDailog", ShopId = shopId, Value = "true" });
            }
            else
            {
                var date = result.First(x => x.Key == "/ErpWeb/OutAccount/Agent/UnSetPriceDailog").Value.toDateTime();
                if (date.AddDays(15) < DateTime.Now)
                    result.First(x => x.Key == "/ErpWeb/OutAccount/Agent/UnSetPriceDailog").Value = "true";
                else
                    result.First(x => x.Key == "/ErpWeb/OutAccount/Agent/UnSetPriceDailog").Value = "false";
            }
            if (result.Any(x => x.Key == "/ErpWeb/OutAccount/Supplier/UnSetPriceDailog") == false)
            {
                result.Add(new CommonSetting { Key = "/ErpWeb/OutAccount/Supplier/UnSetPriceDailog", ShopId = shopId, Value = "true" });
            }
            else
            {
                var date = result.First(x => x.Key == "/ErpWeb/OutAccount/Supplier/UnSetPriceDailog").Value.toDateTime();
                if (date.AddDays(15) < DateTime.Now)
                    result.First(x => x.Key == "/ErpWeb/OutAccount/Supplier/UnSetPriceDailog").Value = "true";
                else
                    result.First(x => x.Key == "/ErpWeb/OutAccount/Supplier/UnSetPriceDailog").Value = "false";
            }

            if (result.Any(x => x.Key == keyOrderDisplaySetting) == false)
            {
                //设置默认值
                result.Add(new CommonSetting { Key = keyOrderDisplaySetting, ShopId = shopId, Value = CommonSettingService.DefaultOrderDisplaySetting.ToJson() });
            }
            #endregion

            ViewBag.CommonSettings = result;

            Dictionary<int, string> newStatusDic = new Dictionary<int, string>();
            foreach (var item in Enum.GetValues(typeof(AgentBingSupplierStatus)))
            {
                newStatusDic.Add((int)item, ((AgentBingSupplierStatus)item).GetEnumDescription());
            }
            ViewBag.SupplierUsers = suppliers.ToJson();
            ViewBag.Status = newStatusDic;
            FxUserAddress defaultAddres = _fxUserAddressService.GetByFxUserId(SiteContext.Current.CurrentFxUserId); //缓存性能优化:默认不加载地址数据
            ViewBag.FxUserAddres = defaultAddres != null ? defaultAddres.ToJson() : (new FxUserAddress()).ToJson();
            var checkResult = _fxUserShopService.IsFxUserIsHaveAvaliableCount(fxUserId, ServiceVersionTypeEnum.FactoryUnit);
            ViewBag.AvailableCountInfo = checkResult.ToJson();
            ViewBag.IsAddAvailable = checkResult.IsAvailable;

            ViewBag.HideCancelSupplier = _commonSettingService.GetString("/ErpWeb/MySupplier/HideCancelSupplier", SiteContext.Current.CurrentShopId);

            // 页面按钮展示权限
            ViewBag.ShowPermDict = new Dictionary<string, bool>
            {
                {$"#{nameof(FxPermission.AddBindSupplier)}",SiteContext.HasPermission(FxPermission.AddBindSupplier)},
                {$"#{nameof(FxPermission.SupplierInvite)}",SiteContext.HasPermission(FxPermission.SupplierInvite)},
                {$"#{nameof(FxPermission.AddVirtualSupplier)}",SiteContext.HasPermission(FxPermission.AddVirtualSupplier)},
                {$".{nameof(FxPermission.UnbindSupplier)}",SiteContext.HasPermission(FxPermission.UnbindSupplier)},
                {$".{nameof(FxPermission.AgreeAndRefuseSupplier)}",SiteContext.HasPermission(FxPermission.AgreeAndRefuseSupplier)},
                {$".{nameof(FxPermission.ApplyPrepay)}",SiteContext.HasPermission(FxPermission.ApplyPrepay)},
            }.ToJson();

            return View();
        }

        public ActionResult LoadMySupplierList(string key, AgentBingSupplierStatus? status, int pageIndex, int pageSize)
        {
            int fxUserId = SiteContext.Current.CurrentFxUserId;
            var result = _supplierUserService.GetSupplierList(fxUserId, key, status, pageIndex, pageSize, needEncryptAccount: true);

            #region 担保交易厂家白名单
            var supplierWhitekey = "/ErpWeb/1688Supply/CanApplyPrepay/Supplier/FxUserIds";
            var supplierWhiteSetting = _commonSettingService.GetString(supplierWhitekey, 0, false);
            var supplierWhiteIds = new List<int>();
            if (!supplierWhiteSetting.IsNullOrEmpty())
            {
                var ids = supplierWhiteSetting.Split(',');
                foreach (var id in ids)
                {
                    int parsedNumber;
                    bool success = int.TryParse(id, out parsedNumber);
                    if (success)
                        supplierWhiteIds.Add(parsedNumber);
                }
            }
            result.Item2.ForEach(item =>
            {
                if (supplierWhiteIds.Any(p => p == item.SupplierFxUserId))
                    item.IsSupportApplyPrepay = true;
            });
            #endregion

            return SuccessResult(new { Total = result.Item1, List = result.Item2 });
        }

        /// <summary>
        /// 编辑
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        //public ActionResult GetBind(int id)
        //{
        //    if (id <= 0)
        //        return FalidResult("唯一标识不能为空");

        //    var result = _supplierUserService.GetSupplierBindInfo(id);
        //    if (result == null)
        //        return FalidResult("未找到绑定关系");
        //    return SuccessResult(result);
        //}

        /// <summary>
        /// 绑定厂家申请
        /// </summary>
        /// <param name="_model"></param>
        /// <returns></returns>
        [LogForOperatorFilter("绑定厂家")]
        [FxMigrateLockFilter()]
        [FxAuthorize]
        public ActionResult AddBindSupplier(SupplierUser _model)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            _model.FxUserId = fxUserId;
            _model.CreateBy = fxUserId;
            _model.From = "PC";
            if (_model.SupplierFxUserId <= 0)
                return FalidResult("请输入正确的厂家账号");

            if (_model.Province == "0")
                _model.Province = null;
            if (_model.City == "0")
                _model.City = null;
            if (_model.District == "0")
                _model.District = null;
            //2021-10-27,项颖说隐藏。和二维码扫描绑定保持一致。没有代发地址
            //if (string.IsNullOrEmpty(_model.SenderName))
            //    return FalidResult("请输入发件人姓名");
            //if (string.IsNullOrEmpty(_model.SenderMobile) && string.IsNullOrEmpty(_model.SenderTelePhone))
            //    return FalidResult("联系电话、联系固话至少填写一个");
            //if (string.IsNullOrEmpty(_model.Province) || string.IsNullOrEmpty(_model.City) || string.IsNullOrEmpty(_model.District))
            //    return FalidResult("请选择发件人省市区");
            //if (string.IsNullOrEmpty(_model.Address))
            //    return FalidResult("请输入发件人详细地址");

            // 后端再次校验厂家是否可以绑定
            if (fxUserId == _model.SupplierFxUserId)
                return FalidResult("不能绑定自己");
            var supplier = _userFxService.GetByNamebeltStatus(fxUserId, _model.SupplierFxUserId.ToString(), isMobile: false);
            if (supplier == null || supplier.Id == 0)
                return FalidResult("厂家未找到");
            else if (supplier.Status == 4)
                return FalidResult("厂家已经被绑定");
            else if (supplier.Status == 5)
                return FalidResult("不能绑定自己");
            else if (supplier.Status == 6 && supplier.Status2 != 4) //已经被绑定且关系不是已取消
                return FalidResult("已经绑定");

            // l688新厂家【1688new】：商家默认都开启预付
            // 20241220 调整为：1688 新店铺或新注册用户，将不会默认对下游开启使用担保交易流程
            var currentSupplierUser = _userFxService.GetUserFx(_model.SupplierFxUserId);
            Log.Debug($"绑定厂家1：{_model.ToJson()},UserFlag:{currentSupplierUser?.UserFlag}", "AddBindSupplierDebug.txt");
            if (currentSupplierUser != null && false && currentSupplierUser.UserFlag.ToString2().Contains("1688new"))
            {
                //厂家是否开通轻应用
                var qingService = new QingService(_model.SupplierFxUserId);
                if (qingService.IsOpenQing)
                {

                    //厂家是否有1688店铺
                    var key = BusinessSettingKeys.SupplyBy1688.ShopBy1688;
                    var systemShopId = _fxUserShopService.GetFxUserIdMapping(new List<int> { _model.SupplierFxUserId })?.FirstOrDefault()?.ShopId ?? 0;
                    var shopId = new BusinessSettingsService().GetsByShopId(systemShopId, new List<string> { key }).FirstOrDefault()?.Value.ToInt() ?? 0;
                    Log.Debug($"绑定厂家2：开通了轻应用，{key},{systemShopId},{shopId}", "AddBindSupplierDebug.txt");

                    if (shopId > 0)
                    {
                        _model.IsPrePay = true;
                        _model.ApplyPrepayStatus = -10;
                        _model.LastOpenPrepayTime = DateTime.Now;
                    }
                }
            }

            var log = LogForOperatorContext.Current.logInfo;
            var subLog = new LogForOperator() { OperatorType = "检查是否有足够的可用数量" };
            LogForOperatorContext.Current.StartStep(subLog);
            var checkAvaliableCountResult = _fxUserShopService.IsFxUserIsHaveAvaliableCount(fxUserId, ServiceVersionTypeEnum.FactoryUnit);
            subLog.Detail = checkAvaliableCountResult;
            LogForOperatorContext.Current.EndStep(subLog);
            if (checkAvaliableCountResult.IsAvailable == false)
                return FalidResult("绑定供应商失败，您的供应商数量已经超出限额了，请联系我们升级版本", "NEED_UPGRADE_FACTORY_COUNT", checkAvaliableCountResult);
            var op = OptimisticLockOperationType.FxAddSupplier;
            var opId = $"FX{fxUserId}";
            var result = 0;
            try
            {
                var _commonSettingService = new CommonSettingService();
                var r = _commonSettingService.ExecuteWithOptimisticLock(() =>
                {
                    try
                    {
                        if (supplier.Status == 6)
                        {
                            _model.Status = AgentBingSupplierStatus.UnBindFail;  //绑定申请中
                            _model.Id = supplier.SupplierId;
                            _model.CreateTime = DateTime.Now;
                        }
                        result = _supplierUserService.Add(_model);
                        // 同步货盘
                        _supplierUserService.TriggerSyncSupplierUser(_model);

                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"添加厂家时发生异常，用户ID:{fxUserId}，异常信息：{ex}");
                    }
                    return true;

                }, null, op, opId);
            }
            catch (Exception ex)
            {
                var errMsg = $"添加厂家时发生异常，用户ID:{fxUserId}，异常信息：{ex}，请求参数：{(_model?.ToJson() ?? "null")}";
                log.Exception = errMsg;
                Log.WriteError(errMsg);
            }

            if (result > 0)
            {
                int? subFxUserId = null;
                if (SiteContext.IsSubAccount())
                {
                    subFxUserId = SiteContext.Current.SubFxUserId;
                }
                _cooperateStatusRecordService.AddCooperateStatus(fxUserId, supplier.SupplierId, fxUserId, AgentBingSupplierStatus.UnBind, subFxUserId);
                #region 消息埋点

                // 场景：商家对厂家发起合作申请
                new SiteMessageService().ConvertToMessage(new SiteMessageContentModel
                {
                    FirstType = SiteMessageTypeEnum.PrimaryType.Cooperation.ToString(),
                    SecondType = SiteMessageTypeEnum.PrimaryType.Cooperation.ToString(),
                    FromUserId = fxUserId,
                    ToUserIds = new List<int> { _model.SupplierFxUserId },
                    JumpUrl = "/Partner/MyAgent",
                    Content = "用户[商家id]申请与您合作，成为您的下游商家。",
                    TemplateParas = new { 商家id = fxUserId.ToString() }.ToJsonExt()
                });

                #endregion

                return SuccessResult();
            }
            return FalidResult("绑定失败");
        }

        /// <summary>
        /// 绑定厂家申请
        /// </summary>
        /// <param name="_model"></param>
        /// <returns></returns>
        [FxMigrateLockFilter()]
        [FxAuthorize]
        public ActionResult AddVirtualSupplier(SupplierUser _model)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            _model.CreateTime = DateTime.Now;
            _model.FxUserId = fxUserId;
            _model.CreateBy = fxUserId;
            _model.Status = AgentBingSupplierStatus.Binded;  //虚拟厂家直接绑定成功
            _model.From = "PC";
            _model.SupplierType = "Virtual";
            if (string.IsNullOrEmpty(_model.NickName?.Trim()))
                return FalidResult("请填写虚拟厂家名称");
            // 长度是否超过128个字符
            if (_model.NickName.Length > 128)
                return FalidResult("虚拟厂家名称过长");

            //创建虚拟厂家
            var userFxService = new UserFxService();
            _model.SupplierFxUserId = userFxService.CreateVirtualFxUser(_model.NickName, fxUserId);
            if (_model.SupplierFxUserId <= 0)
                return FalidResult("新增虚拟厂家失败，请稍会儿再试");
            var result = _supplierUserService.Add(_model);
            // 同步货盘
            _supplierUserService.TriggerSyncSupplierUser(_model);
            if (result > 0)
            {
                return SuccessResult();
            }
            return FalidResult("绑定失败");
        }

        /// <summary>
        /// 编辑保存
        /// </summary>
        /// <param name="_model"></param>
        /// <returns></returns>
        [LogForOperatorFilter("更新厂家")]
        [FxMigrateLockFilter()]
        public ActionResult EditBindAddress(SupplierUser _model)
        {
            if (_model.Id <= 0)
                return FalidResult("您要编辑的厂家信息不存在或已被删除，请刷新重试");
            if (_model.SupplierFxUserId > 0)
                return FalidResult("厂家不能修改");
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var _bins = _supplierUserService.Get(_model.Id);
            if (_bins == null)
                return FalidResult("当前厂家信息不存在，请刷新重试");
            if (_bins.FxUserId != fxUserId)
                return FalidResult("当前厂家信息不存在，请刷新重试");
            //_model.Status = AgentBingSupplierStatus.Apply;  //绑定申请中
            if (_model.Status == AgentBingSupplierStatus.Apply)
            {
                _model.CreateBy = fxUserId;
            }
            else if (_model.Status == AgentBingSupplierStatus.Binded && _bins.Status != _model.Status)
            {
                var checkAvaliableCountResult = _fxUserShopService.IsFxUserIsHaveAvaliableCount(fxUserId, ServiceVersionTypeEnum.FactoryUnit);
                if (checkAvaliableCountResult.IsAvailable == false)
                    return FalidResult("绑定供应商失败，您的供应商数量已经超出限额了，请联系我们升级版本", "NEED_UPGRADE_FACTORY_COUNT", checkAvaliableCountResult);
            }
            //更新预付开启时间
            _model.OpenPrePayTime = _bins.OpenPrePayTime;
            if (_model.Status == AgentBingSupplierStatus.Binded && _bins.OpenPrePayTime.HasValue == false && _bins.IsPrePay.HasValue && _bins.IsPrePay.Value)
            {
                _model.OpenPrePayTime = DateTime.Now;

                //添加日志
                var model = new PrepayStatusChangeRecord()
                {
                    FxUserId = fxUserId,
                    SupplierFxUserId = _bins.SupplierFxUserId,
                    PrepayStatus = 1,
                    CreateFxUserId = fxUserId,
                    CreateTime = DateTime.Now,
                };
                new PrepayStatusChangeRecordService().Add(model);

                //更新商家是否显示1688菜单的缓存（预付状态更新触发点2）
                //_commonSettingService.UpdateIsShow1688MenuCache(fxUserId);
                FxCaching.ForeRefeshCache(FxCachingType.Show1688Menu, fxUserId.ToString2());

                //设置版本
                _shopService.SetAboutFx1688Version(fxUserId, SiteContext.Current.CurrentShopId);
            }
            var result = _supplierUserService.UpdateSendInfo(_model);

            // 同步货盘
            _bins.Remark = _model.Remark;
            _bins.RemarkName = _model.RemarkName;
            _bins.Status = _model.Status;
            _bins.MemberLevelCode = null;
            _supplierUserService.TriggerSyncSupplierUser(_bins);
            if (!result)
                return FalidResult("保存失败，请刷新重试");
            if (_model.Status == AgentBingSupplierStatus.Binded)
            {
                // 增加绑定日志
                _cooperateStatusRecordService.AddCooperateStatus(fxUserId, _bins.SupplierFxUserId, fxUserId, AgentBingSupplierStatus.Binded, SiteContext.GetSubFxUserId());
            }
            else
            {
                // 增加绑定日志
                _cooperateStatusRecordService.AddCooperateStatus(fxUserId, _bins.SupplierFxUserId, fxUserId, AgentBingSupplierStatus.UnBind, SiteContext.GetSubFxUserId());
            }

            #region 消息埋点

            // 场景：商家同意合作申请
            new SiteMessageService().ConvertToMessage(new SiteMessageContentModel
            {
                FirstType = SiteMessageTypeEnum.PrimaryType.Cooperation.ToString(),
                SecondType = SiteMessageTypeEnum.PrimaryType.Cooperation.ToString(),
                FromUserId = fxUserId,
                ToUserIds = new List<int> { _bins.SupplierFxUserId },
                JumpUrl = "/Partner/MySupplier",
                Content = "用户[厂家id]同意与您合作，成为您的上游厂家。",
                TemplateParas = new { 厂家id = fxUserId.ToString() }.ToJsonExt()
            });

            #endregion

            return SuccessResult("编辑成功");
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <param name="status"></param>
        /// <param name="type">0 绑定厂家 1绑定商家 2绑定厂家，没有关联商品的解除合作 4绑定商家，拒绝合作</param>
        /// <param name="from">鉴权时需要用到</param>
        /// <returns></returns>
        [LogForOperatorFilter("切换绑定状态")]
        [FxMigrateLockFilter()]
        [FxAuthorize(AuthorizeType.UpdatePartnerBindStatus)]
        public ActionResult UpdateSupplierBindStatus(int id, AgentBingSupplierStatus? status, int type, string from)
        {
            int userId = SiteContext.Current.CurrentFxUserId;
            if (status != null)
            {
                //var curUser = SiteContext.Current.CurrentFxUser;

                var _bins = _supplierUserService.Get(id);
                if (_bins == null)
                    return FalidResult("当前绑定信息不存在，请刷新重试");
                //if(_bins.SupplierFxUserId != userId)
                //    return FalidResult("当前绑定信息不存在，请刷新重试");
                //虚拟厂家绑定不需要确认，直接绑定或解绑
                if (_bins.SupplierType == "Virtual")
                {
                    switch (status)
                    {
                        case AgentBingSupplierStatus.UnBind:
                            _supplierUserService.Update(_bins.Id, AgentBingSupplierStatus.UnBind);
                            _supplierUserService.TriggerSyncSupplierUser(_bins, AgentBingSupplierStatus.UnBind);
                            _cooperateStatusRecordService.AddCooperateStatus(_bins.FxUserId, _bins.SupplierFxUserId, userId, AgentBingSupplierStatus.UnBind, SiteContext.GetSubFxUserId());
                            break;
                        case AgentBingSupplierStatus.Apply:
                            _supplierUserService.Update(_bins.Id, AgentBingSupplierStatus.Binded);
                            _supplierUserService.TriggerSyncSupplierUser(_bins, AgentBingSupplierStatus.Binded);
                            _cooperateStatusRecordService.AddCooperateStatus(_bins.FxUserId, _bins.SupplierFxUserId, userId, AgentBingSupplierStatus.Binded, SiteContext.GetSubFxUserId());
                            break;
                    }

                    return SuccessResult();
                }
                var supplierId = _bins.SupplierFxUserId;
                var fxUserId = _bins.FxUserId;
                var curLoginFxUserId = SiteContext.Current.CurrentFxUserId;
                var subFxUserId = SiteContext.GetSubFxUserId();
                var isAgent = supplierId == curLoginFxUserId;
                //验证版本数量，fxUserId == userId时为：我的供应商，验证供应商数量，否则为我的商家，验证商家数量
                if (status == AgentBingSupplierStatus.Apply || status == AgentBingSupplierStatus.Binded)
                {
                    if (fxUserId == userId)
                    {
                        //验证供应商数量
                        var checkAvaliableCountResult = _fxUserShopService.IsFxUserIsHaveAvaliableCount(curLoginFxUserId, ServiceVersionTypeEnum.FactoryUnit);
                        if (checkAvaliableCountResult.IsAvailable == false)
                            return FalidResult("绑定供应商失败，您的供应商数量已经超出限额了，请联系我们升级版本", "NEED_UPGRADE_FACTORY_COUNT", checkAvaliableCountResult);
                    }
                    else
                    {
                        //验证商家数量
                        var checkAvaliableCountResult = _fxUserShopService.IsFxUserIsHaveAvaliableCount(curLoginFxUserId, ServiceVersionTypeEnum.SellerUnit);
                        if (checkAvaliableCountResult.IsAvailable == false)
                            return FalidResult("绑定商家失败，您的商家数量已经超出限额了，请联系我们升级版本", "NEED_UPGRADE_SELLER_COUNT", checkAvaliableCountResult);
                    }
                }
                var model = new BindSupplierRequestModel { isSelf = false, Configs = new List<BindConfigModel>() };
                switch (status)
                {
                    case AgentBingSupplierStatus.Apply:
                        //1.A发起绑定B，申请发起人为A ，B拒绝，A重新绑定，只需修改状态为申请中。
                        //2.A发起绑定B，申请发起人为A ，B拒绝，B重新绑定，需修改状态为申请中，且修改申请发起人为B。
                        if (userId != _bins.CreateBy)
                        {
                            _bins.CreateBy = userId;
                        }
                        _bins.Status = AgentBingSupplierStatus.Apply;
                        _supplierUserService.Update(_bins);
                        _supplierUserService.TriggerSyncSupplierUser(_bins);
                        //开启预付，设置版本
                        if (_bins.IsPrePay.HasValue && _bins.IsPrePay.Value)
                        {
                            var agentId = _bins.FxUserId;
                            var agentSystemShopId = _fxUserShopService.GetFxUserIdMapping(new List<int> { agentId })?.FirstOrDefault()?.ShopId ?? 0;
                            _shopService.SetAboutFx1688Version(agentId, agentSystemShopId);
                        }

                        #region 消息埋点

                        // 场景：厂家/商家 对 商家/厂家 发起合作申请（重新绑定）
                        if (isAgent) // 当前用户身份为厂家
                        {
                            new SiteMessageService().ConvertToMessage(new SiteMessageContentModel
                            {
                                FirstType = SiteMessageTypeEnum.PrimaryType.Cooperation.ToString(),
                                SecondType = SiteMessageTypeEnum.PrimaryType.Cooperation.ToString(),
                                FromUserId = curLoginFxUserId,
                                ToUserIds = new List<int> { fxUserId },
                                JumpUrl = "/Partner/MySupplier",
                                Content = "用户[厂家id]申请与您合作，成为您的上游厂家。",
                                TemplateParas = new { 厂家id = curLoginFxUserId.ToString() }.ToJsonExt()
                            });
                        }
                        else // 当前用户身份为商家
                        {
                            new SiteMessageService().ConvertToMessage(new SiteMessageContentModel
                            {
                                FirstType = SiteMessageTypeEnum.PrimaryType.Cooperation.ToString(),
                                SecondType = SiteMessageTypeEnum.PrimaryType.Cooperation.ToString(),
                                FromUserId = curLoginFxUserId,
                                ToUserIds = new List<int> { supplierId },
                                JumpUrl = "/Partner/MyAgent",
                                Content = "用户[商家id]申请与您合作，成为您的下游商家。",
                                TemplateParas = new { 商家id = curLoginFxUserId.ToString() }.ToJsonExt()
                            });
                        }

                        #endregion
                        break;
                    case AgentBingSupplierStatus.Binded:
                        //厂家同意商家和自己建立关系
                        _supplierUserService.Update(_bins.Id, AgentBingSupplierStatus.Binded, true);
                        _bins.MemberLevelCode = null;
                        _supplierUserService.TriggerSyncSupplierUser(_bins, AgentBingSupplierStatus.Binded);
                        _cooperateStatusRecordService.AddCooperateStatus(fxUserId, supplierId, userId, AgentBingSupplierStatus.Binded, subFxUserId);
                        //开启预付，设置版本
                        if (_bins.IsPrePay.HasValue && _bins.IsPrePay.Value)
                        {
                            var systemShopId = SiteContext.Current.CurrentLoginShop.Id;
                            _supplierUserService.SetPrePay(_bins.SupplierFxUserId, _bins.FxUserId, 1, systemShopId, false);

                            var agentId = _bins.FxUserId;
                            var agentSystemShopId = _fxUserShopService.GetFxUserIdMapping(new List<int> { agentId })?.FirstOrDefault()?.ShopId ?? 0;
                            _shopService.SetAboutFx1688Version(agentId, agentSystemShopId);
                        }

                        #region 消息埋点

                        // 场景：厂家同意合作申请
                        new SiteMessageService().ConvertToMessage(new SiteMessageContentModel()
                        {
                            FirstType = SiteMessageTypeEnum.PrimaryType.Cooperation.ToString(),
                            SecondType = SiteMessageTypeEnum.PrimaryType.Cooperation.ToString(),
                            FromUserId = curLoginFxUserId,
                            ToUserIds = new List<int> { fxUserId },
                            JumpUrl = "/Partner/MyAgent",
                            Content = "用户[商家id]同意与您合作，成为您的下游商家。",
                            TemplateParas = new { 商家id = curLoginFxUserId.ToString() }.ToJsonExt()
                        });

                        #endregion
                        break;
                    case AgentBingSupplierStatus.Reject:
                        model = new BindSupplierRequestModel { BindId = _bins.Id, isSelf = false, Configs = new List<BindConfigModel>() };
                        var task = TriggerReBindProductOrder(model, supplierId, fxUserId, isAgent: isAgent, bindId: _bins.Id, callback: (int bindId, bool isAddLog) =>
                          {
                              if (isAddLog)
                              {
                                  _supplierUserService.Update(bindId, AgentBingSupplierStatus.UnBind, true);
                                  if (type == 1)
                                  {
                                      _cooperateStatusRecordService.AddCooperateStatus(fxUserId, supplierId, userId, AgentBingSupplierStatus.Reject, subFxUserId);
                                  }
                                  _bins.MemberLevelCode = null;
                                  _supplierUserService.TriggerSyncSupplierUser(_bins, AgentBingSupplierStatus.UnBind);
                              }
                          });
                        #region 消息埋点

                        // 场景：厂家/商家 对 商家/厂家 解除合作
                        if (isAgent) // 当前用户身份为厂家
                        {
                            new SiteMessageService().ConvertToMessage(new SiteMessageContentModel
                            {
                                FirstType = SiteMessageTypeEnum.PrimaryType.Cooperation.ToString(),
                                SecondType = SiteMessageTypeEnum.PrimaryType.Cooperation.ToString(),
                                FromUserId = curLoginFxUserId,
                                ToUserIds = new List<int> { fxUserId },
                                JumpUrl = "/Partner/MySupplier",
                                Content = "您的厂家[厂家id]解除了与您的合作关系。",
                                TemplateParas = new { 厂家id = curLoginFxUserId.ToString() }.ToJsonExt()
                            });
                        }
                        else // 当前用户身份为商家
                        {
                            new SiteMessageService().ConvertToMessage(new SiteMessageContentModel
                            {
                                FirstType = SiteMessageTypeEnum.PrimaryType.Cooperation.ToString(),
                                SecondType = SiteMessageTypeEnum.PrimaryType.Cooperation.ToString(),
                                FromUserId = curLoginFxUserId,
                                ToUserIds = new List<int> { supplierId },
                                JumpUrl = "/Partner/MyAgent",
                                Content = "您的商家[商家id]解除了与您的合作关系。",
                                TemplateParas = new { 商家id = curLoginFxUserId.ToString() }.ToJsonExt()
                            });
                        }

                        #endregion
                        #region 注释
                        //try
                        //{
                        //    // 上下文切换到商家，更新路径后再切回来
                        //    var agentUser = new UserFxService().Get(fxUserId);
                        //    var _siteContext = new SiteContext(agentUser);
                        //    //更新分配给该厂家的的订单和商品到商家自营
                        //    var model = new BindSupplierRequestModel { isSelf = false, Configs = new List<BindConfigModel>() };
                        //    ReBindProductOrder(model, supplierId, fxUserId, isAgent);
                        //    //厂家拒绝商家向自己发来的申请绑定
                        //    _supplierUserService.Update(_bins.Id, AgentBingSupplierStatus.Reject);
                        //}
                        //catch (Exception ex)
                        //{
                        //    Log.WriteError($"厂家【{supplierId}】解除商家【{fxUserId}】更新绑定信息失败：{ex}");
                        //}
                        //finally
                        //{
                        //    //切回当前登录的用户
                        //    var _siteContext = new SiteContext(curUser);
                        //} 
                        #endregion
                        break;
                    case AgentBingSupplierStatus.UnBind:
                        // 解绑操作（厂家精选平台无商品绑定情况，可能在拼多多或京东有商品绑定）
                        //厂家取消和商家建立的关系,当前关系状态是-商家申请成功绑定厂家
                        model = new BindSupplierRequestModel
                        { BindId = _bins.Id, isSelf = true, Configs = new List<BindConfigModel>() };
                        TriggerReBindProductOrder(model, supplierId, fxUserId, isAgent: isAgent, bindId: _bins.Id, callback: (int bindId, bool isAddLog) =>
                        {
                            if (isAddLog)
                            {
                                _supplierUserService.Update(bindId, AgentBingSupplierStatus.UnBind, true);
                                if (type == 2)
                                {
                                    _cooperateStatusRecordService.AddCooperateStatus(fxUserId, supplierId, userId, AgentBingSupplierStatus.UnBind, subFxUserId);
                                }
                                _bins.MemberLevelCode = null;
                                _supplierUserService.TriggerSyncSupplierUser(_bins, AgentBingSupplierStatus.UnBind);

                                Log.Debug($"重置供货厂家（设为自营）1：fxUserId={fxUserId}，supplierId={supplierId}", "ResetSupplier.txt");
                                // 处理基础商品供货厂家
                                new BaseProductSkuService().ResetSupplier(fxUserId, supplierId);

                                //解绑店铺代发
                                new FxUserShopDefaultSupplierService().UpdateDelBySupplier(fxUserId, supplierId);
                            }
                        });

                        #region 消息埋点

                        // 场景：厂家/商家 对 商家/厂家 解除合作
                        if (isAgent) // 当前用户身份为厂家
                        {
                            new SiteMessageService().ConvertToMessage(new SiteMessageContentModel
                            {
                                FirstType = SiteMessageTypeEnum.PrimaryType.Cooperation.ToString(),
                                SecondType = SiteMessageTypeEnum.PrimaryType.Cooperation.ToString(),
                                FromUserId = curLoginFxUserId,
                                ToUserIds = new List<int> { fxUserId },
                                JumpUrl = "/Partner/MySupplier",
                                Content = "您的厂家[厂家id]解除了与您的合作关系。",
                                TemplateParas = new { 厂家id = curLoginFxUserId.ToString() }.ToJsonExt()
                            });
                        }
                        else // 当前用户身份为商家
                        {
                            new SiteMessageService().ConvertToMessage(new SiteMessageContentModel
                            {
                                FirstType = SiteMessageTypeEnum.PrimaryType.Cooperation.ToString(),
                                SecondType = SiteMessageTypeEnum.PrimaryType.Cooperation.ToString(),
                                FromUserId = curLoginFxUserId,
                                ToUserIds = new List<int> { supplierId },
                                JumpUrl = "/Partner/MyAgent",
                                Content = "您的商家[商家id]解除了与您的合作关系。",
                                TemplateParas = new { 商家id = curLoginFxUserId.ToString() }.ToJsonExt()
                            });
                        }

                        #endregion
                        break;
                    default:
                        return FalidResult("操作失败");
                }
            }

            return SuccessResult();
        }

        /// <summary>
        /// 解绑更新历史数据
        /// </summary>
        /// <param name="model"></param>
        /// <param name="fxUserId"></param>
        /// <param name="isAgent">supplierFxUserId == curLoginFxUserId</param>
        [FxMigrateLockFilter()]
        public void ReBindProductOrder(BindSupplierRequestModel model, int supplierId, int fxUserId, bool isAgent, AsyncTask asyncTask = null)
        {
            try
            {
                model.IP = Request.UserHostAddress;
            }
            catch
            { }
            var productFxService = new ProductFxService();
            productFxService.ReBindProductOrderLogic(model, supplierId, fxUserId, isAgent, asyncTask);
        }

        private void BindSupplier(BindSupplierRequestModel bindModel, ProductFxService productFxService, List<PathFlowReference> pathRefs, int fxUserId, int supplierId, bool isAgent, bool isDefault = false, AsyncTask asyncTask = null)
        {
            try
            {
                bindModel.IP = Request.UserHostAddress;
            }
            catch
            { }

            productFxService.BindSupplierLogic(bindModel, productFxService, pathRefs, fxUserId, supplierId, isAgent, isDefault, asyncTask);
        }

        #endregion

        #region 线程任务

        /// <summary>
        /// 写供应商解绑日志
        /// </summary>
        /// <param name="batchId"></param>
        /// <param name="subBusinessType"></param>
        /// <param name="bindId"></param>
        /// <param name="supplierId"></param>
        /// <param name="fxUserId"></param>
        /// <param name="platformType"></param>
        /// <param name="content"></param>
        /// <param name="exceptionMessage"></param>
        private void WriteSupplierUnbindLog(string batchId, string subBusinessType,
            int bindId, int supplierId, int fxUserId, string platformType, string content = null,
            string exceptionMessage = null)
        {
            try
            {
                var creatorId = SiteContext.Current.CurrentFxUserId;
                BusinessLogDataEventTrackingService.Instance.WriteLog(new List<BusinessLogModel>
                {
                    new BusinessLogModel
                    {
                        BatchId = batchId,
                        BusinessType = BusinessTypes.SupplierUnbind.ToString(),
                        SubBusinessType = subBusinessType,
                        BusinessId = bindId.ToString(),
                        SysBusinessId = supplierId.ToString(),
                        FxUserId = fxUserId,
                        PlatformType = platformType,
                        CreatorId = creatorId,
                        Content = content,
                        ExceptionMessage = exceptionMessage,
                    }
                });
            }
            catch
            {
                // ignored
            }
        }

        /// <summary>
        /// 触发 解绑厂家
        /// </summary>
        /// <param name="model"></param>
        /// <param name="fxUserId"></param>
        /// <param name="isAgent">supplierFxUserId == curLoginFxUserId</param>
        /// <param name="supplierId"></param>
        /// <param name="bindId"></param>
        /// <param name="callback"></param>
        /// <returns></returns>
        [IgnoreDoubleAuth]
        public AsyncTask TriggerReBindProductOrder(BindSupplierRequestModel model, int supplierId, int fxUserId, bool isAgent, int bindId, Action<int, bool> callback)
        {
            //站点云平台
            var siteCloudPlatformType = CustomerConfig.CloudPlatformType;
            var curFxUserId = SiteContext.Current.CurrentFxUserId;
            var dbname = Request["dbname"] ?? "";
            //读取token，实例化sitecontext
            var token = Request["token"] ?? "";
            if (string.IsNullOrEmpty(token))
                throw new LogicException($"发往云平台{siteCloudPlatformType}的TriggerReBindProductOrder请求token丢失");

            var subFxUserId = SiteContext.GetSubFxUserId();
            Log.Debug(() => $"TriggerReBindProductOrder==> subFxUserId：{SiteContext.GetSubFxUserId()}", "wm.txt");

            //var initSiteContextResult = InitSiteContextByToken(token, dbname);

            //if (initSiteContextResult == false)
            //    throw new LogicException($"发往云平台{siteCloudPlatformType}的TriggerReBindProductOrder请求token【{token}】解析失败");

            var apiUrl = "/PartnerApi/GetOtherCloudAsyncTask";
            var defaultCloudHost = CustomerConfig.DefaultFenFaSystemUrl;
            var pddCloudHost = CustomerConfig.PinduoduoFenFaSystemUrl;
            var jdCloudHost = CustomerConfig.JingdongFenFaSystemUrl;
            var toutiaoCloudHost = CustomerConfig.ToutiaoFenFaSystemUrl;

            //批次ID
            var batchId = Guid.NewGuid().ToReplaceHyphen();

            #region 判断当前是否有进行中的任务，若有直接返回并提示；没有再创建任务（默认在同一配置库查精选和京东平台）
            var asyncTaskService = new AsyncTaskService();
            var query = new AsyncTaskQueryModel
            {
                ShopId = SiteContext.Current.CurrentShopId,
                FxUserId = SiteContext.Current.CurrentFxUserId,
                StatusList = new List<int>() { 0, 1 },
                Flag = "ReBindProductOrder",
                IsSetExpiress = true,
                ExpiressTime = 30
            };

            var task = asyncTaskService.GetAsyncTask(query);
            //写日志
            WriteSupplierUnbindLog(batchId, "检测当前是否有进行中任务_步骤1", bindId, supplierId, fxUserId,
                PlatformType.Alibaba.ToString(), task?.ToJson(true));
            if (task != null)
                throw new LogicException("当前有解绑厂家任务进行，请等待任务结束后操作！");

            //查询拼多多云任务
            if ((!string.IsNullOrWhiteSpace(pddCloudHost) && pddCloudHost != defaultCloudHost))
            {
                var pddCloudHostUrl = pddCloudHost.TrimEnd("/") + apiUrl;

                var pddApiResult = Common.PostFxSiteApi<AsyncTaskQueryModel, AsyncTask>(
                    pddCloudHostUrl,
                    curFxUserId,
                    query,
                    "查询拼多多云任务-转发到拼多多云");

                //写日志
                WriteSupplierUnbindLog(batchId, "检测当前是否有进行中任务_步骤1", bindId, supplierId, fxUserId,
                    PlatformType.Pinduoduo.ToString(), pddApiResult?.ToJson(true));

                if (pddApiResult != null)
                    throw new LogicException("当前有解绑厂家任务进行，请等待任务结束后操作！");
            }
            //查询抖店云任务
            if ((!string.IsNullOrWhiteSpace(toutiaoCloudHost) && toutiaoCloudHost != defaultCloudHost))
            {
                var toutiaoCloudHostUrl = toutiaoCloudHost.TrimEnd("/") + apiUrl;

                var ttApiResult = Common.PostFxSiteApi<AsyncTaskQueryModel, AsyncTask>(
                    toutiaoCloudHostUrl,
                    curFxUserId,
                    query,
                    "查询抖店云任务-转发到抖店云");

                //写日志
                WriteSupplierUnbindLog(batchId, "检测当前是否有进行中任务_步骤1", bindId, supplierId, fxUserId,
                    PlatformType.TouTiao.ToString(), ttApiResult?.ToJson(true));

                if (ttApiResult != null)
                    throw new LogicException("当前有解绑厂家任务进行，请等待任务结束后操作！");
            }


            task = new AsyncTask();
            task.Flag = "ReBindProductOrder";
            task.CData = model.ToJson();
            asyncTaskService.AddAsyncTask(task);

            //写日志
            WriteSupplierUnbindLog(batchId, "添加解绑供应商任务_步骤2", bindId, supplierId, fxUserId,
                PlatformType.Alibaba.ToString(), model?.ToJson(true));

            //// 添加京东平台任务 
            //var jdTask = task.ToJson().ToObject<AsyncTask>();
            //jdTask.Id = 0;
            //jdTask.CloudPlatformType = PlatformType.Jingdong.ToString();
            //jdTask.Status = 0;
            //asyncTaskService.AddAsyncTask(jdTask);

            //执行前先把状态设为“解绑处理中”
            _supplierUserService.Update(bindId, AgentBingSupplierStatus.UnBinding);

            //写日志
            WriteSupplierUnbindLog(batchId, "更新供应商状态解绑处理中_步骤3", bindId, supplierId, fxUserId,
                PlatformType.Alibaba.ToString(), model?.ToJson(true));

            //启动线程，执行任务
            ThreadPool.QueueUserWorkItem(state =>
            {
                var curUser = SiteContext.Current.CurrentFxUser;
                //操作成功才记录日志
                bool isAddLog = true;

                try
                {
                    //if (CustomerConfig.IsDebug)
                    //    Thread.Sleep(60 * 1000);

                    if (!string.IsNullOrEmpty(dbname))
                        dbname = DES.DecryptDES(dbname, CustomerConfig.LoginCookieEncryptKey);

                    var userFxService = new UserFxService();
                    var userFx = userFxService.Get(fxUserId);
                    SubUserFx subUserFx = null;
                    if (subFxUserId > 0)
                    {
                        subUserFx = new SubUserFxService().Get(subFxUserId);
                    }
                    SiteContext siteContext = new SiteContext(userFx, dbname, new SiteContextConfig { NeedShopExpireTime = false, NeedRelationShops = false }, subUserFx); //实例化商家的SiteContext   

                    task.Status = 1;//进行中
                    task.UpdateTime = DateTime.Now;
                    asyncTaskService.UpdatePendingStatus(task);
                    //写日志
                    WriteSupplierUnbindLog(batchId, "更新解绑任务状态解绑中_步骤4", bindId, supplierId, fxUserId,
                        PlatformType.Alibaba.ToString(), task?.ToJson(true));

                    ReBindProductOrder(model, supplierId, fxUserId, isAgent, task);

                    //写日志
                    WriteSupplierUnbindLog(batchId, "解绑精选商品订单完成_步骤5", bindId, supplierId, fxUserId,
                        PlatformType.Alibaba.ToString());

                    #region 其他分库
                    ToOtherDbExecute(dbname, curUser, userFx, (toDbName) =>
                    {
                        ReBindProductOrder(model, supplierId, fxUserId, isAgent, null);
                        //写日志
                        WriteSupplierUnbindLog(batchId, "解绑精选商品订单其他区完成_步骤5.1", bindId, supplierId, fxUserId,
                            PlatformType.Alibaba.ToString());
                    });
                    #endregion
                    //（精选平台任务）执行完成
                    task.Status = 5;
                    task.UpdateTime = DateTime.Now;
                    asyncTaskService.UpdateStatus(task);
                    //写日志
                    WriteSupplierUnbindLog(batchId, "更新解绑任务状态为解绑完成_步骤6", bindId, supplierId, fxUserId,
                        PlatformType.Alibaba.ToString(), task?.ToJson(true));
                    #region 通过API转发到拼多多、京东、抖店云

                    apiUrl = "/PartnerApi/SaveReBindProductOrder";
                    var requestApiModel = new BindSupplierRequestForApiModel { RequestModel = model, SupplierId = supplierId, FxUserId = fxUserId, CurFxUserId = curFxUserId, IsAgent = isAgent, Task = task };
                    //更新拼多多云
                    if ((!string.IsNullOrWhiteSpace(pddCloudHost) && pddCloudHost != defaultCloudHost))
                    {
                        var pddCloudHostUrl = pddCloudHost.TrimEnd("/") + apiUrl;

                        var pddApiResult = Common.PostFxSiteApi<BindSupplierRequestForApiModel, bool>(
                            pddCloudHostUrl,
                            curFxUserId,
                            requestApiModel,
                            "解绑厂家-转发到拼多多云");
                        //写日志
                        WriteSupplierUnbindLog(batchId, "触发拼多多解绑厂家操作_步骤7.1", bindId, supplierId, fxUserId,
                            PlatformType.Pinduoduo.ToString(),
                            new { Request = requestApiModel, Url = pddCloudHostUrl, Response = pddApiResult }.ToJson(true));
                    }

                    //更新京东云
                    if (!string.IsNullOrWhiteSpace(jdCloudHost) && jdCloudHost != defaultCloudHost)
                    {
                        var jdCloudHostUrl = jdCloudHost.TrimEnd("/") + apiUrl;

                        var jdApiResult = Common.PostFxSiteApi<BindSupplierRequestForApiModel, bool>(
                            jdCloudHostUrl,
                            curFxUserId,
                            requestApiModel,
                            "解绑厂家-转发到京东云");
                        //写日志
                        WriteSupplierUnbindLog(batchId, "触发京东解绑厂家操作_步骤7.2", bindId, supplierId, fxUserId,
                            PlatformType.Jingdong.ToString(),
                            new { Request = requestApiModel, Url = jdCloudHostUrl, Response = jdApiResult }
                                .ToJson(true));
                    }

                    //更新抖店云
                    if (!string.IsNullOrWhiteSpace(toutiaoCloudHost) && toutiaoCloudHost != defaultCloudHost)
                    {
                        var toutiaoCloudHostUrl = toutiaoCloudHost.TrimEnd("/") + apiUrl;

                        var ttApiResult = Common.PostFxSiteApi<BindSupplierRequestForApiModel, bool>(
                            toutiaoCloudHostUrl,
                            curFxUserId,
                            requestApiModel,
                            "解绑厂家-转发到抖店云");
                        //写日志
                        WriteSupplierUnbindLog(batchId, "触发抖店解绑厂家操作_步骤7.3", bindId, supplierId, fxUserId,
                            PlatformType.TouTiao.ToString(),
                            new { Request = requestApiModel, Url = toutiaoCloudHostUrl, Response = ttApiResult }
                                .ToJson(true));
                    }

                    #endregion

                    #region 检查任务是否已完成 （30分钟自动过期）
                    var checkTaskApiUrl = "/PartnerApi/GetOtherCloudAsyncTask";
                    // 循环检查任务完成情况，最多循环30分钟
                    var startTime = DateTime.Now;
                    while (true)
                    {
                        var isAllFinished = true;
                        //京东任务状态
                        query.CloudPlatformType = PlatformType.Jingdong.ToString();
                        query.StatusList = null;
                        var jdTask = ExceptionHandler.TryOneTime(() => asyncTaskService.GetAsyncTask(query), false);
                        //写日志
                        WriteSupplierUnbindLog(batchId, "检测京东解绑厂家任务状态_步骤8", bindId, supplierId, fxUserId,
                            PlatformType.Jingdong.ToString(),
                            new { Request = query, Response = jdTask }
                                .ToJson(true));
                        var jdStatus = jdTask?.Status ?? 0;
                        if (jdStatus == -1)
                        {
                            Log.WriteError($"京东平台解绑厂家失败：{jdTask?.ExceptionDesc}");
                            throw new LogicException($"京东平台解绑厂家失败：{jdTask?.ExceptionDesc}");
                        }
                        if (jdStatus != 5)
                        {
                            isAllFinished = false;
                        }
                        //拼多多任务状态
                        if (!string.IsNullOrWhiteSpace(pddCloudHost) && pddCloudHost != defaultCloudHost)
                        {
                            var pddCloudHostUrl = pddCloudHost.TrimEnd("/") + checkTaskApiUrl;
                            var pddTask = ExceptionHandler.TryOneTime(() =>
                                Common.PostFxSiteApi<AsyncTaskQueryModel, AsyncTask>(
                                    pddCloudHostUrl,
                                    curFxUserId,
                                    query,
                                    "查询任务状态-转发到拼多多云"), false);
                            //写日志
                            WriteSupplierUnbindLog(batchId, "检测拼多多解绑厂家任务状态_步骤8", bindId, supplierId, fxUserId,
                                PlatformType.Pinduoduo.ToString(),
                                new { Request = query, Url = pddCloudHostUrl, Response = pddTask }
                                    .ToJson(true));

                            var pddStatus = pddTask?.Status ?? 0;
                            if (pddStatus == -1)
                            {
                                Log.WriteError($"拼多多平台解绑厂家失败：{pddTask?.ExceptionDesc}");
                                throw new LogicException($"拼多多平台解绑厂家失败：{pddTask?.ExceptionDesc}");
                            }
                            if (pddStatus != 5)
                            {
                                isAllFinished = false;
                            }
                        }

                        //抖店任务状态
                        if (!string.IsNullOrWhiteSpace(toutiaoCloudHost) && toutiaoCloudHost != defaultCloudHost)
                        {
                            var toutiaoCloudHostUrl = toutiaoCloudHost.TrimEnd("/") + checkTaskApiUrl;
                            var ttTask = ExceptionHandler.TryOneTime(() =>
                                Common.PostFxSiteApi<AsyncTaskQueryModel, AsyncTask>(
                                    toutiaoCloudHostUrl,
                                    curFxUserId,
                                    query,
                                    "查询任务状态-转发到抖店云"), false);
                            //写日志
                            WriteSupplierUnbindLog(batchId, "检测抖店解绑厂家任务状态_步骤8", bindId, supplierId, fxUserId,
                                PlatformType.TouTiao.ToString(),
                                new { Request = query, Url = toutiaoCloudHostUrl, Response = ttTask }
                                    .ToJson(true));
                            var ttStatus = ttTask?.Status ?? 0;
                            if (ttStatus == -1)
                            {
                                Log.WriteError($"抖店云平台解绑厂家失败：{ttTask.ExceptionDesc}");
                                throw new LogicException($"抖店云平台解绑厂家失败：{ttTask.ExceptionDesc}");
                            }
                            if (ttStatus != 5)
                            {
                                isAllFinished = false;
                            }
                        }
                        //var spendTimes = (DateTime.Now - startTime).TotalMinutes;
                        //if ((pddTask == null && jdTask == null) || spendTimes > 30)
                        //    break;
                        if (isAllFinished)
                        {
                            //写日志
                            WriteSupplierUnbindLog(batchId, "检测解绑任务状态全平台完成_步骤8", bindId, supplierId, fxUserId,
                                PlatformType.Alibaba.ToString());
                            break;
                        }
                        Thread.Sleep(3000);
                    }
                    #endregion

                    //检测是否存在商品绑定关系
                    CheckIsHasProductBindRelation(new BindSupplierRequestForApiModel
                    {
                        BatchId = batchId,
                        CurFxUserId = curFxUserId,
                        FxUserId = requestApiModel.FxUserId,
                        SupplierId = requestApiModel.SupplierId,
                        IsAgent = requestApiModel.IsAgent,
                        BindId = bindId
                    }, dbname);

                    #region 其他分库
                    ToOtherDbExecute(dbname, curUser, userFx, (toDbName) =>
                    {
                        CheckIsHasProductBindRelation(new BindSupplierRequestForApiModel
                        {
                            BatchId = batchId,
                            CurFxUserId = curFxUserId,
                            FxUserId = requestApiModel.FxUserId,
                            SupplierId = requestApiModel.SupplierId,
                            IsAgent = requestApiModel.IsAgent,
                            BindId = bindId
                        }, toDbName, false);
                    });
                    #endregion
                    //写日志
                    WriteSupplierUnbindLog(batchId, "解绑厂家完成_步骤10", bindId, supplierId, fxUserId,
                        PlatformType.Alibaba.ToString());
                }
                catch (Exception ex)
                {
                    Log.WriteError($"商品绑定厂家-更新订单发生异常：{ex}");

                    isAddLog = false;

                    //写日志
                    WriteSupplierUnbindLog(batchId, "解绑厂家异常_步骤(-1)", bindId, supplierId, fxUserId,
                        PlatformType.Alibaba.ToString(), exceptionMessage: $"{ex}");

                    //异常时把状态设为“解绑失败”
                    _supplierUserService.Update(bindId, AgentBingSupplierStatus.UnBindFail);

                    task.Status = -1;//失败
                    task.ExceptionDesc = ex.Message;
                    task.UpdateTime = DateTime.Now;

                    asyncTaskService.UpdateAsyncTask(task);
                }

                // 绑定完成回调更新绑定关系
                callback(bindId, isAddLog);
            });

            #endregion

            return task;
        }

        /// <summary>
        /// 跳转其他分库执行
        /// </summary>
        /// <param name="dbname"></param>
        /// <param name="curUser"></param>
        /// <param name="userFx"></param>
        /// <param name="action"></param>
        private void ToOtherDbExecute(string dbname, UserFx curUser, UserFx userFx, Action<string> action)
        {
            if (SiteContext.Current.CurrentLoginShop != null && SiteContext.Current.CurrentDbAreaConfig != null && SiteContext.Current.CurrentDbAreaConfig.Any())
            {
                var dbArea = SiteContext.Current.CurrentDbAreaConfig.Select(x => x.DbNameConfig).Select(y => new { DbName = y.DbName, y.NickName }).ToList();
                if (dbArea != null && dbArea.Count >= 2)
                {
                    //跳过当前分库
                    dbArea = dbArea.Where(a => a.DbName != dbname).ToList();
                    foreach (var _db in dbArea)
                    {
                        //指定分库
                        var sc = new SiteContext(userFx, _db.DbName, new SiteContextConfig { NeedShopExpireTime = false, NeedRelationShops = false });
                        action(_db.DbName);
                    }
                    //设回当前分库
                    var curSiteContext = new SiteContext(curUser, dbname, new SiteContextConfig { NeedShopExpireTime = false, NeedRelationShops = false });
                }
            }

        }

		/// <summary>
		/// 是否存在商品关系
		/// </summary>
		/// <param name="model"></param>
		/// <param name="dbName"></param>
		/// <param name="toOtherCloud">是否通过api到其它云平台</param>
		/// <returns></returns>
		private void CheckIsHasProductBindRelation(BindSupplierRequestForApiModel model, string dbName = "", bool toOtherCloud = true)
        {
            //用户信息
            var fxUserId = model.FxUserId;
            var supplierUserId = model.SupplierId;
            var currentFxUserId = SiteContext.Current.CurrentFxUserId;
            var bindId = model.BindId;
            //商家存在商品绑定关系

            //初始化用户信息
			new SiteContext(new UserFxService().Get(fxUserId), dbName, new SiteContextConfig { NeedShopExpireTime = false, NeedRelationShops = false });

			var isHasProductRelation = new PathFlowReferenceService().IsHasProductBindRelation(fxUserId, supplierUserId);

            //写日志
            WriteSupplierUnbindLog(model.BatchId, "检测精选是否存在商品绑定关系_步骤9", bindId, supplierUserId, fxUserId,
                PlatformType.Alibaba.ToString(), new { RoleName = "商家", isHasProductRelation, dbName }.ToJson());
            var message = "精选云平台解绑厂家失败：存在商品绑定关系,清理绑定关系数据失败";
            if (isHasProductRelation)
            {
                //删除商品绑定关系数据
                try
                {
                    DelProductRelation(fxUserId, supplierUserId);
                }
                catch (Exception ex)
                {
					Log.WriteError($"删除商品绑定关系出现异常:{ex}");
					throw new LogicException(message);
				}
			}

			//厂家存在商品绑定关系
			//初始化用户信息
			new SiteContext(new UserFxService().Get(supplierUserId), dbName, new SiteContextConfig { NeedShopExpireTime = false, NeedRelationShops = false });

            isHasProductRelation = new PathFlowReferenceService().IsHasProductBindRelation(fxUserId, supplierUserId);

			//写日志
			WriteSupplierUnbindLog(model.BatchId, "检测精选是否存在商品绑定关系_步骤9", bindId, supplierUserId, fxUserId,
                PlatformType.Alibaba.ToString(), new { RoleName = "厂家", isHasProductRelation, dbName }.ToJson());
            if (isHasProductRelation)
            {
				//删除商品绑定关系数据
				try
				{
					DelProductRelation(fxUserId, supplierUserId);
				}
				catch (Exception ex)
				{
					Log.WriteError($"删除商品绑定关系出现异常:{ex}");
					throw new LogicException(message);
				}
			}

            if (!toOtherCloud)
            {
                return;
            }
            #region 通过API转发到拼多多、京东、抖店云检查商品绑定关系

            var defaultCloudHost = CustomerConfig.DefaultFenFaSystemUrl;
            var pddCloudHost = CustomerConfig.PinduoduoFenFaSystemUrl;
            var jdCloudHost = CustomerConfig.JingdongFenFaSystemUrl;
            var touTiaoCloudHost = CustomerConfig.ToutiaoFenFaSystemUrl;
            //其他云API
            const string apiUrl = "/PartnerApi/IsHasAndDelProductBindRelation";
            //检查商品绑定关系拼多多云
            if (!string.IsNullOrWhiteSpace(pddCloudHost) && pddCloudHost != defaultCloudHost)
            {
                var pddCloudHostUrl = pddCloudHost.TrimEnd("/") + apiUrl;
                var pddApiResult = Common.PostFxSiteApi<BindSupplierRequestForApiModel, bool>(
                    pddCloudHostUrl,
                    currentFxUserId,
                    model,
                    "检查商品绑定关系-转发到拼多多云");
                //写日志
                WriteSupplierUnbindLog(model.BatchId, "检测拼多多是否存在商品绑定关系_步骤9", bindId, supplierUserId, fxUserId,
                    PlatformType.Pinduoduo.ToString(),
                    new { Request = model, Url = pddCloudHostUrl, Response = pddApiResult }.ToJson(true));
                if (pddApiResult)
                {
                    message = "拼多多云平台解绑厂家失败：存在商品绑定关系,清理绑定关系数据失败";
                    Log.WriteError(message);
                    throw new LogicException(message);
                }
            }

            //检查商品绑定关系京东云
            if (!string.IsNullOrWhiteSpace(jdCloudHost) && jdCloudHost != defaultCloudHost)
            {
                var jdCloudHostUrl = jdCloudHost.TrimEnd("/") + apiUrl;
                var jdApiResult = Common.PostFxSiteApi<BindSupplierRequestForApiModel, bool>(
                    jdCloudHostUrl,
                    currentFxUserId,
                    model,
                    "检查商品绑定关系-转发到京东云");
                //写日志
                WriteSupplierUnbindLog(model.BatchId, "检测京东是否存在商品绑定关系_步骤9", bindId, supplierUserId, fxUserId,
                    PlatformType.Jingdong.ToString(),
                    new { Request = model, Url = jdCloudHostUrl, Response = jdApiResult }.ToJson(true));
                if (jdApiResult)
                {
                    message = "京东云平台解绑厂家失败：存在商品绑定关系,清理绑定关系数据失败";
                    Log.WriteError(message);
                    throw new LogicException(message);
                }
            }

            //检查商品绑定关系抖店云
            if (!string.IsNullOrWhiteSpace(touTiaoCloudHost) && touTiaoCloudHost != defaultCloudHost)
            {
                var touTiaoCloudHostUrl = touTiaoCloudHost.TrimEnd("/") + apiUrl;
                var ttApiResult = Common.PostFxSiteApi<BindSupplierRequestForApiModel, bool>(
                    touTiaoCloudHostUrl,
                    currentFxUserId,
                    model,
                    "检查商品绑定关系-转发到抖店云");
                //写日志
                WriteSupplierUnbindLog(model.BatchId, "检测抖店是否存在商品绑定关系_步骤9", bindId, supplierUserId, fxUserId,
                    PlatformType.TouTiao.ToString(),
                    new { Request = model, Url = touTiaoCloudHostUrl, Response = ttApiResult }.ToJson(true));
                if (ttApiResult)
                {
                    message = "抖店云平台解绑厂家失败：存在商品绑定关系,清理绑定关系数据失败";
                    Log.WriteError(message);
                    throw new LogicException(message);
                }
            }

            #endregion
        }

        /// <summary>
        /// 删除商品绑定关系
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="supplierUserId"></param>
		public void DelProductRelation(int fxUserId, int supplierUserId)
		{
			PathFlowReferenceService pathFlowReferenceService = new PathFlowReferenceService();
			PathFlowReferenceConfigService pathFlowReferenceConfigService = new PathFlowReferenceConfigService();

			int pageSize = 500;
			while (true)
			{
				var list = pathFlowReferenceService.GetProductBindRelationList(fxUserId, supplierUserId, pageSize);

				//删除
				pathFlowReferenceService.DelReferencesByIds(list.Select(s => s.Id).ToList());
				pathFlowReferenceConfigService.DelReferencesConfigByRelationCode(list.Select(s => s.RelationCode).ToList());

				if (list.Count < 500)
				{
					break;
				}
			}
		}

		/// <summary>
		/// 获取 解绑厂家 任务执行状态
		/// </summary>
		/// <returns></returns>
		public ActionResult GetReBindProductOrderStatus()
        {
            var siteCloudPlatformType = CustomerConfig.CloudPlatformType; //站点云平台

            var dbname = Request["dbname"] ?? "";
            var token = Request["token"] ?? "";                           //读取token，实例化sitecontext
            if (string.IsNullOrEmpty(token))
                return SuccessResult($"发往云平台{siteCloudPlatformType}的GetReBindProductOrderStatus请求token丢失");

            //var initSiteContextResult = InitSiteContextByToken(token, dbname);

            //if (initSiteContextResult == false)
            //    return SuccessResult($"发往云平台{siteCloudPlatformType}的GetReBindProductOrderStatus请求token【{token}】解析失败");


            var curFxUserId = SiteContext.Current.CurrentFxUserId;
            #region 查询最近一条任务
            var asyncTaskService = new AsyncTaskService();
            var query = new AsyncTaskQueryModel
            {
                ShopId = SiteContext.Current.CurrentShopId,
                FxUserId = curFxUserId,
                Flag = "ReBindProductOrder",
                StartCreateTime = DateTime.Now.AddMinutes(-30),
                CloudPlatformType = PlatformType.Alibaba.ToString()
            };

            var allPtTaskList = new List<AsyncTask>();
            // 当前平台最后一条任务(精选平台)
            var task = asyncTaskService.GetAsyncTask(query);
            if (task != null)
                allPtTaskList.Add(task);

            // 京东平台最后一条任务
            var jdQuery = query.ToJson().ToObject<AsyncTaskQueryModel>();
            jdQuery.CloudPlatformType = PlatformType.Jingdong.ToString();
            var jdTask = asyncTaskService.GetAsyncTask(jdQuery);
            if (jdTask != null)
                allPtTaskList.Add(task);

            #region 其他平台最后一条任务
            const string apiUrl = "/PartnerApi/GetOtherCloudAsyncTask";
            var defaultCloudHost = CustomerConfig.DefaultFenFaSystemUrl;
            var pddCloudHost = CustomerConfig.PinduoduoFenFaSystemUrl;
            var jdCloudHost = CustomerConfig.JingdongFenFaSystemUrl;
            var ttCloudHost = CustomerConfig.ToutiaoFenFaSystemUrl;

            //拼多多云-查询任务状态
            if ((!string.IsNullOrWhiteSpace(pddCloudHost) && pddCloudHost != defaultCloudHost))
            {
                pddCloudHost = pddCloudHost.TrimEnd("/") + apiUrl;

                var pddApiResult = Common.PostFxSiteApi<AsyncTaskQueryModel, AsyncTask>(
                    pddCloudHost,
                    curFxUserId,
                    query,
                    "查询任务状态-转发到拼多多云");
                if (pddApiResult != null)
                    allPtTaskList.Add(pddApiResult);
            }

            //抖店云-查询任务状态
            if ((!string.IsNullOrWhiteSpace(ttCloudHost) && ttCloudHost != defaultCloudHost))
            {
                ttCloudHost = ttCloudHost.TrimEnd("/") + apiUrl;

                var ttApiResult = Common.PostFxSiteApi<AsyncTaskQueryModel, AsyncTask>(
                    ttCloudHost,
                    curFxUserId,
                    query,
                    "查询任务状态-转发到抖店云");
                if (ttApiResult != null)
                    allPtTaskList.Add(ttApiResult);
            }

            ////更新京东云
            //if (!string.IsNullOrWhiteSpace(jdCloudHost) && jdCloudHost != defaultCloudHost)
            //{
            //    jdCloudHost = jdCloudHost.TrimEnd("/") + apiUrl;

            //    var jdApiResult = Common.PostFxSiteApi<AsyncTaskQueryModel, AsyncTask>(
            //   jdCloudHost,
            //   curFxUserId,
            //   query,
            //   "查询任务状态-转发到京东云");
            //    allPtTaskList.Add(jdApiResult);
            //}

            #endregion

            if (allPtTaskList != null && allPtTaskList.Any())
            {
                var returnTask = allPtTaskList.First();
                returnTask.SuccessCount = allPtTaskList.Sum(x => x.SuccessCount);
                returnTask.TotalCount = allPtTaskList.Sum(x => x.TotalCount);
                returnTask.FailCount = allPtTaskList.Sum(x => x.FailCount);

                if (allPtTaskList.Any(t => t.Status == 0 || t.Status == 1))
                {
                    return SuccessResult($"任务执行中，请稍候", returnTask);
                }
                else if (allPtTaskList.All(t => t.Status == 5))
                {
                    return SuccessResult($"无进行中的任务.", returnTask);
                }
                else
                {
                    return SuccessResult($"任务执行出现异常，请稍候重新提交", returnTask);
                }
            }

            #endregion

            return SuccessResult("无进行中的任务", null);
        }

        #endregion

        #region 我的商家
        public ActionResult MyAgent()
        {
            ViewBag.DefaultMobile = Request["mobile"].ToString2();

            #region 不展示非合作展示
            var shopId = SiteContext.Current.CurrentShopId;
            var keyOrderDisplaySetting = CommonSettingService.OrderDisplaySetting;
            List<string> keys = new List<string>() {
                 "/FenFa/System/Config/IsShowCancelUser",
                 "/FenFa/System/Config/IsSalePricePublic",
                 "/FenFa/System/Config/IsAgentSendAddress",
                "/FenFa/System/Config/IsShopNamePublic",
                "/FenFa/System/Config/StockOutType",
                "/FenFa/System/Config/StockOutZHType",
                "/ErpWeb/OutAccount/Agent/UnSetPriceDailog",
                "/ErpWeb/OutAccount/Supplier/UnSetPriceDailog",
                "/FenFa/System/Config/UnSetMemberLevelDialog",
               keyOrderDisplaySetting
            };
            var result = _commonSettingService.GetSets(keys, shopId);

            //没有设置则用默认
            if (result.Any(x => x.Key == "/FenFa/System/Config/IsAgentSendAddress") == false)
            {
                var defaultIsAgentSendAddress = _commonSettingService.Get("/FenFa/System/Config/IsAgentSendAddress", 0);
                if (defaultIsAgentSendAddress != null)
                    result.Add(defaultIsAgentSendAddress);
            }
            if (result.Any(x => x.Key == "/FenFa/System/Config/UnSetMemberLevelDialog") == false)
            {
                //设置默认值
                result.Add(new CommonSetting { Key = "/FenFa/System/Config/UnSetMemberLevelDialog", ShopId = shopId, Value = "false" });
            }
            if (result.Any(x => x.Key == "/ErpWeb/OutAccount/Agent/UnSetPriceDailog") == false)
            {
                result.Add(new CommonSetting { Key = "/ErpWeb/OutAccount/Agent/UnSetPriceDailog", ShopId = shopId, Value = "true" });
            }
            else
            {
                var date = result.First(x => x.Key == "/ErpWeb/OutAccount/Agent/UnSetPriceDailog").Value.toDateTime();
                if (date.AddDays(15) < DateTime.Now)
                    result.First(x => x.Key == "/ErpWeb/OutAccount/Agent/UnSetPriceDailog").Value = "true";
                else
                    result.First(x => x.Key == "/ErpWeb/OutAccount/Agent/UnSetPriceDailog").Value = "false";
            }
            if (result.Any(x => x.Key == "/ErpWeb/OutAccount/Supplier/UnSetPriceDailog") == false)
            {
                result.Add(new CommonSetting { Key = "/ErpWeb/OutAccount/Supplier/UnSetPriceDailog", ShopId = shopId, Value = "true" });
            }
            else
            {
                var date = result.First(x => x.Key == "/ErpWeb/OutAccount/Supplier/UnSetPriceDailog").Value.toDateTime();
                if (date.AddDays(15) < DateTime.Now)
                    result.First(x => x.Key == "/ErpWeb/OutAccount/Supplier/UnSetPriceDailog").Value = "true";
                else
                    result.First(x => x.Key == "/ErpWeb/OutAccount/Supplier/UnSetPriceDailog").Value = "false";
            }

            if (result.Any(x => x.Key == keyOrderDisplaySetting) == false)
            {
                //设置默认值
                result.Add(new CommonSetting { Key = keyOrderDisplaySetting, ShopId = shopId, Value = CommonSettingService.DefaultOrderDisplaySetting.ToJson() });
            }

            #endregion
            ViewBag.CommonSettings = result;

            Dictionary<int, string> newStatusDic = new Dictionary<int, string>();
            foreach (var item in Enum.GetValues(typeof(AgentBingSupplierStatus)))
            {
                newStatusDic.Add((int)item, ((AgentBingSupplierStatus)item).GetEnumDescription());
            }
            ViewBag.Status = newStatusDic;
            var fxuserId = SiteContext.Current.CurrentFxUserId;
            var systemShopId = SiteContext.Current.CurrentShopId;
            var checkResult = _fxUserShopService.IsFxUserIsHaveAvaliableCount(fxuserId, ServiceVersionTypeEnum.SellerUnit);
            ViewBag.AvailableCountInfo = checkResult.ToJson();
            ViewBag.IsAddAvailable = checkResult.IsAvailable;
            ViewBag.HideCancelAgent = _commonSettingService.GetString("/ErpWeb/MyAgent/HideCancelAgent", systemShopId);
            //是否开通了1688轻应用
            var qingService = new QingService(fxuserId);
            ViewBag.IsOpenQing = QingService.Instance.IsOpenQing;
            ViewBag.Is1688new = QingService.Instance.Is1688new;

            // 页面按钮展示权限 
            ViewBag.ShowPermDict = new Dictionary<string, bool>
            {
                {$"#{nameof(FxPermission.AddBindAgent)}",SiteContext.HasPermission(FxPermission.AddBindAgent)},
                {$"#{nameof(FxPermission.AgentInvite)}",SiteContext.HasPermission(FxPermission.AgentInvite)},
                {$".{nameof(FxPermission.AgreeAndRefuseAgent)}",SiteContext.HasPermission(FxPermission.AgreeAndRefuseAgent)},
                {$".{nameof(FxPermission.UnbindAgent)}",SiteContext.HasPermission(FxPermission.UnbindAgent)},
                {$".{nameof(FxPermission.SetPrePay)}",SiteContext.HasPermission(FxPermission.SetPrePay)},
            }.ToJson();

            return View();
        }

        [LogForOperatorFilter("添加商家绑定")]
        [FxMigrateLockFilter()]
        public ActionResult AddBindAgent(int agentId, string remark, bool? isPrePay, string memberLevelCode = null)
        {
            int fxUserId = SiteContext.Current.CurrentFxUserId;
            SupplierUser _new = new SupplierUser();
            _new.FxUserId = agentId;
            _new.SupplierFxUserId = fxUserId;
            _new.CreateBy = fxUserId;

            _new.From = "PC";
            _new.Remark = remark ?? "";


            // l688新厂家【1688new】：商家默认都开启预付
            // 20241220 调整为：1688 新店铺或新注册用户，将不会默认对下游开启使用担保交易流程
            var currentUser = _userFxService.GetUserFx(fxUserId);
            if (currentUser != null && currentUser.UserFlag.ToString2().Contains("1688new") && false)
            {
                _new.IsPrePay = true;
                _new.ApplyPrepayStatus = -11;
                isPrePay = true;
            }

            //1688预付款设置
            if (isPrePay.HasValue)
            {
                _new.IsPrePay = isPrePay.Value;
                _new.LastOpenPrepayTime = DateTime.Now;
                //开启预付，设置版本
                if (isPrePay.Value)
                {
                    var agentSystemShopId = _fxUserShopService.GetFxUserIdMapping(new List<int> { agentId })?.FirstOrDefault()?.ShopId ?? 0;
                    _shopService.SetAboutFx1688Version(agentId, agentSystemShopId);

                    //设置发货方式
                    var settings = new BusinessSettingsService().GetsByCurrentUser(new List<string>
                    {
                        BusinessSettingKeys.SupplyBy1688.DeliveryMode
                    });
                    var deliveryMode = settings.FirstOrDefault(m => m.Key == BusinessSettingKeys.SupplyBy1688.DeliveryMode)?.Value.ToInt() ?? 0;
                    _new.DeliveryMode = deliveryMode;
                }

            }
            // 后端再次校验商家是否可以绑定
            if (fxUserId == agentId)
                return FalidResult("不能绑定自己");
            var supplier = _userFxService.GetByNamebeltStatusV1(fxUserId, agentId.ToString(), isMobile: false);
            if (supplier == null || supplier.Id == 0)
                return FalidResult("商家未找到");
            else if (supplier.Status == 4)
                return FalidResult("商家已经被绑定");
            else if (supplier.Status == 5)
                return FalidResult("不能绑定自己");
            else if (supplier.Status == 6 && supplier.Status2 != 4) //已经被绑定且关系不是已取消
                return FalidResult("已经绑定");

            var checkAvaliableCountResult = _fxUserShopService.IsFxUserIsHaveAvaliableCount(fxUserId, ServiceVersionTypeEnum.SellerUnit);
            if (checkAvaliableCountResult.IsAvailable == false)
                return FalidResult("绑定供应商失败，您的供应商数量已经超出限额了，请联系我们升级版本", "NEED_UPGRADE_FACTORY_COUNT", checkAvaliableCountResult);
            if (supplier.Status == 6)//已经取消
            {
                _new.Id = supplier.SupplierId;
                _new.Status = AgentBingSupplierStatus.UnBindFail;  //绑定申请中
                _new.CreateTime = DateTime.Now;
                _new.MemberLevelCode = memberLevelCode;
            }
            var result = _supplierUserService.Add(_new);
            // 同步货盘
            _supplierUserService.TriggerSyncSupplierUser(_new);

            if (result > 0)
            {
                //如果暂时还没有业务数据,且和Agent不在同一库,则移动到Agent库,只限第一个添加的Agent
                //var sc = new SiteContext(userFx, dbname, new SiteContextConfig { NeedShopExpireTime = false, NeedRelationShops = false });
                //if (SiteContext.Current.CurrentDbAreaConfig.Count == 1
                //    && SiteContext.Current.CurrentDbAreaConfig.FirstOrDefault().DbNameConfig.DbName == SiteContext.Current.CurrentDbConfig.DbNameConfig.DbName)
                //{
                //    var suppliers = _supplierUserService.GetByFxUserId(fxUserId, false);
                //    if (suppliers != null && suppliers.Count == 1)
                //    {
                //        //查找suppliers的库
                //        var dbconfig = DbPolicyExtension.GetConfigFxUserId(agentId);
                //        if (DES.EncryptDES(dbconfig.DbNameConfig.DbName, CustomerConfig.LoginCookieEncryptKey) != SiteContext.Current.CurrentDbConfig.DbNameConfig.DbName)
                //        {
                //            //移动库
                //            bool resultchange = DbPolicyExtension.ChangeUserDb(fxUserId, agentId);
                //            if (!resultchange)
                //            {
                //                Log.WriteError($"添加第一个商家时,更改用户数据库分库配置失败,用户ID{fxUserId},商家ID{agentId}");
                //            }
                //        }
                //    }
                //}

                #region 消息埋点

                // 场景：厂家对商家发起合作申请
                new SiteMessageService().ConvertToMessage(new SiteMessageContentModel
                {
                    FirstType = SiteMessageTypeEnum.PrimaryType.Cooperation.ToString(),
                    SecondType = SiteMessageTypeEnum.PrimaryType.Cooperation.ToString(),
                    FromUserId = fxUserId,
                    ToUserIds = new List<int> { agentId },
                    JumpUrl = "/Partner/MySupplier",
                    Content = "用户[厂家id]申请与您合作，成为您的上游厂家。",
                    TemplateParas = new { 厂家id = fxUserId.ToString() }.ToJsonExt()
                });

                #endregion

                return SuccessResult("成功");
            }
            return FalidResult("失败");
        }

        [LogForOperatorFilter("更新商家备注")]
        public ActionResult EditAgentRemark(int id, string remark)
        {
            if (remark.Length > 64)
                return FalidResult("备注不能超过64个字符");
            int result = _supplierUserService.EditAgentRemark(id, remark);
            // 同步货盘
            var model = _supplierUserService.Get(id);
            _supplierUserService.TriggerSyncSupplierUser(model);
            if (result > 0)
                return SuccessResult("成功");
            return FalidResult("失败");
        }

        public ActionResult EditSupplierRemark(int id, string remark)
        {
            if (remark.Length > 32)
                return FalidResult("备注不能超过32个字符");
            int result = _supplierUserService.EditSupplierRemark(id, remark);
            // 同步货盘
            var model = _supplierUserService.Get(id);
            _supplierUserService.TriggerSyncSupplierUser(model);
            if (result > 0)
                return SuccessResult("成功");
            return FalidResult("失败");
        }

        public ActionResult LoadMyAgentList(string key, AgentBingSupplierStatus? status, int pageIndex, int pageSize)
        {
            int fxUserId = SiteContext.Current.CurrentFxUserId;
            var result = _supplierUserService.GetAgentList(fxUserId, key, status, pageIndex, pageSize, needEncryptAccount: true);

            //获取商家的分库情况
            var agentDbs = DbPolicyExtension.GetConfigFxSupplierNames(fxUserId, false);
            _supplierUserService.ProcessDbArea(result.Item2, agentDbs.Item1, agentDbs.Item2);

            return SuccessResult(new { Total = result.Item1, List = result.Item2 });
        }

        public ActionResult GetAgentsForManualSync()
        {

            var agentList = _supplierUserService.GetAgentList(SiteContext.GetCurrentFxUserId(), "", null, 1, 9999);
            var bindStatusList = new List<AgentBingSupplierStatus>() { AgentBingSupplierStatus.Binded, AgentBingSupplierStatus.UnBindFail, AgentBingSupplierStatus.UnBinding };

            return SuccessResult(agentList.Item2.Where(a => bindStatusList.Contains(a.Status)).Select(a => new { Id = a.FxUserId, a.Status, Name = a.AgentMobileAndRemark }));
        }

        public ActionResult LoadMyAgentQrCode()
        {
            var exceedTime = DateTime.Now.AddDays(1);//这个过期时间后续可做配置
            var code = GetQrCode("MyAgent", exceedTime);

            return SuccessResult(new { QrCode = code, ExceedTime = exceedTime });
        }


        #endregion

        /// <summary>
        /// 解绑操作（厂家有商品绑定情况）
        /// </summary>
        /// <returns>厂家绑定逻辑</returns>
        [LogForOperatorFilter("解除厂家绑定")]
        [FxMigrateLockFilter()]
        [FxAuthorize]
        public ActionResult UnbundlingEvent(BindSupplierRequestModel model)
        {
            if (model == null)
                throw new LogicException("数据解析失败，请刷新重试");
            if (model.from != "supplier")
                throw new LogicException("数据非法，请刷新重试");
            if (model.unbindSupplierId <= 0)
                throw new LogicException("您要解绑的厂家已取消绑定，请刷新页面确认");
            //查询该厂家相关联的商品信息
            var supllierUser = _supplierUserService.Get(model.unbindSupplierId);
            if (supllierUser == null)
                throw new LogicException("您要解绑的厂家已取消绑定，请刷新页面确认");
            if (supllierUser.Status != AgentBingSupplierStatus.Binded && supllierUser.Status != AgentBingSupplierStatus.UnBinding && supllierUser.Status != AgentBingSupplierStatus.UnBindFail)
                throw new LogicException("当前的厂家绑定状态不是【绑定成功、解绑中、解绑失败】，不需要解绑");

            var fxUserId = SiteContext.Current.CurrentFxUserId;
            //检查目标厂家是否已解绑
            if (model.isSelf == false && model.Configs.Any() == false)
                throw new LogicException("请选择厂家");
            if (model.isSelf == false && model.Configs.Any())
            {
                foreach (var config in model.Configs)
                {
                    var targetSupplierUser = _supplierUserService.GetByFxIds(new List<int> { fxUserId }, new List<int> { config.SupplierId })?.FirstOrDefault();
                    if (targetSupplierUser == null || targetSupplierUser.Status != AgentBingSupplierStatus.Binded)
                    {
                        var userFx = new UserFxService().Get(targetSupplierUser.SupplierFxUserId);
                        throw new LogicException($"您选择的厂家【{(userFx?.Mobile ?? "")}】不是【绑定成功】状态，请刷新重试，或更换其他厂家");
                    }
                }
            }
            // 解绑更新历史数据
            //ReBindProductOrder(model, model.unbindSupplierFxUserId, fxUserId, isAgent: false);
            //TriggerReBindProductOrder(model, model.unbindSupplierFxUserId, fxUserId, isAgent: false);
            TriggerReBindProductOrder(model, model.unbindSupplierFxUserId, fxUserId, isAgent: false, bindId: model.unbindSupplierId, callback: (int bindId, bool isAddLog) =>
            {
                if (isAddLog)
                {
                    _supplierUserService.Update(bindId, AgentBingSupplierStatus.UnBind, true);
                    // 同步货盘
                    _supplierUserService.TriggerSyncSupplierUserStatu(fxUserId, model.unbindSupplierId, 0, AgentBingSupplierStatus.UnBind);
                    // 增加解绑记录
                    _cooperateStatusRecordService.AddCooperateStatus(fxUserId, model.unbindSupplierFxUserId, fxUserId, AgentBingSupplierStatus.UnBind, SiteContext.GetSubFxUserId());
                    new FxUserShopDefaultSupplierService().UpdateDelBySupplier(fxUserId, model.unbindSupplierFxUserId);

                    Log.Debug($"重置供货厂家（UnbundlingEvent）1：fxUserId={fxUserId}，supplierId={model.unbindSupplierFxUserId}", "ResetSupplier.txt");
                    // 处理基础商品供货厂家
                    new BaseProductSkuService().ResetSupplier(fxUserId, model.unbindSupplierFxUserId);
                }
            });


            #region 消息埋点

            new SiteMessageService().ConvertToMessage(new SiteMessageContentModel
            {
                FirstType = SiteMessageTypeEnum.PrimaryType.Cooperation.ToString(),
                SecondType = SiteMessageTypeEnum.PrimaryType.Cooperation.ToString(),
                FromUserId = fxUserId,
                ToUserIds = new List<int> { model.unbindSupplierId },
                JumpUrl = "/Partner/MyAgent",
                Content = "您的商家[商家id]解除了与您的合作关系。",
                TemplateParas = new { 商家id = fxUserId.ToString() }.ToJsonExt()
            });

            #endregion

            return SuccessResult();
        }

        /// <summary>
        /// 判断当前关系有没有关联商品
        /// </summary>
        /// <param name="fxUserId">当前用户FxUserId</param>
        /// <param name="supplierId">厂家UserId</param>
        /// <returns></returns>
        public ActionResult GetUnboundInfo(int supplierId)
        {
            //var isBindPublic = _pathFlowNodeService.GetPathFlowNodeContainsChiNodeCount(supplierId);
            var productFxService = new ProductFxService();
            var isExist = productFxService.CheckSupplierHasProduct(supplierId, SiteContext.Current.CurrentFxUserId);
            //isExist = true;//调试解绑，临时设为true
            return SuccessResult(new
            {
                IsBind = isExist
            });
        }

        /// <summary>
        /// (厂家)检验用户名是否存在或者已经绑定
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        public ActionResult GetByNamebeltStatus(string key)
        {
            if (key.IsNullOrEmpty())
                return FalidResult("厂家信息不能为空");

            var cuurFxUser = SiteContext.Current.CurrentFxUser;
            if (key.Trim() == cuurFxUser.Mobile.Trim())
            {
                return SuccessResult(new
                {
                    Id = cuurFxUser.Id,
                    NickName = cuurFxUser.NickName,
                    Mobile = cuurFxUser.Mobile,
                    Status = 5  //说明这个是自己的账号
                });
            }

            var result = _userFxService.GetByNamebeltStatus(cuurFxUser.Id, key);
            if (result == null)
                return FalidResult("厂家未找到");

            //提示,Status是6的话说明这个用户已经被绑定
            return SuccessResult(new
            {
                Id = result.Id,
                NickName = result.NickName,
                Mobile = result.Mobile,
                Status = result.Status,
                Status2 = result.Status2
            });
        }
        /// <summary>
        /// (商家)检验用户名是否存在或者已经绑定
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        public ActionResult GetByNamebeltStatusV1(string key)
        {
            if (key.IsNullOrEmpty())
                return FalidResult("商家信息不能为空");

            var cuurFxUser = SiteContext.Current.CurrentFxUser;
            if (key.Trim() == cuurFxUser.Mobile.Trim())
            {
                return SuccessResult(new
                {
                    Id = cuurFxUser.Id,
                    NickName = cuurFxUser.NickName,
                    Mobile = cuurFxUser.Mobile,
                    Status = 5  //说明这个是自己的账号
                });
            }

            var result = _userFxService.GetByNamebeltStatusV1(cuurFxUser.Id, key);
            if (result == null)
                return FalidResult("商家未找到");

            //提示,Status是4的话说明这个用户已经被绑定
            return SuccessResult(new
            {
                Id = result.Id,
                NickName = result.NickName,
                Mobile = result.Mobile,
                Status = result.Status,
                Status2 = result.Status2
            });
        }


        public string GetQrCode(string types, DateTime exceedTime)
        {

            var fxuser = SiteContext.Current.CurrentFxUser;

            var model = new CommonQRCode();
            model.FxUserId = fxuser.Id;
            model.UserId = 0;
            model.ShopId = 0;
            model.IsUse = false;
            model.Name = fxuser.Mobile;
            model.QrCodeType = types;
            model.CreateTime = DateTime.Now;
            model.ExceedTime = exceedTime;

            var id = _commonQRCodeService.AddQRcode(model);

            var joinCode = "";
            if (id > 0)
            {
                joinCode = DES.EncryptUrl($"{id}", CustomerConfig.LoginCookieEncryptKey);
                model.JoinCode = joinCode;

                model.Id = id;

                _commonQRCodeService.Update(model);
            }
            else
                throw new LogicException("生成失败(id=0)！");


            if (string.IsNullOrEmpty(joinCode))
                throw new LogicException("生成失败(code 为空)！");

            joinCode = CustomerConfig.DefaultFenFaSystemUrl.TrimEnd("/") + "/PhoneQrCode/Index?c=" + joinCode;

            return joinCode;
        }

        public ActionResult GetUserVersionInfo()
        {
            return SuccessResult();
            ////判断当前用户是否开通白名单
            //var fxUserId = SiteContext.Current.CurrentFxUserId;
            //var settingService = new CommonSettingService();
            //var isShouldPay = settingService.IsFxUserShouldPay(fxUserId);
            //if (isShouldPay)
            //{
            //    var service = new UserServiceVersionMappingService();
            //    var model = service.GetUserVersionInfo(fxUserId);
            //    return SuccessResult(model);
            //}
            //else
            //    return SuccessResult();
        }

        /// <summary>
        /// 置顶
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public ActionResult SetTop(int id)
        {
            try
            {
                _supplierUserService.UpdateTop(id, true);
            }
            catch (Exception ex)
            {
                Log.WriteError($"置顶失败id:{id}:错误消息:{ex}");
                return FalidResult("置顶失败");

            }
            return SuccessResult("置顶成功");
        }

        /// <summary>
        /// 取消置顶
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public ActionResult CancelTop(int id)
        {
            try
            {
                _supplierUserService.UpdateTop(id, false);
            }
            catch (Exception ex)
            {
                Log.WriteError($"取消置顶失败id:{id}:错误消息:{ex}");
                return FalidResult("取消置顶失败");

            }
            return SuccessResult("取消置顶成功");
        }


        /// <summary>
        /// 设置预付状态
        /// </summary>
        /// <param name="fxUserId">商家FxUserId</param>
        /// <param name="isPrePay">1:开启、其它值:关闭</param>
        /// <returns></returns>
        [FxAuthorize]
        public ActionResult SetPrePay(int fxUserId, int isPrePay)
        {
            var strTitle = "开启预付";
            if (isPrePay != 1)
                strTitle = "关闭预付";
            try
            {
                //切换到自己的分区
                SwitchSelfDbName();

                var supplierFxUserId = SiteContext.Current.CurrentFxUser.Id;
                var systemShopId = SiteContext.Current.CurrentLoginShop.Id;
                var result = _supplierUserService.SetPrePay(supplierFxUserId, fxUserId, isPrePay, systemShopId);
                if (result.Success)
                {
                    var changeModel = (ChangePrePayForCheckModel)result.ReturnData;

                    #region 成功后还要保存到京东和拼多多平台、抖店云平台
                    var apiUrl = "/ChangePrePayApi/SetPrePay";
                    var aliCloudHost = CustomerConfig.DefaultFenFaSystemUrl.TrimEnd("/") + apiUrl;
                    var pddCloudHost = CustomerConfig.PinduoduoFenFaSystemUrl.TrimEnd("/") + apiUrl;
                    var jdCloudHost = CustomerConfig.JingdongFenFaSystemUrl.TrimEnd("/") + apiUrl;
                    var ttCloudHost = CustomerConfig.ToutiaoFenFaSystemUrl.TrimEnd("/") + apiUrl;

                    //支持1688代销的平台
                    var fx1688SupportPlatformTypes = CustomerConfig.Fx1688SupportPlatformTypes;

                    //分单系统主站点在阿里云，所有分发到朵朵云和京东云、抖店云平台
                    if (!string.IsNullOrEmpty(CustomerConfig.PinduoduoFenFaSystemUrl) && aliCloudHost != pddCloudHost && (fx1688SupportPlatformTypes.Contains(PlatformType.Pinduoduo.ToString()) || fx1688SupportPlatformTypes.Contains(PlatformType.KuaiTuanTuan.ToString())))
                        Common.PostFxSiteApi<ChangePrePayForCheckModel, bool>(pddCloudHost, supplierFxUserId, changeModel, "跨云平台预付状态变化-订单审核处理");

                    if (!string.IsNullOrEmpty(CustomerConfig.JingdongFenFaSystemUrl) && aliCloudHost != jdCloudHost && fx1688SupportPlatformTypes.Contains(PlatformType.Jingdong.ToString()))
                        Common.PostFxSiteApi<ChangePrePayForCheckModel, bool>(jdCloudHost, supplierFxUserId, changeModel, "跨云平台预付状态变化-订单审核处理");

                    if (!string.IsNullOrEmpty(CustomerConfig.ToutiaoFenFaSystemUrl) && aliCloudHost != ttCloudHost && fx1688SupportPlatformTypes.Contains(PlatformType.TouTiao.ToString()))
                        Common.PostFxSiteApi<ChangePrePayForCheckModel, bool>(ttCloudHost, supplierFxUserId, changeModel, "跨云平台预付状态变化-订单审核处理");
                    #endregion

                    #region 消息埋点

                    if (isPrePay == 1)
                    {
                        // 场景：厂家对商家开启预付
                        new SiteMessageService().ConvertToMessage(new SiteMessageContentModel
                        {
                            FirstType = SiteMessageTypeEnum.PrimaryType.Cooperation.ToString(),
                            SecondType = SiteMessageTypeEnum.PrimaryType.Cooperation.ToString(),
                            FromUserId = supplierFxUserId,
                            ToUserIds = new List<int> { fxUserId },
                            JumpUrl = "/GeneralizeIndex/aliDistributionIntroduce",
                            Content = "您的厂家[厂家id]对您开启了线上担保交易，请及时查看。",
                            TemplateParas = new { 厂家id = supplierFxUserId.ToString() }.ToJsonExt()
                        });
                    }

                    #endregion

                    return SuccessResult($"{strTitle}成功");
                }
                else
                    return FalidResult($"{strTitle}失败", result.Message);

            }
            catch (Exception ex)
            {
                Log.WriteError($"{strTitle}失败FxUserId:{fxUserId}:错误消息:{ex}");
                return FalidResult($"{strTitle}失败");

            }
        }

        /// <summary>
        /// 确认预付
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <param name="isPrePay"></param>
        /// <returns></returns>
        [FxAuthorize(FxPermission.SetPrePay)]
        public ActionResult ConfirmPrePay(List<int> fxUserId, int isPrePay)
        {
            var strTitle = isPrePay == 1 ? "确认开启预付" : "确认拒绝预付";
            var supplierFxUserId = SiteContext.Current.CurrentFxUser.Id;
            foreach (var id in fxUserId)
            {
                try
                {
                    if (isPrePay == 1)
                    {
                        // 设置预付
                        var res = this.CurrentSetPrePay(id, isPrePay);
                        // 设置预付状态
                        if (res)
                        {
                            _supplierUserService.SetApprovePrePay(supplierFxUserId, id, isPrePay);

                        }
                        else
                        {
                            _supplierUserService.SetApprovePrePay(supplierFxUserId, id, isPrePay, true);
                        }
                    }
                    else
                    {
                        _supplierUserService.SetApprovePrePay(supplierFxUserId, id, isPrePay);
                    }
                }
                catch (Exception ex)
                {
                    Log.WriteError($"{strTitle}失败FxUserId:{fxUserId}:错误消息:{ex}");
                }
            }
            return SuccessResult($"{strTitle}成功");
        }

        /// <summary>
        /// 担保交易弹窗数据
        /// </summary>
        /// <param name="type">0：商家、1：厂家</param>
        /// <param name="popType">数据类型</param>
        /// <returns></returns>
        [FxAuthorize(AuthorizeType.PermissionOR, FxPermission.SetPrePay, FxPermission.ApplyPrepay)]
        public ActionResult PopPrePayData(int type, List<int> popType, bool isAll)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var systemShopId = SiteContext.Current.CurrentLoginShop.Id;
            var data = new List<ApplyPrepayPopModel>();
            DateTime? daySu = null;
            DateTime? daybu = null;

            var supplierKey = "/ErpWeb/1688Supply/Supplier/LastTipDateTime";
            var businessKye = "/ErpWeb/1688Supply/Agent/LastTipDateTime";
            var supplierSetting = _commonSettingService.GetString(supplierKey, systemShopId);
            var businessSetting = _commonSettingService.GetString(businessKye, systemShopId);
            if (!supplierSetting.IsNullOrEmpty())
                daySu = Convert.ToDateTime(supplierSetting);
            if (!businessSetting.IsNullOrEmpty())
                daybu = Convert.ToDateTime(businessSetting);

            bool isCurrentSu = daySu.HasValue && daySu.Value.Date == DateTime.Now.Date ? true : false;
            bool isCurrentBu = daybu.HasValue && daybu.Value.Date == DateTime.Now.Date ? true : false;
            var dbNowTime = _supplierUserService.GetNowTime();
            //var dbNowTime = DateTime.Now;
            if (isAll)
            {
                isCurrentSu = false;
                isCurrentBu = false;
            }
            if (type == 0)
            {
                // 商家弹窗(数据)：厂家同意预付申请(全局)
                if (popType.Contains(ApplyPrepayPop.Pop1.ToInt()))
                {
                    var supplier1 = _supplierUserService.GetSupplierConfirm(fxUserId, daybu, isCurrentBu);
                    var data1 = new ApplyPrepayPopModel();
                    data1.PopType = ApplyPrepayPop.Pop1;
                    data1.PopData = supplier1;
                    data.Add(data1);
                }
                // 商家弹窗(数据)：厂家拒绝预付申请(全局)
                if (popType.Contains(ApplyPrepayPop.Pop2.ToInt()))
                {
                    var supplier2 = _supplierUserService.GetSupplierReject(fxUserId, daybu, isCurrentBu);
                    var data2 = new ApplyPrepayPopModel();
                    data2.PopType = ApplyPrepayPop.Pop2;
                    data2.PopData = supplier2;
                    data.Add(data2);
                }
                // 商家弹窗(数据)：厂家默认开启预付[绑定店铺、绑定厂家]
                if (popType.Contains(ApplyPrepayPop.Pop3.ToInt()))
                {
                    var supplier3 = _supplierUserService.GetSupplierStartB(fxUserId, daybu, isCurrentBu);
                    var data3 = new ApplyPrepayPopModel();
                    data3.PopType = ApplyPrepayPop.Pop3;
                    data3.PopData = supplier3;
                    data.Add(data3);
                }
                _commonSettingService.Set(businessKye, dbNowTime.ToString(), systemShopId, true);
            }
            else
            {
                // 厂家弹窗(数据)：商家发起预付申请
                if (popType.Contains(ApplyPrepayPop.Pop4.ToInt()))
                {
                    var supplier4 = _supplierUserService.GetBusineApply(fxUserId, daySu, isCurrentSu);
                    var data4 = new ApplyPrepayPopModel();
                    data4.PopType = ApplyPrepayPop.Pop4;
                    data4.PopData = supplier4;
                    data.Add(data4);
                }

                // 厂家弹窗(数据)：厂家默认开启预付[绑定店铺、绑定厂家]
                if (popType.Contains(ApplyPrepayPop.Pop5.ToInt()))
                {
                    var supplier5 = _supplierUserService.GetSupplierStartS(fxUserId, daySu, isCurrentSu);
                    var data5 = new ApplyPrepayPopModel();
                    data5.PopType = ApplyPrepayPop.Pop5;
                    data5.PopData = supplier5;
                    data.Add(data5);
                }
                _commonSettingService.Set(supplierKey, dbNowTime.ToString(), systemShopId, true);
            }
            return SuccessResult(new { Total = data.Count, List = data });
        }



        /// <summary>
        /// 设置发货方式
        /// </summary>
        /// <param name="fxUserId">商家FxUserId</param>
        /// <param name="deliveryMode"></param>
        /// <returns></returns>
        public ActionResult SetDeliveryMode(int fxUserId, int deliveryMode)
        {
            try
            {
                SwitchSelfDbName();
                var supplierFxUserId = SiteContext.Current.CurrentFxUser.Id;
                var result = _supplierUserService.SetDeliveryMode(supplierFxUserId, fxUserId, deliveryMode);
                if (result.Success)
                {
                    return SuccessResult($"设置成功");
                }
                else
                    return FalidResult($"设置失败", result.Message);

            }
            catch (Exception ex)
            {
                Log.WriteError($"设置发货方式失败FxUserId:{fxUserId}:错误消息:{ex}");
                return FalidResult($"设置失败");

            }
        }

        [FxMigrateLockFilter()]
        public ActionResult PlatformAddShop(string pt)
        {
            if (pt.IsNullOrEmpty())
                return FalidResult("请先选择店铺平台");

            // 根据订购记录显示可订购的应用（2023-01-05 改为新增店铺都只取最新上架的分销应用）
            pt = pt.ToString2().ToLower();
            var appService = AppServiceFactory.GetAppService(pt);
            List<AppPlatformModel> fxApps = new List<AppPlatformModel>();
            if (pt == PlatformType.KuaiShou.ToString().ToLower())
            {
                var fxuserId = SiteContext.Current.CurrentFxUserId;
                //快手新老应用开通信息
                var isOpenNewApp = _shopService.GetOpenAppShops(fxuserId, "KuaiShou", new List<string> { CustomerConfig.KuaiShouNewFxAppKey });
                var isOpenOldApp = _shopService.GetOpenAppShops(fxuserId, "KuaiShou", new List<string> { CustomerConfig.KuaiShouFxAppKey });
                if (isOpenNewApp && isOpenOldApp)
                {
                    fxApps.Add(appService.getAppModel(CustomerConfig.KuaiShouFxAppKey));
                    fxApps.Add(appService.getAppModel(CustomerConfig.KuaiShouNewFxAppKey));
                }
                else
                {
                    if (isOpenOldApp)
                    {
                        fxApps.Add(appService.getAppModel(CustomerConfig.KuaiShouFxAppKey));
                    }
                    else
                    {
                        fxApps.Add(appService.getAppModel(CustomerConfig.KuaiShouNewFxAppKey));
                    }
                }
            }
            else if (pt == PlatformType.TouTiao.ToString().ToLower())
                fxApps.Add(appService.getAppModel(CustomerConfig.TouTiaoFxNewAppKey)); //头条添加新店铺使用新应用
            else if (pt == PlatformType.TouTiaoSaleShop.ToString().ToLower())
                fxApps.Add(appService.getAppModel(CustomerConfig.FxTouTiaoSaleShopAppKey));
            else if (pt == PlatformType.Pinduoduo.ToString().ToLower())
            {
                fxApps.Add(appService.getAppModel(CustomerConfig.PddFxAppKey));
                //追加打单应用
                var isUsePrintSystemApp = _commonSettingService.PddIsUsePrintSystemApp();
                if (isUsePrintSystemApp)
                    fxApps.Add(appService.getAppModel(CustomerConfig.PinduoduoAppKey));
            }
            else if (pt == PlatformType.XiaoHongShu.ToString().ToLower())
                fxApps.Add(appService.getAppModel(CustomerConfig.XiaoHongShuFXAppKey));
            else if (pt == PlatformType.DuXiaoDian.ToString().ToLower())
                fxApps.Add(appService.getAppModel(CustomerConfig.DuXiaoDianV2FxAppKey));
            else if (pt == PlatformType.Alibaba.ToString().ToLower())
            {
                fxApps.Add(appService.getAppModel(CustomerConfig.AlibabaAppKey));
                fxApps.Add(appService.getAppModel(CustomerConfig.AlibabaQingAppKey));
            }
            else if (pt == PlatformType.Jingdong.ToString().ToLower())
            {
                var IsJingDongTwoApp = _userFxService.CheckFxUserHasOldJingdongAppUnExpired(SiteContext.Current.CurrentFxUserId);
                if (IsJingDongTwoApp)
                {
                    fxApps.Add(appService.getAppModel(CustomerConfig.JingDongAppKey));
                }
                fxApps.Add(appService.getAppModel(CustomerConfig.JingDongFxAppKey));
            }
            // 这些平台年后在上新应用，暂时注释掉
            //else if (pt == PlatformType.Jingdong.ToString().ToLower())
            //    fxApp = appService.getAppModel(CustomerConfig.JingDongFxAppKey);
            //else if (pt == PlatformType.YouZan.ToString().ToLower())
            //    fxApp = appService.getAppModel(CustomerConfig.YouZanFxAppKey);
            //else if (pt == PlatformType.MoGuJie.ToString().ToLower() || pt == PlatformType.MeiLiShuo.ToString().ToLower())
            //    fxApp = appService.getAppModel(CustomerConfig.MoGuFxAppKey);
            //else if (pt == PlatformType.Suning.ToString().ToLower())
            //    fxApp = appService.getAppModel(CustomerConfig.SuningFxAppKey);
            //else if (pt == PlatformType.VipShop.ToString().ToLower())
            //    fxApp = appService.getAppModel(CustomerConfig.VipShopFxAppKey);
            //else if (pt == PlatformType.WeiDian.ToString().ToLower())
            //    fxApp = appService.getAppModel(CustomerConfig.WeiDianFxAppKey);
            else
                fxApps.Add(appService.getAppModel(""));
            //... 其他平台上分销应用后再添加

            return SuccessResult(new { Data = fxApps });

            //if (result != null)
            //    result = result.Where(w => w.Platform.ToLower() == pt.ToLower())?.ToList();
            //return SuccessResult(new { Data = result });
        }



        #region 拼多多店铺：是否开启境外单

        public ActionResult LoadMyPddShopList()
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var shops = _shopService.GetShopSelf(fxUserId, PlatformType.Pinduoduo.ToString());
            _shopService.GetIsOpenCrossBorder(shops);

            return SuccessResult(new { Total = shops.Count(), List = shops });
        }

        public ActionResult SetIsOpenCrossBorder(int shopId, int isOpenCrossBorder)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            _shopService.UpdateIsOpenCrossBorder(shopId, isOpenCrossBorder, fxUserId);
            return SuccessResult();
        }
        #endregion

        /// <summary>
        /// 解绑指定店铺前置检查：当前店铺是否存在1688待发货的预付款订单
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        public ActionResult Check1688UnBindShop(int Id)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var fxUserShop = _fxUserShopService.Get(Id);
            if (fxUserShop == null || fxUserShop.FxUserId != fxUserId)
                return FalidResult("店铺信息未找到");

            if (fxUserShop.PlatformType != PlatformType.Alibaba.ToString())
                return SuccessResult("校验通过");

            var unShopId = fxUserShop.ShopId;
            //检查是否是1688分销店铺
            var key = BusinessSettingKeys.SupplyBy1688.ShopBy1688;
            var systemShopId = SiteContext.Current.CurrentShopId;
            var aliShopId = new CommonSettingService().Get(key, systemShopId, false)?.Value.ToInt() ?? 0;
            if (aliShopId != unShopId)
                return SuccessResult("校验通过");

            //是否存在已付款待发货的预付款订单
            SwitchSelfDbName();
            var hasWaitSendOrder = new PurchaseOrderRelationService().IsHasWaitSendByPurchaseOrderShopId(unShopId);
            if (hasWaitSendOrder)
                return FalidResult("存在已付款待发货的预付款订单");

            return SuccessResult("校验通过");
        }

        /// <summary>
        /// 解绑商家合作关系前置检查：当前合作的商家是否存在1688待发货的预付款订单
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        public ActionResult Check1688UnBindAgent(int id)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var _bins = _supplierUserService.Get(id);
            if (_bins == null || _bins.SupplierFxUserId != fxUserId)
                return FalidResult("当前绑定信息不存在，请刷新重试");
            //检查是否是1688分销店铺
            var key = BusinessSettingKeys.SupplyBy1688.ShopBy1688;
            var systemShopId = SiteContext.Current.CurrentShopId;
            var aliShopId = new CommonSettingService().Get(key, systemShopId, false)?.Value.ToInt() ?? 0;
            if (aliShopId <= 0)
                return SuccessResult("校验通过");

            //是否存在已付款待发货的预付款订单
            SwitchSelfDbName();
            var hasWaitSendOrder = new PurchaseOrderRelationService().IsHasWaitSendByPurchaseOrderShopId(aliShopId, _bins.FxUserId);
            if (hasWaitSendOrder)
                return FalidResult("存在已付款待发货的预付款订单");

            return SuccessResult("校验通过");
        }

        /// <summary>
        /// 解绑厂家合作关系前置检查：当前合作的商家是否存在1688待发货的预付款订单
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        public ActionResult Check1688UnBindSupplier(int id)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var _bins = _supplierUserService.Get(id);
            if (_bins == null || _bins.FxUserId != fxUserId)
                return FalidResult("当前绑定信息不存在，请刷新重试");

            //是否存在已付款待发货的预付款订单
            SwitchSelfDbName();
            var hasWaitSendOrder = new PurchaseOrderRelationService().IsHasWaitSendBySupplierId(_bins.SupplierFxUserId, _bins.FxUserId);
            if (hasWaitSendOrder)
                return FalidResult("存在已付款待发货的预付款订单");

            return SuccessResult("校验通过");
        }

        /// <summary>
        /// 注销账号前置检查：当前用户是否存在1688待发货的预付款订单
        /// </summary>
        /// <returns></returns>
        public ActionResult Check1688PurchaseData()
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var systemShopId = SiteContext.Current.CurrentShopId;
            //检查是否是1688分销店铺
            var key = BusinessSettingKeys.SupplyBy1688.ShopBy1688;
            var aliShopId = new CommonSettingService().Get(key, systemShopId, false)?.Value.ToInt() ?? 0;
            if (aliShopId <= 0)
                return SuccessResult("校验通过");

            //是否存在已付款待发货的预付款订单
            SwitchSelfDbName();
            var hasWaitSendOrder = new PurchaseOrderRelationService().IsHasWaitSendByPurchaseOrderShopId(aliShopId);
            if (hasWaitSendOrder)
                return FalidResult("存在已付款待发货的预付款订单");

            return SuccessResult("校验通过");
        }

        /// <summary>
        /// 紧急解绑店铺检查身份验证
        /// </summary>
        /// <param name="model"></param>
        /// <param name="isSimpleLogin">是否使用短信验证码登录</param>
        /// <returns></returns>
        [FxMigrateLockFilter()]
        public ActionResult CheckAccount(UserLoginModel model, bool isSimpleLogin = false)
        {
            if (model == null) return FalidResult("数据为空");
            var curUser = SiteContext.Current.CurrentFxUser;

            // 图形验证码校验
            if (!isSimpleLogin && !CheckValidCode(model.ValidCode)) return FalidResult("验证码不正确，请重新输入");

            if (model.Mobile.IsNullOrEmpty()) return FalidResult("手机号不能为空");
            if (model.Mobile != curUser.Mobile) return FalidResult("手机号与该账号的手机号不一致");

            // isSimpleLogin为true时,不校验密码
            if (!isSimpleLogin && model.Password.IsNullOrEmpty()) return FalidResult("密码不能为空");
            if (!isSimpleLogin && curUser.Password != DES.EncryptUrl(model.Password, CustomerConfig.LoginCookieEncryptKey))
                return FalidResult("密码不正确");

            // 短信验证码校验
            if (isSimpleLogin)
            {
                if (_userService.CheckPhoneCodeIsValid(model.Mobile, model.ValidCode) == 1)
                    return FalidResult("短信验证码不正确，请重新获取");
                if (_userService.CheckPhoneCodeIsValid(model.Mobile, model.ValidCode) == 2)
                    return FalidResult("短信验证码已过期，请重新获取");
                if (_userService.CheckPhoneCodeIsValid(model.Mobile, model.ValidCode) == 3)
                    return FalidResult("短信验证码已被使用，请重新获取");
                _userService.UpdateVeriCodeIsEmploy(new UserRegisterModel { Mobile = model.Mobile, MobileMeessageCode = model.MobileMeessageCode });
            }

            return SuccessResult("验证通过");
        }

        /// <summary>
        /// 检查登录验证码是否正确
        /// </summary>
        /// <param name="code">验证码</param>
        /// <returns></returns>
        private bool CheckValidCode(string code)
        {
            if (Utility.Other.VerifyCode.CheckVerifyCode(code, true))
                return true;
            return false;
        }

        /// <summary>
        /// 申请解绑
        /// </summary>
        /// <param name="Id"></param>
        /// <param name="isUrgent">是否紧急解绑</param>
        /// <param name="platform">店铺平台类型</param>
        /// <param name="appKey"></param>
        /// <returns></returns>
        [FxAuthorize]
        [FxMigrateLockFilter()]
        public ActionResult SetUnBindShop(int Id, bool isUrgent = false, string platform = null, string appKey = null)
        {
            FxUnBindTaskService unbindservice = new FxUnBindTaskService();
            try
            {
                var fxUserId = SiteContext.Current.CurrentFxUserId;
                var fxUserShop = _fxUserShopService.Get(Id);
                if (fxUserShop == null || fxUserShop.FxUserId != fxUserId)
                    return FalidResult("店铺信息未找到");
                var systemshopId = SiteContext.Current.CurrentShopId;
                var fxUser = _userFxService.Get(fxUserId);
                if (fxUser == null)
                    return FalidResult("用户未找到");

                var result = false;
                var isApplication = false;

                // 判断该平台是否属于铺货平台内
                if (CustomerConfig.FxListingSupportPlatformTypes.Contains(fxUserShop.PlatformType))
                {
                    // 获取该店铺所有的应用
                    var shop = new ShopExtensionService("all").GetShopAppKey(new List<int> { fxUserShop.ShopId });
                    // 按AppKey是否包含在铺货应用列表中，进行分组
                    var shopList = shop.GroupBy(x =>
                            CustomerConfig.FxListingAppKeys.Contains(x.AppKey))
                        .ToDictionary(x => x.Key, x => x.ToList());
                    // 判断该店铺是否含有铺货应用
                    var hasListing = new ShopService("all").GetAppShopAuth(fxUserId, fxUserShop.ShopId, CustomerConfig.FxListingAppKeys);
                    if (hasListing && shopList.Count > 1)
                    {
                        isApplication = true;
                        // 走应用解绑流程
                        result = unbindservice.SetApplication(fxUserShop.ShopId, fxUserShop.FxUserId, systemshopId, fxUserShop.PlatformType, fxUser.Mobile, appKey, isUrgent);
                    }
                }

                if (isApplication == false)
                    result = unbindservice.SetShop(fxUserShop.ShopId, fxUserShop.FxUserId, systemshopId,
                        fxUserShop.PlatformType, fxUser.Mobile, isUrgent);

                if (!result) return FalidResult("申请解绑失败");
                if (!isUrgent) return SuccessResult("申请解绑成功");
                if (appKey == null || CustomerConfig.FxListingAppKeys.Contains(appKey)) return SuccessResult("申请解绑成功");

                // 只有非应用解绑才会走到这里
                // 根据路径流信息获取路径流节点信息
                var nodeList = _pathFlowNodeService.GetListByPathFlowCodes(fxUserShop.ShopId, fxUserId, platform);
                if (nodeList.IsNotNullAndAny())
                {
                    _pathFlowNodeService.UpdateStatus(nodeList, fxUserId, -1, fxUserShop.ShopId, platform);
                }

                return SuccessResult("申请解绑成功");
            }
            catch (LogicException lex)
            {
                return FalidResult(lex.Message);
            }
            catch (Exception ex)
            {
                Log.WriteError($"SetUnBindShop:{ex}");
                return FalidResult("系统异常");
            }
        }

        /// <summary>
        /// 取消解绑
        /// </summary>
        /// <param name="Id"></param>
        /// <param name="appKey"></param>
        /// <returns></returns>
        [FxMigrateLockFilter()]
        public ActionResult CancelUnBindShop(int Id, string appKey = null)
        {
            FxUnBindTaskService unbindservice = new FxUnBindTaskService();
            try
            {
                var fxuserId = SiteContext.Current.CurrentFxUserId;
                var fxUserShop = _fxUserShopService.Get(Id);
                if (fxUserShop == null || fxUserShop.FxUserId != fxuserId)
                    return FalidResult("店铺信息未找到");
                var result = unbindservice.CancelShop(fxUserShop.ShopId, fxUserShop.FxUserId, appKey ?? "");
                if (result)
                    return SuccessResult("取消解绑成功");
                return FalidResult("取消解绑失败");
            }
            catch (LogicException lex)
            {
                return FalidResult(lex.Message);
            }
            catch (Exception ex)
            {
                Log.WriteError($"CancelUnBindShop:{ex.Message}");
                return FalidResult("系统异常");
            }
        }

        /// <summary>
        /// 切换到自己的分区
        /// </summary>
        public void SwitchSelfDbName()
        {
            if (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString())
            {
                var dbName = Request["dbname"] + "";
                if (!string.IsNullOrEmpty(dbName))
                {
                    try
                    {
                        dbName = DES.DecryptDES(dbName, CustomerConfig.LoginCookieEncryptKey);
                    }
                    catch (Exception ex) { }
                }
                var selfDbName = SiteContext.Current.CurrentDbSettingConfig?.DbNameConfig?.DbName ?? "";
                if (dbName != selfDbName && !string.IsNullOrEmpty(selfDbName))
                {
                    var sc = new SiteContext(SiteContext.Current.CurrentFxUser, selfDbName);
                    selfDbName = DES.EncryptDES(selfDbName, CustomerConfig.LoginCookieEncryptKey);
                    ViewBag.DbName = selfDbName;
                }
            }
        }

        /// <summary>
        /// 查询预付状态变更日志
        /// </summary>
        /// <param name="fxUserId">商家用户Id</param>
        /// <returns></returns>
        public ActionResult GetPrepayStatusChangeRecord(int fxUserId)
        {
            var curFxUserId = SiteContext.Current.CurrentFxUserId;
            var list = new PrepayStatusChangeRecordService().GetList(curFxUserId, fxUserId);
            if (list != null)
            {
                return Json(list);
            }
            return Json("");
        }

        /// <summary>
        /// 绑定厂家页面查询绑定日志
        /// </summary>
        /// <param name="supplierFxUserId"></param>
        /// <returns></returns>
        public ActionResult GetCooperateStatusBySupplier(int supplierFxUserId)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var cooperateList = _cooperateStatusRecordService.GetCooperateStatusBySupplier(fxUserId, supplierFxUserId).ToList();
            return SuccessResult(cooperateList);
        }

        /// <summary>
        /// 绑定商家页面查询绑定日志
        /// </summary>
        /// <param name="agentId"></param>
        /// <returns></returns>
        public ActionResult GetCooperateStatusByAgent(int agentId)
        {
            var supplierFxUserId = SiteContext.Current.CurrentFxUserId;
            var cooperateList = _cooperateStatusRecordService.GetCooperateStatusByAgent(supplierFxUserId, agentId).ToList();
            return SuccessResult(cooperateList);
        }


        /// <summary>
        /// 获取对应厂家的名片以及备注
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [FxAuthorize()]
        public ActionResult GetSupplierCardById(int id)
        {
            // 是否是当前用户关联的厂家
            var suppliers = _supplierService.GetAgentOrSupplierIds(SiteContext.GetCurrentFxUserId());
            if (!suppliers.Contains(id))
            {
                return FalidResult("服务器繁忙，请稍会儿再试");
            }
            return SuccessResult(_businessCardService.GetViewModel(id));
        }


        /// <summary>
        /// 获取对应商家的名片以及备注
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [FxAuthorize()]
        public ActionResult GetAgentCardById(int id)
        {
            // 是否是当前用户关联的商家
            var agents = _supplierService.GetAgentOrSupplierIds(SiteContext.GetCurrentFxUserId(), true);
            if (!agents.Contains(id))
            {
                return FalidResult("服务器繁忙，请稍会儿再试");
            }
            return SuccessResult(_businessCardService.GetViewModel(id));
        }

        /// <summary>
        /// 增加或更新名片备注
        /// </summary>
        /// <param name="fxUserOrSupplierId">厂家或商家的Id</param>
        /// <param name="remark">备注</param>
        /// <returns></returns>
        public ActionResult AddOrUpdateBusinessCardRemark(int fxUserOrSupplierId, string remark)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var result = _businessCardRemarkService.AddOrUpdateBusinessCardRemark(fxUserOrSupplierId, fxUserId, remark);

            return result ? SuccessResult("更新成功") : FalidResult("更新失败");
        }

        /// <summary>
        /// 增加或更新合作评价
        /// </summary>
        /// <param name="forFxUserId"></param>
        /// <param name="tags"></param>
        /// <param name="remark"></param>
        /// <returns></returns>
        public ActionResult AddCooperateEvaluation(int forFxUserId, string tags, string remark)
        {
            var curFxUserId = SiteContext.Current.CurrentFxUserId;
            var result = _cooperateEvaluateService.AddCooperateEvaluation(forFxUserId, curFxUserId, tags, remark);

            return result ? SuccessResult("保存成功") : FalidResult("保存失败");
        }

        /// <summary>
        /// 删除合作评价(逻辑删除)
        /// </summary>
        /// <param name="forFxUserId"></param>
        /// <returns></returns>
        public ActionResult DelCooperateEvaluation(int forFxUserId)
        {
            var curFxUserId = SiteContext.Current.CurrentFxUserId;
            var cooperateEvaluate = _cooperateEvaluateService.GetEvaluationByFxUserId(forFxUserId, curFxUserId);
            if (cooperateEvaluate == null) return FalidResult("合作评价不存在");

            cooperateEvaluate.IsDeleted = true;
            cooperateEvaluate.UpdateTime = DateTime.Now;
            var result = _cooperateEvaluateService.Update(cooperateEvaluate);

            return result ? SuccessResult("删除成功") : FalidResult("删除失败");
        }

        /// <summary>
        /// 判断用户是否有发货记录
        /// </summary>
        /// <param name="forFxUserId"></param>
        /// <param name="isSupplier"></param>
        /// <returns></returns>
        public ActionResult CheckUserInfo(int forFxUserId, bool isSupplier)
        {
            var curFxUserId = SiteContext.Current.CurrentFxUserId;
            var ent = isSupplier ? _supplierUserService.GetByUser(forFxUserId, curFxUserId) : _supplierUserService.GetByUser(curFxUserId, forFxUserId);
            var isHasSent = false;
            if (ent != null) isHasSent = ent.IsHasSended ?? false;

            var userInfo = _supplierUserService.GetUserInfoByUserId(forFxUserId, curFxUserId, isSupplier);
            var infoStr = isSupplier ? userInfo?.SupplierMobileAndRemark : userInfo?.AgentMobileAndRemark;

            if (isHasSent)
            {
                return SuccessResult(new
                {
                    IsSent = true,
                    Info = infoStr
                });
            }

            // 切换角色再次查询是否有发货记录
            ent = isSupplier ? _supplierUserService.GetByUser(curFxUserId, forFxUserId) : _supplierUserService.GetByUser(forFxUserId, curFxUserId);
            if (ent != null) isHasSent = ent.IsHasSended ?? false;

            try
            {
                // 如果实体中没有标记为已发货，则检查业务库数据
                if (!isHasSent)
                {
                    isHasSent = _sendHistoryService.IsExistSent(forFxUserId, curFxUserId) || _sendHistoryService.IsExistSent(curFxUserId, forFxUserId);
                    if (isHasSent)
                    {
                        ent.IsHasSended = true;
                        _supplierUserService.Update(ent);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"CheckUserInfo异常：{ex.Message}");
                isHasSent = false;
            }

            return SuccessResult(new
            {
                IsSent = isHasSent,
                Info = infoStr
            });
        }


        /// <summary>
        /// 加载当前失效店铺
        /// </summary>
        /// <returns></returns>
        public ActionResult LoadMyInvalidShopList()
        {
            var queryType = Request["queryType"];   //查询类型，当前只有顶部失效组件用到
            var shopType = Request["shopType"];   //查询类型，当前只有顶部失效组件用到
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var shopId = SiteContext.Current.CurrentShopId;
            int AboutToExpireCount = 0;
            try
            {
                //拼多多是否使用打单应用
                var isUsePrintSystemApp = _commonSettingService.PddIsUsePrintSystemApp();

                //过期服务店铺或授权过期
                var myExpiredShop = _fxUserShopService.GetCurrentExpireShopByFxUserId(fxUserId, queryType, shopId);
                myExpiredShop.Item1?.ForEach(s => { s.PddIsUsePrintSystemApp = isUsePrintSystemApp; });

                var expiredShops = myExpiredShop.Item1.OrderByDescending(x => x.Id).Select(x => new
                {
                    x.ShopId,
                    x.NickName,
                    x.PlatformType,
                    x.Status,
                    x.FxUserId,
                    x.PlatformTypeName,
                    IsAuthUrl = x.AuthUrl.IsNotNullOrEmpty(),
                    x.AuthUrl,
                    x.PlatformAuthUrl,
                    x.IsExpire,
                    x.PlatformPayUrl,
                    x.PlatformPayUrl2,
                    x.TouTiaoOldOrNew,
                    x.PddIsUsePrintSystemApp
                }).ToList<object>(); //.ToJson()

                AboutToExpireCount = myExpiredShop.Item2.Count;

                //获取跨境店铺授权店铺过期
                var crossBorderExpiredShop = _fxUserForeignShopService.GetCurrentExpireShopByFxUserId(fxUserId, queryType, shopId);
                ///用户开启了跨境站点并且选择的店铺类型是跨境
                //if (SiteContext.Current.IsShowCrossBorder
                if (_commonSettingService.IsShowCrossBorder()
                    && shopType == "2")
                    AboutToExpireCount = crossBorderExpiredShop.Item2.Count;
                if (crossBorderExpiredShop.Item1.Any())
                {
                    foreach (var x in crossBorderExpiredShop.Item1)
                    {
                        expiredShops.Add(new
                        {
                            x.ShopId,
                            x.NickName,
                            x.PlatformType,
                            x.Status,
                            x.FxUserId,
                            x.PlatformTypeName,
                            IsAuthUrl = x.AuthUrl.IsNotNullOrEmpty(),
                            x.AuthUrl,
                            PlatformAuthUrl = x.AuthUrl,
                            x.IsExpire,
                            x.PlatformPayUrl,
                            PlatformPayUrl2 = string.Empty
                        });
                    }
                }

                return SuccessResult(new
                {
                    expiredShops = expiredShops.ToJson(),
                    aboutCount = AboutToExpireCount,//myExpiredShop.Item2.Count //快过期店铺数量
                });
            }
            catch (Exception ex)
            {
                return FalidResult("获取失效店铺出错" + ex.Message);
            }
        }

        /// <summary>
        /// 获取快手店铺平台授权是否存在被绑定状态
        /// </summary>
        /// <returns>返回授权状态，如果已被绑定则返回绑定信息</returns>
        public ActionResult GetKuaiShouAuthStatus()
        {
            try
            {
                var fxUserId = SiteContext.Current.CurrentFxUserId;
                var redisKey = $"DianGuanJiaApp:FenDan:KuaiShouAuthFailed:{fxUserId}";
        
                // 判断是否存在Key
                if (!RedisHelper.Exists(redisKey))
                    return SuccessResult(false);
        
                // 如果存在，获取Redis值
                var redisValue = RedisHelper.Get(redisKey);
                if (string.IsNullOrEmpty(redisValue))
                    return SuccessResult(false);
                Log.Debug(() => $"获取到的Redis值：{redisValue}");
                RedisHelper.Del(redisKey);
            
                // 从字符串json转换为对象
                Dictionary<string, string> shopData;
                try 
                {
                    shopData = redisValue.ToObject<Dictionary<string, string>>();
                }
                catch (Exception e)
                {
                    Log.WriteError($"GetKuaiShouAuthStatus异常：{redisValue} : {e.Message}");
                    return SuccessResult(true);
                }
        
                // 判断是否存在必要的店铺信息
                if (!shopData.ContainsKey("ShopId") || !shopData.ContainsKey("FxUserMobile"))
                    return SuccessResult(false);
            
                // 获取店铺信息
                var shopId = shopData["ShopId"].ToInt();
                var mobile = shopData["FxUserMobile"];
        
                // 检查是否有运单编码
                var isExist = new WaybillCodeService().IsExistWaybillCodeByShopId(shopId, CloudPlatformType.Alibaba.ToString());
                var errorCode = !isExist ? "CAN_UNBIND" : "NOT_UNBIND";
        
                // 立即绑定才需要设置Redis
                if (!isExist)
                {
                    // 新增Redis，防止越权
                    var redisKeyByShop = $"DianGuanJiaApp:FenDan:AuthFailed:{fxUserId}:{shopId}";
                    RedisHelper.Set(redisKeyByShop, true, 60 * 5);
                }
     
                return FalidResult($"{mobile}", errorCode, shopId);
            }
            catch (Exception ex)
            {
                Log.WriteError($"GetKuaiShouAuthStatus异常: {ex.Message}");
                return SuccessResult(true);
            }
        }

        /// <summary>
        /// 立即绑定（先紧急解绑再处理店铺信息）
        /// </summary>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public ActionResult UrgentBindShop(int shopId)
        {
            // 避免越权
            var fxUserMobile = SiteContext.Current.CurrentFxUser.Mobile;
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var systemShopId = SiteContext.Current.CurrentShopId;
            var redisKeyByShop = $"DianGuanJiaApp:FenDan:AuthFailed:{fxUserId}:{shopId}"; 
            
            // 判断是否存在Key
            if (!RedisHelper.Exists(redisKeyByShop))
                return FalidResult("无绑定权限，绑定失败，请刷新重试");
            
            // 获取店铺信息
            var shop = new ShopService().Get(shopId);
            if (shop == null) return FalidResult("店铺信息未找到");

            var fxUserShop = new FxUserShopService().GetFxUserShop(shop.ShopId, shop.PlatformType);
            if (fxUserShop == null) return FalidResult("店铺信息未找到");
            
            var unbindTaskService = new FxUnBindTaskService();

            // 新增紧急解绑任务
            var unbindService = new FxUnBindTaskService();
            var result = unbindService.SetShop(fxUserShop.ShopId, fxUserShop.FxUserId, systemShopId, fxUserShop.PlatformType, fxUserMobile, true);
            if (result == false) return FalidResult("解绑失败，请联系客服");
            
            var task = unbindTaskService.GetByShopId(shopId, fxUserShop.FxUserId);
            if (task == null) return FalidResult("绑定失败，请刷新重试");
            RedisHelper.Del(redisKeyByShop);

            // 设置超时时间为3分钟
            var timeoutTime = DateTime.Now.AddMinutes(3);
            while (DateTime.Now < timeoutTime)
            {
                // 轮询查询绑定状态，不等于Running
                task = unbindTaskService.GetByShopId(shopId, fxUserShop.FxUserId, UnBindTaskState.Runing);

                if (task.TaskState == UnBindTaskState.Faild)
                {
                    Log.WriteError($"解绑失败，{task.ErrorMsg}");
                    return FalidResult("解绑失败，请登录原账号进行解绑");
                }

                if (task.TaskState == UnBindTaskState.Cancel)
                    return FalidResult("解绑已取消，请刷新重试");

                if (task.TaskState == UnBindTaskState.Success)
                {
                    // 绑定店铺
                    _fxUserShopService.CreateNewFxUserShop(new FxUserShop
                    {
                        FxUserId = fxUserId,
                        ShopId = shop.Id,
                        PlatformType = shop.PlatformType,
                        NickName = shop.NickName,
                        CreateTime = DateTime.Now,
                        AuthTime = shop.ExpireTime ?? DateTime.Now,
                        Status = FxUserShopStatus.Binded,
                    });
                    
                    return SuccessResult("绑定成功");
                }
                
                // 延迟1.5s
                Thread.Sleep(1500);
            }

            return FalidResult("解绑超时，请刷新重试");
        }
        
        /// <summary>
        /// 申请担保交易
        /// </summary>
        /// <param name="_model"></param>
        /// <returns></returns>
        [LogForOperatorFilter("申请担保交易")]
        [FxMigrateLockFilter()]
        [FxAuthorize()]
        public ActionResult ApplyPrepay(int supplierFxUserId, int applyPrepayStatus)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var result = _supplierUserService.SetApplyPrepay(supplierFxUserId, fxUserId, applyPrepayStatus);
            if (!result)
            {
                return FalidResult("申请失败，请刷新重试");
            }
            else
            {
                #region 消息埋点

                // 场景：商家对厂家申请开启预付 
                new SiteMessageService().ConvertToMessage(new SiteMessageContentModel
                {
                    FirstType = SiteMessageTypeEnum.PrimaryType.Cooperation.ToString(),
                    SecondType = SiteMessageTypeEnum.PrimaryType.Cooperation.ToString(),
                    FromUserId = fxUserId,
                    ToUserIds = new List<int> { supplierFxUserId },
                    JumpUrl = "/Partner/MyAgent",
                    Content = "您的商家[商家id]申请使用线上担保交易，请及时处理。",
                    TemplateParas = new { 商家id = fxUserId.ToString() }.ToJsonExt()
                });

                #endregion

                return SuccessResult("申请成功！");
            }
        }


        /// <summary>
        /// 设置预付状态
        /// </summary>
        /// <param name="fxUserId">商家FxUserId</param>
        /// <param name="isPrePay">1:开启、其它值:关闭</param>
        /// <returns></returns>
        private bool CurrentSetPrePay(int fxUserId, int isPrePay)
        {
            var strTitle = "批量开启预付";
            if (isPrePay != 1)
                strTitle = "批量关闭预付";
            try
            {
                //切换到自己的分区
                SwitchSelfDbName();

                var supplierFxUserId = SiteContext.Current.CurrentFxUser.Id;
                var systemShopId = SiteContext.Current.CurrentLoginShop.Id;
                var result = _supplierUserService.SetPrePay(supplierFxUserId, fxUserId, isPrePay, systemShopId);
                if (result.Success)
                {
                    var changeModel = (ChangePrePayForCheckModel)result.ReturnData;

                    #region 成功后还要保存到京东和拼多多平台、抖店云平台
                    var apiUrl = "/ChangePrePayApi/SetPrePay";
                    var aliCloudHost = CustomerConfig.DefaultFenFaSystemUrl.TrimEnd("/") + apiUrl;
                    var pddCloudHost = CustomerConfig.PinduoduoFenFaSystemUrl.TrimEnd("/") + apiUrl;
                    var jdCloudHost = CustomerConfig.JingdongFenFaSystemUrl.TrimEnd("/") + apiUrl;
                    var ttCloudHost = CustomerConfig.ToutiaoFenFaSystemUrl.TrimEnd("/") + apiUrl;

                    //支持1688代销的平台
                    var fx1688SupportPlatformTypes = CustomerConfig.Fx1688SupportPlatformTypes;

                    //分单系统主站点在阿里云，所有分发到朵朵云和京东云、抖店云平台
                    if (!string.IsNullOrEmpty(CustomerConfig.PinduoduoFenFaSystemUrl) && aliCloudHost != pddCloudHost && (fx1688SupportPlatformTypes.Contains(PlatformType.Pinduoduo.ToString()) || fx1688SupportPlatformTypes.Contains(PlatformType.KuaiTuanTuan.ToString())))
                        Common.PostFxSiteApi<ChangePrePayForCheckModel, bool>(pddCloudHost, supplierFxUserId, changeModel, "跨云平台预付状态变化-订单审核处理");

                    if (!string.IsNullOrEmpty(CustomerConfig.JingdongFenFaSystemUrl) && aliCloudHost != jdCloudHost && fx1688SupportPlatformTypes.Contains(PlatformType.Jingdong.ToString()))
                        Common.PostFxSiteApi<ChangePrePayForCheckModel, bool>(jdCloudHost, supplierFxUserId, changeModel, "跨云平台预付状态变化-订单审核处理");

                    if (!string.IsNullOrEmpty(CustomerConfig.ToutiaoFenFaSystemUrl) && aliCloudHost != ttCloudHost && fx1688SupportPlatformTypes.Contains(PlatformType.TouTiao.ToString()))
                        Common.PostFxSiteApi<ChangePrePayForCheckModel, bool>(ttCloudHost, supplierFxUserId, changeModel, "跨云平台预付状态变化-订单审核处理");
                    #endregion
                    return true;
                }
                else
                    return false;

            }
            catch (Exception ex)
            {
                Log.WriteError($"{strTitle}失败FxUserId:{fxUserId}:错误消息:{ex}");
                return false;

            }
        }

        /// <summary>
        /// 手动更新服务到期时间，会预先检查授权信息是否有效
        /// </summary>
        /// <param name="shopId"></param>
        /// <param name="appKey">为空就是获取Shop表的授权来更新否则读ShopExtension的授权</param>
        /// <returns></returns>
        public ActionResult HandSyncExpireTime(int shopId, string appKey = "")
        {
            var newTime = _shopService.HandSyncShopExpireTime(shopId, appKey);
            if (newTime != null)
            {
                return SuccessResult(newTime.Value.ToString("yyyy-MM-dd HH:mm:ss"));
            }
            return FalidResult("更新失败");
        }
        public ActionResult CheckQualification()
        {
            return View();
        }
        public ActionResult SubmitEvaluates()
        {
            return View();
        }

    }

    /// <summary>
    /// 授权管理
    /// 跨境相关的功能
    /// </summary>
    public partial class PartnerController
    {
        private FxUserForeignShopService _fxUserForeignShopService = new FxUserForeignShopService();

        // GET: Partner
        public ActionResult CrossBorder()
        {
            var token = Request.QueryString["token"];
            ViewBag.DefaultShopId = Request["shopId"].ToString2();
            ViewBag.SupportPlatform = CustomerConfig.GetFxUserCrossBorderPlatform(token).ToJson();
            ViewBag.AvailableCountInfo = null;//checkResult.ToJson();
            ViewBag.IsAddAvailable = true;

            return View();
        }

        /// <summary>
        /// 加载绑定的跨境店铺
        /// </summary>
        /// <returns></returns>
        public ActionResult LoadCrossBorderShop()
        {
            var fxuserId = SiteContext.Current.CurrentFxUserId;
            var agentShops = _fxUserForeignShopService.GetShopsByFxUserId(fxuserId);
            var shopLimit = 0;// new CommonSettingService().Get("/ErpWeb/BindShopLimit", 0)?.Value.ToInt() ?? 1000;
            if (agentShops != null && agentShops.Any())
            {
                //初始化tiktok业务库
                var fxDbConfig = new FxDbConfig
                {
                    FxUserId = SiteContext.Current.CurrentFxUserId,
                    SystemShopId = SiteContext.Current.CurrentShopId,
                    DbCloudPlatform = CloudPlatformType.ChinaAliyun.ToString(),
                    FromFxDbConfig = 1,
                    Status = "authshop"
                };
                new FxDbConfigService().TryToCreateCloudFxDbConfig(new List<FxDbConfig> { fxDbConfig },
                    new List<string> { CloudPlatformType.ChinaAliyun.ToString() });
            }
            return SuccessResult(new { shops = agentShops, limt = shopLimit });
        }

        #region 获取绑定的跨境店铺

        public ActionResult LoadCrossBorderShopList(FxUserShopQueryModel _reqModel)
        {
            var shopId = Request["ShopId"].ToInt();
            if (shopId > 0 && _reqModel.ShopId == 0)
                _reqModel.ShopId = shopId;
            _reqModel.FxUserId = SiteContext.Current.CurrentFxUserId;
            _reqModel.QueryPageType = 1;
            _reqModel.IsShowAccount = true;
            var tutles = _fxUserForeignShopService.GetList(_reqModel);
            var newResult = tutles.Item2;
            if (newResult.Any() == false)
                return SuccessResult(new { Total = 0, List = newResult });

            var shopIds = newResult.Select(x => x.ShopId).Distinct().ToList();

            var subShopIds = newResult.Where(f => f.SubDatas != null && f.SubDatas.Any())
                .SelectMany(f => f.SubDatas.Select(x => x.ShopId)).Distinct().ToList();

            if (subShopIds.Any())
                shopIds.AddRange(subShopIds);

            //获取店铺解绑申请信息
            var unbindservice = new FxUnBindTaskService();
            var unbindtasks = unbindservice.GetShops(shopIds, _reqModel.FxUserId);
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            Parallel.ForEach(newResult, new ParallelOptions { MaxDegreeOfParallelism = 10 }, (r) =>
            {
                try
                {
                    var unbindTask = unbindtasks?.OrderByDescending(x => x.CreateTime).FirstOrDefault(x => x.ShopId == r.ShopId && x.FxUserId == fxUserId);
                    if (unbindTask != null)
                        r.UnBindTaskState = unbindTask.TaskState.ToInt(); //r.IsUnBind = true;
                    else
                        r.UnBindTaskState = -1; // 申请解绑
                    if (!string.IsNullOrEmpty(unbindservice.TimeCheck()))
                        r.IsHideUnBindButton = true;
                    if (r.SubDatas != null)
                    {
                        r.SubDatas.ForEach(s =>
                        {
                            try
                            {
                                var unbindTaskBySubData = unbindtasks?.OrderByDescending(x => x.CreateTime).FirstOrDefault(x => x.ShopId == s.ShopId && x.FxUserId == fxUserId);
                                if (unbindTaskBySubData != null)
                                    s.UnBindTaskState = unbindTaskBySubData.TaskState.ToInt(); //r.IsUnBind = true;
                                else
                                    s.UnBindTaskState = -1; // 申请解绑
                                if (!string.IsNullOrEmpty(unbindservice.TimeCheck()))
                                    s.IsHideUnBindButton = true;
                            }
                            catch { }
                        });
                    }
                }
                catch { }
            });

            return SuccessResult(new { Total = tutles.Item1, List = newResult });
        }

        #endregion

        /// <summary>
        /// 申请解绑
        /// </summary>
        /// <param name="shopid"></param>
        /// <returns></returns>
        [FxMigrateLockFilter()]
        public ActionResult SetUnBindCrossBorderShop(int Id)
        {
            FxUnBindTaskService unbindservice = new FxUnBindTaskService();
            try
            {
                var fxuserId = SiteContext.Current.CurrentFxUserId;
                var fxUserForeignShop = _fxUserForeignShopService.Get(Id);
                if (fxUserForeignShop == null || fxUserForeignShop.FxUserId != fxuserId)
                    return FalidResult("店铺信息未找到");
                var systemshopId = SiteContext.Current.CurrentShopId;// _shopService.GetFxSystemShopByFxId(fxuserId);
                //if (systemshop == null)
                //    return FalidResult("用户系统店铺未找到");
                var fxuser = _userFxService.Get(fxuserId);
                if (fxuser == null)
                    return FalidResult("用户未找到");
                var result = unbindservice.SetShop(fxUserForeignShop.ShopId, fxUserForeignShop.FxUserId, systemshopId, fxUserForeignShop.PlatformType, fxuser.Mobile);
                if (result)
                    return SuccessResult("申请解绑成功");
                return FalidResult("申请解绑失败");
            }
            catch (LogicException lex)
            {
                return FalidResult(lex.Message);
            }
            catch (Exception ex)
            {
                Log.WriteError($"SetUnBindShop:{ex.Message}");
                return FalidResult("系统异常");
            }
        }

        /// <summary>
        /// 取消解绑
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [FxMigrateLockFilter()]
        public ActionResult CancelUnBindCrossBorderShop(int Id, string appKey)
        {
            FxUnBindTaskService unbindservice = new FxUnBindTaskService();
            try
            {
                var fxuserId = SiteContext.Current.CurrentFxUserId;
                var fxUserShop = _fxUserForeignShopService.Get(Id);
                if (fxUserShop == null || fxUserShop.FxUserId != fxuserId)
                    return FalidResult("店铺信息未找到");
                var result = unbindservice.CancelShop(fxUserShop.ShopId, fxUserShop.FxUserId, appKey ?? "");
                if (result)
                    return SuccessResult("取消解绑成功");
                return FalidResult("取消解绑失败");
            }
            catch (LogicException lex)
            {
                return FalidResult(lex.Message);
            }
            catch (Exception ex)
            {
                Log.WriteError($"CancelUnBindShop:{ex.Message}");
                return FalidResult("系统异常");
            }
        }

    }
}