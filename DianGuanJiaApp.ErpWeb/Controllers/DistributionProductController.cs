using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Threading.Tasks;
using System.Web.Mvc;
using DianGuanJiaApp.Data;

namespace DianGuanJiaApp.ErpWeb.Controllers
{
    /// <summary>
    /// 分销商品相关controller
    /// </summary>
    [SessionState(System.Web.SessionState.SessionStateBehavior.Disabled)]
    public class DistributionProductController : BaseController
    {
        private CommonSettingService _commonSettingService = new CommonSettingService();
        private DistributorProductService _service = new DistributorProductService();
        private DistributorProductMappingService dpMappingService = new DistributorProductMappingService();
        private DistributorProductSkuMappingService dpSkuMappingService = new DistributorProductSkuMappingService();
        private ProductFxService _productFxService = new ProductFxService();
        private SupplierUserService _supplierUserService = new SupplierUserService();
        private readonly FxUserShopService _fxUserShopService = new FxUserShopService();
        /// <summary>
        /// 分销品列表
        /// 供应商角色
        /// </summary>
        /// <returns></returns>
        [FxAuthorize(FxPermission.ListBySupplier)]
        public ActionResult ListBySupplier()
        {
            //1.检查当前用户是否有设置 1688 下单店铺
            //见开放方案，第三章第三小节（下单店铺设置）：https://doc.weixin.qq.com/doc/w3_APIAwgZGAEonO8YyXPWQL06RYZE6k
            //var key = BusinessSettingKeys.SupplyBy1688.ShopBy1688;
            //var shopId = SiteContext.Current.CurrentShopId;
            //var supplier1688ShopSetting = _commonSettingService.Get(key, shopId);
            //if (supplier1688ShopSetting == null || string.IsNullOrWhiteSpace(supplier1688ShopSetting.Value) || supplier1688ShopSetting.Value == "0")
            //{
            //    ViewBag.Supplier1688Shop = 0; //未设置1688下单店铺
            //    //ViewBag.Supplier1688Shop = 1835; //为了展示测试数据
            //    var shopsBy1688 = _fxUserShopService.GetCurrentUserShopsByAlibaba();
            //    ViewBag.ShopsBy1688 = shopsBy1688?.OrderByDescending(m => m.CreateTime).ToList();
            //}
            //else
            //{
            //    ViewBag.Supplier1688Shop = supplier1688ShopSetting.Value; //有设置1688下单店铺
            //    #region 供应商查看分销品页面，切换到自己所属分区
            //    var dbName = Request["dbname"] + "";
            //    var token = Request["token"] + "";
            //    if (!string.IsNullOrEmpty(dbName))
            //    {
            //        try
            //        {
            //            dbName = DES.DecryptDES(dbName, CustomerConfig.LoginCookieEncryptKey);
            //        }
            //        catch (Exception ex)
            //        {
            //            Log.WriteError($"dbName【{dbName}】解析失败：{ex.Message}");
            //        }
            //    }
            //    var selfDbName = SiteContext.Current.CurrentDbSettingConfig?.DbNameConfig?.DbName ?? "";
            //    if (dbName != selfDbName && !string.IsNullOrEmpty(selfDbName))
            //    {
            //        selfDbName = DES.EncryptDES(selfDbName, CustomerConfig.LoginCookieEncryptKey);
            //        _commonSettingService.Set("/ErpWeb/DefaultPlatformDbArea", selfDbName, shopId);
            //        Response.Clear();
            //        var url = $"{Request.Url.Scheme}://{Request.Url.Host}{(Request.Url.Port == 80 ? "" : ":" + Request.Url.Port)}/DistributionProduct/ListBySupplier?token={token}&dbname={selfDbName}";
            //        Response.Redirect(url);
            //        return View();
            //    }
            //    #endregion
            //}

            LoadSupplierDefaultConfig();

            var shopId = SiteContext.Current.CurrentShopId;
            #region 供应商查看分销品页面，切换到自己所属分区
            var dbName = Request["dbname"] + "";
            var token = Request["token"] + "";
            if (!string.IsNullOrEmpty(dbName))
            {
                try
                {
                    dbName = DES.DecryptDES(dbName, CustomerConfig.LoginCookieEncryptKey);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"dbName【{dbName}】解析失败：{ex.Message}");
                }
            }
            var selfDbName = SiteContext.Current.CurrentDbSettingConfig?.DbNameConfig?.DbName ?? "";
            if (dbName != selfDbName && !string.IsNullOrEmpty(selfDbName))
            {
                selfDbName = DES.EncryptDES(selfDbName, CustomerConfig.LoginCookieEncryptKey);
                _commonSettingService.Set("/ErpWeb/DefaultPlatformDbArea", selfDbName, shopId);
                Response.Clear();
                var url = $"{Request.Url.Scheme}://{Request.Url.Host}{(Request.Url.Port == 80 ? "" : ":" + Request.Url.Port)}/DistributionProduct/ListBySupplier?token={token}&dbname={selfDbName}";
                Response.Redirect(url);
                return View();
            }
            #endregion

            //供应商 新增分销商品关联
            ViewBag.AddRelationIFrameSrc = "/DistributionProduct/SkuBind" + "?" + Request.QueryString;
            ViewBag.UnbindRelationIFrameSrc = "/DistributionProduct/SkuUnbind" + "?" + Request.QueryString;

            //查看绑定的分销商品列表
            ViewBag.DistributorMapProduct = $"/SupplySet1688/DistributorMapProduct?{Request.QueryString}&productCode={{productCode}}&fromId={{supplierUserId}}&from=supplier";

            var fxUserId = SiteContext.Current.CurrentFxUserId;
            //已全部迁移，不需要再兼容判断 2023-10-13
            //var fxDbConfig = new FxDbConfigService().GetByFxUserId(fxUserId, CloudPlatformType.TouTiao.ToString());
            //ViewBag.ShowTouTiaoCloud = fxDbConfig == null ? "0" : "1"; //是否显示 抖店分区

            //同步时间处理
            var times = "";
            var isSyncButton = "1";
            try
            {
                times = new SyncStatusService().LastTimeProductByFxUserId(fxUserId);
                isSyncButton = new SyncStatusService().GetIsShowProductSyncButton(fxUserId);

                //如果停止，则不检测
                if (CustomerConfig.IsSyncDisabledByProductExamine)
                    isSyncButton = "1";
            }
            catch (Exception e)
            {
                Log.WriteError($"商品列表错误：{e.Message}");
            }

            ViewBag.LastTime = times;
            ViewBag.IsShowSyncButton = isSyncButton;
            ViewBag.SyncShopName = _fxUserShopService.GetCurrentUserShopNickNameByAlibaba();

            //是否是白名单用户
            var isWhiteUser = SiteContext.Current.IsWhiteUser;
            ViewBag.IsWhiteList = isWhiteUser;

            return View();
        }

        public void LoadSupplierDefaultConfig()
        {
            var currentFxUserId = SiteContext.Current.CurrentFxUserId;
            var key = BusinessSettingKeys.SupplyBy1688.ShopBy1688;
            var shopId = SiteContext.Current.CurrentShopId;

            var supplier1688ShopSetting = _commonSettingService.Get(key, shopId);

            if (supplier1688ShopSetting == null || string.IsNullOrWhiteSpace(supplier1688ShopSetting.Value) || supplier1688ShopSetting.Value == "0")
            {
                var cloudPlatformType = CustomerConfig.CloudPlatformType;
                Log.Debug($"当前云没有1688下单店铺，{currentFxUserId}，{shopId},{cloudPlatformType}");
                if (cloudPlatformType != "Alibaba")
                {
                    //非精选云，查精选配置库
                    supplier1688ShopSetting = _commonSettingService.GetAlibaba(key, shopId);
                    if (supplier1688ShopSetting != null && !string.IsNullOrWhiteSpace(supplier1688ShopSetting.Value) && supplier1688ShopSetting.Value != "0")
                    {
                        Log.Debug($"精选云存在1688下单店铺，{currentFxUserId}，{shopId},{supplier1688ShopSetting.Value}");
                        //在精选云有查到数据,存到当前云
                        _commonSettingService.Set(key, supplier1688ShopSetting.Value,shopId);
                    }
                }
            }

            if (supplier1688ShopSetting == null || string.IsNullOrWhiteSpace(supplier1688ShopSetting.Value) || supplier1688ShopSetting.Value == "0")
            {
                ViewBag.Supplier1688Shop = 0; //未设置1688下单店铺
                //ViewBag.Supplier1688Shop = 1835; //为了展示测试数据
                var shopsBy1688 = _fxUserShopService.GetCurrentUserShopsByAlibaba();
                ViewBag.ShopsBy1688 = shopsBy1688?.OrderByDescending(m => m.CreateTime).ToList();
            }
            else
            {
                ViewBag.Supplier1688Shop = supplier1688ShopSetting.Value; //有设置1688下单店铺                
            }

            key = BusinessSettingKeys.SupplyBy1688.MemberSettlementPriceSwitch;
            var memberSettlementPriceSwitch = _commonSettingService.Get(key, shopId);
            //是否开启会员等级结算价
            ViewBag.MemberSettlementPriceSwitch = memberSettlementPriceSwitch?.Value ?? "false";
            var supplier1688ShoId = string.Empty;
            supplier1688ShoId = supplier1688ShopSetting != null ? supplier1688ShopSetting.Value : "0";
            var statusList = new PurchaseOrderRelationService().CheckPrePayStatus(currentFxUserId, supplier1688ShoId, "Supplier");

            statusList.TryGetValue("productMappingStatus", out var isProductMapping);
            statusList.TryGetValue("distributorMappingStatus", out var isDistributorMapping);

            ViewBag.IsProductMapping = isProductMapping; // 是否导入货源商品
            ViewBag.IsDistributorMapping = isDistributorMapping; // 是否绑定下游商品
        }

        public ActionResult LoadBuyerAuth()
        {
            dynamic buyerAuth = new
            {
                IsPing = true,//默认畅通（授权没过期）
                IsExpired = false, //没有过期
                ShopNickName = "",
                AuthUrl = ""
            };

            var shopRepository = new ShopRepository();
            var buyerShop = shopRepository.GetAlibabaDefalutQingShopsByFxUserId(SiteContext.Current.CurrentFxUserId);
            //var buyerShop = shopRepository.GetAlibabaQingShopsByFxUserId(SiteContext.Current.CurrentFxUserId).FirstOrDefault();
            //var buyerShopRelation = new FxAlibabaBuyerShopRelationService().GetBuyerShopInfoByFxUserId(SiteContext.Current.CurrentFxUserId);

            if (buyerShop != null)
            {
                //校验是否过期
                var service = PlatformFactory.GetPlatformService(buyerShop) as AlibabaPlatformService;
                var isPing = service.PingNoThrow();
                var expiredTime = service.GetExpiredTime();

                buyerAuth = new
                {
                    IsPing = isPing,
                    IsExpired = (expiredTime < DateTime.Now),//判断轻应用是否过期
                    ShopNickName = buyerShop.NickName,
                    AuthUrl = base.Get1688AuthUrl() //只有授权过期的情况下，才需要生成授权地址
                };
            }

            return SuccessResult(buyerAuth);
        }


        /// <summary>
        /// 查询数据
        /// </summary>
        /// <param name="queryModel">参数模型</param>
        /// <returns></returns>
        [DianGuanJiaApp.Controllers.IgnoreDoubleAuth]
        public ActionResult LoadListBySupplier(QueryDistributionProductModel queryModel, bool isNeedQueryRelationCount = true)
        {
            //1.检查当前用户是否有设置 1688 下单店铺
            if (queryModel.AlibabaShopId == null || !queryModel.AlibabaShopId.Any(f => f > 0))
            {
                return FalidResult("1688授权店铺Id参数异常");
            }

            SwitchSelfDbName();

            //2.查询
            var rst = _service.LoadDistributionProductListBySupplier(queryModel, isNeedQueryRelationCount);
            return SuccessResult(new { TotalCount = rst.Item1, List = rst.Item2 });
        }

        /// <summary>
        /// 分销品列表
        /// 分销商角色
        /// </summary>
        /// <returns></returns>
        public ActionResult ListByAgent()
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId; //当前用户id

            //厂家数据源-只显示绑定成功的
            var suppliers = _supplierUserService.GetSupplierList(fxUserId,needEncryptAccount:true);
            //new SysPermissionFxService().EncryptAccount(suppliers, isAgent: false);
            var supplierJson = suppliers?.Select(x => new { UserName = x.SupplierMobileAndRemark, FxUserId = x.SupplierFxUserId, Status = x.Status, IsTop = x.IsTop }).Distinct().ToJson();
            ViewBag.Suppliers = supplierJson.IsNullOrEmpty() ? null : supplierJson;

            ViewBag.Supplier1688ShopIsSet = false.ToString().ToLower(); //未设置1688下单店铺

            ViewBag.SupplierFxUserIds = new List<int>().ToJson();//厂家id

            //遍历供应商的下单店铺给到前端使用
            var dict = new Dictionary<int, int>(); //key-FxUserId,Val-1688ShopId
            var all1688Sid = new List<int>();

            if (suppliers.Any())
            {
                var supplierFxUserIds = suppliers.Select(f => f.SupplierFxUserId).ToList(); //获取厂家id
                ViewBag.SupplierFxUserIds = supplierFxUserIds.ToJson(); //厂家id
                //1.检查当前用户的厂家是否有设置 1688 下单店铺
                //见开放方案，第三章第三小节（下单店铺设置）：https://doc.weixin.qq.com/doc/w3_APIAwgZGAEonO8YyXPWQL06RYZE6k
                var key = BusinessSettingKeys.SupplyBy1688.ShopBy1688;// "/ErpWeb/1688Supply/Supplier/1688Shop";
                var supplierSysShops = new ShopService().GetFxSystemShopByFxIdV1(supplierFxUserIds, "s.id,s.NickName,fus.FxUserId AS FxUserIds");
                var supplierSidDict = supplierSysShops.ToLookup(f => f.Id, f => f.FxUserIds).ToDictionary(f => f.Key, f => f.FirstOrDefault());
                var supplierSystemSids = supplierSysShops.Select(f => f.Id).ToList();
                var supplier1688ShopSettings = _commonSettingService.GetSettingModelByShopIds(key, supplierSystemSids);
                if (supplier1688ShopSettings == null || !supplier1688ShopSettings.Any(f => f?.Value?.ToInt() > 0))
                {
                    ViewBag.Supplier1688ShopIsSet = false.ToString().ToLower(); //未设置1688下单店铺
                }
                else
                {
                    ViewBag.Supplier1688ShopIsSet = true.ToString().ToLower(); //有设置1688下单店铺的供应商
                    foreach (var item in supplier1688ShopSettings)
                    {
                        //有配置值
                        if (item != null
                            && !string.IsNullOrWhiteSpace(item.Value)
                            && item.Value.ToInt() > 0
                            && supplierSidDict.ContainsKey(item.ShopId))
                        {
                            var aliSid = item.Value.ToInt();
                            all1688Sid.Add(aliSid);
                            var fxId = supplierSidDict[item.ShopId];
                            dict.Add(fxId, aliSid); //key-FxUserId,Val-1688ShopId
                        }
                    }
                }
            }

            ViewBag.Supplier1688ShopDict = dict.ToJson(); //厂家的下单店铺id字典，前端通过选择的厂家，到里面找到对应的下单店铺id
            ViewBag.All1688Sid = all1688Sid.ToJson(); //所有厂家的下单店铺id

            //查看绑定的分销商品列表
            ViewBag.DistributorMapProduct = $"/SupplySet1688/DistributorMapProduct?{Request.QueryString}&productCode={{productCode}}&fromId={{supplierUserId}}&from=agent";
            return View();
        }

        /// <summary>
        /// 1688货源查询数据
        /// </summary>
        /// <param name="queryModel">参数模型</param>
        /// <returns></returns>
        public ActionResult LoadListByAgent(QueryDistributionProductModel queryModel)
        {
            //1.检查当前用户是否有设置 1688 下单店铺
            if (queryModel.AlibabaShopId == null || !queryModel.AlibabaShopId.Any(f => f > 0))
            {
                return FalidResult("1688授权店铺异常");
            }
            //2.校验是否单选厂家
            if (queryModel != null && queryModel.SupplierUserId.Count() > 1)
            {
                return FalidResult("仅支持厂家单选");
            }
            
            var fxUserId = SiteContext.Current.CurrentFxUserId; //当前登录分销商id
            #region 指定厂家,通过厂家配置信息设置上下文
            // 获取唯一供应商用户 Id
            var supplierUserId = queryModel.SupplierUserId.FirstOrDefault();
            // 3. 通过指定厂家获取用户分区，注：用户可能存在新旧分区，FromFxDbConfig 降序第一个为准
            string baseCloudPlatformType = CloudPlatformType.Alibaba.ToString();
            var supplierDbConfigs = new DbConfigRepository().GetListByFxUserIds(queryModel.SupplierUserId, baseCloudPlatformType);
            // 若用户存在新旧分区，取新分区
            supplierDbConfigs = supplierDbConfigs.GroupBy(m => m.DbConfig.UserId)
                .Select(m => m.OrderByDescending(o => o.FromFxDbConfig).First()).ToList();
            var targetDbConfig = supplierDbConfigs.First();
            if (targetDbConfig == null)
            {
                return SuccessResult(new { TotalCount = 0, List = new List<DistributionProductViewModel>() });
            }
            ///注 这是厂家的！！！用户信息
            var fxUser = new UserFxRepository().Get(supplierUserId);
            // 4. 设置站点厂家上下文
            new SiteContext(fxUser, targetDbConfig.DbNameConfig.DbName);
            #endregion
            //5.查询1688货源数据
            var rst = _service.LoadDistributionProductListByAgent(queryModel, fxUserId);
            return SuccessResult(new { TotalCount = rst.Item1, List = rst.Item2 });
        }

        /// <summary>
        /// 供应商 关联的 分销商商品列表
        /// </summary>
        /// <returns></returns>
        public ActionResult RelationAgentProductList()
        {
            ViewBag.ProductCode = Request["productCode"]; //厂家分销商品的ProductCode
            ViewBag.From = Request["from"]; //进入页面的来源 supplier or agent ，商家只能查看，无相关操作
            ViewBag.SupplierUserId = Request["fromId"];

            //供应商 新增分销商品关联
            ViewBag.AddRelationIFrameSrc = "/DistributionProduct/SkuBind" + "?" + Request.QueryString;

            var fxUserId = SiteContext.Current.CurrentFxUserId;
            //已全部迁移，不需要再兼容判断 2023-10-13
            //var fxDbConfig = new FxDbConfigService().GetByFxUserId(fxUserId, CloudPlatformType.TouTiao.ToString());
            //ViewBag.ShowTouTiaoCloud = fxDbConfig == null ? "0" : "1"; //是否显示 抖店分区

            //非精选平台，获取相关的精选分区
            if (CustomerConfig.CloudPlatformType != CloudPlatformType.Alibaba.ToString())
            {
                //被当前厂家开启过预付的商家
                //var agentFxUserIds = _supplierUserService.GetOpenedPrePayAgentFxUserIds(fxUserId);
                //查询这些商家所包含的精选库
                var agentsDbs =
                    SiteContext.Current.GetAgentAlibabaDbs(new List<int> { SiteContext.Current.CurrentFxUserId });
                if (agentsDbs != null && agentsDbs.Any())
                {
                    ViewBag.DbArea = agentsDbs.Select(x => x.DbNameConfig).OrderBy(x => x.Id).Select(y => new { DbName = DES.EncryptDES(y.DbName, CustomerConfig.LoginCookieEncryptKey), y.NickName, y.ApplicationName }).ToList().ToJson();
                }
            }

            var agents = _supplierUserService.GetAgentList(fxUserId,needEncryptAccount:true);//_supplierUserService.GetAgentList(fxUserId, null, null, 1, 10000);
            var agentJson = agents?.Select(x => new { UserName = x.AgentMobileAndRemark, x.FxUserId, x.Status, x.IsTop, x.NickName, x.Remark }).Distinct().ToJson();
            ViewBag.Agents = agentJson.IsNullOrEmpty() ? null : agentJson;

            return View();
        }

        /// <summary>
        /// 查询数据
        /// 厂家分销品关联的商家商品列表
        /// </summary>
        /// <param name="queryModel">参数模型</param>
        /// <returns></returns>
        public ActionResult LoadRelationProductList(QueryMapProductModel queryModel)
        {
            //1.厂家商品信息判断
            if (string.IsNullOrEmpty(queryModel.AlibabaProductCode) || queryModel.SuplierUserId == 0)
                return FalidResult("参数异常：厂家信息为空");

            //如果是商家查看，只查看关联自己的商品
            if (queryModel.From == "agent")
                queryModel.AgentUserId = SiteContext.Current.CurrentFxUserId;

            //2.查询 商家商品
            var rst = _service.LoadRelationProductList(queryModel);

            //3.查询 厂家分销品信息
            ProductFx supplierProduct = null;
            if (rst.Item2 != null && rst.Item2.Any())
            {
                var fields = new List<string> {
                    "sku.Id",
                    "sku.ImgUrl",
                    "sku.SkuCode",
                    "sku.SkuId",
                    "sku.ProductCode",
                    "sku.Name",
                    "sku.CargoNumber",
                    "sku.SystemStatus",
                    "p.Id",
                    "p.ProductCode",
                    "p.ImageUrl",
                    "p.Subject",
                    "p.CargoNumber",
                    "p.PlatformId",
                    "p.ShopId",
                    "p.SourceUserId",
                    "p.Status" };

                //用ProductCode维度查，带出所有Skus
                var productCodes = rst.Item2.SelectMany(f => f.Skus).Where(f => string.IsNullOrEmpty(f.UpProductCode) == false).Select(f => f.UpProductCode).Distinct().ToList();
                supplierProduct = _productFxService.GetSupplierProductByProductCodes(productCodes, new List<int> { queryModel.SuplierUserId }, fields)?.FirstOrDefault(f => f.ProductCode == queryModel.AlibabaProductCode);

            }
            return SuccessResult(new
            {
                TotalCount = rst.Item1,
                AgentProducts = rst.Item2,
                SupplierProduct = (supplierProduct != null ? new
                {
                    supplierProduct.Id,
                    supplierProduct.ProductCode,
                    supplierProduct.Subject,
                    supplierProduct.CargoNumber,
                    supplierProduct.PlatformId,
                    supplierProduct.ImageUrl,
                    supplierProduct.Status,
                    Skus = supplierProduct.Skus?.Select(sku => new
                    {
                        sku.Id,
                        sku.SkuCode,
                        sku.SkuId,
                        sku.ProductCode,
                        sku.Name,
                        sku.ImgUrl,
                        sku.CargoNumber,
                        sku.SystemStatus,
                        ProductStatus = supplierProduct.Status,
                    })
                } : null)
            });
        }

        /// <summary>
        /// 新增关联 页面
        /// </summary>
        /// <returns></returns>
        public ActionResult SkuBind(string productCode)
        {
            LoadDefaultConfig();
            return View();
        }

        /// <summary>
        ///  新增关联,显示商家列表（轻应用需要）
        /// </summary>
        /// <param name="productCode"></param>
        /// <returns></returns>
        public ActionResult LoadAgentListByPrePayUser()
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var successStatus = CustomerConfig.AgentBingSupplierSuccessStatus;
            var agents = _supplierUserService.GetAgentListV2(new List<int> { fxUserId }, successStatus, true, isPrePay: 1,needEncryptAccount:true);
            var agentList = agents?.Select(x => new { UserName = x.AgentMobileAndRemark, x.FxUserId, x.Status, x.IsTop }).Distinct().ToList();

            return SuccessResult(new { AgentList = agentList });
        }

        /// <summary>
        ///  订单列表，显示商家列表（轻应用需要）
        /// </summary>
        /// <param name="productCode"></param>
        /// <returns></returns>
        public ActionResult LoadAgentListByPartition()
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            //商家数据源
            var fields = "t1.Id,t1.FxUserId,t1.Status,t1.IsTop,t1.SupplierType,t1.Remark,t1.RemarkName";
            var agents = _supplierUserService.GetAgentList(fxUserId, onlyGetCurDb: true, fields: fields,needEncryptAccount:true);
            var agentList = agents?.Select(x => new { UserName = x.AgentMobileAndRemark, x.FxUserId, x.Status, x.IsTop }).Distinct().ToList();

            return SuccessResult(new { AgentList = agentList });
        }

        /// <summary>
        /// 解绑关联 页面
        /// </summary>
        /// <returns></returns>
        public ActionResult SkuUnBind(string productCode)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var agents = _supplierUserService.GetAgentList(fxUserId,needEncryptAccount:true);//_supplierUserService.GetAgentList(fxUserId, null, null, 1, 10000);
            var agentJson = agents?.Select(x => new { UserName = x.AgentMobileAndRemark, x.FxUserId, x.Status, x.IsTop, x.NickName, x.Remark }).Distinct().ToJson();
            ViewBag.Agents = agentJson.IsNullOrEmpty() ? null : agentJson;

            return View();
        }

        public void LoadDefaultConfig()
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var successStatus = CustomerConfig.AgentBingSupplierSuccessStatus;
            var agents = _supplierUserService.GetAgentListV2(new List<int> { fxUserId }, successStatus, true, isPrePay: 1,needEncryptAccount:true);
            var agentJson = agents?.Select(x => new { UserName = x.AgentMobileAndRemark, x.FxUserId, x.Status, x.IsTop }).Distinct().ToJson();
            ViewBag.Agents = agentJson.IsNullOrEmpty() ? null : agentJson;
        }

        /// <summary>
        /// 新增关联 页面：查询商家商品数据
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult BindSkuLoadProductList(ProductFxRequertModule model)
        {
            model.QueryFlag = "supplier";
            model.IsFilterDPSkuMapping = true;
            model.NeedSupplierMapping = true;
            model.SettlementType = 1;
            var data = _productFxService.GetProductFxList(SiteContext.Current.CurrentFxUserId, model);
            var agentFxUserIds = data.Rows.Select(x => x.UpFxUserId).Distinct().ToList();
            // 获取商家会员等级
            var agentLevels = _supplierUserService.GetAgentLevelByIds(agentFxUserIds, BaseSiteContext.Current.CurrentFxUserId);
            data.Rows.ForEach(row =>
            {
                row.MemberLevel = agentLevels.FirstOrDefault(x => x.FxUserId == row.UpFxUserId)?.MemberLevel ?? 0;
            });

            var result = Json(data);
            return result;
        }

        /// <summary>
        /// 解绑关联 页面：查询已绑定的商家商品数据
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult UnBindSkuLoadProductList(ProductFxRequertModule model)
        {
            model.IsFilterDPSkuMapping = false;
            var data = _productFxService.GetProductFxList(SiteContext.Current.CurrentFxUserId, model);
            if (data != null && data.Rows != null && data.Rows.Any())
            {
                //商品维度商家名称为空用Sku维度
                data.Rows.ForEach(row =>
                {
                    if (string.IsNullOrEmpty(row.AgentName) && row.Skus != null)
                        row.AgentName = string.Join(",", row.Skus.Select(sku => sku.AgentName).Distinct());
                });
            }
            var result = Json(data);
            return result;
        }

        /// <summary>
        /// 绑定
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult SaveBind(DPSkuBindModel model)
        {
            if (model == null)
                return FalidResult("操作失败");
            if (string.IsNullOrEmpty(model.UpProductCode))
                return FalidResult("请选择1688商品");
            if (model.DownSkuCodes == null || model.DownSkuCodes.Count == 0)
                return FalidResult("请选择要绑定的Sku");

            var curFxUserId = SiteContext.Current.CurrentFxUser.Id;
            model.CreateFxUserId = curFxUserId;
            var errMessage = dpSkuMappingService.Save(model);

            if (string.IsNullOrEmpty(errMessage))
            {
                return SuccessResult();
            }
            else
            {
                return FalidResult("保存成功<p>个别失败如下：<p>" + errMessage);
            }
        }

        /// <summary>
        /// 【未关联列表页】发起的绑定
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult SaveByDownProductCodes(DPSkuBindModel model)
        {
            if (model == null)
                return FalidResult("操作失败");
            if (string.IsNullOrEmpty(model.UpProductCode))
                return FalidResult("请选择1688商品");
            if (model.DownProductCodes == null || model.DownProductCodes.Count == 0)
                return FalidResult("请选择要绑定的商品");

            var curFxUserId = SiteContext.Current.CurrentFxUser.Id;
            model.CreateFxUserId = curFxUserId;
            var errMessage = dpSkuMappingService.SaveByDownProductCodes(model);

            if (string.IsNullOrEmpty(errMessage))
            {
                return SuccessResult();
            }
            else
            {
                return FalidResult("保存成功<p>个别失败如下：<p>" + errMessage);
            }
        }

        /// <summary>
        /// 解除绑定(Product维度)
        /// </summary>
        /// <param name="codes">ProductMappingCodes</param>
        /// <returns></returns>
        public ActionResult DeleteBind(List<string> codes)
        {
            if (codes == null || codes.Any() == false)
                return FalidResult("操作失败");
            var curFxUserId = SiteContext.Current.CurrentFxUser.Id;
            dpMappingService.SetDeleted(codes, curFxUserId);
            return SuccessResult();
        }

        /// <summary>
        /// 更改绑定
        /// </summary>
        /// <param name="model">ChangeSkuMappingModel</param>
        /// <returns></returns>
        public ActionResult ChangeBind(ChangeSkuMappingModel model)
        {
            if (model == null || string.IsNullOrEmpty(model.OldSkuMappingCode) || string.IsNullOrEmpty(model.UpSkuCode) || string.IsNullOrEmpty(model.DownSkuCode))
                return FalidResult("操作失败");
            if (model.OldSkuMappingCode == model.SkuMappingCode)
                return FalidResult("无变化");
            var curFxUserId = SiteContext.Current.CurrentFxUser.Id;
            model.UpFxUserId = curFxUserId;
            var newSkuMappingCode = dpSkuMappingService.Change(model);
            return SuccessResult(newSkuMappingCode);
        }


        /// <summary>
        /// 解除绑定(Sku维度)
        /// </summary>
        /// <param name="codes">SkuMappingCodes</param>
        /// <returns></returns>
        public ActionResult DeleteSkuBind(List<string> codes)
        {
            if (codes == null || codes.Any() == false)
                return FalidResult("操作失败");
            var curFxUserId = SiteContext.Current.CurrentFxUser.Id;
            dpSkuMappingService.SetDeleted(codes, curFxUserId);
            return SuccessResult();
        }

        /// <summary>
        /// 切换到当前自己的分区
        /// </summary>
        public void SwitchSelfDbName()
        {
            if (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString())
            {
                var dbName = Request["dbname"] + "";
                if (!string.IsNullOrEmpty(dbName))
                {
                    try
                    {
                        dbName = DES.DecryptDES(dbName, CustomerConfig.LoginCookieEncryptKey);
                    }
                    catch (Exception ex) { }
                }
                var selfDbName = SiteContext.Current.CurrentDbSettingConfig?.DbNameConfig?.DbName ?? "";
                if (dbName != selfDbName && !string.IsNullOrEmpty(selfDbName))
                {
                    var sc = new SiteContext(SiteContext.Current.CurrentFxUser, selfDbName);
                    selfDbName = DES.EncryptDES(selfDbName, CustomerConfig.LoginCookieEncryptKey);
                    ViewBag.DbName = selfDbName;
                }
            }
        }

        [FxAuthorize(FxPermission.SelectionDistribution)]
        public ActionResult SelectionDistribution()
        {

            return View();
        }

        public ActionResult DistributionDetails()
        {

            return View();
        }
        /// <summary>
        /// 用户名片产品列表
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult UserCallingCardProduct()
        {

            return View();
        }


        public ActionResult AgentProductList()
        {
            //var fxUserId = SiteContext.Current.CurrentFxUserId;
            //var successStatus = CustomerConfig.AgentBingSupplierSuccessStatus;
            //var agents = _supplierUserService.GetAgentListV2(new List<int> { fxUserId }, successStatus, true, isPrePay: 1);
            //var agentJson = agents?.Select(x => new { UserName = x.AgentMobileAndRemark, x.FxUserId, x.Status, x.IsTop }).Distinct().ToJson();
            //ViewBag.Agents = agentJson.IsNullOrEmpty() ? null : agentJson;

            LoadDefaultConfig();
            LoadSupplierDefaultConfig();

            return View();
        }

        /// <summary>
        /// 查询下游推送商品列表（未关联列表）
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult LoadAgentProductList(ProductFxRequertModule model)
        {
            //需要关联厂商
            model.QueryFlag = "supplier";
            model.NeedSupplierMapping = true;
            model.SettlementType = 2;
            var data = _productFxService.GetProductFxList(SiteContext.Current.CurrentFxUserId, model);

            //商品维度商家名称为空用Sku维度
            data.Rows.ForEach(row =>
            {
                if (string.IsNullOrEmpty(row.AgentName) && row.Skus != null)
                    row.AgentName = string.Join(",", row.Skus.Select(sku => sku.AgentName).Distinct());
            });

            //设置已绑定的商品SKU(货源信息)
            var supplierProducts = SetSupplierProducts(model, data.Rows);

            //已绑定关联商品列表才需要厂家商品数据
            //if (model.IsFilterDPSkuMapping == false)
            //    data.SupplierProducts = supplierProducts;

            var result = Json(data);
            return result;
        }


        public ActionResult AfterSaleList()
        {

            return View();
        }

        /// <summary>
        /// 查询自动售后记录
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public ActionResult LoadAfterSaleActionRecordList(AfterSaleActionRecordQuery query)
        {
            if (query.PageIndex <= 0)
                query.PageIndex = 1;
            if (query.PageSize <= 0 || query.PageSize > 10000)
                query.PageSize = 20;

            query.SourceFxUserId = SiteContext.Current.CurrentFxUserId;
            var _asarService = new AfterSaleActionRecordService();
            var result = _asarService.GetPageList(query);

            return SuccessResult(new { Total = result.Item1, List = result.Item2.Select(a => new { a.Id, a.SeqNumber, a.Status, a.UpPlatformOrderId, a.SourceLogicOrderId, a.SourcePlatformOrderId, a.SourceAfterSaleId, a.UpAfterSaleId, a.UpRefundType, a.SourceRefundType, a.CreateTime, a.UpdateTime, ErrorMessage = a.ErrorMessage.NoHTML().CutString(100) }) });
        }

        private List<ProductFx> SetSupplierProducts(ProductFxRequertModule model, List<ProductFx> ProductFxLists)
        {
            if (model.NeedSupplierMapping == false)
            {
                return new List<ProductFx>();
            }
            var fields = new List<string> {
                    "sku.Id",
                    "sku.ImgUrl",
                    "sku.SkuCode",
                    "sku.SkuId",
                    "sku.ProductCode",
                    "sku.Name",
                    "sku.CargoNumber",
                    "sku.SystemStatus",
                    "p.Id",
                    "p.ProductCode",
                    "p.ImageUrl",
                    "p.Subject",
                    "p.CargoNumber",
                    "p.PlatformId",
                    "p.ShopId",
                    "p.SourceUserId" };

            //var supplierIds1 = SiteContext.Current.CurrentFxUserId;
            //var supplierIds2 = ProductFxLists.Select(p=>p.SourceUserId).Distinct().ToList();
            //Stopwatch stopwatch1 = new Stopwatch();
            //stopwatch1.Start();
            var supplierIds = ProductFxLists.SelectMany(p => p.Skus).Select(p => p.CreateFxUserId).Distinct().ToList();
            var productCodes = ProductFxLists.SelectMany(f => f.Skus).Where(f => string.IsNullOrEmpty(f.UpProductCode) == false).Select(f => f.UpProductCode).Distinct().ToList();
            var supplierProducts = _productFxService.GetSupplierProductByProductCodes(productCodes, supplierIds, fields);
            //stopwatch1.Stop();
            //Log.WriteLine($"SetSupplierProducts=》GetSupplierProductByProductCodes 耗时;{stopwatch1.ElapsedMilliseconds}ms", "耗时测试.txt");

            foreach (var prod in ProductFxLists)
            {
                if (prod.Skus.IsNullOrEmptyList())
                {
                    continue;
                }
                var upProductCode = prod.Skus.Select(p => p.UpProductCode).First();
                var supProduct = supplierProducts.Where(p => p.ProductCode == upProductCode).FirstOrDefault();
                if (supProduct != null)
                {
                    prod.SupplierProSubject = supProduct.Subject;
                    prod.SupplierProImageUrl = supProduct.ImageUrl;
                    prod.SupplierProCode = supProduct.ProductCode;
                    prod.SupplierProductId = supProduct.PlatformId;
                    prod.SupplierProCargoNumber = supProduct.CargoNumber;
                    if (supProduct.Skus.IsNullOrEmptyList())
                    {
                        continue;
                    }
                    foreach (var sku in prod.Skus)
                    {
                        var supProductSku = supProduct.Skus.Where(p => p.SkuCode == sku.UpSkuCode).FirstOrDefault();
                        if (supProductSku != null)
                        {
                            sku.SupplierProductSkuName = supProductSku.Name;
                            sku.SupplierProductSkuImgUrl = supProductSku.ImgUrl;
                            sku.SupplierProductSkuCode = supProductSku.SkuCode;
                            sku.SupplierProductSkuId = supProductSku.SkuId;
                            sku.SupplierProductSkuCargoNumber = supProductSku.CargoNumber;
                        }
                    }
                }

            }

            return supplierProducts;
        }


    }
}