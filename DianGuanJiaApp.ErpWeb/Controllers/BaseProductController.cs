using DianGuanJiaApp.Controllers;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Entity.BaseProduct;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.FxModel;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Model.BaseProduct;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Services.BaseProduct;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.Helpers;
using DianGuanJiaApp.Warehouse.Model.Request;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web.Mvc;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Data.Repository.BaseProduct;
using DianGuanJiaApp.Utility.Other;
using System.Data;
using DianGuanJiaApp.Utility.NPOI;
using Newtonsoft.Json.Linq;
using System.Text;
using System.Threading;
using DianGuanJiaApp.Models;
using DianGuanJiaApp.Services.Services.BaseProduct;
using Microsoft.Ajax.Utilities;
using System.Diagnostics;
using DianGuanJiaApp.Services.Services;
using System.Collections.Concurrent;
using DianGuanJiaApp.Data.Repository.Settings;
using DianGuanJiaApp.Services.Services.DataEventTracking;

namespace DianGuanJiaApp.ErpWeb.Controllers
{
    [SessionState(System.Web.SessionState.SessionStateBehavior.Disabled)]
    [FxWhiteUserFilter]
    public class BaseProductController : BaseController
    {

        private ProductFxService _productFxService = new ProductFxService();
        private PathFlowService _pathFlowService = new PathFlowService();
        private ShopService _shopService = new ShopService();
        private CommonSettingService _commonSettingService = new CommonSettingService();
        private WareHouseService _service = new WareHouseService();

        private BaseProductSkuService _baseProductSkuService = null;
        private SupplierUserService _supplierUserService = new SupplierUserService();
        private AsyncTaskService _asyncTaskService = new AsyncTaskService();
        private BaseProductAbnormalService _baseProductAbnormalService = null;

        public ActionResult ProductCollectionBox(){

            return View();
        }
        /// <summary>
        /// 异常解绑时启用异步的阈值
        /// </summary>
        private const int ABNORMAL_UNBIND_ENABLE_ASYNC_COUNT = 200;

        /// <summary>
        /// 异常解绑任务锁
        /// </summary>
        private const string ABNORMAL_UNBIND_ASYNC_TASK_LOCK = "DianGuanJiaApp:BaseProduct:AbnormalUnbindTaskLock";

        /// <summary>
        /// 异常解绑异步任务结果
        /// </summary>
        // ReSharper disable once InconsistentNaming
        private const string ABNORMAL_UNBIND_ASYNC_TASK_RESULT_KEY = "DianGuanJiaApp:BaseProduct:AbnormalUnbindResult";


        #region 初始页面相关
        /// <summary>
        /// 基础商品（主页）
        /// </summary>
        /// <returns></returns>
        [FxAuthorize(FxPermission.BaseProduct)]
        public ActionResult BaseProduct()
        {
            ViewBag.DefaultFenFaSystemUrl = CustomerConfig.DefaultFenFaSystemUrl;
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var currShopId = SiteContext.Current.CurrentShopId;
            var _commonSettingService = new CommonSettingService();

            //厂家数据源
            var suppliers = _supplierUserService.GetSupplierList(fxUserId, onlyGetCurDb: true,needEncryptAccount:true);
            ViewBag.Suppliers = suppliers?.Select(x => new { UserName = x.SupplierMobileAndRemark, FxUserId = x.SupplierFxUserId, x.Status, x.IsTop, x.IsFilter }).Distinct().ToJson();
            ViewBag.SupplierUsers = suppliers?.Select(x => new { UserName = x.SupplierMobileAndRemark, FxUserId = x.SupplierFxUserId }).GroupBy(x => x.FxUserId).ToDictionary(x => x.Key, x => x.FirstOrDefault()?.UserName ?? "");
            // 页码配置
            var pageKey = "/ErpWeb/BaseProduct/PageSize";
            var keys = new List<string> { pageKey };
            var commSets = _commonSettingService.GetSets(keys, currShopId);
            var pageSizeSet = commSets.FirstOrDefault(x => x.Key == pageKey);
            ViewBag.PageSize = pageSizeSet?.Value ?? "20";
            CheckTaskStatus();
            return View();
        }

        [FxAuthorize(FxPermission.BaseProduct)]
        public ActionResult NewBaseProduct()
        {
            ViewBag.DefaultFenFaSystemUrl = CustomerConfig.DefaultFenFaSystemUrl;
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var currShopId = SiteContext.Current.CurrentShopId;
            var _commonSettingService = new CommonSettingService();

            //厂家数据源
            var suppliers = _supplierUserService.GetSupplierList(fxUserId, onlyGetCurDb: true,needEncryptAccount:true);
            ViewBag.Suppliers = suppliers?.Select(x => new { UserName = x.SupplierMobileAndRemark, FxUserId = x.SupplierFxUserId, x.Status, x.IsTop, x.IsFilter }).Distinct().ToJson();
            ViewBag.SupplierUsers = suppliers?.Select(x => new { UserName = x.SupplierMobileAndRemark, FxUserId = x.SupplierFxUserId }).GroupBy(x => x.FxUserId).ToDictionary(x => x.Key, x => x.FirstOrDefault()?.UserName ?? "");
            // 页码配置
            var pageKey = "/ErpWeb/BaseProduct/PageSize";
            var keys = new List<string> { pageKey };
            var commSets = _commonSettingService.GetSets(keys, currShopId);
            var pageSizeSet = commSets.FirstOrDefault(x => x.Key == pageKey);
            ViewBag.PageSize = pageSizeSet?.Value ?? "20";

            //预警库存数量
            int loginShopId = SiteContext.Current.CurrentShopId;
            var inventoryKeys = new List<string> { "/Export/StockDetail/ExportUpdateTime", "ErpWeb/StockDetail/WarnStockNum" };
            var inventoryCommSets = _commonSettingService.GetSets(inventoryKeys, loginShopId);
            ViewBag.WarnStockNum = inventoryCommSets.FirstOrDefault(x => x.Key == "ErpWeb/StockDetail/WarnStockNum")?.Value ?? "";

            CheckTaskStatus();
            return View();
        }

        /// <summary>
        /// 基础商品（关联页面）
        /// </summary>
        /// <returns></returns>
        [FxAuthorize(FxPermission.BaseProduct)]
        public ActionResult BaseProductSkuRelationBindIndex()
        {
            string baseProductSkuUid = Request.Params["baseProductSkuUid"];
            string baseProductUid = Request.Params["baseProductUid"];
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var currShopId = SiteContext.Current.CurrentShopId;
            // 页码配置
            var pageKey = "/ErpWeb/BaseProductSkuRelationBindList/PageSize";
            var keys = new List<string> { pageKey };
            var commSets = _commonSettingService.GetSets(keys, currShopId);
            var pageSizeSet = commSets.FirstOrDefault(x => x.Key == pageKey);
            ViewBag.PageSize = pageSizeSet?.Value ?? "20";
            CheckWareHouseStatus();
            LoadDefaultConfig();
            return View();
        }

        /// <summary>
        /// 基础商品（关联明细页面）
        /// </summary>
        /// <returns></returns>
        [FxAuthorize(FxPermission.BaseProduct)]
        public ActionResult BaseProductSkuRelationDetailIndex()
        {
            string baseProductSkuUid = Request.Params["baseProductSkuUid"];
            string baseProductUid = Request.Params["baseProductUid"];
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var currShopId = SiteContext.Current.CurrentShopId;
            // 页码配置
            var pageKey = "/ErpWeb/BaseProductSkuRelationDetailList/PageSize";
            var keys = new List<string> { pageKey };
            var commSets = _commonSettingService.GetSets(keys, currShopId);
            var pageSizeSet = commSets.FirstOrDefault(x => x.Key == pageKey);
            ViewBag.PageSize = pageSizeSet?.Value ?? "20";
            LoadDefaultConfig();
            return View();
        }

        /// <summary>
        /// 关联店铺商品列表页面
        /// </summary>
        /// <returns></returns>
        [FxAuthorize(FxPermission.BaseProduct)]
        public ActionResult BaseOfPtSkuRelation_equal_products()
        {
            LoadDefaultConfig();
            CheckWareHouseStatus();
            return View();
        }

        /// <summary>
        /// 关联商品列表页面 - 商品统一化
        /// </summary>
        /// <returns></returns>
        public ActionResult BaseOfPtSkuRelationEqualProducts()
        {
            return View();
        }
        /// <summary>
        /// 关联商品列表页面 - 商品统一化 - 未关联列表
        /// </summary>
        /// <returns></returns>
        public ActionResult BaseOfPtSkuUnrelatedList()
        {
            return View();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [FxAuthorize(FxPermission.BaseProduct)]
        public ActionResult BaseOfPtSkuRelation_detail()
        {
            return View();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [FxAuthorize(FxPermission.BaseProduct)]
        public ActionResult BaseOfPtSkuRelation_detail_products()
        {
            CheckWareHouseStatus();
            return View();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [FxAuthorize(FxPermission.BaseProduct)]
        public ActionResult BaseOfPtSkuRelation_equal()
        {
            return View();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [FxAuthorize(FxPermission.BaseProduct)]
        public ActionResult BaseOfPtProducts()
        {
            var sysShopId = SiteContext.Current.CurrentShopId;
            // 获取当前页码配置
            const string pageKey = "/ErpWeb/BaseOfPtProducts/PageSize";
            var keys = new List<string> { pageKey };
            var commSets = _commonSettingService.GetSets(keys, sysShopId);
            var pageSizeSet = commSets.FirstOrDefault(x => x.Key == pageKey);
            ViewBag.PageSize = pageSizeSet?.Value ?? "200";
            
            return View();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [FxAuthorize(FxPermission.BaseProduct)]
        public ActionResult CreateBaseProduct()
        {
            var supplierService = new SupplierUserService();
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var currShopId = SiteContext.Current.CurrentShopId;

            //厂家数据源
            var suppliers = _supplierUserService.GetSupplierList(fxUserId, onlyGetCurDb: true);
            ViewBag.Suppliers =
                suppliers?.Select(x => new { UserName = x.SupplierMobileAndRemark, FxUserId = x.SupplierFxUserId, x.Status, x.IsTop, x.IsFilter }).Distinct().ToJson();
            ViewBag.SupplierUsers =
                suppliers?.Select(x => new { UserName = x.SupplierMobileAndRemark, FxUserId = x.SupplierFxUserId }).GroupBy(x => x.FxUserId).ToDictionary(x => x.Key, x => x.FirstOrDefault()?.UserName ?? "");
            // 仓库数据
            var req = new WarehouseListRequest
            {
                PageIndex = 1,
                PageSize = 20,
                OwnerCode = _service.GetOwnerCode(SiteContext.Current.CurrentFxUserId),
            };
            var res = _service.LoadStoreManagementList(req);
            if (res.Warehouses != null && res.Warehouses.Count > 0)
            {
                ViewBag.WareHouseStatus = 1;
            }
            else
            {
                ViewBag.WareHouseStatus = 0;
            }
            return View();
        }

        /// <summary>
        /// 页面初始配置
        /// </summary>
        private void LoadDefaultConfig()
        {
            // 渠道数据类型商家 // 渠道数据类型店铺
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var _fxUserShopService = new FxUserShopService();
            var currShopId = SiteContext.Current.CurrentShopId;
            var _commonSettingService = new CommonSettingService();
            var _supplierUserService = new SupplierUserService();

            var pts = CustomerConfig.GetAllPlatformAuthLinks().ToList();
            if (!pts.Any(x => x.PlatformType == "Toutiao"))
            {
                pts.Add(new SuportPlatformAuthEntryModel
                {
                    PlatformType = "TouTiao",
                    Name = "抖音",
                    Index = 4
                });
            }
            if (!pts.Any(x => x.PlatformType == PlatformType.Virtual.ToString()))
            {
                pts.Add(new SuportPlatformAuthEntryModel
                {
                    PlatformType = "Virtual",
                    Name = "线下单",
                    Index = 6
                });
            }

            var fxUserShops = GetFxUserShops();
            //绑定店铺
            ViewBag.Shops =
                fxUserShops.Select(x => new { x.NickName, x.ShopId, x.PlatformType }).Distinct().ToJson();
            //未过期店铺
            ViewBag.ShopsEffect =
                fxUserShops.Where(w => w.Status == FxUserShopStatus.Binded).Select(x => new { x.NickName, x.ShopId, x.PlatformType }).Distinct().ToJson();

            var pathFlowList = _pathFlowService.GetPathFlowByFxUserId(fxUserId);
            var sids = pathFlowList.Where(f => f.SourceShopId > 0).Select(f => f.SourceShopId).ToList();
            var shops = _shopService.GetShopByIds(sids, "id,shopId,nickName,platformtype");
            if (CustomerConfig.CloudPlatformType == PlatformType.Pinduoduo.ToString())
                shops = shops.Where(x => CustomerConfig.FxPinduoduoCloudPlatformTypes.Contains(x.PlatformType)).ToList();
            else if (CustomerConfig.CloudPlatformType == PlatformType.Jingdong.ToString())
                shops = shops.Where(x => CustomerConfig.FxJingDongCloudPlatformTypes.Contains(x.PlatformType)).ToList();
            else if (CustomerConfig.CloudPlatformType == PlatformType.TouTiao.ToString())
                shops = shops.Where(x => CustomerConfig.FxDouDianCloudPlatformTypes.Contains(x.PlatformType)).ToList();
            else
                shops = shops.Where(x =>
                    CustomerConfig.FxJingDongCloudPlatformTypes.Contains(x.PlatformType) == false &&
                    CustomerConfig.FxPinduoduoCloudPlatformTypes.Contains(x.PlatformType) == false &&
                    CustomerConfig.FxDouDianCloudPlatformTypes.Contains(x.PlatformType) == false).ToList();
            fxUserShops.ForEach(fx =>
            {
                if (!shops.Any(s => s.Id == fx.ShopId))
                    shops.Add(new Shop { Id = fx.ShopId, PlatformType = fx.PlatformType });
            });

            //店铺对应的平台
            ViewBag.PlatformTypes = pts.Where(x => shops.Any(n => n.PlatformType.ToLower() == x.PlatformType.ToLower())).OrderBy(x => x.Index).Select(x => new { x.PlatformType, x.Name }).ToJson();


            //商家数据源
            var agents = _supplierUserService.GetAgentList(fxUserId, onlyGetCurDb: true,needEncryptAccount:true);
            ViewBag.Agents = agents.Select(x => new { UserName = x.AgentMobileAndRemark, x.FxUserId, x.Status, x.IsTop }).Distinct().ToJson();
            //厂家数据源
            var suppliers = _supplierUserService.GetSupplierList(fxUserId, onlyGetCurDb: true,needEncryptAccount:true);
            ViewBag.Suppliers = suppliers?.Select(x => new { UserName = x.SupplierMobileAndRemark, FxUserId = x.SupplierFxUserId, x.Status, x.IsTop, x.IsFilter }).Distinct().ToJson();
            ViewBag.SupplierUsers = suppliers?.Select(x => new { UserName = x.SupplierMobileAndRemark, FxUserId = x.SupplierFxUserId }).GroupBy(x => x.FxUserId).ToDictionary(x => x.Key, x => x.FirstOrDefault()?.UserName ?? "");
            var currentUser = SiteContext.Current.CurrentFxUser;
            var currentAgent = new SupplierUser { Mobile = currentUser.Mobile, Remark = currentUser.Remark, NickName = currentUser.NickName, UserFx = currentUser, FxUserId = fxUserId, IsTop = true };
            agents.Insert(0, currentAgent);
            ViewBag.AllAgents = agents.Select(x => new { UserName = x.AgentMobileAndRemark, x.FxUserId, x.Status, x.IsTop }).Distinct().ToJson();
        }

        #endregion

        #region 接口数据相关

        /// <summary>
        /// 基础商品详情
        /// </summary>
        /// <param name="baseProductUid"></param>
        /// <param name="baseProductSkuUid"></param>
        /// <returns></returns>
        [LogForOperatorFilter("基础商品详情")]
        public ActionResult GetBaseProductSkuDetail(long baseProductUid, long baseProductSkuUid)
        {
            var curCloudPlatform = CustomerConfig.CloudPlatformType;
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            BaseProductSku baseProductSkuInfo;

            // 目标云为精选平台 
            if (curCloudPlatform == CloudPlatformType.Alibaba.ToString())
            {
                baseProductSkuInfo = new BaseProductSkuCommonService().GetBaseProductSkuDetail(baseProductUid, baseProductSkuUid);
            }
            else
            {
                var isPdd = curCloudPlatform == CloudPlatformType.Pinduoduo.ToString();
                var apiUrl = "/BaseProductApi/GetBaseProductSkuDetail";
                var targetSiteUrl = isPdd ? CustomerConfig.AlibabaMessageDomainForPdd : CustomerConfig.AlibabaFenFaSystemUrl;
                targetSiteUrl = targetSiteUrl.TrimEnd("/") + apiUrl;
                var model = new Tuple<long, long>(baseProductUid, baseProductSkuUid);

                baseProductSkuInfo = Common.PostFxSiteApi<Tuple<long, long>, BaseProductSku>(targetSiteUrl, fxUserId, model, "跨云基础商品详情", isEncrypt: true);
            }
            var result = Json(baseProductSkuInfo);
            return result;
        }

        /// <summary>
        /// 基础商品详情
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [LogForOperatorFilter("基础商品详情")]
        public ActionResult GetBaseProductDetail(BaseProductDetailReqModel model)
        {
            try
            {
                var fxUserId = SiteContext.Current.CurrentFxUserId;
                var res = new BaseProductSkuService().GetBaseProductDetail(model, fxUserId);
                return SuccessResult(res);
            }
            catch (Exception ex)
            {
                Log.WriteError($"获取基础商品详情出错: {ex.StackTrace}");
                return FalidResult("获取基础商品详情出错！");
            }
        }

        /// <summary>
        /// 基础商品SPU列表查询
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>

        [LogForOperatorFilter("基础商品SPU列表")]
        public ActionResult LoadBaseProductList(BaseProductSearchModel model)
        {

            if (!string.IsNullOrEmpty(model.SkuCode) && model.SkuCode.Contains(','))
            {
                if (model.SkuCode.SplitToList(",").Count > 500)
                {
                    return FalidResult("查询的商品不要超过500");
                }
            }
            if (!string.IsNullOrEmpty(model.SpuCode) && model.SpuCode.Contains(','))
            {
                if (model.SpuCode.SplitToList(",").Count > 500)
                {
                    return FalidResult("查询的商品不要超过500");
                }
            }
            if (!string.IsNullOrEmpty(model.SkuUid) && model.SkuUid.Contains(','))
            {
                if (model.SkuUid.SplitToList(",").Count > 500)
                {
                    return FalidResult("查询的商品不要超过500");
                }
            }
            // 查询数据
            _baseProductSkuService = new BaseProductSkuService();
            var tuple = _baseProductSkuService.GetBaseProductList(model);
            var list = tuple.Item2;
            var listCount = tuple.Item1;
            // 数据组装
            if (list.Count > 0)
            {
                // 厂家数据
                var fxUserId = SiteContext.Current.CurrentFxUserId;
                var suppliers = _supplierUserService.GetSupplierList(fxUserId,needEncryptAccount:true);
                var supplierList = suppliers?.GroupBy(x => x.SupplierFxUserId).ToDictionary(x => x.Key, x => x.FirstOrDefault()?.SupplierMobileAndRemark ?? "");
                var skus = list.SelectMany(a => a.Skus).ToList();
                var skuCodes = skus.Select(a => a.SkuCode).Distinct().ToList();
                var skuUid = skus.Select(a => a.Uid).Distinct().ToList();

                var mainImageIds = list.Select(a => a.Uid).Where(a => a != 0).Distinct().ToList();
                var mainImages = new BaseProductImageRepository().GetListByProductUid(mainImageIds);
                // 商品资料信息
                var baseProductUid = list.Select(a => a.Uid).Distinct().ToList();
                var supplierRelations = new BaseOfSupplierSkuRelationService(fxUserId).GetListByProductUid(baseProductUid, fxUserId);
                var supplierInfos = supplierRelations.GroupBy(a => a.BaseProductUid).Select(g => g.FirstOrDefault()).ToList();
                // 平台资料
                var ptInfos = new PtProductInfoService().GetByProductUids(baseProductUid, fxUserId);
                // 绑定信息
                var supplierConfigs = new BaseProductSkuSupplierConfigService().GetAllListByProductUids(baseProductUid);
                // 规格库存数据
                var stocks = _service.GetStockBySkuCode(skuCodes);
                // 规格关联数据
                var baseProductSkuRelations = new BaseOfPtSkuRelationService().GetListBySkuUids(skuUid, fxUserId);
                var baseProductSkuRelationsValue = baseProductSkuRelations.SelectMany(a => a.Value).ToList();
                // 规格货盘关联数据
                var supplierProductSkuRelationService = new BaseOfSupplierSkuRelationService(fxUserId);
                var supplierProudctSkuRelations =
                    supplierProductSkuRelationService.GetListByProductSkuUid(skuUid, fxUserId);

                foreach (var sku in skus)
                {
                    var skuConfig = supplierConfigs.Where(c => c.RefUid == sku.Uid && c.RefType == "Sku").FirstOrDefault();
                    var proConfig = supplierConfigs.Where(c => c.RefUid == sku.BaseProductUid && c.RefType == "Product").FirstOrDefault();

                    // 绑定信息采用方式
                    sku.SkuSupplierUserId = 0;
                    if (proConfig != null)
                        sku.SkuSupplierUserId = proConfig?.SupplierFxUserId ?? 0;
                    if (skuConfig != null)
                        sku.SkuSupplierUserId = skuConfig?.SupplierFxUserId ?? 0;
                    var supplierRes = supplierList.Where(t => t.Key == sku.SkuSupplierUserId).FirstOrDefault();
                    sku.SupplierName = supplierRes.Value;

                    var stock = stocks.Where(s => s.SkuCargoNumber == sku.SkuCode).FirstOrDefault();
                    var relations = baseProductSkuRelationsValue.Where(a => a.BaseProductSkuUid == sku.Uid).ToList();
                    var relationSuppliers = supplierProudctSkuRelations.Where(a => a.BaseProductSkuUid == sku.Uid).ToList();
                    sku.ImageUrl = ImgHelper.ChangeImgUrl(sku.ImageUrl);
                    sku.StockCount = stock?.StockCount;
                    sku.RelationCount = relations == null ? 0 : relations.Count;
                    if (relationSuppliers.Count > 0)
                        sku.RelationCount = sku.RelationCount + relationSuppliers.Count;
                }
                // 商品库存
                foreach (var p in list)
                {
                    var hasStocks = p.Skus.Where(a => a.StockCount.HasValue).ToList(); ;
                    //p.Skus.FirstOrDefault().StockCount = Int32.MaxValue;
                    p.StockCount = hasStocks.Count > 0 ? hasStocks.Sum(a => a.StockCount?.ToLong()) : null;

                    var supplierInfo = supplierInfos.Where(a => a.BaseProductUid == p.Uid).Distinct().ToList();
                    var ptInfo = ptInfos.Where(a => a.BaseProductUid == p.Uid).Distinct().ToList();
                    var infos = _baseProductSkuService.MakeInfoTage(supplierInfo, ptInfo);
                    p.PlatformInfoTags = infos;


                    var pConfig = supplierConfigs.Where(c => c.RefUid == p.Uid && c.RefType == "Product").FirstOrDefault();
                    p.ProductSupplierUserId = pConfig?.SupplierFxUserId ??0;

                    var supplierRes = supplierList.Where(t => t.Key == p.ProductSupplierUserId).FirstOrDefault();
                    p.SupplierName = supplierRes.Value;

                    p.RelationCount = p.Skus.Where(a => a.RelationCount.HasValue).Sum(a => a.RelationCount);
                    p.IsMultipleSupplierUser = p.Skus.Where(a => a.SkuSupplierUserId.HasValue).Count() > 0 ? true : false;

                    var costPriceMax = p.Skus.Where(a => a.CostPrice.HasValue).Max(a => a.CostPrice);
                    var costPriceMin = p.Skus.Where(a => a.CostPrice.HasValue).Min(a => a.CostPrice);
                    p.CostPrice = _baseProductSkuService.MakePrice(costPriceMax, costPriceMin);

                    var SettlePriceMax = p.Skus.Where(a => a.SettlePrice.HasValue).Max(a => a.SettlePrice);
                    var SettlePriceMin = p.Skus.Where(a => a.SettlePrice.HasValue).Min(a => a.SettlePrice);
                    p.SettlePrice = _baseProductSkuService.MakePrice(SettlePriceMax, SettlePriceMin);

                    var DistributePriceMax = p.Skus.Where(a => a.DistributePrice.HasValue).Max(a => a.DistributePrice);
                    var DistributePriceMin = p.Skus.Where(a => a.DistributePrice.HasValue).Min(a => a.DistributePrice);
                    p.DistributePrice = _baseProductSkuService.MakePrice(DistributePriceMax, DistributePriceMin);

                    //p.DistributePrice = DistributePriceMin == DistributePriceMax ? $"{DistributePriceMax}" : $"{DistributePriceMin}-{DistributePriceMax}";

                    p.SkuCount = p.Skus.Count();
                    p.IsMore = p.Skus.Count > 8 ? true : false;
                    p.SupplyMethod = _baseProductSkuService.MakeSupplierTag(p, fxUserId);

                    //if (p.Skus.All(a => a.SupplierFxUserId == 0))
                    //    p.SupplyMethod = 0;
                    //else if (p.Skus.All(a => a.SupplierFxUserId == -1))
                    //    p.SupplyMethod = 1;
                    //else if (p.Skus.All(a => a.SupplierFxUserId > 0))
                    //    p.SupplyMethod = 2;
                    //else if (p.Skus.Exists(a => a.SupplierFxUserId == -1) && p.Skus.Where(a => a.SupplierFxUserId > 0).ToList().Count == 1)
                    //    p.SupplyMethod = 3;
                    //else if (p.Skus.Exists(a => a.SupplierFxUserId == -1) && p.Skus.Where(a => a.SupplierFxUserId > 0).ToList().Count == 0)
                    //    p.SupplyMethod = 1;
                    //else if (p.Skus.Where(a => a.SupplierFxUserId == -1).ToList().Count == 0 && p.Skus.Exists(a => a.SupplierFxUserId > 0))
                    //    p.SupplyMethod = 2;
                    //else if (p.Skus.Exists(a => a.SupplierFxUserId == -1) && p.Skus.Exists(a => a.SupplierFxUserId > 0))
                    //    p.SupplyMethod = 4;

                    // 未包含sku搜索默认显示8条数据
                    if (model.SkuCode.IsNotNullOrEmpty() ||
                        model.SkuUid.IsNotNullOrEmpty() ||
                        model.SkuSubject.IsNotNullOrEmpty()
                        //model.SkuSupplyType.HasValue ||
                        /*model.SelectSupplierId.IsNotNullOrEmpty()*/)
                    {
                        p.Skus = p.Skus;
                    }
                    else
                    {
                        p.Skus = p.Skus.Take(8).ToList();
                    }


                    var m = mainImages.FirstOrDefault(a => a.ImageObjectId == p.MainImageObjectId);
                    if (m != null)
                    {
                        var suffix = m.Suffix.IsNullOrEmpty() ? string.Empty : $".{m.Suffix}";
                        var url = $"{m?.Domain}/{m?.Url}/{m?.Name}{suffix}";
                        p.MainImageUrl = ImgHelper.ChangeImgUrl(url);
                    }

                    p.Skus = _baseProductSkuService.SortBaseProductSku(p.Skus);
                }
            }
            var res = new PagedResultModel<BaseProductEntity>()
            {
                PageIndex = model.PageIndex,
                PageSize = model.PageSize,
                Rows = list,
                Total = listCount
            };
            var result = Json(res);
            return result;
        }

        /// <summary>
        /// 基础商品SKU列表查询
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>

        [LogForOperatorFilter("基础商品SKU列表")]
        public ActionResult LoadBaseProductSkuList(long baseProductUid)
        {
            _baseProductSkuService = new BaseProductSkuService();
            var list = _baseProductSkuService.GetBaseProductSkuList(baseProductUid);
            if (list != null && list.Count > 0)
            {
                // 厂家数据
                var fxUserId = SiteContext.Current.CurrentFxUserId;
                var suppliers = _supplierUserService.GetSupplierList(fxUserId,needEncryptAccount:true);
                var supplierList = suppliers?.GroupBy(x => x.SupplierFxUserId).ToDictionary(x => x.Key, x => x.FirstOrDefault()?.SupplierMobileAndRemark ?? "");
                var skuCodes = list.Select(a => a.SkuCode).Where(a => a.IsNotNullOrEmpty()).Distinct().ToList();
                var skuUid = list.Select(a => a.Uid).Distinct().ToList();

                // 规格库存数据
                var stocks = _service.GetStockBySkuCode(skuCodes);

                // 规格关联数据
                var baseProductSkuRelations = new BaseOfPtSkuRelationService().GetListBySkuUids(skuUid, fxUserId);
                var baseProductSkuRelationsValue = baseProductSkuRelations.SelectMany(a => a.Value).ToList();

                // 规格货盘关联数据
                var supplierProductSkuRelationService = new BaseOfSupplierSkuRelationService(fxUserId);
                var supplierProudctSkuRelations =
                    supplierProductSkuRelationService.GetListByProductSkuUid(skuUid, fxUserId);

                foreach (var sku in list)
                {
                    var skuSupplierUserId = sku.SkuSupplierUserId ?? 0;
                    if (skuSupplierUserId == 0)
                        sku.SkuSupplierUserId = sku.ProdutSupplierUserId??0;

                    var supplierRes = supplierList.Where(t => t.Key == sku.SkuSupplierUserId).FirstOrDefault();
                    sku.SupplierName = supplierRes.Value;

                    var stock = stocks.Where(s => s.SkuCargoNumber == sku.SkuCode).FirstOrDefault();
                    var relations = baseProductSkuRelationsValue.Where(a => a.BaseProductSkuUid == sku.Uid).ToList();
                    var relationSuppliers = supplierProudctSkuRelations.Where(a => a.BaseProductSkuUid == sku.Uid).ToList();

                    sku.StockCount = stock?.StockCount;
                    sku.ImageUrl = ImgHelper.ChangeImgUrl(sku.ImageUrl);

                    sku.RelationCount = relations == null ? 0 : relations.Count;
                    if (relationSuppliers.Count > 0)
                        sku.RelationCount = sku.RelationCount + relationSuppliers.Count;
                }
            }
            // 排序处理
            list = _baseProductSkuService.SortBaseProductSku(list);
            var result = Json(list);
            return result;
        }

        /// <summary>
        /// 基础商品关联列表
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [LogForOperatorFilter("基础商品关联列表")]
        public ActionResult BaseProductSkuRelationBindList(BaseProductSkuBindSearchModel model)
        {
            if (!string.IsNullOrEmpty(model.Codes) && model.Codes.SplitToList(",").Count > 500)
            {
                return FalidResult("查询的SKU不要超过500！");
            }
            if (!model.BaseProductUid.HasValue)
            {
                return FalidResult("基础商品Uid不能为空！");
            }
            if (!model.BaseProductSkuUid.HasValue)
            {
                return FalidResult("基础商品SkuUid不能为空！");
            }
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var _reqModel = new FxUserShopQueryModel { FxUserId = fxUserId };
            var fxUserShops = new FxUserShopService().GetList(_reqModel)?.Item2 ?? new List<FxUserShop>();
            fxUserShops = fxUserShops.Where(x => x.Status != FxUserShopStatus.UnBind).ToList();
            model.ShopId = fxUserShops.Select(a => a.ShopId).Distinct().ToList();
            model.Type = null;
            model.FxUserId = fxUserId;
            try
            {
                var res = _productFxService.GetProductSkuBindList(model);
                var total = res.Item1;
                var rows = res.Item2;
                return SuccessResult(new
                {
                    PageIndex = model.PageIndex,
                    PageSize = model.PageSize,
                    Total = total,
                    List = rows
                });
            }
            catch (Exception ex)
            {
                Log.WriteError($"基础商品绑定列表查询！：{ex.Message}");
                return FalidResult("基础商品绑定列表查询！");
            }
        }

        /// <summary>
        /// 基础商品关联明细列表
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [LogForOperatorFilter("基础商品关联明细列表")]
        public ActionResult BaseProductSkuRelationDetailList(BaseProductSkuBindSearchModel model)
        {
            if (!string.IsNullOrEmpty(model.Codes) && model.Codes.Contains(','))
            {
                if (model.Codes.SplitToList(",").Count > 500)
                {
                    return FalidResult("查询的SKU不要超过500");
                }
            }

            var fxUserId = SiteContext.Current.CurrentFxUserId;
            model.FxUserId = fxUserId;
            var _reqModel = new FxUserShopQueryModel { FxUserId = fxUserId };
            var fxUserShops = new FxUserShopService().GetList(_reqModel)?.Item2 ?? new List<FxUserShop>();
            fxUserShops = fxUserShops.Where(x => x.Status != FxUserShopStatus.UnBind).ToList();
            model.ShopId = fxUserShops.Select(a => a.ShopId).Distinct().ToList();
            var res = _productFxService.GetProductSkuBindDetailList(model);
            var total = res.Item1;
            var rows = res.Item2;

            if (!model.IsQueryOtherCount)
                return SuccessResult(new { Total = total, List = rows });

            #region 跨云查询其他云平台的关联数量 2025-03-17 更新

            model.OnlyQueryCount = true;
            var relationCountDict = new ConcurrentDictionary<string, int>();
            var cloudRelationCount = res.Item3;
            var cloudList = new List<string>()
            {
                CloudPlatformType.Alibaba.ToString(),
                CloudPlatformType.TouTiao.ToString(),
                CloudPlatformType.Jingdong.ToString(),
                CloudPlatformType.Pinduoduo.ToString(),
                "MyStation"
            };
            cloudList.Remove(CustomerConfig.CloudPlatformType);
            var maxParall = 4;
            if (CustomerConfig.IsLocalDbDebug)
                maxParall = 1;
            Parallel.ForEach(cloudList, new ParallelOptions() { MaxDegreeOfParallelism = maxParall }, cloud =>
            {
                if (CustomerConfig.CloudPlatformType != cloud && CustomerConfig.IsLocalDbDebug)
                {
                    relationCountDict.TryAdd(cloud, 0);
                    return;
                }
                try
                {
                    var count = 0;
                    if (cloud == "MyStation")
                    {
                        try
                        {
                            if (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString())
                            {
                                var service = new BaseOfSupplierSkuRelationService(fxUserId);
                                count = service.GetSupplierSkuRelations(model, fxUserId).Item1;
                            }
                            else
                            {
                                var apiUrl = "/BaseProductApi/GetSupplierSkuRelations";
                                var targetSiteUrl =
                                    CustomerConfig.GetTargetSiteUrl(CloudPlatformType.Alibaba.ToString()).TrimEnd("/") +
                                    apiUrl;

                                // 拼多多无法直接访问外网，需要中转请求
                                if (CustomerConfig.CloudPlatformType == CloudPlatformType.Pinduoduo.ToString())
                                {
                                    targetSiteUrl = CustomerConfig.AlibabaMessageDomainForPdd.TrimEnd("/") + apiUrl;
                                }

                                count = Common.PostFxSiteApi<BaseProductSkuBindSearchModel,
                                        Tuple<int, List<BaseOfSupplierProductSkuRelation>>>(targetSiteUrl, fxUserId,
                                        model,
                                        "跨云查询我的小站关联规格", isEncrypt: true)?.Item1 ?? 0;

                                Log.Debug(() =>$"{fxUserId}用户跨云查询我的小站关联规格，发起云：{CustomerConfig.CloudPlatformType}，url：{targetSiteUrl}，查询结果{count}",
                                    ModuleFileName.BaseProduct);

                            }
                        }
                        catch (Exception ex)
                        {
                            Log.WriteError($"{CustomerConfig.CloudPlatformType},{fxUserId}用户查询我的小站关联规格发生异常：{ex}", ModuleFileName.BaseProduct);
                        }
                    }
                    else
                    {
                        model.FromCloudPlatform = CustomerConfig.CloudPlatformType;
                        var apiUrl = "/BaseProductApi/BaseProductSkuRelationDetailList";
                        var targetSiteUrl = CustomerConfig.GetTargetSiteUrl(cloud).TrimEnd("/") + apiUrl;

                        // 拼多多无法直接访问外网，需要中转请求
                        if (CustomerConfig.CloudPlatformType == CloudPlatformType.Pinduoduo.ToString())
                        {
                            targetSiteUrl = CustomerConfig.AlibabaMessageDomainForPdd.TrimEnd("/") + apiUrl;
                        }

                        count = Common
                            .PostFxSiteApi<BaseProductSkuBindSearchModel, Tuple<int, List<ProductSkuFx>>>(targetSiteUrl,
                                fxUserId, model, "跨云查询基础商品规格关联数", isEncrypt: true)?.Item1 ?? 0;

                        Log.Debug(() => $"{fxUserId}用户跨云查询基础商品规格关联数，发起云：{CustomerConfig.CloudPlatformType}，url：{targetSiteUrl}，查询结果{count}",
                            ModuleFileName.BaseProduct);
                        cloudRelationCount += count;
                    }

                    var overCount = 5;
                    while (!relationCountDict.TryAdd(cloud, count) && overCount > 0)
                    {
                        overCount--;
                    }

                    if (overCount <= 0)
                        Log.WriteError("跨云查询基础商品规格关联数，写入并发字典失败次数过多", ModuleFileName.BaseProduct);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"{cloud} 跨云查询基础商品规格关联数发生异常：{ex}", ModuleFileName.BaseProduct);
                }

            });

            #endregion

            #region 如果基础商品库的关联数不等于四个云平台的关联数，则进入关联数据一致性检查逻辑

            if ((model.BaseProductDbRelationCount != cloudRelationCount && model.BaseProductSkuUid != null) || CustomerConfig.IsLocalDbDebug)
            {
                cloudList.Remove("MyStation");
                Log.Debug(
                    () => $"用户{fxUserId}基础商品查询关联规格，检测到关联数据不一致，当前云：{CustomerConfig.CloudPlatformType}，基础商品库关联数：{model.BaseProductDbRelationCount}，全云关联数：{cloudRelationCount}，进行关联数据一致性检查",
                    ModuleFileName.BaseProduct);
                // 同步在当前云进行检测和补偿，其他云异步
                var result = BusinessDbRelationDataCompensate(cloudList, model);
                if (result != null)
                {
                    total = result.Item1;
                    rows = result.Item2;
                }
            }

            #endregion

            return SuccessResult(new { Total = total, List = rows, RelationCountDict = relationCountDict });
        }

        /// <summary>
        /// 基础商品关联数据补偿
        /// </summary>
        /// <param name="cloudList"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        private Tuple<int, List<ProductSkuFx>, int> BusinessDbRelationDataCompensate(List<string> cloudList,BaseProductSkuBindSearchModel model)
        {
            var fxUserId = SiteContext.GetCurrentFxUserId();
            var skuUid = model.BaseProductSkuUid.ToLong();

            cloudList.Remove("MyStation");

            #region 获取任务

            var taskService = new AsyncTaskService();
            var query = new AsyncTaskQueryModel
            {
                ShopId = SiteContext.Current.CurrentShopId,
                FxUserId = fxUserId,
                StatusList = new List<int>() { 0, 1 },
                Flag = "BaseProduct-BusinessDbRelationDataCompensate",
                IsSetExpiress = true,
                ExpiressTime = 5
            };

            var task = taskService.GetAsyncTask(query);

            #endregion

            if (task == null)
            {
                task = new AsyncTask
                {
                    Flag = "BaseProduct-BusinessDbRelationDataCompensate",
                    CData = $"BaseSkuUid：{skuUid}",
                };
                taskService.AddAsyncTask(task);

                task.Status = 1;
                task.UpdateTime = DateTime.Now;
                taskService.UpdateAsyncTask(task);

                // 同步在当前云进行检测和补偿，其他云异步
                try
                {
                    // 查询基础商品库的关联数据
                    List<BaseOfPtSkuRelation> baseProductDbRelationList = null;

                    #region 查询基础商品库的关联数据
                    // 精选平台直接连接基础商品库查询

                    if (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString() || CustomerConfig.IsLocalDbDebug)
                    {

                        var baseProductService = new BaseOfPtSkuRelationRepository(true, fxUserId);
                        baseProductDbRelationList = baseProductService.GetListBySkuUid(skuUid, fxUserId: fxUserId);

                        var list = baseProductDbRelationList;
                        Log.Debug(() => $"用户{fxUserId}补偿业务库基础商品关联数据，在精选云平台查询基础商品库，关联数：{list?.Count}", ModuleFileName.BaseProduct);

                    }
                    else // 非精选平台通过WebPost查询基础商品库
                    {
                        Log.Debug(() => $"用户{fxUserId}补偿业务库基础商品关联数据，在云平台{CustomerConfig.CloudPlatformType}调用WebPost查询基础商品库", ModuleFileName.BaseProduct);

                        var token = Request.Params["token"];
                        if (token.IsNullOrEmpty())
                            token = new ShopService().GetTokenByShopId(SiteContext.GetCurrentShopId())?.Token;
                        if (token.IsNullOrEmpty())
                            throw new LogicException($"{fxUserId}用户token获取失败！");

                        var apiUrl = "/BaseProductApi/GetBaseSkuRelationListApi";
                        var targetSiteUrl = CustomerConfig.AlibabaFenFaSystemUrl.TrimEnd("/") + apiUrl;

                        // 拼多多无法直接访问外网，需要中转请求
                        if (CustomerConfig.CloudPlatformType == CloudPlatformType.Pinduoduo.ToString())
                        {
                            targetSiteUrl = CustomerConfig.AlibabaMessageDomainForPdd.TrimEnd("/") + apiUrl;
                        }

                        baseProductDbRelationList = Common.PostFxSiteApi<long, List<BaseOfPtSkuRelation>>(targetSiteUrl, fxUserId, skuUid, "跨云查询基础商品规格关联数", isEncrypt: true);

                        Log.Debug(() => $@"用户{fxUserId}补偿业务库基础商品关联数据，在云平台{CustomerConfig.CloudPlatformType}调用WebPost查询基础商品库，targetSiteUrl：{targetSiteUrl}
                                   ，当前Ip：{Request?.UserHostAddress}，查询数据条数{baseProductDbRelationList?.Count}", ModuleFileName.BaseProduct);

                    }

                    if (baseProductDbRelationList.IsNullOrEmptyList())
                    {
                        Log.WriteWarning($"用户{fxUserId}补偿业务库基础商品关联数据，查询基础商品库关联数据为空！", ModuleFileName.BaseProduct);
                        return null;
                    }

                    #endregion

                    // 当前云直接执行，其他云通过HttpPost执行
                    var (currentResult, filterSkuCodeList, isDataChange) =
                        new BaseOfPtSkuRelationService(false, fxUserId).BusinessDbRelationDataCompensate(skuUid, baseProductDbRelationList);

                    Log.Debug(
                        () => $"用户{fxUserId}补偿基础商品关联数据，当前云：{CustomerConfig.CloudPlatformType}，执行结果：{currentResult}",
                        ModuleFileName.BaseProduct);

                    // 其他云调用补偿接口
                    if (!CustomerConfig.IsLocalDbDebug)
                    {
                        Task.Run(() =>
                        {
                            try
                            {
                                cloudList.ForEach(cloud =>
                                {
                                    var apiUrl = "/BaseProductApi/BusinessDbRelationDataCompensate";


                                    var targetSiteUrl = CustomerConfig.GetTargetSiteUrl(cloud).TrimEnd("/") + apiUrl;

                                    if (CustomerConfig.CloudPlatformType == CloudPlatformType.Pinduoduo.ToString())
                                    {
                                        targetSiteUrl = CustomerConfig.AlibabaMessageDomainForPdd.TrimEnd("/") + apiUrl;
                                    }

                                    Log.Debug(() => $"用户{fxUserId}跨云补偿基础商品关联数据，开始--当前云：{CustomerConfig.CloudPlatformType}，目标云：{cloud}，targetSiteUrl={targetSiteUrl}",
                                        ModuleFileName.BaseProduct);

                                    var result = Common.PostFxSiteApi<Tuple<long, List<string>>, string>(targetSiteUrl,
                                        fxUserId, Tuple.Create<long, List<string>>(model.BaseProductSkuUid.ToLong(), filterSkuCodeList),
                                        "跨云补偿业务库基础商品关联", isEncrypt: true);

                                    Log.Debug(() => $"用户{fxUserId}跨云补偿基础商品关联数据，补偿结果--当前云：{CustomerConfig.CloudPlatformType}，目标云：{cloud}，targetSiteUrl={targetSiteUrl}，执行结果：{result}",
                                        ModuleFileName.BaseProduct);

                                });
                            }
                            catch (Exception ex)
                            {
                                Log.Debug(() => $"用户{fxUserId}基础商品查询关联规格，检测到关联数据不一致，跨云处理补偿发生异常：{ex}",
                                    ModuleFileName.BaseProduct);
                            }

                        });
                    }

                    // 有数据修改，当前云重新查询一次数据
                    if (isDataChange)
                    {
                        model.IsQueryAllDbRelationCount = false;
                        return _productFxService.GetProductSkuBindDetailList(model);
                    }
                }
                catch (Exception ex)
                {
                    task.ExceptionDesc = $"{ex.ToJson()}";
                    Log.WriteError($"用户{fxUserId}业务库关联数据补偿发生异常，当前云：{CustomerConfig.CloudPlatformType}，异常：{ex}",
                        ModuleFileName.BaseProduct);
                }
                finally
                {
                    // 测试环境才更新状态，正式环境不更新任务状态，过期时间5分钟之内，都会是执行中，防止请求过于频繁
                    if (CustomerConfig.IsDebug && fxUserId != 74)
                        task.Status = task.ExceptionDesc.IsNullOrEmpty() ? 5 : -1;
                    task.UpdateTime = DateTime.Now;
                    taskService.UpdateAsyncTask(task);
                }
            }
            else
            {
                Log.WriteWarning($"用户{fxUserId}检查关联数据一致性，当前云平台：{CustomerConfig.CloudPlatformType}。有任务正在进行，TaskId：{task.Id}", ModuleFileName.BaseProduct);
                return null;
            }
            

            return null;
        }

        /// <summary>
        /// 基础商品自动关联
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [LogForOperatorFilter("基础商品自动关联")]
        public ActionResult AutoRelationBind(BaseProductAutoMappingModel model)
        {
            return AutoRelationBindNew(model);
            if (model.SkuUidStrDic == null || model.SkuUidStrDic.Any() == false) return FalidResult("请选择关联商品！");

            var dbName = Request["dbname"];
            if (dbName.IsNotNullOrEmpty())
            {
                try
                {
                    dbName = DES.DecryptDES(dbName, CustomerConfig.LoginCookieEncryptKey);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"平台dbName解析失败：{ex.Message}");
                    return FalidResult("平台dbName解析失败！");
                }
            }
            else return FalidResult("平台dbName不能为空！");

            var curFxUserId = SiteContext.Current.CurrentFxUserId;

            var baseProductSkuService = new BaseProductSkuService();
            if (!model.NoCheckedSkuUids.IsNullOrEmptyList() && !model.NoCheckedProductUids.IsNullOrEmptyList())
                baseProductSkuService.FiltedBaseProductSku(model.SkuUids, curFxUserId, model.NoCheckedSkuUids, model.NoCheckedProductUids);

            var skuUidList = model.SkuUids.SelectMany(a => a.Value).ToList();
            var ents = baseProductSkuService.GetListBySkuUids(skuUidList, curFxUserId);
            model.SkuCodeList = ents.Select(x => x.SkuCode).ToList();
            model.SkuCodeDic = ents.ToDictionary(x => x.SkuCode, x => x.Uid);

            if (model.SkuCodeList.Count == 0) return FalidResult("未找到关联商品！");

            // 查看是否存在任务中
            var existTask = _asyncTaskService.GetAsyncTask(new AsyncTaskQueryModel
            {
                Flag = "BaseProductAutoRelationBind",
                FxUserId = curFxUserId,
                StatusList = new List<int> { 0, 1 }
            });

            if (existTask != null)
                return existTask.Status == 0
                    ? FalidResult("当前有任务但未开始执行，请等3分钟后刷新页面重试！")
                    : FalidResult("当前有任务正在执行，请稍后再试！");

            // 查看是否存在Redis锁
            var redisKey = CacheKeys.BaseProductAutoRelationBindKey.Replace("{FxUserId}", curFxUserId.ToString());
            if (RedisHelper.Exists(redisKey))
            {
                var redisValue = RedisHelper.Get(redisKey).ToObject<List<string>>();
                // 找到redisValue和model.SkuCodeList的交集
                var intersect = redisValue?.Intersect(model.SkuCodeList).ToList();
                if (intersect != null && intersect.Any())
                {
                    // 返回已存在的交集，用逗号分隔
                    if (CustomerConfig.IsDebug) Log.Debug($"操作太频繁啦，请稍后再试！商品SkuCode：{string.Join(",", intersect)}");
                    return FalidResult($"操作太频繁啦，请稍后再试！");
                }
            }
            model.Request = new RequestHttpModel { DbName = Request["dbname"], UserHostAddress = Request.UserHostAddress };
            model.Clear();

            // 生成任务并发送消息
            var task = new AsyncTask
            {
                TaskCode = CommUtls.GetGuidShortMd5(),
                FxUserId = curFxUserId,
                CData = model.ToJson(),
                CloudPlatformType = CustomerConfig.CloudPlatformType,
                Flag = "BaseProductAutoRelationBind",
                TotalCount = model.SkuCodeList.Count,
                CreateTime = DateTime.Now,
            };
            _asyncTaskService.AddAsyncTask(task);

            var msg = new MessageRecord
            {
                BusinessId = task.TaskCode,
                CreateFxUserId = curFxUserId,
                CreateTime = DateTime.Now,
                FxUserId = curFxUserId,
                DbName = dbName,
                DataJson = task.ToJson(),
                TargetCloud = CustomerConfig.CloudPlatformType,
                MsgType = BaseProductMsgType.AutoRelationBind
            };
            var result = new MessageRecordService().SendBusinessMessage(new List<MessageRecord> { msg });
            if (result == 0) return FalidResult("消息发送失败！");

            // 加Redis锁，一分钟
            var setRedisValue = model.SkuCodeList.ToJson();
            RedisHelper.Set(redisKey, setRedisValue, TimeSpan.FromMinutes(1));

            // Redis任务进度标记
            var redisProcessKey =
                CacheKeys.BaseProductAutoRelationTaskProgressKey.Replace("{FxUserId}", curFxUserId.ToString());
            var taskViewModel = new AutoRelationTaskViewModel
            {
                TaskCode = task.TaskCode,
                SuccessCount = 0,
                FailCount = 0,
                Process = 0,
                CreateTime = DateTime.Now
            };

            RedisHelper.Set(redisProcessKey, taskViewModel.ToJson(), 60);
            return SuccessResult("任务已提交，请稍后查看！");
        }

        /// <summary>
        /// 获取自动关联进度
        /// </summary>
        /// <param name="fxShopId"></param>
        /// <returns></returns>
        public ActionResult GetAutoRelationProcess(int fxShopId)
        {
            var curFxUserId = SiteContext.Current.CurrentFxUserId;
            var curShopId = SiteContext.Current.CurrentShopId;
            if (fxShopId != curShopId) return FalidResult("无权限查看！");

            var redisKey = CacheKeys.BaseProductAutoRelationTaskProgressKey.Replace("{FxUserId}", curFxUserId.ToString());

            if (RedisHelper.Exists(redisKey))
            {
                var taskViewModel = RedisHelper.Get<AutoRelationTaskViewModel>(redisKey);
                return SuccessResult(taskViewModel);
            }

            // 任务不存在，或者已经完成
            return SuccessResult(-1);
        }

        /// <summary>
        /// 中断自动关联任务
        /// </summary>
        /// <param name="fxShopId"></param>
        /// <returns></returns>
        public ActionResult BreakTask(int fxShopId)
        {
            var curFxUserId = SiteContext.Current.CurrentFxUserId;
            var curShopId = SiteContext.Current.CurrentShopId;
            if (fxShopId != curShopId) return FalidResult("无权限查看！");

            // 查看是否存在任务中
            var existTask = _asyncTaskService.GetAsyncTask(new AsyncTaskQueryModel
            {
                Flag = "BaseProductAutoRelationBind",
                FxUserId = curFxUserId,
                StatusList = new List<int> { 0, 1 }
            });

            if (existTask == null) return FalidResult("任务不存在！");

            // 中断任务
            existTask.Status = -10;
            existTask.UpdateTime = DateTime.Now;
            _asyncTaskService.UpdateAsyncTask(existTask);

            var redisKey = CacheKeys.BaseProductAutoRelationBindKey.Replace("{FxUserId}", curFxUserId.ToString());
            if (RedisHelper.Exists(redisKey))
            {
                RedisHelper.Del(redisKey);
            }

            var redisProcessKey = CacheKeys.BaseProductAutoRelationTaskProgressKey.Replace("{FxUserId}", curFxUserId.ToString());
            if (RedisHelper.Exists(redisProcessKey))
            {
                RedisHelper.Del(redisProcessKey);
            }

            return SuccessResult("任务已取消！");
        }

        /// <summary>
        /// 基础商品删除
        /// </summary>
        /// <param name="uidList"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public ActionResult BaseProductRemove(List<long> uidList, int fxUserId)
        {
            var curFxUserId = SiteContext.Current.CurrentFxUserId;
            if (fxUserId != curFxUserId) return FalidResult("无权限删除！");

            if (uidList.IsNullOrEmptyList()) return FalidResult("请选择要删除的商品！");

            try
            {
                var res = new BaseProductEntityService().RemoveBaseProduct(uidList, fxUserId);
                return SuccessResult(res);
            }
            catch (Exception ex)
            {
                Log.WriteError($"{curFxUserId}用户删除基础商品发生异常：{ex}",ModuleFileName.BaseProduct);
            }
            
            return FalidResult("系统繁忙，请稍会儿再试");
        }

        /// <summary>
        /// 基础商品SKU删除
        /// </summary>
        /// <param name="uidList"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public ActionResult BaseProductSkuRemove(List<long> uidList, int fxUserId)
        {
            var curFxUserId = SiteContext.Current.CurrentFxUserId;
            if (fxUserId != curFxUserId) return FalidResult("无权限删除！");

            if (uidList.IsNullOrEmptyList()) return FalidResult("请选择要删除的商品Sku！");

            var baseProductSkuService = new BaseProductSkuService();
            var baseProductService = new BaseProductEntityService();
            var baseOfPtSkuRelationService = new BaseOfPtSkuRelationService();

            // 获取sku信息
            var skuEntList = baseProductSkuService.GetListBySkuUids(uidList, curFxUserId);
            if (skuEntList.IsNullOrEmptyList()) return FalidResult("未找到要删除的商品Sku！");

            // 获取基础商品信息
            var baseProductEntity = baseProductService.GetByUid(skuEntList.First().BaseProductUid);

            // 获取关联信息
            var baseSkuPtRelation = baseOfPtSkuRelationService.GetListBySkuUids(uidList, curFxUserId);
            if (baseSkuPtRelation.Any())
            {
                return FalidResult("当前基础商品已关联店铺商品，请先解绑数据再进行删除");
            }

            try
            {
                baseProductSkuService.BulkDelete(skuEntList);
                var existSkuCount = baseProductSkuService.GetCountByProductUid(baseProductEntity.Uid, fxUserId);

                // 如果没有sku了，删除基础商品
                if (existSkuCount == 0)
                {
                    baseProductService.RemoveBaseProduct(baseProductEntity.Uid, fxUserId);
                }
            }
            catch (Exception e)
            {
                Log.WriteError($"基础商品SKU删除失败：{e.Message}");
                return FalidResult("基础商品SKU删除失败！");
            }

            return SuccessResult("删除成功");
        }

        /// <summary>
        /// 关联基础商品
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [LogForOperatorFilter("关联基础商品")]
        public ActionResult BaseProductSkuRelationBind(BaseProductSkuBindModel model)
        {
            if (model.SkuCode.IsNullOrEmpty())
            {
                return FalidResult("基础商品编码不能为空！");
            }
            if (model.SkuList.Count == 0)
            {
                return FalidResult("请选择关联商品！");
            }
            if (model.SkuList.Any(a => string.IsNullOrEmpty(a.ProductCode)))
            {
                return FalidResult("选择的关联商品ProductCode不能为空！");
            }
            if (model.SkuList.Any(a => string.IsNullOrEmpty(a.ProductSkuCode)))
            {
                return FalidResult("选择的关联商品ProductSkuCode不能为空！");
            }
            model.IsAllUseWarehouse = true;//默认为true
            model.SkuList?.ForEach(a => { a.IsDisabledUseWarehouse = false; });//默认为false
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var baseOfPtSkuRelationService = new BaseOfPtSkuRelationService(false);
            var result = baseOfPtSkuRelationService.BaseProductSkuRelationBindNew(model,
                new RequestHttpModel { DbName = Request["dbname"], UserHostAddress = Request.UserHostAddress }, fxUserId);
            return result.Item1 ? SuccessResult(result.Item2) : FalidResult(result.Item2);
        }

        /// <summary>
        /// 基础商品关联切换库存扣减按钮
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult SkuRelationSwitchStatus(BaseProductSkuRelationSwitchModel model)
        {
            var connectionString = SiteContext.Current.CurrentDbConfig.ConnectionString;
            var _wareHouseService = new WareHouseService(connectionString);
            var curUserId = SiteContext.Current.CurrentFxUserId;
            var baseOfPtSkuRelation = new BaseOfPtSkuRelationService(false).GetListBySkuUid(model.SkuUid)
                .FirstOrDefault(x => x.ProductSkuCode == model.ProductSkuCode);

            if (baseOfPtSkuRelation == null) return FalidResult("未找到关联信息！");

            var ownCode = _wareHouseService.GetOwnerCode(curUserId);

            // 开启库存扣减
            if (model.IsUseWarehouse)
            {
                var req = new WarehouseSkuBindRequest
                {
                    IsRollback = false,
                    OwnerCode = ownCode,
                    PlatformSkuCode = baseOfPtSkuRelation.ProductSkuCode,
                    PlatformType = baseOfPtSkuRelation.ProductPlatformType,
                    ShopId = model.ShopId,
                    SkuCode = model.SkuCode
                };
                var warehouseSkuBindResponse = _wareHouseService.BindProduct(req, true, true);

                if (warehouseSkuBindResponse.IsSucc)
                {
                    baseOfPtSkuRelation.IsUseWarehouse = true;

                    var msgList = new List<MessageRecord>
                    {
                        new MessageRecord
                        {
                            MsgType = BaseProductMsgType.SyncPtRelationStatus,
                            FxUserId = curUserId,
                            TargetCloud = "Alibaba",
                            BusinessId = baseOfPtSkuRelation.BaseProductSkuUid.ToString(),
                            DataJson = baseOfPtSkuRelation.ToJson()
                        }
                    };

                    var updateLIst = new List<BaseOfPtSkuRelation> { baseOfPtSkuRelation };
                    new MessageRecordService().SendBusinessMessage(msgList);
                    new BaseOfPtSkuRelationService(false).BulkUpdate(updateLIst);
                    // 冷库更新绑定关系
                    // 看是否全局开启了冷热分离
                    var enableColdGlobal = DuplicationColdStorageSwitchRepository.Instance.IsEnabledColdStorage();
                    var userDbConfig= SiteContext.Current.CurrentDbConfig;
                    if (enableColdGlobal&&userDbConfig!=null&&userDbConfig.EnableColdDb) {
                        var codeService = new BaseOfPtSkuRelationService(userDbConfig.ColdDbConnectionString, false);
                        codeService.BulkUpdate(updateLIst);
                    }
                }

                return !warehouseSkuBindResponse.IsSucc ? FalidResult(warehouseSkuBindResponse.Message) : SuccessResult("切换成功！");
            }
            // 关闭库存扣减
            else
            {
                // 获取库存系统数据的业务库绑定关系
                var wareHouseSkuBindRelation = new WareHouseSkuBindRelationRepository(connectionString).GetBindRelation(ownCode,
                    baseOfPtSkuRelation.ProductSkuCode, baseOfPtSkuRelation.ProductPlatformType);
                if (wareHouseSkuBindRelation == null) return FalidResult("库存系统未找到关联信息！");
                var req = new WarehouseSkuUnbindRequest
                {
                    IsRollback = false,
                    OwnerCode = ownCode,
                    SkuBindRelationCode = wareHouseSkuBindRelation.SkuBindRelationCode
                };

                var warehouseSkuUnbindResponse = _wareHouseService.UnBindProduct(req, true);

                if (warehouseSkuUnbindResponse.IsSucc)
                {
                    baseOfPtSkuRelation.IsUseWarehouse = false;

                    var msgList = new List<MessageRecord>
                    {
                        new MessageRecord
                        {
                            MsgType = BaseProductMsgType.SyncPtRelationStatus,
                            FxUserId = curUserId,
                            TargetCloud = "Alibaba",
                            BusinessId = baseOfPtSkuRelation.BaseProductSkuUid.ToString(),
                            DataJson = baseOfPtSkuRelation.ToJson()
                        }
                    };

                    var updateLIst = new List<BaseOfPtSkuRelation> { baseOfPtSkuRelation };
                    new MessageRecordService().SendBusinessMessage(msgList);
                    new BaseOfPtSkuRelationService(false).BulkUpdate(updateLIst);
                }

                return !warehouseSkuUnbindResponse.IsSucc ? FalidResult(warehouseSkuUnbindResponse.Message) : SuccessResult("切换成功！");
            }
        }

        /// <summary>
        /// 随机编码生成（sku/spu）
        /// </summary>
        /// <returns></returns>
        public ActionResult CodeGenerate()
        {
            var code = Guid.NewGuid().ToString().ToShortMd5();
            return SuccessResult(new { Code = code, Msg = "编码生成成功！" });
        }

        #region 基础商品绑定厂家

        /// <summary>
        /// 触发 基础商品绑定厂家
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [FxMigrateLockFilter()]
        [IgnoreDoubleAuth]
        [LogForOperatorFilter("基础商品绑定厂家")]
        public ActionResult TriggerBaseProductBindSupplier(BaseProductBindSupplierRequestModel model)
        {
            int fxUserId = SiteContext.Current.CurrentFxUserId;
            var dbName = DES.DecryptDES(Request["dbname"], CustomerConfig.LoginCookieEncryptKey);
            model.IP = Request.UserHostAddress;
            var configService = new BaseProductSkuSupplierConfigService();
            //var skuUids = model.SkuUids;
            //model.BindProductType = "oneself";
            if (model.isSelf)
            {
                model.BaseProductConfigModels.ForEach(m => m.SupplierId = fxUserId);
            }
            #region 前置校验
            try
            {
                // 基础商品前置校验 不通过则抛出异常
                configService.PreCheckBaseProductBindSupplier(model, fxUserId);
            }
            catch (LogicException lex)
            {
                return FalidResult(lex.Message);
            }
            catch (Exception ex)
            {
                Log.WriteError($"基础商品绑定厂家前置校验发生异常：{ex}");
                return FalidResult("服务器繁忙，请稍会儿再试");
            }
            //return SuccessResult("");
            #endregion

            #region 是否已创建任务
            var asyncTaskService = new AsyncTaskService();
            var query = new AsyncTaskQueryModel
            {
                ShopId = SiteContext.Current.CurrentShopId,
                FxUserId = fxUserId,
                StatusList = new List<int>() { 0, 1 },
                Flag = "BaseProductBindSupplier",
                IsSetExpiress = true,
                ExpiressTime = 30
            };

            var task = asyncTaskService.GetAsyncTask(query);
            if (task != null)
                return FalidResult("当前有基础商品更换厂家任务正在进行，请等待任务结束后操作！");
            #endregion

            #region 执行任务 绑定厂家

            try
            {
                task = new AsyncTask();
                // 过滤未勾选的sku和product
                new BaseProductSkuService().FiltedBaseProductSku(model.SkuUids, fxUserId, model.NoCheckedSkuUids,
                    model.NoCheckedProductUids);
                configService.BaseProductBindSupplier(model, fxUserId, task, dbName);
            }
            catch (LogicException lex)
            {
                Log.WriteError($"基础商品绑定厂家发生异常：{lex}");
                task.Status = -1; //失败
                task.ExceptionDesc = $"异常消息：{lex.Message}=》异常堆栈：{lex.StackTrace}";
                task.UpdateTime = DateTime.Now;

                asyncTaskService.UpdateAsyncTask(task);
                ExceptionLogDataEventTrackingService.Instance.WriteLog("TriggerBindSupplier", lex, task.ToJson(true));
                return FalidResult($"基础商品绑定厂家发生异常：{lex.Message}");
            }
            catch (Exception ex)
            {
                Log.WriteError($"基础商品绑定厂家发生异常：{ex}");
                task.Status = -1; //失败
                task.ExceptionDesc = $"异常消息：{ex.Message}=》异常堆栈：{ex.StackTrace}";
                task.UpdateTime = DateTime.Now;

                asyncTaskService.UpdateAsyncTask(task);
                ExceptionLogDataEventTrackingService.Instance.WriteLog("TriggerBindSupplier", ex, task.ToJson(true));
                return FalidResult($"基础商品绑定厂家发生异常：{ex.Message}");
            }

            #endregion

            return SuccessResult("绑定成功");
        }

        /// <summary>
        /// 触发 基础商品绑定厂家
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [FxMigrateLockFilter()]
        [IgnoreDoubleAuth]
        [LogForOperatorFilter("基础商品绑定厂家")]
        public ActionResult TriggerBaseSkuBindSupplier(BaseProductBindSupplierRequestModel model)
        {
            int fxUserId = SiteContext.Current.CurrentFxUserId;
            var dbName = DES.DecryptDES(Request["dbname"], CustomerConfig.LoginCookieEncryptKey);
            model.IP = Request.UserHostAddress;
            var configService = new BaseProductSkuSupplierConfigService();
            //var skuUids = model.SkuUids;
            //model.BindProductType = "oneself";
            if (model.isSelf)
            {
                model.BaseProductConfigModels.ForEach(m => m.SupplierId = fxUserId);
            }
            #region 前置校验
            try
            {
                // 基础商品前置校验 不通过则抛出异常
                configService.PreCheckBaseProductBindSupplier(model, fxUserId);
            }
            catch (LogicException lex)
            {
                return FalidResult(lex.Message);
            }
            catch (Exception ex)
            {
                Log.WriteError($"基础商品绑定厂家前置校验发生异常：{ex}");
                return FalidResult("服务器繁忙，请稍会儿再试");
            }
            //return SuccessResult("");
            #endregion

            #region 是否已创建任务
            var asyncTaskService = new AsyncTaskService();
            var query = new AsyncTaskQueryModel
            {
                ShopId = SiteContext.Current.CurrentShopId,
                FxUserId = fxUserId,
                StatusList = new List<int>() { 0, 1 },
                Flag = "BaseProductBindSupplier",
                IsSetExpiress = true,
                ExpiressTime = 30
            };

            var task = asyncTaskService.GetAsyncTask(query);
            if (task != null)
                return FalidResult("当前有基础商品更换厂家任务正在进行，请等待任务结束后操作！");
            #endregion

            #region 执行任务 绑定厂家

            try
            {
                task = new AsyncTask();
                // 过滤未勾选的sku和product
                //new BaseProductSkuService().FiltedBaseProductSku(model.SkuUids, fxUserId, model.NoCheckedSkuUids, model.NoCheckedProductUids);
                configService.BaseProductBindSupplier(model, fxUserId, task, dbName);
            }
            catch (LogicException lex)
            {
                Log.WriteError($"基础商品绑定厂家发生异常：{lex}");
                task.Status = -1; //失败
                task.ExceptionDesc = $"异常消息：{lex.Message}=》异常堆栈：{lex.StackTrace}";
                task.UpdateTime = DateTime.Now;

                asyncTaskService.UpdateAsyncTask(task);
                ExceptionLogDataEventTrackingService.Instance.WriteLog("TriggerBindSupplier", lex, task.ToJson(true));
                return FalidResult($"基础商品绑定厂家发生异常：{lex.Message}");
            }
            catch (Exception ex)
            {
                Log.WriteError($"基础商品绑定厂家发生异常：{ex}");
                task.Status = -1; //失败
                task.ExceptionDesc = $"异常消息：{ex.Message}=》异常堆栈：{ex.StackTrace}";
                task.UpdateTime = DateTime.Now;

                asyncTaskService.UpdateAsyncTask(task);
                ExceptionLogDataEventTrackingService.Instance.WriteLog("TriggerBindSupplier", ex, task.ToJson(true));
                return FalidResult($"基础商品绑定厂家发生异常：{ex.Message}");
            }

            #endregion

            return SuccessResult("绑定成功");
        }

        #endregion

        #region 基础商品编辑

        [LogForOperatorFilter("基础商品编辑")]
        [FxMigrateLockFilter()]
        public ActionResult ModifyBaseProductSku(BaseProductSkuModifyModel model)
        {
            var dbName = DES.DecryptDES(Request["dbname"], CustomerConfig.LoginCookieEncryptKey);
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var baseProductService = new BaseProductSkuService();

            #region 数据校验
            if (string.IsNullOrEmpty(model.Subject))
            {
                return FalidResult("请输入基础商品标题！");
            }
            if (model.Subject.GetStringRealLen() > 120)
            {
                return FalidResult("基础商品标题长度限制60字符以内！");
            }
            if (model.Subject.GetStringRealLen() < 1)
            {
                return FalidResult("基础商品标题长度限制最少1字符！");
            }
            if (!string.IsNullOrEmpty(model.ShortTitle) && model.ShortTitle.GetStringRealLen() > 512)
            {
                return FalidResult("基础商品简称长度限制512字符以内！");
            }
            if (string.IsNullOrEmpty(model.SpuCode))
            {
                return FalidResult("请设置基础商品编码！");
            }
            else if (model.IsNewSpuCode == true)
            {
                var codesr = new BaseProductEntityService().GetExistIdAndCodes(new List<string>() { model.SpuCode }, fxUserId);
                if (codesr.Count > 0)
                    return FalidResult($"基础商品SPU编码: {string.Join(",", model.SpuCode)}已存在！");
            }
            /*if (model.ProductImagesStr.IsNullOrEmptyList())
            {
                return FalidResult("基础商品主图至少上传一张！");
            }*/
            //if (model.ImageId.IsNullOrEmptyList())
            //{
            //    return FalidResult("请传入基础商品主图Id！");
            //}
            if (model.ProductSkus == null || model.ProductSkus.Count < 1)
            {
                return FalidResult("请至少创建一个基础商品SKU！");
            }
            if (model.ProductSkus.Any(a => string.IsNullOrEmpty(a.SkuCode) && a.IsNewSkuCode != true))
            {
                return FalidResult("请设置基础商品SKU编码！");
            }
            if (model.ProductSkus.Count > 0)
            {
                var codes = model.ProductSkus.Select(a => a.SkuCode).Where(c => c.IsNotNullOrEmpty()).ToList();
                if (codes.Distinct().Count() < codes.Count())
                    return FalidResult($"基础商品SKU编码重复，请检查页面数据！");

                codes = model.ProductSkus.Where(s => s.IsNewSkuCode != null).Select(a => a.SkuCode).Where(c => c.IsNotNullOrEmpty()).Distinct().ToList();
                var codesr = baseProductService.GetBaseProductSkuByCode(codes, true, fxUserId);
                if (codesr.Count > 0)
                    return FalidResult($"基础商品SKU编码: {string.Join(",", codesr)}已存在！");

            }
            #endregion
            try
            {
                var oldProductInfo = new BaseProductSkuService().GetBaseProductDetail(new BaseProductDetailReqModel() { BaseProductUid = model.BaseProductUid, Type = false }, fxUserId);
                //移除，不需要 2024.10.17
                //ForceTranSelfSku(oldProductInfo,model,fxUserId);
                new BaseProductEntityService().ModifyBaseProduct(model, fxUserId, dbName);

                // 检测编辑变动
                new PtProductInfoService().UpdatePtProductInfoFromBaseProduct(oldProductInfo);// 平台资料更新
                Task.Run(() =>
                {
                    try
                    {
                       
                        new BaseProductEntityService().CheckSProductInfo(oldProductInfo); // 货盘资料更新
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"基础商品Uid：{oldProductInfo.Uid} 变更同步: 货盘资料异常，异常信息：{ex.ToJson()}");
                    }
                });
            }
            catch (LogicException lex)
            {
                return FalidResult(lex.Message);
            }
            catch (Exception ex)
            {
                Log.WriteError($"基础商品保存编辑发生异常：{ex.StackTrace}");
                return FalidResult("服务器繁忙，请稍会儿再试");
            }

            return SuccessResult("设置成功");
        }
        /// <summary>
        /// 产品说SKU有隐性删除时，导致SKUCODE有丢失，平台规格录入强制转自定义录入模式,丢失的SKU补上
        /// 2024-10-17：不需要了
        /// </summary>
        /// <param name="oldProductInfo"></param>
        /// <param name="model"></param>
        /// <param name="fxUserId"></param>
        private void ForceTranSelfSku(BaseProductEntity oldProductInfo, BaseProductSkuModifyModel model, int fxUserId)
        {
            #region 商品规格编码有丢失的情况，强制走自定义录入模式，这段建议前端弄会比较好
            var skuCodes = oldProductInfo.Skus.Select(x => x.SkuCode).OrderBy(x => x).ToList();
            if (oldProductInfo.SkuModeType == 0 && model.SkuModeType != 1 && !model.ProductSkus.Select(x => x.SkuCode).OrderBy(x => x).Intersect(skuCodes).Any())
            {
                var res = new BaseProductSkuService().GetBaseProductDetail(new BaseProductDetailReqModel { BaseProductUid = model.BaseProductUid }, fxUserId);

                model.SkuModeType = 1;
                List<BaseProductSkuModel> addSkuModels = new List<BaseProductSkuModel>();
                res.Skus?.ForEach(x =>
                {
                    BaseProductSkuModel skuTemp = new BaseProductSkuModel()
                    {
                        SkuCode = x.SkuCode,
                        ImageUrlStr = x.ImageUrlKey,
                        ShortTitle = x.ShortTitle,
                        Subject = x.Subject,
                        CostPrice = x.CostPrice,
                        SettlePrice = x.SettlePrice,
                        DistributePrice = x.DistributePrice,
                        Weight = x.Weight,
                        UpFxUserId = x.UpFxUserId,
                        UpSkuUid = x.UpSkuUid,
                        AttributeValue = x.AttributeValue,
                        IsCombineSku = x.IsCombineSku,
                        SkuUid = x.Uid,
                        OldSkuCode = x.SkuCode,
                        ImageId = x.ImageObjectId.ToString2(),
                        RootNodeFxUserId = x.RootNodeFxUserId,
                        SharePathCode = x.SharePathCode,
                        PathNodeDeep = x.PathNodeDeep,
                        SourceSkuCode = x.SourceSkuCode
                    };
                    skuTemp.Attribute = new BaseProductSkuAttributeModel()
                    {
                        ImgAttributeValueNo = 1,
                        AttributeName1 = "无规格",
                        AttributeName2 = string.Empty,
                        AttributeName3 = string.Empty,
                        AttributeValue1 = $"{x.AttributeModel?.AttributeValue1};{x.AttributeModel?.AttributeValue2};{x.AttributeModel?.AttributeValue3};".Trim(';').Trim(),
                        AttributeValue2 = string.Empty,
                        AttributeValue3 = string.Empty,
                        ValueUrl = x.AttributeModel?.ValueUrl,
                        ValueUrlKey = x.AttributeModel?.ValueUrlKey,

                        // 后补的sku，sku值的Code赋值之前的
                        AttributeCode1 = x.AttributeModel.AttributeCode1,
                        AttributeCode2 = x.AttributeModel.AttributeCode2,
                        AttributeCode3 = x.AttributeModel.AttributeCode3,
                    };
                    addSkuModels.Add(skuTemp);
                });
                //model.ProductSkus.AddRange(addSkuModels);
                model.ProductSkus?.ForEach(x =>
                {
                    if (x.Attribute != null)
                    {
                        x.Attribute.AttributeName1 = "无规格";
                        x.Attribute.AttributeName2 = string.Empty;
                        x.Attribute.AttributeName3 = string.Empty;
                        x.Attribute.AttributeValue1 = $"{x.Attribute?.AttributeValue1};{x.Attribute?.AttributeValue2};{x.Attribute?.AttributeValue3};".Trim(';').Trim();
                        x.Attribute.AttributeValue2 = string.Empty;
                        x.Attribute.AttributeValue3 = string.Empty;
                        x.Attribute.ValueUrl = x.Attribute?.ValueUrl;
                        x.Attribute.ValueUrlKey = x.Attribute?.ValueUrlKey;
                    }
                });
                model.ProductSkus.AddRange(addSkuModels);
            }

            #endregion
        }

        [LogForOperatorFilter("基础商品编辑")]
        [FxMigrateLockFilter()]
        public ActionResult SaveEditBaseProductSku(BaseProductSkuModifyModel model)
        {
            var dbName = DES.DecryptDES(Request["dbname"], CustomerConfig.LoginCookieEncryptKey);
            var fxUserId = SiteContext.Current.CurrentFxUserId;

            try
            {
                new BaseProductSkuService().SaveEdit(model, fxUserId, dbName);

                model.BaseProductUid = model.BaseProductUidStr.ToLong();
                if (model.BaseProductUid > 0)
                {
                    var oldProductInfo = new BaseProductSkuService().GetBaseProductDetail(
                    new BaseProductDetailReqModel()
                    {
                        BaseProductUid = model.BaseProductUid,
                        Type = false
                    }, fxUserId);

                    // 检测编辑变动
                    Task.Run(() => new BaseProductEntityService().CheckPtProductInfo(new List<BaseProductEntity> { oldProductInfo }));
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"基础商品保存编辑发生异常：{ex.StackTrace}");
                return FalidResult($"服务器繁忙，请稍会儿再试");
            }
            return SuccessResult("保存成功");
        }

        [LogForOperatorFilter("基础商品编辑")]
        [FxMigrateLockFilter()]
        public ActionResult SaveEditBaseProduct(BaseProductSkuModifyModel model)
        {
            var dbName = DES.DecryptDES(Request["dbname"], CustomerConfig.LoginCookieEncryptKey);
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            if (model.BaseProductUid <= 0)
            {
                return FalidResult("请传入基础商品Uid");
            }
            try
            {
                new BaseProductSkuService().SaveEditBaseProduct(model, fxUserId, dbName);
                // TODO 库存相关处理
            }
            catch (Exception ex)
            {
                Log.WriteError($"基础商品保存编辑发生异常：{ex.StackTrace}");
                return FalidResult($"基础商品保存编辑发生异常：{ex.Message}");
            }
            return SuccessResult("保存成功");
        }

        [LogForOperatorFilter("基础商品编辑")]
        [FxMigrateLockFilter()]
        public ActionResult SaveEdit(BaseProductSkuModifyModel model)
        {
            int fxUserId = SiteContext.Current.CurrentFxUserId;
            var dbName = DES.DecryptDES(Request["dbname"], CustomerConfig.LoginCookieEncryptKey);
            var baseProductSkuService = new BaseProductSkuService();
            if (model.SkuUids.IsNullOrEmptyList())
            {
                return FalidResult("请选择需要修改的基础商品");
            }
            try
            {
                baseProductSkuService.FiltedBaseProductSku(model.SkuUids, fxUserId, model.NoCheckedSkuUids, model.NoCheckedProductUids);
                #region // 获取变更商品UID
                var detailReqs = new List<BaseProductDetailReqModel>();
                foreach (var uid in model.SkuUids.Select(p => p.Key))
                {
                    var req = new BaseProductDetailReqModel
                    {
                        BaseProductUid = uid,
                        Type = false
                    };
                    detailReqs.Add(req);
                }
                var oldProductInfos = new BaseProductSkuService()
                    .GetAllBaseProductDetail(detailReqs, fxUserId);
                #endregion
                baseProductSkuService.SaveEdit(model, fxUserId, dbName);
                // 检测编辑变动
                Task.Run(() => new BaseProductEntityService().CheckPtProductInfo(oldProductInfos));

            }
            catch (Exception ex)
            {
                Log.WriteError($"基础商品保存编辑发生异常：{ex.StackTrace}");
                return FalidResult($"基础商品保存编辑发生异常：{ex.Message}");
            }
            return SuccessResult("保存成功");
        }

        #endregion


        /// <summary>
        /// 检查库存系统sku是否存在
        /// </summary>
        /// <param name="skuCode"></param>
        /// <returns></returns>
        public ActionResult CheckWarehouseSkuExist(string skuCode)
        {
            var req = new WarehouseSkuGetOneRequest
            {
                SkuCode = skuCode,
                WareHouseSkuCode = "-"
            };

            var res = _service.WarehouseSkuGetOne(req);

            return res.Code != "400" ? SuccessResult(true) : SuccessResult(false);
        }

        /// <summary>
        /// 基础商品创建
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [LogForOperatorFilter("基础商品创建")]
        public ActionResult CreateBaseProductSku(BaseProductSkuAddModel model)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var baseProductService = new BaseProductSkuService();
            // 创建默认仓
            //_service.GenerateDefaultStore();后置处理
            #region 数据校验
            if (string.IsNullOrEmpty(model.Subject))
            {
                return FalidResult("基础商品标题不能为空！");
            }
            if (model.Subject.GetStringRealLen() > 120)
            {
                return FalidResult("基础商品标题长度限制60字以内！");
            }
            if (model.Subject.GetStringRealLen() < 1)
            {
                return FalidResult("基础商品标题长度限制最少1字！");
            }
            if (string.IsNullOrEmpty(model.ShortTitle))
            {
                //return FalidResult("基础商品简称不能为空！");
            }
            if (!string.IsNullOrEmpty(model.ShortTitle) && model.ShortTitle.GetStringRealLen() > 20)
            {
                return FalidResult("基础商品简称长度限制20字以内！");
            }
            if (string.IsNullOrEmpty(model.SpuCode))
            {
                return FalidResult("基础商品编码不能为空！");
            }
            if (!string.IsNullOrEmpty(model.SpuCode))
            {
                var codes = baseProductService.GetBaseProductSkuByCode(new List<string> { model.SpuCode }, false, fxUserId);
                if (codes.Count > 0)
                    return FalidResult($"基础商品编码: {string.Join(",", codes)}已存在！");
            }
            //if (model.ProductImages == null || model.ProductImages.Count < 1)
            //{
            //    return FalidResult("基础商品主图至少上传一张！");
            //}
            //if (model.ProductImagesStr.IsNullOrEmpty())
            //{
            //    return FalidResult("基础商品主图至少上传一张！");
            //}
            if (model.ProductSkus == null || model.ProductSkus.Count < 1)
            {
                return FalidResult("基础商品SKU不能为空！");
            }
            if (model.ProductSkus.Any(a => string.IsNullOrEmpty(a.SkuCode)))
            {
                return FalidResult("基础商品Sku编码不能为空！");
            }
            if (model.ProductSkus.Any(a => string.IsNullOrEmpty(a.ShortTitle)))
            {
                // return FalidResult("基础商品SKU简称不能为空！");
            }
            if (model.ProductSkus.Count > 0)
            {
                var codes = model.ProductSkus.Select(a => a.SkuCode).Distinct().ToList();
                if (codes.Count != model.ProductSkus.Select(a => a.SkuCode).Count())
                {
                    return FalidResult($"基础商品SKU编码不能重复！");
                }
                var codesr = baseProductService.GetBaseProductSkuByCode(codes, true, fxUserId);
                if (codesr.Count > 0)
                    return FalidResult($"基础商品SKU编码: {string.Join(",", codesr)}已存在！");
            }
            if (baseProductService.CheckSkuAttributeLength(model.ProductSkus) == false)
            {
                return FalidResult("基础商品SKU规格名称与规格值长度限制64个字以内！");
            }
                
            #endregion

            #region 商品创建
            try
            {
                var res = baseProductService.CreateBaseProductSku(model, fxUserId);
                return SuccessResult("创建成功！");
            }
            catch (Exception ex)
            {
                Log.WriteError($"基础商品创建异常：{ex.Message}");
                return FalidResult($"创建失败:{ex.Message}！");
            }
            #endregion
        }

        /// <summary>
        /// 解绑基础商品
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [LogForOperatorFilter("基础商品解绑")]
        public ActionResult BaseProductSkuRelationUnbind(BaseProductSkuUnbindModel model)
        {
            var dbName = Request["dbname"];
            if (dbName.IsNotNullOrEmpty())
            {
                try
                {
                    dbName = DES.DecryptDES(dbName, CustomerConfig.LoginCookieEncryptKey);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"平台dbName解析失败：{ex.Message}");
                    return FalidResult("平台dbName解析失败！");
                }
            }
            else
            {
                dbName = new ProductFxRepository().DbConnection.Database;
            }
            model.ProductDbName = dbName;
            var baseProductSkuService = new BaseProductSkuCommonService();
            var result = baseProductSkuService.BaseProductSkuRelationUnbind(model);
            if (result.Success)
                return SuccessResult("解绑成功！");
            else
                return FalidResult(result.Message);

        }

        public ActionResult BusinessCardCreateProduct()
        {
            var supplierService = new SupplierUserService();
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var currShopId = SiteContext.Current.CurrentShopId;

            //厂家数据源
            var suppliers = _supplierUserService.GetSupplierList(fxUserId, onlyGetCurDb: true,needEncryptAccount:true);
            ViewBag.Suppliers =
                suppliers?.Select(x => new { UserName = x.SupplierMobileAndRemark, FxUserId = x.SupplierFxUserId, x.Status, x.IsTop, x.IsFilter }).Distinct().ToJson();
            ViewBag.SupplierUsers =
                suppliers?.Select(x => new { UserName = x.SupplierMobileAndRemark, FxUserId = x.SupplierFxUserId }).GroupBy(x => x.FxUserId).ToDictionary(x => x.Key, x => x.FirstOrDefault()?.UserName ?? "");
            // 仓库数据
            var req = new WarehouseListRequest
            {
                PageIndex = 1,
                PageSize = 20,
                OwnerCode = _service.GetOwnerCode(SiteContext.Current.CurrentFxUserId),
            };
            var res = _service.LoadStoreManagementList(req);
            if (res.Warehouses != null && res.Warehouses.Count > 0)
            {
                ViewBag.WareHouseStatus = 1;
            }
            else
            {
                ViewBag.WareHouseStatus = 0;
            }
            return View();
        }

        public ActionResult CreateBasePlatformProduct()
        {
            var supplierService = new SupplierUserService();
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var currShopId = SiteContext.Current.CurrentShopId;

            //厂家数据源
            var suppliers = _supplierUserService.GetSupplierList(fxUserId, onlyGetCurDb: true,needEncryptAccount:true);
            ViewBag.Suppliers =
                suppliers?.Select(x => new { UserName = x.SupplierMobileAndRemark, FxUserId = x.SupplierFxUserId, x.Status, x.IsTop, x.IsFilter }).Distinct().ToJson();
            ViewBag.SupplierUsers =
                suppliers?.Select(x => new { UserName = x.SupplierMobileAndRemark, FxUserId = x.SupplierFxUserId }).GroupBy(x => x.FxUserId).ToDictionary(x => x.Key, x => x.FirstOrDefault()?.UserName ?? "");
            // 仓库数据
            var req = new WarehouseListRequest
            {
                PageIndex = 1,
                PageSize = 20,
                OwnerCode = _service.GetOwnerCode(SiteContext.Current.CurrentFxUserId),
            };
            var res = _service.LoadStoreManagementList(req);
            if (res.Warehouses != null && res.Warehouses.Count > 0)
            {
                ViewBag.WareHouseStatus = 1;
            }
            else
            {
                ViewBag.WareHouseStatus = 0;
            }
            return View();
        }

        public ActionResult PrepareDistribution()
        {
            var token = Request.QueryString["token"];
            var supplierService = new SupplierUserService();
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var currShopId = SiteContext.Current.CurrentShopId;
            ViewBag.SupportPlatform = CustomerConfig.GetFxUserPlatform(token).ToJson();
            ViewBag.SupportPlatformForListing = CustomerConfig.GetFxUserPlatformForListing(token).ToJson();


            //厂家数据源
            var suppliers = _supplierUserService.GetSupplierList(fxUserId, onlyGetCurDb: true,needEncryptAccount:true);
            ViewBag.Suppliers =
                suppliers?.Select(x => new { UserName = x.SupplierMobileAndRemark, FxUserId = x.SupplierFxUserId, x.Status, x.IsTop, x.IsFilter }).Distinct().ToJson();
            ViewBag.SupplierUsers =
                suppliers?.Select(x => new { UserName = x.SupplierMobileAndRemark, FxUserId = x.SupplierFxUserId }).GroupBy(x => x.FxUserId).ToDictionary(x => x.Key, x => x.FirstOrDefault()?.UserName ?? "");
            // 仓库数据
            var req = new WarehouseListRequest
            {
                PageIndex = 1,
                PageSize = 20,
                OwnerCode = _service.GetOwnerCode(SiteContext.Current.CurrentFxUserId),
            };
            var res = _service.LoadStoreManagementList(req);
            if (res.Warehouses != null && res.Warehouses.Count > 0)
            {
                ViewBag.WareHouseStatus = 1;
            }
            else
            {
                ViewBag.WareHouseStatus = 0;
            }
            return View();
        }

        #region 异常提示相关

        /// <summary>
        /// 是否有待处理异常数据
        /// </summary>
        /// <returns></returns>
        [FxMigrateLockFilter()]
        public ActionResult CheckIsHasAbnormal()
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            _baseProductAbnormalService = new BaseProductAbnormalService(fxUserId);
            var result = _baseProductAbnormalService.IsHasWaitProcessAbnormal(fxUserId);
            if (result)
                return SuccessResult("1");
            else
                return SuccessResult("0");
        }

        /// <summary>
        /// 基础商品异常列表查询
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>

        [LogForOperatorFilter("基础商品异常列表")]
        public ActionResult LoadAbnormalList(BaseProductAbnormalQuery query)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            query.FxUserId = fxUserId;
            _baseProductAbnormalService = new BaseProductAbnormalService(fxUserId);
            var tuple = _baseProductAbnormalService.GetPageList(query);
            var list = tuple.Item2;
            var listCount = tuple.Item1;

            var res = new PagedResultModel<BaseProductAbnormalResult>()
            {
                PageIndex = query.PageIndex,
                PageSize = query.PageSize,
                Rows = list,
                Total = listCount
            };
            var result = Json(res);
            return result;
        }

        /// <summary>
        /// 暂不处理（即状态设为忽略）
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [FxMigrateLockFilter()]
        public ActionResult AbnormalNotProcessed(List<long> ids)
        {
            if (ids.IsNullOrEmptyList())
                return SuccessResult("成功");
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            _baseProductAbnormalService = new BaseProductAbnormalService(fxUserId);
            var model = new BatchOptModel
            {
                FxUserId = fxUserId,
                Ids =ids
            };
            var result = _baseProductAbnormalService.NotProcessed(model);
            if (result > 0)
                return SuccessResult("成功");
            else
                return FalidResult("失败");
        }

        ///// <summary>
        ///// 跨云基础商品解绑
        ///// </summary>
        ///// <returns></returns>
        //[LogForOperatorFilter("跨云基础商品批量解绑")]
        //public ActionResult BatchSkuRelationUnbind(List<BaseProductSkuUnbindModel> models)
        //{
        //    //var models = RequestModel.GetParamModel<List<BaseProductSkuUnbindModel>>();
        //    UnbindRelationResultModel tempResult = null;
        //    try
        //    {
        //        var logFileName = "AbnormalUnBindCloud.txt";
        //        Log.Debug(() => $"跨云，model={models.ToJson()}", logFileName);

        //        //切到指定业务库
        //        models.GroupBy(m => m.ProductDbName).ForEach(dbGroup =>
        //        {
        //            // ReSharper disable once ObjectCreationAsStatement
        //            new SiteContext(SiteContext.Current.CurrentFxUser, dbGroup.Key.ToString2());

        //            var dbModels = dbGroup.ToList();
        //            tempResult = new BaseProductSkuCommonService().BaseProductSkuRelationUnbind(dbModels, true);
        //        });
        //    }
        //    catch (Exception ex)
        //    {
        //        Log.WriteError($"云平台【{CustomerConfig.CloudPlatformType}】跨云基础商品解绑发生异常：{ex}", ModuleFileName.BaseProduct);
        //    }
        //    return SuccessResult(tempResult);
        //}

        //public ActionResult TestAction1(BaseProductSkuUnbindModel model)
        //{
        //    var fxUserId = SiteContext.GetCurrentFxUserId();
        //    var baseProductAbnormalService = new BaseProductAbnormalService(fxUserId);
        //    var lastModels = baseProductAbnormalService.BuildBaseProductSkuUnbindModel(fxUserId, model);
        //    return BatchSkuRelationUnbind(lastModels);
        //}

        /// <summary>
        /// 基础商品解绑（解除关联）
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [FxMigrateLockFilter()]
        public ActionResult AbnormalUnBind(BaseProductSkuUnbindModel model)
        {
            var actionResult = new UnbindRelationResultModel() { IsAbnormalUnbind = true,AllCount = model.AbnormalIdList.Count };
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var shopId = SiteContext.GetCurrentShopId();

            #region 检测异步任务
            var commonSettingService = new CommonSettingService();
            var taskLock = commonSettingService.GetString(ABNORMAL_UNBIND_ASYNC_TASK_LOCK, shopId).ToBool();
            if (taskLock)
            {
                return SuccessResult("当前有异常解绑任务进行中","233");
            }
            #endregion

            var enableAsyncCount = ABNORMAL_UNBIND_ENABLE_ASYNC_COUNT;
            if (CustomerConfig.IsDebug)
                enableAsyncCount = 2;

            // 不启用异步
            if (model.AbnormalIdList.Count < enableAsyncCount)
            {
                try
                {
                    if (!DoAbnormalUnBind())
                    {
                        return FalidResult("解绑失败", actionResult);
                    }
                    //actionResult.SuccessCount = model.AbnormalIdList.Count - actionResult.FailedCount;

                    actionResult.IsSuccess = actionResult.SuccessCount > 0;
                }
                catch (Exception ex)
                {
                    Log.WriteError($"{fxUserId}用户同步模式异常解绑错误：{ex}",ModuleFileName.BaseProduct);
                }

                return actionResult.IsSuccess ? SuccessResult("解绑成功", actionResult) : FalidResult("解绑失败", actionResult);
            }
            // 启用异步任务
            else
            {
                commonSettingService.Set(ABNORMAL_UNBIND_ASYNC_TASK_LOCK, "1", shopId);
                Task.Run(() =>
                {
                    try
                    {
                        if (CustomerConfig.IsDebug)
                            Thread.Sleep(1000 * 10);
                        DoAbnormalUnBind();

                        //actionResult.SuccessCount = model.AbnormalIdList.Count - actionResult.FailedCount;

                        actionResult.IsSuccess = actionResult.SuccessCount > 0;
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"{fxUserId}用户异步模式异常解绑错误：{ex}", ModuleFileName.BaseProduct);
                    }
                    finally
                    {
                        commonSettingService.Set(ABNORMAL_UNBIND_ASYNC_TASK_LOCK, "0", shopId);
                        if (RedisPool.IsInit)
                        {
                            RedisCacheUtls.Set($"{ABNORMAL_UNBIND_ASYNC_TASK_RESULT_KEY}:{SiteContext.Current.CurrentFxUserId}", actionResult.ToJson(), 60 * 60 * 2);
                        }
                    }
                    
                });
                return SuccessResult("创建异常解绑任务成功", "201");
            }

            bool DoAbnormalUnBind()
            {
                _baseProductAbnormalService = new BaseProductAbnormalService(fxUserId);
                var lastModels = _baseProductAbnormalService.BuildBaseProductSkuUnbindModel(fxUserId, model,ref actionResult);
                if (lastModels == null)
                {
                    //actionResult.FailedAbnormalIdDict.Add("解绑失败", model.AbnormalIdList);
                    return false;
                }
                const string logFileName = "AbnormalUnBind.txt";
                var modelGroups = lastModels.GroupBy(m => m.ProductCloudPlatform);

                var successAbnormalIds = new List<long>();
                foreach (var g in modelGroups)
                {
                    var platform = g.Key;
                    var models = g.ToList();

                    UnbindRelationResultModel tempResult = null;
                    var tempAbnormalIds = models.SelectMany(d => d.AbnormalIdList).ToList();

                    if (platform == CloudPlatformType.Alibaba.ToString())
                    {
                        Log.Debug(() => $"非跨云，lastModels={models.ToJson()}", logFileName);

                        //切到指定业务库
                        models.GroupBy(m => m.ProductDbName).ForEach(dbGroup =>
                        {
                            // ReSharper disable once ObjectCreationAsStatement
                            new SiteContext(SiteContext.Current.CurrentFxUser, dbGroup.Key.ToString2());

                            var dbModels = dbGroup.ToList();
                            tempResult = new BaseProductSkuCommonService().BaseProductSkuRelationUnbind(dbModels, true);
                        });
                    }
                    else
                    {
                        // 跨云基础商品解绑
                        var apiUrl = "/BaseProductApi/BatchSkuRelationUnbind";
                        var targetSiteUrl = CustomerConfig.GetTargetSiteUrl(platform).TrimEnd("/") + apiUrl;
                        Log.Debug(() => $"跨云，targetSiteUrl={targetSiteUrl}，lastModels={models.ToJson()}", logFileName);
                        if (CustomerConfig.IsLocalDbDebug)
                        {
                            targetSiteUrl = CustomerConfig.GetTargetSiteUrl("Alibaba").TrimEnd("/") + apiUrl;
                        }

                        tempResult = Common.PostFxSiteApi<List<BaseProductSkuUnbindModel>, UnbindRelationResultModel>(targetSiteUrl, fxUserId, models, "跨云基础商品批量解绑", isEncrypt: true);
                    }

                    if (tempResult == null)
                        continue;

                    var failedAbnormalIds = tempResult.FailedAbnormalIdDict.SelectMany(d => d.Value).ToList();
                    successAbnormalIds.AddRange(tempAbnormalIds.Except(failedAbnormalIds).ToList());

                    foreach (var failedReason in tempResult.FailedAbnormalIdDict.Keys)
                    {
                        if (actionResult.FailedAbnormalIdDict.TryGetValue(failedReason, out var failedList))
                        {
                            failedList.AddRange(failedAbnormalIds);
                        }
                        else
                        {
                            actionResult.FailedAbnormalIdDict.Add(failedReason, failedAbnormalIds);
                        }
                    }
                }

                if (successAbnormalIds.IsNotNullAndAny())
                {
                    _baseProductAbnormalService.UpdateStatus(successAbnormalIds, 1, fxUserId);
                }

                return true;
            }
        }

        /// <summary>
        /// 前端通过此接口询问基础商品异常解绑异步任务状态
        /// </summary>
        /// <returns></returns>
        public ActionResult AbnormalUnBindStatus()
        {
            var commonSettingService = new CommonSettingService();
            var shopId = SiteContext.GetCurrentShopId();
            var fxUserId = SiteContext.Current.CurrentFxUserId;

            var taskLock = commonSettingService.GetString(ABNORMAL_UNBIND_ASYNC_TASK_LOCK, shopId).ToBool();
            if (taskLock)
            {
                return SuccessResult("当前有异常解绑任务进行中", "233");
            }

            if (RedisPool.IsInit)
            {
                try
                {
                    var value = RedisCacheUtls.Get($"{ABNORMAL_UNBIND_ASYNC_TASK_RESULT_KEY}:{SiteContext.Current.CurrentFxUserId}");
                    if (value.IsNotNullOrEmpty())
                    {
                        var result = value.ToObject<UnbindRelationResultModel>();
                        return !result.IsSuccess ? FalidResult("异常解绑任务失败") : SuccessResult("异常解绑任务完成", result);
                    }

                    return FalidResult("异常解绑任务失败");
                }
                catch (Exception ex)
                {
                    Log.WriteError($"{fxUserId}用户异常解绑任务完成，但反序列化失败：{ex}", ModuleFileName.BaseProduct);
                }
            }
            return SuccessResult("异常解绑任务完成", "200");
        }

        #endregion

        #region 基础商品导入
        /// <summary>
        /// 解析文件
        /// </summary>
        /// <returns></returns>
        [LogForOperatorFilter("商品库-解析文件")]
        public ActionResult ExcelUploadBaseProduct()
        {
            var postedFile = Request.Files["upfile"];
            var postedFileName = postedFile.FileName;
            var shopID = SiteContext.Current.CurrentShopId;
            var log = LogForOperatorContext.Current.logInfo;

            if (Request.Files.Count == 0 || postedFile == null)
            {
                return FalidResult("没有选择文件上传");
            }
            if (postedFile.ContentLength > 10 * 1024 * 1024)
            {
                log.Exception = $"商品库【{shopID}】上次文件{postedFile.ContentLength * 1.0 / 1024 / 1024}M，超过10M";
                return FalidResult("上传文件不能超过10M");
            }

            if (postedFileName.IndexOf(".xlsx") == -1 && postedFileName.IndexOf(".xls") == -1)
            {
                log.Exception = $"只能识别xls和xlsx格式的Excel文件：{postedFileName}";
                return FalidResult("只能识别xls和xlsx格式的Excel文件");
            }

            try
            {
                DataTable dt = new DataTable();
                string errMsg = string.Empty;
                string exMsg = string.Empty;
                log.Detail = new { FileSize = postedFile.ContentLength };

                if (postedFileName.IndexOf(".xlsx") > -1) // 2007版本以上  
                    dt = ExcelHelper.GetExcelDataTable(out errMsg, out exMsg, stream: postedFile.InputStream, fileExt: ".xlsx");
                else if (postedFileName.IndexOf(".xls") > -1) // 2003版本  
                    dt = ExcelHelper.GetExcelDataTable(out errMsg, out exMsg, stream: postedFile.InputStream, fileExt: ".xls");

                if (!errMsg.IsNullOrEmpty())
                {
                    log.Exception = errMsg + $"，异常信息：{exMsg}";
                    Log.WriteError($"店铺【{shopID}】{log.Exception}");
                    return FalidResult(errMsg);
                }

                if (dt == null || dt.Rows.Count == 0)
                {
                    log.Exception = "请勿导入空文件";
                    return FalidResult("请勿导入空文件");
                }

                var model = DataTableToCustomOrder(dt);
                var json = JsonExtension.ToJson(model);
                return SuccessResult(json);
            }
            catch (Exception ex)
            {
                log.Exception = ex.Message;
                Log.WriteError("商品库-批量导入解析Excel文件失败：" + ex.Message);
                return FalidResult(ex.Message);
            }
        }

        /// <summary>
        /// 批量导入
        /// </summary>
        /// <returns></returns>
        [LogForOperatorFilter("商品库-批量导入")]
        public ActionResult BulkImportBaseProduct()
        {
            if (CustomerConfig.CloudPlatformType != PlatformType.Alibaba.ToString())
            {
                return FalidResult("请先切换到精选平台，再保存线下单");
            }

            var now = Convert.ToDateTime(DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            var dbname = Request["dbname"].ToString2();
            var shop = SiteContext.Current.CurrentLoginShop;


            var fileName = Request["FileName"].ToString2();
            var rowsJson = Request["Rows"].ToString2();
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var shopId = SiteContext.Current.CurrentShopId;
            var index = 1;
            if (rowsJson.IsNullOrEmpty())
            {
                return FalidResult("导入的信息不能为空");
            }

            // 类型：0 未设置、1 跳过异常数据
            var required = Convert.ToInt32(Request["Required"]);
            // 类型：0 未设置、1 跳过异常数据、 2 使用第一SPU编码
            var repeated = Convert.ToInt32(Request["Repeated"]);
            // 类型：0 未设置、1 跳过异常数据(不匹配)
            var matcheed = Convert.ToInt32(Request["Matcheed"]);

            // 必填验证信息
            var requiredMsg = new StringBuilder();
            // 重复验证信息
            var repeatrMsg = new StringBuilder();
            // 匹配验证信息
            var matchedMsg = new StringBuilder();

            #region 数据转换
            var list = new List<BaseProductJson>();
            try
            {
                var jarray = rowsJson.ToList<JToken>();
                var rowCount = jarray?.Count() ?? 0;
                if (rowCount == 0)
                    return FalidResult("导入的信息不能为空");
                for (int i = 0; i < rowCount; i++)
                {
                    try
                    {
                        var jmodel = jarray[i].ToJson().ToObject<BaseProductJson>();
                        if (jmodel == null)
                            return FalidResult($"请检查第【{i + 2}】行数据类型是否填写有误，如：数值类型字段");
                        list.Add(jmodel);
                    }
                    catch (Exception)
                    {
                        return FalidResult($"请检查第【{i + 2}】行数据类型是否填写有误");
                    }
                }
            }
            catch (Exception ex)
            {
                return FalidResult($"上传数据解析异常！");
            }
            #endregion

            if (required == 0)
            {
                #region 必填验证
                list.ForEach(model =>
                {
                    var line = new StringBuilder();

                    if (string.IsNullOrEmpty(model.ProSubject))
                        line.Append("，基础商品标题不能为空");
                    if (!string.IsNullOrEmpty(model.ProSubject) && model.ProSubject.GetStringRealLen() > 120)
                        line.Append("，基础商品标题长度限制60字以内！");
                    if (!string.IsNullOrEmpty(model.ProSubject) && model.ProSubject.GetStringRealLen() < 16)
                        line.Append("，基础商品标题长度限制最少8字！");
                    if (string.IsNullOrEmpty(model.ProShortTitle))
                        line.Append("，基础商品简称不能为空");
                    if (!string.IsNullOrEmpty(model.ProShortTitle) && model.ProShortTitle.GetStringRealLen() > 20)
                        line.Append("，基础商品简称长度限制20字以内！");
                    if (string.IsNullOrEmpty(model.SpuCode))
                        line.Append("，基础商品编码不能为空");
                    if (string.IsNullOrEmpty(model.SkuCode))
                        line.Append("，基础商品规格编码不能为空");
                    /*if (model.ProductImages == null)
                        line.Append("，基础商品主图至少上传一张！");*/
                    if (line.IsNotNullOrEmpty())
                        requiredMsg.AppendLine($"第【{index}】行错误：{line.ToString().TrimStart("，")}");
                    index++;
                });
                if (requiredMsg.IsNotNullOrEmpty())
                {
                    return FalidResult("导入文件中部分必填字段缺失，无法导入到商品库", new { type = 1, msg = requiredMsg.ToString() });
                }
                #endregion
            }
            if (repeated == 0)
            {
                #region 重复验证
                // var groups = list.GroupBy(p => new { p.SpuCode,p.SkuCode}).Where(a => a.ToList().Count > 1).ToList();
                var groups = list.GroupBy(p => new { p.SpuCode }).Where(a => a.ToList().Count > 1).ToList();
                if (groups.Count > 0)
                {
                    repeatrMsg.AppendLine("导入文件中SKU编码存在多个SPU编码不一致,需手动确认导入更新规则");
                    return FalidResult("导入文件中SKU编码存在多个SPU编码不一致,需手动确认导入更新规则", new { type = 2, msg = repeatrMsg.ToString() });
                }
                #endregion
            }
            if (matcheed == 0)
            {
                #region 匹配验证
                var spuCodes = list.Where(p => p.SpuCode.IsNotNullOrEmpty()).Select(p => p.SpuCode).Distinct().ToList();
                var skuCodes = list.Where(p => p.SkuCode.IsNotNullOrEmpty()).Select(p => p.SkuCode).Distinct().ToList();
                var baseProductSkuService = new BaseProductSkuService();
                if (spuCodes.Count > 0)
                {
                    var codes = baseProductSkuService.GetBaseProductSkuByCode(spuCodes, false, fxUserId);
                    if (codes.Count > 0)
                    {
                        matchedMsg.AppendLine($"基础商品编码: {string.Join(",", codes)}已存在！");
                    }
                }
                if (skuCodes.Count > 0)
                {
                    var codes = baseProductSkuService.GetBaseProductSkuByCode(skuCodes, true, fxUserId);
                    if (codes.Count > 0)
                    {
                        matchedMsg.AppendLine($"基础商品SKU编码: {string.Join(",", codes)}已存在！");
                    }
                }
                if (matchedMsg.IsNotNullOrEmpty())
                {
                    return FalidResult("本地文件SPU与sku编码与商品库编码发生冲突", new { type = 3, msg = matchedMsg.ToString() });
                }
                #endregion
            }

            // 任务创建
            var param = new BaseProductJsonParam();
            param.Repeated = repeated;
            param.Required = required;
            param.Matcheed = matcheed;
            param.Params = list;

            var newTask = new ExportTask
            {
                IP = Request.UserHostAddress,
                CreateTime = DateTime.Now,
                PlatformType = CustomerConfig.CloudPlatformType,
                ShopId = shopId,
                UserId = fxUserId.ToString(),
                Status = 0,
                Type = ExportType.ErpBaseProductImport.ToInt(),
                PageIndex = 1,
                PageSize = 50,
                TotalCount = list.Count,

                FromModule = "商品库--导入基础商品",
                ExtField2 = shop.PlatformType,
                ExtField5 = dbname,
            };
            var service = new ExportTaskService();
            //添加任务参数JSON
            var uniqueCode = service.AddTaskParamJson(param.ToJson());
            newTask.ParamJson = uniqueCode;
            newTask.Id = service.Add(newTask);
            return SuccessResult("导出任务创建成功！", newTask.Id);
        }

        private ExcelDatasModel DataTableToCustomOrder(DataTable dt)
        {
            ExcelDatasModel model = new ExcelDatasModel();
            model.ColumnNames = ExcelHelper.GetColumnsByDataTable(dt).ToList();

            var rows = dt.Rows;
            for (var i = 0; i < rows.Count; i++)
            {
                List<string> row = new List<string>();
                foreach (var col in model.ColumnNames)
                {
                    row.Add(rows[i][col].ToString2());
                }
                model.Rows.Add(row);
            }
            return model;
        }
        #endregion

        /// <summary>
        /// 同步平台商品数据到基础商品 
        /// 只能在平台商品所在云平台发起请求
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [LogForOperatorFilter("更新覆盖平台商品")]
        public ActionResult SyncToPtSku(BaseSkuSyncToPtSkuModel model)
        {
            #region 校验
            var allSkuCodes = model.SkuCodes.SelectMany(v => v.Value) ?? new List<string>();
            if (model.SkuCodes.IsNullOrEmptyList() || allSkuCodes.Any(s => s.IsNullOrEmpty()))
            {
                return FalidResult("请选择需要同步的规格");
            }
            if (model.IsUseCostPrice == true && model.CostPrice == null)
            {
                return FalidResult("请设置成本价");
            }
            if (model.IsUseDistributePrice == true && model.DistributePrice == null)
            {
                return FalidResult("请设置分销价");
            }
            if (model.IsUseSettlePrice == true && model.SettlePrice == null)
            {
                return FalidResult("请设置采购价");
            }
            if (model.IsUseSupplierFxUser == true && model.BaseProductSkuSupplierConfig.IsNullOrEmptyList())
            {
                return FalidResult("请设置供货厂家");
            }
            #endregion

            var logFileName = "SyncToPtSku.txt";
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var dbName = Request["dbname"];
            if (dbName.IsNotNullOrEmpty())
            {
                try
                {
                    dbName = DES.DecryptDES(dbName, CustomerConfig.LoginCookieEncryptKey);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"平台dbName解析失败：{ex.Message}");
                    return FalidResult("平台dbName解析失败！");
                }
            }
            else
            {
                dbName = new ProductFxRepository().DbConnection.Database;
            }
            model.IsAllUseWarehouse = true;//默认为true
            model.FxUserId = fxUserId;
            // 是否来源于异常中心
            if (!model.IsFromCenter) model.DbName = dbName;
            else
            {
                try
                {
                    if (model.DbName.IsNotNullOrEmpty()) model.DbName = DES.DecryptDES(model.DbName, CustomerConfig.LoginCookieEncryptKey);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"平台dbName解析失败：{ex.Message}");
                    return FalidResult("平台dbName解析失败！");
                }

                // 判断要设置的商品是否精选
                if (model.ProductCloudPlatform == PlatformType.Alibaba.ToString())
                {
                    if (dbName != model.DbName)
                    {
                        // 重新初始化上下文重新调用方法
                        var site = new SiteContext(SiteContext.Current.CurrentFxUser, model.DbName);
                    }
                }
                // 跨云调用
                else
                {
                    try
                    {
                        const string apiUrl = "/BaseProductApi/SyncPtInfo";
                        var targetSiteUrl = CustomerConfig.GetTargetSiteUrl(model.ProductCloudPlatform).TrimEnd("/") + apiUrl;

                        Log.Debug(() => $"跨云，targetSiteUrl={targetSiteUrl}，lastModel={model.ToJson()}", logFileName);

                        var result = Common.PostFxSiteApi<BaseSkuSyncToPtSkuModel, bool>(targetSiteUrl, fxUserId, model, "跨云更新平台商品信息", isEncrypt: true);
                        if (result)
                            _baseProductAbnormalService.UpdateStatus(new List<long> { model.AbnormalId }, 1, fxUserId);
                        else
                            return FalidResult("更新失败");
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"跨云更新平台商品信息：{ex.Message}");
                        return FalidResult("同步成功");
                    }
                }
            }

            try
            {
                var changeSkuCodes = new List<string>();
                new BaseProductSkuCommonService().SyncToPtSku(model, ref changeSkuCodes, isNeedUseMemberLevel: model.IsNeedUseMemberLevel);
            }
            catch (Exception ex)
            {
                Log.WriteError($"更新覆盖平台商品发生异常: {ex.StackTrace}");
                return FalidResult($"服务器繁忙，请稍会儿再试");
            }

            if (model.IsFromCenter) new BaseProductAbnormalService().UpdateStatus(new List<long> { model.AbnormalId }, 1, fxUserId);
            return SuccessResult("同步成功");
        }
        #endregion

        #region 公用方法
        /// <summary>
        /// 检测库存仓库
        /// </summary>
        private void CheckWareHouseStatus()
        {
            var req = new WarehouseListRequest
            {
                PageIndex = 1,
                PageSize = 20,
                OwnerCode = _service.GetOwnerCode(SiteContext.Current.CurrentFxUserId),
            };

            var res = _service.LoadStoreManagementList(req);
            if (res.Warehouses != null && res.Warehouses.Count > 0)
            {
                //var status = res.Warehouses.First().Status == "Enabled";
                // 仅校验是否有仓库
                ViewBag.WareHouseStatus = 1;
            }
            else
            {
                ViewBag.WareHouseStatus = 0;
            }
        }

        /// <summary>
        /// 任务中断
        /// </summary>
        private void CheckTaskStatus()
        {
            // 若存在超过3分钟没有开始的任务，则设为手动中断
            var curFxUserId = SiteContext.Current.CurrentFxUserId;
            // 查看是否存在任务中
            // var existTask = _asyncTaskService.GetAsyncTask(new AsyncTaskQueryModel
            // {
            //     Flag = "BaseProductAutoRelationBind",
            //     FxUserId = curFxUserId,
            //     Status = 0
            // });

            for (int i = 1; i < 5; i++)
            {
                var platformType = (CloudPlatformType)i;
                var existTask = _asyncTaskService.GetAsyncTask(new AsyncTaskQueryModel
                {
                    Flag = "BaseProductAutoRelationBind",
                    CloudPlatformType = platformType.ToString(),
                    FxUserId = curFxUserId,
                    Status = 0
                });

                if (existTask != null)
                {
                    var createTime = existTask.CreateTime;
                    if ((DateTime.Now - createTime).TotalMinutes > 3)
                    {
                        existTask.Status = -10;
                        existTask.UpdateTime = DateTime.Now;
                        existTask.ExceptionDesc = "任务超时未开始";
                        _asyncTaskService.UpdateAsyncTask(existTask);
                    }
                }
            }
        }

        /// <summary>
        /// 基础商品自动关联
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [LogForOperatorFilter("基础商品自动关联")]
        public ActionResult AutoRelationBindNew(BaseProductAutoMappingModel model)
        {
            if (model.SkuUidStrDic == null || model.SkuUidStrDic.Any() == false) return FalidResult("请选择关联商品！");

            var dbName = Request["dbname"];
            if (dbName.IsNotNullOrEmpty())
            {
                try
                {
                    dbName = DES.DecryptDES(dbName, CustomerConfig.LoginCookieEncryptKey);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"平台dbName解析失败：{ex.Message}");
                    return FalidResult("平台dbName解析失败！");
                }
            }
            else return FalidResult("平台dbName不能为空！");

            var curFxUserId = SiteContext.Current.CurrentFxUserId;
            model.Request = new RequestHttpModel { DbName = Request["dbname"], UserHostAddress = Request.UserHostAddress };

            // 查看是否存在任务中
            var existTask = _asyncTaskService.GetAsyncTask(new AsyncTaskQueryModel
            {
                Flag = "BaseProductAutoRelationBind",
                FxUserId = curFxUserId,
                StatusList = new List<int> { 0, 1 }
            });

            if (existTask != null)
                return existTask.Status == 0
                    ? FalidResult("当前有任务但未开始执行，请等3分钟后刷新页面重试！")
                    : FalidResult("当前有任务正在执行，请稍后再试！");

            var newModel = new BaseProductSkuCommonService().PreModelInfo(model, curFxUserId);

            if (newModel.SkuCodeList.Count == 0) return FalidResult("未找到关联商品！");
            // 查看是否存在Redis锁
            var redisKey = CacheKeys.BaseProductAutoRelationBindKey.Replace("{FxUserId}", curFxUserId.ToString());
            if (RedisHelper.Exists(redisKey))
            {
                var redisValue = RedisHelper.Get(redisKey).ToObject<List<string>>();
                // 找到redisValue和model.SkuCodeList的交集
                var intersect = redisValue?.Intersect(newModel.SkuCodeList).ToList();
                if (intersect != null && intersect.Any())
                {
                    // 返回已存在的交集，用逗号分隔
                    if (CustomerConfig.IsDebug) Log.Debug($"操作太频繁啦，请稍后再试！商品SkuCode：{string.Join(",", intersect)}");
                    return FalidResult($"操作太频繁啦，请稍后再试！");
                }
            }
            // 加Redis锁，一分钟
            var setRedisValue = newModel.SkuCodeList.ToJson();
            RedisHelper.Set(redisKey, setRedisValue, TimeSpan.FromMinutes(1));

            return SuccessResult("任务已提交，请稍后查看！");
        }

        [FxAuthorize(FxPermission.DistributionLog)]
        public ActionResult DistributionLog()
        {

            return View();
        }

        // 操作日志嵌套页面
        public ActionResult BaseProductOperationLog()
        {

            return View();
        }


        #endregion
    }
}






