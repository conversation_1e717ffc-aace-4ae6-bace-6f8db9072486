body {
  font-family: 'Microsoft YaHei', '思源黑体', 'Source <PERSON> SC', 'PingFang SC', 'Hiragino Sans GB', 'WenQuanYi Micro Hei' !important;
}
i,
s,
em {
  font-style: normal;
  text-decoration: none;
}
.wu-weight400 {
  font-weight: 400;
}
.wu-weight500 {
  font-weight: 500;
}
.wu-weight600 {
  font-weight: 600;
}
.wu-weight700 {
  font-weight: 700;
}
.wu-lineH16 {
  line-height: 16px;
}
.wu-lineH20 {
  line-height: 20px;
}
.wu-lineH24 {
  line-height: 24px;
}
.wu-lineH28 {
  line-height: 28px;
}
.wu-lineH32 {
  line-height: 32px;
}
.wu-color-c9 {
  color: rgba(0, 0, 0, 0.9);
}
.wu-color-c6 {
  color: rgba(0, 0, 0, 0.6);
}
.wu-color-c4 {
  color: rgba(0, 0, 0, 0.4);
}
.wu-color-c3 {
  color: rgba(0, 0, 0, 0.3);
}
.wu-color-c2 {
  color: rgba(0, 0, 0, 0.09);
}
.wu-color-c1 {
  color: rgba(0, 0, 0, 0.04);
}
.wu-color-a {
  color: #0888ff;
}
.wu-color-ab {
  color: #0676ed;
}
.wu-color-a8 {
  color: rgba(8, 136, 255, 0.8);
}
.wu-color-a3 {
  color: rgba(8, 136, 255, 0.3);
}
.wu-color-a2 {
  color: rgba(8, 136, 255, 0.2);
}
.wu-color-a1 {
  color: rgba(8, 136, 255, 0.08);
}
.wu-2radius {
  border-radius: 2px;
}
.wu-4radius {
  border-radius: 4px;
}
.wu-6radius {
  border-radius: 6px;
}
.wu-8radius {
  border-radius: 8px;
}
.wu-c09 {
  color: rgba(0, 0, 0, 0.9);
}
.wu-c06 {
  color: rgba(0, 0, 0, 0.6);
}
.wu-c04 {
  color: rgba(0, 0, 0, 0.4);
}
.wu-c03 {
  color: rgba(0, 0, 0, 0.3);
}
.wu-f36 {
  font-size: 36px;
}
.wu-f24 {
  font-size: 24px;
}
.wu-f20 {
  font-size: 20px;
}
.wu-f18 {
  font-size: 18px;
}
.wu-f16 {
  font-size: 16px;
}
.wu-f14 {
  font-size: 14px;
}
.wu-f12 {
  font-size: 12px;
}
.wu-border {
  border: 1px solid rgba(0, 0, 0, 0.09);
}
.wu-borderL {
  border-left: 1px solid rgba(0, 0, 0, 0.09);
}
.wu-borderR {
  border-right: 1px solid rgba(0, 0, 0, 0.09);
}
.wu-borderT {
  border-top: 1px solid rgba(0, 0, 0, 0.09);
}
.wu-borderB {
  border-bottom: 1px solid rgba(0, 0, 0, 0.09);
}
.wu-p2 {
  padding: 2px !important;
}
.wu-pL2 {
  padding-left: 2px !important;
}
.wu-pR2 {
  padding-right: 2px !important;
}
.wu-pT2 {
  padding-top: 2px !important;
}
.wu-pB2 {
  padding-bottom: 2px !important;
}
.wu-p4 {
  padding: 4px !important;
}
.wu-pL4 {
  padding-left: 4px !important;
}
.wu-pR4 {
  padding-right: 4px !important;
}
.wu-pT4 {
  padding-top: 4px !important;
}
.wu-pB4 {
  padding-bottom: 4px !important;
}
.wu-p8 {
  padding: 8px !important;
}
.wu-pL8 {
  padding-left: 8px !important;
}
.wu-pR8 {
  padding-right: 8px !important;
}
.wu-pT8 {
  padding-top: 8px !important;
}
.wu-pB8 {
  padding-bottom: 8px !important;
}
.wu-p12 {
  padding: 12px !important;
}
.wu-pL12 {
  padding-left: 12px !important;
}
.wu-pR12 {
  padding-right: 12px !important;
}
.wu-pT12 {
  padding-top: 12px !important;
}
.wu-pB12 {
  padding-bottom: 12px !important;
}
.wu-p16 {
  padding: 16px !important;
}
.wu-pL16 {
  padding-left: 16px !important;
}
.wu-pR16 {
  padding-right: 16px !important;
}
.wu-pT16 {
  padding-top: 16px !important;
}
.wu-pB16 {
  padding-bottom: 16px !important;
}
.wu-p20 {
  padding: 20px !important;
}
.wu-pL20 {
  padding-left: 20px !important;
}
.wu-pR20 {
  padding-right: 20px !important;
}
.wu-pT20 {
  padding-top: 20px !important;
}
.wu-pB20 {
  padding-bottom: 20px !important;
}
.wu-p24 {
  padding: 24px !important;
}
.wu-pL24 {
  padding-left: 24px !important;
}
.wu-pR24 {
  padding-right: 24px !important;
}
.wu-pT24 {
  padding-top: 24px !important;
}
.wu-pB24 {
  padding-bottom: 24px !important;
}
.wu-p36 {
  padding: 36px !important;
}
.wu-pL36 {
  padding-left: 36px !important;
}
.wu-pR36 {
  padding-right: 36px !important;
}
.wu-pT36 {
  padding-top: 36px !important;
}
.wu-pB36 {
  padding-bottom: 36px !important;
}
.wu-p48 {
  padding: 48px !important;
}
.wu-pL48 {
  padding-left: 48px !important;
}
.wu-pR48 {
  padding-right: 48px !important;
}
.wu-pT48 {
  padding-top: 48px !important;
}
.wu-pB48 {
  padding-bottom: 48px !important;
}
.wu-p0 {
  padding: 0 !important;
}
.wu-pL0 {
  padding-left: 0 !important;
}
.wu-pR0 {
  padding-right: 0 !important;
}
.wu-pT0 {
  padding-top: 0 !important;
}
.wu-pB0 {
  padding-bottom: 0 !important;
}
.wu-m2 {
  margin: 2px !important;
}
.wu-mL2 {
  margin-left: 2px !important;
}
.wu-mR2 {
  margin-right: 2px !important;
}
.wu-mT2 {
  margin-top: 2px !important;
}
.wu-mB2 {
  margin-bottom: 2px !important;
}
.wu-m4 {
  margin: 4px !important;
}
.wu-mL4 {
  margin-left: 4px !important;
}
.wu-mR4 {
  margin-right: 4px !important;
}
.wu-mT4 {
  margin-top: 4px !important;
}
.wu-mB4 {
  margin-bottom: 4px !important;
}
.wu-m8 {
  margin: 8px !important;
}
.wu-mL8 {
  margin-left: 8px !important;
}
.wu-mR8 {
  margin-right: 8px !important;
}
.wu-mB8 {
  margin-bottom: 8px !important;
}
.wu-mT8 {
  margin-top: 8px !important;
}
.wu-m12 {
  margin: 12px !important;
}
.wu-mL12 {
  margin-left: 12px !important;
}
.wu-mR12 {
  margin-right: 12px !important;
}
.wu-mB12 {
  margin-bottom: 12px !important;
}
.wu-mT12 {
  margin-top: 12px !important;
}
.wu-m16 {
  margin: 16px !important;
}
.wu-mL16 {
  margin-left: 16px !important;
}
.wu-mR16 {
  margin-right: 16px !important;
}
.wu-mB16 {
  margin-bottom: 16px !important;
}
.wu-mT16 {
  margin-top: 16px !important;
}
.wu-m20 {
  margin: 20px !important;
}
.wu-mL20 {
  margin-left: 20px !important;
}
.wu-mR20 {
  margin-right: 20px !important;
}
.wu-mB20 {
  margin-bottom: 20px !important;
}
.wu-mT20 {
  margin-top: 20px !important;
}
.wu-m24 {
  margin: 24px !important;
}
.wu-mL24 {
  margin-left: 24px !important;
}
.wu-mR24 {
  margin-right: 24px !important;
}
.wu-mB24 {
  margin-bottom: 24px !important;
}
.wu-mT24 {
  margin-top: 24px !important;
}
.wu-m36 {
  margin: 36px !important;
}
.wu-mL36 {
  margin-left: 36px !important;
}
.wu-mR36 {
  margin-right: 36px !important;
}
.wu-mT36 {
  margin-top: 36px !important;
}
.wu-mB36 {
  margin-bottom: 36px !important;
}
.wu-m48 {
  margin: 48px !important;
}
.wu-mL48 {
  margin-left: 48px !important;
}
.wu-mR48 {
  margin-right: 48px !important;
}
.wu-mT48 {
  margin-top: 48px !important;
}
.wu-mB48 {
  margin-bottom: 48px !important;
}
.wu-m0 {
  margin: 0 !important;
}
.wu-mL0 {
  margin-left: 0 !important;
}
.wu-mR0 {
  margin-right: 0 !important;
}
.wu-mT0 {
  margin-top: 0 !important;
}
.wu-mB0 {
  margin-bottom: 0 !important;
}
ul,
ol {
  list-style: none;
  margin: 0;
  padding: 0;
}
.wu-layui-select input[type=text],
.wu-layui-select textarea {
  border: 1px solid rgba(0, 0, 0, 0.14);
}
.wu-color-e {
  color: #424a57;
}
.wu-color-e.hover {
  color: rgba(66, 74, 87, 0.8);
}
.wu-color-e.active {
  color: #2d333d;
}
.wu-color-e.disabled {
  color: rgba(66, 74, 87, 0.3);
  cursor: not-allowed;
}
.wu-color-e.wu-operate {
  cursor: pointer;
}
.wu-color-e.wu-operate:hover {
  color: rgba(66, 74, 87, 0.8);
}
.wu-color-e.wu-operate:active {
  color: #2d333d;
}
.wu-color-e.wu-border {
  border: 1px solid rgba(66, 74, 87, 0.2);
}
.wu-color-e.wu-background {
  background-color: rgba(66, 74, 87, 0.1);
}
.wu-color-m {
  color: rgba(0, 0, 0, 0.6);
}
.wu-color-m.hover {
  color: rgba(0, 0, 0, 0.4);
}
.wu-color-m.active {
  color: rgba(0, 0, 0, 0.9);
}
.wu-color-m.disabled {
  color: rgba(0, 0, 0, 0.3);
  cursor: not-allowed;
}
.wu-color-m.wu-operate {
  cursor: pointer;
}
.wu-color-m.wu-operate:hover {
  color: rgba(0, 0, 0, 0.4);
}
.wu-color-m.wu-operate:active {
  color: rgba(0, 0, 0, 0.9);
}
.wu-color-m.wu-border {
  border: 1px solid rgba(0, 0, 0, 0.09);
}
.wu-color-m.wu-background {
  background-color: rgba(0, 0, 0, 0.04);
}
.wu-color-a {
  color: #0888ff;
}
.wu-color-a.hover {
  color: rgba(8, 136, 255, 0.8);
}
.wu-color-a.active {
  color: #0676ed;
}
.wu-color-a.disabled {
  color: rgba(8, 136, 255, 0.3);
  cursor: not-allowed;
}
.wu-color-a.wu-operate {
  cursor: pointer;
}
.wu-color-a.wu-operate:hover {
  color: rgba(8, 136, 255, 0.8);
}
.wu-color-a.wu-operate:active {
  color: #0676ed;
}
.wu-color-a.wu-operate.disabled {
  color: rgba(8, 136, 255, 0.3);
  cursor: not-allowed;
}
.wu-color-a.wu-border {
  border: 1px solid rgba(8, 136, 255, 0.2);
}
.wu-color-a.wu-background {
  background-color: rgba(8, 136, 255, 0.08);
}
.wu-color-b {
  color: #ea572e;
}
.wu-color-b.hover {
  color: rgba(234, 87, 46, 0.8);
}
.wu-color-b.active {
  color: #dc3b0e;
}
.wu-color-b.disabled {
  color: rgba(234, 87, 46, 0.3);
  cursor: not-allowed;
}
.wu-color-b.wu-operate {
  cursor: pointer;
}
.wu-color-b.wu-operate:hover {
  color: rgba(234, 87, 46, 0.8);
}
.wu-color-b.wu-operate:active {
  color: #dc3b0e;
}
.wu-color-b.wu-operate.disabled {
  color: rgba(234, 87, 46, 0.3);
  cursor: not-allowed;
}
.wu-color-b.wu-border {
  border: 1px solid rgba(234, 87, 46, 0.2);
}
.wu-color-b.wu-background {
  background-color: rgba(234, 87, 46, 0.1);
}
.wu-color-c {
  color: #f18c03;
}
.wu-color-c.hover {
  color: rgba(241, 140, 3, 0.8);
}
.wu-color-c.active {
  color: #de7200;
}
.wu-color-c.disabled {
  color: rgba(241, 140, 3, 0.3);
  cursor: not-allowed;
}
.wu-color-c.wu-operate {
  cursor: pointer;
}
.wu-color-c.wu-operate:hover {
  color: rgba(241, 140, 3, 0.8);
}
.wu-color-c.wu-operate:active {
  color: #de7200;
}
.wu-color-c.wu-operate.disabled {
  color: rgba(241, 140, 3, 0.3);
  cursor: not-allowed;
}
.wu-color-c.wu-border {
  border: 1px solid rgba(241, 140, 3, 0.2);
}
.wu-color-c.wu-background {
  background-color: rgba(241, 140, 3, 0.1);
}
.wu-color-d {
  color: #73ac1f;
}
.wu-color-d.hover {
  color: rgba(115, 172, 31, 0.8);
}
.wu-color-d.active {
  color: #639e0c;
}
.wu-color-d.disabled {
  color: rgba(115, 172, 31, 0.3);
  cursor: not-allowed;
}
.wu-color-d.wu-operate {
  cursor: pointer;
}
.wu-color-d.wu-operate:hover {
  color: rgba(115, 172, 31, 0.8);
}
.wu-color-d.wu-operate:active {
  color: #639e0c;
}
.wu-color-d.wu-operate.disabled {
  color: rgba(115, 172, 31, 0.3);
  cursor: not-allowed;
}
.wu-color-d.wu-border {
  border: 1px solid rgba(115, 172, 31, 0.2);
}
.wu-color-d.wu-background {
  background-color: rgba(115, 172, 31, 0.1);
}
.wu-color-f {
  color: #a234d9;
}
.wu-color-f.hover {
  color: rgba(162, 52, 217, 0.8);
}
.wu-color-f.active {
  color: #8e1bc1;
}
.wu-color-f.disabled {
  color: rgba(162, 52, 217, 0.3);
  cursor: not-allowed;
}
.wu-color-f.wu-operate {
  cursor: pointer;
}
.wu-color-f.wu-operate:hover {
  color: rgba(162, 52, 217, 0.8);
}
.wu-color-f.wu-operate:active {
  color: #8e1bc1;
}
.wu-color-f.wu-border {
  border: 1px solid rgba(162, 52, 217, 0.2);
}
.wu-color-f.wu-background {
  background-color: rgba(162, 52, 217, 0.1);
}
.wu-color-g {
  color: #ff42ad;
}
.wu-color-g.hover {
  color: rgba(255, 66, 173, 0.8);
}
.wu-color-g.active {
  color: #d92b90;
}
.wu-color-g.disabled {
  color: rgba(255, 66, 173, 0.3);
  cursor: not-allowed;
}
.wu-color-g.wu-operate {
  cursor: pointer;
}
.wu-color-g.wu-operate:hover {
  color: rgba(255, 66, 173, 0.8);
}
.wu-color-g.wu-operate:active {
  color: #d92b90;
}
.wu-color-g.wu-border {
  border: 1px solid rgba(255, 66, 173, 0.2);
}
.wu-color-g.wu-background {
  background-color: rgba(255, 66, 173, 0.1);
}
.wu-color-j {
  color: #24d3f2;
}
.wu-color-j.hover {
  color: rgba(36, 211, 242, 0.8);
}
.wu-color-j.active {
  color: #16adcc;
}
.wu-color-j.disabled {
  color: rgba(36, 211, 242, 0.3);
  cursor: not-allowed;
}
.wu-color-j.wu-operate {
  cursor: pointer;
}
.wu-color-j.wu-operate:hover {
  color: rgba(36, 211, 242, 0.8);
}
.wu-color-j.wu-operate:active {
  color: #16adcc;
}
.wu-color-j.wu-border {
  border: 1px solid rgba(36, 211, 242, 0.2);
}
.wu-color-j.wu-background {
  background-color: rgba(36, 211, 242, 0.1);
}
.wu-color-n {
  color: rgba(255, 255, 255, 0.7);
}
.wu-color-n.hover {
  color: rgba(255, 255, 255, 0.5);
}
.wu-color-n.active {
  color: rgba(255, 255, 255, 0.9);
}
.wu-color-n.disabled {
  color: rgba(255, 255, 255, 0.3);
  cursor: not-allowed;
}
.wu-color-n.wu-operate {
  cursor: pointer;
}
.wu-color-n.wu-operate:hover {
  color: rgba(255, 255, 255, 0.5);
}
.wu-color-n.wu-operate:active {
  color: rgba(255, 255, 255, 0.9);
}
.wu-color-n.wu-border {
  border: 1px solid rgba(255, 255, 255, 0.2);
}
.wu-color-n.wu-background {
  background-color: #ffffff;
}
.wu-color-k {
  color: rgba(0, 0, 0, 0.4);
}
.wu-color-k.hover {
  color: #0888ff;
}
.wu-color-k.active {
  color: #0676ed;
}
.wu-color-k.disabled {
  color: rgba(0, 0, 0, 0.3);
  cursor: not-allowed;
}
.wu-color-k.wu-operate {
  cursor: pointer;
}
.wu-color-k.wu-operate:hover {
  color: #0888ff;
}
.wu-color-k.wu-operate:active {
  color: #0676ed;
}
.wu-color-k.wu-operate.disabled {
  color: rgba(0, 0, 0, 0.3);
  cursor: not-allowed;
}
.wu-color-k.wu-border {
  border: 1px solid rgba(115, 172, 31, 0.2);
}
.wu-color-k.wu-background {
  background-color: rgba(115, 172, 31, 0.1);
}
.wu-hide {
  display: none !important;
}
.wu-hover {
  cursor: pointer;
}
.wu-flex {
  display: flex;
}
.wu-flex.wu-xCenter {
  justify-content: center;
}
.wu-flex.wu-yCenter {
  align-items: center;
}
.wu-flex.wu-center {
  align-items: center;
  justify-content: center;
}
.wu-flex.wu-betteen {
  justify-content: space-between;
}
.wu-flex.wu-left {
  justify-content: flex-start;
}
.wu-flex.wu-right {
  justify-content: flex-end;
}
.wu-flex.wu-column {
  flex-direction: column;
}
.wu-flex.wu-column.wu-xCenter {
  align-items: center;
}
.wu-flex.wu-column.wu-yCenter {
  justify-content: center;
}
.wu-flex.wu-column.wu-betteen {
  align-items: space-between;
}
.wu-flex.wu-column.wu-left {
  align-items: flex-start;
}
.wu-flex.wu-column.wu-right {
  align-items: flex-end;
}
.wu-yCenter {
  align-items: center;
}
.wu-center {
  align-items: center;
  justify-content: center;
}
.wu-betteen {
  justify-content: space-between;
}
.wu-endA {
  align-items: flex-end;
}
.wu-endB {
  justify-content: flex-end;
}
.wu-flex-1 {
  flex: 1;
}
.wu-flex-2 {
  flex: 2;
}
.wu-flex-3 {
  flex: 3;
}
.wu-btn {
  box-sizing: border-box;
  outline: none;
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  text-align: center;
  border-radius: 8px;
  background-color: #0888ff;
  color: #fff;
  cursor: pointer;
  letter-spacing: 0em;
  line-height: normal;
  padding: 8px 16px;
  height: 40px;
  font-size: 16px;
  border: unset;
}
.wu-btn:hover {
  background: rgba(8, 136, 255, 0.8);
}
.wu-btn:active {
  background-color: #0676ed;
}
.wu-btn.disabled {
  background: rgba(0, 0, 0, 0.3);
  cursor: not-allowed;
}
.wu-btn.wu-one {
  background-color: #ea572e;
}
.wu-btn.wu-one:hover {
  background: rgba(234, 87, 46, 0.8);
}
.wu-btn.wu-one:active {
  background-color: #dc3b0e;
}
.wu-btn.wu-one.disabled {
  background: rgba(0, 0, 0, 0.3);
  cursor: not-allowed;
}
.wu-btn.wu-two {
  background-color: #f18c03;
}
.wu-btn.wu-two:hover {
  background: rgba(241, 140, 3, 0.8);
}
.wu-btn.wu-two:active {
  background-color: #de7200;
}
.wu-btn.wu-two.disabled {
  background: rgba(0, 0, 0, 0.3);
  cursor: not-allowed;
}
.wu-btn.wu-three {
  background-color: #73ac1f;
}
.wu-btn.wu-three:hover {
  background: rgba(115, 172, 31, 0.8);
}
.wu-btn.wu-three:active {
  background-color: #639e0c;
}
.wu-btn.wu-three.disabled {
  background: rgba(0, 0, 0, 0.3);
  cursor: not-allowed;
}
.wu-btn.wu-info.wu-one.wu-styleOne {
  background-color: rgba(8, 136, 255, 0.08);
  color: #0888ff;
}
.wu-btn.wu-info.wu-one.wu-styleOne:hover {
  color: rgba(8, 136, 255, 0.8);
}
.wu-btn.wu-info.wu-one.wu-styleOne:active {
  color: #0676ed;
}
.wu-btn.wu-info.wu-one.wu-styleTwo {
  background-color: rgba(241, 140, 3, 0.1);
  color: #f18c03;
}
.wu-btn.wu-info.wu-one.wu-styleTwo:hover {
  color: rgba(241, 140, 3, 0.8);
}
.wu-btn.wu-info.wu-one.wu-styleTwo:active {
  color: #de7200;
}
.wu-btn.wu-info.wu-one.wu-styleThree {
  background-color: rgba(234, 87, 46, 0.1);
  color: #ea572e;
}
.wu-btn.wu-info.wu-one.wu-styleThree:hover {
  color: rgba(234, 87, 46, 0.8);
}
.wu-btn.wu-info.wu-one.wu-styleThree:active {
  color: #dc3b0e;
}
.wu-btn.wu-info.wu-one.disabled {
  opacity: 0.4;
}
.wu-btn-mid {
  height: 32px;
  font-size: 14px;
  padding: 6px 12px;
  border-radius: 6px;
}
.wu-btn-small {
  height: 24px;
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 4px;
}
.wu-btn .wu-btn-icon {
  font-size: 16px;
  margin-right: 8px;
  margin-left: -3px;
}
.wu-btn .wu-btn-icon.wu-btn-icon-b {
  margin-left: 8px;
  margin-right: -4px;
}
.wu-btn.wu-btn-mid .wu-btn-icon {
  font-size: 14px;
  margin-right: 4px;
}
.wu-btn.wu-btn-mid .wu-btn-icon.wu-btn-icon-b {
  margin-left: 6px;
  margin-right: -4px;
}
.wu-btn.wu-btn-small .wu-btn-icon {
  font-size: 12px;
  margin-right: 4px;
  margin-left: -2px;
}
.wu-btn.wu-btn-small .wu-btn-icon.wu-btn-icon-b {
  margin-left: 4px;
  margin-right: -2px;
}
.wu-btn .wu-btn-icon-all {
  margin: 0 !important;
}
.wu-btn.wu-info {
  background-color: #f5f5f5;
  color: rgba(0, 0, 0, 0.9);
}
.wu-btn.wu-info:hover {
  color: rgba(8, 136, 255, 0.8);
  background-color: rgba(8, 136, 255, 0.08);
}
.wu-btn.wu-info:active {
  color: #0888ff;
}
.wu-btn.wu-info.disabled {
  cursor: not-allowed;
  color: rgba(0, 0, 0, 0.3);
}
.wu-btn.wu-info.disabled:hover {
  background: rgba(0, 0, 0, 0.09);
}
.wu-btn.wu-info.wu-two {
  background-color: rgba(8, 136, 255, 0.08);
  color: #0888ff;
  border: 1px solid rgba(8, 136, 255, 0.2);
}
.wu-btn.wu-info.wu-two:hover {
  color: rgba(8, 136, 255, 0.8);
}
.wu-btn.wu-info.wu-two:active {
  color: #0676ed;
}
.wu-btn.wu-info.wu-two.disabled {
  color: rgba(0, 0, 0, 0.3);
  background-color: rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(0, 0, 0, 0.09);
}
.wu-btn.wu-primary {
  border: 1px solid rgba(0, 0, 0, 0.09);
  background-color: #fff;
  color: rgba(0, 0, 0, 0.9);
}
.wu-btn.wu-primary:hover {
  border: 1px solid #0888ff;
  color: #0888ff;
}
.wu-btn.wu-primary:active {
  border: 1px solid #0676ed;
  color: #0676ed;
}
.wu-btn.wu-primary.disabled {
  cursor: not-allowed;
  border: 1px solid rgba(0, 0, 0, 0.09);
  color: rgba(0, 0, 0, 0.3);
  background-color: #fff;
}
.wu-btn.wu-primary.wu-two {
  border: 1px solid #0888ff;
  background-color: #fff;
  color: #0888ff;
}
.wu-btn.wu-primary.wu-two:hover {
  border: 1px solid rgba(8, 136, 255, 0.8);
  color: rgba(8, 136, 255, 0.8);
}
.wu-btn.wu-primary.wu-two:active {
  border: 1px solid #0676ed;
  color: #0676ed;
}
.wu-btn.wu-primary.wu-two.disabled {
  border-color: rgba(8, 136, 255, 0.3);
  color: rgba(8, 136, 255, 0.3);
  cursor: not-allowed;
}
.wu-btn.wu-primary.wu-two.disabled:hover {
  background-color: #FFFFFF;
  border-color: rgba(8, 136, 255, 0.3);
  color: rgba(8, 136, 255, 0.3);
}
.wu-btn.wu-primary.wu-three {
  border: 1px solid #ea572e;
  background-color: #fff;
  color: #ea572e;
}
.wu-btn.wu-primary.wu-three:hover {
  border: 1px solid rgba(234, 87, 46, 0.8);
  color: rgba(234, 87, 46, 0.8);
}
.wu-btn.wu-primary.wu-three:active {
  border: 1px solid #dc3b0e;
  color: #dc3b0e;
}
.wu-btn.wu-primary.wu-three.disabled {
  border-color: rgba(234, 87, 46, 0.3);
  color: rgba(234, 87, 46, 0.3);
  cursor: not-allowed;
}
.wu-btn.wu-primary.wu-three.disabled:hover {
  background-color: #FFFFFF;
  border-color: rgba(234, 87, 46, 0.3);
  color: rgba(234, 87, 46, 0.3);
}
.wu-btn.wu-primary.wu-four {
  border: 1px solid #f18c03;
  background-color: #fff;
  color: #f18c03;
}
.wu-btn.wu-primary.wu-four:hover {
  border: 1px solid rgba(241, 140, 3, 0.8);
  color: rgba(241, 140, 3, 0.8);
}
.wu-btn.wu-primary.wu-four:active {
  border: 1px solid #de7200;
  color: #de7200;
}
.wu-btn.wu-primary.wu-four.disabled {
  border-color: rgba(241, 140, 3, 0.3);
  color: rgba(241, 140, 3, 0.3);
  cursor: not-allowed;
}
.wu-btn.wu-primary.wu-four.disabled:hover {
  background-color: #FFFFFF;
  border-color: rgba(241, 140, 3, 0.3);
  color: rgba(241, 140, 3, 0.3);
}
.wu-btn.wu-primary.wu-five {
  border: 1px solid #73ac1f;
  background-color: #fff;
  color: #73ac1f;
}
.wu-btn.wu-primary.wu-five:hover {
  border: 1px solid rgba(115, 172, 31, 0.8);
  color: rgba(115, 172, 31, 0.8);
}
.wu-btn.wu-primary.wu-five:active {
  border: 1px solid #639e0c;
  color: #639e0c;
}
.wu-btn.wu-primary.wu-five.disabled {
  border-color: rgba(115, 172, 31, 0.3);
  color: rgba(115, 172, 31, 0.3);
  cursor: not-allowed;
}
.wu-btn.wu-primary.wu-five.disabled:hover {
  background-color: #FFFFFF;
  border-color: rgba(115, 172, 31, 0.3);
  color: rgba(115, 172, 31, 0.3);
}
.wu-dHColor {
  color: #0676ed;
}
.wu-dCColor {
  color: rgba(8, 136, 255, 0.8);
}
.wu-dDColor {
  color: rgba(8, 136, 255, 0.3);
}
.wu-dColor {
  color: #0888ff;
}
.wu-eHColor {
  color: #dc3b0e;
}
.wu-eCColor {
  color: rgba(234, 87, 46, 0.8);
}
.wu-eDColor {
  color: rgba(234, 87, 46, 0.3);
}
.wu-eColor {
  color: #ea572e;
}
.wu-fHColor {
  color: #de7200;
}
.wu-fCColor {
  color: rgba(241, 140, 3, 0.8);
}
.wu-fDColor {
  color: rgba(241, 140, 3, 0.3);
}
.wu-fColor {
  color: #f18c03;
}
.wu-gHColor {
  color: #639e0c;
}
.wu-gCColor {
  color: rgba(115, 172, 31, 0.8);
}
.wu-gDColor {
  color: rgba(115, 172, 31, 0.3);
}
.wu-gColor {
  color: #73ac1f;
}
.wu-eColor.wu-common-btn:hover {
  color: #dc3b0e;
}
.wu-eColor.wu-common-btn:active {
  color: #ea572e;
}
.wu-fColor.wu-common-btn:hover {
  color: #f18c03;
}
.wu-fColor.wu-common-btn:active {
  color: #f18c03;
}
.wu-hover {
  cursor: pointer;
}
.wu-common-btn {
  cursor: pointer;
  white-space: nowrap;
}
.wu-dColor.wu-common-btn:hover {
  color: rgba(8, 136, 255, 0.8);
}
.wu-dColor.wu-common-btn:active {
  color: #0676ed;
}
.wu-common-btn.wu-f16 .iconfont {
  font-size: 16px;
  margin-right: 8px;
}
.wu-common-btn.wu-f14 .iconfont {
  font-size: 14px;
  margin-right: 6px;
}
.wu-common-btn.wu-f12 .iconfont {
  font-size: 12px;
  margin-right: 4px;
}
.wu-common-btn.wu-f16 .iconfont.wu-btn-icon-right {
  margin-right: 0;
  margin-left: 8px;
}
.wu-common-btn.wu-f14 .iconfont.wu-btn-icon-right {
  margin-right: 0;
  margin-left: 6px;
}
.wu-common-btn.wu-f12 .iconfont.wu-btn-icon-right {
  margin-right: 0;
  margin-left: 4px;
}
.wu-dColor.disabled {
  color: rgba(8, 136, 255, 0.3);
  cursor: not-allowed;
}
.wu-inputWrap {
  display: inline-flex;
  align-items: center;
  position: relative;
}
.wu-inputWrap.wu-active .wu-input {
  background-color: rgba(8, 136, 255, 0.08);
  border: 1px solid rgba(8, 136, 255, 0.2);
}
.wu-inputWrap.wu-warn {
  flex-direction: column;
  align-items: flex-start;
}
.wu-inputWrap.wu-warn .wu-input {
  border: 1px solid #ea572e;
}
.wu-inputWrap.wu-warn .wu-warn-title {
  display: inline-block;
  color: #ea572e;
  margin-top: 4px;
  font-size: 12px;
}
.wu-inputWrap.disabled::after {
  position: absolute;
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  border-radius: 8px;
  background: rgba(0, 0, 0, 0.04);
  z-index: 10;
}
.wu-inputWrap.disabled {
  cursor: not-allowed;
}
.wu-inputWrap.disabled .wu-input {
  border: 1px solid rgba(0, 0, 0, 0.04);
  pointer-events: none;
}
.wu-inputWrap.disabled .wu-input.no-focus {
  pointer-events: none;
}
.wu-inputWrap.disabled .wu-inputWrap-content {
  border: 1px solid rgba(0, 0, 0, 0.04);
}
.wu-inputWrap .icon-a-close-circle-filled1x {
  position: absolute;
  right: -12px;
  color: #999999;
  cursor: pointer;
  font-size: 18px;
  opacity: 0;
  top: 10px;
}
.wu-inputWrap .icon-a-close-circle-filled1x:hover {
  color: #666;
}
.wu-inputWrap:focus-within .icon-a-close-circle-filled1x {
  display: inline-block;
  opacity: 1;
  right: 8px;
}
.wu-inputWrap .wu-input {
  border: 1px solid rgba(0, 0, 0, 0.14);
  outline: unset;
  border-radius: 8px;
  height: 40px;
  padding: 8px 10px;
  display: flex;
  box-sizing: border-box;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.9);
  width: 100%;
}
.wu-inputWrap .wu-input::placeholder {
  color: rgba(0, 0, 0, 0.5);
}
.wu-inputWrap .wu-input:hover {
  border: 1px solid #0888ff;
}
.wu-inputWrap .wu-input:focus {
  border-color: #0888ff !important;
  border: 1px solid #0888ff;
  box-shadow: 0px 0px 0px 2px rgba(8, 136, 255, 0.2);
}
.wu-inputWrap.wu-form-mid .wu-input {
  height: 32px;
  font-size: 12px;
  padding: 6px 8px;
  border-radius: 6px;
}
.wu-inputWrap.wu-form-mid .wu-input::placeholder {
  font-size: 12px;
}
.wu-inputWrap.wu-form-mid:focus-within .icon-a-close-circle-filled1x {
  font-size: 16px;
  right: 6px;
  top: 8px;
  background-color: #fff;
  border-radius: 50%;
}
.wu-inputWrap.wu-form-mid .icon-a-close-circle-filled1x:active {
  opacity: 1;
  right: 8px;
  font-size: 14px;
}
.wu-inputWrap.wu-form-small .wu-input {
  height: 24px;
  font-size: 12px;
  padding: 4px 6px;
  border-radius: 4px;
}
.wu-inputWrap.wu-form-small:focus-within .icon-a-close-circle-filled1x {
  font-size: 14px;
  right: 4px;
  top: 5px;
}
.wu-inputWrap.wu-formCombine {
  display: inline-flex;
}
.wu-inputWrap.wu-formCombine.wu-active .wu-inputWrap-content {
  border: 1px solid rgba(8, 136, 255, 0.2);
  background-color: rgba(8, 136, 255, 0.08);
}
.wu-inputWrap.wu-formCombine.wu-active .wu-input {
  border: unset;
  background-color: unset;
}
.wu-inputWrap.wu-formCombine .wu-inputWrap-content {
  display: inline-flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  border: 1px solid rgba(0, 0, 0, 0.14);
  border-radius: 8px;
}
.wu-inputWrap.wu-formCombine .wu-inputWrap-content .wu-input {
  height: 38px;
}
.wu-inputWrap.wu-formCombine .wu-inputWrap-content .wu-inputWrap-left-title {
  display: inline-flex;
  align-items: center;
}
.wu-inputWrap.wu-formCombine.disabled .wu-inputWrap-content {
  border: 1px solid rgba(0, 0, 0, 0.04);
}
.wu-inputWrap.wu-formCombine.wu-warn .wu-inputWrap-content {
  border: 1px solid #ea572e;
}
.wu-inputWrap.wu-formCombine.wu-form-mid .wu-inputWrap-content {
  border-radius: 6px;
}
.wu-inputWrap.wu-formCombine.wu-form-mid .wu-inputWrap-left-title {
  font-size: 14px;
  padding-left: 8px;
  height: 28px;
}
.wu-inputWrap.wu-formCombine.wu-form-mid .wu-inputWrap-right-title {
  font-size: 14px;
  padding-right: 8px;
}
.wu-inputWrap.wu-formCombine.wu-form-mid .wu-inputWrap-content .wu-input {
  height: 30px;
}
.wu-inputWrap.wu-formCombine.wu-form-small .wu-inputWrap-content {
  border-radius: 4px;
}
.wu-inputWrap.wu-formCombine.wu-form-small .wu-inputWrap-left-title {
  font-size: 12px;
  padding-left: 4px;
  height: 22px;
}
.wu-inputWrap.wu-formCombine.wu-form-small .wu-inputWrap-right-title {
  font-size: 12px;
  padding-right: 4px;
}
.wu-inputWrap.wu-formCombine.wu-form-small .wu-inputWrap-content .wu-input {
  height: 22px;
}
.wu-inputWrap.wu-formCombine .wu-inputWrap-content:focus-within {
  border: 1px solid #0888ff;
  box-shadow: 0px 0px 0px 2px rgba(8, 136, 255, 0.2);
}
.wu-inputWrap.wu-formCombine .wu-inputWrap-content:hover {
  border: 1px solid #0888ff;
}
.wu-inputWrap.wu-formCombine.disabled .wu-inputWrap-left-title {
  color: rgba(0, 0, 0, 0.4);
}
.wu-inputWrap.wu-formCombine.disabled .wu-inputWrap-right-title {
  color: rgba(0, 0, 0, 0.4);
}
.wu-inputWrap.wu-formCombine .wu-inputWrap-left-title {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.6);
  white-space: nowrap;
  padding-left: 10px;
}
.wu-inputWrap.wu-formCombine .wu-inputWrap-right-title {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.6);
  white-space: nowrap;
  padding-right: 10px;
}
.wu-inputWrap.wu-formCombine .wu-input {
  border: unset;
  display: flex;
  align-items: center;
}
.wu-inputWrap.wu-formCombine .wu-input::placeholder {
  position: relative;
  top: 1px;
}
.wu-inputWrap.wu-formCombine .wu-input:focus {
  box-shadow: unset;
}
.wu-inputWrap.wu-formCombine .wu-formCombine-left {
  position: relative;
  display: flex;
  align-items: center;
  flex: 1;
}
.wu-selectWrap {
  position: relative;
  display: inline-flex;
  align-items: center;
  box-sizing: border-box;
}
.wu-selectWrap.disabled {
  cursor: not-allowed;
  color: rgba(0, 0, 0, 0.3);
}
.wu-selectWrap.disabled .wu-select {
  border: 1px solid rgba(0, 0, 0, 0.04);
}
.wu-selectWrap.disabled .wu-selectWrap-content .wu-selectWrap-left-title {
  color: rgba(0, 0, 0, 0.3);
}
.wu-selectWrap.disabled .wu-selectWrap-content .wu-selectWrap-right-title {
  color: rgba(0, 0, 0, 0.3);
}
.wu-selectWrap.disabled .wu-selectWrap-content .wu-select {
  border: 1px solid rgba(0, 0, 0, 0.04);
  color: rgba(0, 0, 0, 0.3);
}
.wu-selectWrap.disabled::after {
  position: absolute;
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  border-radius: 8px;
  background: rgba(0, 0, 0, 0.04);
  z-index: 100;
}
.wu-selectWrap.disabled:hover .wu-select {
  border: 1px solid rgba(0, 0, 0, 0.04);
}
.wu-selectWrap:hover .wu-select {
  border: 1px solid #0888ff;
}
.wu-selectWrap .wu-select {
  width: 100%;
  height: 40px;
  display: flex;
  flex-direction: row;
  padding: 0 10px;
  border: 1px solid rgba(0, 0, 0, 0.14);
  box-sizing: border-box;
  border-radius: 8px;
  font-size: 16px;
  outline: unset;
  color: rgba(0, 0, 0, 0.5);
  appearance: none;
  -webkit-appearance: none;
  cursor: pointer;
  direction: ltl;
}
.wu-selectWrap .wu-select option {
  background-color: #fff;
  color: rgba(0, 0, 0, 0.9);
}
.wu-selectWrap .wu-select.wu-active {
  color: rgba(0, 0, 0, 0.9);
}
.wu-selectWrap .wu-select:focus {
  border: 1px solid #0888ff;
  box-shadow: 0px 0px 0px 2px rgba(8, 136, 255, 0.2);
}
.wu-selectWrap .icon-a-chevron-down1x {
  position: absolute;
  right: 8px;
  z-index: 10;
  font-size: 18px;
  color: rgba(0, 0, 0, 0.5);
  padding-left: 8px;
  background-color: #fff;
  pointer-events: none;
  top: 11px;
}
.wu-selectWrap.wu-form-mid {
  font-size: 12px;
}
.wu-selectWrap.wu-form-mid .wu-select {
  height: 32px;
  padding: 0 8px;
  font-size: 12px;
  border-radius: 6px;
  color: rgba(0, 0, 0, 0.9);
}
.wu-selectWrap.wu-form-mid .icon-a-chevron-down1x {
  right: 6px;
  font-size: 16px;
  top: 8px;
}
.wu-selectWrap.wu-form-small .wu-select {
  height: 24px;
  padding: 0 6px;
  font-size: 12px;
  border-radius: 4px;
}
.wu-selectWrap.wu-form-small .icon-a-chevron-down1x {
  right: 4px;
  font-size: 14px;
  top: 5px;
}
.wu-selectWrap .wu-selectWrap-content {
  display: inline-flex;
  align-items: center;
  font-size: 16px;
  border-radius: 8px;
  width: 100%;
  position: relative;
}
.wu-selectWrap .wu-selectWrap-content .wu-selectWrap-left-title {
  white-space: nowrap;
  color: rgba(0, 0, 0, 0.6);
  position: absolute;
  z-index: 10;
  padding-left: 10px;
  pointer-events: none;
}
.wu-selectWrap .wu-selectWrap-content .wu-selectWrap-right-title {
  white-space: nowrap;
  color: rgba(0, 0, 0, 0.6);
  position: absolute;
  z-index: 10;
  padding-left: 10px;
  pointer-events: none;
  right: 35px;
  background-color: #fff;
}
.wu-selectWrap .wu-selectWrap-content .wu-select {
  border: unset;
  padding-right: 25px;
  border: 1px solid rgba(0, 0, 0, 0.14);
}
.wu-selectWrap .wu-selectWrap-content .wu-select:hover {
  border: 1px solid #0888ff;
}
.wu-selectWrap .wu-selectWrap-content .wu-select:focus-within {
  border: 1px solid #0888ff;
  box-shadow: 0px 0px 0px 2px rgba(8, 136, 255, 0.2);
}
.wu-selectWrap .wu-selectWrap-content .wu-selectWrap-select {
  flex: 1;
  display: flex;
  align-items: center;
}
.wu-selectWrap .wu-selectWrap-content .wu-selectWrap-select .icon-a-chevron-down1x {
  right: 6px;
}
.wu-selectWrap.wu-form-mid .wu-selectWrap-left-title {
  font-size: 14px;
  padding-left: 8px;
  top: 6px;
}
.wu-selectWrap.wu-form-mid .wu-selectWrap-right-title {
  font-size: 14px;
  right: 27px;
}
.wu-selectWrap.wu-form-small .wu-selectWrap-left-title {
  font-size: 12px;
  padding-left: 6px;
}
.wu-selectWrap.wu-form-small .wu-selectWrap-right-title {
  font-size: 12px;
  line-height: 22px;
  right: 24px;
}
.wu-selectWrap .wu-selectWrap-select {
  position: relative;
}
.wu-selectWrap.wu-formCombine.wu-form-mid .icon-a-chevron-down1x {
  right: 4px;
}
.wu-selectWrap.wu-formCombine.wu-form-mid .wu-select {
  height: 32px;
}
.wu-selectWrap.wu-formCombine.wu-form-small .icon-a-chevron-down1x {
  right: 2px;
}
.wu-selectWrap.wu-formCombine.wu-form-small .wu-select {
  height: 24px;
}
.wu-selectWrap.wu-formCombine.wu-form-right .wu-select {
  padding-left: 0;
}
.wu-selectWrap.wu-warn {
  flex-direction: column;
  align-items: flex-start;
}
.wu-selectWrap.wu-warn .wu-select {
  border-color: #ea572e;
}
.wu-selectWrap.wu-warn .wu-warn-title {
  display: inline-block;
  color: #ea572e;
  margin-top: 4px;
  font-size: 12px;
}
.wu-selectWrap.wu-active .wu-select {
  color: rgba(0, 0, 0, 0.9);
  background-color: rgba(8, 136, 255, 0.08);
  border: 1px solid rgba(8, 136, 255, 0.2);
  box-shadow: unset;
  animation-duration: .3s;
}
.wu-selectWrap.wu-active .wu-select option {
  background-color: #fff;
}
.wu-selectWrap.wu-active .icon-a-chevron-down1x {
  background-color: unset;
}
.selectWrap {
  width: 100%;
}
.selectWrap .electWrap_search_wrap {
  box-shadow: unset;
}
.selectWrap .electWrap_search_wrap .selectWrap-search-input {
  height: 30px;
  padding-left: 6px;
}
.selectWrap .electWrap_search_wrap::before {
  top: 6px;
}
.selectWrap .electWrap_search_wrap::after {
  top: 18px;
}
.selectWrap.disabled::after {
  position: absolute;
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  border-radius: 8px;
  background: rgba(0, 0, 0, 0.04);
  z-index: 10;
  cursor: not-allowed;
}
.selectWrap.disabled .selectMore {
  border: 1px solid rgba(0, 0, 0, 0.04);
  color: rgba(0, 0, 0, 0.3);
}
.selectWrap.disabled .selectMore .selectMore-choose > li {
  color: rgba(0, 0, 0, 0.3);
}
.selectWrap.disabled .selectMore .leftTitle {
  color: rgba(0, 0, 0, 0.3);
}
.selectWrap.wu-select-skin {
  width: 100%;
  font-size: 12px;
}
.selectWrap.wu-select-skin .electWrap_search_wrap {
  border-radius: 6px;
}
.selectWrap.wu-select-skin .electWrap_search_wrap .selectWrap-search-input {
  background-color: unset;
}
.selectWrap.wu-select-skin .selectMore-ul {
  font-size: 12px;
}
.selectWrap.wu-select-skin.wu-showActive.wu-active .selectMore,
.selectWrap.wu-select-skin.wu-showActive.activeChoose .selectMore {
  box-shadow: unset !important;
  background-color: rgba(8, 136, 255, 0.08) !important;
  border: 1px solid rgba(8, 136, 255, 0.2) !important;
}
.selectWrap.wu-select-skin.wu-showActive.wu-active .selectMore:before,
.selectWrap.wu-select-skin.wu-showActive.activeChoose .selectMore:before {
  background-color: unset;
}
.selectWrap.wu-select-skin.wu-showActive.wu-active .selectMore-choose > li,
.selectWrap.wu-select-skin.wu-showActive.activeChoose .selectMore-choose > li {
  background-color: unset;
}
.selectWrap.wu-select-skin.wu-showActive.wu-active .selectMore-choose .isRadio,
.selectWrap.wu-select-skin.wu-showActive.activeChoose .selectMore-choose .isRadio {
  background-color: unset;
}
.selectWrap.wu-select-skin .electWrap_search_wrap {
  box-shadow: unset;
  font-size: 14px;
}
.selectWrap.wu-select-skin .electWrap_search_wrap .selectWrap-search-input {
  height: 28px;
  padding-left: 5px;
}
.selectWrap.wu-select-skin .electWrap_search_wrap::before {
  top: 5px;
}
.selectWrap.wu-select-skin .electWrap_search_wrap::after {
  top: 16px;
}
.selectWrap.wu-select-skin .selectMore-ul {
  font-size: 12px;
}
.selectWrap.wu-select-skin .selectMore-choose {
  width: 100%;
}
.selectWrap.wu-select-skin .selectMore-choose > li {
  max-width: 80%;
}
.selectWrap.wu-select-skin .selectMore-choose > li .selectMore-choose-title {
  max-width: unset;
}
.selectWrap.wu-select-skin .selectMore-ul li .radio-label {
  color: rgba(0, 0, 0, 0.9);
}
.selectWrap.wu-select-skin .selectMore-ul li span {
  color: rgba(0, 0, 0, 0.9);
}
.selectWrap.wu-select-skin.wu-form-mid .selectMore {
  border-radius: 6px;
  height: 32px;
  font-size: 12px;
}
.selectWrap.wu-select-skin.wu-form-mid .selectMore .leftTitle {
  padding-left: 8px;
}
.selectWrap.wu-select-skin.wu-form-mid .showMoreicon {
  right: 10px;
  top: 11px !important;
  width: 5px;
  height: 5px;
}
.selectWrap.wu-select-skin.wu-form-mid .selectMore-choose > li:first-child {
  margin-left: 3px;
}
.selectWrap.wu-select-skin.wu-form-mid .selectWrap-box {
  animation: mySelectAnimation2 0.3s ease-in-out 0s 1 alternate forwards;
}
.selectWrap.wu-select-skin.wu-form-small .electWrap_search_wrap {
  box-shadow: unset;
  font-size: 12px;
}
.selectWrap.wu-select-skin.wu-form-small .electWrap_search_wrap .selectWrap-search-input {
  height: 24px;
  padding-left: 5px;
}
.selectWrap.wu-select-skin.wu-form-small .electWrap_search_wrap::before {
  top: 4px;
}
.selectWrap.wu-select-skin.wu-form-small .electWrap_search_wrap::after {
  top: 12px;
}
.selectWrap.wu-select-skin.wu-form-small .selectMore-ul {
  font-size: 12px;
}
.selectWrap.wu-select-skin.wu-form-small .selectMore {
  height: 24px;
  font-size: 12px;
  border-radius: 4px;
}
.selectWrap.wu-select-skin.wu-form-small .selectMore .leftTitle {
  padding-left: 6px;
}
.selectWrap.wu-select-skin.wu-form-small .showMoreicon {
  right: 8px;
  top: 8px;
  width: 4px;
  height: 4px;
}
.selectWrap.wu-select-skin.wu-form-small .selectMore-choose > li:first-child {
  margin-left: 1px;
}
.selectWrap.wu-select-skin.wu-form-small .selectWrap-box {
  animation: mySelectAnimation3 0.3s ease-in-out 0s 1 alternate forwards;
}
.selectWrap.wu-select-skin .selectMore {
  top: unset;
  display: flex;
  align-items: center;
  height: 40px;
  border-radius: 8px;
  font-size: 14px;
  border: 1px solid rgba(0, 0, 0, 0.14);
}
.selectWrap.wu-select-skin .selectMore .leftTitle {
  display: inline-block;
  flex: 1;
  word-break: keep-all;
  white-space: nowrap;
  color: rebeccapurple;
  color: rgba(0, 0, 0, 0.6);
  padding-left: 10px;
}
.selectWrap.wu-select-skin .selectMore::before {
  width: 20px;
  height: 24px;
  padding-bottom: 10px;
}
.selectWrap.wu-select-skin .selectMore-choose > li {
  color: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
}
.selectWrap.wu-select-skin .selectMore-choose > li:first-child {
  margin-left: 5px;
}
.selectWrap.wu-select-skin .showMoreicon {
  right: 13px;
  z-index: 100;
  border-bottom: 1.2px solid rgba(0, 0, 0, 0.5);
  border-left: 1.2px solid rgba(0, 0, 0, 0.5);
  width: 6px;
  height: 6px;
  transform: rotate(-45deg);
  border-top: unset;
  border-right: unset;
  top: 15px;
}
.selectWrap.wu-select-skin .selectWrap-box {
  opacity: 0;
  animation: mySelectAnimation100 0.3s ease-in-out 0s 1 alternate forwards;
  flex-direction: column;
  padding: 8px;
  background: #FFFFFF;
  border-radius: 8px;
  border: unset;
  box-shadow: 0px 12px 24px 0px rgba(0, 0, 0, 0.04), 0px 8px 16px 0px rgba(0, 0, 0, 0.04), 0px 2px 4px 0px rgba(0, 0, 0, 0.08);
  width: 100%;
}
.selectWrap.wu-select-skin .selectMore:hover {
  border: 1px solid #0888FF !important;
  cursor: pointer;
}
.selectWrap.wu-select-skin.active .selectMore {
  border: 1px solid #0888FF !important;
  box-shadow: 0px 0px 0px 2px rgba(8, 136, 255, 0.2) !important;
}
.selectWrap.wu-select-skin .selectMore-ul > li {
  box-sizing: border-box;
  color: rgba(0, 0, 0, 0.6);
  cursor: pointer;
  border-radius: 6px;
  display: flex;
  align-items: center;
  position: relative;
  margin: 4px 0;
}
.selectWrap.wu-select-skin .selectMore-ul > li:hover {
  background: rgba(0, 0, 0, 0.04);
}
.selectWrap.wu-select-skin .selectMore-ul > li.active {
  background: rgba(0, 0, 0, 0.04);
}
.selectWrap.wu-select-skin .selectMore-ul > li label {
  padding: 6px 8px;
  display: flex;
  width: 100%;
  align-items: center;
}
.selectWrap.wu-select-skin input[type="checkbox"] {
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 4px;
  position: relative;
  -webkit-appearance: none;
  /* 移除默认样式 */
  -moz-appearance: none;
  /* 移除默认样式 */
  appearance: none;
  /* 移除默认样式 */
}
.selectWrap.wu-select-skin input[type="checkbox"]::after {
  position: absolute;
  top: -1px;
  left: -1px;
  background-color: white;
  color: #000;
  width: 16px;
  height: 16px;
  border-radius: 4px;
  display: inline-block;
  visibility: visible;
  padding-left: 0px;
  text-align: center;
  content: '';
  box-sizing: border-box;
  border: 1px solid #dbdbdb;
  z-index: 10;
}
.selectWrap.wu-select-skin input[type="checkbox"]:checked::before {
  position: absolute;
  content: "";
  width: 8px;
  height: 4px;
  border-left: 2px solid #ffffff;
  border-bottom: 2px solid #fff;
  z-index: 100;
  display: block;
  transform: rotate(315deg);
  top: 3px;
  left: 2px;
}
.selectWrap.wu-select-skin input[type="checkbox"]:checked::after {
  border: 1px solid #0888ff;
  background-color: #0888ff;
  font-size: 12px;
  font-weight: bold;
  color: #fff;
  box-sizing: border-box;
}
.selectWrap.wu-select-skin .electWrap-allSelect {
  color: #0888ff;
}
.selectWrap.wu-select-skin .electWrap-allSelect label {
  display: flex;
  align-items: center;
}
.selectWrap.wu-select-skin .allSelect-checkbox {
  margin-left: 8px;
}
.selectWrap.wu-select-skin .list-icon {
  display: none !important;
}
.selectWrap.wu-select-skin.activeChoose .selectMore {
  border: 1px solid #0888ff !important;
  box-shadow: 0px 0px 0px 2px rgba(8, 136, 255, 0.2) !important;
}
.selectWrap .selectMore-choose-title {
  color: rgba(0, 0, 0, 0.9);
}
@keyframes darationTime {
  0% {
    right: 8px;
  }
  100% {
    right: 100px;
  }
}
@keyframes mySelectAnimation100 {
  0% {
    opacity: 0;
    top: 62px;
  }
  100% {
    opacity: 1;
    top: 42px;
  }
}
@keyframes mySelectAnimation2 {
  0% {
    opacity: 0;
    top: 54px;
  }
  100% {
    opacity: 1;
    top: 34px;
  }
}
@keyframes mySelectAnimation3 {
  0% {
    opacity: 0;
    top: 46px;
  }
  100% {
    opacity: 1;
    top: 26px;
  }
}
.wu-mySelectWrap {
  display: flex;
  flex-direction: column;
}
.wu-mySelectWrap .wu-select-skin {
  margin-bottom: 0;
}
.wu-mySelectWrap .wu-select-skin .selectMore {
  border-color: #ea572e;
}
.wu-mySelectWrap .wu-warn-title {
  display: inline-block;
  color: #ea572e;
  margin-top: 4px;
  font-size: 12px;
}
.wu-layui-select {
  display: inline-block;
}
.wu-layui-select .layui-unselect input[type=text] {
  color: rgba(0, 0, 0, 0.9);
  font-size: 12px;
}
.wu-layui-select .layui-unselect input[type=text]::placeholder {
  color: rgba(0, 0, 0, 0.9);
  font-size: 12px;
  font-family: 'Microsoft YaHei', '˼Դ����';
}
.wu-layui-select.wu-active .layui-unselect input[type=text] {
  color: rgba(0, 0, 0, 0.9) !important;
}
.wu-layui-select.wu-layui-connect {
  border: 1px solid rgba(0, 0, 0, 0.09);
  display: inline-flex;
  border-radius: 8px;
  padding-left: 10px;
  align-items: center;
  position: relative;
}
.wu-layui-select.wu-layui-connect.wu-form-mid {
  border-radius: 6px;
  padding-left: 8px;
}
.wu-layui-select.wu-layui-connect.wu-form-mid .wu-layui-select-title {
  font-size: 12px;
}
.wu-layui-select.wu-layui-connect.wu-form-small {
  border-radius: 4px;
  padding-left: 6px;
}
.wu-layui-select.wu-layui-connect.wu-form-small .wu-layui-select-title {
  font-size: 12px;
}
.wu-layui-select.wu-layui-connect:hover {
  border: 1px solid #0888ff;
}
.wu-layui-select.wu-layui-connect:focus-within {
  border: 1px solid #0888ff;
  box-shadow: 0px 0px 0px 2px rgba(8, 136, 255, 0.2);
}
.wu-layui-select.wu-layui-connect input[type=text] {
  border: unset;
}
.wu-layui-select.wu-layui-connect .wu-layui-select-title {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.6);
}
.wu-layui-select.wu-layui-connect .layui-form-select {
  flex: 1;
  position: unset;
}
.wu-layui-select.wu-layui-connect .layui-unselect input[type=text] {
  color: rgba(0, 0, 0, 0.4);
}
.wu-layui-select.wu-layui-connect .layui-unselect:focus {
  box-shadow: unset;
}
.wu-layui-select.wu-active .layui-input {
  background-color: rgba(8, 136, 255, 0.08);
  border: 1px solid rgba(8, 136, 255, 0.2);
}
.wu-layui-select .layui-select-disabled .layui-select-title::after {
  position: absolute;
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  border-radius: 8px;
  background: rgba(0, 0, 0, 0.04);
  z-index: 10;
  cursor: not-allowed;
}
.wu-layui-select .layui-select-disabled .layui-unselect::placeholder {
  color: rgba(0, 0, 0, 0.5);
}
.wu-layui-select .layui-select-disabled .layui-unselect:hover {
  border-color: rgba(0, 0, 0, 0.04) !important;
}
.wu-layui-select .layui-select-disabled .layui-unselect:focus {
  border-color: rgba(0, 0, 0, 0.04) !important;
  box-shadow: unset;
}
.wu-layui-select.wu-form-mid .layui-form-select {
  font-size: 12px;
}
.wu-layui-select.wu-form-mid .layui-unselect {
  height: 32px;
  border-radius: 6px;
}
.wu-layui-select.wu-form-mid .layui-unselect input {
  padding-left: 8px;
}
.wu-layui-select.wu-form-mid .layui-form-select .layui-edge {
  border-bottom: 1.2px solid rgba(0, 0, 0, 0.5);
  border-left: 1.2px solid rgba(0, 0, 0, 0.5);
  width: 5px;
  height: 5px;
  transform: rotate(-45deg);
  margin-top: -5px;
  right: 10px;
}
.wu-layui-select.wu-form-mid .layui-anim-upbit {
  top: 33px;
}
.wu-layui-select.wu-form-small .layui-form-select {
  font-size: 12px;
}
.wu-layui-select.wu-form-small .layui-unselect {
  height: 24px;
  border-radius: 4px;
}
.wu-layui-select.wu-form-small .layui-unselect input {
  padding-left: 6px;
}
.wu-layui-select.wu-form-small .layui-form-select .layui-edge {
  border-bottom: 1.2px solid rgba(0, 0, 0, 0.5);
  border-left: 1.2px solid rgba(0, 0, 0, 0.5);
  width: 4px;
  height: 4px;
  transform: rotate(-45deg);
  margin-top: -4px;
  right: 8px;
}
.wu-layui-select.wu-form-small .layui-anim-upbit {
  top: auto;
  bottom: 26px;
}
.wu-layui-select .layui-unselect {
  border-radius: 8px;
  height: 40px;
}
.wu-layui-select .layui-unselect::placeholder {
  color: rgba(0, 0, 0, 0.5);
}
.wu-layui-select .layui-unselect:hover {
  border-color: #0888ff !important;
}
.wu-layui-select .layui-unselect:focus {
  border-color: #0888ff !important;
  box-shadow: 0px 0px 0px 2px rgba(8, 136, 255, 0.2);
}
.wu-layui-select .layui-form-select {
  font-size: 16px;
}
.wu-layui-select .layui-form-select .layui-edge {
  border-top-color: unset;
  border-top-style: unset;
  border-top: unset;
  border-style: unset;
  border-width: unset;
  border-bottom: 1.2px solid rgba(0, 0, 0, 0.5);
  border-left: 1.2px solid rgba(0, 0, 0, 0.5);
  width: 6px;
  height: 6px;
  transform: rotate(-45deg);
  margin-top: -5px;
  right: 12px;
}
.wu-layui-select .layui-form-select .layui-anim-upbit {
  padding: 8px;
  background: #FFFFFF;
  border-radius: 8px;
  border: unset;
  box-shadow: 0px 12px 24px 0px rgba(0, 0, 0, 0.04), 0px 8px 16px 0px rgba(0, 0, 0, 0.04), 0px 2px 4px 0px rgba(0, 0, 0, 0.08);
  width: 100%;
}
.wu-layui-select .layui-form-select .layui-anim-upbit dd {
  padding-left: 6px !important;
  padding: 6px 8px;
  box-sizing: border-box;
  color: rgba(0, 0, 0, 0.9);
  cursor: pointer;
  border-radius: 6px;
  display: flex;
  align-items: center;
  position: relative;
  margin: 0;
  line-height: unset;
  margin: 4px 0;
}
.wu-layui-select .layui-form-select .layui-anim-upbit dd.layui-this {
  padding-left: 6px !important;
  background: rgba(0, 0, 0, 0.04);
  color: rgba(0, 0, 0, 0.9);
}
.wu-radioWrap input[type=radio] {
  cursor: pointer;
  width: 14px !important;
  height: 14px !important;
  border: unset !important;
  box-sizing: border-box;
  position: relative;
  border-color: transparent !important;
}
.wu-radioWrap input[type=radio]:hover {
  border-color: #0888ff;
}
.wu-radioWrap input[type=radio]::before {
  position: relative;
  content: "";
  top: -1px;
  left: -1px;
  width: 16px;
  height: 16px;
  display: block;
  border-radius: 50%;
  background-color: #fff;
  border: 1px solid rgba(0, 0, 0, 0.14);
  z-index: 5;
  box-sizing: border-box;
}
.wu-radioWrap input[type=radio]:hover::before {
  border: 1px solid #0888ff;
}
.wu-radioWrap input[type=radio]:checked::before {
  border: 1px solid #0888ff;
}
.wu-radioWrap input[type=radio]:checked::after {
  position: relative;
  content: "";
  bottom: 13px;
  left: 3px;
  width: 8px;
  height: 8px;
  display: block;
  border-radius: 50%;
  background-color: #0888ff;
  z-index: 6;
}
.wu-radioWrap input[type=radio]:disabled {
  cursor: not-allowed;
}
.wu-radioWrap input[type=radio]:disabled:hover::before {
  border: 1px solid rgba(0, 0, 0, 0.14);
}
.wu-radioWrap {
  display: inline-flex;
  position: relative;
}
.wu-radioWrap.disabled {
  opacity: 0.3;
}
.wu-radioWrap.disabled input[type=radio]:before {
  border: 1px solid rgba(0, 0, 0, 0.5);
}
.wu-radioWrap.disabled input[type=radio]:checked:before {
  opacity: 1;
  border: 1px solid #0888ff;
}
.wu-radioWrap.disabled::after {
  position: absolute;
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  z-index: 10;
  background-color: rgba(0, 0, 0, 0.13);
  cursor: not-allowed;
}
.wu-radioWrap.checked.disabled::after {
  background-color: rgba(8, 136, 255, 0.3);
}
.wu-my-radioWrap {
  display: inline-flex;
  position: relative;
  cursor: pointer;
  transition: 0.3s all;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}
.wu-my-radioWrap:hover .wu-my-radio {
  border-color: #0888ff;
}
.wu-my-radioWrap .wu-my-radio {
  padding: 0;
  margin: 0;
  width: 16px;
  height: 16px;
  display: inline-flex;
  border-radius: 50%;
  background-color: #fff;
  border: 1px solid rgba(0, 0, 0, 0.14);
  box-sizing: border-box;
  font-size: 12px;
  position: relative;
  justify-content: center;
  align-items: center;
}
.wu-my-radioWrap .wu-my-radio:hover {
  border-color: #0888ff;
}
.wu-my-radioWrap.checked .wu-my-radio {
  border-color: #0888ff;
}
.wu-my-radioWrap.checked .wu-my-radio::after {
  position: absolute;
  content: "";
  width: 8px;
  height: 8px;
  display: block;
  border-radius: 50%;
  background-color: #0888ff;
}
.wu-my-radioWrap.disabled {
  cursor: not-allowed;
}
.wu-my-radioWrap.disabled .wu-my-radio {
  background: rgba(0, 0, 0, 0.04);
  box-sizing: border-box;
  border: 1px solid rgba(0, 0, 0, 0.1);
}
.wu-my-radioWrap.disabled.checked .wu-my-radio {
  border: 1px solid rgba(8, 136, 255, 0.3);
  background-color: #e6f3ff;
}
.wu-my-radioWrap.disabled.checked .wu-my-radio::after {
  background-color: rgba(8, 136, 255, 0.3);
}
.wu-checkboxWrap {
  display: inline-flex;
  cursor: pointer;
  position: relative;
}
.wu-checkboxWrap input[type="checkbox"] {
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 4px;
  position: relative;
  -webkit-appearance: none;
  /* 移除默认样式 */
  -moz-appearance: none;
  /* 移除默认样式 */
  appearance: none;
  /* 移除默认样式 */
  cursor: pointer;
}
.wu-checkboxWrap input[type="checkbox"]::after {
  position: absolute;
  top: -1px;
  left: -1px;
  background-color: white;
  color: #000;
  width: 16px;
  height: 16px;
  border-radius: 4px;
  display: inline-block;
  visibility: visible;
  padding-left: 0px;
  text-align: center;
  content: '';
  box-sizing: border-box;
  border: 1px solid #dbdbdb;
  z-index: 10;
}
.wu-checkboxWrap input[type="checkbox"]:hover::after {
  border-color: #0888ff;
}
.wu-checkboxWrap input[type="checkbox"]:checked::before {
  position: absolute;
  content: "";
  width: 8px;
  height: 4px;
  border-left: 2px solid #ffffff;
  border-bottom: 2px solid #fff;
  z-index: 100;
  display: block;
  transform: rotate(315deg);
  top: 2px;
  left: 2px;
}
.wu-checkboxWrap input[type="checkbox"]:checked::after {
  border: 1px solid #0888ff;
  background-color: #0888ff;
  font-size: 12px;
  font-weight: bold;
  color: #fff;
  box-sizing: border-box;
}
.wu-checkboxWrap input[type="checkbox"].disabled {
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.04);
  cursor: not-allowed;
}
.wu-checkboxWrap.disabled {
  cursor: not-allowed;
}
.wu-checkboxWrap.disabled:after {
  display: block;
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.04);
  top: 0;
  left: 0;
  z-index: 10000;
}
.wu-checkboxWrap.disabled input[type="checkbox"]::after {
  border: 1px solid rgba(0, 0, 0, 0.04);
}
.wu-checkboxWrap.disabled input[type="checkbox"]:checked::before {
  position: absolute;
  content: "";
  width: 8px;
  height: 4px;
  border-left: 2px solid rgba(8, 136, 255, 0.3);
  border-bottom: 2px solid rgba(8, 136, 255, 0.3);
  z-index: 100;
  display: block;
  transform: rotate(315deg);
  top: 3px;
  left: 3px;
}
.wu-checkboxWrap.disabled input[type="checkbox"]:checked::after {
  border: 1px solid rgba(8, 136, 255, 0.1);
  background-color: rgba(8, 136, 255, 0.1);
  font-size: 12px;
  font-weight: bold;
  color: #fff;
  box-sizing: border-box;
}
.wu-checkboxWrap.disabled.checked:after {
  background: rgba(0, 0, 0, 0);
}
.wu-my-checkboxWrap {
  box-sizing: border-box;
  display: inline-flex;
  transition: 0.3s all;
  cursor: pointer;
}
.wu-my-checkboxWrap .wu-my-checkbox {
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 4px;
  border: 1px solid #dbdbdb;
  box-sizing: border-box;
  position: relative;
  background-color: #fff;
}
.wu-my-checkboxWrap:hover .wu-my-checkbox {
  border: 1px solid #0888ff;
}
.wu-my-checkboxWrap.checked .wu-my-checkbox {
  border: 1px solid #0888ff;
  background-color: #0888ff;
}
.wu-my-checkboxWrap.checked .wu-my-checkbox:before {
  position: absolute;
  content: "";
  width: 8px;
  height: 4px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  z-index: 100;
  display: block;
  transform: rotate(315deg);
  top: 2px;
  left: 2px;
}
.wu-my-checkboxWrap.part_checked .wu-my-checkbox {
  border: 1px solid #0888ff;
  background-color: #0888ff;
}
.wu-my-checkboxWrap.part_checked .wu-my-checkbox:before {
  position: absolute;
  content: "";
  width: 8px;
  height: 2px;
  background-color: #fff;
  z-index: 100;
  display: block;
  top: 6px;
  left: 3px;
}
.wu-my-checkboxWrap.disabled {
  cursor: not-allowed;
}
.wu-my-checkboxWrap.disabled .wu-my-checkbox {
  background: rgba(0, 0, 0, 0.04);
}
.wu-my-checkboxWrap.disabled:hover .wu-my-checkbox {
  border: 1px solid #dbdbdb;
}
.wu-my-checkboxWrap.disabled.checked .wu-my-checkbox {
  background: rgba(8, 136, 255, 0.1);
  border: 1px solid rgba(8, 136, 255, 0.1);
}
.wu-my-checkboxWrap.disabled.checked .wu-my-checkbox:before {
  border-left: 2px solid rgba(8, 136, 255, 0.3);
  border-bottom: 2px solid rgba(8, 136, 255, 0.3);
}
.wu-my-checkboxWrap.disabled.part_checked .wu-my-checkbox {
  background: rgba(8, 136, 255, 0.1);
  border: 1px solid rgba(8, 136, 255, 0.1);
}
.wu-my-checkboxWrap.disabled.part_checked .wu-my-checkbox:before {
  background-color: rgba(8, 136, 255, 0.3);
}
.wu-switch {
  position: relative;
  height: 24px;
  line-height: 24px;
  min-width: 44px;
  padding: 0 7px 0 5px;
  border-radius: 20px;
  cursor: pointer;
  background-color: #fff;
  display: inline-block;
  -webkit-transition: .1s linear;
  transition: .1s linear;
  box-sizing: border-box;
  background-color: rgba(66, 74, 87, 0.1);
}
.wu-switch.wu-form-mid {
  height: 20px;
  min-width: 36px;
}
.wu-switch.wu-form-mid i {
  width: 16px;
  height: 16px;
}
.wu-switch.wu-form-mid.active i {
  left: 100%;
  margin-left: -18px;
  background-color: #fff;
}
.wu-switch.wu-form-small {
  height: 16px;
  min-width: 28px;
}
.wu-switch.wu-form-small i {
  width: 12px;
  height: 12px;
}
.wu-switch.wu-form-small.active i {
  left: 100%;
  margin-left: -14px;
  background-color: #fff;
}
.wu-switch:hover {
  background-color: rgba(66, 74, 87, 0.2);
}
.wu-switch:hover.active {
  background-color: #0676ed;
}
.wu-switch em {
  position: relative;
  top: 0;
  padding: 0 !important;
  text-align: center !important;
  color: rgba(0, 0, 0, 0.6);
  font-style: normal !important;
  margin-left: 18px;
  font-size: 12px;
  margin-right: 1px;
}
.wu-switch i {
  position: absolute;
  left: 2px;
  top: 2px;
  width: 20px;
  height: 20px;
  border-radius: 20px;
  background-color: #fff;
  -webkit-transition: .1s linear;
  transition: .1s linear;
}
.wu-switch.active {
  border-color: #0888ff;
  background-color: #0888ff;
}
.wu-switch .switch-active {
  display: none;
}
.wu-switch.active .switch-close {
  display: none;
}
.wu-switch.active .switch-active {
  display: inline-block;
}
.wu-switch.active i {
  left: 100%;
  margin-left: -22px;
  background-color: #fff;
}
.wu-switch.active em {
  margin-left: 3px;
  margin-right: 16px;
  color: #fff;
}
.wu-switch.disabled {
  cursor: not-allowed;
  background-color: rgba(66, 74, 87, 0.3);
}
.wu-switch.disabled i {
  background: rgba(255, 255, 255, 0.5);
}
.wu-switch.disabled em {
  color: rgba(0, 0, 0, 0.3);
}
.wu-switch.disabled.active {
  background: rgba(8, 136, 255, 0.3);
}
.wu-switch.disabled.active i {
  background: rgba(255, 255, 255, 0.5);
}
.wu-switch.disabled.active em {
  color: rgba(255, 255, 255, 0.5);
}
.wu-tableWrap {
  border: 1px solid rgba(0, 0, 0, 0.09);
  border-radius: 6px;
  overflow-y: auto;
  color: rgba(0, 0, 0, 0.9);
  width: min-content;
  min-width: 100%;
  box-sizing: border-box;
}
.wu-tableWrap.min560 {
  min-width: 560px;
}
.wu-tableWrap.min888 {
  min-width: 888px;
}
@media screen and (max-width: 1440px) {
  .wu-tableWrap.min1200 body {
    background-color: #ff0000 !important;
  }
}
.wu-tableWrap .stockup_table_content {
  background-color: #fff;
}
.wu-tableWrap .stockup_table_content thead tr th {
  border-right: unset;
  border-top: unset;
  border-bottom: unset;
}
.wu-tableWrap .stockup_table_content tbody tr td {
  border-right: unset;
  border-top: unset;
  border-bottom: unset;
}
.wu-tableWrap .wu-table-one {
  border-left: none;
  width: 100%;
}
.wu-tableWrap .wu-table-one .wu-operate {
  letter-spacing: normal;
}
.wu-tableWrap .wu-table-one .wu-operate > span {
  margin-left: 12px;
}
.wu-tableWrap .wu-table-one .icon-a-help-circle1x {
  font-size: 16px;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.6);
  margin-left: 4px;
}
.wu-tableWrap .wu-table-one thead tr {
  background: rgba(0, 0, 0, 0.04);
}
.wu-tableWrap .wu-table-one thead tr.wu-active {
  background: #fff;
}
.wu-tableWrap .wu-table-one thead tr.wu-active .wu-header-StickyOperate {
  background: #fff;
}
.wu-tableWrap .wu-table-one thead tr th {
  border-right: none;
  padding: 8px 12px;
  text-align: left;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.9);
  font-size: 12px;
}
.wu-tableWrap .wu-table-one thead tr th.wu-header-StickyOperate {
  position: sticky;
  right: 0;
  background-color: #f5f5f5;
}
.wu-tableWrap .wu-table-one thead tr th.wu-header-StickyOperate::after {
  position: absolute;
  content: "";
  height: 100%;
  background-color: red;
  left: 0;
  display: block;
  top: 0;
  width: 8px;
  opacity: 1;
  left: -8px;
  background: linear-gradient(270deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0) 100%);
}
.wu-tableWrap .wu-table-one thead tr th:first-child {
  border-top-left-radius: 6px;
}
.wu-tableWrap .wu-table-one thead tr th:last-child {
  border-top-right-radius: 6px;
}
.wu-tableWrap .wu-table-one tbody tr.wu-active {
  background: rgba(8, 136, 255, 0.08);
}
.wu-tableWrap .wu-table-one tbody tr.wu-active .wu-content-StickyOperate {
  background: rgba(8, 136, 255, 0.08);
}
.wu-tableWrap .wu-table-one tbody tr.active {
  background: rgba(8, 136, 255, 0.08);
}
.wu-tableWrap .wu-table-one tbody tr.active .wu-content-StickyOperate {
  background: rgba(8, 136, 255, 0.08);
}
.wu-tableWrap .wu-table-one tbody tr td {
  border-right: none;
  border-top: 1px solid #e2e2e2;
  padding: 12px;
}
.wu-tableWrap .wu-table-one tbody tr td.wu-content-StickyOperate {
  position: sticky;
  right: 0;
  background-color: #fff;
}
.wu-tableWrap .wu-table-one tbody tr td.wu-content-StickyOperate::after {
  position: absolute;
  content: "";
  height: 100%;
  background-color: red;
  left: 0;
  display: block;
  top: 0;
  width: 8px;
  opacity: 1;
  left: -8px;
  background: linear-gradient(270deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0) 100%);
}
.wu-tableWrap .wu-table-one tbody tr:last-child td:first-child {
  border-bottom-left-radius: 8px;
}
.wu-tableWrap .wu-table-one tbody tr:last-child td:last-child {
  border-bottom-right-radius: 8px;
}
.wu-tableWrap .wu-table-one tbody tr:hover {
  background: rgba(0, 0, 0, 0.04);
}
.wu-tableWrap .wu-table-one tbody tr:hover .wu-content-StickyOperate {
  background: #f5f5f5;
}
.wu-tableWrap .wu-table-one .tableNoDataShow {
  padding-bottom: unset;
  padding: 12px;
}
.wu-tableWrap .wu-table-one .tableNoDataShow img {
  display: none;
}
.wu-tableWrap .wu-table-one .tableNoDataShow .tableNoDataShow-title {
  text-align: center;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.4);
}
.wu-tableWrap .wu-table-one .tableNoDataShow .tableNoDataShow-title:before {
  width: 100px;
  height: 100px;
  display: block;
  content: "";
  background: url(/Content/images/data-status-2025-2-13.png);
  background-size: 800px 300px;
  background-position: -200px -200px;
}
.wu-table {
  border-left: none;
  width: 100%;
}
.wu-table .icon-a-help-circle1x {
  font-size: 16px;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.6);
  margin-left: 4px;
}
.wu-table.wu-one {
  border: 1px solid rgba(0, 0, 0, 0.09);
  border-radius: 8px;
}
.wu-table thead tr {
  background: rgba(0, 0, 0, 0.04);
}
.wu-table thead tr.wu-active {
  background: #fff;
}
.wu-table thead tr.wu-active .wu-header-StickyOperate {
  background: #fff;
}
.wu-table thead tr th {
  border-right: none;
  border-top: 1px solid #e2e2e2;
  border-bottom: 1px solid #e2e2e2;
  padding: 8px;
  text-align: left;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.6);
  font-size: 14px;
}
.wu-table thead tr th.wu-header-StickyOperate {
  position: sticky;
  right: 0;
  background-color: #f5f5f5;
}
.wu-table thead tr th.wu-header-StickyOperate::after {
  position: absolute;
  content: "";
  height: 100%;
  background-color: red;
  left: 0;
  display: block;
  top: 0;
  width: 8px;
  opacity: 1;
  left: -8px;
  background: linear-gradient(270deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0) 100%);
}
.wu-table thead tr th:first-child {
  padding-left: 16px;
}
.wu-table thead tr th:last-child {
  padding-right: 16px;
  text-align: right;
}
.wu-table tbody tr td {
  border-right: none;
  border-top: 1px solid #e2e2e2;
  border-bottom: 1px solid #e2e2e2;
  padding: 8px;
}
.wu-table tbody tr td.wu-content-StickyOperate {
  position: sticky;
  right: 0;
  background-color: #fff;
}
.wu-table tbody tr td.wu-content-StickyOperate::after {
  position: absolute;
  content: "";
  height: 100%;
  background-color: red;
  left: 0;
  display: block;
  top: 0;
  width: 8px;
  opacity: 1;
  left: -8px;
  background: linear-gradient(270deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0) 100%);
}
.wu-table tbody tr td:first-child {
  padding-left: 16px;
}
.wu-table tbody tr td:last-child {
  padding-right: 16px;
  text-align: right;
}
.wu-table tbody tr.wu-active {
  background: rgba(0, 0, 0, 0.04);
}
.wu-table tbody tr.wu-active .wu-content-StickyOperate {
  background: unset;
  background: #f5f5f5;
}
.wu-table tbody tr:hover {
  background: rgba(0, 0, 0, 0.09);
}
.wu-table tbody tr:hover .wu-content-StickyOperate {
  background: #f5f5f5;
}
.stickyWrap {
  width: 100%;
  overflow-x: auto;
}
.stickyWrap .wu-table {
  overflow-x: auto;
}
.wu-tabNav {
  width: 100%;
  white-space: nowrap;
  font-size: 0;
  transition: all .2s;
  padding: 0 16px;
  position: relative;
  border-bottom: 1px solid rgba(0, 0, 0, 0.09);
  display: inline-flex;
  color: rgba(0, 0, 0, 0.6);
  box-sizing: border-box;
}
.wu-tabNav .wu-tabNav-li,
.wu-tabNav > li {
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  text-align: center;
  cursor: pointer;
  font-size: 14px;
  box-sizing: border-box;
  margin-right: 8px;
}
.wu-tabNav .wu-tabNav-li.wu-active,
.wu-tabNav > li.wu-active,
.wu-tabNav .wu-tabNav-li.active,
.wu-tabNav > li.active {
  color: #0888ff;
}
.wu-tabNav .wu-tabNav-li.wu-active::after,
.wu-tabNav > li.wu-active::after,
.wu-tabNav .wu-tabNav-li.active::after,
.wu-tabNav > li.active::after {
  content: "";
  width: 100%;
  height: 2px;
  position: absolute;
  display: block;
  left: 0;
  bottom: 0;
  background-color: #0888ff;
}
.wu-tabNav .wu-tabNav-li .wu-tabNav-li-span,
.wu-tabNav > li .wu-tabNav-li-span,
.wu-tabNav .wu-tabNav-li > span,
.wu-tabNav > li > span {
  padding: 18px 12px;
  border-radius: 8px;
}
.wu-tabNav .wu-tabNav-li .wu-tabNav-li-span:hover,
.wu-tabNav > li .wu-tabNav-li-span:hover,
.wu-tabNav .wu-tabNav-li > span:hover,
.wu-tabNav > li > span:hover {
  color: rgba(8, 136, 255, 0.8);
}
.wu-tabNav .wu-tabNav-li .wu-tabNav-li-span:active,
.wu-tabNav > li .wu-tabNav-li-span:active,
.wu-tabNav .wu-tabNav-li > span:active,
.wu-tabNav > li > span:active {
  color: #0676ed;
}
.wu-tabNav .wu-tabNav-li.disabled,
.wu-tabNav > li.disabled {
  color: rgba(0, 0, 0, 0.3);
  cursor: not-allowed;
}
.wu-tabNav .wu-tabNav-li.disabled:hover .wu-tabNav-li-span,
.wu-tabNav > li.disabled:hover .wu-tabNav-li-span {
  color: rgba(0, 0, 0, 0.3);
}
.wu-tabNav-two {
  border-bottom: unset;
  padding: 0;
  display: inline-flex;
}
.wu-tabNav-two .wu-tabNav-li,
.wu-tabNav-two > li {
  cursor: pointer;
  padding: 6px 8px;
  background: rgba(0, 0, 0, 0.04);
  border-radius: 8px;
  margin: 0;
  margin-right: 8px;
  height: 32px;
  box-sizing: border-box;
}
.wu-tabNav-two .wu-tabNav-li:last-child,
.wu-tabNav-two > li:last-child {
  margin-right: 0;
}
.wu-tabNav-two .wu-tabNav-li.wu-active,
.wu-tabNav-two > li.wu-active,
.wu-tabNav-two .wu-tabNav-li.layui-this,
.wu-tabNav-two > li.layui-this {
  color: #0888ff;
  background: rgba(8, 136, 255, 0.08);
}
.wu-tabNav-two .wu-tabNav-li.wu-active::after,
.wu-tabNav-two > li.wu-active::after,
.wu-tabNav-two .wu-tabNav-li.layui-this::after,
.wu-tabNav-two > li.layui-this::after {
  display: none;
}
.wu-tabNav-two .wu-tabNav-li .wu-tabNav-li-span,
.wu-tabNav-two > li .wu-tabNav-li-span {
  border-radius: 8px;
}
.wu-tabNav-two .wu-tabNav-li .wu-tabNav-li-span:active,
.wu-tabNav-two > li .wu-tabNav-li-span:active {
  color: #0676ed;
}
.wu-tabNav-two .wu-tabNav-li:hover,
.wu-tabNav-two > li:hover {
  background: rgba(8, 136, 255, 0.08);
  color: rgba(8, 136, 255, 0.8);
}
.wu-tabNav-two .wu-tabNav-li.disabled,
.wu-tabNav-two > li.disabled {
  cursor: not-allowed;
  background: rgba(0, 0, 0, 0.04);
  color: rgba(0, 0, 0, 0.3);
}
.wu-tabNav-two .wu-tabNav-li.disabled:active .wu-tabNav-li-span,
.wu-tabNav-two > li.disabled:active .wu-tabNav-li-span {
  color: rgba(0, 0, 0, 0.3);
}
.wu-tabNav-three {
  border: 1px solid rgba(0, 0, 0, 0.09);
  display: inline-flex;
  box-sizing: border-box;
  border-bottom-left-radius: 8px;
  border-top-left-radius: 8px;
  border-bottom-right-radius: 8px;
  border-top-right-radius: 8px;
}
.wu-tabNav-three .wu-tabNav-li {
  height: 32px;
  padding: 6px 12px;
  cursor: pointer;
  border-right: 1px solid rgba(0, 0, 0, 0.09);
  box-sizing: border-box;
}
.wu-tabNav-three .wu-tabNav-li:last-child {
  border-right: unset;
}
.wu-tabNav-three .wu-tabNav-li.wu-active {
  color: #0888ff;
}
.wu-tabNav-three .wu-tabNav-li:hover {
  color: rgba(8, 136, 255, 0.8);
}
.wu-tabNav-three .wu-tabNav-li.disabled {
  cursor: not-allowed;
  color: rgba(0, 0, 0, 0.3);
}
.wu-tabNav-three .wu-tabNav-li.disabled.wu-active {
  color: rgba(0, 0, 0, 0.3);
}
.wu-tabNav-three .wu-tabNav-li.disabled:hover {
  color: rgba(0, 0, 0, 0.3);
}
.wu-layui-tab {
  margin: 0;
}
.wu-layui-tab .layui-tab-title {
  height: 56px;
  padding: 0 16px;
  box-sizing: border-box;
}
.wu-layui-tab .layui-tab-title li {
  line-height: 56px;
  padding: 0 12px;
  min-width: unset;
  font-size: 14px;
  margin-right: 8px;
  color: rgba(0, 0, 0, 0.9);
}
.wu-layui-tab .layui-tab-title li:hover {
  color: rgba(8, 136, 255, 0.8);
}
.wu-layui-tab .layui-tab-title .layui-this {
  color: #0888ff !important;
  position: relative;
  font-weight: 600;
}
.wu-layui-tab .layui-tab-title .layui-this:after {
  content: "";
  width: 100%;
  height: 2px;
  position: absolute;
  display: block;
  left: 0;
  bottom: 0;
  background-color: #0888ff;
  top: unset;
  border-width: unset;
  border-style: unset;
  border-radius: unset;
  box-sizing: unset;
  pointer-events: unset;
}
.wu-tag {
  padding: 0 2px;
  line-height: 16px;
  background: #f5f5f5;
  font-size: 12px;
  font-weight: normal;
  color: rgba(0, 0, 0, 0.9);
  display: inline-flex;
  align-items: center;
  text-align: center;
  box-sizing: border-box;
  border-radius: 2px;
}
.wu-tag .iconfont {
  font-size: 14px;
}
.wu-tag.wu-dot {
  padding: 0 1px;
}
.wu-tag.wu-default.wu-dot {
  border: 0.5px solid rgba(66, 74, 87, 0.2);
}
.wu-tag.wu-default-dev {
  background-color: rgba(0, 0, 0, 0.9);
  color: #fff;
}
.wu-tag.wu-default-dev.wu-dot {
  background-color: #fff;
  color: rgba(0, 0, 0, 0.9);
  border: 0.5px solid rgba(66, 74, 87, 0.2);
}
.wu-tag.wu-processing {
  background-color: rgba(8, 136, 255, 0.08);
  color: #0888ff;
}
.wu-tag.wu-processing.wu-dot {
  border: 0.5px solid rgba(8, 136, 255, 0.2);
}
.wu-tag.wu-processing-dev {
  background-color: #0888ff;
  color: #fff;
}
.wu-tag.wu-processing-dev.wu-dot {
  border: 0.5px solid rgba(8, 136, 255, 0.2);
  background-color: #fff;
  color: #0888ff;
}
.wu-tag.wu-error {
  background-color: rgba(234, 87, 46, 0.1);
  color: #ea572e;
}
.wu-tag.wu-error .wu-badge-gan {
  font-size: 700;
  color: rgba(234, 87, 46, 0.2);
}
.wu-tag.wu-error.wu-dot {
  border: 0.5px solid rgba(234, 87, 46, 0.2);
}
.wu-tag.wu-error-dev {
  background-color: #ea572e;
  color: #fff;
}
.wu-tag.wu-error-dev.wu-dot {
  border: 0.5px solid rgba(234, 87, 46, 0.2);
  background-color: #fff;
  color: #ea572e;
}
.wu-tag.wu-warning {
  background-color: rgba(241, 140, 3, 0.1);
  color: #f18c03;
}
.wu-tag.wu-warning.wu-dot {
  border: 0.5px solid rgba(241, 140, 3, 0.2);
}
.wu-tag.wu-warning-dev {
  background-color: #f18c03;
  color: #fff;
}
.wu-tag.wu-warning-dev.wu-dot {
  border: 0.5px solid rgba(241, 140, 3, 0.2);
  background-color: #fff;
  color: #f18c03;
}
.wu-tag.wu-success {
  background-color: rgba(115, 172, 31, 0.1);
  color: #73ac1f;
}
.wu-tag.wu-success.wu-dot {
  border: 0.5px solid rgba(115, 172, 31, 0.2);
}
.wu-tag.wu-success-dev {
  background-color: #73ac1f;
  color: #fff;
}
.wu-tag.wu-success-dev.wu-dot {
  border: 0.5px solid rgba(115, 172, 31, 0.2);
  background-color: #fff;
  color: #73ac1f;
}
.wu-badge {
  display: inline-flex;
  align-items: center;
  color: rgba(0, 0, 0, 0.9);
  font-size: 12px;
}
.wu-badge .wu-badge-dot {
  width: 6px;
  height: 6px;
  display: inline-block;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.3);
  margin-right: 4px;
}
.wu-badge.wu-processing .wu-badge-dot {
  background-color: #0888ff;
}
.wu-badge.wu-error .wu-badge-gan {
  font-weight: 700;
  color: #ea572e;
  font-size: 14px;
}
.wu-badge.wu-error .wu-badge-dot {
  background-color: #ea572e;
}
.wu-badge.wu-warning .wu-badge-dot {
  background-color: #f18c03;
}
.wu-badge.wu-success .wu-badge-dot {
  background-color: #73ac1f;
}
.wu-alert {
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 14px;
  display: flex;
  align-items: flex-start;
  position: relative;
  box-sizing: border-box;
}
.wu-alert .iconfont {
  display: inline-block;
  margin-right: 4px;
  position: relative;
  top: 2px;
}
.wu-alert .wu-alert-title {
  display: flex;
  align-items: center;
  color: rgba(0, 0, 0, 0.9);
}
.wu-alert.wu-processing {
  background-color: rgba(8, 136, 255, 0.08);
  color: #0888ff;
  border: 0.5px solid rgba(8, 136, 255, 0.2);
}
.wu-alert.wu-error {
  background-color: rgba(234, 87, 46, 0.1);
  color: #ea572e;
  border: 0.5px solid rgba(234, 87, 46, 0.2);
}
.wu-alert.wu-warning {
  background-color: rgba(241, 140, 3, 0.1);
  color: #f18c03;
  border: 0.5px solid rgba(241, 140, 3, 0.2);
}
.wu-alert.wu-success {
  background-color: rgba(115, 172, 31, 0.1);
  color: #73ac1f;
  border: 0.5px solid rgba(115, 172, 31, 0.2);
}
.wu-alert .icon-a-close1x {
  position: absolute;
  right: 10px;
  top: 10px;
  color: rgba(0, 0, 0, 0.6);
  cursor: pointer;
}
.wu-toast {
  background-color: #fff;
  max-width: 500px;
  font-size: 14px;
  display: inline-flex;
  align-items: flex-start;
  position: relative;
  box-sizing: border-box;
  padding: 12px 16px;
  border: 0.5px solid rgba(0, 0, 0, 0.09);
  border-radius: 6px;
  box-shadow: 0px 6px 30px 5px rgba(0, 0, 0, 0.05), 0px 16px 24px 2px rgba(0, 0, 0, 0.04), 0px 8px 10px -5px rgba(0, 0, 0, 0.08);
}
.wu-toast .iconfont {
  display: inline-block;
  margin-right: 4px;
  position: relative;
  top: 2px;
}
.wu-toast .wu-toast-title {
  display: flex;
  align-items: center;
  color: rgba(0, 0, 0, 0.9);
  max-width: 500px;
  background-color: #fff;
}
.wu-toast.wu-processing {
  color: #0888ff;
}
.wu-toast.wu-error {
  color: #ea572e;
}
.wu-toast.wu-warning {
  color: #f18c03;
}
.wu-toast.wu-success {
  color: #73ac1f;
}
.wu-toast .icon-a-close1x {
  position: absolute;
  right: 10px;
  top: 10px;
  color: rgba(0, 0, 0, 0.6);
  cursor: pointer;
}
.wu-msg {
  font-size: 12px;
  position: fixed;
  z-index: 105000000000000;
  width: 100%;
  top: 0;
  display: inline-flex;
  justify-content: center;
  animation: wu_msgAni 0.5s ease-in-out 0s 1 alternate forwards;
  pointer-events: none;
  opacity: 0;
}
.wu-msg .wu-alert-title {
  max-width: 800px;
}
@keyframes wu_msgAni {
  0% {
    top: 0;
    opacity: 0;
  }
  100% {
    top: 80px;
    opacity: 1;
  }
}
.wu-pop {
  border-radius: 8px;
  opacity: 1;
  /* 中性色/White */
  background: #FFFFFF;
  box-sizing: border-box;
  /* 中性色/Gray2 */
  border: 0.5px solid rgba(0, 0, 0, 0.09);
  max-width: 250px;
  display: flex;
  flex-direction: column;
  padding: 8px 12px;
  align-self: stretch;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  font-size: 12px;
  color: rgba(0, 0, 0, 0.9);
  position: relative;
  align-items: center;
}
.wu-pop .wu-pop-title {
  white-space: pre-wrap;
  text-align: left;
}
.wu-pop::after {
  width: 100%;
  height: 10px;
  content: "";
  display: block;
  position: absolute;
  left: 0;
}
.wu-pop .wu-pop-icon {
  border: 1px solid rgba(0, 0, 0, 0.09);
  width: 9px;
  height: 9px;
  display: inline-block;
  transform: rotate(45deg);
  background-color: #fff;
  position: absolute;
}
.wu-pop.wu-leftTop::after {
  top: -10px;
}
.wu-pop.wu-leftTop .wu-pop-icon {
  border-bottom: unset;
  border-right: unset;
  top: -6px;
  left: 15px;
}
.wu-pop.wu-leftBottom::after {
  top: unset;
  bottom: -10px;
}
.wu-pop.wu-leftBottom .wu-pop-icon {
  border-top: unset;
  border-left: unset;
  bottom: -6px;
  left: 15px;
}
.wu-pop.wu-midTop::after {
  top: -10px;
}
.wu-pop.wu-midTop .wu-pop-icon {
  border-bottom: unset;
  border-right: unset;
  top: -6px;
  left: unset;
}
.wu-pop.wu-midBottom .wu-pop-icon {
  border-top: unset;
  border-left: unset;
  bottom: -6px;
  left: unset;
}
.wu-pop.wu-midBottom::after {
  bottom: -10px;
}
.wu-pop.wu-rightTop::after {
  top: -10px;
}
.wu-pop.wu-rightTop .wu-pop-icon {
  border-bottom: unset;
  border-right: unset;
  top: -6px;
  right: 15px;
}
.wu-pop.wu-rightBottom::after {
  bottom: -10px;
}
.wu-pop.wu-rightBottom .wu-pop-icon {
  border-top: unset;
  border-left: unset;
  bottom: -6px;
  right: 15px;
}
.wu-tooltip .wu-pop {
  position: absolute;
  width: 250px;
  left: 0;
  display: none;
  animation: wu_tooltip 0.3s ease-in-out 0s 1 alternate forwards;
  opacity: 0;
  z-index: 10;
}
.wu-tooltip:hover .wu-pop {
  display: block;
}
.wu-tooltip .wu-midTop {
  left: unset;
}
.wu-tooltip .wu-midBottom {
  left: unset;
}
.wu-tooltip .wu-rightTop {
  left: unset;
  right: 0;
}
.wu-tooltip .wu-rightBottom {
  left: unset;
  right: 0;
}
@keyframes wu_tooltip {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
.wu-dailog.layui-layer {
  border-radius: 10px;
}
.wu-dailog .layui-layer-title {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.9);
  padding-left: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.09);
  background-color: #fff;
  padding: 12px 16px;
  box-sizing: border-box;
  height: unset;
  line-height: unset;
}
.wu-dailog.wu-modal-skin .layui-layer-content {
  padding: 24px 16px 0 16px;
}
.wu-dailog .layui-layer-content {
  padding: 16px;
  max-height: 450px;
}
.wu-dailog .layui-layer-content.layui-layer-padding {
  padding: 20px 20px 20px 55px;
}
.wu-dailog .layui-layer-btn {
  padding: 0 16px 12px;
}
.wu-dailog .layui-layer-btn a {
  border-radius: 6px;
  height: 32px;
  line-height: 30px;
  margin-top: 0;
  box-sizing: border-box;
  padding: 0 12px;
  font-family: 微软雅黑;
  font-size: 14px;
}
.wu-dailog .layui-layer-btn a:nth-child(1) {
  color: #fff;
  background: #0888ff;
  border: 1px solid #0888ff;
}
.wu-dailog .layui-layer-btn a:nth-child(1):active {
  background: #0676ed;
  border: 1px solid #0676ed;
  opacity: 1;
}
.wu-dailog .layui-layer-btn a:last-child {
  margin-right: 0;
}
.wu-dailog .layui-layer-btn a:last-child:hover {
  /*                    background-color: @A1Color;*/
  border-color: rgba(8, 136, 255, 0.8);
  color: rgba(8, 136, 255, 0.8);
}
.wu-dailog .layui-layer-btn a:last-child:active {
  border-color: #0676ed;
  color: #0676ed;
}
.wu-dailog .layui-layer-btn a.layui-layer-btn0:hover {
  background: rgba(8, 136, 255, 0.8) !important;
  border: 1px solid rgba(8, 136, 255, 0.8) !important;
  color: #fff !important;
}
.wu-dailog.wu-warn .layui-layer-btn a.layui-layer-btn1 {
  border: 1px solid #dedede;
  background-color: #fff;
  color: #333;
}
.wu-dailog.wu-warn .layui-layer-btn a.layui-layer-btn1:hover {
  background-color: rgba(8, 136, 255, 0.08);
  border-color: rgba(8, 136, 255, 0.3);
  color: #0676ed;
}
.wu-dailog.wu-warn .layui-layer-btn a:first-child {
  background: #ea572e !important;
  border: 1px solid #ea572e !important;
  color: #fff !important;
}
.wu-dailog .layui-layer-setwin .layui-layer-close2 {
  right: 0;
  top: 0;
  background-position: 1px -40px;
  width: 0;
  height: 0;
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
}
.wu-dailog .layui-layer-setwin .layui-layer-close2::before {
  display: block;
  content: "\e74b";
  left: -12px;
  top: -7px;
  position: absolute;
  font-size: 20px;
}
.wu-modal {
  display: flex;
}
.wu-modal .wu-modal-title {
  color: rgba(0, 0, 0, 0.9);
  font-size: 14px;
}
.wu-modal .wu-modal-icon {
  font-size: 16px;
  margin-right: 8px;
}
.wu-modal .wu-modal-content {
  display: flex;
  flex-direction: column;
}
.wu-modal .wu-modal-content .wu-modal-title {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.9);
}
.wu-modal .wu-modal-content .wu-modal-main {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.6);
  margin-top: 8px;
}
.wu-modal .wu-modal-icon {
  margin-top: 2px;
}
.wu-modal .wu-modal-icon.icon-a-info-circle-filled1x {
  color: #0888ff;
}
.wu-modal .wu-modal-icon.icon-a-error-circle-filled1x {
  color: #ea572e;
}
.wu-modal .wu-modal-icon.icon-a-check-circle-filled1x {
  color: #73ac1f;
}
.wu-tip {
  border-radius: 8px;
  background-color: #f5f5f5;
}
.wu-container {
  display: flex;
  margin: 16px 16px 0 16px;
  background-color: #fff;
  border-radius: 8px;
  flex-direction: column;
  min-width: 1200px;
  padding: 16px;
  box-sizing: border-box;
  font-size: 14px;
  box-shadow: unset;
}
.wu-container.wu-toFull {
  margin: 0 !important;
  padding: 0 !important;
  border-radius: 0;
}
.wu-container.wu-two {
  min-width: 888px;
}
.wu-container.wu-three {
  min-width: 560px;
}
.wu-searchWrap {
  display: flex;
  padding: 16px 16px 8px 16px;
  box-sizing: border-box;
  width: 100%;
  flex-wrap: wrap;
  background-color: unset;
}
.wu-searchWrap .wu-layui-select {
  width: 100%;
}
.wu-searchWrap > * {
  margin-bottom: 8px;
}
.wu-searchWrap .wu-searchWrap-item {
  min-width: 226px;
  width: 20%;
  padding-right: 8px;
  box-sizing: border-box;
}
.wu-searchWrap .wu-searchWrap-item .wu-inputWrap {
  width: 100%;
}
.wu-searchWrap .wu-searchWrap-item .selectWrap {
  display: block;
}
.wu-searchWrap .wu-searchWrap-item .layui-form-select dl {
  top: unset;
  bottom: auto;
}
.wu-searchWrap .wu-searchWrap-contactTime {
  width: 40%;
  min-width: 452px;
  box-sizing: border-box;
  padding-right: 8px;
}
.wu-searchWrap .wu-searchWrap-contactTime .wu-timeWrap {
  display: flex;
}
.wu-searchWrap .wu-searchWrap-contactTime .newinputSelectTime {
  width: unset;
  flex: 1;
}
.wu-searchWrap:not(.wu-searchWrap-contactTime) .wu-time {
  width: 100%;
}
.wu-searchWrap:not(.wu-searchWrap-contactTime) .wu-time .newinputSelectTime {
  width: unset !important;
  flex: 1;
}
.wu-searchWrap .wu-selectWrap {
  width: 100%;
}
.wu-timeWrap {
  display: inline-flex;
  align-items: center;
  position: relative;
}
.wu-timeWrap .newinputSelectTime .newwrapperTime_input i {
  font-size: 12px;
}
.wu-timeWrap:hover .wu-selectWrap .wu-select {
  border-color: #0888ff;
}
.wu-timeWrap:hover .wu-selectWrap .wu-timeWrap-left {
  border-color: #0888ff !important;
}
.wu-timeWrap:hover .newinputSelectTime {
  border-color: #0888ff;
}
.wu-timeWrap .wu-selectWrap .wu-select {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.wu-timeWrap .wu-selectWrap .wu-select:focus {
  box-shadow: unset;
}
.wu-timeWrap .wu-selectWrap .wu-select option {
  background-color: #fff;
}
.wu-timeWrap .wu-selectWrap .icon-a-chevron-down1x {
  background-color: unset;
}
.wu-timeWrap .wu-selectWrap.wu-form-mid .wu-timeWrap-left {
  width: 100%;
  height: 32px;
  border-radius: 6px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border: 1px solid rgba(0, 0, 0, 0.14);
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding: 0 8px;
  color: rgba(0, 0, 0, 0.9);
}
.wu-timeWrap .newinputSelectTime {
  margin-left: -1px;
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
  border-color: rgba(0, 0, 0, 0.14);
  border-left: unset;
  height: 32px !important;
}
.wu-timeWrap .newinputSelectTime .newwrapperTime {
  margin-top: 1px;
  animation: myTimeAnimation 0.3s ease-in-out 0s 1 alternate forwards;
}
.wu-timeWrap .newinputSelectTime .newwrapperTime_input {
  padding-left: 8px;
  color: rgba(0, 0, 0, 0.9);
}
.wu-timeWrap .newinputSelectTime .newwrapperTime_input i {
  color: rgba(0, 0, 0, 0.9);
}
.wu-timeWrap .icon-a-calendar1x {
  position: absolute;
  right: 8px;
  color: rgba(0, 0, 0, 0.4);
  pointer-events: none;
}
.wu-time {
  position: relative;
  display: inline-flex;
  align-items: center;
}
.wu-time .newinputSelectTime {
  border-radius: 6px;
}
.wu-time .newinputSelectTime .newwrapperTime_input {
  padding-left: 8px;
}
.wu-time .newinputSelectTime .newwrapperTime_input i {
  color: rgba(0, 0, 0, 0.9);
  font-size: 14px;
}
.wu-time .newinputSelectTime:hover {
  border-color: #0888ff;
}
.wu-time .newinputSelectTime .newwrapperTime {
  margin-top: 1px;
  animation: myTimeAnimation 0.3s ease-in-out 0s 1 alternate forwards;
}
.wu-time .icon-a-calendar1x {
  position: absolute;
  right: 8px;
  color: rgba(0, 0, 0, 0.4);
  pointer-events: none;
}
.wu-skin .calender-cell.active,
.wu-skin .calender-year-cell.active,
.wu-skin .calender-mon-cell.active {
  background: #0888ff !important;
}
.wu-skin .newcalender-footer-button {
  border-radius: 8px;
  height: 32px;
  font-size: 14px;
  padding: 6px 12px;
  box-sizing: border-box;
  display: inline-flex;
  align-items: center;
  position: relative;
  top: -10px;
}
.wu-skin .newcalender-footer-button:hover {
  border: 1px solid rgba(8, 136, 255, 0.3);
  color: #0888ff;
  background-color: rgba(8, 136, 255, 0.1);
}
@keyframes myTimeAnimation {
  0% {
    opacity: 0;
    margin-top: 15px;
  }
  100% {
    opacity: 1;
    margin-top: 1px;
  }
}
.wu-quickWrap {
  width: 100%;
}
.wu-operateWrap {
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.wu-shadow {
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.09), 0px 0px 8px 0px rgba(0, 0, 0, 0.04), inset 0px 0px 0px 0.5px rgba(0, 0, 0, 0.09);
}
.wu-shadow-one {
  box-shadow: 0px 0px 16px 0px rgba(0, 0, 0, 0.09), inset 0px 0px 0px 0.5px rgba(0, 0, 0, 0.09);
}
.wu-shadow-two {
  box-shadow: 0px 0px 24px 0px rgba(0, 0, 0, 0.09), inset 0px 0px 0px 0.5px rgba(0, 0, 0, 0.09);
}
.wu-shadow-three {
  box-shadow: 0px 0px 0px 2px rgba(8, 136, 255, 0.2);
}
.wu-shadow-down {
  box-shadow: 0px 0.5px 0px 0px rgba(0, 0, 0, 0.09), 0px 4px 8px 0px rgba(0, 0, 0, 0.04);
}
.wu-shadow-right {
  box-shadow: 0.5px 0px 0px 0px rgba(0, 0, 0, 0.09), 4px 0px 8px 0px rgba(0, 0, 0, 0.04);
}
.wu-shadow-up {
  box-shadow: 0px -0.5px 0px 0px rgba(0, 0, 0, 0.09), 0px -4px 8px 0px rgba(0, 0, 0, 0.04);
}
.wu-shadow-left {
  box-shadow: -0.5px 0px 0px 0px rgba(0, 0, 0, 0.09), -4px 0px 8px 0px rgba(0, 0, 0, 0.04);
}
.wu-page.layui-laypage.wu-one a,
.wu-page.layui-laypage.wu-one span {
  display: inline-block;
}
.wu-page.layui-laypage.wu-one .layui-laypage-limits {
  margin-right: 0;
}
.wu-page.layui-laypage.wu-two .layui-laypage-skip {
  display: none;
}
.wu-page.layui-laypage.wu-two .layui-laypage-limits {
  margin-right: 0;
}
.wu-page.layui-laypage.wu-three .layui-laypage-skip {
  display: none;
}
.wu-page.layui-laypage.wu-three .layui-laypage-limits {
  display: none;
}
.wu-page.layui-laypage .layui-laypage-curr .layui-laypage-em {
  background-color: #0888ff;
}
.wu-page.layui-laypage a:hover {
  color: #0888ff !important;
  border-color: #0888ff !important;
  position: relative;
  z-index: 100;
}
.wu-page.layui-laypage a:hover:active {
  color: #0676ed !important;
  border: 1px solid #0676ed !important;
}
.wu-page.layui-laypage a:hover.layui-disabled {
  border-color: #e2e2e2 !important;
  color: #d2d2d2 !important;
}
.wu-page.layui-laypage .n-hidePages span {
  display: none;
}
.wu-page.layui-laypage span {
  height: 32px;
  line-height: 30px;
  box-sizing: border-box;
  display: none;
  padding: 0 12px;
  font-size: 14px;
}
.wu-page.layui-laypage span.layui-laypage-limits {
  display: inline-block;
  padding: 0;
  padding-right: 0;
  border-left: unset !important;
  margin-left: 8px;
  margin-right: 8px;
}
.wu-page.layui-laypage a {
  height: 32px;
  line-height: 30px;
  box-sizing: border-box;
  display: none;
  padding: 0 12px;
  font-size: 14px;
}
.wu-page.layui-laypage a.layui-laypage-prev {
  display: inline-block;
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}
.wu-page.layui-laypage a.layui-laypage-prev:hover {
  position: relative;
  z-index: 10;
}
.wu-page.layui-laypage a.layui-laypage-next {
  display: inline-block;
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
  position: relative;
}
.wu-page.layui-laypage .layui-laypage-count {
  display: inline-block;
  margin-right: 8px;
  padding: 0;
}
.wu-page.layui-laypage span.layui-laypage-limits select {
  border-radius: 6px;
  height: 32px;
  box-sizing: border-box;
}
.wu-page.layui-laypage span.layui-laypage-skip {
  display: inline-block;
  padding: 0;
  margin-left: 8px;
}
.wu-page.layui-laypage span.layui-laypage-skip > input {
  border-radius: 6px;
  height: 32px;
  font-size: 14px;
}
.wu-page.layui-laypage .layui-laypage-count {
  border: unset;
}
.wu-page.layui-laypage button {
  border-radius: 6px;
  height: 32px;
  margin-left: 16px;
}
.wu-page.layui-laypage button:hover {
  border: 1px solid #0888ff;
  color: #0888ff;
}
.wu-page.layui-laypage button:active {
  color: #0676ed;
  border: 1px solid #0676ed;
}
.wu-page.layui-laypage input:hover {
  border-color: #0888ff !important;
}
.wu-page.layui-laypage input:focus {
  border: 1px solid #0888ff !important;
  box-shadow: 0px 0px 0px 2px rgba(8, 136, 255, 0.2);
}
.wu-page.layui-laypage select:hover {
  border-color: #0888ff !important;
}
.wu-page.layui-laypage select:focus {
  border: 1px solid #0888ff !important;
  box-shadow: 0px 0px 0px 2px rgba(8, 136, 255, 0.2);
}
.wu-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  color: #dddddd;
}
.wu-scrollbar::-webkit-scrollbar-thumb {
  border-radius: 12px;
  background: rgba(0, 0, 0, 0.14);
}
.wu-scrollbar::-webkit-scrollbar-track {
  border-radius: 12px;
  background: rgba(0, 0, 0, 0.09);
}
.wu-dropdown {
  position: relative;
  cursor: pointer;
}
.wu-dropdown:hover .icon-a-chevron-down1x {
  display: inline-block;
  transform: rotate(180deg);
  transition: 0.3s;
}
.wu-dropdown:hover .wu-dropdown-ul {
  display: block;
  animation: downToAnimation 0.2s ease-in-out 0s 1 alternate forwards;
}
.wu-dropdown.wu-mid .wu-dropdown-ul {
  left: unset;
}
.wu-dropdown.wu-right .wu-dropdown-ul {
  left: unset;
  right: 0;
}
.wu-dropdown .wu-dropdown-ul {
  left: 0;
  top: 33px;
  width: 226px;
  position: absolute;
  border-radius: 8px;
  opacity: 0;
  display: none;
  background: #FFFFFF;
  box-sizing: border-box;
  border: 0.5px solid rgba(0, 0, 0, 0.09);
  padding: 8px;
  box-shadow: 0px 0px 16px 0px rgba(0, 0, 0, 0.04), 0px 0px 8px 0px rgba(0, 0, 0, 0.04), 0px 0px 4px 0px rgba(0, 0, 0, 0.04);
}
.wu-dropdown .wu-dropdown-ul::after {
  position: absolute;
  width: 100%;
  height: 3px;
  top: -3px;
  content: "";
  display: block;
  left: 0;
}
.wu-dropdown .wu-dropdown-ul .wu-dropdown-ul-li {
  padding: 6px 8px;
  color: rgba(0, 0, 0, 0.9);
  display: flex;
  justify-content: flex-start;
  margin-bottom: 4px;
}
.wu-dropdown .wu-dropdown-ul .wu-dropdown-ul-li:last-child {
  margin-bottom: 0;
}
.wu-dropdown .wu-dropdown-ul .wu-dropdown-ul-li.wu-active {
  background: rgba(0, 0, 0, 0.04);
}
.wu-dropdown .wu-dropdown-ul .wu-dropdown-ul-li:hover {
  background: rgba(0, 0, 0, 0.04);
}
.wu-dropdown.wu-top:hover .wu-dropdown-ul {
  animation: upToAnimation 0.3s ease-in-out 0s 1 alternate forwards;
}
.wu-dropdown.wu-top:hover .wu-dropdown-ul::before {
  position: absolute;
  width: 100%;
  height: 3px;
  bottom: -3px;
  content: "";
  display: block;
  left: 0;
}
.wu-dropdown.wu-top.wu-mid {
  left: unset;
}
.wu-dropdown.wu-top.wu-right {
  right: 0;
}
@keyframes upToAnimation {
  0% {
    opacity: 0;
    bottom: 0;
  }
  100% {
    opacity: 1;
    top: auto;
    bottom: 100%;
    margin-bottom: 1px;
  }
}
@keyframes downToAnimation {
  0% {
    opacity: 0;
    top: 200%;
  }
  100% {
    opacity: 1;
    top: 100%;
    margin-top: 1px;
  }
}
@keyframes wu_dropdownAni {
  0% {
    top: 0;
    opacity: 0;
  }
  100% {
    top: 32px;
    opacity: 1;
  }
}
.wu-Breadcrumb {
  justify-content: space-between;
  height: 76px;
  /* 自动布局 */
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 22px 16px;
  gap: 24px;
  width: 100%;
  box-sizing: border-box;
}
.wu-productInfoWrap {
  display: flex;
  align-items: center;
  width: 100%;
}
.wu-productInfoWrap .wu-productInfoShow {
  display: flex;
  align-items: center;
  flex: 1;
}
.wu-productInfoWrap .wu-productInfoShow img {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  margin-right: 4px;
}
.wu-productInfoWrap .wu-productInfoShow .wu-productInfoShow-right {
  display: flex;
  flex-direction: column;
}
.wu-productInfoWrap .wu-productInfoShow .wu-productInfoShow-right .wu-productInfoShow-title {
  font-size: 14px;
  line-height: 20px;
  color: rgba(0, 0, 0, 0.9);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}
.wu-productInfoWrap .wu-productInfoShow .wu-productInfoShow-right .wu-productInfoShow-sku {
  color: rgba(0, 0, 0, 0.4);
  font-size: 12px;
}
.wu-productInfoWrap .wu-productInfoShow .wu-productInfoShow-right > span {
  margin-bottom: 4px;
}
.wu-productInfoWrap .wu-productInfoShow .wu-productInfoShow-right > span:last-child {
  margin-bottom: 0;
}
.layui-form .layui-form-switch {
  position: relative;
  height: 24px;
  line-height: 24px;
  min-width: 44px;
  padding: 0 7px 0 5px;
  border-radius: 20px;
  cursor: pointer;
  background-color: #fff;
  display: inline-block;
  -webkit-transition: .1slinear;
  transition: .1slinear;
  box-sizing: border-box;
  background-color: rgba(66, 74, 87, 0.1);
  border: unset;
}
.layui-form .layui-form-switch i {
  position: absolute;
  left: 2px;
  top: 2px;
  width: 20px;
  height: 20px;
  border-radius: 20px;
  background-color: #fff;
  -webkit-transition: .1slinear;
  transition: .1slinear;
}
.layui-form .layui-form-switch em {
  position: relative;
  top: 0;
  padding: 0 !important;
  text-align: center !important;
  color: rgba(0, 0, 0, 0.6) !important;
  font-style: normal !important;
  margin-left: 18px;
  font-size: 12px;
  margin-right: 1px;
}
.layui-form .layui-form-switch.layui-form-onswitch {
  border-color: #0888ff;
  background-color: #0888ff;
}
.layui-form .layui-form-switch.layui-form-onswitch i {
  left: 100%;
  margin-left: -22px;
  background-color: #fff;
}
.layui-form .layui-form-switch.layui-form-onswitch em {
  margin-left: 3px;
  margin-right: 16px;
  color: #fff !important;
}
.wu-steps .wu-step-inProgress {
  display: flex;
  width: 162px;
  height: 40px;
  margin-right: 32px;
}
.wu-steps .wu-step-inProgress .step-left {
  margin-right: 4px;
}
.wu-steps .wu-step-inProgress .step-left .step-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background-color: #0888ff;
  border-radius: 50%;
  color: rgba(255, 255, 255, 0.9);
}
.wu-steps .wu-step-inProgress .step-right .step-title {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.9);
}
.wu-steps .wu-step-inProgress .step-right .step-title::after {
  content: '';
  display: inline-block;
  width: 80px;
  height: 1px;
  margin-left: 16px;
  border-bottom: 1px dashed rgba(0, 0, 0, 0.09);
}
.wu-steps .wu-step-inProgress .step-right .step-content {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.4);
}
.wu-steps .wu-step-waiting {
  display: flex;
  width: 162px;
  height: 40px;
  margin-right: 32px;
}
.wu-steps .wu-step-waiting .step-left {
  margin-right: 4px;
}
.wu-steps .wu-step-waiting .step-left .step-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background-color: #f5f5f5;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.3);
}
.wu-steps .wu-step-waiting .step-right .step-title {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.4);
}
.wu-steps .wu-step-waiting .step-right .step-title::after {
  content: '';
  display: inline-block;
  width: 80px;
  height: 1px;
  margin-left: 16px;
  border-bottom: 1px dashed rgba(0, 0, 0, 0.09);
}
.wu-steps .wu-step-waiting .step-right .step-content {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.4);
}
.wu-steps .wu-step-finshed {
  display: flex;
  width: 162px;
  height: 40px;
  margin-right: 32px;
}
.wu-steps .wu-step-finshed .step-left {
  margin-right: 4px;
}
.wu-steps .wu-step-finshed .step-left .step-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 19px;
  height: 19px;
  background-color: #0888ff;
  border-radius: 50%;
  color: rgba(255, 255, 255, 0.9);
}
.wu-steps .wu-step-finshed .step-left .icon-dagou1 {
  font-size: 20px;
}
.wu-steps .wu-step-finshed .step-right .step-title {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.9);
}
.wu-steps .wu-step-finshed .step-right .step-title::after {
  content: '';
  display: inline-block;
  width: 80px;
  height: 1px;
  margin-left: 16px;
  border-bottom: 1px dashed rgba(0, 0, 0, 0.09);
}
.wu-steps .wu-step-finshed .step-right .step-content {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.4);
}
.wu-platformWrap .wu-tabNav > li {
  color: rgba(0, 0, 0, 0.9);
}
.wu-platformWrap .wu-tabNav > li.active {
  color: #0888ff;
}
.wu-platformWrap .wu-tabNav > li > span {
  display: inline-flex;
}
.wu-platformWrap .wu-tabNav > li > span .wu-pintaiIcon {
  margin-right: 4px;
}
.wu-platformWrap .layui-tab {
  margin-top: 12px;
  margin-bottom: 0;
}
.wu-platformWrap .areaNavWrap {
  border-bottom: 1px solid rgba(0, 0, 0, 0.09);
}
.wu-platformWrap .wu-tabNav-two > li {
  position: unset;
  line-height: unset;
  min-width: unset;
  color: rgba(0, 0, 0, 0.9);
  padding: 6px 12px;
}
.wu-platformWrap .wu-tabNav-two > li.layui-this {
  color: #0888ff;
}
.wu-platformWrap .wu-tabNav-two > li:hover {
  color: rgba(8, 136, 255, 0.8);
}
.wu-commonPage.wu-one .layui-laypage a,
.wu-commonPage.wu-one .layui-laypage span {
  display: inline-block;
}
.wu-commonPage.wu-one .layui-laypage .layui-laypage-limits {
  margin-right: 0;
}
.wu-commonPage.wu-two .layui-laypage .layui-laypage-skip {
  display: none;
}
.wu-commonPage.wu-two .layui-laypage .layui-laypage-limits {
  margin-right: 0;
}
.wu-commonPage.wu-two .layui-laypage .layui-laypage-skip {
  display: none;
}
.wu-commonPage.wu-two .layui-laypage .layui-laypage-limits {
  margin-right: 0;
}
.wu-commonPage.wu-three .layui-laypage .layui-laypage-skip {
  display: none;
}
.wu-commonPage.wu-three .layui-laypage .layui-laypage-limits {
  display: none;
}
.wu-commonPage .layui-laypage .layui-laypage-curr .layui-laypage-em {
  background-color: #0888ff;
}
.wu-commonPage .layui-laypage a:hover {
  color: #0888ff !important;
  border-color: #0888ff !important;
  position: relative;
  z-index: 10;
}
.wu-commonPage .layui-laypage a:hover:active {
  color: #0676ed !important;
  border: 1px solid #0676ed !important;
}
.wu-commonPage .layui-laypage a:hover.layui-disabled {
  border-color: #e2e2e2 !important;
  color: #d2d2d2 !important;
}
.wu-commonPage .layui-laypage .n-hidePages span {
  display: none;
}
.wu-commonPage .layui-laypage span {
  height: 32px;
  line-height: 30px;
  box-sizing: border-box;
  display: none;
  padding: 0 12px;
  font-size: 14px;
}
.wu-commonPage .layui-laypage span.layui-laypage-limits {
  display: inline-block;
  padding: 0;
  padding-right: 0;
  border-left: unset !important;
  margin-left: 8px;
  margin-right: 8px;
}
.wu-commonPage .layui-laypage a {
  height: 32px;
  line-height: 30px;
  box-sizing: border-box;
  display: none;
  padding: 0 12px;
  font-size: 14px;
}
.wu-commonPage .layui-laypage a.layui-laypage-prev {
  display: inline-block;
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}
.wu-commonPage .layui-laypage a.layui-laypage-prev:hover {
  position: relative;
  z-index: 10;
}
.wu-commonPage .layui-laypage a.layui-laypage-next {
  display: inline-block;
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
  position: relative;
}
.wu-commonPage .layui-laypage .layui-laypage-count {
  display: inline-block;
  margin-right: 8px;
  padding: 0;
}
.wu-commonPage .layui-laypage span.layui-laypage-limits select {
  border-radius: 6px;
  height: 32px;
  box-sizing: border-box;
}
.wu-commonPage .layui-laypage span.layui-laypage-skip {
  display: inline-block;
  padding: 0;
  margin-left: 8px;
}
.wu-commonPage .layui-laypage span.layui-laypage-skip > input {
  border-radius: 6px;
  height: 32px;
  font-size: 14px;
}
.wu-commonPage .layui-laypage .layui-laypage-count {
  border: unset;
}
.wu-commonPage .layui-laypage button {
  border-radius: 6px;
  height: 32px;
  margin-left: 16px;
}
.wu-commonPage .layui-laypage button:hover {
  border: 1px solid #0888ff;
  color: #0888ff;
}
.wu-commonPage .layui-laypage button:active {
  color: #0676ed;
  border: 1px solid #0676ed;
}
.wu-commonPage .layui-laypage input:hover {
  border-color: #0888ff !important;
}
.wu-commonPage .layui-laypage input:focus {
  border: 1px solid #0888ff !important;
  box-shadow: 0px 0px 0px 2px rgba(8, 136, 255, 0.2);
}
.wu-commonPage .layui-laypage select:hover {
  border-color: #0888ff !important;
}
.wu-commonPage .layui-laypage select:focus {
  border: 1px solid #0888ff !important;
  box-shadow: 0px 0px 0px 2px rgba(8, 136, 255, 0.2);
}
.n-bButton,
.n-mButton,
.n-sButton {
  border-radius: 6px;
}
.layui-header .layui-nav-item .layui-icon {
  color: #0888ff !important;
}
.layui-nav-tree .layui-nav-child dd.layui-this,
.layui-nav-tree .layui-nav-child dd.layui-this a,
.layui-nav-tree .layui-this,
.layui-nav-tree .layui-this > a,
.layui-nav-tree .layui-this > a:hover {
  background-color: #0888ff;
}
.layui-tab-brief > .layui-tab-title .layui-this {
  color: #0888ff;
}
.layui-tab-brief > .layui-tab-more li.layui-this:after,
.layui-tab-brief > .layui-tab-title .layui-this:after {
  border-bottom-color: #0888ff;
}
