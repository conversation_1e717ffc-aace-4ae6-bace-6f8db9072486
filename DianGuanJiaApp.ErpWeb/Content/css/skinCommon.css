body {
	background-color: #edf0f4;
}

.c09 {
	color: rgba(0, 0, 0, 0.9);
}

.c06 {
	color: rgba(0, 0, 0, 0.6);
}

.c04 {
	color: rgba(0, 0, 0, 0.4);
}

.f16 {
	font-size: 16px;
}

.f14 {
	font-size: 14px;
}

.f12 {
	font-size: 12px;
}

.n-dColor {
	color: #0888FF;
}

.pL4 {
	padding-left: 4px;
}

.pR4 {
	padding-right: 4px;
}

.pL8 {
	padding-left: 8px;
}

.pR8 {
	padding-right: 8px;
}

.pL16 {
	padding-left: 16px;
}

.pR16 {
	padding-right: 16px;
}
.pL24 {
	padding-left: 24px;
}
.pR24 {
	padding-right: 24px;
}
.mL4 {
	margin-left: 4px;
}

.mR4 {
	margin-right: 4px;
}

.mL8 {
	margin-left: 8px;
}

.mR8 {
	margin-right: 8px;
}

.mL16 {
	margin-left: 16px;
}

.mR16 {
	margin-right: 16px;
}
.mL24 {
	margin-left: 24px;
}

.mR24 {
	margin-right: 24px;
}

.n-dColor:hover {
	color: #0877FF;
}

.n-dBgColor {
	background-color: #0888FF;
}

	.n-dBgColor:hover {
		background-color: #0877FF;
	}

.n-sColor {
	color: #EA572E;
}

	.n-sColor:hover {
		color: #DF4216;
	}

.n-sBgColor {
	background-color: #EA572E;
}

	.n-sBgColor:hover {
		background-color: #DF4216;
	}

.n-tColor {
	color: #DC8715;
}

	.n-tColor:hover {
		color: #CE770C;
	}

.n-tBgColor {
	background-color: #DC8715;
}

	.n-tBgColor:hover {
		background-color: #CE770C;
	}

.n-fColor {
	color: #73AC1F;
}

	.n-fColor:hover {
		color: #689F17;
	}

.n-fBgColor {
	background-color: #73AC1F;
}

	.n-fBgColor:hover {
		background-color: #689F17;
	}

.n-mColor {
	color: #424A57;
}

	.n-mColor:hover {
		color: #323843;
	}

.n-mBgColor {
	background-color: #424A57;
}

	.n-mBgColor:hover {
		background-color: #323843;
	}

.n-bButton,
.n-mButton,
.n-sButton {
	display: inline-block;
	text-align: center;
	color: #fff;
	background: #0888FF;
	border-radius: 4px;
	cursor: pointer;
	box-sizing: border-box;
}

	.n-bButton:hover,
	.n-mButton:hover,
	.n-sButton:hover {
		background: #0877FF;
	}

.n-bButton {
	height: 40px;
	line-height: 40px;
	font-size: 16px;
	padding: 0 16px;
}

.n-mButton {
	height: 32px;
	line-height: 32px;
	padding: 0 12px;
	font-size: 14px;
}

.n-sButton {
	padding: 0 8px;
	height: 24px;
	line-height: 24px;
	font-size: 12px;
}

	.n-bButton.stop,
	.n-mButton.stop,
	.n-sButton.stop {
		opacity: 0.3;
		cursor: not-allowed;
	}

	.n-bButton.n-pActive,
	.n-mButton.n-pActive,
	.n-sButton.n-pActive {
		background: rgba(8, 136, 255, 0.1);
		color: #0888FF;
	}

		.n-bButton.n-pActive:hover, .n-mButton.n-pActive:hover, .n-sButton.n-pActive:hover {
			color: #0877FF;
			background: rgba(8, 136, 255, 0.2);
		}

	.n-bButton.n-sActive:hover, .n-mButton.n-sActive:hover, .n-sButton.n-sActive:hover {
		color: #0888FF;
		border: 1px solid #0888FF;
	}

.n-bButton.n-pActive:active, .n-mButton.n-pActive:active .n-sButton.n-pActive:active {
	color: rgba(8, 136, 255, 0.8);
	background: rgba(8, 136, 255, 0.1);
}
.newProductPicShow-item.n-active {
	color: rgba(0, 0, 0, 0.9);
}
.n-active:hover {
	color: #0888FF;
	border: 1px solid #0888FF;
}

.n-bButton.n-pActive.stop,
.n-mButton.n-pActive.stop,
.n-sButton.n-pActive.stop {
	background: rgba(0, 0, 0, 0.04);
	color: rgba(0, 0, 0, 0.4);
	cursor: not-allowed;
}


.n-bButton.n-mActive,
.n-mButton.n-mActive,
.n-sButton.n-mActive {
	background-color: #fff;
	color: #0888FF;
	border: 1px solid #0888FF;
	box-sizing: border-box;
}

.n-bButton.n-sActive,
.n-mButton.n-sActive,
.n-sButton.n-sActive {
	background-color: #fff;
	color: rgba(0, 0, 0, 0.9);
	border: 1px solid rgba(0, 0, 0, 0.09);
	box-sizing: border-box;
}

.n-bButton.n-mActive:hover,
.n-mButton.n-mActive:hover,
.n-mButton.n-mActive:hover {
	background-color: #fff;
	color: #0877FF;
	border: 1px solid #0877FF;
}

.n-bButton.n-mActive.stop,
.n-mButton.n-mActive.stop,
.n-sButton.n-mActive.stop {
	border: 1px solid rgba(0, 0, 0, 0.09);
	color: rgba(0, 0, 0, 0.3);
}

.n-bshadow {
	box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.04), 0px 2px 4px 0px rgba(0, 0, 0, 0.04), 0px 1px 2px 0px rgba(0, 0, 0, 0.08);
}

.n-mshadow {
	box-shadow: 0px 12px 24px 0px rgba(0, 0, 0, 0.04), 0px 8px 16px 0px rgba(0, 0, 0, 0.04), 0px 2px 4px 0px rgba(0, 0, 0, 0.08);
}

.n-ushadow {
	box-shadow: 0px 16px 32px 4px rgba(0, 0, 0, 0.05), 0px 12px 24px 0px rgba(0, 0, 0, 0.04), 0px 4px 8px -4px rgba(0, 0, 0, 0.08);
}

.n-ashadow {
	border: 1px solid #0888FF;
	box-shadow: 0px 0px 0px 2px rgba(8, 136, 255, 0.n-2);
}

.n-layui-input, .n-input, .n-textarea {
	height: 32px;
	border-radius: 4px;
	opacity: 1;
	border: 1px solid rgba(0, 0, 0, 0.14);
	font-size: 14px;
	color: rgba(0, 0, 0, 0.9);
	padding: 0 8px;
	box-sizing: border-box;
}


	.n-layui-input:hover, .n-select:hover {
		border: 1px solid #0888FF !important;
		outline: none;
	}

	.n-layui-input:focus, .n-select:focus {
		border: 1px solid #0888FF !important;
		box-shadow: 0px 0px 0px 2px rgba(8, 136, 255, 0.2) !important;
	}
/*.n-layui-input:focus::placeholder {
	color: rgba(0, 0, 0, 0.9);
}*/


.n-layui-form .layui-input,
.layui-select {
	height: 32px;
	border: 1px solid rgba(0, 0, 0, 0.14);
	border-radius: 4px;
}

.n-layui-form {
}

	.n-layui-form .layui-form-radio > i:hover,
	.n-layui-form .layui-form-radioed > i {
		color: #0888FF;
	}

	.n-layui-form .layui-form-checked[lay-skin=primary] i {
		border-color: #0888FF !important;
		background-color: #0888FF;
		color: #fff;
	}

	.n-layui-form .layui-form-checkbox[lay-skin=primary]:hover i {
		border-color: #0888FF;
		color: #fff;
	}

	.n-layui-form .layui-form-checkbox[lay-skin=primary] {
		height: 30px !important;
	}

	.n-layui-form .layui-form-onswitch {
		border-color: #3aadff;
		background-color: #0888FF;
	}

.n-tarTxt {
	font-size: 12px;
	font-weight: normal;
	line-height: 16px;
	display: inline-block;
	letter-spacing: 0em;
	font-variation-settings: "opsz" auto;
	/* �ı�?Font Wh1 */
	color: rgba(255, 255, 255, 0.9);
	padding: 4px 8px;
	gap: 8px;
	border-radius: 4px;
	background: #0888FF;
}

.n-tarTxt01 {
	background: rgba(8, 136, 255, 0.1);
	color: #0888FF;
}

.n-tarTxt02 {
	background: rgba(234, 87, 46, 0.1);
	color: #EA572E;
}

.n-tarTxt03 {
	background: rgba(230, 158, 62, 0.1);
	color: #DC8715;
}

.n-tarTxt04 {
	background: rgba(136, 189, 58, 0.1);
	color: #73AC1F;
}

.n-tarTxt05 {
	background: rgba(66, 74, 87, 0.1);
	color: #424A57;
}

.n-alert {
	padding: 8px 12px;
	border-radius: 6px;
	/* 品牌�Brand1 */
	background: #fff;
	border: unset;
	color: rgba(0, 0, 0, 0.9);
	box-sizing: border-box;
	/* 品牌�Brand2 */
	background: #fceeea;
	border: 0.5px solid rgba(234, 87, 46, 0.2);
}
.n-alert i {
	margin-right: 5px;
}
.n-alert-02 i {
	color: #EA572E;
	margin-right: 5px;
}
.n-toast {
	font-size:14px;
	display:flex;
	align-items:center;
	padding: 12px 16px;
	border-radius: 6px;
	/* 品牌�Brand1 */
	background: #fff;
	border: unset;
	color: rgba(0, 0, 0, 0.9);
	box-sizing: border-box;
	/* 品牌�Brand2 */
	box-shadow: 0px 6px 30px 5px rgba(0, 0, 0, 0.05),0px 16px 24px 2px rgba(0, 0, 0, 0.04),0px 8px 10px -5px rgba(0, 0, 0, 0.08);
}

.n-toast i {
	margin-right: 8px;
	font-size:18px;
}

.n-toast-01 i {
	color: #0888FF;
	margin-right: 5px;
}
.n-toast-02 i {
	color: #EA572E;
}
.n-toast-03 i {
	color: #DC8715;
	margin-right: 5px;
}
.n-toast-04 i {
	color: #73AC1F;
}
.n-toast-05 i {
	color: #73AC1F;
}


.n-tooltip {
	font-size:12px;
	display: flex;
	flex-direction: column;
	padding: 8px 12px;
	align-self: stretch;
	border-radius: 8px;
	/* 中性色/White */
	background: #FFFFFF;
	color:rgba(0,0,0,0.9);
	box-sizing: border-box;
	/* 中性色/Gray2 */
	border: 0.5px solid rgba(0, 0, 0, 0.09);
	position: relative;
	box-shadow: 0px 16px 32px 4px rgba(0, 0, 0, 0.05), 0px 12px 24px 0px rgba(0, 0, 0, 0.04), 0px 4px 8px -4px rgba(0, 0, 0, 0.08);
}

.n-leftUp::after,
.n-midUp::after,
.n-rightUp::after,
.n-leftdown::after,
.n-middown::after,
.n-rightdown::after {
	position: absolute;
	content: "";
	width: 8.49px;
	height: 8.49px;
	opacity: 1;
	background: #FFFFFF;
	border-style: solid;
	border-color: rgba(0, 0, 0, 0.09);
	box-sizing: border-box;
}

.n-leftUp::after {
	transform: rotate(-45deg);
	border-width: 0.5px 0.5px 0px 0px;
	top: -5px;
	left: 15px;
}

.n-rightUp::after {
	transform: rotate(-45deg);
	border-width: 0.5px 0.5px 0px 0px;
	top: -5px;
	right: 15px;
}

.n-leftdown::after {
	transform: rotate(-45deg);
	border-width: 0 0 0.5px 0px;
	bottom: -5px;
	left: 15px;
}

.n-rightdown::after {
	transform: rotate(-45deg);
	border-width: 0 0 0.5px 0px;
	bottom: -5px;
	right: 15px;
}

.n-middown::after {
	border-width: 0 0 0.5px 0px;
	bottom: -5px;
	left: 50%;
	transform: translateX(-50%) rotate(-45deg);
}

.n-skin.layui-layer {
	border-radius: 10px;
}

.n-skin .layui-layer-title {
	border-top-left-radius: 10px;
	border-top-right-radius: 10px;
}

.n-skin .layui-layer-btn {
	border-top: 1px solid #e8e8e8;
}

.n-skin .layui-layer-btn {
	border-top: 1px solid #e8e8e8;
}

.n-skin .layui-layer-btn a {
	border-radius: 4px;
	height: 32px;
	line-height: 32px;
	color: rgba(0, 0, 0, 0.9);
	margin-top: 0;
	box-sizing:border-box;
}

.n-skin .layui-layer-btn a:nth-child(1) {
	border: 1px solid #dedede;
	background-color: #fff;
	color: rgba(0, 0, 0, 0.9);
}

.n-skin .layui-layer-btn a:last-child {
	color: #fff;
	background: #0888FF;
	border: 1px solid #0888FF;
}
.n-skin .layui-layer-btn a:last-child:hover {
	color: #fff !important;
}

.n-skin .layui-layer-content {
	padding: 16px;
}

.n-body {
	padding: 0 24px;
}

.n-breadcrumb {
	display: flex;
	flex-direction: row;
	align-items: center;
	padding: 24px 0px;
	gap: 232px;
}

.n-breadcrumb-title {
	font-size: 20px;
	color: rgba(0, 0, 0, 0.9);
}

.n-carMain {
	width: 100%;
	border-radius: 8px;
	background-color: #fff;
	display: flex;
	flex-direction: column;
	padding: 24px;
	box-sizing: border-box;
}

.n-font1,
.n-font2,
.n-font3,
.n-font4,
.n-font5,
.n-font6 {
	color: rgba(0, 0, 0, 0.9);
	letter-spacing: 0em;
}

.n-font1 {
	font-size: 36px;
	line-height: 44px;
	font-weight: 700;
}

.n-font2 {
	font-size: 24px;
	line-height: 32px;
	letter-spacing: 0em;
	font-weight: 700;
}

.n-font3 {
	font-size: 20px;
	line-height: 28px;
	font-weight: 700;
}

.n-font4 {
	font-size: 16px;
	line-height: 24px;
}

.n-font5 {
	font-size: 14px;
	line-height: 20px;
}

.n-font6 {
	font-size: 12px;
	line-height: 18px;
}

.n-tabNav {
	display: flex;
	flex-direction: row;
	width: 100%;
	border-bottom: 1px solid #e8e8e8;
}

	.n-tabNav .n-tabNav-item {
		padding: 12px 16px;
		font-size: 14px;
		cursor: pointer;
	}

		.n-tabNav .n-tabNav-item.active {
			position: relative;
			color: #0888FF;
		}

			.n-tabNav .n-tabNav-item.active::after {
				display: block;
				content: "";
				position: absolute;
				bottom: -1px;
				left: 0;
				width: 100%;
				height: 2px;
				background-color: #0888FF;
			}

.n-layui-form .layui-form-select dl dd.layui-this {
	background-color: #0888FF;
}

.n-table thead tr th,
.n-table tbody tr td {
	border-right: none;
	border-top: 1px solid #e2e2e2;
	border-bottom: 1px solid #e2e2e2;
}

.n-table {
	border-left: none;
	width: 100%;
}

	.n-table thead tr th {
		background: rgba(0, 0, 0, 0.04);
		padding: 8px;
		text-align: left;
		font-weight: 500;
		color: rgba(0, 0, 0, 0.6);
		font-size:14px;
	}
	.n-table tbody tr:hover {
		background: rgba(0, 0, 0, 0.04);
	}
	.n-table tbody tr td {
		padding: 8px;
	}

.n-inpuCheck {
	height: 16px !important;
	width: 16px !important;
	position: relative;
	opacity: 0.85;
}

.n-productInfo {
	display: flex;
	flex-direction: row;
}

.n-productInfo-img {
	width: 55px;
	height: 55px;
	margin-right: 3px;
}

	.n-productInfo-img img {
		width: 55px;
		height: 55px;
		border-radius: 2px;
		border: 1px solid #e2e2e2;
	}

.n-productInfo-texts {
	display: flex;
	flex-direction: column;
}

.n-layui-mywrap {
	padding: 0 16px;
	background-color: #fff;
	border-radius: 8px;
	box-sizing: border-box;
}

	.n-layui-mywrap .n-layui-mywrap-title {
		padding: 16px 0;
		color: rgba(0, 0, 0, 0.9);
		font-weight:500;
	}

.n-select {
	height: 32px;
	border-radius: 4px;
	opacity: 1;
	border: 1px solid rgba(0, 0, 0, 0.14);
	font-size: 14px;
	color: rgba(0, 0, 0, 0.9);
	padding-left:6px;
}

.my-form-switch.active {
	border-color: #0888FF;
	background-color: #0888FF;
}

.n-productInfo .icon-down {
	font-size: 12px;
	margin-left: 5px;
}
/*多选框*/
.n-newCheckbox {
	width: 14px;
	height: 14px;
	display: inline-block;
	border: 1px solid #dcdee1;
	margin-right: 8px;
	background-color: #fff;
	border-radius: 4px;
	position: relative;
	cursor: pointer;
}
	.n-newCheckbox.stop {
/*		cursor: no-drop;
*/		background-color: #f0f0f0;
	}

	.n-newCheckbox.activeP, .n-newCheckbox.activeF {
		border: 1px solid #0888FF;
		background-color: #0888FF;
		position: relative;
	}

		.n-newCheckbox.activeP::before {
			content: "";
			display: block;
			width: 9px;
			height: 2px;
			background-color: #fff;
			top: 6px;
			position: absolute;
			left: 2.5px;
		}

		.n-newCheckbox.activeF::before {
			content: "";
			display: block;
			width: 5px;
			height: 2px;
			background-color: #fff;
			top: 7px;
			position: absolute;
			left: 2px;
			transform: rotate(45deg);
		}

		.n-newCheckbox.activeF::after {
			content: "";
			display: block;
			width: 10px;
			height: 2px;
			background-color: #fff;
			top: 6px;
			position: absolute;
			left: 4px;
			transform: rotate(135deg);
		}
		

.icon-a-help-circle1x {
	color: #999;
	font-size: 14px;
	margin-left: 3px;
	cursor: pointer;
}

.n-breadcrumb.active {
	position: fixed;
	width: 960px;
	background-color: #edf0f4;
	z-index: 10000;
	top: 60px;
}

.layui-laypage .layui-laypage-curr .layui-laypage-em {
	background-color: #0888FF;
}

.n-productInfo-texts .n-font6 {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	width: 150px;
	display: inline-block;
	font-size: 14px;
}

.n-tabNav-item .icon-guolvqi {
	font-size: 14px;
	margin-right: 5px;
}

.n-skin .layui-layer-title {
	color: rgba(0, 0, 0, 0.9);
	background-color:unset;
}

.productLibrary .new-full-mask .full-mask-main {
	padding: 0 16px;
}

#productLibrary .layui-laypage a, #productLibrary .layui-laypage span {
	padding: 0 10px;
}

#productLibrary .layui-laypage .layui-laypage-skip {
	margin-left: 0;
}

#productLibrary .layui-laypage .layui-laypage-limits {
	padding: 0;
}

.n-inputWrap {
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: flex-start !important;
	justify-content: center;
}

.input-warnTitle {
	padding: 5px 0;
	color: #EA572E;
	display: none;
}

.input-num {
	position: absolute;
	right: 10px;
	color: rgba(0, 0, 0, 0.4);
	top: 7px;
	font-size: 14px;
}

[contenteditable="true"] {
	caret-color: transparent;
}

.addnewProductPicShow_item ::selection {
	border: none;
}

.n-form-switch {
	background: rgba(66, 74, 87, 0.1);
	border-radius: 12px;
}

	.n-form-switch.m-switch {
		width: 28px;
		height: 16px;
		padding: 2px;
		box-sizing: border-box;
		display: flex;
		transition: all 0.3s ease-in-out;
		cursor: pointer;
	}

		.n-form-switch.m-switch.active {
			display: flex;
			justify-content: flex-end;
		}

	.n-form-switch .n-form-switch-icon {
		width: 12px;
		height: 12px;
		opacity: 1;
		background: rgba(255, 255, 255, 0.9);
		display: inline-block;
		border-radius: 50%;
		box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.04), 0px 2px 4px 0px rgba(0, 0, 0, 0.04), 0px 1px 2px 0px rgba(0, 0, 0, 0.08);
	}

	.n-form-switch.m-switch.active {
		background: #0888FF;
	}

.priceInputWrap input[type=text] {
	box-sizing: border-box;
}

.n-inputWrap.warnInput input[type=text] {
	border: 1px solid #fe6f4f !important;
}

.priceInputWrap .input-warnTitle {
	display: none;
}

.priceInputWrap.warnInput .input-warnTitle {
	display: block;
}

.new-full-mask {
	position: fixed;
	top: 0;
	bottom: 0;
	/* 	left: 0; */
	width: 100%;
	height: 100%;
	opacity: 0;
	pointer-events: auto;
	z-index: 10000000;
	display: none;
}
.newVersionsLeftNav .new-full-mask{    
	z-index: 1000000000;
}

	.new-full-mask.active {
		opacity: 1;
		right: 0;
		display: block;
	}

		.new-full-mask.active.hideActive {
			animation: hideNewFullMask 0.4s linear 0s 1 alternate forwards;
		}

@keyframes hideNewFullMask {
	0% {
		opacity: 1;
	}

	100% {
		opacity: 0;
		display: none;
	}
}

.new-full-mask .full-mask-back {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	background-color: rgba(0, 0, 0, 0.45);
	opacity: 0;
	filter: alpha(opacity=45);
	transition: opacity 0.3s linear, height 0s ease 0.3s;
	pointer-events: none;
	height: 100%;
	opacity: 0;
	pointer-events: auto;
	animation: newFullMaskBack 0.4s linear 0s 1 alternate forwards;
}

@keyframes newFullMaskBack {
	0% {
		opacity: 0;
	}

	100% {
		opacity: 1;
	}
}

.new-full-mask .full-mask-content-wrapper {
	position: fixed;
	width: 100%;
	height: 100%;
}

	.new-full-mask .full-mask-content-wrapper.full-mask-right {
		right: -1500px;
	}

	.new-full-mask .full-mask-content-wrapper .full-mask-content {
		position: relative;
		z-index: 1;
		overflow: auto;
		background-color: #ffffff;
		background-clip: padding-box;
		border: 0;
		width: 100%;
		height: 100%;
		border-top: 1px solid #e2e2e2;
	}

.new-full-mask.active .full-mask-content-wrapper.full-mask-right {
	right: -1500px;
	animation: newFullMask 0.4s linear 0s 1 alternate forwards;
}

.new-full-mask.hideActive .full-mask-content-wrapper.full-mask-right {
	right: 0;
	animation: hidenewFullMaskRigh 0.4s linear 0s 1 alternate forwards;
}

@keyframes hidenewFullMaskRigh {
	0% {
		right: 0;
		opacity: 1;
	}

	100% {
		right: -1500px;
		opacity: 1;
	}
}

@keyframes newFullMask {
	0% {
		right: -1000px;
		opacity: 0;
	}

	100% {
		right: 0;
		opacity: 1;
	}
}

.full-mask-header {
	position: relative;
	padding: 12px 16px;
	color: #333;
	background: #ffffff;
	border-bottom: 1px solid #eeeff0;
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	background-color: #F8F8F8;
	font-size: 14px;
	height: 44px;
	box-sizing: border-box;
	align-items: center;
}

.full-mask-header .icon-chuyidong {
	font-size: 24px;
	cursor: pointer;
	color: #666;
}

.new-full-mask .full-mask-header {
	position: relative;
	padding: 12px 16px;
	color: #333;
	background: #ffffff;
	border-bottom: 1px solid #eeeff0;
	/*	border-radius: 4px 4px 0 0;
*/ display: flex;
	flex-direction: row;
	justify-content: space-between;
	background-color: #F8F8F8;
	font-size: 14px;
	height: 44px;
	box-sizing: border-box;
	align-items: center;
}

	.new-full-mask .full-mask-header .icon-chuyidong {
		font-size: 24px;
		cursor: pointer;
		color: #666;
	}

.new-full-mask .full-mask-footer {
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	text-align: center;
	padding: 10px 15px 12px;
	pointer-events: auto;
	box-sizing: border-box;
	border-top: 1px solid #e2e2e2;
}
.new-full-mask .full-mask-newfooter {
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	padding: 12px 16px;
	pointer-events: auto;
	box-sizing: border-box;
	border-top: 1px solid rgba(0, 0, 0, 0.09);
	background: #fff;
}

	.new-full-mask .full-mask-footer span {
		display: inline-block;
		height: 28px;
		line-height: 28px;
		margin: 5px 5px 0;
		padding: 0 15px;
		border: 1px solid #dedede;
		background-color: #fff;
		color: #333;
		border-radius: 2px;
		font-weight: 400;
		cursor: pointer;
		text-decoration: none;
	}

		.new-full-mask .full-mask-footer span:hover {
			opacity: 0.8;
		}

	.new-full-mask .full-mask-footer .full-mask-btn0 {
		border-color: #1E9FFF;
		background-color: #1E9FFF;
		color: #fff;
	}

.new-full-mask .full-mask-main {
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	flex: 1;
	overflow: auto;
}

.new-full-mask .full-mask-main-title {
	margin-bottom: 10px;
	color: #12141a;
}

.new-full-mask .full-mask-content {
	display: flex;
	flex-direction: column;
}


.new-full-mask .full-mask-main-ul .full-mask-main-ul-li {
	flex: 1;
	border-right: 1px solid #eeeff0;
	overflow-y: auto;
}

	.new-full-mask .full-mask-main-ul .full-mask-main-ul-li:last-child {
		border-right: unset;
	}

.new-full-mask .full-main-items {
	display: flex;
	flex-direction: column;
	padding: 8px 0;
	box-sizing: border-box;
	height: 100%;
	overflow-y: auto;
}

	.new-full-mask .full-main-items .full-main-items-item {
		padding: 6px 12px;
		line-height: 20px;
		white-space: nowrap;
		text-overflow: ellipsis;
		cursor: pointer;
		transition: all 0.3s;
		position: relative;
		padding-right: 30px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 100%;
		box-sizing: border-box;
	}

		.new-full-mask .full-main-items .full-main-items-item:hover, .new-full-mask .full-main-items .full-main-items-item.active {
			background-color: #f0f6ff;
			color: #3aadff;
		}

.new-full-mask .full-main-items-item.active .icon-xiangxia {
	color: #3aadff;
}

.new-full-mask .full-main-items .full-main-items-item .icon-xiangxia {
	display: inline-block;
	transform: rotate(275deg);
	font-size: 12px;
}

.new-full-mask .full-main-items-item .checkboxClass {
	width: 15px;
	height: 15px;
	display: inline-block;
	border: 1px solid #dcdee1;
	margin-right: 5px;
	background-color: #fff;
}

.new-full-mask .full-main-items-item.activeA .checkboxClass {
	border: 1px solid #3aadff;
	background-color: #3aadff;
	position: relative;
}

.new-full-mask .full-main-items-item.activeA .icon-xiangxia {
	color: #3aadff;
}

.new-full-mask .full-main-items-item.activeP .checkboxClass {
	border: 1px solid #3aadff;
	background-color: #3aadff;
	position: relative;
}

.new-full-mask .full-main-items-item.activeP .icon-xiangxia {
	color: #3aadff;
}

.new-full-mask .full-main-items-item.activeP .checkboxClass::before {
	content: "";
	display: block;
	width: 10px;
	height: 1px;
	background-color: #fff;
	top: 7px;
	position: absolute;
	left: 3px;
}

.new-full-mask .full-main-items-item-left {
	display: flex;
	align-items: center;
}

.new-full-mask .full-main-items-item .icon-xiangxia {
	color: #ddd;
}

.new-full-mask .full-main-items-item.activeA .checkboxClass::before {
	content: "";
	display: block;
	width: 5px;
	height: 1px;
	background-color: #fff;
	top: 9px;
	position: absolute;
	left: 2px;
	transform: rotate(45deg);
}

.new-full-mask .full-main-items-item.activeA .checkboxClass::after {
	content: "";
	display: block;
	width: 10px;
	height: 1px;
	background-color: #fff;
	top: 7px;
	position: absolute;
	left: 5px;
	transform: rotate(135deg);
}

.full-mask-main{
	box-sizing: border-box;
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: auto;
}
.full-mask-main-tableWrap{
	margin-top: -1px;
    overflow-y: auto;
}

.n-skin .layui-layer-btn a:last-child {
	margin-right: 0;
}
.n-skin.layui-layer-page .layui-layer-btn {
	padding: 12px 16px;
}
[contenteditable]:focus {
	outline: none;
}
.n-skin .layui-layer-btn a {
	font-size:14px;
}
.icon-icon_shanchu-:hover {
	color: #EA572E!important;
}
.n-skin .layui-layer-btn a:nth-child(1):hover {
	border: 1px solid #0888FF;
	color: #0888FF;
}
/* .n-skin .layui-layer-btn a:last-child:hover {
	background: #0877FF;
	opacity:1;
} */
.w-msg {
	font-size: 12px;
	position: fixed;
	z-index: 105000000000000;
	width: 100%;
	top: 0;
	display: flex;
	justify-content: center;
	animation: msgAni 0.6s ease-in-out 0s 1 alternate forwards;
}

.w-alert .n-alert {
	display: inline-block;
}


@keyframes msgAni {
	0% {
		top: 0;
	}

	100% {
		top: 80px;
	}
}
.n-mySelect {
	width: 269.33px;
	height: 32px;
	border-radius: 4px;
	border: 1px solid #e6e6e6;
	box-sizing: border-box;
	background-color: #fff;
	font-size: 14px;
	position: relative;
	top: 0;
	left: 0;
}
	.n-mySelect.stop {
		border: 1px solid rgba(0, 0, 0, 0.04);
		background-color: rgba(0, 0, 0, 0.04);
		color: rgba(0, 0, 0, 0.3);
		position:relative;

	}
		.n-mySelect.stop::after {
			position: absolute;
			width: 100%;
			height: 100%;
			content: '';
			display: block;
			top: 0;
			left: 0;
		}
	.n-mySelect.stop .n-mySelect-title-left-title {
		color: rgba(0, 0, 0, 0.3);
	}
	.n-mySelect.stop .n-mySelect-title-chooseItem {
		color: rgba(0, 0, 0, 0.3);
	}
		.n-mySelect.stop:hover {
			border: 1px solid rgba(0, 0, 0, 0.04)!important;
		}

	.n-mySelect.active .n-mySelect-showContent {
		display: flex;
	}

	.n-mySelect:hover {
		border: 1px solid #0888FF !important;
	}

	.n-mySelect.active {
		border: 1px solid #0888FF !important;
		box-shadow: 0px 0px 0px 2px rgba(8, 136, 255, 0.2) !important;
	}

	.n-mySelect .n-mySelect-title {
		display: flex;
		justify-content: space-between;
		padding: 6px 8px;
		cursor: pointer;
		align-items: center;
	}

	.n-mySelect .n-mySelect-title-left {
		flex: 1;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		display:flex;
		position: relative;
	}

	.n-mySelect .n-mySelect-title-left-title {
		color: rgba(0, 0, 0, 0.6)
	}

	.n-mySelect .n-mySelect-title-right-title {
		color: rgba(0, 0, 0, 0.6);
	}

	.n-mySelect .n-mySelect-title-placeholder {
		color: rgba(0, 0, 0, 0.4);
		padding-left:4px;
	}
	.n-mySelect .n-mySelect-title-chooseItem {
		color: rgba(0, 0, 0, 0.9);
		padding-left: 4px;
		display: none;
	}
	.n-mySelect.hasActive .n-mySelect-title-chooseItem {
		display: inline-block;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
	.n-mySelect.hasActive .n-mySelect-title-placeholder {
		display: none;
	}
	.n-mySelect .n-mySelect-title .icon-a-chevron-down1x {
		color: rgba(0, 0, 0, 0.4);
	}

	.n-mySelect.active .n-mySelect-title .icon-a-chevron-down1x {
		display: inline-block;
		transition: 0.3s;
		transform: rotate(180deg);
	}

.n-mySelect-showContent {
	display: none;
	top: 0;
	left: unset;
	position: absolute;
	box-sizing: border-box;
	width: 100%;
	flex-direction: column;
	padding: 8px;
	background: #FFFFFF;
	border-radius: 4px;
	box-shadow: 0px 12px 24px 0px rgba(0, 0, 0, 0.04), 0px 8px 16px 0px rgba(0, 0, 0, 0.04), 0px 2px 4px 0px rgba(0, 0, 0, 0.08);
	animation: mySelectAnimation 0.3s ease-in-out 0s 1 alternate forwards;
	z-index: 10000;
}

@keyframes mySelectAnimation {
	0% {
		opacity: 0;
		top: 0;
	}

	100% {
		opacity: 1;
		top: 35px;
	}
}

.n-mySelect .n-mySelect-search {
	width: 100%;
	height: 32px;
	box-sizing: border-box;
	position: relative;
}

	.n-mySelect .n-mySelect-search .n-input {
		width: 100%;
		border-radius: 2px;
		height: 25px;
		padding: 6px;
		box-sizing: border-box;
		position: relative;
		padding-right: 30px;
	}

.n-mySelect .icon-a-search1x1 {
	position: absolute;
	right: 4px;
	color: rgba(0, 0, 0, 0.4);
	font-size: 19px;
	top: 3px;
	cursor:pointer;
}


.n-mySelect .n-mySelect-showContent-ul {
	display: flex;
	flex-direction: column;
	max-height: 280px;
	overflow-y: auto;
}

	.n-mySelect .n-mySelect-showContent-ul .n-mySelect-showContent-ul-li {
		padding: 6px 8px;
		height: 32px;
		box-sizing: border-box;
		color: rgba(0, 0, 0, 0.6);
		cursor: pointer;
		border-radius: 4px;
		display:flex;
		align-items:center;
		position:relative;
	}
		.n-mySelect .n-mySelect-showContent-ul .n-mySelect-showContent-ul-li.activeItem {
			color: #0888FF;
		}
		.n-mySelect .n-mySelect-showContent-ul .n-mySelect-showContent-ul-li.checkbox, .n-mySelect .n-mySelect-showContent-ul .n-mySelect-showContent-ul-li.checkboxlist {
			padding-left:25px;
		}

			.n-mySelect .n-mySelect-showContent-ul .n-mySelect-showContent-ul-li.checkbox::before, .n-mySelect .n-mySelect-showContent-ul .n-mySelect-showContent-ul-li.checkboxlist::before {
				width: 14px;
				height: 14px;
				display: inline-block;
				border: 1px solid #dcdee1;
				margin-right: 8px;
				border-radius: 4px;
				position: absolute;
				cursor: pointer;
				background-color: #fff;
				content: '';
				left: 5px;
			}
		.n-mySelect .n-mySelect-showContent-ul .n-mySelect-showContent-ul-li.activeItem.checkbox::before, .n-mySelect .n-mySelect-showContent-ul .n-mySelect-showContent-ul-li.activeItem.checkboxlist::before {
			width: 14px;
			height: 14px;
			display: inline-block;
			border: 1px solid #0888FF;
			margin-right: 8px;
			border-radius: 4px;
			position: absolute;
			cursor: pointer;
			background-color: #0888FF;
			content: '';
			left:5px;
		}
		.n-mySelect .n-mySelect-showContent-ul .n-mySelect-showContent-ul-li.activeItem.checkbox::after, .n-mySelect .n-mySelect-showContent-ul .n-mySelect-showContent-ul-li.activeItem.checkboxlist::after {
			width: 4px;
			height: 8px;
			display: inline-block;
			border-left-color: #fff;
			border-bottom-color: #fff;
			position: absolute;
			content: '';
			border-right: 2px solid #fff;
			border-bottom: 2px solid #fff;
			transform: rotate(45deg);
			top: 10px;
			left: 10px;
		}
		.n-mySelect .n-mySelect-showContent-ul .n-mySelect-showContent-ul-li .checkbox, .n-mySelect .n-mySelect-showContent-ul .n-mySelect-showContent-ul-li .checkboxlist {
			padding-left: 20px;
		}
		
		.n-mySelect .n-mySelect-showContent-ul .n-mySelect-showContent-ul-li.noData {
			justify-content: center;
			align-items: center;
			display: flex;
			cursor: auto;
		}

		.n-mySelect .n-mySelect-showContent-ul .n-mySelect-showContent-ul-li:hover {
			background: rgba(0, 0, 0, 0.04);
		}
		.selected-li-active {
			background: rgba(0, 0, 0, 0.04);
		}

.n-mySelect .n-mySelect-operate {
	color: #0888FF;
	padding: 6px 8px 0 8px;
	height: 32px;
	cursor: pointer;
	display: flex;
	align-items: center;
}

.n-progressWrap {
	display: flex;
	align-items: center;
	width: 100%;
}

.n-progressWrap-content {
	flex: 1;
	background: rgba(0, 0, 0, 0.04);
	height: 16px;
	border-radius: 8px;
	overflow: hidden;
}

	.n-progressWrap-content .n-progressWrap-content-step {
		background-color: #0888FF;
		height: 16px;
		border-radius: 8px;
		display: inline-block;
	}

.n-progressWrap .n-progressWrap-text {
	margin-left: 16px;
	color: #3D3D3D;
}

.n-writtenWrp {
	width: 200px;
	border-radius: 8px 0px 0px 8px;
	/* 自动布局 */
	display: flex;
	flex-direction: column;
	padding: 0px 0px 8px 0px;
	background: #FFFFFF;
	font-size: 14px;
}

	.n-writtenWrp .n-writtenWrp-title {
		padding: 8px;
		color: rgba(0, 0, 0, 0.6);
		font-size: 12px;
	}

	.n-writtenWrp .n-writtenWrp-main {
		display: flex;
		flex-direction: column;
	}

		.n-writtenWrp .n-writtenWrp-main .n-writtenWrp-main-li {
			cursor: pointer;
			display: flex;
			align-items: center;
			color: rgba(0, 0, 0, 0.6);
		}
			.n-writtenWrp .n-writtenWrp-main .n-writtenWrp-main-li a {
				display: flex;
				align-items: center;
				color: rgba(0, 0, 0, 0.6);
				justify-content: space-between;
				width: 100%;
				padding: 8px;
			}
			.n-writtenWrp .n-writtenWrp-main .n-writtenWrp-main-li .n-writtenWrp-main-li-left {
				display: flex;
				align-items: center;
				flex: 1;
			}

			.n-writtenWrp .n-writtenWrp-main .n-writtenWrp-main-li .icon-a-arrow-right1x {
				display: none;
			}

			.n-writtenWrp .n-writtenWrp-main .n-writtenWrp-main-li.active {
				background: rgba(0, 0, 0, 0.04);
				color: rgba(0, 0, 0, 0.9);
			}

		.n-writtenWrp .n-writtenWrp-main .n-writtenWrp-main-li-left-text {
			margin-right: 8px;
		}

		.n-writtenWrp .n-writtenWrp-main .active .n-writtenWrp-main-li-left-text {
			color: rgba(0, 0, 0, 0.9);
			font-weight:500;
		}

			.n-writtenWrp .n-writtenWrp-main .n-writtenWrp-main-li.active .icon-a-arrow-right1x {
				display: block;
			}
.iframeMask.new-full-mask .full-mask-content-wrapper .full-mask-content{
	background: unset;
}

.n-alert-01 {
	background: #e6f3ff;
	border: 0.5px solid rgba(8, 136, 255, 0.2);
}

.n-alert-01 i {
	color: #0888FF;
	margin-right: 5px;
}

.n-alert-02 {
	background: #fceeea;
	/* ����??Error2 */
	border: 0.5px solid rgba(234, 87, 46, 0.2);
}

.n-alert-02 i {
	color: #EA572E;
	margin-right: 5px;
}

.n-alert-03 {
	background: #fcf5eb;
	border: 0.5px solid rgba(230, 158, 62, 0.2);
}

	.n-alert-03 i {
		color: #DC8715;
		margin-right: 5px;
	}

.n-alert-04 {
	background: #f3f8eb;
	border: 0.5px solid rgba(136, 189, 58, 0.2);
}

	.n-alert-04 i {
		color: #73AC1F;
		margin-right: 5px;
	}
.n-alert .icon-a-close1x {
	position:absolute;
	right:0;
	color:rgba(0,0,0,0.6);
	cursor:pointer;
}
i.layui-icon.laydate-icon.laydate-prev-m:before {
	display: block;
	content: "";
	width: 20px;
	height: 20px;
	background-color: #fff;
	position: absolute;
	top: 5px;
}
i.layui-icon.laydate-icon.laydate-prev-m:after {
	display: block;
	content: "";
	width: 10px;
	height: 10px;
	position: absolute;
	border-left: 1px solid #666;
	border-bottom: 1px solid #666;
	top: 8px;
	transform: rotate(45deg);
}
.layui-laydate-header i.laydate-next-m:before {
	display: block;
	content: "";
	width: 20px;
	height: 20px;
	background-color: #fff;
	position: absolute;
	top: 5px;
}
.layui-laydate-header i.laydate-next-m::after {
	display: block;
	content: "";
	width: 10px;
	height: 10px;
	position: absolute;
	border-left: 1px solid #666;
	border-bottom: 1px solid #666;
	top: 8px;
	transform: rotate(225deg);
}
body .layui-laydate .layui-this {
	background-color: #0888FF!important;
}

.choosePlatformDailog {
	width: 560px;
	padding:16px;
	box-sizing:border-box;
}
.choosePlatformWrap {
	display:flex;
	flex-direction:row;
	width:100%;
	flex-wrap:wrap;

}
.choosePlatformWrap .choosePlatformWrap-item {
	width: 126px;
	height: 48px;
	border-radius: 4px;
	opacity: 1;
	display: flex;
	flex-direction: row;
	align-items: center;
	padding: 4px 4px 4px 8px;
	background: #FFFFFF;
	box-sizing: border-box;
	border: 1px solid rgba(0, 0, 0, 0.14);
	margin-right:8px;
	margin-bottom:8px;
}
.choosePlatformWrap .choosePlatformWrap-item:nth-child(4n) {
	margin-right: 0;
}
.choosePlatformWrap .choosePlatformWrap-item label{
	display:flex;
	width:100%;
	height:100%;
	align-items:center;
	justify-content:space-between;
}
.choosePlatformWrap .choosePlatformWrap-item .choosePlatformWrap-icon {
	width: 90px;
	height: 40px;
	border-radius: 4px;
	opacity: 1;
}
	.choosePlatformWrap .choosePlatformWrap-item input[type=checkbox] {
		width: 16px;
		height: 16px;
		border-radius: 4px;
		opacity:0.8;
	}


.n-addressDailog {
	display: flex;
	flex-direction: column;
	box-sizing: border-box;
}
.n-addressDailog .n-addressDailog-item {
	margin-bottom: 16px;
}
	.n-addressDailog .n-addressDailog-item .n-addressDailog-item-title{
		font-size: 14px;
		font-weight: normal;
		display: flex;
		color: rgba(0, 0, 0, 0.6);
		margin-bottom: 8px;
	}
	.n-addressDailog .n-addressDailog-item-content {
		display: flex;
	}
	.n-addressDailog .n-addressDailog-item-content label{
		display: flex;
		flex: 1;
		margin-right: 4px;
	}
	.n-addressDailog .n-addressDailog-item-content label input[type=text] {
		width: 100%;
	}
		.n-addressDailog .n-addressDailog-item-content label .n-select {
			width: 100%;
		}
		.n-addressDailog .n-addressDailog-item-content label:last-child {
			display: flex;
			flex: 1;
			margin-right: 0;
		}
	.n-addressDailog .n-textareaWrap {
		display: flex;
		margin-top: 8px;
	}
		.n-addressDailog .n-textareaWrap .n-input {
			width: 100%;
		}

.n-pintaiIcon {
	background-image: url('/Content/images/svg/n-pintai-icons2x.png');
/*	background-size: 50%;
*/	background-position: 0 0;
	background-repeat: no-repeat;
	display:inline-block;
	background-size:540px 200px;
}
.n-bigIcon {
	width: 90px;
	height: 40px;
	background-position: 0 -40px;
}
	.n-bigIcon.TouTiao {
		background-position: 0 -40px;
	}
	.n-bigIcon.Pinduoduo {
		background-position: -90px -40px;
	}
	.n-bigIcon.KuaiShou {
		background-position: -180px -40px;
	}
	.n-bigIcon.Alibaba {
		background-position: -270px -40px;
	}
	.n-bigIcon.Taobao {
		background-position: -360px -40px;
	}
	.n-bigIcon.Jingdong {
		background-position: -450px -40px;
	}
	.n-bigIcon.WxXiaoShangDian{
		background-position: 0 -80px;
	}
	.n-bigIcon.WxVideo {
		background-position: -90px -80px;
	}
	.n-bigIcon.YouZan {
		background-position: -180px -80px;
	}
	.n-bigIcon.WeiMeng {
		background-position: -270px -80px;
	}
	.n-bigIcon.AlibabaC2M {
		background-position: -360px -80px;
	}
	.n-bigIcon.DuXiaoDian {
		background-position: -450px -80px;
	}
	.n-bigIcon.MoGuJie {
		background-position: 0 -120px;
	}
	.n-bigIcon.MeiLiShuo {
		background-position: -90px -120px;
	}
	.n-bigIcon.MoKuai {
		background-position: -180px -120px;
	}
	.n-bigIcon.WeiDian {
		background-position: -270px -120px;
	}
	.n-bigIcon.SuNing {
		background-position: -360px -120px;
	}
	.n-bigIcon.XiaoHongShu {
		background-position: -450px -120px;
	}
	.n-bigIcon.TuanHaoHuo {
		background-position: 0 -160px;
	}
	.n-bigIcon.VipShop {
		background-position: -90px -160px;
	}
	.n-bigIcon.KuaiTuanTuan {
		background-position: -180px -160px;
	}
	.n-bigIcon.Cainiaolink {
		background-position: -270px -160px;
	}

.n-smallIcon {
	background-image: url('/Content/images/svg/n-pintai-icons2x.png');
	background-position: 0 0;
	background-repeat: no-repeat;
	display: inline-block;
	width: 20px;
	height: 20px;
	background-size: 540px 200px;
}

.n-smallIcon.TouTiao {
	background-position: 0 0;
}

.n-smallIcon.Pinduoduo {
	background-position: -20px 0;
}

.n-smallIcon.KuaiShou {
	background-position: -40px 0;
}

.n-smallIcon.Alibaba {
	background-position: -60px 0;
}

.n-smallIcon.Taobao {
	background-position: -80px 0;
}

.n-smallIcon.Jingdong {
	background-position: -100px 0;
}

.n-smallIcon.WxXiaoShangDian {
	background-position: -120px 0;
}

.n-smallIcon.WxVideo {
	background-position: -140px 0;
}

.n-smallIcon.YouZan {
	background-position: -160px 0;
}

.n-smallIcon.WeiMeng {
	background-position: -180px 0;
}

.n-smallIcon.AlibabaC2M {
	background-position: -200px 0;
}

.n-smallIcon.DuXiaoDian {
	background-position: -220px 0;
}

.n-smallIcon.MoGuJie {
	background-position: -240px 0;
}

.n-smallIcon.MeiLiShuo {
	background-position: -260px 0;
}

.n-smallIcon.MoKuai {
	background-position: -280px 0;
}

.n-smallIcon.WeiDian {
	background-position: -300px 0;
}

.n-smallIcon.SuNing {
	background-position: -320px 0;
}

.n-smallIcon.XiaoHongShu {
	background-position: -340px 0;
}

.n-smallIcon.TuanHaoHuo {
	background-position: -360px 0;
}

.n-smallIcon.VipShop {
	background-position: -380px 0;
}

.n-smallIcon.KuaiTuanTuan {
	background-position: -400px 0;
}
.n-smallIcon.Cainiaolink {
	background-position: -420px 0;
}
.n-smallIcon.PuHuo {
	background-position: -440px 0;
}
.chooseShopPlatform {
	width: 48px;
	height: 48px;
	border-radius: 12px;
	display: inline-block;
	background-image: url('/Content/images/svg/n-choosepintai-2024-7-11.png');
	background-size: 400px 48px;
}
.chooseShopPlatform.TouTiao {
	background-position:0 0;
}
.dgActive {
	position: relative;
	color: rgba(0, 0, 0, 0.9);
}
.dgActive::before {
	border-bottom: 16px solid #0888FF;
	border-left: 16px solid transparent;
	position: absolute;
	bottom: 0;
	right: 0;
	display: block;
	content: "";
}
.dgActive::after {
	display: block;
	content: "";
	width: 6px;
	height: 4px;
	border-left: 1px solid #fff;
	border-bottom: 1px solid #fff;
	position: absolute;
	bottom: 3px;
	right: 1px;
	transform: rotate(313deg);
}
.rightOperate {
	text-align:right!important;
}
.n-table.active thead tr th {
	background-color:#fff;
	
}
.showMoreAddCatePropsBtnsWrap {
/*	margin-top: 8px;*/
	display:none;
}

	.showMoreAddCatePropsBtnsWrap .showMoreAddCatePropsBtns {
		display: flex;
		align-items: center;
		font-size: 14px;
		width: 60px;
	}
		.showMoreAddCatePropsBtnsWrap .showMoreAddCatePropsBtns.active {
			margin-top:8px;
		}
		.showMoreAddCatePropsBtnsWrap .showMoreAddCatePropsBtns .icon-down {
			font-size: 10px;
			display: inline-block;
			margin-left: 8px;
			transition: 0.3s all;
		}
		.showMoreAddCatePropsBtnsWrap .showMoreAddCatePropsBtns.active .icon-down {
			transform:rotate(180deg);
		}
.hideMorewCatePropsWrap {
	max-height: 48px;
	overflow-y: hidden;
	transition:0.3s all;
}

#allCatePropsWrap .new-n-mySelect .n-mySelect-showContent {
	width:260px;
	top: unset;
	left: unset;
	position: fixed;
	animation: mySelectAnimation02 0.3s ease-in-out 0s 1 alternate forwards;
}
@keyframes mySelectAnimation02 {
	0% {
		opacity: 0;
		margin-top:-30px;
	}

	100% {
		opacity: 1;
		margin-top: 0;
	}
}
.n-tableNoDataShow {
	display:flex;
	flex-direction:column;
	justify-content:center;
	align-items:center;
}
.n-tableNoDataShow .icon-wushuju1 {
	font-size: 30px;
	display: flex;
	width: 30px;
	height: 30px;
	justify-content: center;
	align-items: center;
	color: rgba(0, 0, 0, 0.4);
	margin-bottom:4px;
}
.n-tableNoDataShow .tableNoDataShow-title {
	font-size: 12px;
	color: rgba(0, 0, 0, 0.4);
}

#catePropsWrap02 .n-mySelect-showContent {
	position: fixed;
	width: 230px;
	animation: mySelectAnimation3 0.3s ease-in-out 0s 1 alternate forwards;
	
}


@keyframes mySelectAnimation3 {
	0% {
		opacity: 0;
		margin-top:-32px;
	}

	100% {
		opacity: 1;
		margin-top: 0;
	}
}
.DistributionStatusWrap {
	width: 560px;
}

.DistributionStatusWrap-title {
	font-size: 16px;
	color: rgba(0, 0, 0, 0.9);
	padding: 16px;
}

.DistributionStatusWrap-content {
	padding: 0 16px 16px 16px;
}

.DistributionStatusWrap-btnWrap {
	padding: 16px;
	display: flex;
	justify-content: space-between;
	border-top: 1px solid #e2e2e2;
}

.n-skin.distributionStatusWrapSkin .layui-layer-content {
	padding: 0;
}

.DistributionStatusWrap-btnWrap-left {
	color: rgba(0, 0, 0, 0.6);
	font-size: 14px;
}


.n-skin.distributionStatusWrapSkin .content-span {
	display: inline-block;
	margin-right: 16px;
}
.n-skin.distributionStatusWrapSkin .content-span i {
	font-weight: 700;
}
.n-skin.distributionStatusWrapSkin DistributionStatusWrap-btnWrap-right span{
	display:inline-block;
	margin-left:8px;
}
.order-column-header-SalePrice .n-leftdown.th-tooltip {
	width:305px;
}
.n-mySelect .n-mySelect-showContent-ul .n-mySelect-showContent-ul-li.hideCateLi {
	display:none!important;
}

.createProductIframe.activeScreen {
	position: fixed;
	top: 0;
	bottom: 0;
	right: 0;
	left: 0;
}
#addSkuWrap_Skus_ul .showSkuValue-btns {
	margin-top:12px;
	margin-bottom:16px;
}
.addSkuWrap-noEdit.addSkuWrap-Skus .showSkuValue-item-down-img {
	border: 1px solid #ececee;
	box-sizing: border-box;
	border-radius: 4px;
}
.createBase-footer {
	z-index:10;
}

/* ���� */
.n-flex{
	display: flex;
}
.n-box-nav{
	width: 100%;
	display: flex;
	flex-direction: row;
}
.n-box-nav .n-box-nav-li{
	display: flex;
	justify-content: center;
	align-items: center;
	height: 28px;
	flex:1;
	border:1px solid rgba(0, 0, 0, 0.14);
	cursor: pointer;
	font-size: 14px;
	font-weight: 500;
	box-sizing: border-box;
	color: rgba(0, 0, 0, 0.9);
}
.n-box-nav .n-box-nav-li:nth-child(n+2){
	margin-left:-1px;
}
.n-box-nav .n-box-nav-li:first-child{
	border-top-left-radius: 4px;
	border-bottom-left-radius: 4px;	
}
.n-box-nav .n-box-nav-li:last-child{
	border-top-right-radius: 4px;
	border-bottom-right-radius: 4px;	
}
.n-box-nav .n-box-nav-li.active{
	border:1px solid #0888FF;
	color: #0888FF;
} 
.n-box-nav .n-box-nav-li:hover{
	border:1px solid #0888FF;
	color: #0888FF;
}
	.n-box-nav .n-box-nav-li.stop {
		border-color: rgba(0, 0, 0, 0.14);
		color: rgba(0, 0, 0, 0.3);
		cursor:not-allowed;
		position:relative;
	} 



/* ����9-2 */
.selectWrap.n-wuSelect{
	width: 100%;
	font-size: 14px;
}
.selectWrap.n-wuSelect .selectMore {
    top: unset;	
}
.selectWrap.n-wuSelect .selectMore{
	height: 32px;
	border-radius: 4px;
	background-color: #fff;
}
.selectWrap.n-wuSelect .selectMore-choose > li{
	color: rgba(0, 0, 0, 0.4);
	font-size: 14px;
	display: flex;
	align-items: center;		
}
.selectWrap.n-wuSelect .showMoreicon{
	right: 8px;
	z-index: 100;
	border-bottom: 1px solid rgba(0, 0, 0, 0.4);
	border-left: 1px solid rgba(0, 0, 0, 0.4);
	width: 5px;
	height: 5px;
	transform: rotate(-45deg);	
	border-top:unset;
}
.selectWrap.n-wuSelect .selectWrap-box{
	opacity: 0;
	animation: mySelectAnimation 0.3s ease-in-out 0s 1 alternate forwards;
	flex-direction: column;
	padding: 8px;
	background: #FFFFFF;
	border-radius: 4px;
	border: unset;
	box-shadow: 0px 12px 24px 0px rgba(0, 0, 0, 0.04), 0px 8px 16px 0px rgba(0, 0, 0, 0.04), 0px 2px 4px 0px rgba(0, 0, 0, 0.08);
}
.selectWrap.n-wuSelect .selectMore:hover{
	border: 1px solid #0888FF !important;
	cursor: pointer;
	
}
.selectWrap.n-wuSelect.active .selectMore {
    border: 1px solid #0888FF !important;
    box-shadow: 0px 0px 0px 2px rgba(8, 136, 255, 0.2) !important;
}
.selectWrap.n-wuSelect .selectWrap-box{
	width: 100%;
}
.selectWrap.n-wuSelect .selectMore-ul > li {
    padding: 6px 8px;
    box-sizing: border-box;
    color: rgba(0, 0, 0, 0.6);
    cursor: pointer;
    border-radius: 4px;
    display: flex;
    align-items: center;
    position: relative;
}
.selectWrap.n-wuSelect .selectMore-ul > li:hover {
	background: rgba(0, 0, 0, 0.04);
}

.selectWrap.n-wuSelect .selectMore-ul > li.active{
    background: rgba(0, 0, 0, 0.04);
}
.selectWrap.n-wuSelect .selectMore-ul > li > label{
	padding: 0;
	display: flex;
	width: 100%;
	align-items: center;
}
.selectWrap.n-wuSelect .list-icon{
	display: none!important;
}
.selectWrap.n-wuSelect .selectMore-ul > li{
	margin: 0;	
}
.selectWrap.n-wuSelect .selectMore-ul > li > label input[type="checkbox"]{
	display: inline-block;
}
.selectWrap.n-wuSelect .selectMore{
	display: flex;
	align-items: center;
}
.selectWrap.n-wuSelect .selectMore .leftTitle{
    display: inline-block;
    flex: 1;
    word-break: keep-all;
    white-space: nowrap;
    color: rebeccapurple;
    color: rgba(0, 0, 0, 0.6);
    font-size: 14px;
    padding-left: 8px;
	padding-right: 8px;
}

	.selectWrap.n-wuSelect.activeChoose .selectMore {
		border: 1px solid #0888FF !important;
		box-shadow: 0px 0px 0px 2px rgb(8 136 255 / 20%) !important;
	}
	.selectWrap.n-wuSelect .selectMore::before {
		width: 20px;
		height: 24px;
	}

.n-footerWrap {
	position: fixed;
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	align-items: center;
	height: 56px;
	/* ����ɫ/White */
	background: #FFFFFF;
	box-sizing: border-box;
	/* ����ɫ/Gray3 */
	border-width: 0.5px 0px 0px 0px;
	border-style: solid;
	border-color: rgba(0, 0, 0, 0.14);
	box-shadow: 0px -4px 8px 0px rgba(0, 0, 0, 0.04);
	padding-left: 170px;
	padding-top: 12px;
	padding-bottom: 12px;
	bottom: 0;
	width: 100%;
	left: 0;
	display: flex;
	justify-content: space-between;
	font-size: 14px;
	min-width: 1200px;
}
.n-footerWrap .n-footerWrap-left{
	padding-left: 36px;
	display: flex;
	align-items: center;
}
.n-footerWrap .n-footerWrap-right{
	padding-right: 36px;
	display: flex;
	align-items: center;

}


.n-BatchButton {
	position: relative;
}

	.n-BatchButton .icon-a-chevron-up1x {
		display: inline-block;
		font-size: 14px;
		transform: rotate(-180deg);
	}

	.n-BatchButton:hover .icon-a-chevron-up1x {
		display: inline-block;
		font-size: 14px;
		transform: rotate(0deg);
		transition: 0.3s;
	}

.n-BatchButton-ul {
	display: none;
	box-sizing: border-box;
	width: 100%;
	position: absolute;
	left: 0;
	bottom: 35px;
	background-color: #fff;
	color: rgba(0, 0, 0, 0.9);
	flex-direction: column;
	padding: 8px;
	gap: 4px;
	background: #FFFFFF;
	border-radius: 4px;
	/* �в�ͶӰ */
	box-shadow: 0px 12px 24px 0px rgba(0, 0, 0, 0.04),0px 8px 16px 0px rgba(0, 0, 0, 0.04),0px 2px 4px 0px rgba(0, 0, 0, 0.08);
	animation: myBatchButton 0.3s ease-in-out 0s 1 alternate forwards;
	opacity: 0;
}

.n-downBatchButton .n-BatchButton-ul {
	top: 0;
	animation: myBatchButton02 0.3s ease-in-out 0s 1 alternate forwards;
	background-color: #fff;
	height: max-content;
	z-index: 1000;
	width: max-content;
}
	.n-downBatchButton .n-BatchButton-ul:before {
		position: absolute;
		width: 100%;
		height: 5px;
		top: -5px;
		left: 0;
		content: "";
	}

	@keyframes myBatchButton {
		0% {
		opacity: 0;
		bottom: 0;
	}

	100% {
		opacity: 1;
		bottom: 35px;
	}
}

@keyframes myBatchButton02 {
	0% {
		opacity: 0;
		top: 0;
	}

	100% {
		opacity: 1;
		top: 35px;
	}
}

.n-BatchButton-ul::after {
	position: absolute;
	width: 100%;
	height: 5px;
	bottom: -5px;
	left: 0;
	content: "";
}

.n-BatchButton:hover .n-BatchButton-ul {
	display: inline-block;
}

.n-BatchButton-ul > li {
	padding: 6px 8px;
	height: 32px;
	box-sizing: border-box;
	opacity: 1;
	display: flex;
	flex-direction: row;
	align-items: center;
	align-self: stretch;
}

	.n-BatchButton-ul > li:hover {
		border-radius: 4px;
		/* ����ɫ/Gray1 */
		background: rgba(0, 0, 0, 0.04);
	}

.n-newRadio {
	width: 16px;
	height: 16px;
	opacity: 1;
	/* �ı�??Font Wh1 */
	background: rgba(255, 255, 255, 0.9);
	box-sizing: border-box;
	/* ����ɫ/Gray3 */
	border: 1px solid rgba(0, 0, 0, 0.14);
	display: inline-block;
	border-radius: 50%;
	cursor: pointer;
	position: relative;
}

	.n-newRadio.active {
		border: 1px solid #0888FF;
	}

		.n-newRadio.active::after {
			width: 8px;
			height: 8px;
			border-radius: 50%;
			background: #0472F9;
			content: "";
			display: block;
			position: absolute;
			left: 3px;
			top: 3px;
		}

	.n-newRadio.stop {
		background: rgba(0, 0, 0, 0.04);
		box-sizing: border-box;
		/* ����ɫ/Gray1 */
		border: 1px solid rgba(0, 0, 0, 0.04);
	}

	.n-newRadio:hover {
		border: 1px solid #0888FF;
	}

	.n-newRadio.stop:hover {
		border: 1px solid rgba(0, 0, 0, 0.04);
		cursor: not-allowed;
	}

	.n-newRadio.active.stop::after {
		background: rgba(8, 136, 255, 0.1);
		box-sizing: border-box;
		/* Ʒ��??Brand1 */
		border: 1px solid rgba(8, 136, 255, 0.1);
	}

.n-formWrap {
	display: inline-block;
	height: 32px;
	border: 1px solid rgba(0, 0, 0, 0.14);
	border-radius: 4px;
	background-color: #fff;
	position: relative;
}

	.n-formWrap:hover {
		border: 1px solid #0888FF !important
	}

	.n-formWrap:focus-within {
		border: 1px solid #0888FF !important;
		box-shadow: 0px 0px 0px 2px rgb(8 136 255 / 20%) !important;
	}




	.n-formWrap .n-input {
		border: unset;
		padding-left: 0;
	}

		.n-formWrap .n-input:hover:after {
			border: 1px solid #ff0000;
			width: 10px;
			height: 10px;
			position: absolute;
			top: 0;
			left: 0;
			display: block;
			content: "";
		}

.n-formWrap .n-formWrap-title {
	background-color: #fff;
	font-size: 14px;
	padding-left: 8px;
	color: rgba(0, 0, 0, 0.6);
}
.n-focus:focus-within, .n-layui-input.n-focus.active {
	border: 1px solid #0888FF !important;
	box-shadow: 0px 0px 0px 2px rgb(8 136 255 / 20%) !important;
}
.n-flex {
	display:flex;
}
.n-flex-center {
	display: flex;
	justify-content:center;
	align-items:center;
}
#productSku .n-box-nav > .n-box-nav-li {
	font-size: 12px;
}
.n-between {
	justify-content: space-between;
}
.n-center {
	align-items:center;
}
.n-alert .icon-chuyidong {
	cursor: pointer;
	color: rgba(0, 0, 0, 0.6);
	font-size:16px;
}
.new-full-mask.new-full-downTomask .full-mask-content-wrapper.full-mask-right {
	bottom: -1500px;
}

.new-full-mask.new-full-downTomask .full-mask-content-wrapper.full-mask-right {
	bottom: -1500px;
	animation: newFullMask02 0.4s linear 0s 1 alternate forwards;
}

.new-full-mask.new-full-downTomask .full-mask-content-wrapper {
	height: 560px;
	width: 100%;
}

@keyframes newFullMask02 {
	0% {
		bottom: -1500px;
		right: 0;
		opacity: 0;
	}

	100% {
		bottom: 0;
		right: 0;
		opacity: 1;
	}
}
#custom_Table_Wrap tbody tr td.content-CustomOperate {
	background-color: #fff !important;
}


.selectWrap.n-wuSelect .newShowMoreicon {
	position: absolute;
	top: 11px;
	right: 8px;
    z-index: 100;
	transform: none;
	color: rgba(0, 0, 0, .25);
}

/* ���� */
.n-flex {
	display: flex;
}

.n-box-nav {
	width: 100%;
	display: flex;
	flex-direction: row;
}

	.n-box-nav .n-box-nav-li {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 32px;
		flex: 1;
		border: 1px solid rgba(0, 0, 0, 0.14);
		cursor: pointer;
		font-size: 14px;
		font-weight: 500;
		box-sizing: border-box;
	}

		.n-box-nav .n-box-nav-li:nth-child(n+2) {
			margin-left: -1px;
		}

		.n-box-nav .n-box-nav-li:first-child {
			border-top-left-radius: 4px;
			border-bottom-left-radius: 4px;
		}

		.n-box-nav .n-box-nav-li:last-child {
			border-top-right-radius: 4px;
			border-bottom-right-radius: 4px;
		}

		.n-box-nav .n-box-nav-li.active {
			border: 1px solid #0888FF;
			color: #0888FF;
		}

		.n-box-nav .n-box-nav-li:hover {
			border: 1px solid #0888FF;
			color: #0888FF;
		}

		.n-box-nav .n-box-nav-li.stop {
			border-color: rgba(0, 0, 0, 0.14);
			color: rgba(0, 0, 0, 0.3);
			cursor: not-allowed;
			position: relative;
		}



/* ����9-2 */
.selectWrap.n-wuSelect {
	width: 100%;
	font-size: 14px;
}

	.selectWrap.n-wuSelect .selectMore {
		top: unset;
	}

	.selectWrap.n-wuSelect .selectMore {
		height: 32px;
		border-radius: 4px;
		background-color: #fff;
	}

	.selectWrap.n-wuSelect .selectMore-choose > li {
		color: rgba(0, 0, 0, 0.4);
		font-size: 14px;
		display: flex;
		align-items: center;
	}

	.selectWrap.n-wuSelect .showMoreicon {
		right: 13px;
		z-index: 100;
		border-bottom: 1px solid rgba(0, 0, 0, 0.4);
		border-left: 1px solid rgba(0, 0, 0, 0.4);
		width: 5px;
		height: 5px;
		transform: rotate(-45deg);
		border-top: unset;
		border-right: unset;
	}

	.selectWrap.n-wuSelect .selectWrap-box {
		opacity: 0;
		animation: mySelectAnimation 0.3s ease-in-out 0s 1 alternate forwards;
		flex-direction: column;
		padding: 8px;
		background: #FFFFFF;
		border-radius: 4px;
		border: unset;
		box-shadow: 0px 12px 24px 0px rgba(0, 0, 0, 0.04), 0px 8px 16px 0px rgba(0, 0, 0, 0.04), 0px 2px 4px 0px rgba(0, 0, 0, 0.08);
	}

	.selectWrap.n-wuSelect .selectMore:hover {
		border: 1px solid #0888FF !important;
		cursor: pointer;
	}

	.selectWrap.n-wuSelect.active .selectMore {
		border: 1px solid #0888FF !important;
		box-shadow: 0px 0px 0px 2px rgba(8, 136, 255, 0.2) !important;
	}

	.selectWrap.n-wuSelect .selectWrap-box {
		width: 100%;
	}

	.selectWrap.n-wuSelect .selectMore-ul > li {
		padding: 6px 8px;
		box-sizing: border-box;
		color: rgba(0, 0, 0, 0.6);
		cursor: pointer;
		border-radius: 4px;
		display: flex;
		align-items: center;
		position: relative;
	}

		.selectWrap.n-wuSelect .selectMore-ul > li:hover {
			background: rgba(0, 0, 0, 0.04);
		}

		.selectWrap.n-wuSelect .selectMore-ul > li.active {
			background: rgba(0, 0, 0, 0.04);
		}

		.selectWrap.n-wuSelect .selectMore-ul > li > label {
			padding: 0;
			display: flex;
			width: 100%;
			align-items: center;
		}

	.selectWrap.n-wuSelect .list-icon {
		display: none !important;
	}

	.selectWrap.n-wuSelect .selectMore-ul > li {
		margin: 0;
	}

		.selectWrap.n-wuSelect .selectMore-ul > li > label input[type="checkbox"] {
			display: inline-block;
		}

	.selectWrap.n-wuSelect .selectMore {
		display: flex;
		align-items: center;
	}

		.selectWrap.n-wuSelect .selectMore .leftTitle {
			display: inline-block;
			flex: 1;
			word-break: keep-all;
			white-space: nowrap;
			color: rebeccapurple;
			color: rgba(0, 0, 0, 0.6);
			font-size: 14px;
			padding-left: 8px;
			padding-right: 8px;
		}

	.selectWrap.n-wuSelect.activeChoose .selectMore {
		border: 1px solid #0888FF !important;
		box-shadow: 0px 0px 0px 2px rgb(8 136 255 / 20%) !important;
	}

	.selectWrap.n-wuSelect .selectMore::before {
		width: 20px;
		height: 24px;
	}

.n-footerWrap {
	position: fixed;
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	align-items: center;
	height: 56px;
	/* ����ɫ/White */
	background: #FFFFFF;
	box-sizing: border-box;
	/* ����ɫ/Gray3 */
	border-width: 0.5px 0px 0px 0px;
	border-style: solid;
	border-color: rgba(0, 0, 0, 0.14);
	box-shadow: 0px -4px 8px 0px rgba(0, 0, 0, 0.04);
	padding-left: 170px;
	padding-top: 12px;
	padding-bottom: 12px;
	bottom: 0;
	width: 100%;
	left: 0;
	display: flex;
	justify-content: space-between;
	font-size: 14px;
	min-width: 1200px;
}

	.n-footerWrap .n-footerWrap-left {
		padding-left: 36px;
		display: flex;
		align-items: center;
	}

	.n-footerWrap .n-footerWrap-right {
		padding-right: 36px;
		display: flex;
		align-items: center;
	}


.n-BatchButton {
	position: relative;
}

	.n-BatchButton .icon-a-chevron-up1x {
		display: inline-block;
		font-size: 14px;
		transform: rotate(-180deg);
	}

	.n-BatchButton:hover .icon-a-chevron-up1x {
		display: inline-block;
		font-size: 14px;
		transform: rotate(0deg);
		transition: 0.3s;
	}

.n-BatchButton-ul {
	display: none;
	box-sizing: border-box;
	width: 100%;
	position: absolute;
	left: 0;
	bottom: 35px;
	background-color: #fff;
	color: rgba(0, 0, 0, 0.9);
	flex-direction: column;
	padding: 8px;
	gap: 4px;
	background: #FFFFFF;
	border-radius: 4px;
	/* �в�ͶӰ */
	box-shadow: 0px 12px 24px 0px rgba(0, 0, 0, 0.04),0px 8px 16px 0px rgba(0, 0, 0, 0.04),0px 2px 4px 0px rgba(0, 0, 0, 0.08);
	animation: myBatchButton 0.3s ease-in-out 0s 1 alternate forwards;
	opacity: 0;
}

.n-downBatchButton .n-BatchButton-ul {
	top: 0;
	animation: myBatchButton02 0.3s ease-in-out 0s 1 alternate forwards;
	background-color: #fff;
	height: max-content;
	z-index: 1000;
	width: max-content;
}

	.n-downBatchButton .n-BatchButton-ul:before {
		position: absolute;
		width: 100%;
		height: 5px;
		top: -5px;
		left: 0;
		content: "";
	}

@keyframes myBatchButton {
	0% {
		opacity: 0;
		bottom: 0;
	}

	100% {
		opacity: 1;
		bottom: 35px;
	}
}

@keyframes myBatchButton02 {
	0% {
		opacity: 0;
		top: 0;
	}

	100% {
		opacity: 1;
		top: 35px;
	}
}

.n-BatchButton-ul::after {
	position: absolute;
	width: 100%;
	height: 5px;
	bottom: -5px;
	left: 0;
	content: "";
}

.n-BatchButton:hover .n-BatchButton-ul {
	display: inline-block;
}

.n-BatchButton-ul > li {
	padding: 6px 8px;
	height: 32px;
	box-sizing: border-box;
	opacity: 1;
	display: flex;
	flex-direction: row;
	align-items: center;
	align-self: stretch;
}

	.n-BatchButton-ul > li:hover {
		border-radius: 4px;
		/* ����ɫ/Gray1 */
		background: rgba(0, 0, 0, 0.04);
	}

.n-newRadio {
	width: 16px;
	height: 16px;
	opacity: 1;
	/* �ı�ɫ/Font Wh1 */
	background: rgba(255, 255, 255, 0.9);
	box-sizing: border-box;
	/* ����ɫ/Gray3 */
	border: 1px solid rgba(0, 0, 0, 0.14);
	display: inline-block;
	border-radius: 50%;
	cursor: pointer;
	position: relative;
}

	.n-newRadio.active {
		border: 1px solid #0888FF;
	}

		.n-newRadio.active::after {
			width: 8px;
			height: 8px;
			border-radius: 50%;
			background: #0472F9;
			content: "";
			display: block;
			position: absolute;
			left: 3px;
			top: 3px;
		}

	.n-newRadio.stop {
		background: rgba(0, 0, 0, 0.04);
		box-sizing: border-box;
		/* ����ɫ/Gray1 */
		border: 1px solid rgba(0, 0, 0, 0.04);
	}

	.n-newRadio:hover {
		border: 1px solid #0888FF;
	}

	.n-newRadio.stop:hover {
		border: 1px solid rgba(0, 0, 0, 0.04);
		cursor: not-allowed;
	}

	.n-newRadio.active.stop::after {
		background: rgba(8, 136, 255, 0.1);
		box-sizing: border-box;
		/* Ʒ��ɫ/Brand1 */
		border: 1px solid rgba(8, 136, 255, 0.1);
	}

.n-formWrap {
	display: inline-block;
	height: 32px;
	border: 1px solid rgba(0, 0, 0, 0.14);
	border-radius: 4px;
	background-color: #fff;
	position: relative;
}

	.n-formWrap:hover {
		border: 1px solid #0888FF !important
	}

	.n-formWrap:focus-within {
		border: 1px solid #0888FF !important;
		box-shadow: 0px 0px 0px 2px rgb(8 136 255 / 20%) !important;
	}




	.n-formWrap .n-input {
		border: unset;
		padding-left: 0;
	}

		.n-formWrap .n-input:hover:after {
			border: 1px solid #ff0000;
			width: 10px;
			height: 10px;
			position: absolute;
			top: 0;
			left: 0;
			display: block;
			content: "";
		}

	.n-formWrap .n-formWrap-title {
		background-color: #fff;
		font-size: 14px;
		padding-left: 8px;
		color: rgba(0, 0, 0, 0.6);
	}

.n-focus:focus-within, .n-layui-input.n-focus.active {
	border: 1px solid #0888FF !important;
	box-shadow: 0px 0px 0px 2px rgb(8 136 255 / 20%) !important;
}

.n-flex {
	display: flex;
}

.n-flex-center {
	display: flex;
	justify-content: center;
	align-items: center;
}

#productSku .n-box-nav > .n-box-nav-li {
	font-size: 12px;
}

.n-between {
	justify-content: space-between;
}

.n-center {
	align-items: center;
}

.n-alert .icon-chuyidong {
	cursor: pointer;
	color: rgba(0, 0, 0, 0.6);
	font-size: 16px;
}

.new-full-mask.new-full-downTomask .full-mask-content-wrapper.full-mask-right {
	bottom: -1500px;
}

.new-full-mask.new-full-downTomask .full-mask-content-wrapper.full-mask-right {
	bottom: -1500px;
	animation: newFullMask02 0.4s linear 0s 1 alternate forwards;
}

.new-full-mask.new-full-downTomask .full-mask-content-wrapper {
	height: 560px;
	width: 100%;
}

@keyframes newFullMask02 {
	0% {
		bottom: -1500px;
		right: 0;
		opacity: 0;
	}

	100% {
		bottom: 0;
		right: 0;
		opacity: 1;
	}
}


/* ���� */

.selectWrap.n-wuSelect .showMoreicon {
	right: 13px;
	border-right: unset;
}

.selectWrap.n-wuSelect .selectMore-ul > li.stop {
	cursor: not-allowed;
}

	.selectWrap.n-wuSelect .selectMore-ul > li.stop .radio-label {
		color: rgba(0, 0, 0, 0.4);
		cursor: not-allowed;
	}

.selectWrap.n-wuSelect .selectMore-choose > .isRadio > .selectMore-choose-title {
	color: rgba(0, 0, 0, 0.9);
}
.selectWrap.n-wuSelect .selectWrap-search-input {
	height: 32px;
	border-radius: 4px;
}
.selectWrap.n-wuSelect .electWrap_search_wrap {
	box-shadow: unset;
	border-radius: 4px;
}
	.selectWrap.n-wuSelect .electWrap_search_wrap::before {
		top: 8px;
	}
	.selectWrap.n-wuSelect .electWrap_search_wrap::after {
		top: 19px;
	}
	.newVersionsLeftNav .new-full-mask.active{
		z-index: 1000000000;
	}      

/* 分页组件 新样式 */
.layui-laypage-n-page {
    display: flex;
	align-items: center;
	height: 32px;
	margin: 0;
}
.layui-laypage-n-page span.layui-laypage-count {
	border: unset;
	margin-right: 8px !important;
	padding: 0 !important;
}
.layui-laypage-n-page span.layui-laypage-limits {
	border-left: unset !important;
	margin-left: 8px !important;
	margin-right: 0 !important;
	position: relative;
	padding: 0 !important;
}
.layui-laypage-n-page span.layui-laypage-limits select {
	border-radius: 4px;
	height: 32px;
	box-sizing: border-box;
	font-size: 14px;
	color: rgba(0, 0, 0, 0.9);
	appearance: none; /* 移除默认箭头 */
	position: relative;
	padding: 6px 26px 6px 8px;
}
.layui-laypage-n-page span.layui-laypage-limits::after {
	content: ''; /* 内容为空 */
	position: absolute;
	top: 10px;
	right: 12px; /* 箭头距离右侧的距离 */
	border-bottom: 1px solid rgba(0, 0, 0, 0.4);
	border-left: 1px solid rgba(0, 0, 0, 0.4);
	width: 6px;
	height: 6px;
	border-top: unset;
	border-right: unset;
	transform: rotate(-45deg);
}
.layui-laypage-n-page span.layui-laypage-skip {
	margin-left: 16px !important;
	margin-right: 0 !important;
	padding: 0 !important;
}
.layui-laypage-n-page span.layui-laypage-skip > input {
	border-radius: 4px;
	min-width: 44px;
	height: 32px;
	font-size: 14px;
	margin: 0 8px;
	color: rgba(0, 0, 0, 0.9);
	padding: 6px 8px;
	box-sizing: border-box;
}
.layui-laypage-n-page span.layui-laypage-skip > button {
	border-radius: 4px;
	padding: 0px 12px;
	height: 32px;
	box-sizing: border-box;
	margin-left: 16px;
	color: rgba(0, 0, 0, 0.9);
	font-size: 14px;
}
.layui-laypage-n-page a.layui-laypage-prev {
	border-top-left-radius: 4px;
	border-bottom-left-radius: 4px;
}
.layui-laypage-n-page a.layui-laypage-next {
	border-top-right-radius: 4px;
	border-bottom-right-radius: 4px;
	position: relative;
}
.layui-laypage-n-page a.layui-laypage-prev, .layui-laypage-n-page a.layui-laypage-next {
	padding: 0px 12px !important;
	color: rgba(0, 0, 0, 0.9);
}
.layui-laypage-n-page a.layui-disabled {
	color: rgba(0, 0, 0, 0.3) !important;
}
.layui-laypage-n-page span.layui-laypage-count, .layui-laypage-n-page span.layui-laypage-skip {
	color: rgba(0, 0, 0, 0.6);
}
.layui-laypage-n-page a.layui-laypage-next::after {
	display: block;
	content: "";
	width: 1px;
	height: 28px;
	background-color: #e2e2e2;
	position: absolute;
	right: -1px;
	top: 1px;
}
.layui-laypage-n-page a, .layui-laypage-n-page span {
	font-size: 14px;
	height: 32px !important;
	line-height: 32px !important;
	box-sizing: border-box;
	display: flex;
	align-items: center;
	margin: 0 -1px 0 0 !important;
}
.layui-laypage-n-page a.layui-laypage-prev, .layui-laypage-n-page a.layui-laypage-next, .layui-laypage-n-page span.layui-laypage-limits select, .layui-laypage-n-page span.layui-laypage-skip>input, .layui-laypage-n-page span.layui-laypage-skip>button {
	border-color: rgba(0, 0, 0, 0.14) !important;
}
.layui-laypage-n-page a:hover {
	color:#0888FF;
}
