
.goBack {
    cursor:pointer;
}
.layui-btn-primar {
    border: 1px solid #C9C9C9;
    background-color: #fff;
    color: #555;
}

.layui-btn.layui-btn-primar:hover {
    opacity: .8;
    filter: alpha(opacity=80);
    color: #555;
}

.layui-mywrap {
    padding: 15px;
}

.layui-mywrap-title {
    margin: 15px 0;
    padding: 15px;
    background-color: #f8f8f8;
    color: #333;
    font-size: 14px;
    width: 1070px;
}

.layui-content-ul {
    display: flex;
    flex-direction: column;
}

    .layui-content-ul > li {
        margin-bottom:16px;
        box-sizing: border-box;
        flex-direction:column;
    }

        .layui-content-ul > li  span.layui-content-ul-title {
            margin-bottom: 8px;
            display: inline-block;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.6);
        }
            .layui-content-ul > li > span.layui-content-ul-title .sColor {
                margin-right: 5px;

            }
        .layui-content-ul > li input[type=text] {
            height: 32px;
            border: 1px solid #e6e6e6;
            font-size:14px;
        }

        .layui-content-ul > li .icon-tubiaolunkuo- {
            margin-bottom:8px;
        }

            .layui-content-ul > li .icon-tubiaolunkuo-.skuPic {
                width: 50px;
                height: 50px;
                font-size: 30px;
            }

        .layui-content-ul > li .picTitle {
            color: #04385d;
            padding: 5px 0 0 0;
        }

        .layui-content-ul > li.addSku-wrap {
        }


    /* 		.layui-content-ul .stockup_table_content thead tr th,
		.stockup_table_content tbody tr td {
			border-right: unset;
		} */

    .layui-content-ul .stockup_table_content {
        width: 100%;
        border-left: unset
    }

.batchoperate-table > span {
    font-size: 14px;
    color: #333;
}

.batchoperate-table > i {
    font-size: 14px;
    color: #3aadff;
    cursor: pointer;
    padding-right: 10px;
}

.submitButtons > .layui-btn {
    padding: 0 25px;
}

.addSkuWrap {
    padding: 15px;
    flex: 1;
    display: none;
    border-bottom: 0.5px solid rgba(0, 0, 0, 0.14);
}

#productPic {
    display: none;
}

.productPicWrap {
    display: flex;
}

.productPicShow {
    display: flex;
}

    .productPicShow > .productPic-list {
        margin-right: 15px;
        position: relative;
        border: 1px solid #e2e2e2;
        border-radius: 2px;
        cursor: move;
    }

        .productPicShow > .productPic-list > img {
            width: 85px;
            height: 85px;
        }

        .productPicShow > .productPic-list > .icon-laji {
            width: 85px;
            background: rgba(0, 0, 0, .3);
            height: 20px;
            justify-content: center;
            align-items: center;
            color: #ff511c;
            position: absolute;
            bottom: 0;
            left: 0;
            cursor: pointer;
            display: none;
        }

        .productPicShow > .productPic-list:hover > .icon-laji {
            display: flex;
        }

/* 添加规格属性 */

.addSkuWrap > div > .addSkuWrap-Skus {
    display: flex;
    flex-direction: column;
}

    .addSkuWrap > div > .addSkuWrap-Skus > .chooseSkuName {
        background-color: #f6f9fd;
        padding: 8px 15px;
        display: flex;
        align-items: center;
        position: relative;
    }

        .addSkuWrap > div > .addSkuWrap-Skus > .chooseSkuName > .warn-shanchu {
            position: absolute;
            right: 15px;
            cursor: pointer;
            color: #ff511c;
            margin-left: 10px;
            /* 			display: none; */
        }

        .addSkuWrap > div > .addSkuWrap-Skus > .chooseSkuName:hover > .warn-shanchu {
            display: inline-block;
        }

    .addSkuWrap > div > .addSkuWrap-Skus > .chooseSkuValue {
        padding: 15px;
        line-height: 35px;
        display: flex;
        flex-wrap: wrap;
    }

        .addSkuWrap > div > .addSkuWrap-Skus > .chooseSkuValue > .showSkuValue {
            display: flex;
            flex-wrap: wrap;
        }

            .addSkuWrap > div > .addSkuWrap-Skus > .chooseSkuValue > .showSkuValue > span {
                padding: 0 10px;
                border: 1px solid #d3dfef;
                border-radius: 2px;
                cursor: pointer;
                margin: 0 8px;
                position: relative;
                display: inline-block;
                height: 30px;
                line-height: 30px;
            }

                .addSkuWrap > div > .addSkuWrap-Skus > .chooseSkuValue > .showSkuValue > span > .icon-shanchu {
                    position: absolute;
                    top: -8px;
                    right: -16px;
                    color: #ff511c;
                    display: none;
                }

                .addSkuWrap > div > .addSkuWrap-Skus > .chooseSkuValue > .showSkuValue > span:hover > .icon-shanchu {
                    display: inline-block;
                }

        .addSkuWrap > div > .addSkuWrap-Skus > .chooseSkuValue > .addSkuValue {
            cursor: pointer;
            color: #3aadff;
            margin-left: 10px;
        }

    .addSkuWrap > div > .addSkuWrap-Skus > .chooseSkuName > select {
        height: 30px;
        border: 1px solid #e2e2e2;
        width: 150px;
    }

.sku-title {
    color: #04385d;
}

.inputSkuValu-wrap {
    padding: 15px 15px 0 15px;
    box-sizing: border-box;
}

    .inputSkuValu-wrap > input {
        border: 1px solid #e6e6e6;
        height: 30px;
        width: 250px;
    }

.tableSku-skuShow > span {
    margin-right: 8px;
    color: #04385d;
}

    .tableSku-skuShow > span > i {
        color: #888;
    }

.table-tbody-skuImg {
    position: relative;
}

    .table-tbody-skuImg > img {
        width: 52px;
        height: 52px;
    }

    .table-tbody-skuImg > .icon-laji {
        width: 55px;
        background: rgba(0, 0, 0, .3);
        height: 18px;
        justify-content: center;
        align-items: center;
        color: #ff511c;
        position: absolute;
        bottom: 0;
        left: 0;
        cursor: pointer;
        display: none;
    }

    .table-tbody-skuImg:hover > .icon-laji {
        display: flex;
    }

.layui-content-ul > li .noInputData {
    color: #ff511c;
    width: 100px;
    text-align: left;
    padding-left: 5px;
}

.activeBorderColor {
    border: 1px solid #ff511c !important;
}

/* 		.layui-content-ul>li .warnTar{
			display: none;
		} */
.stockup_table_content tbody tr td {
    position: relative;
}

    .stockup_table_content tbody tr td .noInputData {
        color: #ff511c;
        position: absolute;
        bottom: 0;
        left: 10px;
    }

.stockup_table_content tbody tr:hover {
    background-color: #fbfff7;
}

.icon-xiayi {
    display: inline-block;
    transform: rotate(90deg);
    position: relative;
    top: 1px;
    margin-right:10px;
    font-size:16px;
    opacity:0.6;
    cursor:pointer;
}

.layui-mywrap-title .icon-xiangxia {
    font-size: 12px;
    margin-left: 5px;
    color: #333;
}

.layui-mywrap .sColor {
    position: relative;
    top: 3px;
    margin-right: 5px;
}

.newProductPicShow {
    display: flex;
    flex-wrap:wrap;
}

    .newProductPicShow-item {
        align-items: center;
        background-color: #fff;
        border: 1px dashed #dcdee1;
        border-radius: 4px;
        color: #565960;
        display: inline-flex;
        font-size: 12px;
        justify-content: center;
        justify-content: space-evenly;
        overflow: hidden;
        position: relative;
        text-align: center;
        margin-right: 8px;
        width: 80px;
        height: 80px;
        border-radius: 4px;
        align-items: center;
        cursor: pointer;
        display: inline-flex;
        flex-direction: column;
        justify-content: center;
        overflow: hidden;
        position: relative;
        background: rgba(0, 0, 0, 0.04);
    }
li.newProductPicShow-item {
    border: 1px solid #dcdee1;
}

        .newSkuPicShow-item {
            position: relative;
            height: 100%;
            width: 100%;
        }

    .newSkuPicShow-item:hover .icon-laji {
        display: flex;
    }

    .newSkuPicShow-item .icon-laji {
        height: 15px;
        line-height: 16px;
        justify-content: center;
        align-items: center;
        color: #fff;
        cursor: pointer;
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        font-size: 13px;
        background: rgba(0, 0, 0, 0.4);
        display:none;
    }
    .newSkuPicShow-item:hover .icon-laji {
        display: block;
    }
.pic-operateWrap {
    width:100%;
    height:100%;
    background:rgba(0,0,0,0.6);
    position:absolute;
    top:0;
    left:0;
    display:none;
}
.newProductPicShow-item:hover .pic-operateWrap {
    display: block;
}
.pic-operateWrap-up {
    display: flex;
    justify-content:space-between;
    padding: 8px;
}
    .pic-operateWrap-up .iconfont {
        color: #fff;
        font-size:18px;
        cursor:pointer;
    }
        .pic-operateWrap-up .iconfont.icon-yidongshu {
            cursor:move;
        }
    .pic-operateWrap-up .icon-icon_shanchu- {
        font-size:19px;
    }
        .newSkuPicShow-item:hover .icon-laji {
            display: flex;
        }

.icon-jia-copy1 {
    transform: rotate(0deg);
    font-size: 18px;
    margin-bottom: 10px;
}

.newProductPicShow-item-fade {
    background: #3aadff;
    color: #fff;
    font-size: 12px;
    height: 18px;
    left: 0;
    line-height: 18px;
    position: absolute;
    right: 0;
    text-align: center;
    top: 0;
}

.decorateWrap {
    background: #fff;
    border: 1px dashed #dcdee1;
    border-radius: 4px;
    box-sizing: border-box;
    padding: 16px;
    padding-bottom: 0;
    flex: 1;
    max-height: 700px;
    overflow-y: auto;
    background-color: #f5f5f5;
}

.decorateContent-up {
    position: relative;
    display:flex;
    align-items:center;
}


.decorateContent-up-aXinc {
    lign-items: center;
    color: #333;
    display: flex;
    font-size: 14px;
    line-height: 20px;
    margin-bottom: 4px;
}

.decorateContent-up-aXinc-ImgEditDesc {
    color: #999;
    font-size: 12px;
    line-height: 20px;
    margin-left: 16px;
}

.showSkuValue-item {
    display: flex;
    align-items: center;
    flex-direction: column;
    margin-left: 26px;
    margin-bottom: 4px;
}
.showSkuValue-item.addNewSkuValue {
    margin-bottom:16px;
}
.showSkuValue-item-up {
    width: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}
#addSkuWrap_Skus_ul .addSkuWrap-Skus .addNewSkuValue .showSkuValue-item-up .n-layui-input {
    width:723px;
}
.showSkuValue-item-up .icon-icon_shanchu- {
    position: absolute;
    right: 8px;
    color: #888;
    top: 7px;
    font-size: 18px;
    cursor: pointer;
}

.showSkuValue-item-down {
    border: 1px dashed #dcdee1;
    border-radius: 4px;
    color: #565960;
    font-size: 12px;
    text-align: center;
    margin-right: 4px;
    width: 32px;
    height: 32px;
    min-width:32px;
    border-radius: 2px;
    cursor: pointer;
    overflow: hidden;
    position: relative;
    display: flex;
    flex-direction: column;
    display: flex;
    box-sizing: border-box;
    background-color: #f5f5f5;
    justify-content:center;
    align-items:center;
}
    .showSkuValue-item-down .layui-input {
        flex: 1;
        width: unset;
    }

.showSkuValue-item-down .icon-jia-copy1 {
    position: relative;
    top: 5px;
}

    .showSkuValue-item-down i {
        position: relative;
        top: -12px;
    }
    .showSkuValue-item-down .n-layui-input {
        width:unset;
        flex:1;
    }

    .getProductBtn {
        width: 135px;
        height: 35px;
        border: 1px solid #f59c1a;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #f59c1a;
        border-radius: 2px;
        cursor: pointer;
        position: absolute;
        left: 1150px;
        top: 25px;
    }

.my-form-switch {
    height: 16px;
    line-height: 16px;
}

    .my-form-switch i {
        top: 2px;
    }

    .my-form-switch em {
        width: 15px;
    }

.showSkuValue-item-down-img {
    width: 100%;
    height: 100%;
}

#table_tbody_orderList input[type=text], #newSkuTableWrap input[type=text] {
    height: 30px;
    border: 1px solid #e6e6e6;
    width: 100%;
    box-sizing:border-box;
}
#newSkuTableWrap .ShortTitle .n-layui-input {
    padding-right: 50px;
}

.decorateContent-down {
    padding-bottom: 16px;
}
.decorateContent-down .productDetailsPic-item {
    max-width: 100%;
    display: inline-block;
}

    .decorateContent-down .productDetailsPic-item img {
        max-width: 100%;
        position: relative;
    }

    .decorateContent-down .productDetailsPic-item:hover {
        box-shadow: 0 0 8px 0px #fc7748;
    }

.productDetailsPic-item {
    margin-top: 15px;
    position: relative;
}

    .productDetailsPic-item:hover .icon-chuyidong {
        display: block;
    }

    .productDetailsPic-item .icon-chuyidong {
        width: 20px;
        height: 20px;
        background: rgba(0, 0, 0, 0.5);
        position: absolute;
        top: 0;
        right: 0;
        border-radius: 0 0 0 60px;
        font-size: 16px;
        color: #fff;
        text-align: center;
        cursor: pointer;
        display: none;
    }

.newProductPicShow-item {
    position: relative;
}

    .newProductPicShow-item:hover .icon-laji {
        display: block;
    }

    .newProductPicShow-item .icon-laji {
        width: 100%;
        background: rgba(0, 0, 0, .3);
        height: 22px;
        line-height: 22px;
        justify-content: center;
        align-items: center;
        color: #ff511c;
        position: absolute;
        bottom: 0;
        left: 0;
        cursor: pointer;
        display: none;
    }

.showSkuValue-item-down-upimg {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    justify-content: center;
    align-items: center;
}

.table-sku-img {
    width: 50px;
    height: 50px;
}

.icon-laji {
    cursor: pointer;
}

.skuJurisdictionSelect {
    height: 30px;
    border: 1px solid #e6e6e6;
    width: 100%;
}

.img_div {
}

    .img_div img {
        width: 100px;
        height: 100px;
    }

.productSkuSpec {
    background-color: #f5f5f5;
}

.popoverCommon-warn {
    bottom: 45px;
}

.popoverCommon03 .popoverCommon-warn {
    background-color: #000;
    color: #fff;
    left: -1px;
}

    .popoverCommon03 .popoverCommon-warn::after {
        display: none;
    }
#skuTable .n-table thead tr {
    position: sticky;
    position: -webkit-sticky;
    top: -1px;
    z-index: 9;
}
#skuTable .stockup_table_content thead tr th,
.stockup_table_content tbody tr td {
    text-align: center;
}

#createCustonSku {
    width: 100%;
    box-sizing: border-box;
    height: 32px;
}
.getproductlist-mytable {
    width:780px;
    box-sizing:border-box;
    display:none;
}
.getproductlist-mytable .productShow img {
    width: 50px;
    height: 50px;
    margin-right: 3px;
}

.getproductlist-mytable {
    padding: 15px 15px 0 15px;
    height: 580px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
}

    .getproductlist-mytable .productShow {
        display: flex;
    }

        .getproductlist-mytable .productShow > ul {
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            height: auto;
            flex:1;
        }

            .getproductlist-mytable .productShow > ul > li {
                text-align: left;
            }

    .getproductlist-mytable .mysearch-partOne {
        margin-bottom: 0;
    }

    .getproductlist-mytable .layui-form {
        margin-bottom: 15px;
    }

    .getproductlist-mytable .stockup_table_content thead tr th,
    .getproductlist-mytable .stockup_table_content tbody tr td {
        padding: 5px;
    }

    .getproductlist-mytable .tableWrap {
        flex: 1;
        display: flex;
        flex: 1;
        overflow-y: auto;
    }


.adialog-bindsupplier {
    background-color: #fff;
}

.bindsupplier-main-ul {
    background-color: #f8f8f8;
    display: none;
    flex-direction: column;
    padding: 5px;
}

    .bindsupplier-main-ul .bindsupplier-main-ul-title {
        margin-bottom: 15px;
        font-size: 14px;
        color: #04385d;
    }

    .bindsupplier-main-ul .bindsupplier-main-ul-li {
        margin-bottom: 15px;
    }

        .bindsupplier-main-ul .bindsupplier-main-ul-li:last-child {
            margin-bottom: 0;
        }

.mySelectWrap {
    background-color: #eef8ff;
    color: #3aadff;
    border: 1px solid #c9def6;
}

.adialog-bindsupplier .select-supplier {
    display: none;
}

.adialog-bindsupplier .bindsupplier-main {
    background-color: #f5f5f5;
}

.mySelectWrap .mySelectWrap-exportText {
    color: #3aadff;
}

.mySelectWrap .mySelectWrap-options > li > a {
    display: inline-block;
    width: 100%;
}

.selectWrap .selectMore {
    height: 35px;
    background-color: #fff;
    line-height: 35px;
    cursor: pointer;
}

.selectWrap .showMoreicon {
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid #c2c2c2;
    position: absolute;
    top: 7px;
    right: 8px;
    z-index: 100;
    top: 18px !important;
}

body .selectWrap-box {
    top: 40px;
}

.selectMore-choose-title {
    height: 35px;
}
.batchMappingSupplier-skin .selectWrap {
    width: 250px;
}
.batchMappingSupplier-skin .selectMore-choose > .isRadio > .selectMore-choose-title {
    width: 220px;
}
.newProductPicShow .newProductPicShow-item > img {
    width:100%;
    height:100%;
}
.batchMappingSupplier-skin .selectMore-ul {
    max-height: 220px;
}

.getproductlist-mytable .stockup_table_content {
    height: max-content;
}
.setStockCount-warn {
    display:flex;
    flex-direction:column;
}
.StockCount .popoverCommon-warn {
    bottom: 20px;
}
.StockCount .popoverCommon-warn .hover:hover {
    opacity:0.8;
}
.edmitStockCountWrap {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items:center;
}
.wareHouseNum {
    display: none;
    align-items: center;   
}
    .wareHouseNum.active {
        display: flex;
    }
    .wareHouseNum.hide {
        display: none
    }

     .wareHouseNum > input {
        width: 50px!important;
        height: 28px;
        border: 1px solid #e2e2e2;
        padding-left: 3px;
        box-sizing: border-box;
        color: #666;
        font-size: 12px;
        display: none
    }

        .wareHouseNum > input.active {
            display: block
        }

    .wareHouseNum > i {
        width: 20px;
        height: 12px;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #e2e2e2;
        cursor: pointer;
        line-height: 12px;
        font-size: 14px;
        color: #888;
        margin: 0 3px
    }

.showOrHideSku {
    display: flex;
    align-items: center;
    margin: 0 15px;
    color: #3aadff
}

.layui-mytable-operate .iconfont {
    font-size: 14px !important;
    margin-right: 3px
}

.wareHouseNum > .wareHouseNumShow {
    min-width: 35px;
    text-align: right;
    display: inline-block;
    color: #3aadff
}

.wareHouseTitle {
    margin-left: 5px
}
.operateStockCount {
    display:flex;
    align-items:center;
    margin-top:5px;
}


.createStoreDialog .createStoreDialog-main.address {
    display: flex;
    align-items: center;
}

    .createStoreDialog .createStoreDialog-main.address input[type=text], .createStoreDialog .createStoreDialog-main.address select {
        width: 108px;
        border: 1px solid #e2e2e2;
        box-sizing: border-box;
        height: 35px;
        margin-right: 15px;
    }

.layui-input-inline {
}

#newCreateBtnWrap {
    margin-bottom: 15px;
    display: none;
}

.default-address {
    display: flex;
    align-items: center;
}

.default-address > input {
    margin-right: 3px;
}
.createStoreDialog {
    width: 560px;
    background-color: #fff;
    padding: 15px;
    box-sizing: border-box;
    display: none;
}

.createStoreDialog-content > li {
    padding: 10px 0;
    display: flex;
    align-items: center;
}

    .createStoreDialog-content > li.lispantop {
        align-items: flex-start;
    }

    .createStoreDialog-content > li > .createStoreDialog-title {
        color: #04385d;
        width: 80px;
        text-align: right;
    }

.adialog-addSupplier ul > li.adialog-addSupplier-select > label {
    display: inline-block;
    margin-right: 10px;
}

.createStoreDialog-main-select > label {
    display: inline-block;
    margin-right: 10px;
}

    .createStoreDialog-main-select > label select {
        width: 108px;
        border: 1px solid #e2e2e2;
        box-sizing: border-box;
        height: 35px;
    }

.createStoreDialog-main-select > .adialog-addSupplier-textarea {
    margin-top: 15px;
}

    .createStoreDialog-main-select > .adialog-addSupplier-textarea > textarea {
        width: 345px;
        height: 40px;
        border: 1px solid #e2e2e2;
        border-radius: 2px;
        padding-left: 5px;
    }

.createStoreDialog .createStoreDialog-main-contactWrap {
    display: flex;
    flex-direction: column;
    background-color: #f8f8f8;
    padding: 10px;
    width: 360px;
}

.createStoreDialog .createStoreDialog-main-contact {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

    .createStoreDialog .createStoreDialog-main-contact input {
        flex: 1;
    }

    .createStoreDialog .createStoreDialog-main-contact > span {
        width: 70px;
        display: inline-block;
        text-align: right;
        padding-right: 5px;
    }

        .createStoreDialog .createStoreDialog-main-contact > span > i {
            color: #ff511c;
            margin-right: 5px;
        }

.checkCodeWarn {
    padding: 15px;
    font-size: 16px;
    color: #fe6f4f;
}

.checkCodeWarn .checkCodeWarn-item {
    padding:5px 0;
}
.checkCodeWarnSkin .layui-layer-btn .layui-layer-btn1 {
    border-color: #1E9FFF;
    background-color: #1E9FFF;
    color: #fff;
}
.checkCodeWarnSkin .layui-layer-btn .layui-layer-btn0 {
    border: 1px solid #dedede;
    background-color: #fff;
    color: #333;
}
.setStockCountWrap {
    width:50px;
}
td.StockCount {
    text-align:center;
}
td.InOrOut {
    display: none;
}
.warnTitle {
    color: #fe6f4f;
    margin-left: 5px;
    display: none;
    position: absolute;
    top: 32px;
}
.addWarnInput {
    border: 1px solid #fe6f4f!important;
}

.n-breadcrumb {
    justify-content:space-between;
}

.createBaseProductWrap {
    width: 100%;
    box-sizing: border-box;
}
.n-main {
    display: flex;
    justify-content: space-between;
    padding: 16px 16px 0px 16px;
}
#distributionSet_list .n-main {
    padding: 16px 16px 73px 16px;
}
.n-inputWrap {
    position: relative;
    display: flex;
    align-items: center;
}
.icon-tubiaolunkuo- {
    font-size:16px;
    color:#000;
}
.addSkuAttributesWrap {
    padding: 16px;

}
#setSkuAttr_content {
    border-radius: 10px;
    border: 0.5px solid #e2e2e2;
}
.addSkuBtn {
    font-size: 14px;
    display: flex;
    align-items: center;
}
.icon-jia-copy1 {

}
.onloadPic-icon {
    width:16px;
    height:16px;
    display:inline-block;
    display:none;
}
.detailsPicsShow {
    display:flex;
    flex-direction:column;
} 
#detailsPicsShow_ul {

}
    #detailsPicsShow_ul .newProductPicShow-item {
        position: relative;
        margin-top: 16px;
    }
.addSkuWrap-Skus {
    padding: 16px 16px 0 16px;
    border-bottom: 1px solid #e2e2e2
}
.addSkuWrap-Skus-main-title {
    padding: 0 0 8px 28px;
    display: flex;
    justify-content: space-between;
    color: rgba(0, 0, 0, 0.6)
}
.addSkuWrap-Skus-main-title2 {
    padding: 6px 8px;
    background: #f5f5f5;
    box-sizing: border-box;
    border: 1px solid rgba(0, 0, 0, 0.04);
    border-radius: 4px;
    width: 100%;
}

.addSkuWrap-Skus-main-title2::after {
    position: absolute;
    content: '';
    width: 15px;
    height: 25px;
    background-color: #f5f5f5;
    top: 3px;
    right: 3px;
    z-index: 2;
}
.addSkuWrap-Skus-main-select .icon-yidongshu, .addSkuWrap-Skus-main-select  .icon-a-move1x1 {
    margin-right: 11px;
    cursor: move;
    color:#999;
}
.addSkuWrap-Skus-main-select {
    display:flex;
/*    align-items:center;
*/    position:relative;
}
.addSkuWrap-Skus-main-select::after {
    position: absolute;
    content: '';
    width: 15px;
    height: 25px;
    background-color: #fff;
    top: 3px;
    right: 3px;
}
.addSkuWrap-noEdit .addSkuWrap-Skus-main-select::after {
    display:none;
}
.addSkuWrap-noEdit .n-active:hover {
    color: #565960;
    border: unset;
}

.notMapped.n-select {
    flex: 1;
}
#addSkuWrap > ul > .addSkuWrap-Skus:nth-child(3) {
    border-bottom:unset;
}
.addSkuWrap-Skus-main {
    margin-bottom:16px;
}
.my-form-switch.active em {
    margin-left: 0;
    width: 10px;
    font-size: 12px;
    margin-right: 13px;
}
.showSkuValue-btns {
    display:flex;
    flex-direction:row;
    justify-content:flex-end;
}
.my-form-switch i {
    width: 12px;
    height: 12px;
}
.my-form-switch em {
    font-size:12px;
    margin-left:16px;

}
.my-form-switch {
    min-width:28px;
}
.my-form-switch.active i {
    margin-left: -18px;
}
.showSkuValue-btns .n-pActive {
    background: rgba(234, 87, 46, 0.1);
    color: #EA572E;
    margin-right: 8px;
}
.w-msg {
    font-size: 12px;
    position: fixed;
    z-index: 105000000000000;
    width: 100%;
    top: 0;
    display: flex;
    justify-content: center;
    animation: msgAni 0.6s ease-in-out 0s 1 alternate forwards;
    
}
.w-alert .n-alert {
    display:inline-block;
}


@keyframes msgAni {
    0% {
        top: 0;
    }

    100% {
        top:80px;
    }
}
.noEdit-show .showSkuValue-item {
    margin-left: 0;

}
.noEdit-show .noEdit-showSkuValue-item {
    border-radius: 2px;
    background: rgba(66, 74, 87, 0.1);
    height: 32px;
    align-items: center;
    display: flex;
    margin-right: 8px;
    margin-bottom: 8px;
}
.noEdit-showSkuValue-item-text {
    padding: 0 8px;
    display:inline-block;
}
.noEdit-show .showSkuValue-item-down {
    margin-right: 0;
    border:unset;
}
.noEdit-show {
    display: flex;
    flex-wrap: wrap;
    flex:1;
}
.showSkuValue-item-up .icon-a-move1x1 {
    margin-right: 11px;
    cursor: move;
    color: #999;
}
.skuTableWrap {
    margin:16px 0;
    flex-direction:column;
}
.skuTableWrap-title {
    display:flex;
    justify-content:space-between;
    margin-bottom:8px;
    padding:0 16px;
    box-sizing:border-box;
}
.skuTableWrap-title .skuTableWrap-title-left {
    font-size: 14px;
    color: #666;
    margin-bottom:8px;
    display:inline-block;
}
.skuTableWrap-title .n-dColor {
    font-size: 14px;
}
.skuTableWrap-title .n-dColor .icon-a-fullsreen1x {
    font-size: 14px;
    margin-right:5px;
}
.skuTableWrap .n-table {
    width: 100%;
    overflow-x: auto;
}
#skuTable {
    width: 100%;
    overflow-x: auto;
    max-height:800px;
}
.skuTableWrap.active {
    position: fixed;
    top: -16px;
    background-color: #fff;
    right: 0;
    left: 0;
    bottom: 0;
    display: flex;
    border: 16px solid #edf0f4;
    z-index: 100000;
}

.skuTableWrap.active #skuTable {
    overflow-y: auto;
    box-sizing:border-box;
}
.skuTableWrap.active .skuTableWrap-title {
    padding:16px;
    margin-bottom:0;
}
.skuTableWrap.active #skuTable .n-table {
    width: 100%;
    box-sizing: border-box;
    overflow: hidden;
}
.n-productInfo .n-productInfo-texts {
    margin-left:3px;
    justify-content:space-around;
}
.n-productInfo {
    align-items:center;
}
.n-productInfo .n-inpuCheck {
    margin-right:5px;
}
.n-productInfo-img img {
    border: unset;
}
.thskuHeader {
    display:flex;
    align-items:center;
}
.tdskubodyS {
    display: flex;
    align-items: center;
    /* position:relative; */
}
.thskuHeader input[type=checkbox] {
    margin-right: 5px;
}
.tdskubody {
    display: flex;
    padding-left: 22px;
    align-items:center;
/*    position: relative;
*/}
.tdskubody input[type=checkbox], .tdskubodyS input[type=checkbox] {
    margin-right: 5px;
}
.icon-down {
    cursor: pointer;
    line-height: 19px;
}
.icon-down.active {
    transform: rotate(180deg);
    display: inline-block;
}
.n-productInfo .icon-down {
    line-height: 19px;
    margin-left: 4px;
}
.chx.hide {
    display:none;
}
.priceInputWrap {
    display:flex;
    align-items:center;
    position:relative;
}
.priceInputWrap .priceIcon {
    position:absolute;
    left:5px;
    top:7px;
}
.priceInputWrap .n-layui-input.priceInput {
    padding-left: 18px;
}
.headerTr .icon-a-edit1x {
    font-size:14px;
    margin-left:5px;
    cursor:pointer;
}
.batchSku-skin .layui-layer-content {
    padding:0 0 15px 0!important;
}
.batchSku-skin .layui-layer-btn {
    padding-bottom:0;
}
.batchSkus-title {
    font-size: 14px;
    line-height: 20px;
    color: #666;
    margin-bottom:8px;
}
.batchSkus-content {
   padding:16px;
}
.batchSkus-inputWrap {
    position: relative;
    display: flex;
    align-items:center;
}
.batchSkus-inputWrap .priceNum {
    position:absolute;
    left:5px;
}
.batchSkus-inputWrap .priceInput {
    padding-left:18px;
    width:100%;
    box-sizing:border-box;
}
.batchSku-skin .layui-layer-btn {
    display: flex;
    justify-content: space-between;
    align-items:center;
}
.batchSku-skin .layui-layer-btn-text {
    font-size:14px;
}
.batchSku-skin .layui-layer-btn-text i{
    color:#000;
    margin:0 3px;
}
.n-skin .layui-layer-btn a.stop {
    opacity:0.5;
    cursor:not-allowed;
}
.n-skin .layui-layer-btn {
    padding: 16px 16px 0 16px;
}
.stockCountskin.n-skin .layui-layer-btn {
    padding:12px 16px;
}
.chx.chxActive {
    background-color: #f5f5f5;
}
.rightSpan s {
    cursor: pointer;
}
.rightSpan s:hover {
    color: #0888FF;
}
.custonSkuskin .layui-layer-btn {
    padding-bottom:16px;
}
#skuTable .icon-a-edit1x {
    display: none;
}

.order-column-header-StockCount .icon-a-help-circle1x,
.order-column-header-SkuCode .icon-a-help-circle1x,
.order-column-header-ShortTitle .icon-a-help-circle1x {
    display: none;
}

th .icon-a-help-circle1x.active {
    display: inline-block!important;
}
.order-column-header-StockCount .icon-a-help-circle1x.active,
.order-column-header-SkuCode .icon-a-help-circle1x.active,
.order-column-header-ShortTitle .icon-a-help-circle1x.active {
    display: none !important;
}

#skuTable th.active .icon-a-edit1x {
    display: inline-block;
}
th.active .icon-a-help-circle1x {
    display: none;
}
th .rightSpan {
    display:none;
}
th.active .rightSpan {
    display: inline-block;
}
.goBackskin.n-skin .layui-layer-btn {
    padding:12px 16px;
}
.goBackskin .layui-layer-btn a:last-child {
    background-color: #EA572E;
    border: 1px solid #EA572E;
}
.skuCode-toast {
    position: absolute;
    bottom: 33px;
    z-index: 1000;
    width: 100%;
}
.skuCode-toast {
    display:none;
}
.priceInputWrap-left {
    position:relative;
    flex:1;
    display:flex;
}
.stockCountIcons {
    width: 24px;
    background-color: #f5f5f5;
    border-radius: 2px;
    position: absolute;
    left: 2px;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    overflow: hidden;
    top:3px;
}
.stockCountIcons i {
    height:13px;
    line-height:14px;
    font-size:14px;
    cursor:pointer;
}
.stockCountIcons i.active {
    background-color: #0888FF;
    color:#fff;
}
.stockCountText {
    color: rgba(0, 0, 0, 0.4);
    position: absolute;
    right: 45px;
    font-size: 14px;
}
.rawSkusWrap-main-item img {
    
}
.rawSkusWrap-main-item {
    display: flex;
    padding: 6px 8px;
}
    .rawSkusWrap-main-item:hover {
        background: rgba(0, 0, 0, 0.04);
    }
    .rawSkusWrap-main-item .n-font4 {
        line-height: 20px;
    }
    .rawSkusWrap-main-item .n-font6 {
        color: rgba(0, 0, 0, 0.6)
    }
.chooseSku .chooseSku-title {
    color: rgba(0, 0, 0, 0.4);
    font-size:12px;

}
.chooseSku .rawSkusWrap-main-item {
    opacity:0.4;
}
.chooseSku .rawSkusWrap-main-item:hover {
    background: unset;
}

.matchedMoreSku {
    cursor:pointer;
}
.matchedMoreSku .icon-down {
    font-size: 12px;
    margin-left: 5px;
}
.moreSku-title {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.9);
    display: inline-block;
    flex:1;
}
.rawSkusWrap {
    position: fixed;
    width: 400px;
    max-height: 280px;
    overflow-y: auto;
    border-radius: 4px;
    opacity: 1;
    display: flex;
    flex-direction: column;
    padding: 8px;
    gap: 4px;
    background: #FFFFFF;
    box-shadow: 0px 12px 24px 0px rgba(0, 0, 0, 0.04), 0px 8px 16px 0px rgba(0, 0, 0, 0.04), 0px 2px 4px 0px rgba(0, 0, 0, 0.08);
    z-index: 1000;

}
.matchedMoreSku.active {
    color: #0888FF;
}
.icon-a-close-circle-filled1x {
    font-size: 14px;
    margin-left: 5px;
    color: rgba(0, 0, 0, 0.4);
    display:none;
}
.matchedMoreSku.active .icon-a-close-circle-filled1x {
    display:inline-block;
}
.matchedMoreSku-title {
    color: rgba(0, 0, 0, 0.6);
}
.matchedMoreSku-title2 {
    display: none;
    color: rgba(0, 0, 0, 0.6);
}
.matchedMoreSku.active .matchedMoreSku-title2 {
    display: inline-block;
}
.n-productInfo-right {
    flex:1;
}

.tdskubodyS .newStatus {
    display:none;
}
.edmitStockCountText {
    min-width: 72px;
    height: 32px;
    border-radius: 4px;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 6px 8px;
    background: rgba(0, 0, 0, 0.04);
    box-sizing: border-box;
    border: 1px solid rgba(0, 0, 0, 0.04);
    margin-right:2px;
}
.checkCodeWarnSkin .layui-layer-btn {
    padding-bottom:16px;
}
.edmitTable thead .icon-a-edit1x {
    display: none!important;
}
/*.edmitTable.activeEdmitTable thead .icon-a-edit1x {
    display: inline-block !important;
}*/
/*.edmitTable thead .icon-a-help-circle1x {
    display: inline-block !important;
}
*/
/*.edmitTable.activeEdmitTable thead .icon-a-help-circle1x {
    display: none !important;
}*/
.n-breadcrumb-right {
    min-width:200px;
    display:flex;
    justify-content:flex-end;
}
.n-breadcrumb {
    width: 960px;
    box-sizing: border-box;
    gap: 0;
}
.n-breadcrumb .n-breadcrumb-left {
    width: 656px;
    display: flex;
    align-items: center;
}
.showSkuValue {
    max-height: 500px;
    overflow-y: auto;
}
.matchedMoreSku-title-wrap {
    display:inline-block;
}
.matchedMoreSku.active .matchedMoreSku-title-wrap {
    display: none;
}
.showSkuValue-btns .n-pActive:hover {
    background: rgba(234, 87, 46, 0.2);
    color: #eb5126;
}
#skuTable .n-table thead tr th:first-child, #skuTable .n-table tbody tr td:first-child {
    padding-left:16px;
}
#skuTable .n-table tbody tr td:first-child {
    position:relative;
}
#skuTable .n-table thead tr th:last-child, #skuTable .n-table tbody tr td:last-child {
    padding-right: 16px;
}
.SkuCode .n-layui-input, .ShortTitle .n-layui-input{
    padding-left: 8px;
}
#addSkuWrap_Skus_ul .addSkuWrap-Skus .addNewSkuValue.frist-item .showSkuValue-item-up .n-layui-input {
    width: 750px;
}
.headerTr {
    display:flex;
    align-items:center;
}
.SkuCode .n-toast i.n-dColor {
    font-size: 14px;
}
.th-tooltip {
    position:fixed;
}
/*.headerTr .icon-a-help-circle1x {
    position:relative;
}*/
.headerTr .n-tooltip {
    position: fixed;
    width: 250px;
    margin-top: -60px;
    margin-left: -12px;
    display:none;
}
.headerTr .icon-a-help-circle1x:hover .n-tooltip {
    display: block;
}
.headerTr {
    font-size:14px;
}
.showSkuValue-item-down.n-active02:hover {
    border: 1px solid #0888FF;
    border-radius: 4px;
}
    .showSkuValue-item-down.n-active02:hover .icon-a-image-add1x1 {

        color: #0888FF;
    }
.order-column-header-SkuName {
    font-size:14px;
}

.newStatusWrap {
    position: absolute;
    left: 0;
    width: 18px;
    height: 100%;
    display: flex;
    align-items: center;
    top: 0;
}
.newStatus {
    width: 18px;
    height: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    line-height: unset;
    padding: 0;
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}
.checkCodeWarnSkin.n-skin .layui-layer-btn a:nth-child(1):hover {
    color: #0888FF;
}
.createBase-footer {
    position: fixed;
    bottom: 0;
    right: 0;
    display: flex;
    justify-content: flex-end;
    background-color: #fff;
    width: 905px;
    padding: 12px 16px;
    box-sizing: border-box;
    border-width: 0.5px 0px 0px 0px;
    border-style: solid;
    border-color: rgba(0, 0, 0, 0.09);
}
.allUpAndDown{
    display: none;
}
body{
    background: unset!important;
}
.newCreateProductWrap{
    display: flex;
    flex-direction: row;
}
.newCreateProductWrap .n-writtenWrp{
    height: fit-content;
}
.newCreateProductWrap .newCreateProductWrap-left{
    width: 200px;
    height:100vh;
}
.newCreateProductWrap .n-writtenWrp {
    position: fixed;
    top: 120px;
    left: 0;
}

    .newCreateProductWrap .full-mask-content-wrapper {
        position:absolute;
        top:0;
        bottom:0;
        right:0;
        height:100%;
        display:flex;
        flex-direction:column;
    }
        .newCreateProductWrap .full-mask-content-wrapper .full-mask-content {
            flex:1;
            overflow-y:auto;
        }

.n-productInfo-title {
    display:flex;
}
.n-productInfo-left {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width:200px;
}
.intelligence-categoryMeu {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 8px;
    padding:8px 12px;
    background: rgba(0, 0, 0, 0.04);
    margin-top:8px;
}
    .intelligence-categoryMeu .intelligence-title {
        font-family: Source Han Sans;
        font-size: 14px;
        font-weight: 600;
        line-height: 20px;
        letter-spacing: 0em;
        font-variation-settings: "opsz" auto;
        /* 正确色/Success5 */
        color: #73AC1F;
        margin-bottom:4px;
    }
    .intelligence-categoryMeu .intelligence-categoryMeuInfo {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.9);
    }
    .catePropsWrap {
    display: flex;
    flex-wrap: wrap;
}
.catePropsWrap > .catePropsItemWrap {
    margin-right: 8px;
    width: 269px;
    margin-top: 8px;
    display:flex;
    flex-direction:column;
}
.catePropsWrap > .catePropsItemWrap:nth-child(3n) {
    margin-right: 0;
}
.catePropsWrap > .catePropsItemWrap .catePropsItem {
    width:100%;
}
    .catePropsWrap > .catePropsItemWrap .n-sColor {
        margin-right: 4px;
        display: inline-block;
        color: #EA572E;
    }
    .catePropsWrap .n-mySelect-input {
        height: auto !important;
        border: unset !important;
        flex: 1;
        padding-left: 5px;
    }

.multiValueMeasureWrap {
    display: flex;
    flex: 1;
    align-items: baseline;
}
.n-mySelect-title-left-perIcon {
    position:absolute;
    right:8px;

}
.catePropsItemWrap {
    position:relative;
}
.catePropsItemWrap .requiredIcon {
    position: absolute;
    left: 5px;
    z-index: 100;
    top:10px;
}
    .catePropsItemWrap.required .n-mySelect .n-mySelect-title {
        padding-left:10px;
    }
.multiValueMeasureWrap  .icon-a-delete1x {
    cursor:pointer;
    color:rgba(0,0,0,0.6);
    display:inline-block;
    margin-left:4px;
}
.catePropsBtnWrap {
    margin-top:8px;
}
#dateTimeRange {
    height: unset;
    border: unset;
}
.categoryMeuItem.formWarn .categoryMeu-wrap .categoryMeu-title {
    border: 1px solid #fe6f4f !important;
}
.categoryMeuItem.formWarn .input-warnTitle {
    display:block;
}
.formWarn .n-mySelect {
    border: 1px solid #fe6f4f;
}
.catePropsWrap > .catePropsItemWrap.formWarn .input-warnTitle {
    display:block;
}
.layui-content-ul .layui-content-ul-li-item {
    flex:1;
}
.layui-content-ul-li-item .n-inputWrap {
    width:100%;
}
.layui-content-ul-li-item .n-mySelect {
    width: 100%;
}
.layui-content-ul-li-item layui-content-li-icons {
   display:flex;
}
.layui-content-ul-li-item .layui-content-li-icons .layui-content-li-icons-item {
    display: inline-block;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-right:4px;
}
.setPrivacy {
    display: flex;
    flex-direction: row;
    font-size: 14px;
    color: #3D3D3D;
}
.setPrivacy .setPrivacy-right {
    display: flex;
    flex-direction: row;
    align-items:center;
}
.setPrivacy .setPrivacy-right label {
    margin-left:16px;
    display:flex;
    align-items:center;
}
.setPrivacy .setPrivacy-right label span {
    display:inline-block;
    margin:0 3px;
}
.setPrivacy .setPrivacy-left {
    color: #3D3D3D;
}
.setPrivacy-right .icon-a-help-circle1x {
    margin-left: 0;
    position:relative;
}
.setPrivacy-right .n-tooltip {
    position: absolute;
    bottom: 22px;
    left: -10px;
    width: 280px;
    display:none;
}
.setPrivacy-right .icon-a-help-circle1x:hover .n-tooltip {
    display: flex;
}
.edmitBody #setSkuAttr_content .showSkuValue-btns .n-pActive, .batchBody #setSkuAttr_content .showSkuValue-btns .n-pActive {
    display: none;
}

.edmitBody #setSkuAttr_content .addNewSkuValue, .batchBody #setSkuAttr_content .addNewSkuValue {
    display: none;
}

.edmitBody #setSkuAttr_content .showSkuValue-item-up .icon-icon_shanchu- {
    display: none;
}
.edmitBody #addSkuBtnWrap {
    display: none!important;
}
#sku_tbody input[name=DistributePrice], #sku_tbody input[name=SettlePrice] {
    border: unset !important;
    position: relative !important;
    background-color: unset !important;
}
#sku_tbody td.DistributePrice {
    position:relative;
}
#sku_tbody td.SettlePrice {
    position: relative;
}
#sku_tbody td.DistributePrice::after {
    position: absolute;
    display: block;
    content: '';
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
#sku_tbody td.SettlePrice::after {
    position: absolute;
    display: block;
    content: '';
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
.addSkuBtnWarn {
    padding: 16px;
    font-size:14px;
}
.addSkuBtnWarn .n-dColor {
    padding:0 4px;
}
.prepareDistribution-nav {
    width: 905px;
    height: 44px;
    display: flex;
    align-items: center;
    font-size: 14px;
    padding: 0 248px;
    box-sizing: border-box;
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.09);
}
.prepareDistribution-nav .prepareDistribution-nav-line {
    flex: 1;
    width: 100%;
    height: 1px;
    background-color: rgba(0, 0, 0, 0.09);
    margin:0 4px;
}
.prepareDistribution-nav .prepareDistribution-nav-item {
    display:flex;
    flex-direction:row;
    align-items:center;
}
.prepareDistribution-nav .prepareDistribution-nav-icon {
    width: 16px;
    height: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    font-size: 12px;
    border: 1px solid rgba(0, 0, 0, 0.14);
    color: rgba(0, 0, 0, 0.6);
    margin-right:4px;
}
.prepareDistribution-nav .prepareDistribution-nav-item.active .prepareDistribution-nav-icon {
    background-color: #000;
    color: rgba(255,255,255,09);
    border:unset;
}
.choosePlatform {
    display: flex;
    flex-direction: row;
    margin: 8px 16px 0 16px;
}
.choosePlatform .chooseShopPlatformWrap {
    margin-right: 32px;
    padding:8px;
    display:flex;
    justify-content:center;
    align-items:center;
    flex-direction:column;
}
.choosePlatform .chooseShopPlatformWrap.dgActive {
    background: rgba(8, 136, 255, 0.1);
    border: 1px solid #0888FF;
    border-radius: 4px;
}
.choosePlatform .chooseShopPlatformWrap .chooseShopPlatform-icon {
    margin-bottom:4px;
}
.choosePlatform .chooseShopPlatformWrap .chooseShopPlatform-title {
    font-size:12px;
}
.chooseShopWrap {

}
.chooseShopBtns {
    border-top: 1px solid rgba(0, 0, 0, 0.09);
    padding:16px;
    margin:16px 0 0 0;

}
.th-header {
    display:flex;
    align-items:center;
}
.td-body {
    display: flex;
    align-items: center;
}
    .td-body .n-smallIcon {
        margin-right:4px;
    }
.chooseShopTable tbody tr td, .chooseShopTable thead tr th {
    padding: 12px;
    font-size:14px;
}
.chooseShop-full {
    background-color: #fff;
    display:flex;
    flex-direction:column;
}
.chooseShopTableWrap {
    flex:1;
    overflow-y:auto;
    padding-bottom:50px;
}
.createBase-footer .n-mButton.stop {
    background: rgba(8, 136, 255, 0.3);
}

.chooseShopTable tbody tr.active {
    background: rgba(0, 0, 0, 0.04);
}
.n-mySelect .n-mySelect-showContent-ul .n-mySelect-showContent-ul-li.n-mySelect-showContent-ul-li-edmit {
    color: #0888FF;
    display:flex;
    align-items:center;
}
.n-mySelect-showContent-ul-li-edmit .icon-a-edit1x {
    margin-right:4px;
    display:inline-block;
    font-size:15px;
}

.edmitShippingTemplates {
    width: 560px;
    background: #fff;
}
.n-tabNavWrap {

}
    .n-tabNavWrap .n-tabNav {
        overflow-x: auto;
        min-width: 100%;
        overflow-y: hidden;
    }
.edmitShippingTemplates .n-tabNav-item {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    white-space: pre;
}

    .edmitShippingTemplates .layui-content-ul {
        display: flex;
        flex-direction: column;
    }

        .edmitShippingTemplates .layui-content-ul > li {
            margin-top: 16px;
            box-sizing: border-box;
            flex-direction: column;
        }

            .edmitShippingTemplates .layui-content-ul > li span.layui-content-ul-title {
                margin-bottom: 8px;
                display: inline-block;
                font-size: 14px;
                color: rgba(0, 0, 0, 0.6);
            }

    .edmitShippingTemplates .rightOperateWrap {
        display: flex;
        align-items: center;
    }

        .edmitShippingTemplates .rightOperateWrap .icon-tongbu {
            display: inline-block;
            margin-left: 8px;
            cursor: pointer;
        }

    .edmitShippingTemplates .n-mySelect-showContent02 {
        left: unset;
        position: fixed;
        z-index: 10000000000;
        display: none;
        bottom: unset;
        box-sizing: border-box;
        width: 240px;
        flex-direction: column;
        padding: 8px;
        background: #FFFFFF;
        border-radius: 4px;
        box-shadow: 0px 12px 24px 0px rgba(0, 0, 0, 0.04), 0px 8px 16px 0px rgba(0, 0, 0, 0.04), 0px 2px 4px 0px rgba(0, 0, 0, 0.08);
        animation: mySelectAnimation02 0.3s ease-in-out 0s 1 alternate forwards;
    }

    .edmitShippingTemplates .active .n-mySelect-showContent02 {
        display: block;
    }

@keyframes mySelectAnimation02 {
    0% {
        opacity: 0;
        margin-top: -30px;
    }

    100% {
        opacity: 1;
        margin-top: 0;
    }
}
.edmitShippingTemplatesSkin.n-skin .layui-layer-content {
    padding:0;
}
.edmitShippingTemplatesSkin.n-skin .tableShippingWrap {
    max-height: 400px;
    overflow-y: auto;
}
.edmitShippingTemplatesSkin.n-skin .sipping-name {
    display: flex;
    align-items: center;
}
.edmitShippingTemplatesSkin.n-skin .n-smallIcon {
    margin-right:4px;
}
.edmitShippingTemplatesSkin.n-skin .shopName {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.9);
    word-wrap: break-word;
    white-space: break-spaces;
    word-break: break-all;
    flex:1;
}
.showEdmitFreightTemplate-btn {
    cursor:pointer;

}
.showEdmitFreightTemplate-btn .icon-a-edit1x {
    margin-right:4px;
    display:inline-block;
}
#edmitShippingTemplates_nav .n-tabNav-item:last-child {
    position: sticky;
    right: 0;
    background-color: #fff;
    box-shadow: -4px 0 12px 0px #e7e7e7;
}
#freightTemplateWrap .n-mySelect .n-mySelect-showContent-ul .n-mySelect-showContent-ul-li {
    height: unset;
}
#tbody_sipping_data_1 .n-mySelect .n-mySelect-showContent-ul .n-mySelect-showContent-ul-li {
    height: unset;
}
#addSkuWrap_Skus_ul .addSkuWrap-Skus:last-child {
    border-bottom:unset;
}
.tableShippingTable .n-mySelect .n-mySelect-showContent-ul .n-mySelect-showContent-ul-li {
    height: unset;
}
#setSkuAttr_content .addSkuWrap-Skus-main-select .handle {
    opacity: 0;
    cursor: auto;
}
.skuAttributes-warp {
    display: flex;
    align-items: center;
}

.skuAttributes-warp-input {
    display: flex;
    align-items: center;
    border-radius: 4px;
}
.skuAttributes-warp {
    margin-bottom: -4px;
    flex-wrap: wrap;
}

    .skuAttributes-warp .skuAttributes-warp-input {
        margin-bottom: 4px;
    }

    .skuAttributes-warp-input .flex {
        display: flex;
        align-items: center;
    }

#setSkuAttr_content .addSkuWrap-Skus-main-select .handle {
    opacity: 0;
    cursor: auto;
}

.n-mySelect-showContentBox {
    width: calc(100% - 28px);
    margin-left: 28px;
}

.n-mySelect-showContentImg {
    width: calc(100% - 64px);
    margin-left: 64px;
}
.addAttribute {
    height: 40px;
    display: flex;
    align-items: center;
}

    .addAttribute .flex {
        display: flex;
        align-items: center;
    }

    .addAttribute .addAttribute_box {
        display: none;
        padding-top: 4px;
    }

        .addAttribute .addAttribute_box .iconfont {
            padding: 4px;
        }
.n-layui-input-disabled {
    position: relative;
}

    .n-layui-input-disabled::after {
        display: block;
        content: '';
        position: absolute;
        left: 0px;
        top: 0px;
        width: 100%;
        height: 100%;
        border: 1px solid #0888FF !important;
        box-shadow: 0px 0px 0px 2px rgb(8 136 255 / 20%) !important;
        box-sizing: border-box;
        border-radius: 4px;
    }
/*    更新----------*/

.defaultPaddingTdWrap {
    padding: 5px 0;
    display: flex;
}

.defaultPaddingTdWrap-right {
    flex: 1;
    text-align: right;
}

.defaultPaddingTdWrap .moreSku-title {
    max-width: 200px;
    word-break: break-all;
}

.defaultPaddingTdWrap .n-newCheckbox {
    cursor: no-drop;
    background-color: #f0f0f0;
}
#new_prepareDistribution_nav {
    padding:0 120px;
    display:none;
}
.showSkuValue-item .isRequiredTip {
    margin-top: 4px;
    margin-bottom: 4px;
    font-size: 12px;
    line-height: 18px;
}
.goBackBatchSetSkin .n-toast-02 i {
    color: #0888FF;
}
.mL12 {
    margin-left:12px;
}
.n-bButton.stop, .n-mButton.stop, .n-sButton.stop {
    opacity:1;
}
.createBase-footer {
    z-index: 10000;
}
.noDataStop {
    cursor:not-allowed!important;
}
.addSkuBtn {
    display: none;
}