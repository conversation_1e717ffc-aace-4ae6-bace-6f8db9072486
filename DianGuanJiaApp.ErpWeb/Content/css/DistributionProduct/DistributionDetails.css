.flex {
    display: flex;
    align-items: center;
}
.fs20 {
    font-size: 20px;
}
.mB8 {
    margin-bottom: 8px;
}
.ellipsis{
    overflow: hidden;    
    text-overflow:ellipsis;    
    white-space: nowrap;
}
.mL4{
    margin-left: 4px;
}
@font-face {
    font-family: "D-DIN";
    src: url("../../../font/glyphicons-halflings-regular.woff2") format('truetype');
}
.stockup_table_content thead tr {
    background: rgba(0, 0, 0, 0.04);
}
.react-container {
    min-height: 100vh;
    height: 100%;
    background-repeat: no-repeat;
    background-size: 100% auto;
    background-color: #e7f5ff;
}
.react-container .goodsInfo {
    background-color: rgba(255, 255, 255, 0.5);
}
.react-container .goodsInfo .goods-cont{
    width: 1200px;
    height: 476px;
    margin: 0 auto;
}
.react-container .goodsInfo .header {
    height: 76px;
}
.react-container .goodsInfo .header .icon-xiayi {
    margin-right: 8px;
    transform:rotate(90deg);
    display: none;
}
.react-container .goodsInfo .goods-cont .header .title {
    font-size: 24px;
    font-weight: 600;
    color: #000;
    line-height: 76px;
}
.react-container .goodsInfo .goods-cont .cont {
    align-items: flex-start;
}
.react-container .goodsInfo .goods-cont .cont .cont-l {
    width: 312px;
    margin-right: 16px;
}
.react-container .goodsInfo .goods-cont .cont .cont-l .goods-img{
    width: 312px;
    height: 312px;
    border: 0.5px solid rgba(0, 0, 0, 0.14);
    box-sizing: border-box;
    background: rgba(0, 0, 0, 0.04);
    margin-bottom: 8px;
}
.react-container .goodsInfo .goods-cont .cont .cont-l .goods-list{

}
.react-container .goodsInfo .goods-cont .cont .cont-l .goods-list .item{
    width: 56px;
    height: 56px;
    opacity: 1;

    /* 中性色/Gray1 */
    background: rgba(0, 0, 0, 0.04);

    box-sizing: border-box;
    border: 0.5px solid rgba(0, 0, 0, 0.14);
    margin-right: 8px;
}
.react-container .goodsInfo .goods-cont .cont .cont-r {
    width: calc(100% - 328px);
    color: rgba(0, 0, 0, 0.9);
    
}
.react-container .goodsInfo .goods-cont .cont .cont-r .title {
    font-size: 20px;
    line-height: 28px;
    font-weight: 600;
    margin-bottom: 8px;
    padding-left:16px;
}
.react-container .goodsInfo .goods-cont .cont .cont-r .label {
    color: rgba(0, 0, 0, 0.4);
    margin-right: 8px;
    word-break: keep-all;
}
.react-container .goodsInfo .goods-cont .cont .cont-r .txtbox {
    margin-bottom: 16px;
    padding-left:16px;
}
.react-container .goodsInfo .goods-cont .cont .cont-r .txtbox .txt{
    margin-right: 16px;
}
.react-container .goodsInfo .goods-cont .cont .cont-r .price {
    height: 64px;
    line-height: 64px;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.5);
    border: 0.5px solid #FFFFFF;
    margin-bottom: 24px;

}
.react-container .goodsInfo .goods-cont .cont .cont-r .price .label{
    color: rgba(0, 0, 0, 0.6);
    margin-right: 30px;
    padding-left: 16px;
}
.react-container .goodsInfo .goods-cont .cont .cont-r .price .pricebox {
    font-family: D-DIN;
    font-weight: 700;
    font-size: 28px;
    color: #EA572E;
}
.field {
    padding: 0 8px 0 16px;
    height: 174px;
}
.field .field-box{
    align-items: flex-start;
}
.field .field-box .field-label {
    margin-right: 44px;
    font-weight: 600;
    word-wrap: break-word;
    word-break: keep-all;
    
}
.field .field-box .field-list {
    flex-wrap: wrap;
}
.field .field-box .field-item {
    width: 250px;
    margin-right: 12px;
    margin-bottom: 16px;
}
.field .field-box .field-item:nth-child(3n){
    margin-right: 0;
}
.hidden{
    overflow: hidden;
} 
.field .field-box .field-item .field-item-cont{
   
}
.field .field-box .field-item .relative{
    position: relative;
}
.field .field-box .field-item .field-item-cont .icon {
    width: 20px;
    display: inline-block;
    height: 20px;
    margin-right: 4px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.04);
    flex-shrink:0;
    color: rgba(0, 0, 0, 0.4);
}
.field .field-box .field-item .field-item-cont .icon.icon-a-chevron-down1x:before{
    display: inline-block;
    width: 20px;
    height: 20px;
    padding-top: 2px;
    padding-left: 2px;
}

.field .field-box .field-item .field-item-cont .freight-warp{
    position: absolute;
    left: -150px;
    top:20px;
    display: none;
    width: 400px;
    padding:16px;
    border-radius: 8px;
    box-sizing: border-box;
    /* 中性色/White */
    background: #FFFFFF;
    /* 中层投影 */
    box-shadow: 0px 12px 24px 0px #0000000a,0px 8px 16px 0px rgba(0, 0, 0, 0.04),0px 2px 4px 0px rgba(0, 0, 0, 0.08);
}
.field .field-box .field-item .relative .freight-warp:hover{
    
}
.field .field-box .field-item .relative:hover .freight-warp{
    
}
.field .field-box .field-item .field-item-cont .freight-warp .freight-warp-item{
    margin-bottom: 16px;
}
.field .field-box .field-item .field-item-cont .freight-warp .freight-warp-item:last-child{
    margin-bottom: 0px;
}
.react-container .goodsInfo .goods-cont .cont .cont-r .btn-box {
    padding-bottom: 16px;
}
.react-container .goodsInfo .goods-cont .cont .cont-r .btn-box .n-bButton {
    margin-right: 8px;
}
#distributionDetails {
    background: #e9f4ff;
    font-size: 14px;
}
.react-container .details {
    
}
.react-container .details .cont {
    width: 1200px;
    margin: 0 auto;
    padding: 12px 0;
    align-items: flex-start;
}
.react-container .details .cont .cont-l {
    width: 312px;
    margin-right: 16px;
    width: 312px;
    height: 184px;
    border-radius: 8px;
    padding: 16px;
    box-sizing: border-box;
    background: rgba(255, 255, 255, 0.5);
    border: 0.5px solid #FFFFFF;
    box-sizing: border-box;
}
.react-container .details .cont .cont-l .suppliers-info {
    margin-bottom: 12px;
}
.react-container .details .cont .cont-l .suppliers-info img{
    width: 48px;
    height: 48px;
    border-radius: 50%;
    margin-right: 8px;
}
.react-container .details .cont .cont-l .suppliers-info .suppliers-info-cont-t {
    flex-direction: column;
    align-items: flex-start
}
.react-container .details .cont .cont-l .suppliers-info .suppliers-info-cont-t-name {
    color: #3D3D3D;
    font-weight: 600;
    margin-bottom: 4px;

}
.react-container .details .cont .cont-l .suppliers-info .suppliers-info-cont-t-prove {
    padding: 3px 4px;
    border-radius: 4px;
    font-size: 12px;
    background: rgba(8, 136, 255, 0.1);
    border: 0.5px solid rgba(8, 136, 255, 0.2);
}
.react-container .details .cont .cont-l .suppliers-info .not-present {
    background: rgba(66, 74, 87, 0.1);
    border: 0.5px solid rgba(66, 74, 87, 0.2);
    color: #424A57;
}
.react-container .details .cont .cont-l .suppliers-info .suppliers-info-cont-t-prove .icon-a-secured1x {
    font-size: 14px;
    margin-right: 4px;
}
.react-container .details .cont .cont-l .suppliers-info .suppliers-info-cont-t-prove .icon-a-chevron-down1x {
    transform: rotate(270deg);
    margin-left: 4px;
}
.react-container .details .cont .cont-l .n-bButton{
    width: 280px;
    font-size:14px;
    line-height:38px;
}
.react-container .details .cont .cont-r {
    flex: 1;
    width: calc(100% - 328px);
    color: rgba(0, 0, 0, 0.9);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.5);
    border: 0.5px solid #FFFFFF;
    box-sizing: border-box;
}
.react-container .details .cont .cont-r .title {
    padding: 12px 16px;
    font-size: 16px;
    font-weight: 600;;
}
.react-container .details .cont .cont-r .attribute{
    flex-wrap: wrap;
    padding: 0 16px;
}
.react-container .details .cont .cont-r .attribute .attribute-item {
    width: 264px;
    margin-bottom: 12px;
    margin-right: 22px;
}
.react-container .details .cont .cont-r .attribute .attribute-item:nth-child(3n){
    margin-right: 0;
}
.react-container .details .cont .cont-r .attribute .attribute-item>span {
    width:calc(100% - 124px)
}
.react-container .details .cont .cont-r .attribute .attribute-item .label {
    display: inline-block;
    width: 124px;
    margin-right: 16px;
    color: rgba(0, 0, 0, 0.4);
}
.react-container .details .cont .cont-r .descriptionStr .descriptionStr-item >img{
    max-width: 100%;
    height: auto;
    margin: 0 auto;
    display: block;
}
.react-container .details .cont .cont-r .descriptionStr {
    padding: 0 16px 16px;
}


.preview-wrap {
	width: 352px;
}

.intemInfo-wrap {
	margin-left: 30px;
	flex: 1;

}

.track {
	width: 210px;
	height: 600px;
	/* background-color:cornflowerblue; */

	float: left;


}

.main-img {
	width: 312px;
	height: 312px;
	border: 0.5px solid rgba(0, 0, 0, 0.14);
	margin-bottom: 8px;
    box-sizing: border-box;
	position: relative;

}

.main-img img {
	width: 310px;
	height: 310px;
}

.spec-list {
	width: 312px;
	height: 56px;
	/* background-color: bisque; */
	position: relative;

}

.prev,
.next {
	position: absolute;
	top: 55%;
	width: 20px;
	height: 20px;
	margin-top: -16px;
	cursor: pointer;
	text-decoration: none;
	border-left: 3px solid #ddd;
	border-bottom: 3px solid #ddd;
	display: block;
	cursor: not-allowed;
}

.prev {
	transform: rotate(45deg);

}
.prev.active,
.next.active{
	border-left: 3px solid #aaa;
	border-bottom: 3px solid #aaa;
	cursor: pointer;
}

.next {
	right: 0;
	transform: rotate(-135deg);
}

.spec-items {
	position: relative;
	width: 250px;
	height: 56px;
	margin: 0 auto;

	overflow: hidden;


}

.spec-items ul {
	width: 2000px;
	height: 56px;
	position: absolute;
	top: 0;
	left: 0;
	transition: all 0.2s;

}

.spec-items ul li {

	box-sizing: border-box;
	width: 56px;
	height: 56px;
	float: left;
	list-style-type: none;
    margin-right: 8px;
    border: 0.5px solid rgba(0, 0, 0, 0.14);
}

.spec-items ul li img {
	width: 56px;
	height: 56px;
	box-sizing: border-box;
}

/* .spec-items ul li img:hover{
    border:2px solid red;
} */
.spec-items ul .img-hover img {
	border: 2px solid #0888FF;
}

.zoom-pup {
	width: 236px;
	height: 236px;
	background-color: rgb(8 136 255 / 60%);
	opacity: 0.5;
	position: absolute;
	top: 0;
	left: 0;
	display: none;
}

.zoom-div {
	width: 540px;
	height: 540px;
	position: absolute;
	top: 0;
	left: 351px;
	overflow: hidden;
	display: none;

}

.zoom-div img {
	position: absolute;
	top: 0;
	left: 0;
	height: 800px;
	width: 800px;
	z-index: 10;
}

element.style {
    display: flex;
}
.form-common-tip-w-280 {
    width: 280px;
}
.form-common-tip-w-340 {
    width: 340px;
}
.form-common-tip-wrap {
    position: absolute;
    background: #FFFFFF;
    z-index: 4;
    padding: 16px;
    box-sizing: border-box;
}
.form-common-tip-wrap .common-address-content{
    justify-content: space-between;
}
.n-leftUp::after {
    width: 0 !important;
    height: 0 !important;
}
.business-contact-info {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.dailog-common-icon {
    color: rgba(0, 0, 0, 0.4);
    font-size: 40px;
}

.dailog-common-title {
    font-family: Source Han Sans;
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    color: rgba(0, 0, 0, 0.4);
    text-align: center;
}
.business-contact-ul {
    list-style: none; /* 去除默认的列表样式 */
    padding: 0; /* 去除内边距 */
    margin: 0; /* 去除外边距 */
    display: flex;
    flex-wrap: wrap;
    gap: 16px; /* 设置项目之间的间隙为16px */
}

.contact-card-li {
    display: flex;
    flex-direction: column;
    padding: 16px;
    box-sizing: border-box;
    border: 1px solid rgba(0, 0, 0, 0.09);
    height: 84px;
    border-radius: 8px;
    width: calc(50% - 8px); /* 计算每列的宽度，减去一半的间隙 */
    /* 下方间隙为16px margin-bottom: 16px; */
}

.contact-card-name {
    font-family: Source Han Sans;
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    color: rgba(0, 0, 0, 0.9);
}

.contact-card-data {
    font-family: Source Han Sans;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    color: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.scan-code-wrap {
    cursor: pointer;
    &:hover

{
    color: #0888FF;
}

}

.feedback-info {
    padding: 42px;
    box-sizing: border-box;
}

.feedback-card-ul {
    padding: 8px 0;
    box-sizing: border-box;
}

.feedback-card-li {
    margin-top: 24px;
    &:first-child

{
    margin-top: 0;
}

}

.feedback-time {
    font-family: Source Han Sans;
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    color: rgba(0, 0, 0, 0.4);
}

.feedback-content {
    font-family: Source Han Sans;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    color: #3D3D3D;
    margin-top: 4px;
}

.common-view-more {
    color: #0888FF;
    cursor: pointer;
}

.n-skin .layui-layer-content {
    padding: 0;
}

    .n-skin .layui-layer-content .dialog-wrap {
        padding: 16px 16px 72px;
    }

.n-skin .dialogBtns {
    border-top: 1px solid #e5e9f2;
    padding: 12px 16px;
    justify-content: space-between;
    position: absolute;
    bottom: 0;
    width: 100%;
    box-sizing: border-box;
}

.n-skin .dialogBtns .n-mButton {
    margin-left: 10px;
}

.stockup_table_content {
    border-left:none;
    background-color: transparent;
}
.stockup_table_content thead tr th :last-child(){
    border-right: none;
}
.stockup_table_content tbody tr td :last-child(){
    border-right: none;
}
.stockup_table_content {
    font-size: 14px;
}
#layui_side_left_new{
    display: none;
}
#layui_side_left{
    display: none;
}
.layui-bodys {
    margin:0 !important;
}
#top_layui_header {
    display: none;
}

.companyInfo-list {
    flex-direction: column;
}
    .companyInfo-list .companyInfo-list-item {
        margin-bottom: 16px;
        width: 100%;
        word-break: break-all;
    }
        .companyInfo-list .companyInfo-list-item:last-child {
            margin-bottom: 0px;
        }
        .companyInfo-list .companyInfo-list-item-label {
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
        color: rgba(0, 0, 0, 0.4);
        margin-right: 8px;
        /* min-width: 56px; */
        max-width: 112px;
    }
    .companyInfo-list .companyInfo-list-item-value {
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
        color: rgba(0, 0, 0, 0.9);
    }


.main-img .img.noImg {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background-color: #fff;
}

.main-img .img.noImg .iconfont {
    font-size: 80px;
    color: rgba(0, 0, 0, 0.14);
}

.main-img .img.noImg .imgbox {
    width: 80px;
    height: 80px;
    background-repeat: no-repeat;
    background-size: 100%;
}
.stockup_table_content thead tr th:last-child {
    border-right: none;
}
.stockup_table_content tbody tr td:last-child {
    border-right: none;
}
