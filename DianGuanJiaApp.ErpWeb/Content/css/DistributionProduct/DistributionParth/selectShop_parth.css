.choosePlatform {
    display: flex;
    flex-direction: row;
    margin: 8px 16px 0 16px;
}

    .choosePlatform .chooseShopPlatformWrap {
        margin-right: 20px;
        padding: 8px;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        cursor:pointer;
    }
    .choosePlatform .chooseShopPlatformWrap.stop {
        opacity:0.4;
        cursor:not-allowed;

    }

        .choosePlatform .chooseShopPlatformWrap.dgActive {
            background: rgba(8, 136, 255, 0.1);
            border-radius: 4px;
            box-sizing:border-box;
            position:relative;
        }
            .choosePlatform .chooseShopPlatformWrap.dgActive::before {
                position: absolute;
                width: 100%;
                height: 100%;
                content: "";
                display: block;
                top: 0;
                left: 0;
                border: 1px solid #0888FF;
                border-radius: 4px;
            }

            .choosePlatform .chooseShopPlatformWrap .chooseShopPlatform-icon {
                margin-bottom: 4px;
            }

        .choosePlatform .chooseShopPlatformWrap .chooseShopPlatform-title {
            font-size: 12px;
        }

.chooseShopWrap {
}

.chooseShopBtns {
    border-top: 1px solid rgba(0, 0, 0, 0.09);
    padding: 16px;
    margin: 16px 0 0 0;
}
