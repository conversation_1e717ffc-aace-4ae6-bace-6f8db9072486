.distributionSetList .flex {
    display: flex;
    align-items: center;
}

.distributionSetList .distributionsListWrap {
    margin-top: -1px;
}

    .distributionSetList .distributionsListWrap .n-table th {
        box-sizing: border-box;
    }

.distributionSetList .productShow > img {
    width: 56px;
    height: 56px;
    border-radius: 3px;
    box-sizing: border-box;
    border: 0.5px solid rgba(0, 0, 0, 0.09);
    margin-right: 8px;
}

.distributionSetList .productShow .product-title {
    text-align: left;
    width: calc(100% - 64px);
}

    .distributionSetList .productShow .product-title li:first-child {
        grid-column: 1 / -1;
    }

.distributionSetList .product-title-li {
    display: flex;
    justify-content: space-between;
}

    .distributionSetList .product-title-li span:nth-child(1) {
        flex: 1;
    }

    .distributionSetList .product-title-li span:nth-child(2) {
        width: 52px;
        display: inline-block;
        text-align: right;
    }

.distributionSetList .product-price {
    display: flex;
    flex-direction: column;
}

    .distributionSetList .product-price .product-price-li {
        margin-bottom: 4px;
        font-size: 12px;
    }

        .distributionSetList .product-price .product-price-li:last-child {
            margin-bottom: 0;
        }

        .distributionSetList .product-price .product-price-li:nth-child(1) {
            color: rgba(0, 0, 0, 0.9);
            font-size: 14px;
            margin-bottom: 4px;
        }

.distributionSetList .centerOperate {
    text-align: center;
    vertical-align: baseline;
}

.distributionSetList .popoverCommon-warn {
    bottom: 22px;
}

.distributionSetList .productShow {
    flex: 1;
    display: flex;
}

.distributionsListWrap .n-table {
    margin-bottom: 16px;
}

.distributionsListWrap .n-table {
    margin-bottom: 16px;
}

.distributionSetList .distributionsListWrap .n-table th {
    background: rgba(0, 0, 0, 0.04);
}

#DistributionbaseListY_data .icon-a-edit1x {
    display: none;
}

.n-table tbody#DistributionbaseListY_data tr:hover .icon-a-edit1x {
    display: inline;
}

.n-table tbody#DistributionbaseListY_data tr .icon-a-edit1x:hover {
    color: #0888FF;
}

.n-table #DistributionbaseListY_data .ActiveTr {
    background: rgba(0, 0, 0, 0.04);
}

.n-table thead .ActiveTh th {
    background: #fff !important;
}

.n-table #DistributionbaseListY_data .ActiveTr.stopTr {
    background: rgba(234, 87, 46, 0.1) !important;
}

#batchDistributionbtn {
    opacity: unset;
}

.createBase-footer .n-mButton.navStop {
    cursor: not-allowed;
    background: rgba(8, 136, 255, 0.3) !important;
    background-color: rgba(8, 136, 255, 0.3) !important;
}

.createBase-footer .n-mButton.stop:hover .n-tooltip {
    top: -30px;
    left: 70px;
}

.createBase-footer .n-mButton.navStop:hover .n-tooltip {
    top: -30px;
    left: 40px;
}

.createBase-footer .n-mButton.stop:hover .n-tooltip::after {
    left: 60px;
}

.createBase-footer .n-mButton.navStop:hover .n-tooltip::after {
    left: 90px;
}

#batchDistributionbtn .n-tooltip {
    line-height: normal;
    display: none;
}
/*    #batchDistributionbtn .n-leftdown::after {
        left: 90px;
    }*/
#batchDistributionbtn.stop:hover .n-tooltip {
    display: block;
}

.ZeroForSaleWrap {
    color: #EA572E !important;
}

    .ZeroForSaleWrap .icon-gantan {
        font-size: 14px;
        color: #EA572E;
        margin-right: 4px;
    }
    .ZeroForSaleWrap .n-layui-input {
        color: #EA572E;
    }
.leftOperate {
    text-align: left !important;
}

.n-mySelect .n-mySelect-showContent-ul .n-mySelect-showContent-ul-li.activeItem {
    background: rgba(0, 0, 0, 0.04);
    color: rgba(0, 0, 0, 0.9);
}

.n-table tbody tr.activeHover {
    background: rgba(0, 0, 0, 0.04);
}

.n-table tbody tr.chx-warn td {
    border-top: unset;
    padding-top: 0;
}

.n-table tbody tr.hasWarnTd td {
    border-bottom: unset;
}
