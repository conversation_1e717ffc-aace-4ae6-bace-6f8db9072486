.distributionSet .n-layui-mywrap-content {
    width: 100%;
}

.distributionSet .popoverCommon-warn {
    bottom: 22px;
    border-radius: 8px;
    color:rgba(0, 0, 0, 0.9)
}

.distributionSet .setpriceWrap {
    display: flex;
    flex-direction: column;
}

    .distributionSet .setpriceWrap .setpriceWrap-item {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        padding: 8px 16px 8px 8px;
        background: rgba(0, 0, 0, 0.04);
        border-radius: 4px;
        margin-bottom: 8px;
    }

        .distributionSet .setpriceWrap .setpriceWrap-item .n-inputWrap.warnInput input[type=text] {
            border: 1px solid #e6e6e6 !important;
        }

        .distributionSet .setpriceWrap .setpriceWrap-item.setpriceWrap-active .n-inputWrap.warnInput input[type=text] {
            border: 1px solid #fe6f4f !important;
        }
        
.distributionSet .layui-content-ul {
    width: 100%;
}
.distributionSet .basic-settings {
    display:flex;
    justify-content:space-between;
}
.distributionSet .basic-settings .layui-content-ul {
    width: 100%;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
}
.distributionSet .basic-settings .layui-content-ul > li {
    width: calc(50% - 8px);
}


.distributionSet .setpriceWrap-item-right {
    display: flex;
    flex-direction: row;
}

    .distributionSet .setpriceWrap-item-right .setpriceWrap-item-right-item {
        margin-left: 16px;
        font-size: 14px;
        cursor: pointer;
    }

        .distributionSet .setpriceWrap-item-right .setpriceWrap-item-right-item:hover {
            color: #0888FF;
        }


        .distributionSet .setpriceWrap-item-right .setpriceWrap-item-right-item.stop {
            color: rgba(0, 0, 0, 0.3);
            cursor: not-allowed;
        }

.distributionSet .n-form-switch.m-switch {
    width: 36px;
    height: 20px;
}

.distributionSet .n-form-switch .n-form-switch-icon {
    width: 16px;
    height: 16px;
}

.distributionSet .setpriceWrap-item-left-up i {
    color: rgba(0, 0, 0, 0.9);
    font-size: 14px;
    margin-right: 8px;
    display: inline-block;
}

.distributionSet .setpriceWrap-item-left-up .setpriceWrap-item-left-up-title {
    color: rgba(0, 0, 0, 0.9);
    font-size: 14px;
}

.distributionSet .setpriceWrap-item-left-down {
    color: rgba(0, 0, 0, 0.6);
    font-size: 12px;
    margin-top: 8px;
    padding-left: 17px;
}

.distributionSet .sumIcon {
    font-size: 14px;
    padding: 0 4px;
}

.distributionSet .setpriceWrap-item.n-center {
    padding-left: 0;
    background-color: #fff;
    justify-content: flex-start;
    padding-bottom: 0;
    margin-bottom: 0;
}

#New_setPrepareDistribution_3 {
    margin-bottom: 16px;
}

#distributionSet_step {
    display: none;
}

.distributionSet .n-mySelect.formWarn {
    border: 1px solid #fe6f4f !important;
}

.showEdmitFreightTemplate-btn {
    padding: 6px 8px;
}

    .showEdmitFreightTemplate-btn:hover {
        padding: 6px 8px;
        background: rgba(0, 0, 0, 0.04);
    }
    #setPrepareDistribution .layui-content-ul {
        width: 100%;
    }
    #setPrepareDistribution .basic-settings {
        display:flex;
        justify-content:space-between;
    }
    #setPrepareDistribution .basic-settings .layui-content-ul {
        width: 100%;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: space-between;
    }
    #setPrepareDistribution .basic-settings .layui-content-ul > li {
        width: calc(50% - 8px);
    }
