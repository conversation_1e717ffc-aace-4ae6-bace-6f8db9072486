

// 商品管理页面 设置结算价 js
var SetSettlementPriceModule = (function (module, common, $, layer) {

     
    module.setSettlementPrice = function (chx) {
    
    // 打开弹窗
    layer.open({
        type: 1,
        area: ['1600px', 'auto'],
        maxHeight: 800,
        title: '设置结算价', // 是否显示标题栏
        content: $('#setSettlementPriceTable'),
        btn: ['保存', '取消'],
        btnAlign: 'c', // 按钮居中布局

        success: function (layero) {
                

        },
        yes: function (index, layero, that) {
            // do something
            layer.close(index); // 关闭弹层
        },
        btn2: function () {
                
        },
        cancel: function () {
               
        }
    });
    }


    return module;
}(bindSupplierModule || {}, commonModule, jQuery, layer));
    
