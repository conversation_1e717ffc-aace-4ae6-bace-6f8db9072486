
var DistributionbaseListAllCheck = null;
var DistributionbaseListSingleCheck = null;
var GoLastBatchDistribution = null;//返回上一步
var submitBatchDistribution = null;//开始批量铺货



var DistributionbaseListModule = (function (module, $, layer) {
    var DataNeedUpdateY = [];
    var DataNeedUpdateN = [];


    $(function () {

        eventListenDistributionbaseListNav();

    })
    function eventListenDistributionbaseListNav() {
        $$.navActive("#changeDistributionbaseListNav", function (index, item) {
            $("#distributionsListTableWrap .n-table").hide();
            $("#distributionsListTableWrap .n-table:eq(" + index + ")").css({ display: 'table' });

            if (index == 1) {
                $("#batchDistributionbtn").addClass("navStop").addClass("stop");
                $("#DistributionbaseListN_tar").show();
                $("#batchDistributionbtn_warn").html("请切换到已完善平台资料进行操作");
            } else {
                $("#batchDistributionbtn").removeClass("navStop").removeClass("stop");
                $("#DistributionbaseListN_tar").hide();
                $("#batchDistributionbtn_warn").html("请选择要铺货的商品");
            }
        });
    }

    module.GetPtInfoDrafts = function (PtProductUniqueCodeArray) {//获取多商品铺货草稿列表

        var model = {};
        model.PlatformType = setPlatformTypeName;
        model.ShopIds = ChooseShopId;
        model.PtProductUniqueCode = PtProductUniqueCodeArray;

        commonModule.Ajax({
            url: '/api/PtProductInfoDraft/GetPtInfoDrafts',
            type: "POST",
            loading: true,
            contentType: 'application/json',
            data: JSON.stringify(model),
            success: function (rsp) {
                if (commonModule.IsError(rsp)) {
                    return;
                }
                if (rsp.Success) {

                    $("#distributionSet_step").hide();
                    $("#distributionSet_list").show();

                    var resultData = rsp.Data;
                    var NeedUpdateCountY = resultData.NeedUpdateCountY;
                    var NeedUpdateCountN = resultData.NeedUpdateCountN;
                    $("#NeedUpdateCountY").text(NeedUpdateCountY);
                    $("#NeedUpdateCountN").text(NeedUpdateCountN);

                    DataNeedUpdateY = resultData.NeedUpdateY || [];
                    DataNeedUpdateN = resultData.NeedUpdateN || [];

                    DataNeedUpdateY.forEach(function (item, index) {
                        item.forMatCategoryName = item.CategoryName.replaceAll(',', '>');
                        item.SubjectLengData = item.SubjectLength.split('/');
                        item.SubjectStringLeng = getByteLen(item.Subject);
                        item.isCheck = true;
                    })


                    if (DataNeedUpdateY.length > 0) {
                        $("#batchDistributionbtn").removeClass("stop");
                        $("#batchDistributionbtn_title").html("开始铺货(" + DataNeedUpdateY.length + ")");
                        $("#DistributionbaseListAll_check").addClass("activeF").removeClass("activeP");
                        $("#changeDistributionbaseListNav .n-tabNav-item:eq(0)").trigger("click");


                    } else {
                        $("#batchDistributionbtn").addClass("stop");
                        $("#batchDistributionbtn_title").text("开始铺货");
                        $("#DistributionbaseListAll_check").removeClass("activeF").removeClass("activeP");
                        $("#changeDistributionbaseListNav .n-tabNav-item:eq(1)").trigger("click");
                        $("#changeDistributionbaseListNav .n-tabNav-item:eq(0)").addClass("noDataStop");

                    }
                    if (!NeedUpdateCountN.length) {
                        $("#changeDistributionbaseListNav .n-tabNav-item:eq(1)").addClass("noDataStop");
                    }

                    if (DataNeedUpdateY.length == 0 || DataNeedUpdateN.length == 0) {
                        $("#changeDistributionbaseListNav").off();
                    }
                    if (DataNeedUpdateY.length > 0 && DataNeedUpdateN.length > 0) {
                        $("#changeDistributionbaseListNav .n-tabNav-item:eq(0)").removeClass("noDataStop");
                        $("#changeDistributionbaseListNav .n-tabNav-item:eq(1)").removeClass("noDataStop");
                        eventListenDistributionbaseListNav();
                    }
                    var DataNeedUpdateList = DataNeedUpdateY.concat(DataNeedUpdateN);
                    // 单个铺货
                    if (DataNeedUpdateList && DataNeedUpdateList.length === 1 && isBatchPuhuo) {
                        DataNeedUpdateList[0].isCheck = true;
                        DataNeedUpdateY = DataNeedUpdateList;
                        EdmitDistributionItem(DataNeedUpdateList[0].UniqueCode, DataNeedUpdateList[0].Subject)
                        return
                    }
                    SetPtInfoDraftsHtml(DataNeedUpdateY, DataNeedUpdateN);

                }
            }

        });

    }

    function SetPtInfoDraftsHtml(DataNeedUpdateY, DataNeedUpdateN) {

        var tplt = $.templates("#DistributionbaseListY_temp");
        var html = tplt.render({ data: DataNeedUpdateY });
        $("#DistributionbaseListY_data").html(html);

        var tplt02 = $.templates("#DistributionbaseListN_temp");
        var html02 = tplt02.render({ data: DataNeedUpdateN });
        $("#DistributionbaseListN_data").html(html02);
        // 铺厂家隐藏分销价
        if (!isFxUser) {
            $('#New_setPrepareDistribution_list .distributePrice-li').hide();
        }
        // 拼多多铺货部分功能隐藏
        if (setPlatformTypeName == "Pinduoduo") {
            $('.isshow_table_pdd').css('display', 'table-cell');
            $('.ishide_pdd').css('display', 'none');
        } else {
            $('.isshow_table_pdd').css('display', 'none');
            $('.ishide_pdd').css('display', 'table-cell');
        }

        $("#DistributionbaseListY_data tr").hover(function () {
            var index = $(this).attr("data-index");
            $("#DistributionbaseListY_data tr.chx-item" + index).addClass("activeHover")
        }, function () {

            $("#DistributionbaseListY_data tr").removeClass("activeHover")

        })


    }


    DistributionbaseListAllCheck = function () {
        $(this).toggleClass("activeF").removeClass("activeP");
        var isAllCheck = $(this).hasClass("activeF");
        DataNeedUpdateY.forEach(function (item, i) {
            item.isCheck = isAllCheck;
        })
        SetPtInfoDraftsHtml(DataNeedUpdateY, DataNeedUpdateN);
        checkBatchDistributioCount();
    }

    DistributionbaseListSingleCheck = function (index) {

        DataNeedUpdateY.forEach(function (item, i) {
            if (index == i) {
                item.isCheck = !item.isCheck;
            }
        })
        SetPtInfoDraftsHtml(DataNeedUpdateY, DataNeedUpdateN);
        var CheckboxLen = $("#DistributionbaseListY_data .productWrap .n-newCheckbox").length;
        var ActiveCheckboxLen = $("#DistributionbaseListY_data .productWrap .n-newCheckbox.activeF").length;

        if (ActiveCheckboxLen > 0) {
            if (CheckboxLen > ActiveCheckboxLen) {
                $("#DistributionbaseListAll_check").addClass("activeP").removeClass("activeF");
            }
            if (CheckboxLen == ActiveCheckboxLen) {
                $("#DistributionbaseListAll_check").addClass("activeF").removeClass("activeP");
            }
        } else {
            $("#DistributionbaseListAll_check").removeClass("activeF").removeClass("activeP");
        }
        checkBatchDistributioCount()
    }

    function checkBatchDistributioCount() {
        var Count = 0;
        var text = "";
        DataNeedUpdateY.forEach(function (item, i) {
            if (item.isCheck) {
                Count++;
            }
        })
        if (Count > 0) {
            $("#distributionsList_tr").addClass("ActiveTh");
            text = "开始铺货(" + Count + ")";
            $("#batchDistributionbtn").removeClass("stop");
        } else {
            $("#distributionsList_tr").removeClass("ActiveTh");
            text = "开始铺货";
            $("#batchDistributionbtn").addClass("stop");
        }
        $("#batchDistributionbtn_title").html(text);
    }

    function checkBatchDistribution() {
        var isReturn = false;

        $("#DistributionbaseListY_data tr").each(function (index, item) {
            if ($(item).hasClass("ZeroForSalePriceClass") && $(item).hasClass("ActiveTr")) {
                $(item).addClass("stopTr");
                commonModule.w_alert({ type: 2, content: '商品售价不能为0' });
                isReturn = true;
            }
        })
        if ($("#DistributionbaseListY_data tr.stopTr").length > 0) {
            $("#distributionSet_list").animate({ scrollTop: $("#DistributionbaseListY_data tr.stopTr:eq(0)")[0].offsetTop + 50 }, "100");
        }
        // 拼多多平台类型校验
        if (setPlatformTypeName == "Pinduoduo") {
            var isRender = false;
            // 参考价校验
            DataNeedUpdateY.forEach(function (item, index) {
                if (item.isCheck) {
                    var _SalePrice = replaceHtml(item.SalePrice);
                    var _ReferencePrice = replaceHtml(item.ReferencePrice);
                    var _SinglePrice = replaceHtml(item.SinglePrice);
                    if (_ReferencePrice == 0 || _ReferencePrice > _SinglePrice * 5 || _ReferencePrice < _SinglePrice){
                        item.IsZeroForReferencePrice = true;
                        isReturn = true;
                        isRender = true;
                    }
                }
            })
            if (isRender) {
                SetPtInfoDraftsHtml(DataNeedUpdateY, DataNeedUpdateN);
            }
        }
        return isReturn;

    }

    GoLastBatchDistribution = function () {
        $("#distributionSet_list").hide();
        $("#distributionSet_step").show();

        $("#new_prepareDistribution_nav .prepareDistribution-nav-item").removeClass("active");
        $("#new_prepareDistribution_nav .prepareDistribution-nav-item:eq(1)").addClass("active");
    }

    SubmitBatchDistribution = function () {   //批量开始铺货
        
        if ($(this).hasClass("stop") || $(this).hasClass("navStop")) {
            return;
        }
        if (checkBatchDistribution()) {
            return;
        }

        var options = {};
        options.ListingShopIds = ChooseShopId;
        //options.ListingShopIds = [168, 1687];
        options.PlatformType = setPlatformTypeName;
        options.ListingPtProducts = [];

        DataNeedUpdateY.forEach(function (item, index) {
            if (item.isCheck) {
                var obj = {};
                obj.ListingPtProductUniqueCode = item.UniqueCode;
                obj.ShopCount = item.ShopCount;
                options.ListingPtProducts.push(obj);
            }
        })

        commonModule.Ajax({
            url: '/api/PtProductInfoDraft/BatchSaveListings',
            type: "POST",
            loading: true,
            contentType: 'application/json',
            data: JSON.stringify(options),
            success: function (rsp) {
                if (commonModule.IsError(rsp)) {
                    return;
                }
                if (rsp.Success) {

                    window.parent.postMessage({ resultData: rsp.Data, operateType: 'BatchStartDistribution' }, '*');

                }
            }
        });




    }

    DistributionbaseListColumnEdit = function (keyName, UniqueCode) {
        var itemData = null;
        DataNeedUpdateY.forEach(function (item, i) {
            if (item.UniqueCode == UniqueCode) {
                itemData = item;
            }
        })
        switch (keyName) {
            case 'Subject':
                DistributionbaseListColumnEditSubject(itemData);
                break;
            case 'Price':
                queryQaram.UniqueCode = itemData.UniqueCode
                queryQaram.PriceType = 2; 
                sourceType = keyName;
                isDistributionSetBatch = true;
                DistributionSettingsModule.onDialogSetPrice();
                break;
            case 'SinglePrice':
                queryQaram.UniqueCode = itemData.UniqueCode
                queryQaram.PriceType = 3; //单买价
                sourceType = keyName;
                isDistributionSetBatch = true;
                DistributionSettingsModule.onDialogSetSinglePricePdd()
                break;
            case 'Stock':
                queryQaramStock.UniqueCode = itemData.UniqueCode
                sourceType = 'Stock';
                isDistributionSetBatch = true;
                DistributionSettingsModule.onDialogSetStock()
                break;
            case 'ReferencePrice':
                
                DistributionbaseListColumnEditReferencePrice(itemData,this);
                break;
            default:
                title = '编辑采购价';
                break;
        }
    }
    function getByteLen(val) {
        var len = 0;
        if (!val) return
        for (var i = 0; i < val.length; i++) {
            if (val[i].match(/[^\x00-\xff]/ig) != null) //全角
                len += 2;
            else
                len += 1;
        }
        return len;
    }
    DistributionbaseListColumnEditSubject = function (itemData) {
        var html = '<div class="n-inputWrap" id="dialogSetSubject" style="height:auto;">'
        html += '<input type="text" class="layui-input n-layui-input" style="width: 100%;padding-right:60px;" name="productFullName" value="' + itemData.Subject + '" placeholder="请输入内容" >'
        html += '<span class="input-warnTitle" > 请输入15 - 60个字符（8 - 30个汉字）</span>'
        html += '<span class="input-num">'
        html += '<i class="titleLength" id="productFullNameLen">' + commonModule.getByteLen(itemData.Subject) + '</i>/60'
        html += '</span>'
        html += '</div>'
        var loadIndex = layer.open({
            type: 1,
            title: '编辑标题', //不显示标题
            content: html,
            skin: 'n-skin',
            shadeClose: true, // 点击遮罩关闭层
            area: '560px', //宽高
            shade: 0,
            btn: ['取消', '确定'],
            btn2: function (index) {

                if ($('#dialogSetSubject input[name="productFullName"]').hasClass('addWarnInput')) {
                    return false
                }
                commonModule.Ajax({
                    url: '/api/PtProductInfoDraft/UpdatePtInfoDraftSubject',
                    type: 'POST',
                    showMasker: false,
                    data: {
                        "UniqueCode": itemData.UniqueCode,
                        "Subject": $('#dialogSetSubject input[name="productFullName"]').val()
                    },
                    success: function (rsp) {
                        if (rsp.Success) {
                            layer.close(loadIndex);

                            DistributionbaseListModule.GetPtInfoDrafts(PtProductUniqueCodeArray);
                            //sourceSetDom = null;

                        } else {
                            layer.msg(rsp.Message || rsp.Data || '失败');
                        }
                    }
                })
            },
            success: function () {
                $('#dialogSetSubject input[name="productFullName"]').on('input', function () {
                    var val = $(this).val().trim();
                    var valLen = getByteLen(val);
                    if (valLen < 15 || valLen > 60) {
                        $(this).addClass("addWarnInput");
                        $(this).next().css({ display: 'block' });
                    } else {
                        $(this).removeClass("addWarnInput");
                        $(this).next().css({ display: 'none' });
                    }
                    $('#dialogSetSubject #productFullNameLen').text(valLen)
                })
            },
            cancel: function (index) {
                layer.close(loadIndex)
            },
        });
    }
    DistributionbaseListColumnEditReferencePrice = function (itemData,that) {
        var ReferencePrice = $(that).val();
        console.log("ReferencePrice", ReferencePrice)
        var _SalePrice = replaceHtml(itemData.SalePrice)
        if (!ReferencePrice) {
            $(that).val('');
            return false
        }
        commonModule.Ajax({
            url: '/api/PtProductInfoDraft/UpdatePtInfoDraftReferencePrice',
            type: 'POST',
            showMasker: false,
            data: {
                "UniqueCode": itemData.UniqueCode,
                "ReferencePrice": ReferencePrice,
            },
            success: function (rsp) {
                if (rsp.Success) {
                    DistributionbaseListModule.GetPtInfoDrafts(PtProductUniqueCodeArray);
                } else {
                    $(that).val('');
                    layer.msg(rsp.Message || rsp.Data || '失败');
                }
            }
        })
    }
    replaceHtml = function (str) {
        if (str && str.indexOf('-') > -1) {
            str = str.split('-')[1];
        }
        str = parseFloat(str.replace('￥', ""));
        return str;
    }
    return module;
})(DistributionbaseListModule || {}, jQuery, layer);