var BatchListingSettingObj = {};  //所有铺货设置对象
var renderBasePriceSetHtml = null; //渲染价格设置
var BasePriceSetMoveUpOrDown = null;
var changePriceSwitchFun = null;
var changeBatchListingRaio = null;
var changeBatchListingInput = null;
var DistributionNextStep = null;
var BatchBatchselectPrepareDistribution = null;
var BatchEdmitTemplateObj = [];//批量运费模板数据
var DistributionSetLastStep = null;
var changeCheckboxFun = null;


var DistributionbaseSetModule = (function (module, $, layer) {


    var getFxUserId = commonModule.getQueryVariable('FxUserId') || '';
    var getFromCode = commonModule.getQueryVariable('FromCode') || '';
    var getFromType = commonModule.getQueryVariable('FromType') || (getFromCode ? 4 : getFxUserId ? 3 : 2); //0跟据基础商品铺货，1 跟据平台资料，2 跟据自己货盘，3 跟据厂商货盘，4 铺货任务

    BatchListingSettingObj = {
        "Id": "0",
        "UserListingSetting": {
            "IsDefault": true,
            "PlatformType": setPlatformTypeName,
            "ListingSettingValue": null,
            "BatchSettingValue": {
                "PriceSetting": "ByDouyinPrice,ByDistributorPrice,ByPurChasePrice",
                "ByDouyinPrice": true,
                "ByPurChasePrice": true,
                "ByPurChasePrice_Percent": "",
                "ByPurChasePrice_Num": "",
                "ByDistributorPrice": false,
                "ByDistributorPrice_Percent": "",
                "ByDistributorPrice_Num": "",
                "MinuteOfArc": "",
                "Repertory": "",
                "ProductStatus": 2,
                "Delivery": {
                    "DeliveryMode": 1,
                    "DeliveryTime": 1
                },
                "HonourAgreement": {
                    "ExpressTemplateGroupCode": "",
                    "AftersalesPolicy": 1
                },
                "WarehouseCalcRule": 1,
                "ReturnPolicyDays": true,//7天无理由 （2024-12-06 新增）
                "FakeOneCompensateTen": true,//假一赔十（2024-12-06 新增
                "DamageCoverage": false, //坏了包赔（2024-12-06 新增
                "SinglePrice_Num": null, //单买价
                "ReferencePrice_Num": null, //参考价
                "BulkDiscount": null, // 满减折扣
            },
            "ServiceMobile": "string"
        },
        "ProductUids": edmitBaseproductuidArray,
        "PlatformType": setPlatformTypeName,
        "FromType": getFromType,
        "FxUserId": 0
    }

    var BatchListingSettingObjUserListingSetting = null;
    var basePriceSetDatas = [
        { Type: 'ByDouyinPrice', IsChoose: false, Percent: '', Num: '' },
        { Type: 'ByPurChasePrice', IsChoose: false, Percent: '', Num: '' },
        { Type: 'ByDistributorPrice', IsChoose: false, Percent: '', Num: '' }
    ]

    renderBasePriceSetHtml = function () {
        var tplt = $.templates("#basePriceSet_temp");
        var html = tplt.render({
            data: basePriceSetDatas,
        });
        $("#setpriceWrap_ul").html(html);
       
        listenInputBlur();
        SetBatchSettingValue();//修改后赋值给 铺货设置-价格设置
    }
    renderBaseSinglePriceSetHtml = function () {
        var BatchSettingValue = BatchListingSettingObj.UserListingSetting.BatchSettingValue;
        // 单买价
        var tplt = $.templates("#baseSinglePriceSet_temp");
        var html = tplt.render({
            data: BatchSettingValue,
        });
        $('#singlePriceWrap_ul').html(html);
        listenInputBlur();
        SetBatchSettingValue();//修改后赋值给 铺货设置-价格设置
    }
    renderReferencePriceSetHtml = function () {
        var BatchSettingValue = BatchListingSettingObj.UserListingSetting.BatchSettingValue;
        // 单买价
        var tplt = $.templates("#baseReferencePriceSet_temp");
        var html = tplt.render({
            data: BatchSettingValue,
        });
        $('#referencePriceWrap_ul').html(html);
        listenInputBlur();
        SetBatchSettingValue();//修改后赋值给 铺货设置-价格设置
    }

    module.GetBatchListingSetting = function () {//获取铺货设置
        var req = {};
        req.PlatformType = setPlatformTypeName;
        req.FromType = getFromType;
        //req.FxUserId = getFxUserId;
        req.FromType = 0;
        req.FxUserId = 0;
        commonModule.Ajax({
            url: '/api/Listing/GetBatchListing',
            type: "POST",
            loadingMessage: "加载中",
            contentType: 'application/json',
            data: JSON.stringify(req),
            success: function (rsp) {
                if (commonModule.IsError(rsp)) {
                    //$(that).removeClass("active");
                    return;
                }
                if (rsp.Success) {
                    var BatchSettingValue = rsp.Data || {};
                    BatchListingSettingObj.UserListingSetting.BatchSettingValue = BatchSettingValue;
                    SetBaseMinuteOfArcAndRepertory(BatchListingSettingObj);//复现库存规则和角分处理
                    SetBasePriceSetDatas(BatchListingSettingObj);//复现价格设置
                    BatchListingSettingObjUserListingSetting = BatchListingSettingObj.UserListingSetting;
                    getTemplates(BatchSetUserListingSetting);//获取模板
                    
                }
            }

        });

    }


    BasePriceSetMoveUpOrDown = function (index, isTrue) {
        if ($(this).hasClass("stop")) {
            return;
        }
        var tarIndex = index;
        var nextIndex = 0;
        var tarItem = basePriceSetDatas[tarIndex];
        var nextItem = null;
        if (isTrue) {
            nextIndex = tarIndex - 0 - 1;
        } else {
            nextIndex = tarIndex - 0 + 1;
        }
        nextItem = basePriceSetDatas[nextIndex];
        basePriceSetDatas[tarIndex] = nextItem;
        basePriceSetDatas[nextIndex] = tarItem;
        renderBasePriceSetHtml();
    }



    changePriceSwitchFun = function (index) {
        basePriceSetDatas.forEach(function (item, i) {
            if (i == index) {
                item.IsChoose = !item.IsChoose;
            }
        })
        renderBasePriceSetHtml();
    }

    function listenInputBlur() { //监听
        $("#setpriceWrap_ul .n-inputWrap .n-layui-input").off();
        $("#setpriceWrap_ul .n-inputWrap .n-layui-input").on("focus", function () {
            $(this).closest(".n-inputWrap").removeClass("warnInput");
        })
    }

    function SetBasePriceSetDatas(BatchListingSettingObj) {
        basePriceSetDatas.forEach(function (item, i) {
            if (item.Type == "ByDouyinPrice") {
                item.IsChoose = BatchListingSettingObj.UserListingSetting.BatchSettingValue.ByDouyinPrice
            }
            if (item.Type == "ByPurChasePrice") {
                item.Percent = BatchListingSettingObj.UserListingSetting.BatchSettingValue.ByPurChasePrice_Percent;
                item.Num = BatchListingSettingObj.UserListingSetting.BatchSettingValue.ByPurChasePrice_Num;
                item.IsChoose = BatchListingSettingObj.UserListingSetting.BatchSettingValue.ByPurChasePrice;
            }
            if (item.Type == "ByDistributorPrice") {
                item.Percent = BatchListingSettingObj.UserListingSetting.BatchSettingValue.ByDistributorPrice_Percent;
                item.Num = BatchListingSettingObj.UserListingSetting.BatchSettingValue.ByDistributorPrice_Num;
                item.IsChoose = BatchListingSettingObj.UserListingSetting.BatchSettingValue.ByDistributorPrice;
            }
        })
        var sortTyps = BatchListingSettingObj.UserListingSetting.BatchSettingValue.PriceSetting.split(",");
        var newBasePriceSetDatas = [];
        sortTyps.forEach(function (item) {
            basePriceSetDatas.forEach(function (cItem) {
                if (item == cItem.Type) {
                    newBasePriceSetDatas.push(cItem);
                }
            })
        })
        basePriceSetDatas = newBasePriceSetDatas;

        renderBasePriceSetHtml();//渲染价格设置
        renderBaseSinglePriceSetHtml();//渲染单笔价格设置
        renderReferencePriceSetHtml();//渲染参考价格设置
        
    }

    function SetBaseMinuteOfArcAndRepertory() {
        console.log("BatchListingSettingObj===",BatchListingSettingObj)
        var MinuteOfArc = BatchListingSettingObj.UserListingSetting.BatchSettingValue.MinuteOfArc;
        var Repertory = BatchListingSettingObj.UserListingSetting.BatchSettingValue.Repertory;
        var BulkDiscount = BatchListingSettingObj.UserListingSetting.BatchSettingValue.BulkDiscount;
        $(".n-newRadio.MinuteOfArc").removeClass("active");
        $(".n-newRadio.Repertory").removeClass("active");
        $("#MinuteOfArc_02").removeClass("setpriceWrap-active");

        if (MinuteOfArc == "") {
            $(".n-newRadio.MinuteOfArc:eq(0)").addClass("active");
        } else {
            $("#MinuteOfArc_02").addClass("setpriceWrap-active");
            $(".n-newRadio.MinuteOfArc:eq(1)").addClass("active");
            $("#MinuteOfArc_value").val(MinuteOfArc);
        }

        if (Repertory == "") {
            $(".n-newRadio.Repertory:eq(0)").addClass("active");
        } else {

            $("#hasRepertory_02").addClass("setpriceWrap-active");
            $(".n-newRadio.Repertory:eq(1)").addClass("active");
            $("#Repertory_value").val(Repertory);

        }
        if (BulkDiscount) {
            $("#BulkDiscount_value").val(BulkDiscount);
        }


    }

    function SetBatchSettingValue() {
        var sortTyps = '';
        basePriceSetDatas.forEach(function (item, i) {
            sortTyps += item.Type;
            if (i != basePriceSetDatas.length - 1) {
                sortTyps += ","
            };
            if (item.Type == "ByDouyinPrice") {
                BatchListingSettingObj.UserListingSetting.BatchSettingValue.ByDouyinPrice = item.IsChoose;
            }
            if (item.Type == "ByPurChasePrice") {
                BatchListingSettingObj.UserListingSetting.BatchSettingValue.ByPurChasePrice_Percent = item.Percent;
                BatchListingSettingObj.UserListingSetting.BatchSettingValue.ByPurChasePrice_Num = item.Num;
                BatchListingSettingObj.UserListingSetting.BatchSettingValue.ByPurChasePrice = item.IsChoose;

            }
            if (item.Type == "ByDistributorPrice") {
                BatchListingSettingObj.UserListingSetting.BatchSettingValue.ByDistributorPrice_Percent = item.Percent;
                BatchListingSettingObj.UserListingSetting.BatchSettingValue.ByDistributorPrice_Num = item.Num;
                BatchListingSettingObj.UserListingSetting.BatchSettingValue.ByDistributorPrice = item.IsChoose;
            }

        })
        BatchListingSettingObj.UserListingSetting.BatchSettingValue.PriceSetting = sortTyps;
    }

    changeBasePrice = function (type) {
        var val = $(this).val().trim();
        if (val != "") {
            var re = "";
            if ($(this).hasClass('n-input-price')) { //匹配价格
                re = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/; //金额正则
            } else if ($(this).hasClass('n-init')) {
                re = /^[1-9]\d*$/; //整数正则
            } else {
                re = /^[0-9]+(\.[0-9]+)?$/;
            }
            if (re.test(val) === false) {
                if (type == "ByPurChasePrice_Num" || type == "ByDistributorPrice_Num" || type == "SinglePrice_Num" || type == "ReferencePrice_Num") {
                    val = 0;
                    $(this).val("0");
                } else {
                    $(this).val("");
                    $(this).closest(".n-inputWrap").addClass("warnInput");
                }
                return;
            }
        } else {
            if (type == "ByPurChasePrice_Num" || type == "ByDistributorPrice_Num"|| type == "SinglePrice_Num" || type == "ReferencePrice_Num") {
                val = 0;
                $(this).val("0");
            } else {
                $(this).val("");
            }

            //commonModule.w_alert({
            //    type: 2,
            //    content: '请输入!'
            //});
        }

        BatchListingSettingObj.UserListingSetting.BatchSettingValue[type] = val;
        SetBasePriceSetDatas(BatchListingSettingObj);
    }

    changeBatchListingRaio = function (field, type) {
        $("." + field).removeClass("active");
        $(this).addClass("active");
        $(this).addClass("active");
        $("#MinuteOfArc_02").removeClass("setpriceWrap-active");
        $("#MinuteOfArc_01").removeClass("setpriceWrap-active");
        $("#hasRepertory_02").removeClass("setpriceWrap-active");

        $(this).closest(".setpriceWrap-item ").addClass("setpriceWrap-active");;

        if (type == 1) {
            BatchListingSettingObj.UserListingSetting.BatchSettingValue[field] = "";
        } else {
            BatchListingSettingObj.UserListingSetting.BatchSettingValue[field] = $("#" + field + "_value").val();
        }
    }

    changeBatchListingInput = function (field) {
        $(this).closest(".n-inputWrap").removeClass("warnInput");
        var val = $(this).val().trim();
        if (field == 'MinuteOfArc') {
            re = /^[0-9]+(\.[0-9]+)?$/;
        } else {
            re = /^[0-9]\d*$/; //整数正则
        }
        if (field == 'BulkDiscount') {
            re = /^\d+(\.\d)?$/   //一位小数
            if(re.test(val) === false || val > 9.9 || val < 5){
                $(this).val("");
            } else{
                BatchListingSettingObj.UserListingSetting.BatchSettingValue[field] = val;
            }
        }
        if (re.test(val) === false && field != 'BulkDiscount') {
            $(this).closest(".n-inputWrap").addClass("warnInput");
            $(this).val("");
        }

        var activeLen = $(this).closest(".setpriceWrap-item").find(".n-newRadio.active").length;
        if (activeLen > 0) {
            BatchListingSettingObj.UserListingSetting.BatchSettingValue[field] = val;
        }
    }

    function checkBatchListingSetting() {  //检查铺货设置

        var MinuteOfArc = BatchListingSettingObj.UserListingSetting.BatchSettingValue.MinuteOfArc;
        var Repertory = BatchListingSettingObj.UserListingSetting.BatchSettingValue.Repertory;
        var isReturn = false;

        var BatchSettingValue = BatchListingSettingObj.UserListingSetting.BatchSettingValue;
        if (BatchSettingValue.ByPurChasePrice_Percent == "") {
            if (BatchSettingValue.ByPurChasePrice) {
                commonModule.w_alert({
                    type: 2,
                    content: '请正确填写价格设置!'
                });
                isReturn = true;
            }
        }
        if (BatchSettingValue.ByDistributorPrice_Percent == "") {
            if (BatchSettingValue.ByDistributorPrice) {
                commonModule.w_alert({
                    type: 2,
                    content: '请正确填写价格设置!'
                });
                isReturn = true;
            }
        }
        if (!BatchSettingValue.SinglePrice_Num && setPlatformTypeName == "Pinduoduo") {
            commonModule.w_alert({
                type: 2,
                content: '请正确填写单买价格设置!'
            });
            isReturn = true;
        }
        if (!BatchSettingValue.ReferencePrice_Num && setPlatformTypeName == "Pinduoduo") {
            commonModule.w_alert({
                type: 2,
                content: '请正确填写参考价格设置!'
            });
            isReturn = true;
        }

        if ($(".hasMinuteOfArc").hasClass("active") && MinuteOfArc == "") {
            commonModule.w_alert({
                type: 2,
                content: '请填写角分处理!'
            });
            isReturn = true;
        }


        if ($(".hasRepertory").hasClass("active") && Repertory == "") {
            commonModule.w_alert({
                type: 2,
                content: '请填库存规则!'
            });
            isReturn = true;
        }


        $("#New_setPrepareDistribution .catePropsItem").each(function (index, item) {
            if (!$(item).hasClass('hasActive')) {
                $(item).addClass("formWarn");
                commonModule.w_alert({
                    type: 2,
                    content: '请选择基础设置！'
                });
                isReturn = true;
            }

        })

        return isReturn;

    }


    /*----基础设置开始---------------------------------------------*/

    BatchselectPrepareDistribution = function (type, value) {
        var text = $(this).html();
        $(this).closest(".n-mySelect").find('.n-mySelect-showContent-ul-li').removeClass('activeItem');
        $(this).addClass('activeItem').closest(".n-mySelect").addClass("hasActive").find('.n-mySelect-title-chooseItem').html(text);

        var BatchSettingValue = BatchListingSettingObj.UserListingSetting.BatchSettingValue;
        if (type == "ProductStatus" || type == "WarehouseCalcRule") {
            BatchSettingValue[type] = value;
        }
        if (type == "DeliveryTime") {
            BatchSettingValue.Delivery.DeliveryTime = value;
        }
    }

    function BatchSetUserListingSetting(UserListingSetting) {   //铺货设置赋值
        UserListingSetting = BatchListingSettingObjUserListingSetting

        if (!UserListingSetting || UserListingSetting == null || JSON.stringify(UserListingSetting) == "{}") {
            UserListingSetting = {
                Id: 1,
                IsDefault: false,
                PlatformType: setPlatformTypeName,
                BatchSettingValue: {
                    ProductStatus: 2,
                    Delivery: {
                        DeliveryMode: 1,
                        DeliveryTime: 3
                    },
                    HonourAgreement: {
                        ExpressTemplateGroupCode: "",  //"c08356ddbd1d6b04"
                        AftersalesPolicy: 1
                    },
                    WarehouseCalcRule: 1
                },
                ListingLogisticsTemplateGroupCode: null,
                ServiceMobile: null
            }
        }

        if (!UserListingSetting.IsDefault && UserListingSetting.BatchSettingValue == null) {

            UserListingSetting.BatchSettingValue = {
                ProductStatus: 2,
                Delivery: {
                    DeliveryMode: 1,
                    DeliveryTime: 3
                },
                HonourAgreement: {
                    ExpressTemplateGroupCode: "",  //"c08356ddbd1d6b04"
                    AftersalesPolicy: 1
                },
                WarehouseCalcRule: 1
            }
        }

        //完成铺货的商品状态
        var ListingSettingValue = UserListingSetting.BatchSettingValue;

        var ProductStatus = ListingSettingValue.ProductStatus;
        if (ProductStatus) {
            var productStatusTitle = ProductStatus == 1 ? '立即上架' : ProductStatus == 2 ? '放入仓库' : ProductStatus == 3 ? '放入草稿箱' : '';
            $("#Batch_productStatus_set").find(".n-mySelect").addClass("hasActive").find(".n-mySelect-title-chooseItem").text(productStatusTitle);
            $("#Batch_productStatus_set .n-mySelect-showContent-ul .n-mySelect-showContent-ul-li").each(function (index, item) {
                var value = $(item).attr("data-value");
                $(item).removeClass("activeItem");
                if (value == ProductStatus) {
                    $(item).addClass("activeItem");
                }
            });
        }

        //发货
        var Delivery = ListingSettingValue.Delivery;
        var DeliveryTime = Delivery.DeliveryTime;
        if (Delivery.DeliveryTime) {
            // 快手平台发货时间
            if (setPlatformTypeName == "KuaiShou") {
                var DeliveryTimeTitle = DeliveryTime == 1 ? '24小时' : DeliveryTime == 2 ? '48小时' : '';
                $("#Batch_deliveryTime_set").find(".n-mySelect").addClass("hasActive");
                $("#Batch_deliveryTime_set_ks").find(".n-mySelect").addClass("hasActive").find(".n-mySelect-title-chooseItem").text(DeliveryTimeTitle);
                $("#Batch_deliveryTime_set_ks .n-mySelect-showContent-ul .n-mySelect-showContent-ul-li").each(function (index, item) {
                    var value = $(item).attr("data-value");
                    $(item).removeClass("activeItem");
                    if (value == DeliveryTime) {
                        $(item).addClass("activeItem");
                    }
                    
                });
            } else {
                var DeliveryTimeTitle = DeliveryTime == 1 ? '当日发' : DeliveryTime == 2 ? '次日发' : DeliveryTime == 3 ? '48小时内发货' : '';
                $("#Batch_deliveryTime_set").find(".n-mySelect").addClass("hasActive").find(".n-mySelect-title-chooseItem").text(DeliveryTimeTitle);
                $("#Batch_deliveryTime_set .n-mySelect-showContent-ul .n-mySelect-showContent-ul-li").each(function (index, item) {
                    var value = $(item).attr("data-value");
                    $(item).removeClass("activeItem");
                    if (value == DeliveryTime) {
                        $(item).addClass("activeItem");
                    }
                });
            }
        }
        

        //库存计数
        var WarehouseCalcRule = ListingSettingValue.WarehouseCalcRule;
        if (WarehouseCalcRule) {
            var WarehouseCalcRuleTitle = WarehouseCalcRule == 1 ? '下单减库存' : WarehouseCalcRule == 2 ? '付款减库存' : '';
            $("#Batch_warehouseCalcRule_set").find(".n-mySelect").addClass("hasActive").find(".n-mySelect-title-chooseItem").text(WarehouseCalcRuleTitle);
            $("#Batch_warehouseCalcRule_set .n-mySelect-showContent-ul .n-mySelect-showContent-ul-li").each(function (index, item) {
                var value = $(item).attr("data-value");
                $(item).removeClass("activeItem");
                if (value == WarehouseCalcRule) {
                    $(item).addClass("activeItem");
                }
            });
        }
        // 承诺
        if (ListingSettingValue.ReturnPolicyDays) {
            var checkboxArr = ['ReturnPolicyDays','FakeOneCompensateTen','DamageCoverage'];
            for (var i = 0; i < checkboxArr.length; i++) {
                var isCheck = ListingSettingValue[checkboxArr[i]];
                $("span[name="+checkboxArr[i]+"]").removeClass("activeF");
                if (isCheck) {
                    $("span[name="+checkboxArr[i]+"]").addClass("activeF");
                }
            }
        }


        //履约
        var HonourAgreement = ListingSettingValue.HonourAgreement;
        var ExpressTemplateGroupCode = HonourAgreement.ExpressTemplateGroupCode;

        var isHasExpressTemplateGroupCode = false;
        if (ExpressTemplateGroupCode) {

            BatchEdmitTemplateObj.forEach(function (item) {
                if (ExpressTemplateGroupCode == item.UniqueCode && item.IsSelect) {
                    $("#Batch_freightTemplate_ul .n-mySelect-showContent-ul-li").each(function (index, item) {
                        var uniquecode = $(item).attr("data-uniquecode");
                        if (uniquecode == ExpressTemplateGroupCode) {
                            $(item).trigger("click");
                            $(item).addClass("activeItem");

                        }
                    })
                    isHasExpressTemplateGroupCode = true;
                }
            })
        }
        if (!isHasExpressTemplateGroupCode) {
            ExpressTemplateGroupCode = "";
        }
        if (setPlatformTypeName == "Pinduoduo") {
            // 库存计数，拼多多平台隐藏
            $('#Batch_warehouseCalcRule_set').closest('li').hide();
            // 铺货状态，拼多多平台隐藏
            if ($('#Batch_productStatus_set ul li').attr('data-value') == "1") {
                $('#Batch_productStatus_set ul li:eq(1)').remove();
            }
            // 拼多多平台显示
            $('.isshow_pdd').css({display: 'flex'});
        } else if (setPlatformTypeName == "KuaiShou") {
            // 快手平台显示
            $('.isshow_ks').css({display: 'flex'});
            $('.ishide_ks').css({display: 'none'});
        } else {
            // 拼多多平台隐藏
            $('.isshow_pdd').css({display: 'none'});
            // 快手平台隐藏
            $('.isshow_ks').css({display: 'none'});
            $('.ishide_ks').css({display: 'block'});
            
            $('#Batch_warehouseCalcRule_set').closest('li').css({display: 'flex'});
            var isShowList = true;
            $('#Batch_productStatus_set ul li').each(function () {
                if ($(this).attr('data-value') == "1") {
                    isShowList = false;
                }
            })
            if (isShowList) {
                var liHtml = '<li class="n-mySelect-showContent-ul-li" data-value="2" onclick="BatchselectPrepareDistribution.bind(this)("ProductStatus",2)">放入仓库</li>';
                $('#Batch_productStatus_set ul li:eq(0)').after(liHtml);
            }
        }
    }




    BatchSelectSetValTemplate = function (UniqueCode, isTrue, index) {



        if (isTrue == "false") {
            showEdmitFreightTemplate();
            eventTemplatesNav(index)

        } else {

            $("#Batch_freightTemplate_ul .n-mySelect-showContent-ul-li").removeClass("activeItem");
            $(this).addClass("activeItem");

            var selectObj = {};
            BatchEdmitTemplateObj.forEach(function (item) {
                if (item.UniqueCode == UniqueCode) {
                    selectObj = item;
                }
            })
            $("#Batch_template_select").addClass("hasActive");
            $("#Batch_template_select").find(".n-mySelect-title-chooseItem").text(selectObj.TemplateGroupName);
            $(this).closest(".catePropsItem").removeClass("formWarn");

            var ListingSettingValue = BatchListingSettingObj.UserListingSetting.BatchSettingValue;
            ListingSettingValue.HonourAgreement.ExpressTemplateGroupCode = UniqueCode;

        }

    }


    DistributionSetLastStep = function () {

        $("#full-content_step01").show();
        $("#distributionSet_step").hide();

        $("#new_prepareDistribution_nav .prepareDistribution-nav-item").removeClass("active");
        $("#new_prepareDistribution_nav .prepareDistribution-nav-item:eq(0)").addClass("active");

    }


    DistributionSetNextStep = function () {
        //console.log("BatchListingSettingObjaaaaaaaaa", BatchListingSettingObj)
        //return

        if (checkBatchListingSetting()) {
            return;
        }
        var req = BatchListingSettingObj;
        commonModule.Ajax({
            url: '/api/Listing/BatchListingSet',
            type: "POST",
            loadingMessage: "加载中",
            contentType: 'application/json',
            data: JSON.stringify(req),
            success: function (rsp) {
                if (rsp.Success) {
                    $("#new_prepareDistribution_nav .prepareDistribution-nav-item").removeClass("active");

                    PtProductUniqueCodeArray = rsp.Data;
                    // 单个铺货
                    if (PtProductUniqueCodeArray && PtProductUniqueCodeArray.length > 1 && isBatchPuhuo) {
                        $("#new_prepareDistribution_nav .prepareDistribution-nav-item:eq(2)").addClass("active");
                    }
                    DistributionbaseListModule.GetPtInfoDrafts(PtProductUniqueCodeArray);
                } else {
                    commonModule.w_alert({
                        type: 2,
                        content: rsp.Message || '请正确填写价格设置!'
                    });
                }
            }

        });
    }

    refGetPtInfoDrafts = function () {
        DistributionbaseListModule.GetPtInfoDrafts(PtProductUniqueCodeArray);
    }
    changeCheckboxFun = function(keyName) { // 复选框
        var BatchSettingValue = BatchListingSettingObj.UserListingSetting.BatchSettingValue;
		BatchSettingValue[keyName] = !BatchSettingValue[keyName];
        $('span[name="' + keyName + '"]').removeClass('activeF');
		if (BatchSettingValue[keyName]) {
			$('span[name="' + keyName + '"]').addClass('activeF');
		}
	}
    return module;
})(DistributionbaseSetModule || {}, jQuery, layer);