
var chooseRemindItem = null;
var closeCretaeProductShow = null;
var selectBatchFun = null;
var mouseHelpShowText = null;
var searchBrand = null;
var selectCateProps = null;
var inputCateProps = null;
var delCateProps = null;
var selectCommonOptions = null;
var showPlatformDailogFun = null;
var showAddressDailog = null
var showEdmitSendaddress = null;
var showEdmitAfterSaleaddress = null;
var changeOtherAttribute = null;
var changePlatformStatus = null;
var showDailogExpressBillIcon = null;
var createBrandFun = null;
var changeSetInfo = null;
var OperateType = commonModule.getQueryVariable('CreateFrom');//为 edmit 时重新铺货
var TargetShopId = commonModule.getQueryVariable('TargetShopId');
var TargetShopName = decodeURIComponent(commonModule.getQueryVariable('TargetShopName'));
var edmitBaseproductuid = commonModule.getQueryVariable('baseproductuid');
var PtProductUniqueCodeArray = [];//多商品铺货Code
var refreshPage = null;
var goToBaseProductEdmitFun = null;
var gotToAddShopFun = null;
var nextDistribution = null;
var getShopsFun = null;
var changeAllShopsCheck = null;
var goLastNext = null;
var showEdmitFreightTemplate = null;
var getTemplatesFun = null;
var getTemplates = null;
var selectTemplate = null;
var changeTemplateInput = null;
var refreshDeliveryTemplateFun = null;
var setPlatformTypeName = commonModule.getQueryVariable('PlatformType') || 'TouTiao';  //默认抖店平台
var selectSetValTemplate = null;//运费模板选择
var latformRenewUrlFun = null;
var showMoreCateProps = null;
var selectPrepareDistribution = null;
var changeDefaultSet = null;
var AutoCategoryInfos = null;  // 智能预测类目
var BatchEdmitTemplateObj = [];//批量运费模板数据
var eventTemplatesNav = null;
var ShopsData = [];//所有店铺
var Templates = [];//运费模板
var ChooseShopsData = [];//选择后店铺
var ChooseShopId = [];///选择后店铺ID数组
var setBaseProductInfo = null;
var EdmitDistributionItem = null;//批量编辑商品
var goBackBatchDistributionSet = null;//推出批量编辑商品
var recoverBatchEdmit = null;//恢复批量编辑商品
var changeSyncPtProductInfo = null;
var saveBatchDistributionSet = null;//批量编辑 保存
var batchEdmitStepNum = 0;//批量铺货操作步骤
var refGetPtInfoDrafts = null;
var isFxUser = true; // 铺自己true，铺厂家false
var UrlFromType = commonModule.getQueryVariable('FromType') || '';
var initCommonSelectEvent = null;


if (edmitBaseproductuid == "" || !edmitBaseproductuid) {
	var edmitBaseproductuidArray = JSON.parse(localStorage.getItem("edmitBaseproductuid"));
	edmitBaseproductuidArray = Array.isArray(edmitBaseproductuidArray) ? edmitBaseproductuidArray : [];
	var isBatchPuhuo = true;  //是否批量铺货
	edmitBaseproductuid = edmitBaseproductuidArray.join();
	console.log("edmitBaseproductuid", edmitBaseproductuid)
	//0跟据基础商品铺货，1 跟据平台资料，2 跟据自己货盘，3 跟据厂商货盘，4 铺货任务
	if ((UrlFromType && UrlFromType == 3) || UrlFromType == 4) {
		isFxUser = false;
	}
	if (UrlFromType == 0 || UrlFromType == 1 || UrlFromType == 2) {
		isFxUser = true;
	}
}

var DistributionCommonModule = (function (module, $, layer) {
	$(function () {

		window.addEventListener('message', function (event) {
			// 定义子页面中要被父页面调用的方法
			if (event.data === 'closePrepareDistributionIframe') {
				module.goBack();
			}
		});
	})

	module.goBack = function () {
		if ($(".goBackBatchSetSkin").length == 1) {
			return;
		}

		window.parent.postMessage({ operateType: 'closePrepareDistribution', times: 3000 }, '*');

		return
		if ($(".goBackskin").length == 1) {
			return;
		}

		layer.open({
			type: 1,
			title: '未保存，是否退出页面？', //不显示标题
			content: '<div style="font-size:14px;" class="c06">退出后，本次所有更改不会生效。</div>',
			area: '560px', //宽高
			btn: ['留下', '退出页面'],
			shade: false,
			skin: 'n-skin goBackskin',
			yes: function (index) {
				layer.close(index);
			},
			btn2: function () {
				window.parent.postMessage({ operateType: 'closePrepareDistribution' }, '*');
			}
		});
	}

	return module;
})(DistributionCommonModule || {}, jQuery, layer);



