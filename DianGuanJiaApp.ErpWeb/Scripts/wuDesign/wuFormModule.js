; var wuFormModule = (function (wuModule, $, layer) {

	// 搜索条件高亮 开始-----------

	wuModule.initblurInput = function (parentElem) {
		$(parentElem + " .wu-input").each(function (index, item) {
			$(item).on("blur", function () {
				var val = $(item).val().trim();
				if (val) {
					$(item).closest(".wu-inputWrap").addClass("wu-active");
				} else {
					$(item).closest(".wu-inputWrap").removeClass("wu-active");
				}
			})
		})
	}

	wuModule.initSelect = function (parentElem) {
		if (!parentElem) {
			return;
		}
		$(parentElem + " .wu-select").each(function (index, item) {
			$(item).on("change", function () {
				var val = $(item).val().trim();		
				if (val) {
					$(item).closest(".wu-selectWrap").addClass("wu-active");
				} else {
					$(item).closest(".wu-selectWrap").removeClass("wu-active");
				}
			})
		})
	}


	wuModule.initLayuiSelect = function (filterName) {
		var form = layui.form;
		form.on('select(' + filterName + ')', function (data,index) {
			var val = $(data.elem).val();
			var selectedindex = $(data.elem).prop('selectedIndex');
			if (selectedindex!=0) {
				$(data.elem).closest(".wu-layui-select").addClass("wu-active");
			} else {
				$(data.elem).closest(".wu-layui-select").removeClass("wu-active");
			}
		});
	}



	wuModule.initMySelect = function (parentElem) {
		$(parentElem + " .wu-select-skin").each(function (index, item) {
			$(item).addClass("wu-showActive");
		})
	}


	// wuModule.initLayuiSelect('active-select-filter');//针对layui select选择器  active-select-filter  为 layui select lay-filter="active-select-filter" 名称
	// wuModule.initSelect('.wu-searchWrap');	//wu-searchWrap为父元素类名    针对原生select
	// wuModule.initblurInput('.wu-searchWrap');//wu-searchWrap为父元素类名    针对input输入框
	// wuModule.initMySelect(".wu-searchWrap");//wu-searchWrap为父元素类名     针对自建多选下拉框


	//重置样式  wuFormModule.resetConditionsStyle('.wu-searchWrap')
	wuModule.resetConditionsStyle = function (parentElem) {
		if (!parentElem) {
			return;
		}
		$(parentElem + " .wu-inputWrap").each(function (index, item) {
			$(item).removeClass("wu-active");
		})
		$(parentElem + " .wu-selectWrap").each(function (index, item) {
			$(item).removeClass("wu-active");
		})
		$(parentElem + " .wu-layui-select").each(function (index, item) {
			$(item).removeClass("wu-active");
		})
		$(parentElem + " .selectWrap").each(function (index, item) {
			$(item).removeClass("wu-active");
		})
	}

	// 搜索条件高亮 结束-------------


		wuModule.clearInput = function(that) {
			$(that).closest('.wu-inputWrap').find('.wu-input').val('').blur();
			$(that).closest(".wu-inputWrap").removeClass("wu-active");
			$(that).closest(".wu-inputWrap").find("input[type=text]").removeClass('activeInput');
			
		}

		wuModule.switch = function(that, callBack) {
			if ($(that).hasClass("disabled")) {
				return;
			}
			$(that).toggleClass("active");
			if (typeof callBack == "function") {
				callBack();
			}
		}


		wuModule.allTrCheck = function(that, callBack) {
			$(that).toggleClass("checked");
			var isAll = $(that).hasClass("checked");
			$(that).closest("tr").toggleClass("wu-active");
			if(isAll){
				$(that).removeClass("part_checked ");
				
			}
			$(that).closest('table').find(".wu-tbody .wu-my-checkboxWrap").each(function(index, item) {
				var $tr = $(item).closest("tr");
				if (isAll) {
					$(item).addClass("checked");
					$tr.addClass("wu-active");
				} else {
					$(item).removeClass("checked");
					$tr.removeClass("wu-active");
				}
			})
			if (typeof callBack == "function") {
				callBack(isAll);
			}
		}

		wuModule.chooseTr = function(_this, callBack) {
			var isHas = false;
			if ($(_this).hasClass("wu-active")) {
				$(_this).removeClass("wu-active");
				$(_this).find(".wu-my-checkboxWrap").removeClass('checked');
				isHas = false;
			} else {
				$(_this).addClass("wu-active");
				$(_this).find(".wu-my-checkboxWrap").addClass('checked');
				isHas = true;
			}
			var checkedLen = $(_this).closest("table").find("tbody .wu-my-checkboxWrap.checked").length;
			var allLen = $(_this).closest("table").find("tbody .wu-my-checkboxWrap").length;
			var isAll = allLen == checkedLen;
			if (isAll) {
				$(_this).closest("table").find("thead tr .wu-my-checkboxWrap").addClass("checked");
				$(_this).closest("table").find("thead tr").addClass("wu-active");
				$(_this).closest("table").find("thead tr .wu-my-checkboxWrap").removeClass("part_checked");

			} else {
				$(_this).closest("table").find("thead tr .wu-my-checkboxWrap").removeClass("checked");
				$(_this).closest("table").find("thead tr").removeClass("wu-active");

				if (checkedLen > 0) {
					$(_this).closest("table").find("thead tr .wu-my-checkboxWrap").addClass("part_checked");
				} else {
					$(_this).closest("table").find("thead tr .wu-my-checkboxWrap").removeClass("part_checked");
				}
			}

			if (typeof callBack == "function") {
				callBack(_this);
			}

		}

		//导航切换
		wuModule.navActive = function(ele, callBack, active) {
			var active = active ? active : "active"
			$(ele).on("click", function(e) {
				if (e.stopPropagation) {
					e.stopPropagation();
					e.preventDefault();
				} else {
					window.event.returnValue = false;
					window.event.cancelBubble = true;
				};

				var tarEle = e.target;
				var parentEle = tarEle.nodeName.toLocaleUpperCase() == "LI" ? tarEle : $(tarEle)
					.closest("li")[0];

				var tarEleName = tarEle.nodeName.toLocaleUpperCase();
				var parentEleName = parentEle.nodeName.toLocaleUpperCase();

				if ($(parentEle).hasClass("disabled")) {
					return;
				}

				if (tarEleName == "LI" || parentEleName == "LI") {
					if (tarEleName == "LI") {
						$(ele + ">li").removeClass(active);
						$(tarEle).addClass(active);
					} else if (parentEleName == "LI") {
						$(ele + ">li").removeClass(active);
						$(tarEle).closest("li").addClass(active);
					}
					$(ele + ">li").each(function(index, item) {
						if ($(item).hasClass(active)) {
							if (callBack || typeof callBack == "function") {
								callBack(index, item)
							}
						}
					})
				}
			})
		}

		wuModule.wu_alert = function(options) {
			$(".show-w-alert").remove();
			var html = "";
			var type = options.type ? options.type : 1;
			var content = options.content;
			var skin = options.skin ? options.skin : "";
			var className = '';
			var className2 = '';
			if (type == 1) {
				className = "icon-a-heart-filled1x";
				className2 = "wu-processing";
			} else if (type == 2) {
				className = "icon-a-error-circle-filled1x";
				className2 = "wu-error";
			} else if (type == 3) {
				className = "icon-a-error-circle-filled1x";
				className2 = "wu-warning";
			} else if (type == 4) {
				className = "icon-a-check-circle-filled1x";
				className2 = "wu-success";
			} else if (type == 5) {
				className = "icon-a-error-circle-filled1x n-dColor";
				type = 0;
			}
			var id = 'alert' + new Date().getTime();
			if (true) {
				var skinClass = "wu-msg show-w-alert";
				if (skin) {
					skinClass = "wu-msg show-w-alert " + skin;
				}
				html += '<div class="' + skinClass + '" id=' + id + '>';
				html += '<div style="background-color:#fff;border-radius: 8px;">'
				html += '<div class="wu-alert ' + className2 + '" ><i class="iconfont ' + className +
					'"></i><span class="wu-alert-title">' + content + '</span></div>';
				html += '</div>';
				html += '</div>';
				$("body").append(html);
			}
			var times = options.times ? options.times : 3000;
			setTimeout(function() {
				$("#" + id).remove();
			}, times)
		}

		wuModule.wu_toast = function(options) {
			$(".show-w-toast").remove();
			var html = "";
			var type = options.type ? options.type : 1;
			var content = options.content ? options.content : '';
			var skin = options.skin ? options.skin : "";
			var className = '';
			var className2 = '';
			if (type == 1) {
				className = "icon-a-heart-filled1x";
				className2 = "wu-processing";
			} else if (type == 2) {
				className = "icon-a-error-circle-filled1x";
				className2 = "wu-error";
			} else if (type == 3) {
				className = "icon-a-error-circle-filled1x";
				className2 = "wu-warning";
			} else if (type == 4) {
				className = "icon-a-check-circle-filled1x";
				className2 = "wu-success";
			} else if (type == 5) {
				className = "icon-a-error-circle-filled1x n-dColor";
				type = 0;
			}
			var id = 'alert' + new Date().getTime();
			if (true) {
				var skinClass = "wu-msg show-w-toast";
				if (skin) {
					skinClass = "wu-msg show-w-toast " + skin;
				}
				html += '<div class="' + skinClass + '" id=' + id + '>';
				html += '<div class="wu-toast ' + className2 + '" ><i class="iconfont ' + className +
					'"></i><span class="wu-toast-title">' + content + '</span></div>';
				html += '</div>'
				$("body").append(html);
			}
			var times = options.times ? options.times : 3000;
			setTimeout(function() {
				$("#" + id).remove();
			}, times)
		}

		wuModule.wu_modal = function(options) {
			var type = options.type ? options.type : "";
			var title = options.title ? options.title : "";
			var content = options.content ? options.content : "";
			var width = options.width ? options.width : "320px";
			var skin = options.skin ? options.skin : "";
			var btns = options.btns ? options.btns : ['取消', '确定'];

			var html = "";
			var className = '';
			var className2 = '';
			if (type == 1) {
				className = "icon-a-info-circle-filled1x";
				className2 = "wu-processing";
			} else if (type == 2) {
				className = "icon-a-error-circle-filled1x";
				className2 = "wu-error";
			} else if (type == 3) {
				className = "icon-a-check-circle-filled1x";
				className2 = "wu-warning";
			} else if (type == 4) {
				className = "icon-a-check-circle-filled1x";
				className2 = "wu-success";
			}

			html += '<div class="wu-modal">';
			if (type) {
				html += '<span class="wu-modal-icon iconfont ' + className + '"></span>';
			}
			html += '<div class="wu-modal-content">'
			if (title) {
				html += '<div class="wu-modal-title">' + title + '</div>'
			}
			if (content) {
				html += '<div class="wu-modal-main">' + content + '</div>'
			}
			html += '</div>';
			html += '</div>';



			var layerObj = {};
			layerObj.type = 1;
			layerObj.title = false;
			layerObj.content = html;
			layerObj.area = width;
			layerObj.skin = 'wu-dailog wu-right wu-modal-skin ' + skin;
			if (options.success && typeof options.success == "function") {
				layerObj.success = ptions.success
			}
			layerObj.btn = btns;
			layerObj.cancel = function(that, layer) {
				if (typeof options.cancel == "function") {
					options.cancel(layer);
				}
			}
			layerObj.btn1 = function(index) {
				layer.close(index);
				if (typeof options.btn1 == "function") {
					options.btn1(layer);
				}
			}
			layerObj.btn2 = function(that, layer) {
				if (typeof options.btn2 == "function") {
					options.btn2(layer);
				}
			}


			layer.open(layerObj);


		}


		return wuModule;
}(wuFormModule || {}, jQuery, layer));