/// <reference path="jquery-1.10.2.min.js" />
/// <reference path="layer/layer.js" />
var commonModule = (function (common, $, ly) {

    var _areaCodeInfoList = {}; //保存选择过的区域信息
    var _platformAreaCodeInfoList = {}; //保存选择过的区域信息
    var noticeData = {};
    var subscribeData = {}
    var _noticeFirstType = ["Recommend", "Public", "Order", "Product", "Cooperation", "Bill"];
    var _printerBindList = [];
    var _noticeTypeTextDic = { // 通知类型文本字典
        Product: "商品",
        Cooperation:"合作",
        Order:"订单",
        Bill:"账单",
        Public:"公告",
        ExceptionOrder:"异常订单",
        AfterSaleNotification:"售后通知"
    }
    var isTKPrint = false; // 是否正在打印TK订单

    var _logisticsTraceList = []; //保存接口返回的物流轨迹配置

    // 数组判断兼容IE8
    if (!Array.isArray) {
        Array.isArray = function (arg) {
            return Object.prototype.toString.call(arg) === '[object Array]';
        };
    }
    var _objectClone = function (instance) {
        var newObj = {};
        for (var i in instance) {
            if (instance[i] == null) {
                newObj[i] = null;
            }
            else if (Array.isArray(instance[i]) == true) {
                newObj[i] = _arrayClone(instance[i]);
            }
            else if (typeof (instance[i]) == 'object') {
                newObj[i] = _objectClone(instance[i]);
            }
            else if (typeof (instance[i]) == 'function') {
                newObj[i] = _functionClone(instance[i]);
            }
            else {
                newObj[i] = instance[i];
            }
        }
        return newObj;
    };
    var _arrayClone = function (instance) {
        var newArray = [];
        for (var i = 0; i < instance.length; i++) {
            if (instance[i] == null) {
                newArray[i] = null;
            }
            else if (Array.isArray(instance[i]) == true) {
                newArray.push(_arrayClone(instance[i]));
            }
            else if (typeof (instance[i]) == 'object') {
                newArray.push(_objectClone(instance[i]));//newArray[i] = _objectClone(instance[i]);
            }
            else if (typeof (instance[i]) == 'function') {
                newArray[i] = _functionClone(instance[i]);
            } else {
                newArray.push(instance[i]);//newArray[i] = instance[i];
            }
        }
        return newArray;
    };
    var _functionClone = function (instance) {
        var that = instance;
        var newFunc = function () {
            return that.apply(this, instance.arguments);
        };
        for (var i in this) {
            newFunc[i] = this[i];
        }
        return newFunc;
    };

    common.SupportPlatformTypes = ["TouTiao", "KuaiShou", "Taobao", "WxVideo", "AlibabaC2M", "Pinduoduo", "YouZan", "XiaoHongShu", "Jingdong", "OwnShop"];//支持上传多单号回流的平台
    common.PlatformNameDic = {
        "TouTiao": "抖店",
        "KuaiShou": "快手",
        "Taobao": "淘宝",
        "WxVideo": "视频号",
        "AlibabaC2M": "淘工厂",
        "Pinduoduo": "拼多多",
        "YouZan": "有赞",
        "XiaoHongShu": "小红书",
        "Jingdong": "京东",
        "OwnShop": "自有商城"
    };
    common.WaitStatusSendPts = ["TouTiao", "AlibabaC2M", "Jingdong", "OwnShop"]; // 待发货状态上传多包裹发货平台
    common.WaitStatusSendedPts = ["KuaiShou", "Taobao", "WxVideo", "XiaoHongShu"]; // 已发货的商品不能发，待发货的商品可以发
    common.SendStatusSendPts = ["KuaiShou"];// 已发货状态上传多包裹发货平台 (拼多多独立处理)
    common.ManyCodeSendAway = ""; // 混合多运单号发货处理方式：0：先发单个单号，再弹窗发多运单号，1：全部使用新单号发货，空：每次弹窗确认
    common.PlatformType = ''; //站点平台
    common.CurrentPlatformAuthEntry = ''; //平台授权入口
    common.DigitalProductPlatformType = ["Taobao", "KuaiShou"]; //支持数码产品的平台，需要上传SN/IMTE码

    // 是否是头条系平台
    common.IsTouTiaoXi = function (pt) {
        var isTouTiaoXi = false;
        var platformType = ((pt || "") == "" ? (common.PlatformType || "") : pt).toLowerCase();
        if (platformType == "toutiao" || platformType == "tt" ||
            platformType == "zhidian" || platformType == 'zd' ||
            platformType == "douyinxiaodian" || platformType == "dyxd" ||
            platformType == "toutiaoxiaodian" || platformType == "ttxd" ||
            platformType == "luban" || platformType == "lb") {
            isTouTiaoXi = true;
        }
        return isTouTiaoXi;
    }

    // 系统权限校验相关
    // 导出权限校验  checkType : order=订单 waybillCode=底单 printHistory=打印记录 sendOrder=已发货订单 afterSale=售后单 stockDetail=库存详情
    common.CheckExportPermission = function (callback, checkType) {
        common.FxPermission(function (p) {
            var permission = undefined;
            if (checkType == 'order') {
                var type = common.FxPageType()
                if (type == 2)
                    permission = p.ExportWaitOrder;
                else if (type == 1)
                    permission = p.ExportAllOrder;
                else if (type == 3)
                    permission = p.ExportOfflineOrder;
            } else if (checkType == 'waybillCode') {
                var type = getWaybillCodePageType();
                if (type ==22)
                    permission = p.ExportWaybillCode;
                else if (type == 11)
                    permission = p.ExportWaybillCode_Cj;
            } else if (checkType == 'printHistory') {
                permission = p.ExportPrintHistory;
            } else if (checkType == 'sendOrder') {
                permission = p.ExportSendOrder;
            } else if (checkType == 'afterSale') {
                permission = p.ExportAfterSale;
            } else if (checkType == 'stockDetail') {
                permission = p.ExportStockDetail;
            } else {
                layer.msg("权限校验失败，请联系我们");
                callback(false);
            }
            common.CheckPermission(function (success) {
                callback(success);
            },permission);
        });
        //console.log("sadad");
        /*var url = '/CheckExportPermission';
        var data = null;
        
        //console.log(page);
        // 导出权限校验
        common.Ajax({
            type: "POST",
            url: url,
            data: data,
            success: function (rsp) {
                //console.log(rsp);
                if (rsp.Success) {
                    if (rsp.Data == true) {
                        if (typeof (callback) == 'function')
                            callback(true);
                    }  
                    else {
                        layer.msg("暂无权限，请联系管理员");
                    }
                } else {
                    layer.msg(rsp.Message);
                }
            }
        });
        callback(false);*/
    }

    // 指定权限的校验 type: null=默认，校验单条 or = 校验多条，逻辑或
    common.CheckPermission = function (callback, permission, type, ignoreWarn, kind) {
        
        console.log("check");
        if (!permission)
        {
            // 从接口重新获取权限列表
            _getPermissions(function () {
                
            }, true);

            console.error("权限校验异常：未指定权限" + permission);
            //return false;
            ignoreWarn = true;
        }
        var url = "/SubAccount/CheckPermission";
        if (type && type == "or") {
            url = "/SubAccount/CheckPermissionOR";
        }
        common.Ajax({
            type: "POST",
            url: url,
            data: {permission},
            success: function (rsp) {
                //console.log(rsp);
                if (rsp.Success) {
                    if (rsp.Data == true) {
                        
                        if (typeof (callback) == 'function')
                            callback(true);
                    }
                    else if (!ignoreWarn){ 
                        
                        if(kind == 1) {
                            basicsetModule.setConfig('cantBindPhone')
                        } else if (kind == 2) {
                            basicsetModule.setConfig('cantBindWx')
                        } else if (kind == 3) {
                            basicsetModule.setConfig('cantBindPhone')
                        } else if (kind == 4) {
                            
                        } else {
                            layer.msg("暂无权限，请联系管理员");
                        }
                        
                        
                       
                    }
                } else {
                    layer.msg(rsp.Message || '请求失败，请重试');
                }
            }
        });
        callback(false);
    }
    common.accoutnSetTip = function() {
        layer.open({
            type: 1,
            skin: 'n-skin',
            title: '暂无权限',
            area: ['480px', '152px'],
            content: $.templates("#accountSetTipsDialog").render(),
            btn: ['知道了'],
        })
    }
    // 隐藏页面上无权限的元素
    common.HideNoPermDiv = function (showPermDict) {
        for (var key in showPermDict) {
            if (showPermDict.hasOwnProperty(key)) {
                var value = showPermDict[key];
                if (value) {
                    $(key).show();
                } else {
                    $(key).hide();
                }
            }
        }
    }

    var getWaybillCodePageType = function () {
        // 获取当前页面 URL 中的查询字符串
        var queryString = window.location.search;

        // 去掉查询字符串中的问号
        var queryStringWithoutQuestionMark = queryString.slice(1);

        // 将查询字符串转换为一个对象，以便访问参数
        var queryParams = {};
        queryStringWithoutQuestionMark.split('&').forEach(function (param) {
            var parts = param.split('=');
            var key = parts[0];
            var value = decodeURIComponent(parts[1]);
            queryParams[key] = value;
        });

        // 获取特定参数的值
        var paramValue = queryParams['type'];
        if (paramValue == 'cj') { // 厂家底单页面
            return 11;
        } else return 22; // 底单查询页面
    }

    common.ConvertToRealPlatformType = function (pt) {
        if (common.IsTouTiaoXi(pt))
            return "TouTiao";
        return pt;
    }

    common.CanUpdateSellerRemark = function (pt) {
        pt = pt.toLowerCase();
        return pt != "mengtui" && pt != "yunji" && pt != "vipshop" && common.IsPddFds() == false; //pt != "toutiao" && 
    }

    var _flag_position = {
        '0': { x: -70, y: -20 },   //灰色
        '1': { x: 0, y: -20 },   //红色
        '4': { x: -14, y: -20 }, //黄色
        '3': { x: -28, y: -20 }, //青色
        '2': { x: -42, y: -20 }, //蓝色
        '5': { x: -56, y: -20 }, //紫色
    }

    common.StampTemplates = []; //常用模板

    common.GetFlagPosition = function () { return _flag_position; }

    //是否是菜鸟快递模板
    common.IsCainiaoTemplate = function (templateType) {
        return templateType == 2 || (templateType > 3 && templateType <= 9);
    }

    //是否是菜鸟快运模板
    common.IsKuaiYunTemplate = function (templateType) {
        return templateType == 7 || templateType == 8;
    }

    //是否是菜鸟官方模板
    common.IsLinkTemplate = function (templateType) {
        return templateType >= 40 && templateType < 50;
    }

    //是否是菜鸟官方快运模板
    common.IsLinkKuaiYunTemplate = function (templateType) {
        return templateType >= 50 && templateType < 60;
    }

    //是否是普通模板 (1:传统面单，2:网点面单，3:顺丰丰桥，13:丰桥云模板）
    common.IsNormalTemplate = function (templateType) {
        return templateType == 1 || templateType == 3 || templateType == 10 || templateType == 13;
    }

    //是否是网点中通
    common.IsZhiLian = function (templateType, companyCode) {
        if (templateType == 3 && (companyCode == "ZTO" || companyCode == "STO" || companyCode == "YTO" || companyCode == "HTKY" || companyCode == "JT"))
            return true;
        return false;
    }

    //是否是顺丰
    common.IsSF = function (templateType) {
        return templateType == 10;
    }

    //是否是网点模板 (1:传统面单，2:网点面单，3:顺丰丰桥,13:丰桥云模板）
    common.IsSiteTemplate = function (templateType) {
        return templateType == 3 || templateType == 10 || templateType == 13;
    }

    //是否是头条的电子面单 (11,12:头条电子面单）
    common.IsTouTiaoTemplate = function (templateType) {
        return templateType == 11 || templateType == 12;
    }

    //是否是快手的电子面单 (111 - 120 快手电子面单）
    common.IsKuaiShouTemplate = function (templateType) {
        return templateType >= 111 && templateType <= 120;
    }
    //是否是快手快运的电子面单 (114）
    common.IsKuaiShouKuaiYunTemplate = function (templateType) {
        return templateType == 114;
    }

    //是否是抖店组件电子面单 (17,18:抖店组件电子面单）
    common.IsTouTiaozjTemplate = function (templateType) {
        return templateType == 17 || templateType == 18;
    }

    //是否是抖店快运模板（19，20）
    common.IsTouTiaoKuaiYunTemplate = function (templateType) {
        return templateType == 19 || templateType == 20;
    }

    //是否是拼多多模板
    common.IsPddTemplate = function (templateType) {
        return templateType > 20 && templateType < 30;
    }

    //是否是拼多多快运模板
    common.IsPddKuaiYunTemplate = function (templateType, companyCode) {

        if (templateType > 30 && templateType < 40)
            return true;

        var exCodes = ["SFKY", "CN7000001021040", "CN7000001000869", "3108002701_1011", "BESTQJT", "CNEX", "2460304407_385", "CRE", "CN7000001009020", "CP471906", "2744832184_543"];  //这里是下面添加的快递公司对应的code
        if (common.IsPddTemplate(templateType) && exCodes.indexOf(companyCode) > -1)
            return true;

        return false;
    }

    //是否是京东无界模板
    common.IsJdWjTemplate = function (templateType) {
        return templateType >= 60 && templateType < 80;
    }

    //是否是京东快递模板
    common.IsJdKdTemplate = function (templateType) {
        return templateType > 80 && templateType < 90;
    }

    //是否是京东组件模板
    common.IsJdzjTemplate = function (templateType) {
        return templateType >= 91 && templateType < 96;
    }

    //是否是京东无界组件模板
    common.IsJdWjzjTemplate = function (templateType) {
        return templateType >= 96 && templateType < 100;
    }

    //是否是小红书的电子面单 (150 - 160 小红书电子面单）
    common.IsXiaoHongShuTemplate = function (templateType) {
        return templateType >= 150 && templateType < 160;
    }
    //是否是新版小红书的电子面单 (180 - 190 小红书电子面单）
    common.IsNewXiaoHongShuTemplate = function (templateType) {
        return templateType >= 180 && templateType < 190;
    }

    //是否是视频号电子面单 (160 - 170视频号电子面单）
    common.IsWxVideoTemplate = function (templateType) {
        return templateType >= 160 && templateType < 170;
    }

    common.HandleXiaoHongShuTemplate = function (pt, tid) {
        if (pt && pt.toLocaleLowerCase() == "xiaohongshu") {
            return tid >= 180 && tid < 190 ? "【新版】" : "【旧版】"
        } else {
            return ""
        }
    }

    //找出网点
    common.FindBranch = function (branchList, templateBranch) {

        var getAddress = function (p, c, a, t, d) {
            var addr = "";
            if (p && p.toLowerCase() != 'null') {
                addr += p;
            }

            if (c && c.toLowerCase() != 'null') {
                addr += c;
            }

            if (a && a.toLowerCase() != 'null') {
                addr += a;
            }

            if (t && t.toLowerCase() != 'null') {
                addr += t;
            }

            if (d && d.toLowerCase() != 'null') {
                addr += d;
            }

            return addr.replace(/ /g, '');
        }

        //取出相应的网点信息
        var tempList = [];
        common.Foreach(branchList, function (i, o) {
            var tmpeBranchCode = templateBranch.BranchCode == null || templateBranch.BranchCode == undefined ? "" : templateBranch.BranchCode;
            var apiBranchCode = o.BranchCode == null || o.BranchCode == undefined ? "" : o.BranchCode;

            if (templateBranch.BrandCode != null && templateBranch.BrandCode != "") {
                if (apiBranchCode == tmpeBranchCode
                    && (o.BranchHashCode == templateBranch.BranchHashCode
                        || templateBranch.BranchAddress.replace(/ /g, '') == getAddress(o.Province, o.City, o.Area, o.Town, o.Detail)
                        || templateBranch.BranchAddress.replace(/ /g, '') == getAddress(o.Province, o.City, o.Area, o.Detail))
                    && o.BrandCode == templateBranch.BrandCode
                ) {
                    tempList.push(o);
                }
            } else {
                if (apiBranchCode == tmpeBranchCode
                    && (o.BranchHashCode == templateBranch.BranchHashCode
                        || templateBranch.BranchAddress.replace(/ /g, '') == getAddress(o.Province, o.City, o.Area, o.Town, o.Detail)
                        || templateBranch.BranchAddress.replace(/ /g, '') == getAddress(o.Province, o.City, o.Area, o.Detail))
                ) {
                    tempList.push(o);
                }
            }
        });
        var branch = null;
        if (tempList.length == 1)
            branch = tempList[0];
        else if (tempList.length > 1) {
            if (templateBranch.SegmentCode != null && templateBranch.SegmentCode != "null") {
                //先按模板号段取
                var segmentList = [];
                common.Foreach(tempList, function (i, o) {
                    if (o.SegmentCode == templateBranch.SegmentCode) {
                        segmentList.push(o);
                    }
                });
                if (segmentList.length == 1)
                    branch = segmentList[0];
                else {
                    var templateServiceList = templateBranch.LogisticsServiceList;
                    branch = common.FindBranchByTemplateServiceCode(segmentList, templateServiceList);
                }
            }
            else {
                var templateServiceList = templateBranch.LogisticsServiceList;
                branch = common.FindBranchByTemplateServiceCode(tempList, templateServiceList);
            }
        }
        return branch;
    }

    //根据模板服务查找网点
    common.FindBranchByTemplateServiceCode = function (branchList, templateServiceList) {
        var tempBranch = [];
        common.Foreach(branchList, function (i, o) {
            var isServiceNotFinded = false; //是否有服务未找到
            common.Foreach(templateServiceList, function (ii, s) {
                var serviceFinded = false; //服务是否找到
                common.Foreach(o.ServiceInfoCols, function (j, os) {
                    if (s.ServiceCode == os.ServiceCode) {
                        serviceFinded = true; //已找到
                        return 'break';
                    }
                });
                //有服务未找到，说明当前遍历的网点不符合
                if (serviceFinded == false) {
                    isServiceNotFinded = true;
                    return 'break';
                }
            });
            //服务都找到了，说明这个网点就是
            if (isServiceNotFinded == false) {
                tempBranch.push(o);
            }
        });
        if (tempBranch.length == 0)
            return null;
        if (tempBranch.length == 1)
            return tempBranch[0];

        //按网点增值服务个数排序，取增值服务个数最少的网点
        tempBranch = common.SortExt(tempBranch, "ServiceInfoCols.length");
        return tempBranch[0];
    }

    //转义正则表达式特殊字符
    common.TranRegCharts = function (str) {
        //var charts = ['{', '}', '[', ']', '(', ')', '\\', '^', '$', '*', '+', '.', '|', '?'];
        var charts = "*.?+$^[](){}|\\";
        var result = '';
        for (var i = 0; i < str.length; i++) {
            if (charts.indexOf(str[i]) > -1) {
                result += '\\' + str[i];
            }
            else {
                result += str[i];
            }
        }
        return result;
    }
    common.GelVal = function (val) {
        if (val == null || val == undefined)
            return '';
        return val;
    }
    common.Clone = function (instance) {
        if (Array.isArray(instance) == true) {
            return _arrayClone(instance);
        }
        else if (typeof instance == "object") {
            return _objectClone(instance);
        }
        else if (typeof instance == 'function') {
            return _functionClone(instance);
        } else {
            return instance;
        }
    }

    //阻止冒泡和默认行为
    common.StopPropagation = function (event) {
        if (event) {
            event.stopPropagation();
            event.preventDefault();
        } else {
            window.event.returnValue = false;
            window.event.cancelBubble = true;
        };
    }

    //复制控件文本
    common.CopyText = function (selector, msg = null) {
        //event.stopPropagation();
        common.stopM(event);
        var newId = "txt" + (new Date()).getTime();
        $('body').append("<textarea id=\"" + newId + "\" style=\"width:1px; height:1px;\">" + ($(selector).val() || $(selector).text()) + "</textarea>");
        var txt = document.getElementById(newId);
        txt.select();
        document.execCommand("Copy");
        $("#" + newId).remove()
        msg = msg || "已复制，可粘贴";
        // layer.msg(msg);
        common.w_alert({ type: 4, content: msg });
    }

    //复制元素内容  使用：<div id="textDiv1" onclick="commonModule.execClick(event);" oncopy="commonModule.execCopy(event,'textDiv1');">这里是要复制的文字</div>
    common.execClick = function (event) {
        event.stopPropagation();
        document.execCommand("copy");
    }
    common.execCopy = function (event, textDiv, id) {

        var textDivVal = textDiv.toString();

        if (textDiv == "allAdress") {
            textDivVal = $("#txtReceiver_" + id + "").val() + "," + $("#txtReceiver_phone_" + id + "").val() + "," + $("#txtReceiver_address_" + id + "").val();
        } else if (textDiv == "partAdress") {
            textDivVal = $("#txtReceiver_address_" + id + "").val();
        }

        if (isIE()) {
            if (window.clipboardData) {
                window.clipboardData.setData("Text", textDivVal);
                window.clipboardData.getData("Text")
                layer.msg("已复制，可粘贴");
            }
        } else {
            event.preventDefault();
            if (event.clipboardData) {
                event.clipboardData.setData("text/plain", textDivVal);
                event.clipboardData.getData("text")
                layer.msg("已复制，可粘贴");
            }
        }
    }

    common.OpenNewTab = function (url) {
        var a = document.createElement("a");
        a.setAttribute("href", url);
        a.setAttribute("target", "_blank");
        a.setAttribute("id", "camnpr" + (new Date()).getTime());
        document.body.appendChild(a);
        a.click();
        a.remove();
    }

    common.DownloadHintInstall = function () {
        layer.alert("下载打印组件后，需安装才能正常使用哦", { skin: 'wu-dailog' });
        //layer.msg('下载打印组件后，需安装才能正常使用哦');
    }

    function isIE() {  //判断是否ie
        var input = window.document.createElement("input");
        if (window.ActiveXObject === undefined) return null;
        if (!window.XMLHttpRequest) return 6;
        if (!window.document.querySelector) return 7;
        if (!window.document.addEventListener) return 8;
        if (!window.atob) return 9;
        if (!input.dataset) return 10;
        return 11;
    }

    //检测字符串中是否存在生僻字
    common.HasRareWords = function (str) {
        try {
            if (!str) return true;
            var tempReg = new RegExp(/\u200e|\u3002|\uFF1F|\uFF01|\uFF0C|\u3001|\uFF1B|\uFF1A|\u300C|\u300D|\u300E|\u300F|\u2018|\u2019|\u201C|\u201D|\uFF08|\uFF09|\u3014|\u3015|\u3010|\u3011|\u2014|\u2026|\u2013|\uFF0E|\u300A|\u300B|\u3008|\u3009/g);

            var rareWrodsIndex = [];
            for (var i = 0; i < str.length; i++) {
                if (str.charCodeAt(i) < 255 || str.charCodeAt(i) == 8236 || str.charCodeAt(i) == 8237)
                    continue;
                var c = str[i];

                if (c.match(tempReg) != null) continue;

                var reg = new RegExp(/[\u4e00-\u9fff]/g);
                if (c.match(reg) == null) {
                    //result = true;
                    rareWrodsIndex.push(i);
                    //break;
                }
            }
            return rareWrodsIndex;
        } catch (e) {
            //console.log(e);
        }
        return str;
    }

    //删除生僻字
    common.DeleteRareWords = function (str) {
        if (str == undefined || str == null || str == "")
            return str;
        var rareWords = common.HasRareWords(str);
        var newStr = '';
        for (var i = 0; i < str.length; i++) {
            var has = false;
            for (var j = 0; j < rareWords.length; j++) {
                if (i == rareWords[j]) {
                    has = true;
                    break;
                }
            }
            if (has == false)
                newStr += str[i];
        }
        return newStr;
    }

    /*1.用浏览器内部转换器实现html转码*/
    common.htmlEncode = function (html) {
        //1.首先动态创建一个容器标签元素，如DIV
        var temp = document.createElement("div");
        //2.然后将要转换的字符串设置为这个元素的innerText(ie支持)或者textContent(火狐，google支持)
        (temp.textContent != undefined) ? (temp.textContent = html) : (temp.innerText = html);
        //3.最后返回这个元素的innerHTML，即得到经过HTML编码转换的字符串了
        var output = temp.innerHTML;
        temp = null;
        return output;
    }

    /*2.用浏览器内部转换器实现html解码*/
    common.htmlDecode = function (text) {
        //1.首先动态创建一个容器标签元素，如DIV
        var temp = document.createElement("div");
        //2.然后将要转换的字符串设置为这个元素的innerHTML(ie，火狐，google都支持)
        temp.innerHTML = text;
        //3.最后返回这个元素的innerText(ie支持)或者textContent(火狐，google支持)，即得到经过HTML解码的字符串了。
        var output = temp.innerText || temp.textContent;
        temp = null;
        return output;
    }

    //转意符换成普通字符
    common.escape2Html = function (str) {
        var arrEntities = { 'lt': '<', 'gt': '>', 'nbsp': ' ', 'amp': '&', 'quot': '"' };
        return str.replace(/&(lt|gt|nbsp|amp|quot);/ig, function (all, t) { return arrEntities[t]; });
    }

    //普通字符转换成转意符
    common.html2Escape = function (sHtml) {
        return sHtml.replace(/[<>&"]/g, function (c) { return { '<': '&lt;', '>': '&gt;', '&': '&amp;', '"': '&quot;' }[c]; });
    }

    //删除html标签
    common.DelHtmlTag = function (data) {
        return data.replace(/<[^>]*>|<\/[^>]*>/gm, "");
    }

    //删除from提交危险字符
    common.DelDangerChar = function (data) {
        if (!data) return '';
        return data.replace(/<|>/gm, "").replace(/&#/gm, '');
    }

    //订单id格式化
    common.OrderIdFormatter = function (orderIdJoin) {

        if (orderIdJoin == undefined || orderIdJoin == null) {
            return '';
        }
        var ids = orderIdJoin.trimEndDgj(',').split(',');
        //if (ids.length > 1) {
        //    return "<lable title='" + orderIdJoin.trimEndDgj(',') + "'>" + ids[0] + "...</label>";
        //}
        //return orderIdJoin;
        return "<lable title='" + orderIdJoin.trimEndDgj(',') + "'>" + ids.join('<br/>') + "</label>";
    }

    //MM to Px
    common.MmToPx = function (w_mm, h_mm) {

        var num = 25.4;
        var dpi = 96;

        var w_px = Math.floor((w_mm / num) * dpi);
        var h_px = Math.floor((h_mm / num) * dpi);

        return { width: w_px, height: h_px };
    }

    // 时间控件初始化
    // {days:30,startDate:"2019-01-01 00:00:00",endDate:"2019-01-01 00:00:00"}
    common.InitDateRangePicker = function ($container, options) {
        Date.prototype.format = function (fmt) {
            var o = {
                "M+": this.getMonth() + 1,                 //月份
                "d+": this.getDate(),                    //日
                "h+": this.getHours(),                   //小时
                "m+": this.getMinutes(),                 //分
                "s+": this.getSeconds(),                 //秒
                "q+": Math.floor((this.getMonth() + 3) / 3), //季度
                "S": this.getMilliseconds()             //毫秒
            };
            if (/(y+)/.test(fmt)) {
                fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
            }
            for (var k in o) {
                if (new RegExp("(" + k + ")").test(fmt)) {
                    fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
                }
            }
            return fmt;
        }

        function getPreMonth(date) {
            var arr = date.split('-');
            var year = arr[0]; //获取当前日期的年份
            var month = arr[1]; //获取当前日期的月份
            var day = arr[2]; //获取当前日期的日
            var days = new Date(year, month, 0);
            days = days.getDate(); //获取当前日期中月的天数
            var year2 = year;
            var month2 = parseInt(month) - 1;
            if (month2 == 0) {
                year2 = parseInt(year2) - 1;
                month2 = 12;
            }
            var day2 = day;
            var days2 = new Date(year2, month2, 0);
            days2 = days2.getDate();
            if (day2 > days2) {
                day2 = days2;
            }
            if (month2 < 10) {
                month2 = '0' + month2;
            }
            var t2 = year2 + '-' + month2 + '-' + day2;
            return t2;
        }

        var endTimes = new Date().format("yyyy-MM-dd") + " 23:59:59";  //当天时间（结束时间）
        var startTimes = getPreMonth(endTimes.substr(0, 10)) + " 00:00:00";
        if (options != undefined) {
            if (options.startDate == undefined || options.startDate == "" || options.endDate == undefined || options.endDate == "") {
                var days = parseInt(options.days) || 0;
                if (days > 0)
                    startTimes = moment().subtract(days, 'days').format('YYYY-MM-DD HH:mm:ss').substr(0, 10) + " 00:00:00";//上个月时间（开始时间，周期30天）
            }
            else {
                startTimes = options.startDate;
                endTimes = options.endDate;
            }
        }

        $container.daterangepicker({
            timePicker: true, //显示时间
            timePicker24Hour: true, //时间制
            timePickerSeconds: true, //时间显示到秒
            autoApply: false,
            linkedCalendars: false,
            startDate: startTimes,
            endDate: endTimes,
            // maxDate: moment(new Date()), //设置最大日期
            opens: "right",
            ranges: {
                // '今天': [moment(), moment()],
                // '昨天': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                '上周': [moment().subtract(6, 'days'), moment()],
                '前30天': [moment().subtract(29, 'days'), moment()],
                '本月': [moment().startOf('month'), moment().endOf('month')],
                '上月': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
            },
            showWeekNumbers: true,
            locale: {
                format: "YYYY-MM-DD HH:mm:ss", //设置显示格式
                separator: "~",
                applyLabel: '确定', //确定按钮文本
                cancelLabel: '取消', //取消按钮文本
                customRangeLabel: '自定义',
                daysOfWeek: ['日', '一', '二', '三', '四', '五', '六'],
                monthNames: ['一月', '二月', '三月', '四月', '五月', '六月',
                    '七月', '八月', '九月', '十月', '十一月', '十二月'
                ],
                firstDay: 1
            },
        }, function (start, end, label) {
            $container.attr("start-date", start.format('YYYY-MM-DD HH:mm:ss'));
            $container.attr("end-date", end.format('YYYY-MM-DD HH:mm:ss'));
        });
        $container.on("click", function () {
            $(".daterangepicker").addClass("daterangepicker dropdown-menu show-calendar openscenter");
        });

        // 设置默认时间
        var dateArr = $container.val().split("~"); commonModule.LoadCommonSetting
        var startTime = dateArr[0] || "";
        var endTime = dateArr[1] || "";
        $container.attr("start-date", startTime);
        $container.attr("end-date", endTime);
    }

    common.InitNewCalenderTime = function (elmt, options) {

        if ($(elmt).length == 0)
            return;

        var key = window.location.pathname;
        key = key == "/" ? "/NewOrder/AllOrder" : key;
        var defaultQueryDateVal = '';
        var defaultQueryDateVals = [];
        commonModule.LoadCommonSetting("/ErpWeb/DefaultDiffDays", false, function (res) {
            if (res.Success && res.Data) {
                defaultQueryDateVals = JSON.parse(res.Data);
                commonModule.DefaultDiffDays = defaultQueryDateVals;
                if (defaultQueryDateVals && defaultQueryDateVals.length > 0) {
                    for (var i = 0; i < defaultQueryDateVals.length; i++) {
                        if (defaultQueryDateVals[i].Key == key) {
                            defaultQueryDateVal = defaultQueryDateVals[i];
                            break;
                        }
                    }
                }
            }
        });

        if (defaultQueryDateVal != '') {
            options.days = common.IsNumber(defaultQueryDateVal.DiffDays) ? parseInt(defaultQueryDateVal.DiffDays) + 1 : 7;
        }

        if (!options.startDate && options.endDate) {
            var endTime = new Date(options.endDate);
            var diff = endTime.getTime() + 1000 - options.days * (1000 * 60 * 60 * 24);
            var startTime = new Date(endTime.setTime(diff));
            options.startDate = startTime.Format("yyyy-MM-dd");
        }

        var option = {
            ele: elmt,
            defaultDayOption: options,
            width: options.width ? options.width : 170,
            height: options.height ? options.height : 35,
            maxDays: options.maxDays ? options.maxDays : 0,
            maxDaysTitle: options.maxDaysTitle ? options.maxDaysTitle : '',
            sureCallBack: function (obj) {
                if (typeof options.sureCallBack == "function") { //渲染后的回调函数
                    options.sureCallBack(obj);
                }

            }
        };
        //时期组件
        var timePicker = new wu_TimePicker();
        timePicker.initData(option);
    }

    // 时间控件初始化
    // {days:30,startDate:"2019-01-01 00:00:00",endDate:"2019-01-01 00:00:00"}
    common.InitCalenderTime = function ($container, options) {

        var setStartTime = function () {
            options.endDate = commonModule.ServerNowDate.replace(' ', 'T');
            options.days = common.IsNumber(options.days) ? parseInt(options.days) : 0;
            if (options.endDate && options.days > 0) {
                var endTime = new Date(options.endDate);
                var diff = endTime.getTime() - options.days * (1000 * 60 * 60 * 24);
                var startTime = new Date(endTime.setTime(diff));
                options.startDate = startTime.Format("yyyy-MM-dd");
            }
        }
        options = options || {};

        // 没有指定起止时间时，使用服务器当前时间
        if (!options.endDate && !options.startDate && commonModule.ServerNowDate) {
            setStartTime();
        }

        if (options.days !== 0)
            options.days = options.days || "";
        options.startDate = options.startDate || "";
        options.endDate = options.endDate || "";

        // shopId大于0表示店铺授权登录的情况，否则不读取配置，使用默认时间配置信息
        var shopId = 0;
        if (commonModule.CurrShop)
            shopId = commonModule.CurrShop.Id || 0;

        if (shopId > 0) {
            var key = window.location.pathname;
            key = key == "/" ? "/Order/Index" : key;
            var defaultQueryDateVal;
            var defaultQueryDateVals = [];
            commonModule.LoadCommonSetting("DefaultQueryDateVal", false, function (res) {
                if (res.Success && res.Data) {
                    defaultQueryDateVals = JSON.parse(res.Data);
                    common.DefaultQueryDateVal = defaultQueryDateVals;
                    if (defaultQueryDateVals && defaultQueryDateVals.length > 0) {
                        for (var i = 0; i < defaultQueryDateVals.length; i++) {
                            if (defaultQueryDateVals[i].Key == key)
                                defaultQueryDateVal = defaultQueryDateVals[i];
                        }
                    }
                }
            });

            if (defaultQueryDateVal) {
                options.days = common.IsNumber(defaultQueryDateVal.DiffDays) ? parseInt(defaultQueryDateVal.DiffDays) : 0;
                if (options.endDate && options.days > 0) {
                    setStartTime();
                }
            }
        }

        calenderTimeModule.InitCalenderTime($container, options);
    }

    //判断ajax返回是否有错误
    common.IsError = function (rsp) {
        if (rsp != undefined && rsp.Success != undefined && rsp.Success == false) {
            var isShowError = true;
            if (rsp.ErrorCode && rsp.ErrorCode.indexOf("UPGRADE") != -1) {
                common.ProcessVersionErrorCode(rsp.ErrorCode, rsp.Data.AvailableCount);
            } else if (rsp.ErrorCode && rsp.ErrorCode.indexOf("ORDER_COUNT") != -1) {
                common.ProcessVersionOrderCountErrorCode(rsp.ErrorCode, rsp.Data.OrderBalanceCount);
            } else if (rsp.ErrorCode && rsp.ErrorCode.indexOf("AuthVersion") != -1) {
                common.ProcessVersionStockErrorCode(rsp.Message);
            }
            else {
                if (rsp.Data != undefined && rsp.Data[0].ErrMsg != undefined && rsp.Data[0].ErrMsg != "") {
                    layer.msg(rsp.Data[0].ErrMsg, { icon: 2 });
                }
                else {
                    layer.msg(rsp.Message, { icon: 2 });
                }
            }
            return true;
        }
        return false;
    }

    //打印模板检查结果处理
    common.CheckPrintTemplateIsError = function (rsp) {
        if (rsp != undefined && rsp.Success != undefined && rsp.Success == false) {

            if (rsp.Data != undefined && rsp.Data[0].ErrMsg != undefined && rsp.Data[0].ErrMsg != "") {
                layer.msg(rsp.Data[0].ErrMsg, { icon: 2 });
            }
            else {
                layer.msg(rsp.Message, { icon: 2 });
            }
            return true;
        }
        return false;
    }

    common.ProcessVersionErrorCode = function (error_code, available_count) {
        var tipMsg = "";
        var versionInfo = common.UserVersionInfo;
        if (!available_count && commonModule.AvailableCountInfo)
            available_count = commonModule.AvailableCountInfo.AvailableCount;
        if (error_code == "SELLER") {
            //根据用户当前版本信息判断使用哪个提示
            if (available_count <= 0 || !versionInfo.IsFactory)
                error_code = "NEED_UPGRADE_TO_FACTORY_VERSION";
            else
                error_code = "NEED_UPGRADE_SELLER_COUNT";
        } else if (error_code == "FACTORY") {
            if (versionInfo && versionInfo.IsGuest || available_count <= 0)
                error_code = "NEED_UPGRADE_VERSION";
            else
                error_code = "NEED_UPGRADE_FACTORY_COUNT";
        } else if (error_code == "SHOP") {
            error_code = "NEED_UPGRADE_SHOP_COUNT";
        }
        if (available_count <= 0)
            error_code = "NEED_UPGRADE_VERSION";//可用数量为0，说明没有功能权限
        switch (error_code) {
            //店铺只有一种提示
            case "NEED_UPGRADE_SHOP_COUNT":
                tipMsg = "您当前版本的绑定店铺数量已达上限（X）个，请联系客服升级套餐后继续使用！";
                break;
            //版本限制功能
            case "NEED_UPGRADE_VERSION":
                tipMsg = "您当前版本不包含该功能，请联系客服升级版本后使用！";
                break;
            //绑定厂家两种情况：没有权限，或数量不够
            case "NEED_UPGRADE_FACTORY_COUNT":
                tipMsg = "您当前版本的【绑定厂家】数量已达上限（X）个，请联系客服升级套餐后继续使用！";
                break;
            //绑定商家两种情况：没有权限（当前用户版本不是厂家版本），或数量不够
            case "NEED_UPGRADE_TO_FACTORY_VERSION":
                tipMsg = "您当前版本没有【绑定商家】的功能，请联系客服升级厂家版本后继续使用！";
                break;
            case "NEED_UPGRADE_SELLER_COUNT":
                tipMsg = "您当前版本的【绑定商家】数量已达上限（X）个，请联系客服升级套餐后继续使用！";
                break;
            default:
                isShowError = false;
                break;
        }
        if (tipMsg != "") {
            //替换占位符
            if (available_count > 0)
                tipMsg = tipMsg.replace("（X）个", "（" + available_count + "）个")
            else
                tipMsg = tipMsg.replace("（X）个", "")
            //添加联系客服
            tipMsg += "<a href='#' style='color:blue;' onclick='commonModule.contactOpenOrClose(true)'>联系客服去升级</a>";
            //弹窗提示
            layer.alert(tipMsg, { skin: 'wu-dailog' });
        }
    }

    //单量不足提示
    common.ProcessVersionOrderCountErrorCode = function (error_code, order_balance_count) {
        var tipMsg = "";
        if (order_balance_count == 0) {
            tipMsg += "您当前的可用打单量为0，请尽快联系客服充值打单量继续发货操作， 联系客服去充值";
        } else {
            tipMsg += "您当前的可用打单量为" + order_balance_count + "，可用单量不足，请尽快联系客服充值打单量继续发货操作， 联系客服去充值";
        }
        //添加联系客服
        tipMsg += "<a href='#' style='color:blue;' onclick='commonModule.contactOpenOrClose(true)'>联系客服去升级</a>";
        //弹窗提示
        layer.alert(tipMsg, { skin: 'wu-dailog' });
    }

    //库存权限
    common.ProcessVersionStockErrorCode = function (message) {
        var tipMsg = message;
        //添加联系客服
        tipMsg += "<a href='#' style='color:blue;' onclick='commonModule.contactOpenOrClose(true)'>联系客服去升级</a>";
        //弹窗提示
        layer.alert(tipMsg, { skin: 'wu-dailog' });
    }

    //判断ajax返回是否有错误
    common.IsAlert = function (rsp) {
        if (rsp.Success == false) {
            layer.alert(rsp.Message, { icon: 2, skin: 'wu-dailog' });
            return true;
        }
        return false;
    }

    common.WaitDevMsg = function (loadingMsg) {
        layer.confirm('开发小哥哥正在努力开发中，敬请期待~', { skin: 'wu-dailog' });
        return;
    }

    common.LoadingMsg = function (loadingMsg, maxWidth) {
        var msg = '<div style="text-align:center;">' + loadingMsg + "</div>";
        if (!maxWidth || maxWidth <= 120)
            maxWidth = 120;
        return layer.msg('<div class="layui-layer-loading"><div id="" class="layui-layer-loading1" style="display:inline-block;"></div>' + msg + '</div>', { shade: 0.01, time: 999999, maxWidth: maxWidth, zIndex: 1000000000 });
    }
    common.isExpired = false;
    common.isExpiredShowed = false;
    //是否迁移锁
    common.isFxMigrateLock = false;
    //上次显示弹窗时间
    common.preShowFxMigrateLockTipTime = new Date(Date.parse("2023/05/01"));
    common.isOpenedFxMigrateLockTip = false;

    //公共的Ajax方法
    common.Ajax = function (options) {
        var loding = null;

        if (common.isFxMigrateLock != undefined && common.isFxMigrateLock == true) {
            //弹窗
            common.checkIsFxMigrateLock();
            return;
        }

        var successHandler = null;
        if (typeof options.success == "function") {
            successHandler = options.success;
        }
        if (options.data && !options.data.IsCustomerOrder && typeof options.data != "string") {
            options.data.IsCustomerOrder = common.isCustomerOrder();
        }
        else {
            options.data = options.data || {};
            options.data.IsCustomerOrder = common.isCustomerOrder();
        }
        if (options.data && !options.data.FxPageType && typeof options.data != "string") {
            options.data.FxPageType = common.FxPageType();
        }
        else {
            options.data = options.data || {};
            options.data.FxPageType = common.FxPageType();
        }
        if (common.checkIsExpired())
            return;
        //过滤尖括号等危险字符
        if (options.data.isFilterDangerChar == undefined || options.data.isFilterDangerChar == true) {
            var safedata = common.DelDangerChar(JSON.stringify(options.data));//.replace(new RegExp(/<[^>]*>|<\/[^>]*>/gm, 'g'), '');
            options.data = JSON.parse(safedata);
        }

        var complete = options.complete;
        //if (options.complete)
        //    options = null;
        options.url = common.rewriteUrl(options.url);
        options.url = common.addTraceIdToAjaxUrl(options.url);
        options.url = common.dbnameToAjaxUrl(options.url);
        options.url = common.dataFlagToAjaxUrl(options.url);
        var setContentType = options.contentType ? options.contentType : 'application/x-www-form-urlencoded';
        var defaultOpts = {
            async: true,
            cache: false,
            type: 'POST',
            contentType: setContentType,
            datatype: 'json',
            beforeSend: function (XHR) {
                if (commonModule.CloudPlatformType == "Pinduoduo") {
                    //尝试设置header:const pati = await window.PDD_OPEN_getPati(),X-PDD-Pati,X-PDD-PageCode
                    var pati = commonModule.Pati;
                    if (!pati || pati == "") {
                        if (commonModule.CurrShop && commonModule.CurrShop != "" && commonModule.CurrShop.id) {
                            var cookiePati = $.cookie(commonModule.CurrShop.Id + "-xpati");
                            pati = cookiePati;
                            if (!commonModule.Pati)
                                commonModule.Pati = pati;
                        }
                    }
                    XHR.setRequestHeader("X-PDD-Pati", pati);
                    XHR.setRequestHeader("X-PDD-PageCode", commonModule.PageCode);
                }
                if (common.checkIsExpired())
                    return;
                if (common.checkIsFxMigrateLock())
                    return;
                if (common.checkIsMigrating())
                    return;
                if (common.VersionTip && common.VersionTip != '' && common.VersionTip.TargetVersion != null) {
                    common.showUpgradeWindow(common.VersionTip, false);
                    return;
                }
                var msg = "";
                if (!options.loading && !options.loadingMessage)
                    return;
                if (options.loadingMessage && options.loadingMessage.trim() !== '')
                    loding = common.LoadingMsg(options.loadingMessage);
                else
                    loding = layer.load(2); //, { shade: [0.2, '#fff'] }
            },
            complete: function (XHR, TS) {
                if (loding != null) {
                    layer.close(loding);
                    loding = null;
                }

                var isLogout = false;
                var json = XHR.responseJSON;
                var text = XHR.responseText;
                if (json) {
                    if (json.ErrorCode == 401)
                        isLogout = true;
                    else if (json.ErrorCode == "ShopExpired")
                        common.isExpired = true;
                    else if (json.ErrorCode == "ShopMigrating")
                        common.isMigrating = true;
                }
                else if (text) {
                    try {
                        json = JSON.parse(text);
                        if (json && json.ErrorCode && json.ErrorCode == 401)
                            isLogout = true;
                    } catch (e) {

                    }
                }
                if (common.checkIsExpired())
                    return;
                if (common.checkIsFxMigrateLock())
                    return;
                if (common.checkIsMigrating())
                    return;
                if (complete && !isLogout) {
                    complete(XHR, TS);
                }
            },
            error: function (XHR, errorMsg, error) {
                if(isTKPrint){
                    return;
                }
                layer.msg(errorMsg, { icon: 2 });
            },
            success: function (data) {

            }
        };
        common.isMigrating = false;
        if (options.beforeSend) {
            var tempBeforeSend = options.beforeSend;
            options.beforeSend = function (temp) {
                defaultOpts.beforeSend(temp);
                tempBeforeSend(temp);
            }
        }
        var opts = $.extend({}, defaultOpts, options);
        opts.success = function (rsp) {
            if (!rsp.Success && rsp.ErrorCode === 10000 && rsp.LimitType === 'ImageCode') {
                setTimeout(function () {
                    //$('#waterproof_wall_verify_code').off('click');
                    //$("#waterproof_wall_verify_code").click();
                    aliyunCaptchaContent.show();
                    $("#aliyunCaptcha-option").hide();
                }, 1000);
            }
            if (!rsp.Success && rsp.ErrorCode === 10000 && rsp.LimitType === 'MessageCode') {
                systemRequestRateLimiting();
            }
            if (loding != null) {
                layer.close(loding);
                loding = null;
            }
            if (rsp && (rsp.ErrorCode == "401" || rsp.ErrorCode == "ShopExpired" || rsp.ErrorCode == "auth_expires" || rsp.ErrorCode == "NEED_UPGRADE_VERSION")) {
                if (rsp.ErrorCode == "401") {
                    layer.open({
                        title: "提示",
                        content: "<h1>登陆过期，请重新登陆</h1>",
                        btn: ['重新登陆', '取消'],
                        yes: function () {
                            window.location.href = common.CurrentPlatformAuthEntry ? common.CurrentPlatformAuthEntry : "/";
                        },
                        btn2: function () {
                            layer.closeAll();
                        },
                    });
                }
                else if (rsp.ErrorCode == "ShopExpired") {
                    common.isExpired = true;
                    common.checkIsExpired();
                }
                else if (rsp.ErrorCode == "auth_expires") {
                    //layer.msg(json.Message);
                    successHandler(rsp);
                }
                else if (rsp.ErrorCode == "NEED_UPGRADE_VERSION") {
                    //检查版本控制
                    common.showUpgradeWindow(rsp.Data, true);
                    layer.closeAll();
                    return;
                    //successHandler(rsp);
                }
            }
            if (rsp && (rsp.ErrorCode == "ShopMigrating")) {
                common.isMigrating = true;
                common.checkIsMigrating();
            }
            else if (rsp && (rsp.ErrorCode == "FX_MIGRATELOCK" || rsp.ErrorCode == "FX_MIGRATELOCK_SYNC")) {
                common.isFxMigrateLock = true;
                common.checkIsFxMigrateLock(rsp.ErrorCode);
            }
            else if (rsp && rsp.ErrorCode == "Tool_0000") {
                layer.alert(rsp.Message, { skin: 'wu-dailog' }, function () {
                    window.location.href = "/Tools/Login";
                });
            }
            else
                successHandler(rsp);
        }

        return $.ajax(opts);
    };
    common.systemRequestInputBlur = function (type) {
        var mobileValue = $("#system_request_mobile").val();
        var smsCodeValue = $("#system_request_smsCode").val();
        var regexVerifyCode = /^\d+$/; // 仅允许数字
        if (type === 'mobile') {
            $("#system_request_mobile_msg_code").addClass("systemRequestMobileMessageCode");
            if (mobileValue) {
                if (!commonModule.checkMoveAndTel(mobileValue, 'move')) {
                    $("#system_request_mobile").css('borderColor', '#EA572E').closest('.systemRequestRateInput').find('.input-warnTitle').show().text('手机号码有误，由数字11位组成，请重新输入');
                } else {
                    $("#system_request_mobile").css('borderColor', 'rgba(0, 0, 0, 0.14)').closest('.systemRequestRateInput').find('.input-warnTitle').hide().text('');
                    $("#system_request_mobile_msg_code").removeClass("systemRequestMobileMessageCode");
                }
            } else {
                $("#system_request_mobile").css('borderColor', '#EA572E').closest('.systemRequestRateInput').find('.input-warnTitle').show().text('手机号码不能为空');
            }
        }
        if (type === 'smsCode') {
            if (smsCodeValue) {
                if (!regexVerifyCode.test(smsCodeValue)) {
                    $("#system_request_smsCode").css('borderColor', '#EA572E').closest('.systemRequestRateInput').find('.input-warnTitle').show().text('验证码无效，请输入数字');
                } else {
                    $("#system_request_smsCode").css('borderColor', 'rgba(0, 0, 0, 0.14)').closest('.systemRequestRateInput').find('.input-warnTitle').hide().text('');
                }
            } else {
                $("#system_request_smsCode").css('borderColor', '#EA572E').closest('.systemRequestRateInput').find('.input-warnTitle').show().text('短信验证码不能为空');
            }
        }
    }
  
    // 获取短信验证码
    common.getSystemRequestRateMobileMsgCode = function () {
        var mobileValue = $("#system_request_mobile").val();
        // 验证码按钮倒计时
        var systemRequestRateMobileCount = 60;
        if (!commonModule.checkMoveAndTel(mobileValue, 'move')) {
            $("#system_request_mobile").css('borderColor', '#EA572E').closest('.systemRequestRateInput').find('.input-warnTitle').show().text('请输入正确的手机号码');
            return false;
        }
        if (systemRequestRateMobileCount < 60 && systemRequestRateMobileCount > 0) {
            return false;
        }
        var getMsgCountdownFn = function () {
            if (systemRequestRateMobileCount === 0) {
                $("#system_request_mobile_msg_code").removeClass("systemRequestMobileMessageCode");
                $("#system_request_mobile_msg_code").html("获取验证码");
                systemRequestRateMobileCount = 60;
                return;
            } else {
                $("#system_request_mobile_msg_code").addClass("systemRequestMobileMessageCode");
                $("#system_request_mobile_msg_code").html("重新发送(" + systemRequestRateMobileCount + ")");
                systemRequestRateMobileCount--;
            }
            setTimeout(function () { getMsgCountdownFn() }, 1000);
        }
        commonModule.Ajax({
            type: 'POST',
            url: '/FxAccount/PostMobileMessageCode',
            data: {
                phone: mobileValue,
                types: 1,

                token: commonModule.getToken()
            },
            success: function (data) {
                if (!data.Success) {
                    commonModule.w_alert({ type: 2, content: data.Message });
                    return;
                }
                getMsgCountdownFn();
            }
        });
    }
    
    // 系统接口请求限流交互开发
    function systemRequestRateLimiting() {
        layer.closeAll();
        var html = "";
        html += '<div class="systemRequestRateForm">';
        html += '<div class="systemRequestRateInput">';
        html += '<div style="display:flex;align-items:center;">';
        html += '<label class="n-font5" style="width:100px;text-align:right;"><i class="n-sColor">*</i>手机号码：</label>';
        html += '<input type="text" class="layui-input n-layui-input" placeholder="请输入手机号码" style="width: 240px;" id="system_request_mobile" onblur="commonModule.systemRequestInputBlur(\'mobile\')" />';
        html += '</div>';
        html += '<div class="input-warnTitle" style="margin-left:100px;padding-bottom:0;"></div>';
        html += '</div>';
        html += '<div class="systemRequestRateInput" style="margin-top: 12px;">';
        html += '<div style="display:flex;align-items:center;">';
        html += '<label class="n-font5" style="width:100px;text-align:right;"><i class="n-sColor">*</i>验证码：</label>';
        html += '<div style="display:flex;align-items:center;position:relative;">';
        html += '<input type="text" maxlength="6" class="layui-input n-layui-input" placeholder="请输入短信验证码" style="width: 240px;padding-right:80px;" id="system_request_smsCode" onblur="commonModule.systemRequestInputBlur(\'smsCode\')" />';
        html += '<span class="n-dColor systemRequestMobileMessageCode" id="system_request_mobile_msg_code" onclick="commonModule.getSystemRequestRateMobileMsgCode()" style="position:absolute;right: 12px;cursor:pointer;">获取验证码</span>';
        html += '</div>';
        html += '</div>';
        html += '<div class="input-warnTitle" style="margin-left:100px;padding-bottom:0;"></div>';
        html += '</div>';
        html += '</div>';
        layer.open({
            type: 1,
            title: '检测到异常请求，请重新完善登录信息',
            content: html,
            area: '420px', //宽高
            closeBtn: 0,
            move: false,
            skin: 'n-skin',
            offset: '120px',
            success: function () {
                $("#system_request_mobile").val('');
                $("#system_request_smsCode").val('');
                $('.systemRequestRateForm .systemRequestRateInput').find('.n-layui-input').css('borderColor', 'rgba(0, 0, 0, 0.14)');
                $('.systemRequestRateForm .systemRequestRateInput').find('.input-warnTitle').hide().text('');
            },
            btn: ['确定'],
            btn1: function (index) {
                var mobileValue = $("#system_request_mobile").val();
                var smsCodeValue = $("#system_request_smsCode").val();
                var regexVerifyCode = /^\d+$/; // 仅允许数字
                if (!commonModule.checkMoveAndTel(mobileValue, 'move')) {
                    $("#system_request_mobile").css('borderColor', '#EA572E').closest('.systemRequestRateInput').find('.input-warnTitle').show().text('请输入正确的手机号码');
                    return false;
                }
                if (!smsCodeValue) {
                    $("#system_request_smsCode").css('borderColor', '#EA572E').closest('.systemRequestRateInput').find('.input-warnTitle').show().text('短信验证码不能为空');
                    return false;
                }
                if (!regexVerifyCode.test(smsCodeValue)) {
                    $("#system_request_smsCode").css('borderColor', '#EA572E').closest('.systemRequestRateInput').find('.input-warnTitle').show().text('验证码无效，请输入数字');
                    return false;
                }
                commonModule.Ajax({
                    url: '/System/CheckMsgCode',
                    data: {
                        mobile: $("#system_request_mobile").val(),
                        smsCode: $("#system_request_smsCode").val(),
                    },
                    type: 'POST',
                    success: function (result) {
                        if (result.Success) {
                            commonModule.w_alert({ type: 4, content: '验证成功' });
                            layer.close(index);
                            window.location.href = window.location.href;
                        } else {
                            commonModule.w_alert({ type: 2, content: result.Message });
                        }
                    }
                });
            },
        });
    }

    common.checkIsExpired = function () {
        if (common.isExpired) {
            //layer.closeAll();
            var tips = $("#expired-h-tips");
            if (tips.length > 0)
                return true;
            //var link = common.getPayLink();
            layer.open({
                title: "服务到期",
                offset: "100px",
                area: ["500px", "200px"],
                shadeClose: false,
                icon: 5,
                content: "<h1 id='expired-h-tips' style='margin-top:-10px;'>非常抱歉，您所使用的店管家批量打印发货服务已到期。</br>为了不影响您的使用，请尽快续费</h1>",
                btn: ['现在续费'],
                yes: function () {
                    var payLink = $("#header-pay-link");
                    var link = payLink.get(0);
                    if (link && link.href && link.href.indexOf("javascript") == -1) {
                        window.open(link.href);
                    }
                    else if (payLink.length > 0) {
                        $("#header-pay-link")[0].click();
                        $("#header-pay-link").click();
                    } else {
                        layer.alert("请前往平台后台进行续费", { skin: 'wu-dailog' });
                    }
                },
                cancel: function () {
                    return false;
                },
            });
            return true;
        }
    }
    common.checkIsMigrating = function () {
        var tip = $("#migrating-tip");
        if (tip && tip.length > 0)
            return;
        if (common.isMigrating) {
            layer.closeAll();
            layer.open({
                title: "提示",
                offset: "100px",
                area: ["500px", "180px"],
                shadeClose: false,
                icon: 1,
                content: "<h1 id='migrating-tip' style='margin-top:-8px;'>为了提升您的订单发货效率，我们正在将您的店铺数据迁往新的服务器，预计会持续10分钟，在此期间不能打单发货操作，请10分钟之后再刷新页面。</h1>",
                btn: [],
                cancel: function () {
                    //layer.msg("数据正在迁移中，请10分钟后再操作...");
                    return false;
                }
            });
            try {
                $(".progress-wrap").hide();
            } catch (e) {

            }
        }
        return common.isMigrating;
    }
    common.checkIsFxMigrateLock = function (errorCode) {
        if (common.isFxMigrateLock) {

            //时间对比
            var $startTime = common.preShowFxMigrateLockTipTime.getTime();
            var $nowTime = new Date().getTime();
            var num = parseInt(($nowTime - $startTime) / 1000);

            if (common.isOpenedFxMigrateLockTip == false && errorCode != "FX_MIGRATELOCK_SYNC") {
                common.isOpenedFxMigrateLockTip = true;
                common.preShowFxMigrateLockTipTime = new Date();
                var html = '<div class="checkMigrateDailog" style="width:600px">';
                html += '<div class="checkMigrate-main">';

                html += '<div class="checkMigrate-main-text"  style="padding-top:5px;">';
                html += '<span style="font-size:20px;font-weight:700;text-align:center;margin:10px 0 20px 0;color:#000;">迁移通知</span>';
                html += '<span style="font-size:16px;color:#666">因平台数据存储要求，正在将您系统的抖音订单迁移至抖店云平台，为保证数据迁移完整，迁移期间暂停该功能操作，预计迁移还需2分钟。迁移完成后，请您前往抖店云平台操作，谢谢您的理解。</span>';
                html += '</div>';
                html += '<div class="checkMigrate-imgWrap" style="margin: 0 auto;position:relative;margin-bottom: 30px;">';
                html += '<img class="checkMigrate-main-img" src="/Content/images/noviceIntroPic/chooseYun-img.png">';
                html += '<span class="checkMigrate-warn-icon"></span>';
                html += '</div>';

                html += '</div>'
                html += '</div>'
                var checkDyYunWarnDailg = layer.open({
                    type: 1,
                    title: false, //不显示标题
                    content: html,
                    area: 600, //宽高
                    skin: 'adialog-Shops-skin',
                    btn: false,
                    cancel: function () {
                        common.isOpenedFxMigrateLockTip = false;
                    }
                });
            }

            //超过N秒，重置
            if (num > 10) {
                common.isFxMigrateLock = false;
            }
            try {
                if (orderTableBuilder) {
                    orderTableBuilder.ResetIsQuerying();
                }
            }
            catch (e) { }
        }
        return common.isFxMigrateLock;
    }
    common.ajax = common.Ajax;
    common.post = function (url, data, callback) {
        /* var shouldTimeout = Math.random() > 0.8; // 20% 概率模拟 504 超时
        if (shouldTimeout) {
            console.log("模拟 504 超时...");
            
            // 先调用回调函数，通知外部请求失败
            callback({
                Success: false,
                Message: "模拟 504 超时",
                Data: { Orders: [] }
            }, "error");

            // 返回一个已 reject 的 Promise，避免阻塞 $.when
            return $.Deferred().reject({
                status: 504,
                statusText: "Gateway Timeout",
                responseJSON: {
                    Success: false,
                    Message: "模拟 504 超时",
                    Data: { Orders: [] }
                }
            }).promise();
        } else {
            return common.ajax({
                url: url,
                data: data,
                success: callback
            });
        } */
        return common.ajax({
            url: url,
            data: data,
            success: callback
        });

    }
    common.getJSON = function (url, callback) {
        return common.ajax({ url: url, success: callback });
    };
    common.get = common.getJSON;
    common.getJSONAsyncFalse = function (url, callback) {
        return common.ajax({ url: url, success: callback, async: false });
    };
    common.getPayLink = function () {
        if (common.PlatformType == "Alibaba") {
            return "http://pc.1688.com/product/detail.htm?productCode=Tz%2BIZt9qCGKsMpNFCCCY9%2BmqRnw6h1ZBD3N%2Fli2CCyg%3D&productType=GROUP&tracelog=app_map_dgj";
        } else if (common.PlatformType == "Taobao") {
            return "https://fuwu.taobao.com/ser/detail.htm?spm=a1z13.8114210.1234-fwlb.4.b7XqWn&service_code=FW_GOODS-1000059019&tracelog=search&from_key=%E5%BA%97%E7%AE%A1%E5%AE%B6";
        } else if (common.PlatformType == "YouZan") {
            return "https://yingyong.youzan.com/cloud-app-detail/43116";
        } else if (common.PlatformType == "JingDong") {
            return "http://fw.jd.com/975802.html?itemCode=FW_GOODS-975802-1";
        } else if (common.PlatformType == "Suning") {
            return "http://fuwu.suning.com/detail/10003725.html";
        } else if (common.PlatformType == "WeiMeng") {
            return "https://fuwu.weimob.com/serviceDetail/21201";
        } else if (common.PlatformType == "XiaoDian") {
            return "https://wechat.xiaodian.com/user/oauth/authorize.html?response_type=code&app_key=100735&redirect_uri=http://auth.dgjapp.com/auth/xiaodian/&state=YOUR_CUSTOM_CODE";
        } else if (common.PlatformType == "WeiDian") {
            return "https://h5.weidian.com/m/signin/index.html?oauth=0&redirect=https%3A%2F%2Foauth.open.weidian.com%2Foauth2%2Fauthorize%3Fappkey%3D690712%26isH5%3Dfalse%26redirect_uri%3Dhttp%3A%2F%2Fauth.dgjapp.com%2Fauth%2Fweidian%2F%26response_type%3Dcode%26sign%3Db78f05b0c18158477bf09324cf14e170%26state%3DYOUR_CUSTOM_CODE";
        } else if (common.PlatformType == "ZhiDian" || common.PlatformType == "DouYinXiaoDian" || common.PlatformType == "TouTiaoXiaoDian" || common.PlatformType == "LuBan") {
            return "http://auth.dgjapp.com/auth/zhidian";
        } else if (common.PlatformType == "MoGuJie") {
            return "https://oauth.mogujie.com/authorize?response_type=code&app_key=101444&redirect_uri=http://auth.dgjapp.com/auth/mogujie/&state=YOUR_CUSTOM_CODE";
        } else {
            return window.location.href;
        }
    }

    common.GetRequestBatch = function () {
        if (commonModule.ServerNowStr) {
            var batch = commonModule.ServerNowStr + (commonModule.DateBatch || '');
            return batch;
        }
        else {
            var padLeft = function (s, l) {
                var str = "";
                var sl = s.toString().length;
                if (sl < l) {
                    var n = l - sl;
                    for (var i = 0; i < n; i++) {
                        str += "0";
                    }
                }
                return str + s;
            }
            var date = new Date();
            var y = date.getFullYear();
            var m = date.getMonth() + 1;
            var d = date.getDate();
            var h = date.getHours();
            var mm = date.getMinutes();
            var dateStr = y + '' + padLeft(m, 2) + '' + padLeft(d, 2) + '' + padLeft(h, 2) + '' + padLeft(mm, 2);
            return dateStr + commonModule.CurrShop.Id + commonModule.Random(1000, 9999);
        }
    }
    //批量异步发送请求
    //url:请求链接
    //datas:需要分批的请求参数
    //requestParaBuilderFunc:请求参数组装函数
    //callbackFunc:每个ajax请求回调 可用作请求进度监控，百分比需乘以batchSize
    //allDoneCallbackFunc:所有请求完成后的回调，参数为数组
    //batchSize:分批后单次请求的数量，默认10
    //loadingText： loadingText有值，则不显示进度条，否则显示进度条
    common.posts_by_datalength = function (url, datas, dataLength, requestParaBuilderFunc, callbackFunc, allDoneCallbackFunc, batchSize, loadingText) {
        if (commonModule.IsCrossBorderSite) {
            tk_doposts(url, datas, dataLength, requestParaBuilderFunc, callbackFunc, allDoneCallbackFunc, batchSize, loadingText);
        } else {
            doposts(url, datas, dataLength, requestParaBuilderFunc, callbackFunc, allDoneCallbackFunc, batchSize, loadingText);
        }
        
    }

    //批量异步发送请求
    //url:请求链接
    //datas:需要分批的请求参数
    //requestParaBuilderFunc:请求参数组装函数
    //callbackFunc:每个ajax请求回调 可用作请求进度监控，百分比需乘以batchSize
    //allDoneCallbackFunc:所有请求完成后的回调，参数为数组
    //batchSize:分批后单次请求的数量，默认10
    //loadingText： loadingText有值，则不显示进度条，否则显示进度条
    common.posts = function (url, datas, requestParaBuilderFunc, callbackFunc, allDoneCallbackFunc, batchSize, loadingText) {
        doposts(url, datas, datas.length, requestParaBuilderFunc, callbackFunc, allDoneCallbackFunc, batchSize, loadingText);
    }

    //批量异步发送请求
    //url:请求链接
    //datas:需要分批的请求参数
    //requestParaBuilderFunc:请求参数组装函数
    //callbackFunc:每个ajax请求回调 可用作请求进度监控，百分比需乘以batchSize
    //allDoneCallbackFunc:所有请求完成后的回调，参数为数组
    //batchSize:分批后单次请求的数量，默认10
    //loadingText： loadingText有值，则不显示进度条，否则显示进度条
    function doposts(url, datas, dataLegnth, requestParaBuilderFunc, callbackFunc, allDoneCallbackFunc, batchSize, loadingText) {
        
        var reqs = [];
        var rsps = [];
        if (!batchSize || batchSize <= 0)
            batchSize = 10;
        if (!datas)
            return;

        var datasClone = JSON.parse(JSON.stringify(datas));
        var pageCount = Math.ceil(datas.length / batchSize);
        var loading = null;
        if (loadingText) {
            if (loadingText != "not_show") {
                loding = common.LoadingMsg(loadingText);
            }
        } else {
            loading = layer.msg('\
                    <div style="width:300px;">\
                        <div class="layui-progress layui-progress-big">\
                            <div id="div_pro_bar" class="layui-progress-bar layui-bg-red" style="width: 0%;"><span class="layui-progress-text" id="div_bar_text"></span></div>\
                        </div>\
                        <p style="font-size:18px;font-weight:bold;"><span id="sp_curr_number">0</span>/<span>'+ dataLegnth + '<span></p>\
                    </div>', { shade: 0.01, time: 999999 }); //datas.length
        }
        var batchFlag = common.GetRequestBatch();//(new Date()).getTime() + common.Random(1000, 10000); //批次标识
        for (var i = 0; i < pageCount; i++) {
            var perRequestDatas = datas.splice(0, batchSize);
            var postData = requestParaBuilderFunc(perRequestDatas);
            postData.RequestBatchNumber = (i + 1) + "/" + pageCount + "/" + batchFlag; //请求批次 i/pageCount;
            var req = common.post(url, postData, callbackFunc);
            reqs.push(req);
        }
        var evalArgs = "";
        for (var i = 0; i < reqs.length; i++) {
            evalArgs += ",reqs[" + i + "]";
        }
        evalArgs = evalArgs.trimStartDgj(',');
        var finish = eval('$.when(' + evalArgs + ')');
        finish.then(function () {
            var args = [];
            layer.close(loading);
            if (pageCount == 1) {
                args.push(arguments[0]);
            }
            else {
                for (var i = 0; i < arguments.length; i++) {
                    args.push(arguments[i][0]);
                }
            }
            allDoneCallbackFunc(args, datasClone);
        });
    }

    /**
     * 批量请求数据并动态限流（完成一个请求就补充一个，保持最大并发）
     * 
     * @param {string} url - 请求的 URL 地址
     * @param {Array} datas - 要发送的数据集合
     * @param {number} dataLegnth - 数据总长度，用于显示进度
     * @param {Function} requestParaBuilderFunc - 构建请求参数的函数
     * @param {Function} callbackFunc - 单个请求的回调函数（如 SingleCallBack）
     * @param {Function} allDoneCallbackFunc - 所有请求完成后的回调
     * @param {number} batchSize - 每批次的数据条数（默认 10）
     * @param {string} loadingText - 控制是否显示进度条
     */
    function tk_doposts(url, datas, dataLegnth, requestParaBuilderFunc, callbackFunc, allDoneCallbackFunc, batchSize, loadingText) {
        // 参数默认值
        batchSize = batchSize > 0 ? batchSize : 10;
        if (!datas || !datas.length) return;
    
        // 克隆原始数据
        var datasClone = JSON.parse(JSON.stringify(datas));
        var allPostData = [];
        var pageCount = Math.ceil(datas.length / batchSize);
        var loading = null;
    
        // 初始化 Loading
        if (loadingText && loadingText !== "not_show") {
            loading = common.LoadingMsg(loadingText);
        } else if (!loadingText) {
            loading = layer.msg(
                '<div style="width:300px;">\
                    <div class="layui-progress layui-progress-big">\
                        <div id="div_pro_bar" class="layui-progress-bar layui-bg-red" style="width: 0%;">\
                            <span class="layui-progress-text" id="div_bar_text">0%</span>\
                        </div>\
                    </div>\
                    <p style="font-size:18px;font-weight:bold;">\
                        <span id="sp_curr_number">0</span>/<span>' + dataLegnth + '<span>\
                    </p>\
                </div>',
                { shade: 0.01, time: 999999 }
            );
        }
    
        // 构建所有请求参数
        var batchFlag = common.GetRequestBatch();
        for (var i = 0; i < pageCount; i++) {
            var startIndex = i * batchSize;
            var endIndex = (i + 1) * batchSize;
            var postData = requestParaBuilderFunc(datas.slice(startIndex, endIndex));
            postData.RequestBatchNumber = (i + 1) + "/" + pageCount + "/" + batchFlag;
            allPostData.push(postData);
        }
        isTKPrint = true; // 设置为正在打印TK订单
    
        var finalResults = [];
        var MAX_CONCURRENT = 6; // 最大并发数
        var activeRequests = 0;  // 当前活跃的请求数
        var currentIndex = 0;    // 当前处理到的请求索引
        var totalRequests = allPostData.length; // 总请求数
        var completedRequests = 0; // 已完成的请求数（包括成功和失败）
    
        /**
         * 发起单个请求
         */
        function sendRequest(postData) {
            activeRequests++;
            
            common.post(url, postData, function() {
                try {
                    callbackFunc.apply(this, arguments);
                } catch (e) {
                    console.error("callbackFunc 执行出错：", e);
                }
            }).then(function(res) {
                finalResults.push(res);
            }).fail(function(err) {
                console.warn("请求失败：", err);
                // 为失败的请求创建一个统一的错误格式
                var errorResponse = {
                    Success: false,
                    Message: "获取平台面单失败，请重试！",
                    Data: {
                        Success: false,
                        Message: "获取平台面单失败，请重试！",
                        RequestBatchNumber: postData.RequestBatchNumber,
                        Orders: []
                    }
                };
                finalResults.push(errorResponse);
            }).always(function() {
                activeRequests--;
                completedRequests++;
                
                // 所有请求完成
                if (completedRequests === totalRequests) {
                    if (loading) layer.close(loading);
                    allDoneCallbackFunc(finalResults, datasClone);
                    isTKPrint = false;
                } else {
                    checkAndSendNext(); // 补充新请求
                }
            });
        }
    
        /**
         * 补充请求（保持最大并发数）
         */
        function checkAndSendNext() {
            while (currentIndex < totalRequests && activeRequests < MAX_CONCURRENT) {
                sendRequest(allPostData[currentIndex]);
                currentIndex++;
            }
        }
    
        // 启动初始批次
        checkAndSendNext();
    }
    
    /**
     * 间隔批量请求（不会等待请求完成）
     * 
     * @param {string} url - 请求的 URL 地址。
     * @param {Array} datas - 要发送的数据集合。
     * @param {number} dataLegnth - 数据总长度，用于显示进度。
     * @param {Function} requestParaBuilderFunc - 用于构建请求参数的函数，传入一批数据并返回相应的请求数据。
     * @param {Function} callbackFunc - 请求成功的回调函数，处理单个请求的返回数据。
     * @param {Function} allDoneCallbackFunc - 所有请求完成后的回调函数，处理所有请求的最终结果。
     * @param {number} batchSize - 每批次请求的最大数据条数。
     * @param {string} loadingText - loadingText有值，则不显示进度条，否则显示进度条。
     */
    function batchPostWithInterval(url, datas, dataLegnth, requestParaBuilderFunc, callbackFunc, allDoneCallbackFunc, batchSize, loadingText) {
    
        if (!batchSize || batchSize <= 0) batchSize = 10;
        if (!datas || !datas.length) return;

        // 克隆原始数据，用于最终回调中保留原始信息
        var datasClone = JSON.parse(JSON.stringify(datas));
        var allPostData = []; // 所有请求参数数据
        var pageCount = Math.ceil(datas.length / batchSize); // 总批次数量
        var loading = null;
    
        // 显示 loading 提示
        if (loadingText && loadingText !== "not_show") {
            loading = common.LoadingMsg(loadingText);
        } else if (!loadingText) {
            loading = layer.msg(
                '<div style="width:300px;">\
                    <div class="layui-progress layui-progress-big">\
                        <div id="div_pro_bar" class="layui-progress-bar layui-bg-red" style="width: 0%;"><span class="layui-progress-text" id="div_bar_text"></span></div>\
                    </div>\
                    <p style="font-size:18px;font-weight:bold;"><span id="sp_curr_number">0</span>/<span>' + dataLegnth + '<span></p>\
                </div>', { shade: 0.01, time: 999999 }
            );
        }
    
        // 批次唯一标识
        var batchFlag = common.GetRequestBatch();
    
        // 拆分数据为每批 MAX_CONCURRENT 个请求
        for (var i = 0; i < pageCount; i++) {
            var startIndex = i * batchSize;
            var endIndex = (i + 1) * batchSize;
            var perRequestDatas = datas.slice(startIndex, endIndex);
            var postData = requestParaBuilderFunc(perRequestDatas);
            postData.RequestBatchNumber = (i + 1) + "/" + pageCount + "/" + batchFlag;
            allPostData.push(postData);
        }
    
        var finalResults = [];
        var MAX_CONCURRENT = 10;
        var interval = 1000;
    
        var currentIndex = 0;
        var completedRequests = 0;
    
        // 每隔 1 秒执行一个批次请求（不等待结果）
        var intervalTimer = setInterval(function () {
            // 每次从 allPostData 取出一批（最多 MAX_CONCURRENT 个）
            var currentBatch = allPostData.slice(currentIndex, currentIndex + MAX_CONCURRENT);
    
            // 没有更多请求要发时，停止定时器
            if (!currentBatch.length) {
                clearInterval(intervalTimer);
                return;
            }
    
            // 发起当前批次请求
            for (var j = 0; j < currentBatch.length; j++) {
                (function (postData) {
                    common.post(url, postData, function () {
                        try {
                            callbackFunc.apply(this, arguments);
                        } catch (e) {
                            console.error("callbackFunc 执行出错：", e);
                        }
                    }).then(function (res) {
                        finalResults.push(res);
                    }).fail(function (err) {
                        console.warn("请求失败：", err,postData);
                        // 为失败的请求创建一个统一的错误格式
                        var errorResponse = {
                            Success: false,
                            Message: "请求失败，请重试！",
                            Data: {
                                Success: false,
                                Message: "请求失败，请重试！",
                                RequestBatchNumber: postData.RequestBatchNumber,
                                Orders: []
                            }
                        };
                        finalResults.push(errorResponse);
                    }).always(function () {
                        // 每个请求无论成功失败，完成时都记录
                        completedRequests++;
    
                        // 所有请求完成后触发回调
                        if (completedRequests === allPostData.length) {
                            if (loading) layer.close(loading);
                            allDoneCallbackFunc(finalResults, datasClone);
                        }
                    });
                })(currentBatch[j]);
            }
    
            // 更新当前已处理的索引
            currentIndex += MAX_CONCURRENT;
        }, interval);
    }
    
    

    common.Random = function (min, max) {
        return Math.floor(Math.random() * (max - min)) + min;
    }

    common.Ajaxs = function (defers, callback) {

        if (!defers || defers.length == 0) {
            layer.alter("没有ajax请求.");
            return;
        }

        var count = 0;
        var evalArgs = "";
        for (var i = 0; i < defers.length; i++) {
            if (defers[i]) {
                evalArgs += ",defers[" + i + "]";
                count++;
            }
        }
        evalArgs = evalArgs.trimStartDgj(',');
        var finish = eval('$.when(' + evalArgs + ')');
        finish.then(function () {
            //获取所有请求返回
            var result = true;
            if (count > 1) {
                for (var i in arguments) {
                    if (arguments[i] == undefined)
                        continue;
                    var r = arguments[i][0];
                    if (r.Success == false)
                        result = false;
                }
            }
            else {
                var r = arguments[0];
                if (r && r.Success == false)
                    result = false;
            }
            callback(result);
        });
    }

    common.AjaxFileUpload = function (options) {
        var loding = null;

        var successHandler = null;
        if (typeof options.success == "function") {
            successHandler = options.success;
        }
        options.url = common.rewriteUrl(options.url);
        options.url = common.addTraceIdToAjaxUrl(options.url);
        options.url = common.dbnameToAjaxUrl(options.url);
        if (options.data && !options.data.IsCustomerOrder)
            options.data.IsCustomerOrder = common.isCustomerOrder();
        var defaultOpts = {
            async: true,
            cache: false,
            type: 'POST',
            contentType: 'application/x-www-form-urlencoded',
            dataType: 'json',
            beforeSend: function (XHR) {
                var msg = "";
                if (!options.loading && !options.loadingMessage)
                    return;
                if (options.loadingMessage && options.loadingMessage.trim() !== '') {
                    msg = '<div style="text-align:center;">' + options.loadingMessage + "</div>";
                    loding = layer.msg('<div class="layui-layer-loading"><div id="" class="layui-layer-loading1" style="display:inline-block;"></div>' + msg + '</div>', { time: 999999 });
                }
                else
                    loding = layer.load(2);
            },
            complete: function (XHR, TS) {
                layer.close(loding);
            },
            error: function (XHR, errorMsg, error) {
                layer.msg(errorMsg, { icon: 2 });
            },
            success: function (data) { }
        };

        var opts = $.extend({}, defaultOpts, options);
        opts.success = function (rsp) {
            layer.close(loding);
            var temp = $.parseJSON(rsp.replace(/<.*?>/ig, ""));
            if (temp && temp.ErrorCode == "NEED_UPGRADE_VERSION") {
                //检查版本控制
                common.showUpgradeWindow(temp.Data, true);
            }
            successHandler(rsp);
        }
        $.ajaxFileUpload(opts);
    };

    //上传文件大小判断
    common.CheckUploadFileSize = function ($id, maxSize) {
        var maxsize = maxSize * 1024 * 1024;
        var errMsg = "上传的附件文件不能超过" + maxSize + "M！";
        var tipMsg = "您的浏览器暂不支持计算上传文件的大小，确保上传文件不要超过" + maxSize + "M，建议使用FireFox、Chrome浏览器。";
        var browserCfg = {};
        var ua = window.navigator.userAgent;
        if (ua.indexOf("MSIE") >= 1) {
            browserCfg.ie = true;
        } else if (ua.indexOf("Firefox") >= 1) {
            browserCfg.firefox = true;
        } else if (ua.indexOf("Chrome") >= 1) {
            browserCfg.chrome = true;
        }
        try {
            var obj_file = document.getElementById($id);
            if (obj_file.value == "") {
                layer.confirm("请先选择上传文件", { skin: 'wu-dailog' });
                return false;
            }
            var filesize = 0;
            if (browserCfg.firefox || browserCfg.chrome) {
                filesize = obj_file.files[0].size;
            } else if (browserCfg.ie) {
                layer.confirm(tipMsg, { skin: 'wu-dailog' });
                return false;
            } else {
                layer.confirm(tipMsg, { skin: 'wu-dailog' });
                return false;
            }
            if (filesize == -1) {
                layer.confirm(tipMsg, { skin: 'wu-dailog' });
                return;
            } else if (filesize > maxsize) {
                layer.confirm(errMsg, { skin: 'wu-dailog' });
                return false;
            } else {
                return true;
            }
        } catch (e) {
            //console.log(e);
        }
        return false;
    }

    //循环集合
    common.Foreach = function (list, callback) {
        if (Array.isArray(list) == false) {
            var i = 0;
            for (var p in list) {
                var break_continue = callback(i, list[p]);
                if (break_continue != undefined && (break_continue.toLowerCase() == 'break' || break_continue.toLowerCase() == 'break;')) {
                    break;
                }
                i++;
            }
        } else {
            for (var i = 0; i < list.length; i++) {
                var break_continue = callback(i, list[i]);
                if (break_continue != undefined && (break_continue.toLowerCase() == 'break' || break_continue.toLowerCase() == 'break;')) {
                    break;
                }
                if (break_continue != undefined && (break_continue.toLowerCase() == 'continue' || break_continue.toLowerCase() == 'continue;')) {
                    continue;
                }
            }
        }
    }



    //数组排序
    common.Sort = function (arr, key, desc) {
        var sortFun = function (a, b) {
            return !desc ? a[key] - b[key] : b[key] - a[key];
        };
        if (arr == null || arr.length < 2)
            return arr;
        return arr.sort(sortFun);
    }

    //数组排序 多个属性排序
    common.SortExt02 = function (arr, keys, desc, ignoreNull) {
        var sortFun = function (a, b) {
            var result = 0;
            for (var i = 0; i < keys.length; i++) {
                var k = keys[i];
                var x, y;
                if (ignoreNull) {
                    x = a == null ? 0 : (a[k] || 0);
                    y = b == null ? 0 : (b[k] || 0);
                }
                else {
                    x = a[k];
                    y = b[k];
                }
                if (x == y) {
                    continue;
                }
                else {

                    if (!desc) {
                        if (isNaN(x) || isNaN(y))
                            result = x.toString().localeCompare(y);
                        else
                            result = x - y;
                    }
                    else {
                        if (isNaN(x) || isNaN(y))
                            result = y.toString().localeCompare(x);
                        else
                            result = y - x;
                    }

                    break;
                }
            }
            return result;
        };

        if (arr == null || arr.length < 2)
            return arr;
        return arr.sort(sortFun);
    }

    //数组对象多级属性排序,key 示例 "Data.Attr"
    common.SortExt = function (arr, key, desc, ignoreNull) {
        var attrs = key.split('.');
        var sortFun = function (a, b) {
            for (var i = 0; i < attrs.length; i++) {
                if (ignoreNull) {
                    a = a == null ? 0 : (a[attrs[i]] || 0);
                    b = b == null ? 0 : (b[attrs[i]] || 0);
                }
                else {
                    a = a == null ? 0 : (a[attrs[i]] || 0);
                    b = b == null ? 0 : (b[attrs[i]] || 0);
                }
            }
            var result = 0;
            if (!desc) {
                if (isNaN(a) || isNaN(b))
                    result = a.toString().localeCompare(b);
                else
                    result = a - b;
            }
            else {
                if (isNaN(a) || isNaN(b))
                    result = b.toString().localeCompare(a);
                else
                    result = b - a;
            }
            return result;
        };
        if (arr == null || arr.length < 2)
            return arr;
        return arr.sort(sortFun);
    }

    //系统权限相关
    var _fxPermission = undefined;
    var _getPermissions = function (callback,getNew) {
        //debugger;
        var lastTime = sessionStorage.getItem('LastGetPermissionTime');
        console.log("LastGetPermissionTime：" + new Date(parseInt(lastTime)));

        if (!lastTime || new Date() > new Date(parseInt(lastTime)).setMinutes(new Date(parseInt(lastTime)).getMinutes() + 15)) {
            sessionStorage.setItem('LastGetPermissionTime', new Date().getTime());
            getNew = true;
            console.log("重设LastGetPermissionTime");
        }

        if(getNew == true)
            console.log("permissionGetNew");
        if (!_fxPermission || getNew == true) {
            var ses = sessionStorage.getItem('FxPermission');
            if (!!ses && getNew != true) {
                _fxPermission = JSON.parse(ses);
                callback(); // 在已有权限信息时执行回调函数
            } else {
                common.Ajax({
                    url: "/SubAccount/GetSysPermissionDict",
                    data: { useCache: !getNew },
                    success: function (rsp) {
                        if (rsp.Success) {
                            if (!!rsp.Data) {
                                sessionStorage.setItem('FxPermission', JSON.stringify(rsp.Data));
                                _fxPermission = rsp.Data;
                            }
                        } else {
                            console.error(rsp.Message);
                        }
                        callback(); // 在获取权限信息后执行回调函数
                    }
                });
            }
        }
        else {
            callback();
        }
    };

    
    Object.defineProperty(common, 'FxPermission', {
        value: function(callback) {
            if (_fxPermission === undefined) {
                _getPermissions(function () {
                    //console.log('per');
                    callback(_fxPermission);
                });
            } else {
                callback(_fxPermission);
            }
        }
    });
    

    //加载系统快递公司
    common.LoadExpressCompany = function (callback) {
        common.Ajax({
            url: '/Common/LoadExpressCompany',
            success: function (response) {
                callback(response);
            }
        });
    }

    //加载系统打印机绑定数据
    common.LoadPrinterBind = function () {
        common.Ajax({
            url: '/Common/LoadPrinterBind',
            success: function (response) {
                if (common.IsError(response)) {
                    return;
                }
                _printerBindList = response.Data;
            }
        });
    };
    //同步加载系统打印机绑定数据
    common.AsyncLoadPrinterBind = function () {
        common.Ajax({
            url: '/Common/LoadPrinterBind',
            async: false,
            success: function (response) {
                if (common.IsError(response)) {
                    return;
                }
                _printerBindList = response.Data;
            }
        });
    };

    //加载系统打印机绑定数据
    common.SetPrinterBind = function (templateId, templateType, printerName, isNotMsg) {
        if (!templateId)
            return;
        if (!printerName)
            return;
        var isExists = false;
        if (_printerBindList != null && _printerBindList != undefined && _printerBindList.length > 0) {
            common.Foreach(_printerBindList, function (i, bm) {
                if (bm.TemplateId == templateId && bm.TemplateType == templateType && bm.PrinterName == printerName && bm.ShopId == common.CurrShop.Id) {
                    isExists = true;
                    return 'break;';
                }
            });
        }
        if (isExists == true && isNotMsg != true) {
            layer.msg("打印机绑定成功");
            return;
        }
        common.Ajax({
            url: '/Common/BindPrinter',
            data: { model: { TemplateId: templateId, TemplateType: templateType, PrinterName: printerName } },
            success: function (response) {
                if (common.IsError(response)) {
                    return;
                } else if (isNotMsg != true)
                    layer.msg("打印机绑定成功");
                //删除之前绑定数据
                common.Foreach(_printerBindList, function (ii, pp) {
                    if (pp.TemplateId == templateId && pp.TemplateType == templateType && pp.ShopId == common.CurrShop.Id) {
                        _printerBindList.splice(ii, 1);
                    }
                });
                //添加绑定数据
                _printerBindList.push(response.Data);
            }
        });
    };


    //加载默认打印机 templateType 1快递单 2发货单 3拿货单
    common.GetDefaultPrinter = function (templateId, templateType) {
        var defaultPrinter = null;
        common.Foreach(_printerBindList, function (i, o) {
            if (o.TemplateId == templateId && o.TemplateType == templateType && o.ShopId == common.CurrShop.Id) {
                defaultPrinter = o;
                return "break";
            }
        });
        return defaultPrinter;
    }

    //根据Sid获取平台ShopId
    common.GetShopIdBySid = function (sid) {
        var shopId = '';
        common.Foreach(common.Shops, function (i, o) {
            if (o.Id == sid) {
                shopId = o.ShopId;
                return "break";
            }
        });
        return shopId;
    }


    //加载地区信息
    common.LoadAreaCodeInfo = function (parentId, callback) {
        common.Ajax({
            url: '/Common/LoadAreaCodeInfo',
            //loading: true,
            async: false,
            data: { parentId: parentId },
            success: function (response) {
                callback(response);
            }
        });
    }

    //加载平台地区信息
    common.LoadPlatformAreaCodeInfo = function (pt, parentKey, callback) {
        common.Ajax({
            url: '/Common/LoadPlatformAreaCodeInfo',
            //loading: true,
            async: false,
            data: { pt: pt, parentKey: parentKey },
            success: function (response) {
                callback(response);
            }
        });
    }

    //加载配置
    common.LoadCommonSetting = function (settingKey, async, callback, isLoading) {
        //var requestUrl = '/Common/LoadCommonSetting?encodeshopid=' + commonModule.CurrShop.EncodeShopId;
        common.Ajax({
            url: '/Common/LoadCommonSetting',
            loading: (isLoading == undefined || isLoading == false) ? false : true,
            async: async == undefined ? true : async,
            data: { settingKey: settingKey },
            success: function (response) {
                if (typeof callback == 'function')
                    callback(response);
            }
        });
    }

    //保存配置
    common.SaveCommonSetting = function (settingKey, settingValue, callback, isLoading) {

        common.Ajax({
            url: '/Common/SaveCommonSetting',
            loading: (isLoading == undefined || isLoading == false) ? false : true,
            data: { settingKey: settingKey, settingValue: settingValue },
            success: function (response) {
                if (typeof callback == 'function')
                    callback(response);
            }
        });
    }

    //保存配置 => settingValue为空则保持服务器当前时间
    common.SaveDateTimeCommonSetting = function (settingKey, settingValue, callback, isLoading) {

        common.Ajax({
            url: '/Common/SaveNowTimeCommonSetting',
            loading: (isLoading == undefined || isLoading == false) ? false : true,
            data: { settingKey: settingKey, settingValue: (settingValue || "") },
            success: function (response) {
                if (typeof callback == 'function')
                    callback(response);
            }
        });
    }

    //加载区域信息到控件
    common.LoadAreaInfoToControl = function (controlId, parentId, changeCallback, setDefaultFun, valueField) {

        var defaultOption = "<option value='0' data-value='0'>==请选择==</option>";

        var __areaDataToControl = function (areaData) {

            _areaCodeInfoList[parentId] = areaData;

            common.Foreach(areaData, function (i, o) {
                if (valueField && valueField.toLowerCase() == "name")
                    control.append("<option data-value='" + o.Id + "' value='" + o.Name + "'>" + o.Name + "</option>");
                else
                    control.append("<option data-value='" + o.Id + "'  value='" + o.Id + "'>" + o.Name + "</option>");
            });

            //事件绑定
            control.unbind('change').bind('change', function () {

                var parentId = "";
                if (valueField && valueField.toLowerCase() == "name") {
                    var select = $(this);
                    var val = select.val();
                    parentId = select.find("option[value='" + val + "']").attr("data-value");
                }
                else {
                    parentId = $(this).val();
                }

                __clearNextControl($(this));

                if (changeCallback != undefined && typeof changeCallback == "function")
                    changeCallback($(this))

                if (parentId == 0) {

                    return;
                }

                var nextControlId = $(this).attr("nextControlId");
                if (nextControlId == undefined) {
                    return;
                }
                common.LoadAreaInfoToControl(nextControlId, parentId, changeCallback, setDefaultFun, valueField);
            });

            //设置默认值
            if (setDefaultFun != undefined && typeof setDefaultFun == "function")
                setDefaultFun(control);

        }

        var __clearNextControl = function (control) {

            var nextControlId = control.attr("nextControlId");
            if (nextControlId == undefined) {
                return;
            }
            var nextControl = $("#" + nextControlId);

            var defaultOptText = nextControl.attr('defaultOptText');
            if (!!defaultOptText == true)
                defaultOptText = "<option value='0' data-value='0'>" + defaultOptText + "</option>";
            else
                defaultOptText = defaultOption;
            nextControl.empty().append(defaultOptText);
            __clearNextControl(nextControl);
        }

        var control = $("#" + controlId);
        if (control.length == undefined || control.length == 0) {
            return;
        }
        __clearNextControl(control);

        var defaultOptText = control.attr('defaultOptText');
        if (!!defaultOptText == true)
            defaultOptText = "<option value='0' data-value='0'>" + defaultOptText + "</option>";
        else
            defaultOptText = defaultOption;
        control.empty().append(defaultOptText);

        if (_areaCodeInfoList[parentId] != undefined) {
            __areaDataToControl(_areaCodeInfoList[parentId])
        }
        else {
            common.LoadAreaCodeInfo(parentId, function (rsp) {

                if (common.IsError(rsp)) {
                    return;
                }
                __areaDataToControl(rsp.Data);
            });
        }

    }

    //清除本地已经加载的 地址数据
    common.ClearAreaInfo = function (parentId) {
        if (_areaCodeInfoList)
            _areaCodeInfoList[parentId] = null;
    }



    //加载平台区域信息到控件
    common.LoadPlatformAreaInfoToControl = function (pt, controlId, parentKey, changeCallback, setDefaultFun, valueField) {

        var defaultOption = "<option value='" + pt + "' data-value='' data-code=''>==请选择==</option>";

        var __areaDataToControl = function (areaData) {

            _platformAreaCodeInfoList[parentKey] = areaData;

            common.Foreach(areaData, function (i, o) {
                if (valueField && valueField.toLowerCase() == "name")
                    control.append("<option data-value='" + o.UniqueKey + "' data-code='" + o.Code + "' value='" + o.Name + "'>" + o.Name + "</option>");
                else
                    control.append("<option data-value='" + o.UniqueKey + "' data-code='" + o.Code + "'  value='" + o.UniqueKey + "'>" + o.Name + "</option>");
            });

            //事件绑定
            control.unbind('change').bind('change', function () {

                var parentKey = "";
                if (valueField && valueField.toLowerCase() == "name") {
                    var select = $(this);
                    var val = select.val();
                    parentKey = select.find("option[value='" + val + "']").attr("data-value");
                }
                else {
                    parentKey = $(this).val();
                }

                __clearNextControl($(this));

                if (changeCallback != undefined && typeof changeCallback == "function")
                    changeCallback($(this))

                if (parentKey == pt) {

                    return;
                }

                var nextControlId = $(this).attr("nextControlId");
                if (nextControlId == undefined) {
                    return;
                }
                common.LoadPlatformAreaInfoToControl(pt, nextControlId, parentKey, changeCallback, setDefaultFun, valueField);
            });

            //设置默认值
            if (setDefaultFun != undefined && typeof setDefaultFun == "function")
                setDefaultFun(control);

        }

        var __clearNextControl = function (control) {

            var nextControlId = control.attr("nextControlId");
            if (nextControlId == undefined) {
                return;
            }
            var nextControl = $("#" + nextControlId);

            var defaultOptText = nextControl.attr('defaultOptText');
            if (!!defaultOptText == true)
                defaultOptText = "<option value='" + pt + "' data-value='" + pt + "'>" + defaultOptText + "</option>";
            else
                defaultOptText = defaultOption;
            nextControl.empty().append(defaultOptText);
            __clearNextControl(nextControl);
        }

        var control = $("#" + controlId);
        if (control.length == undefined || control.length == 0) {
            return;
        }
        __clearNextControl(control);

        var defaultOptText = control.attr('defaultOptText');
        if (!!defaultOptText == true)
            defaultOptText = "<option value='" + pt + "' data-value='" + pt + "'>" + defaultOptText + "</option>";
        else
            defaultOptText = defaultOption;
        control.empty().append(defaultOptText);

        if (_platformAreaCodeInfoList[parentKey] != undefined) {
            __areaDataToControl(_platformAreaCodeInfoList[parentKey])
        }
        else {
            common.LoadPlatformAreaCodeInfo(pt, parentKey, function (rsp) {

                if (common.IsError(rsp)) {
                    return;
                }
                __areaDataToControl(rsp.Data);
            });
        }

    }

    //清除本地已经加载的 地址数据
    common.ClearPlatformAreaInfo = function (parentKey) {
        if (_platformAreaCodeInfoList)
            _platformAreaCodeInfoList[parentKey] = null;
    }

    //拆分地址
    common.AddressSplit = function (address) {
        var retArra = [];
        address = address || address.trimStartDgj().trimEndDgj();
        if (address == false) {
            return ['', '', '', ''];
        }

        var splitAction = function (address, deep, parentId) {

            var areaList = _areaCodeInfoList[parentId];
            if (areaList == null || areaList == undefined || areaList.length == 0) {
                common.Ajax({
                    url: '/Common/LoadAreaCodeInfo',
                    async: false,
                    data: { parentId: parentId },
                    success: function (rsp) {
                        if (common.IsError(rsp)) {
                            return;
                        }
                        if (rsp.Data != null && rsp.Data.length > 0) {
                            _areaCodeInfoList[parentId] = rsp.Data;
                            splitAction(address, deep, parentId);
                        }
                    }
                });
            }
            else {
                var areaModel = null;
                var index = -1;
                common.Foreach(areaList, function (i, o) {
                    index = address.indexOf(o.Name);
                    if (index >= 0) {
                        areaModel = o;
                        return 'break';
                    }
                });
                if (areaModel != null) {
                    retArra.push(areaModel.Name);
                    if (deep < 2) {
                        splitAction(address, (++deep), areaModel.Id);
                    }
                    else {
                        retArra.push(address.substring((index + areaModel.Name.length)));
                    }
                }
            }
        }

        splitAction(address, 0, 1, retArra);

        if (retArra.length < 4) {

            var consignee_province = "";
            var consignee_city = "";
            var consignee_area = "";
            var consignee_address = "";

            var toAreaList = address.split(' ');

            if (toAreaList.length >= 1) {
                consignee_province = toAreaList[0];
                if (consignee_province.length > 20)
                    consignee_province = consignee_province.substring(0, 20);
            }

            if (toAreaList.length >= 2) {
                consignee_city = toAreaList[1];
                if (consignee_city.length > 20)
                    consignee_city = consignee_city.substring(0, 20);
            }

            if (toAreaList.length >= 3) {
                consignee_area = toAreaList[2];
                if (consignee_area.length > 20)
                    consignee_area = consignee_area.substring(0, 20);
            }

            if (toAreaList.length >= 4) {
                consignee_address = toAreaList[3];
                if (consignee_address.length > 64)
                    consignee_address = consignee_address.substring(0, 64);
            }

            var tempList = [];
            tempList.push(consignee_province);
            tempList.push(consignee_city);
            tempList.push(consignee_area);
            tempList.push(consignee_address);

            var startIndex = retArra.length;
            for (var i = startIndex; i < 4; i++) {
                retArra.push(tempList[i]);
            }
        }

        return retArra;
    }

    //文件上传
    common.UploadFile = function (options) {
        var defaultOpts = {
            Url: common.rewriteUrl('/common/SaveFile'), //后台接受文件的action
            FileSaveDirect: 'Files/Temp', //文件保存目录
            IsShowFilePath: true,  //选择文件后界面是否显示文件
            DisplayId: 'txtFileName', //显示文件的空间名称
            UploadedCallback: function (path, ext) { }, //文件上传完成后的回调
            FileExtCheck: function (ext) { }, //文件扩展名检查
            OtherParams: null, //其他参数OtherParams={ p1:'p1',p2:'p2'...}
            //CheckRepeat: true, //检查文件重复上传
            FileFullPath: '', //文件全路劲
            FileExt: '', //文件扩展名(带点)
        };
        defaultOpts.url = common.addTraceIdToAjaxUrl(defaultOpts.url);
        defaultOpts.url = common.dbnameToAjaxUrl(defaultOpts.url);
        var opts = $.extend({}, defaultOpts, options);

        if (opts.Url == false) {
            layer.alert('请指定文件上传的处理方法', { skin: 'wu-dailog' });
            return;
        }

        if (opts.FileSaveDirect == false) {
            layer.alert('请指定文件保存路径', { skin: 'wu-dailog' });
            return;
        }

        var loading = null;

        var iframeName = "ifrm_" + (new Date()).getTime();
        var iframe = $('<iframe name="' + iframeName + '" />');
        //iframe.css({ width: 300, height: 100 });
        iframe.css({ width: 0, height: 0 });

        var form = $('<form>');
        form.attr("action", opts.Url);
        form.attr("method", "post");
        form.attr("enctype", "multipart/form-data");
        form.attr("target", iframeName);
        form.hide();

        var fileInput = $('<input>');
        fileInput.attr("type", "file");
        fileInput.attr("name", "fileInfo");
        form.append(fileInput);

        var param1 = $('<input>');
        param1.attr("type", "text");
        param1.attr("name", "fileDirectory");
        param1.val(opts.FileSaveDirect);
        form.append(param1);

        //var param2 = $('<input>');
        //param2.attr("type", "text");
        //param2.val(opts.CheckRepeat);
        //form.append(param2);

        if (opts.OtherParams != null) {
            for (var p in opts.OtherParams) {
                var v = opts.OtherParams[p];
                if (Array.isArray(v) == true) {
                    for (var i = 0; i < v.length; i++) {
                        var px = $('<input>');
                        px.attr("type", "text");
                        px.attr("name", p + '[' + i + ']');
                        px.val(v[i]);
                        form.append(px);
                    }
                } else {
                    var px = $('<input>');
                    px.attr("type", "text");
                    px.attr("name", p);
                    px.val(v);
                    form.append(px);
                }
            }
        }

        $('body').append(form).append(iframe);

        //iframe.load(function (rsp) {
        //    layer.close(loading);
        //    var innerText = iframe.contents().find('body').text();
        //    var firstIndex = innerText.indexOf('{');
        //    var lastIndex = innerText.lastIndexOf('}');
        //    if (firstIndex > -1 && lastIndex > firstIndex) {
        //        innerText = innerText.substring(firstIndex, (lastIndex + 1));
        //    }
        //    //alert(result);
        //    if (innerText == "")
        //        return;
        //    var result = JSON.parse(innerText);
        //    if (result.Success == false) {
        //        layer.alert('文件上传失败：' + result.Message);
        //        return;
        //    }
        //    if (typeof opts.UploadedCallback == "function") {
        //        opts.UploadedCallback(result, opts.FileFullPath, opts.FileExt);
        //    }
        //    form.remove();
        //    iframe.remove();
        //});
        iframe[0].onload = iframe[0].onreadystatechange = function () {
            if (this.readyState && this.readyState != "complete") return;
            else {
                //获取iframe里面的内容
                var innerText = iframe[0].contentDocument.body.textContent;
                //上传完成后的处理
                if (innerText != "") {
                    var firstIndex = innerText.indexOf('{');
                    var lastIndex = innerText.lastIndexOf('}');
                    if (firstIndex > -1 && lastIndex > firstIndex) {
                        innerText = innerText.substring(firstIndex, (lastIndex + 1));
                    }
                    var result = JSON.parse(innerText);
                    if (result.Success == false) {
                        layer.alert('文件上传失败：' + result.Message, { skin: 'wu-dailog' });
                        return;
                    }
                    if (typeof opts.UploadedCallback == "function") {
                        opts.UploadedCallback(result, opts.FileFullPath, opts.FileExt);
                    }
                    form.remove();
                    iframe.remove();
                }
            }
        }

        fileInput.change(function () {
            var fileFullName = $(this).val();
            opts.FileFullPath = fileFullName;
            if (typeof opts.FileExtCheck == "function") {
                var flagIndex = fileFullName.lastIndexOf('.');
                var ext = '';
                if (flagIndex > -1) {
                    ext = fileFullName.substring(flagIndex);
                }
                opts.FileExt = ext;
                var checkResult = opts.FileExtCheck(ext);
                if (checkResult == false) {
                    layer.alert('不支持上传【' + ext + '】的文件', { skin: 'wu-dailog' });
                    return;
                }
            }
            if (opts.IsShowFilePath) {
                $('#' + opts.DisplayId).val(fileFullName);
            }
            loading = common.LoadingMsg('文件上传中');
            form.submit();
        }).trigger('click');
    }

    //打印预览图片
    var _doPrint = null;
    common.SetPrintAction = function (action) {
        _doPrint = action;
    }

    //转为主域名url，即精选平台的url。带token，不带dbname
    common.rewriteUrlToMainDomainNotDbName = function rewriteUrl(url) {

        var domain = window.location.host;
        var href = window.location.href;
        domain = href.split(domain)[0] + domain;
        domain = domain.replaceAll("fxpdd", "fxali").replaceAll("fxjd", "fxali").replaceAll("fxdd", "fxali");

        if (url.indexOf("/") == 0) {
            url = domain + url;
        }
        else {
            url = domain + "/" + url;
        }

        var newUrl = common.rewriteUrl(url);
        //newUrl = common.dbnameToAjaxUrl(newUrl);//dbname

        return newUrl;
    }

    common.PreviewImg = function (imgs) {
        //var imgJson = {
        //    title: "xxx", //相册标题
        //    id: 123, //相册id
        //    start: 0, //初始显示的图片序号，默认0
        //    data: [], //相册包含的图片，数组格式
        //};
        //common.Foreach(imgs, function (i, img) {
        //    imgJson.data.push(
        //          {
        //              alt: "电子面单" + (i + 1),
        //              pid: i, //图片id
        //              src: img, //原图地址
        //              thumb: "" //缩略图地址
        //          });
        //});

        //layer.photos({
        //    photos: imgJson,
        //    closeBtn: 2,
        //    anim: -1 //0-6的选择，指定弹出图片动画类型，默认随机（请注意，3.0之前的版本用shift参数）
        //});

        var _images = []; //预览的图片
        var _index = 1;
        _images = imgs;
        var img_container = $('#div_preview_imags');
        img_container.empty();
        common.Foreach(imgs, function (i, img) {
            var display = 'none';
            if (i == 0) {
                display = "block";
            }
            img_container.append('<img name="img_' + (i + 1) + '" src="' + img + '" style="display:' + display + ';width:330px;height:600px;" />');
        });

        $('.aialog_wrapper_main_iconClose').unbind('click').bind('click', function () {
            $('#div_print_preview').hide();
        });
        $('.aialog_wrapper_main_iconLeft').unbind('click').bind('click', function () {
            $('#div_preview_imags img[name="img_' + _index + '"]').hide();
            if (_index == 1) {
                _index = _images.length;
            }
            else {
                _index = _index - 1;
            }
            $('#div_preview_imags img[name="img_' + _index + '"]').show();
            $('#lbl_preview_current_count').text(_index);
        });
        $('.aialog_wrapper_main_iconRight').unbind('click').bind('click', function () {
            $('#div_preview_imags img[name="img_' + _index + '"]').hide();
            if (_index == _images.length) {
                _index = 1;
            }
            else {
                _index = _index + 1;
            }
            $('#div_preview_imags img[name="img_' + _index + '"]').show();
            $('#lbl_preview_current_count').text(_index);
        });

        $('#div_preivew_doprint').unbind('click').bind('click', function () {
            if (typeof _doPrint == 'function') {
                _doPrint();
                $('#div_print_preview').hide();
            }
        });

        $('#lbl_preview_current_count').text(_index);
        $('#lbl_preview_count').text(_images.length);
        $('#div_print_preview').show();

    }

    //获取当前模板使用的打印组件
    common.GetUsePrintComponents = function (templateType) {
        var printComponents = '';
        if (templateType == 1 || templateType == 3 || templateType == 10 || templateType == 11 || templateType == 12 || (templateType >= 60 && templateType <= 70) || (templateType >= 80 && templateType <= 90)) {
            printComponents = 'Lodop';
        }
        else if (templateType == 13) {
            printComponents = 'Fengqiao';
        }
        else if (templateType <= 9) {
            printComponents = 'Cainiao';
        }
        else if ((templateType > 20 && templateType < 30) || common.IsPddKuaiYunTemplate(templateType)) {
            printComponents = 'Pinduoduo';
        }
        else if (common.IsLinkTemplate(templateType) || common.IsLinkKuaiYunTemplate(templateType)) {
            printComponents = 'Cainiao';
        }
        else if (templateType >= 91 && templateType < 100) {
            printComponents = 'JingDong';
        }
        else if (common.IsTouTiaozjTemplate(templateType) || common.IsTouTiaoKuaiYunTemplate(templateType)) {
            printComponents = 'TouTiao';
        }
        //else if (templateType == 111 || templateType == 116) {
        else if (common.IsKuaiShouTemplate(templateType)) {
            printComponents = 'KuaiShou';
        }
        else if (common.IsXiaoHongShuTemplate(templateType)) {
            printComponents = 'XiaoHongShu';
        }
        else if (common.IsNewXiaoHongShuTemplate(templateType)) {
            printComponents = 'NewXiaoHongShu';
        }
        else if (common.IsWxVideoTemplate(templateType)) {
            printComponents = 'WxVideo';
        }
        return printComponents;
    }

    common.SavePrintLog = function () {

    }

    common.getPrintSerialNum = function () {

    }

    common.clearSerialNum = function () {

    }

    common.isCustomerOrder = function () {
        var path = window.location.pathname;
        if (path.indexOf("FreePrintList") > -1 || path.indexOf("FreePrintExpress") > -1)
            return true;
        return false;
    }

    //common.isWaitPrintOrder = function () {
    //    var path = window.location.pathname;
    //    if (path && path.toLowerCase().indexOf("waitorder") > -1)
    //        return true;
    //    return false;
    //}

    //1：所有订单页面
    //2：待打单页面
    //3：线下单页面
    //4：已发货明细页面
    //5: 付款失败记录页面
    common.FxPageType = function () {
        var path = window.location.pathname;
        if (!path)
            return 1;
        path = path.toLowerCase();
        if (path.indexOf("allorder") > -1 || path.indexOf("afterSales") > -1)
            return 1;
        else if (path.indexOf("waitorder") > -1 || path.indexOf("scanprint") > -1)
            return 2;
        else if (path.indexOf("offlineorder") > -1 || path.indexOf("offlineproduct") > -1)
            return 3;
        else if (path.indexOf("sendorder") > -1)
            return 4;
        else if (path.indexOf("paymentfailurelog") > -1)
            return 5;
        return 1;
    }

    common.QuickSearchMethod = function () {
        var path = window.location.pathname;
        if (!path)
            return "allorder";
        path = path.toLowerCase();
        if (path.indexOf("allorder") > -1)
            return "allorder";
        else if (path.indexOf("waitorder") > -1)
            return "waitorder";
        else if (path.indexOf("offlineorder") > -1)
            return "offlineorder";
        else if (path.indexOf("sendorder") > -1)
            return "sendorder";
        else if (path.indexOf("afterSales") > -1)
            return "afterSales";
        return "allorder";
    }

    // 判断字符串用逗号隔开后的长度是否超过指定长度
    common.IsOverMaxLength = function (str, maxLength) {
        if (str === null || str === undefined || str === "") {
            return false;
        }

        // 替换中文逗号为英文逗号
        str = str.replace(/，/g, ',');

        // 按逗号分隔字符串
        var strArray = str.split(',');

        // 过滤空字符串
        strArray = strArray.filter(function(item) {
            return item && item.trim() !== '';
        });

        // 检查数组长度是否超过最大长度
        return strArray.length > maxLength;
    };  
    
    common.NameInputOnBlurHandler = function (ipt) {
        var $ipt = $(ipt);
        var val = $ipt.val();
        if (val.length > 20) {
            layer.alert("收发件人姓名不能超过20个字符", { skin: 'wu-dailog' });
        }
    }

    common.PhoneInputOnBlurHandler = function (ipt) {
        var $ipt = $(ipt);
        var val = $ipt.val();
        val = val.replace(/[+-]+/g, '');
        if (isNaN(val) || val.length > 18) {
            layer.alert("电话号码只能包含+、-、数字且不能超过20个字符", { skin: 'wu-dailog' });
        }
    }

    common.IsPddFds = function () {
        return location.href.toLowerCase().indexOf('indexfds') > -1; //是厂商代打页面
    }

    common.closeWindows = function closeWindows() {
        var browserName = navigator.appName;
        var browserVer = parseInt(navigator.appVersion);
        //alert(browserName + " : "+browserVer);
        //document.getElementById("flashContent").innerHTML = "<br>&nbsp;<font face='Arial' color='blue' size='2'><b> You have been logged out of the Game. Please Close Your Browser Window.</b></font>";
        if (browserName == "Microsoft Internet Explorer") {
            var ie7 = (document.all && !window.opera && window.XMLHttpRequest) ? true : false;
            if (ie7) {
                //This method is required to close a window without any prompt for IE7 & greater versions.
                window.open('', '_parent', '');
                window.close();
            }
            else {
                //This method is required to close a window without any prompt for IE6
                this.focus();
                self.opener = this;
                self.close();
            }
        } else {

            try {
                window.focus();
                self.opener = this;
                self.close();
            }
            catch (e) {

            }
            //For NON-IE Browsers except Firefox which doesnt support Auto Close
            try {
                window.location.href = "about:blank";
                window.close();
            } catch (e) {
            }
            try {
                window.open('', '_self', '');
                window.close();
            }
            catch (e) {

            }
        }
    }

    common.getToken = function getToken() {
        var token = $("#token_input").val();
        if (!token)
            token = "";
        return token;
    }

    common.rewriteUrl = function rewriteUrl(url) {
        var token = $("#token_input").val();
        if (!token)
            token = "";
        if (!url)
            return "?token=" + token;
        if (url.indexOf("token=") != -1)
            return url;
        if (url.indexOf("?") != -1)
            return url + "&token=" + token;
        else
            return url + "?token=" + token;
        return url;
    }

    //转为主域名url，即精选平台的url。带token、dbname
    common.rewriteUrlToMainDomain = function rewriteUrl(url) {

        var domain = window.location.host;
        var href = window.location.href;
        domain = href.split(domain)[0] + domain;
        domain = domain.replaceAll("fxpdd", "fxali").replaceAll("fxjd", "fxali").replaceAll("fxdd", "fxali");

        if (url.indexOf("/") == 0) {
            url = domain + url;
        }
        else {
            url = domain + "/" + url;
        }

        var newUrl = common.rewriteUrl(url);
        newUrl = common.dbnameToAjaxUrl(newUrl);//dbname

        return newUrl;
    }


    //转为主域名url，即精选平台的url。带token，不带dbname
    common.rewriteUrlToMainDomainNotDbName = function rewriteUrl(url) {

        var domain = window.location.host;
        var href = window.location.href;
        domain = href.split(domain)[0] + domain;
        domain = domain.replaceAll("fxpdd", "fxali").replaceAll("fxjd", "fxali").replaceAll("fxdd", "fxali");

        if (url.indexOf("/") == 0) {
            url = domain + url;
        }
        else {
            url = domain + "/" + url;
        }

        var newUrl = common.rewriteUrl(url);
        //newUrl = common.dbnameToAjaxUrl(newUrl);//dbname

        return newUrl;
    }

    common.rewriteTopUrl = function rewriteUrl(url) {
        var url = common.rewriteUrl(url);
        return document.parentLocationOrigin + "/" + url;
    }

    common.rewriteUrlMoreArea = function rewriteUrlMoreArea(url) {
        var dbname = $("#dbname_input").val();
        if (!dbname)
            dbname = "";
        var token = $("#token_input").val();
        if (!token)
            token = "";
        if (!url)
            return "?token=" + token + "&dbname=" + dbname;
        if (url.indexOf("token=") != -1)
            return url;
        if (url.indexOf("?") != -1)
            return url + "&token=" + token + "&dbname=" + dbname;
        else
            return url + "?token=" + token + "&dbname=" + dbname;
        return url;
    }

    common.addTraceIdToAjaxUrl = function addTraceIdToAjaxUrl(url) {
        var token = $("#traceid_input").val();
        if (!token)
            token = "";
        if (!url)
            return "?traceId=" + token;
        if (url.indexOf("traceId=") != -1)
            return url;
        if (url.indexOf("?") != -1)
            return url + "&traceId=" + token;
        else
            return url + "?traceId=" + token;
        return url;
    }

    common.dbnameToAjaxUrl = function dbnameToAjaxUrl(url) {

        var dbname = $("#dbname_input").val();
        if (!dbname)
            dbname = "";
        if (!url)
            return "?dbname=" + encodeURIComponent(bname);
        if (url.indexOf("dbname=") != -1) {
            var re = eval('/(' + "dbname" + '=)([^&]*)/gi');
            var nsrc = url.replace(re, "dbname" + '=' + encodeURIComponent(dbname));
            return nsrc;
        }
        if (url.indexOf("?") != -1)
            return url + "&dbname=" + encodeURIComponent(dbname);
        else
            return url + "?dbname=" + encodeURIComponent(dbname);
        return url;
    }


    common.dataFlagToAjaxUrl = function dataFlagToAjaxUrl(url) {
        var dataFlag = $("#dataFlag_input").val();
        if (!dataFlag)
            dataFlag = "";
        if (!url)
            return "?dataFlag=" + dataFlag;
        if (url.indexOf("dataFlag=") != -1)
            return url;
        if (url.indexOf("?") != -1)
            return url + "&dataFlag=" + dataFlag;
        else
            return url + "?dataFlag=" + dataFlag;
        return url;
    }

    common.transferUrl = function (url, target) {
        var newUrl = common.rewriteUrl(url);
        newUrl = common.dbnameToAjaxUrl(newUrl);//dbname
        var a = $('<a>');
        if (target)
            a.attr("target", target);
        a.attr('href', newUrl);
        a.append('<p style="display:none;">跳转</p>');
        $('body').append(a);
        a.children().trigger('click').end().remove();
    }

    //跳到对应的主域名
    common.transferUrlToMainDomain = function (url, target) {

        //var domain = window.location.host;
        //var href = window.location.href;
        //domain = href.split(domain)[0] + domain;
        //domain = domain.replaceAll("fxpdd", "fxali").replaceAll("fxjd", "fxali").replaceAll("fxdd", "fxali");

        //if (url.indexOf("/") == 0) {
        //    url = domain + url;
        //}
        //else {
        //    url = domain + "/" + url;
        //}

        //var newUrl = common.rewriteUrl(url);
        //newUrl = common.dbnameToAjaxUrl(newUrl);//dbname
        var newUrl = common.rewriteUrlToMainDomain(url);
        var a = $('<a>');
        if (target)
            a.attr("target", target);
        a.attr('href', newUrl);
        a.append('<p style="display:none;">跳转</p>');
        $('body').append(a);
        a.children().trigger('click').end().remove();
    }

    $(document).ready(function () {
        $(".navigations a,.leftNav_left a,a").each(function (index, a) {
            if (a.href && a.href != "#" && a.href.indexOf("javascript") == -1 && a.className.indexOf("no-token") == -1) {
                a.href = common.rewriteUrl(a.href);
                a.href = common.dbnameToAjaxUrl(a.href);//dbname
            }
        });
    });

    common.retryReady = function () {
        $(".navigations a,.leftNav_left a,a").each(function (index, a) {
            if (a.href && a.href != "#" && a.href.indexOf("javascript") == -1 && a.className.indexOf("no-token") == -1) {
                a.href = common.rewriteUrl(a.href);
                a.href = common.dbnameToAjaxUrl(a.href);//dbname
            }
        });
    }

    common.isEmptyObject = function (obj) {
        for (var key in obj) {
            return false
        };
        return true
    }

    //自定：定位没有遮罩的弹框
    var positionDialogThis, positionDialogContent;
    common.positionDialog = function (isThis, newObject) {

        positionDialogThis = isThis;          //current接收点击的this;
        positionDialogContent = newObject.content;//为用弹框的class 格式比如：".dialog";
        $(positionDialogThis).children(positionDialogContent).remove();//先清理点击元素里面已经出现的弹框;
        $(positionDialogContent).clone().show().appendTo($(positionDialogThis));
        $(positionDialogContent).css({ top: newObject.top, left: newObject.left, position: "absolute" });

    }
    $(document).mouseup(function (e) {
        if (positionDialogThis) {
            var pop = $(positionDialogThis);
            if (!pop.is(e.target) && pop.has(e.target).length === 0) {
                $(positionDialogThis).children(positionDialogContent).remove();
            }
        }

    });

    //阻止浏览器的默认动作
    common.preventDefault = function (e) {
        if (e && e.preventDefault) {
            e.preventDefault();
        } else {
            window.event.returnValue = false;
        }
    }
    common.openProductUrl = function (id) {
        var platformType = (common.PlatformType || "").toLowerCase();
        var url = "";
        if (platformType == "1688" || platformType == "Alibaba" || platformType == "alibaba")
            url = "https://detail.1688.com/offer/" + id + ".html";
        else if (platformType == "tb" || platformType == "Taobao" || platformType == "taobao"
            || platformType == "AlibabaC2M" || platformType == "alibabac2m"
        )
            url = "https://item.taobao.com/item.htm?id=" + id;
        else if (platformType == "pdd" || platformType == "Pinduoduo" || platformType == "pinduoduo")
            url = "https://mms.pinduoduo.com/goods/goods_detail?goods_id=" + id;
        else if (platformType == "jd" || platformType == "Jingdong" || platformType == "jingdong")
            url = "https://item.jd.com/" + id + ".html";
        else if (platformType == "yz" || platformType == "YouZan" || platformType == "youzan")
            url = "https://detail.youzan.com/show/goods?alias=" + id;
        else if (platformType == "wm" || platformType == "WeiMeng" || platformType == "weimeng")
            url = "https://100000080481.retail.n.weimob.com/saas/retail/100000080481/3060881/goods/detail?id=" + id;
        else if (platformType == "wd" || platformType == "WeiDian" || platformType == "weidian")
            url = "https://weidian.com/item.html?itemID=" + id + "&spider_token=" + Math.random(1000);
        else if (commonModule.IsTouTiaoXi())
            url = "https://haohuo.jinritemai.com/views/product/detail?id=" + id + "&origin_type=604&from=yinliu&spider_token=" + Math.random(1000);
        //else if (platformType == "yunji" || platformType == "yj" )
        //    url = "http://image.yunjiglobal.com/?id=" + id + "&fxg_admin_preview=110&origin_type=604&spider_token=" + Math.random(1000);
        else if (platformType == "kuaishou" || platformType == "ks")
            url = "https://s.kwaishop.com/goods/detail?id=" + id;
        if (url != "")
            window.open(url, "_blank");
        else
            layer.msg("当前平台暂不支持查看商品信息");
    }
    common.openOrderUrl = function (id) {
        var url = "";
        var platformType = (common.PlatformType || "").toLowerCase();
        if (platformType == "1688" || platformType == "Alibaba" || platformType == "alibaba")
            url = "https://trade.1688.com/order/new_step_order_detail.htm?orderId=" + id;
        else if (platformType == "tb" || platformType == "Taobao" || platformType == "taobao"
            //|| platformType == "AlibabaC2M" || platformType == "alibabac2m"
        )
            url = "https://trade.taobao.com/trade/detail/trade_item_detail.htm?bizOrderId=" + id;
        else if (platformType == "pdd" || platformType == "Pinduoduo" || platformType == "pinduoduo")
            url = "https://mms.pinduoduo.com/order.html#/orders/order_detail/index?type=0&sn=" + id;
        else if (platformType == "jd" || platformType == "Jingdong" || platformType == "jingdong")
            url = "https://details.jd.com/normal/item.action?PassKey=4DD91555907E4EBF4732B1EA2E07FA05&orderid=" + id;
        else if (platformType == "yz" || platformType == "YouZan" || platformType == "youzan")
            url = "https://www.youzan.com/v4/trade/order/detail?orderNo=" + id;
        else if (platformType == "wm" || platformType == "WeiMeng" || platformType == "weimeng")
            url = "http://retail.console.weimob.com/#/app/100000080481/3060881/order/order/orderdetail?orderNo=" + id + "&orderType=1";
        else if (platformType == "wd" || platformType == "WeiDian" || platformType == "weidian")
            url = "https://d.weidian.com/orderNew/?orderId=" + id + "#/orderDetail/detail";
        else if (commonModule.IsTouTiaoXi())
            url = "https://fxg.jinritemai.com/index.html#/iview/order/orderPreview/" + id;
        else if (platformType == "kuaishou" || platformType == "ks")
            url = "https://s.kwaishop.com/order/detail?id=" + id;
        if (url != "")
            window.open(url, "_blank");
        else
            layer.msg("当前平台暂不支持查看后台订单信息");
    }

    common.SaveTraditionWaybillCodeConfig = function (template) {
        try {
            //如果是传统模板，有生成规则，且有单号，则记录最后的单号
            // var orderTotalVal = $("#txtOrderTotal").val();
            var addOrSub = $("#sel_logistic_generate").val();
            if (template.TemplateType == 1 && addOrSub != 0 && window.orderTableBuilder) {
                //取最后一个选中的订单的运单号+1 作为 下次自动生成的起始单号
                var lastLogisticCode = '';
                for (var i = 0; orderTableBuilder.rows && i < orderTableBuilder.rows.length; i++) {
                    var row = orderTableBuilder.rows[i];
                    if (row.checked) {
                        var input = $("#order-" + row.Id + " .LastWaybillCode_input");
                        lastLogisticCode = input.val();
                    }
                }
                if (lastLogisticCode) {
                    //加1
                    var endStr = '';
                    if (lastLogisticCode.length > 3) {
                        endStr = lastLogisticCode.substring(lastLogisticCode.length - 4);
                    }
                    else {
                        endStr = lastLogisticCode;
                    }
                    var start_num = parseInt(endStr); //起始
                    start_num++;
                    var tempNum = lastLogisticCode.substring(0, lastLogisticCode.length - start_num.toString().length);
                    lastLogisticCode = tempNum + '' + start_num;
                    //保存 单号
                    tradionalWaybillCodeConfig[template.Id] = {
                        StartWaybillCode: lastLogisticCode,
                        AddOrSub: addOrSub,
                        //OrderTotalNumber: (orderTotalVal ? parseInt(orderTotalVal) : orderTotalVal)
                    };
                    var settingKey = "TradionalWaybillCodeConfig";//配置key
                    common.LoadCommonSetting(settingKey, true, function (rsp) {

                        var params = tradionalWaybillCodeConfig;
                        if (rsp.Data != null) {
                            var settings = eval('(' + rsp.Data + ')');
                            if (settings) {
                                params = $.extend({}, settings, tradionalWaybillCodeConfig);
                            }
                        }
                        common.SaveCommonSetting(settingKey, JSON.stringify(params), function () { });
                    });
                }
            }
        } catch (e) {
            //console.log("ERROR-保存普通面单最后的单号失败:" + e.message);
        }
    }

    //ctrl:添加搜索的控件
    //containerAppendIn:div_container 添加到哪个控件里面
    //searchAction: 查询操作(第一个参数是容器对象div_container,第二个参数 输入的关键字)
    //ptn: 控件的位置 取值 {pt:'offset or position',l:x,t:y}
    //dataAttrs: {属性名:值}，控件自定义属性数据
    //isRemoveFirst:bool，是否先移除，再添加，方式弹窗多次绑定，多次添加容器
    common.AddInputOrSelect = function (ctrl, containerAppendIn, searchAction, ptn, dataAttrs, isRemoveFirst) {

        var div_container = $("<div>");
        div_container.attr("class", "div_container");
        if (dataAttrs) {
            for (var i in dataAttrs) {
                if (i.isPrototypeOf() == false) {
                    div_container.attr(i, dataAttrs[i]);
                }
            }
        }
        if (isRemoveFirst)
            containerAppendIn.find('div.div_container').remove();
        containerAppendIn.append(div_container);

        div_container.unbind('click').on('click', function (event) {
            event.stopPropagation();
        });

        $(document).click('click', function () {
            div_container.hide();
        });

        ctrl.unbind('click').on('click', function (event) {
            var position = (ptn && ptn.pt == 'offset' || ptn == 'offset') ? $(this).offset() : $(this).position();
            var l = (ptn && ptn.l != undefined && isNaN(ptn.l) == false) ? ptn.l : 0;
            var t = (ptn && ptn.t != undefined && isNaN(ptn.t) == false) ? ptn.t : 0;
            position.top += $(this).outerHeight() + 2 + t;
            position.left += l;
            div_container.css({ left: position.left, top: position.top }).html('<img src="/Content/Images/loading.gif" width="20" height="20" />').show();

            var keyWord = '';//$(this).val();
            if (typeof searchAction == 'function')
                searchAction(div_container, keyWord);

            event.stopPropagation();
        }).unbind('input').on('input', function () {
            var keyWord = $(this).val();
            if (typeof searchAction == 'function')
                searchAction(div_container, keyWord);
        });
    }

    //前端报错，日志记录
    common.JsExcptionLog = function (operatorType, errorMsg) {
        common.ajax({
            url: '/Common/JavasScriptExcptionLog',
            data: { operatorType: operatorType, exception: errorMsg },
            success: function (rsp) {
                if (rsp.Success == false) {
                    layer.alert('前端出错且日志记录失败，请联系我们告知错误信息，谢谢你！error：' + rsp.Message, { skin: 'wu-dailog' });
                }
            }
        });
    }

    //前端日志记录
    common.JsLogToMongoDB = function (operatorType, logJson) {
        common.ajax({
            url: '/Common/JavasScriptLog',
            data: { operatorType: operatorType, logJson: logJson },
            success: function (rsp) {
                if (rsp.Success == false) {
                    layer.alert('前端日志记录失败，请联系我们告知错误信息，谢谢你！error：' + rsp.Message, { skin: 'wu-dailog' });
                }
            }
        });
    }

    // 设置用户操作记录 
    //common.SetUserActionRecord = function (record,callback) {
    //    if (!record) {
    //        console.error("用户操作记录为空");
    //    }
    //    common.ajax({
    //        url: '/Common/SetUserActionRecord',
    //        data: { record },
    //        success: function (rsp) {
    //            if (rsp.Success == false && !!rsp.Message) {
    //                console.error(rsp.Message);
    //            }
    //            if (typeof callback === "function") {
    //                callback(rsp.Data);
    //            }
    //        }
    //    });
    //}

    common.GetDateDiffDays = function (startDate, endDate) {
        var startTime = new Date(Date.parse(startDate.replace(/-/g, "/"))).getTime();
        var endTime = new Date(Date.parse(endDate.replace(/-/g, "/"))).getTime();
        var dates = Math.abs((startTime - endTime)) / (1000 * 60 * 60 * 24);
        return parseInt(dates);
    }

    common.compareDate = function (startTime, endTime) {
        var oDate1 = new Date(startTime);
        var oDate2 = new Date(endTime);
        if (oDate1.getTime() > oDate2.getTime()) {//转换成毫秒进行比较
            return false;
        }
        return true;
    }

    common.IsNumber = function (obj) {
        // return typeof obj === 'number' && isFinite(obj)
        return obj === +obj;
    }

    common.IsFloat = function (oNum) {
        if (!oNum) return false;

        var strP = /^\d+(\.\d+)?$/;
        if (!strP.test(oNum)) {
            return false
        };
        try {
            if (parseFloat(oNum) != oNum) {
                return false
            };
        } catch (ex) {
            return false;
        }
        return true;
    }

    // 验证输入框内容是否为浮点数
    common.IsFloatWithInput = function (obj) {
        if (!obj) return false;

        var oNum = $(obj).val();
        if (!oNum) return false;

        var strP = /^\d+(\.\d+)?$/;
        if (!strP.test(oNum)) {
            $(obj).val("");
            return false
        };
        try {
            if (parseFloat(oNum) != oNum) {
                $(obj).val("");
                return false
            };
        } catch (ex) {
            $(obj).val("");
            return false;
        }
        return true;
    }

    common.IsString = function (obj) {
        return obj === obj + ''
    }

    common.IsBoolean = function (obj) {
        return obj === !!obj
    }

    //用户注册
    common.register = function (from, portalUrl, token) {
        if (!portalUrl) {
            layer.alert("注册服务暂不可用，请稍后重试", { skin: 'wu-dailog' });
            return;
        }
        if (from == "pay") {
            layer.open({
                title: "提示",
                content: "<h1>您还没有注册，在续费前请先注册，注册后会自动跳转到续费页面。</h1>",
                btn: ['立即注册并续费', "已注册过，前往登录"],
                yes: function () {

                    openRegisterDialog(from, portalUrl, token);

                },
                btn2: function () {
                    window.open(portalUrl);
                },
            });
        }
        else
            openRegisterDialog("", portalUrl, token);

        //注册成功，跳转到
    }

    function openRegisterDialog(from, portalUrl, token) {
        //打开注册弹框
        //from参数为空表示用户主动注册，注册完后不进行跳转
        //from参数为“pay”,表示点击续费时弹出注册，注册完后自动跳转到续费链接（跳转链接注册接口返回：Data.PayUrl）
        //弹框
        //发送请求带上commonModule.Token

        //layer.alert("打开注册窗口");
        var html = $("#register-user-dialog-tmpl").render({ "from": from, "portalUrl": portalUrl, "token": token });
        layer.open({
            type: 1,
            title: "注册",
            skin: 'layui-layer-rim',
            area: ['380px'],
            content: html
        });
        return;
    }

    common.newRegister = function (from, portalUrl, token) { // -----------注册----------------------------------------

        if (!checkPhone("#intoRegisterTelePhone", "请输入正确的手机号！")) {
            return;
        }
        if (!checkPwd("#intoRegisterTelePass", "密码长度要大于6位，由数字和字母组成")) {
            return;
        }

        if ($('#intoRegisterTelePass').val() != $('#sureIntoRegisterTelePass').val()) {
            $('.loginWrap_content_warn').css({ display: 'flex' }).text('两次输入的密码不一致');
            timeoutsss = setTimeout(function () {
                $('.loginWrap_content_warn').hide(300)
            }, 4000)
            return;
        }

        // var body={};
        // body.Mobile= $("#intoRegisterTelePhone").val();
        // body.Password=$("#intoRegisterTelePass").val();

        var body = {
            Mobile: $("#intoRegisterTelePhone").val(),
            Password: $("#intoRegisterTelePass").val(),
            ConfirmPassword: $("#sureIntoRegisterTelePass").val(),
            MobileMeessageCode: $("#intoRegisterValidCode").val(),
            Token: common.Token
        }
        if (!token)
            body.Token = common.Token;
        else
            body.Token = token;
        $.ajax({
            url: portalUrl + '/Account/RegisterV2',
            type: 'post',
            data: body,
            success: function (rsp) {
                if (!rsp.Success) {
                    if (rsp.ErrorCode == "Has_Registered") {
                        layer.open({
                            title: "提示",
                            content: "该手机号码已经注册过了，请直接登录",
                            btn: ["前往登录", "取消"],
                            yes: function () {
                                window.open(portalUrl);
                            },
                            btn2: function () {
                                return true;
                            },
                        });
                    }
                    else
                        layer.alert(rsp.Message, { skin: 'wu-dailog' });
                } else {
                    $("body").append("<iframe style='display:none;' src='" + portalUrl + "/Account/WriteCookie?t=" + rsp.Data.Token + "'/>")
                    if (from && from == "pay") {
                        if (rsp.Data.PayUrl)
                            window.location = rsp.Data.PayUrl;
                        else
                            window.location = rsp.Data.RedirectUrl;
                    }
                    else {
                        //跳转到个人中心
                        window.location = rsp.Data.RedirectUrl;
                    }
                }
            }
        })

    }


    //页面层




    //短信验证-----------------------------------------------------

    common.postMobileMessageCode = function (portalUrl, types) {

        if (types == "1" && !checkPhone("#intoRegisterTelePhone", "您输入的手机码有误，请重新输入")) {       //检测手机号
            return;
        }

        if (types == "2" && !checkPhone("#againSetPhone", "您输入的手机码有误，请重新输入")) {       //检测手机号
            return;
        }

        var phone = "";
        if (types == "1")
            phone = $("#intoRegisterTelePhone").val();
        if (types == "2")
            phone = $("#againSetPhone").val();


        $.ajax({
            type: 'post',
            url: portalUrl + '/Account/PostMobileMessageCode',
            data: { phone: phone, types: types },
            success: function (data) {
                if (!data.Success) {

                    layer.alert(data.Message, { skin: 'wu-dailog' });
                    return;
                }

                settime();


            }
        });

    }

    function checkPhone(ele, str) {  //匹配手机号，正确返回true 错误返回false
        var phone = $(ele).val();
        if (!(/^1(3|4|5|7|8|9)\d{9}$/.test(phone))) {
            $('.loginWrap_content_warn').css({ display: 'flex' }).text(str);
            timeoutsss = setTimeout(function () {
                $('.loginWrap_content_warn').hide(300)
            }, 4000)
            return false;
        }
        return true;
    }

    function checkPwd(ele, str) {  //匹配密码，正确返回true 错误返回false
        var pwd = $(ele).val();
        var reg = /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,}$/;
        if (!reg.test(pwd)) {
            $('.loginWrap_content_warn').css({ display: 'flex' }).text(str);
            timeoutsss = setTimeout(function () {
                $('.loginWrap_content_warn').hide(300)
            }, 4000)
            return false;
        }
        return true;
    }

    common.isPhone = function (phone) {  //匹配手机号，正确返回true 错误返回false
        if (!(/^1(1|2|3|4|5|6|7|8|9)\d{9}$/.test(phone))) {
            return false;
        }
        return true;
    }
    common.stopM = function (event) {  //阻止冒泡和默认行为
        if (event) {
            event.stopPropagation();
            event.preventDefault();
        } else {
            window.event.returnValue = false;
            window.event.cancelBubble = true;
        };
    }
    common.stopMP = function (event) {  //阻止冒泡
        if (event) {
            event.stopPropagation();
        } else {
            window.event.returnValue = false;
        };
    }

    common.IEBrowserVersion = function IEBrowserVersion() {
        var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
        var isIE = userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1; //判断是否IE<11浏览器
        var isEdge = userAgent.indexOf("Edge") > -1 && !isIE; //判断是否IE的Edge浏览器
        var isIE11 = userAgent.indexOf('Trident') > -1 && userAgent.indexOf("rv:11.0") > -1;
        if (isIE) {
            var reIE = new RegExp("MSIE (\\d+\\.\\d+);");
            reIE.test(userAgent);
            var fIEVersion = parseFloat(RegExp["$1"]);
            if (fIEVersion == 7) {
                return 7;
            } else if (fIEVersion == 8) {
                return 8;
            } else if (fIEVersion == 9) {
                return 9;
            } else if (fIEVersion == 10) {
                return 10;
            } else {
                return 6;//IE版本<=7
            }
        } else if (isEdge) {
            return 'edge';//edge
        } else if (isIE11) {
            return 11; //IE11
        } else {
            return -1;//不是ie浏览器
        }
    }

    var count = 60;
    function settime() {
        if (count == 0) {
            $("#LoginCode1").attr('disabled', false);
            $("#LoginCode2").attr('disabled', false);
            $("#LoginCode1").removeClass("messageCodeclass");
            $("#LoginCode2").removeClass("messageCodeclass");
            $("#LoginCode1").val("免费获取验证码");
            $("#LoginCode2").val("免费获取验证码");
            count = 60;
            return;
        } else {
            $("#LoginCode1").attr('disabled', true);
            $("#LoginCode2").attr('disabled', true);
            $("#LoginCode1").addClass("messageCodeclass");
            $("#LoginCode2").addClass("messageCodeclass");

            $("#LoginCode1").val("重新发送(" + count + ")");
            $("#LoginCode2").val("重新发送(" + count + ")");
            count--;
        }

        setTimeout(function () { settime() }, 1000)
    }

    common.ConvertPlatformStatusToName = function (status) {
        if (status == "waitbuyerpay")
            status = "待买家付款";
        else if (status == "waitsellersend")
            status = "待发货";
        else if (status == "waitbuyerreceive")
            status = "待买家确认收货";
        else if (status == "success")
            status = "交易成功";
        else if (status == "cancel")
            status = "交易关闭";
        else if (status == "confirm_goods_but_not_fund")
            status = "货到付款";
        else if (status == "locked")
            status = "退款中";
        else
            status = "未知";
        return status;
    }

    common.ConvertPlatformTypeToName = function (type) {
        var name = "未识别";
        switch (type) {
            case "OpenV1":
                name = "开放平台";
                break;
            case "MoGuJie":
                name = "蘑菇街";
                break;
            case "WeiDian":
                name = "微店";
                break;
            case "ZhiDian":
                name = "值点";
                break;
            case "AlibabaC2M":
                name = "阿里巴巴C2M";
                break;
            case "Taobao":
                name = "淘宝";
                break;
            case "WeiMeng":
                name = "微盟";
                break;
            case "XiaoDian":
                name = "小店";
                break;
            case "MengTui":
                name = "萌推";
                break;
            case "Alibaba":
                name = "阿里巴巴";
                break;
            case "YouZan":
                name = "有赞";
                break;
            case "YunJi":
                name = "云集";
                break;
            case "TouTiao":
            case "TouTiaoXiaoDian":
                name = "头条";
                break;
            case "Offline":
                name = "微商";
                break;
            case "Pinduoduo":
                name = "拼多多";
                break;
                break;
            case "KuaiShou":
                name = "快手";
                break;
            case "Jingdong":
                name = "京东";
                break;
            case "BeiBei":
                name = "贝贝";
                break;
            case "Suning":
                name = "苏宁";
                break;
        }
        return name;
    }

    common.checkFileExt = function (fileName, arr) {  //-判断文件扩展名------fileName为文件名,arr扩展名集合比如["jpg","gif","png"]
        var flag = false;
        var $arr = arr;
        var index = fileName.lastIndexOf(".");//返回 . 所在的索引号
        var ext = fileName.substr(index + 1).toLowerCase();//获取文件扩展名
        for (var i = 0; i < $arr.length; i++) {
            if (ext == arr[i]) {
                flag = true;//一旦找到合适的，立即退出循环
                break;
            }
        }
        return flag;
    }

    common.IsPhone = function (phone) {
        if (!phone) return false;
        var reg = /^((0\d{2,3}-\d{7,8})|(1[3456789]\d{9})|(400\d+))$/; //校验手机号和固定电话
        if (!reg.test(phone)) {
            return false;
        }
        return true;
    }

    common.ToFixed = function (val, count) { //转化价格的格式
        var val = val;
        if (val != 0) {
            val = parseFloat(val) ? parseFloat(val) : "";
        }
        if (val) {
            var changenum = (parseInt(val * Math.pow(10, count) + 0.5) / Math.pow(10, count)).toString();
            index = changenum.indexOf(".");
            if (index < 0 && count > 0) {
                changenum = changenum + ".";
                for (i = 0; i < count; i++) {
                    changenum = changenum + "0";
                }

            } else {
                index = changenum.length - index;
                for (i = 0; i < (count - index) + 1; i++) {
                    changenum = changenum + "0";
                }
            }
            return changenum;
        } else {
            return val;
        }
    }

    

    //关闭升级续费弹窗
    common.closeUpgradeWindow = function () {
        $("#commonDialog_wrap").fadeOut(200);
        document.body.style.cssText = 'overflow-y:auto';
    }
    common.closeAdvancedEditionWindow = function () {
        $.cookie("noShowUprgradeSuggestWindow", "1", { expires: 1 });
        $("#advancedEdition_wrap").fadeOut(200);
    }
    //edition为升级版本变量，取值 standardEdition为标准版，advancedEdition为高级版
    //nowEdition为当前版本的名称
    //isShowChoseBtn 为是否显示右上角关闭按钮  默认显示，可以不设置
    common.showUpgradeWindowOld = function (edition, nowEdition, isShowChoseBtn) {

        var platformType = (common.PlatformType || "").toLowerCase();
        if (platformType == "1688" || platformType == "Alibaba" || platformType == "alibaba") {
            document.body.style.cssText = 'overflow-y:hidden';
            $("#commonDialog_wrap").fadeIn(200);
            $("#nowEditionName").html(nowEdition ? nowEdition : "");
            var isChoseBtn = true;
            if (typeof isShowChoseBtn == "undefined") {
                isChoseBtn = true
            } else {
                isChoseBtn = isShowChoseBtn
            }
            if (!isChoseBtn) {
                $("#dialog_upgrade_close").hide();
            } else {
                $("#dialog_upgrade_close").show();
            }
            var html = "";
            var html02 = "";
            var orderPeriodicTimeData = [];//订购周期
            var typeName = edition == "standardEdition" ? "标准版" : "高级版";
            if (edition == "standardEdition") { // 标准版
                html += '<li><i></i><span>快递模板绑定对应发件人信息</span></li>'
                html += '<li><i></i><span>扫描订单编号，打印快递单</span></li>'
                html += '<li><i></i><span>扫描包裹上的快递运单号发货，变更订单状态为已发货</span></li>'
                html += '<li><i></i><span>表格导入订单发货</span></li>'
                html += '<li><i></i><span>导入线下快递对账单到工具内，单号使用对账</span></li>'
                orderPeriodicTimeData = [
                    { id: 0, month: 1, price: 42 },
                    { id: 1, month: 3, price: 112 },
                    { id: 2, month: 6, price: 200 },
                    { id: 3, month: 12, price: 352 }
                ];
                $(".dialog-upgrade-btn").css({ paddingTop: 6 });

            } else if (edition == "advancedEdition") {   // 高级版
                html += '<li><i></i><span>待付款订单，一键快速改价</span></li>'
                html += '<li><i></i><span>设置评价内容和规则，订单自动评价</span></li>'
                html += '<li><i></i><span>备货单统计打印小标签，市场档口拿货，分拣打包，扫描打印快递单发货</span></li>'
                html += '<li><i></i><span>手机端打单发货</span></li>'
                orderPeriodicTimeData = [
                    { id: 0, month: 1, price: 50 },
                    { id: 1, month: 3, price: 135 },
                    { id: 2, month: 6, price: 240 },
                    { id: 3, month: 12, price: 420 }
                ];
                $(".dialog-upgrade-btn").css({ paddingTop: 18 });

            }
            $("#dialog_upgrade_funShow").html(html);
            for (var i = 0; i < orderPeriodicTimeData.length; i++) {
                html02 += '<li><span>' + typeName + '-' + orderPeriodicTimeData[i].month + '月</span><span>' + orderPeriodicTimeData[i].price + '元</span><a>立即订购</a></li>'
            }
            var html03 = ' <span class="dialog-upgrade-btns">已经订购，点击激活使用</span>';
            $(".typeEdition").html(typeName);
            $("#orderPeriodicTimeData").html(html02);
            $("#dialog_upgrade_btn").html(html03);
            return true;

        } else {
            return false;
        }

    }

    //edition为升级版本变量，取值 standardEdition为标准版，advancedEdition为高级版
    //nowEdition为当前版本的名称
    //isShowChoseBtn 为是否显示右上角关闭按钮  默认显示，可以不设置
    common.showUpgradeWindow = function (versionInfo, isShowChoseBtn) {

        //头条升级弹窗不一样
        if (commonModule.PlatformType == "TouTiao") {
            $("#customDialog_Bg").show();
            return;
        }

        var existWindows = $("#advancedEdition_wrap");
        if (existWindows && existWindows.length > 0)
            return;
        document.body.style.cssText = 'overflow-y:hidden';
        $("#commonDialog_wrap").fadeIn(200);
        $("#nowEditionName").html(versionInfo.CurrentSystemVersionName);
        var isChoseBtn = true;
        if (typeof isShowChoseBtn == "undefined") {
            isChoseBtn = true
        } else {
            isChoseBtn = isShowChoseBtn
        }
        if (!isChoseBtn) {
            $("#dialog_upgrade_close").hide();
        } else {
            $("#dialog_upgrade_close").show();
        }
        var html = "";
        var html02 = "";
        var orderPeriodicTimeData = [];//订购周期
        var targetVersion = versionInfo.TargetVersion;
        var functions = targetVersion.VersionFunctions;
        var typeName = targetVersion.Name;
        orderPeriodicTimeData = targetVersion.PriceList;
        var shopNames = "";
        for (var i = 0; i < versionInfo.NeedUpgradeShopNames.length; i++) {
            var sname = versionInfo.NeedUpgradeShopNames[i];
            shopNames += "【" + sname + "】";
        }
        $("#versionTip-ShopNames").html(shopNames);
        for (var i = 0; i < functions.length; i++) {
            var f = functions[i];
            html += '<li><i></i><span>' + f + '</span></li>';
        }
        $(".dialog-upgrade-btn").css({ paddingTop: 6 });
        $("#dialog_upgrade_funShow").html(html);
        for (var i = 0; i < orderPeriodicTimeData.length; i++) {
            html02 += '<li><span>' + typeName + '-' + orderPeriodicTimeData[i].month + '月</span><span>' + orderPeriodicTimeData[i].price + '元</span><a target="_blank" href="' + versionInfo.PayLink + '">立即订购</a></li>'
        }
        var html03 = ' <span class="dialog-upgrade-btns" onclick="getExpireTimeFromApi(true,\'激活中，正在重新加载页面...\');">已经订购，点击激活使用</span>';
        $(".typeEdition").html(typeName);
        $("#orderPeriodicTimeData").html(html02);
        $("#dialog_upgrade_btn").html(html03);
        return true;

    }

    //+---------------------------------------------------
    //| 求两个时间的天数差 日期格式为 YYYY-MM-dd
    //+---------------------------------------------------
    common.DaysBetween = function (DateOne, DateTwo) {
        var OneMonth = DateOne.substring(5, DateOne.lastIndexOf('-'));
        var OneDay = DateOne.substring(DateOne.length, DateOne.lastIndexOf('-') + 1);
        var OneYear = DateOne.substring(0, DateOne.indexOf('-'));

        var TwoMonth = DateTwo.substring(5, DateTwo.lastIndexOf('-'));
        var TwoDay = DateTwo.substring(DateTwo.length, DateTwo.lastIndexOf('-') + 1);
        var TwoYear = DateTwo.substring(0, DateTwo.indexOf('-'));

        var cha = ((Date.parse(OneMonth + '/' + OneDay + '/' + OneYear) - Date.parse(TwoMonth + '/' + TwoDay + '/' + TwoYear)) / 86400000);
        return Math.abs(cha);
    }

    //+---------------------------------------------------
    //| 日期合法性验证
    //| 格式为：YYYY-MM-DD或YYYY/MM/DD
    //+---------------------------------------------------
    common.IsValidDate = function (DateStr) {
        var sDate = DateStr.replace(/(^\s+|\s+$)/g, ''); //去两边空格;
        if (sDate == '') return true;
        //如果格式满足YYYY-(/)MM-(/)DD或YYYY-(/)M-(/)DD或YYYY-(/)M-(/)D或YYYY-(/)MM-(/)D就替换为''
        //数据库中，合法日期可以是:YYYY-MM/DD(2003-3/21),数据库会自动转换为YYYY-MM-DD格式
        var s = sDate.replace(/[\d]{ 4,4 }[\-/]{ 1 }[\d]{ 1,2 }[\-/]{ 1 }[\d]{ 1,2 }/g, '');
        if (s == '') //说明格式满足YYYY-MM-DD或YYYY-M-DD或YYYY-M-D或YYYY-MM-D
        {
            var t = new Date(sDate.replace(/\-/g, '/'));
            var ar = sDate.split(/[-/:]/);
            if (ar[0] != t.getYear() || ar[1] != t.getMonth() + 1 || ar[2] != t.getDate()) {
                //alert('错误的日期格式！格式为：YYYY-MM-DD或YYYY/MM/DD。注意闰年。');
                return false;
            }
        }
        else {
            //alert('错误的日期格式！格式为：YYYY-MM-DD或YYYY/MM/DD。注意闰年。');
            return false;
        }
        return true;
    }

    //+---------------------------------------------------
    //| 日期时间检查
    //| 格式为：YYYY-MM-DD HH:MM:SS
    //+---------------------------------------------------
    common.CheckDateTime = function (str) {
        var reg = /^(\d+)-(\d{ 1,2 })-(\d{ 1,2 }) (\d{ 1,2 }):(\d{ 1,2 }):(\d{ 1,2 })$/;
        var r = str.match(reg);
        if (r == null) return false;
        r[2] = r[2] - 1;
        var d = new Date(r[1], r[2], r[3], r[4], r[5], r[6]);
        if (d.getFullYear() != r[1]) return false;
        if (d.getMonth() != r[2]) return false;
        if (d.getDate() != r[3]) return false;
        if (d.getHours() != r[4]) return false;
        if (d.getMinutes() != r[5]) return false;
        if (d.getSeconds() != r[6]) return false;
        return true;
    }

    //+---------------------------------------------------
    //| 字符串转成日期类型
    //| 格式 MM/dd/YYYY MM-dd-YYYY YYYY/MM/dd YYYY-MM-dd
    //+---------------------------------------------------
    common.StringToDate = function (DateStr) {
        var converted = Date.parse(DateStr);
        var myDate = new Date(converted);
        if (isNaN(myDate)) {
            //var delimCahar = DateStr.indexOf('/')!=-1?'/':'-';
            var arys = DateStr.split('-');
            myDate = new Date(arys[0], --arys[1], arys[2]);
        }
        return myDate;
    }

    common.replaceArrayAll = function (str, oldstrArray, newstr) {
        if (Array.isArray(oldstrArray)) {
            for (var i in oldstrArray) {
                try {
                    str = str.replaceAll(oldstrArray[i], newstr);
                } catch (e) {
                    //console.log(oldstrArray[i]);
                }
            }
        }
        return str;
    }
    //新手指引再次封装  相当给力
    common.newNoviceIntroSteps = function (obj, newIntro) {
        var isNewIntro = newIntro ? true : false;
        var newIntro = newIntro;
        var $steps = obj.steps;
        if ($steps instanceof Array || $steps.length > 0) {
            for (var i = 0; i < $steps.length; i++) {
                if ($steps[i].control.imgSrc.indexOf('/Content/Images/noviceIntroPic/') == -1) {
                    $steps[i].control.imgSrc = '/Content/Images/noviceIntroPic/' + $steps[i].control.imgSrc
                }
            }
        } else {
            $steps = [];
        }
        var noviceIntroObj = {
            backgroundColor: obj.backgroundColor ? obj.backgroundColor : "#000",
            opacity: obj.opacity ? obj.opacity : 0.7,
            isStartButton: obj.isStartButton ? obj.isStartButton : false,
            startButtonTitle: obj.startButtonTitle ? obj.startButtonTitle : '',
            startButtonTop: obj.startButtonTop ? obj.startButtonTitle : 0,
            startButtonLeft: obj.startButtonLeft ? obj.startButtonLeft : 0,
            callBack: typeof obj.callBack == "function" ? obj.callBack : function () {
                if (isNewIntro) {
                    commonModule.SaveCommonSetting(newIntro, "1", function (rsp) { });
                }
            },
            steps: $steps
        };
        if (isNewIntro) {
            commonModule.LoadCommonSetting(newIntro, true, function (rsp) {
                if (rsp.Success && rsp.Data != "1") {
                    var newFun = "newFun" + new Date().getTime();
                    var newFun = new noviceIntro();
                    newFun.initData(noviceIntroObj);

                }
            });
        }
    }

    //导航切换
    common.navActive = function (ele, callBack, active) {
        var active = active ? active : "active"
        $(ele).on("click", function (e) {
            if (e.stopPropagation) {
                e.stopPropagation();
                e.preventDefault();
            } else {
                window.event.returnValue = false;
                window.event.cancelBubble = true;
            };
            var tarEle = e.target;
            var parentEle = tarEle.parentNode ? tarEle.parentNode : null;
            var tarEleName = tarEle.nodeName.toLocaleUpperCase();
            var parentEleName = parentEle.nodeName.toLocaleUpperCase();
            if (tarEleName == "LI" || parentEleName == "LI") {
                if (tarEleName == "LI") {
                    $(ele + ">li").removeClass(active);
                    $(tarEle).addClass(active);
                } else if (parentEleName == "LI") {
                    $(ele + ">li").removeClass(active);
                    $(tarEle).parent().addClass(active);
                }
                $(ele + ">li").each(function (index, item) {
                    if ($(item).hasClass(active)) {
                        if (callBack || typeof callBack == "function") {
                            callBack(index, item)
                        }
                    }
                })
            }
        })
    }

    common.getQueryVariable = function (variable) {
        var query = window.location.search.substring(1);
        var vars = query.split("&");
        for (var i = 0; i < vars.length; i++) {
            var pair = vars[i].split("=");
            if (pair[0] == variable) { return pair[1]; }
        }
        return "";
    }


    //下拉选择器切换  
    common.cursorShowUl = function (ele, top) {
        $(ele).hover(function () {
            $(ele + ">ul").css({
                display: "block"
            }).stop().animate({
                top: top ? top : 30,
                opacity: '1'
            }, 250);
            // $(ele + ">ul").css({
            //     display: "block",
            //     top: top ? top : 30,
            //     opacity: '1'
            // })
                
            
        }, function () {
            $(ele + ">ul").stop().animate({
                top: top ? top + 10 : 60,
                opacity: '0'
            }, function () {
                $(ele + ">ul").hide();
            });
            // $(ele + ">ul").hide();
                
            
        })
    }

    //收缩显示
    var initSearchHeight = 0;
    common.showOrHideMoreSearch = function (isThis, tarEle, sHeight,className) {
        if ($(isThis).hasClass("zk")) {
            $(tarEle).css({ overflow: 'visible' }).animate({ height: initSearchHeight, }, 200);
            $(isThis).removeClass("zk").attr("title", "收起搜索条件");
            $(tarEle).css({ paddingBottom: 6 });

        } else {
            initSearchHeight = $(tarEle).height()+21;
            $(isThis).addClass("zk").attr("title", "展开搜索条件");
            $(tarEle).css({ overflow: 'hidden' }).animate({ height: sHeight, }, 200);
            $(tarEle).css({ paddingBottom: 0});
        }

        if (className) {
            $(tarEle).toggleClass(className);
        }


    }

    common.stopM = function () {
        if (event) {
            event.stopPropagation();
        } else {
            window.e.returnValue = false;
        };
    }

    common.PurchaseTabSwitch = function (type) {
        layer.msg("跳转中…", { time: 5000 });
        if (type == "1") {
            var href = commonModule.rewriteUrl('/Purchases/Index');
            href = commonModule.dbnameToAjaxUrl(href);
            location.href = href;
        } else if (type == "2") {
            var href = commonModule.rewriteUrl('/Purchases/PurchasesSet');
            href = commonModule.dbnameToAjaxUrl(href);
            location.href = href;//commonModule.rewriteUrl('/Purchases/PurchasesSet');
        } else {
            var href = commonModule.rewriteUrl('/Purchases/Index');
            href = commonModule.dbnameToAjaxUrl(href);
            location.href = href;//commonModule.rewriteUrl('/Purchases/Index');
        }
    }

    // 去重判断2个数组是否相等（顺序不影响）
    common.isArrayEqual = function (arr1, arr2) {
        if (!Array.isArray(arr1) || !Array.isArray(arr2))
            return false;
        return $(arr1).not(arr2).length === 0 && $(arr2).not(arr1).length === 0;
    }

    //加载过期时间
    common.LoadExpiredShop = function (data, callback) {
        common.Ajax({
            url: '/Partner/LoadMyInvalidShopList',
            data: data,
            loading: false,
            async: true,
            success: function (response) {
                if (typeof callback == 'function')
                    callback(response);
            }
        });
    }
    //商品显示大图
    var timerTableContentImgs = null;
    common.showLargeProPic = function (isThis) {

        timerTableContentImgs = setTimeout(function () {
            var productWrapHeight = 60;
            productWrapHeight = $(isThis).closest(".productShow").height() + 10 || 60;
            var positionFix = {};
            if (productWrapHeight < 200) {
                positionFix.bottom = productWrapHeight;
                positionFix.display = "flex";

            } else {
                positionFix.bottom = "unset";
                positionFix.marginTop = -500;
                positionFix.display = "flex";
            }
            var imgSrc = $(isThis).attr("src");
            var bigImg = "";
            if (imgSrc.substr(imgSrc.length - 10, 10) == ".80x80.jpg" && imgSrc.indexOf("http://cbu01.alicdn.com/") != -1) { //判断图片路径是否阿里的
                bigImg = imgSrc.substr(0, imgSrc.length - 9) + "jpg";
            } else {
                bigImg = imgSrc;
            }
            var productShowBigPic = $(isThis).next(".productShowBigPic").css(positionFix).append($(isThis).clone().attr("src", bigImg)).append($(isThis).nextAll(".product-title").clone());
        }, 800)
    }

    common.showLargeProPic02 = function (id) {
        var that = this;
        timerTableContentImgs = setTimeout(function () {
            var paOffsetLeft = $(that).closest(".new-productWrap").offset().left;
            var offsetTop = $(that).offset().top;
            var offsetLeft = $(that).offset().left;
            var scroll = $(document).scrollTop();
            var $width = $(that).width();
            var $height = $(that).closest(".new-productShow").height();

            var positionFix = {};
            positionFix.bottom = $height;
            positionFix.left = offsetLeft - paOffsetLeft + $width;
            positionFix.display = "flex";

            var imgSrc = $(that).attr("src");
            var bigImg = "";
            if (imgSrc.substr(imgSrc.length - 10, 10) == ".80x80.jpg" && imgSrc.indexOf("http://cbu01.alicdn.com/") != -1) {
                bigImg = imgSrc.substr(0, imgSrc.length - 9) + "jpg";
            } else {
                bigImg = imgSrc;
            }
            var productShowBigPic = $(that).next(".productShowBigPic").css(positionFix).append($(that).clone().attr("src", bigImg)).append($("#product-title-" + id).clone());
        }, 800)
    }
    var OrderCombine = null;
    common.getBaseProductSetting = function (callback) {
        commonModule.Ajax({
            url: '/api/BaseProduct/GetBaseProductSetting',
            type: 'GET',
            showMasker: false,
            success: function (rsp) {
                if (rsp.Success) {
                    OrderCombine = rsp.Data.OrderCombine;
                    callback(rsp.Data)
                }
            }
        })
    }
    common.showLargeProPic03 = function (isThis) {

        if (timerTableContentImgs) {
            hideLargeProPic02();
        }
       // timerTableContentImgs = setTimeout(function () { },100)
        //timerTableContentImgs = setTimeout(function () {
            var productWrapHeight = 60;
            var isrelationbaseproduct = $(isThis).attr('data-isrelationbaseproduct') || false;
            productWrapHeight = $(isThis).closest(".productShow").height() + 10 || 60;
            var positionFix = {};
            if (productWrapHeight < 200) {
                positionFix.bottom = productWrapHeight;
                positionFix.display = "flex";

            } else {
                positionFix.bottom = "unset";
                positionFix.marginTop = -500;
                positionFix.display = "flex";
            }
            var imgSrc = $(isThis).find(".productShow-img").attr("src");
            var bigImg = "";
            if (imgSrc.substr(imgSrc.length - 10, 10) == ".80x80.jpg" && imgSrc.indexOf("http://cbu01.alicdn.com/") != -1) { //判断图片路径是否阿里的
                bigImg = imgSrc.substr(0, imgSrc.length - 9) + "jpg";
            } else {
                bigImg = imgSrc;
            }
            var isHas = $(isThis).next(".productShowBigPic02").find('.productShow-img');
            if ((isrelationbaseproduct == 'false' || isrelationbaseproduct == '0' || !isrelationbaseproduct) && isHas.length == 0) {
                //$(isThis).next(".productShowBigPic02").css(positionFix).prepend($(isThis).clone().attr("src", bigImg));
                var imgBox = '<img class="productShow-img" src="' + bigImg + '" alt="">';
                $(isThis).next(".productShowBigPic02").css(positionFix).prepend(imgBox);
            } else {
                $(isThis).next(".productShowBigPic02").css(positionFix)
            }
            var $footerBtn = $(isThis).next(".productShowBigPic02").find('.productShowBigPic-footer-btn2');
            $($footerBtn).on('click', function (e) {
                e.stopPropagation();
                $($footerBtn).each(function (i, item) {

                    if ($(this).hasClass('active')) {
                        $(this).removeClass('active');
                    }
                    var ptPrduct = $(isThis).next(".productShowBigPic02").find('.product-title').eq(i);

                    var productShowImg = $(isThis).next(".productShowBigPic02").find('.productShow-img').eq(i);
                    if (e.target != item) {
                        ptPrduct.css({ display: 'none' })
                        productShowImg.css({ display: 'none' })
                    } else {
                        ptPrduct.css({ display: 'block' })
                        productShowImg.css({ display: 'block' })
                    }
                })
                $(this).addClass('active')
                //$(this).
            })
            if (OrderCombine === true) {
                $(isThis).next(".productShowBigPic02").find('.productShowBigPic-footer').css({ display: 'flex' });
                if (isrelationbaseproduct && isrelationbaseproduct != 'false') {
                    $(isThis).next(".productShowBigPic02").css({ background: '#f5f5f5' })
                } else {
                    $(isThis).next(".productShowBigPic02").css({ background: '#0888FF' })
                }
            } else {
                if (location.href.indexOf('/Product/Index') <= -1) {
                    $(isThis).next(".productShowBigPic02").find('.productShowBigPic-footer').hide();
                    $(isThis).next(".productShowBigPic02").css({ background: '#fff' })
                }
            }
           
            var hideBox = '<div class="hideBox" style="width:300px;height:100px;position: absolute;left: -20px;z-index: 11000;bottom:-90px;"></div>';
            $(isThis).next().append(hideBox);

            if (!window.showLargeProPic03isThis) {
                window.showLargeProPic03isThisDom = $(isThis).next();
                window.showLargeProPic03isThis = $(isThis).next(".productShowBigPic02");
                window.isrelationbaseproduct = isrelationbaseproduct;
                $(isThis).next(".productShowBigPic02").on('mouseleave', hideLargeProPic02)
                //document.addEventListener('click', hideLargeProPic02)
            }
            
       // }, 100)
    }
    common.hideLargeProPic = function (isThis) {
        $(isThis).next(".productShowBigPic").html("").css({ display: "none" });
        clearTimeout(timerTableContentImgs); //清除
    }
    var hideLargeProPic02 = function (e) {
        
        if (window.showLargeProPic03isThis) {
            var that = window.showLargeProPic03isThis[0];
            var thatNextDom = window.showLargeProPic03isThisDom[0];
            if (e && e.target && (!thatNextDom || !thatNextDom.contains(e.target) || !that.contains(e.target))) {
                return false
            }
            delete window.showLargeProPic03isThis;
            $(that).css({ display: "none" });
            var isrelationbaseproduct = window.isrelationbaseproduct;
            if (!isrelationbaseproduct || isrelationbaseproduct == 'false' || isrelationbaseproduct == '0') {
                $(that).find('.productShow-img').remove();
                $(that).find('.hideBox').remove();
            }
        }
        clearTimeout(timerTableContentImgs); //清除
        delete isrelationbaseproduct;
        //document.removeEventListener('click', hideLargeProPic02);
    }
    var dgjUrl_origin = "https://www.dgjapp.com/";
    //var dgjUrl_origin = "http://*********:30002/";

    //顶部和中间弹窗的显示
    common.showTopOrDailogAD = function (isNew) {
        var obj = {};
        obj.type = "showAD";
        obj.shopId = common.CurrShop.Id ? common.CurrShop.Id : "";
        obj.shopName = common.CurrShop.ShopId ? common.CurrShop.ShopId : "";

        var urlHost = location.hostname;
        var indexLocation = urlHost.indexOf(".dgjapp.com");
        var pt = $.cookie('fx_login_source');
        obj.platformType = pt ? pt : common.PlatformType;
        obj.releaseADVersion = common.IsReleaseVersion() ? 1 : 2;//判断环境 1正式  2灰度
        obj.releaseADVersionNum = urlHost.substr(0, indexLocation);
        $.ajax({
            url: dgjUrl_origin + 'distribution/distributionBanner',
            type: 'get',
            data: obj,
            dataType: 'json',
            success: function (data) {
                if (data.success) {
                    if (data.resultData.topBannerData) {
                        common.setStorage("topOrDailogADLocalStorage", data, 1);//数据保存在缓存1分钟
                    }
                    common.renderTopOrDailogADHtml(data, isNew);
                }
            }
        })
    }
    common.renderTopOrDailogADHtml = function (data, isNew) {  //渲染页面
        var topBannerData = data.resultData.topBannerData;
        var dailogBannerData = data.resultData.dailogBannerData;
        if (topBannerData) {  //获取顶部广告
            if (isNew) {
                $("#newtopBanner").show();
            }
            var userTel = $("#userInfo").children("s").text();  //用户名称
            var topBannerId = userTel + topBannerData.articleId;//用户名称+广告id

            let getTopBannerId=common.getStorage(topBannerId); //新加的
            if(getTopBannerId!=1){//新加的
                var adPics = topBannerData.adPics ? topBannerData.adPics : [];
                var thtml = "";
                if (topBannerData.adPics) {
                    thtml += '<div class="w-slider top-slider" id="top_js_slider">';
                    thtml += '<div class="slider">';
                    thtml += '<div class="slider-main" id="top_js_main_block">';
                    if (topBannerData.changeAdTypeName != 1) {
                        for (var i = 0; i < topBannerData.adPics.length; i++) {
                            thtml += '<div onclick=\'commonModule.addADClick("' + topBannerData.articleId + '","' + topBannerData.adPics[i].Id + '")\' class="slider-main-img" style="left: 0px;">';
                            thtml += '<a href="' + topBannerData.adPics[i].Url + '" target="_blank">';
                            thtml += '<img src="' + dgjUrl_origin + topBannerData.adPics[i].FileContent + '" alt="">';
                            thtml += '</a>';
                            thtml += '</div>';
                        }
                    } else {
                        thtml += '<a href="' + topBannerData.advertisementUrl + '"  target="_blank" onclick=\'commonModule.addADClick("' + topBannerData.articleId + '")\'>';
                        thtml += topBannerData.articlecontent ? topBannerData.articlecontent : "";
                        thtml += '</a>';
                    }
                    thtml += '</div>';
                    thtml += '</div>';
                    thtml += '<div class="slider-ctrl" id="top_slider_ctrl">';
                    thtml += '<span class="slider-ctrl-prev" style="display:none"></span>';
                    thtml += '<span class="slider-ctrl-next" style="display:none"></span>';
                    thtml += '</div>';
                    thtml += '</div>';

                    $("#top_layui_header").css("background-color", topBannerData.color01)
                    $(".icon-shanchu.header-shanchu").css({ color: topBannerData.color02 });
                    $("#header_right").css({ color: topBannerData.color03 });
                    if (!isNew) {

                        $("#layadmin_flexible").after(thtml);
                        if (!topBannerData.isShowCloseTopIcon || topBannerData.isShowCloseTopIcon == 1) {
                            $("#layadmin_flexible").append('<i onclick=\'commonModule.closeNewtopBanner("' + topBannerId + '")\' title="关闭顶部通知" style="color:' + topBannerData.color02 + '" class="iconfont icon-add-fill-hover-copy"></i>');
                        }

                    } else {

                        $("#newtopBanner").append(thtml);
                        if (!topBannerData.isShowCloseTopIcon || topBannerData.isShowCloseTopIcon == 1) {
                            $("#newtopBanner").append('<i onclick=\'commonModule.closeNewtopBanner("' + topBannerId + '")\' title="关闭顶部通知" style="color:' + topBannerData.color02 + '" class="iconfont icon-add-fill-hover-copy"></i>');
                        }
                        $(".newVersionsLeftNav .index-wrap").css({ paddingTop: "60px" });
                        $(".newVersionsLeftNav").addClass("hasTopBanner");
                    }

                    $("#top_layui_header");
                    if (topBannerData.adPics && topBannerData.adPics.length > 1 && topBannerData.changeAdTypeName == 0) {
                        common.slideshowAnimate("top_js_slider", "top_js_main_block", "top_slider_ctrl", 5000);
                    }
                }            
                } else {
                    if (isNew) {
                        $("#newtopBanner").hide();
                    }
            }


            // common.LoadCommonSetting(topBannerId, true, function (rsp) { //是否展示此广告
            //     if (rsp.Success && rsp.Data != "1") {
            //         var adPics = topBannerData.adPics ? topBannerData.adPics : [];
            //         var thtml = "";
            //         if (topBannerData.adPics) {
            //             thtml += '<div class="w-slider top-slider" id="top_js_slider">';
            //             thtml += '<div class="slider">';
            //             thtml += '<div class="slider-main" id="top_js_main_block">';
            //             if (topBannerData.changeAdTypeName != 1) {
            //                 for (var i = 0; i < topBannerData.adPics.length; i++) {
            //                     thtml += '<div onclick=\'commonModule.addADClick("' + topBannerData.articleId + '","' + topBannerData.adPics[i].Id + '")\' class="slider-main-img" style="left: 0px;">';
            //                     thtml += '<a href="' + topBannerData.adPics[i].Url + '" target="_blank">';
            //                     thtml += '<img src="' + dgjUrl_origin + topBannerData.adPics[i].FileContent + '" alt="">';
            //                     thtml += '</a>';
            //                     thtml += '</div>';
            //                 }
            //             } else {
            //                 thtml += '<a href="' + topBannerData.advertisementUrl + '"  target="_blank" onclick=\'commonModule.addADClick("' + topBannerData.articleId + '")\'>';
            //                 thtml += topBannerData.articlecontent ? topBannerData.articlecontent : "";
            //                 thtml += '</a>';
            //             }
            //             thtml += '</div>';
            //             thtml += '</div>';
            //             thtml += '<div class="slider-ctrl" id="top_slider_ctrl">';
            //             thtml += '<span class="slider-ctrl-prev" style="display:none"></span>';
            //             thtml += '<span class="slider-ctrl-next" style="display:none"></span>';
            //             thtml += '</div>';
            //             thtml += '</div>';

            //             $("#top_layui_header").css("background-color", topBannerData.color01)
            //             $(".icon-shanchu.header-shanchu").css({ color: topBannerData.color02 });
            //             $("#header_right").css({ color: topBannerData.color03 });
            //             if (!isNew) {

            //                 $("#layadmin_flexible").after(thtml);
            //                 if (!topBannerData.isShowCloseTopIcon || topBannerData.isShowCloseTopIcon == 1) {
            //                     $("#layadmin_flexible").append('<i onclick=\'commonModule.closeNewtopBanner("' + topBannerId + '")\' title="关闭顶部通知" style="color:' + topBannerData.color02 + '" class="iconfont icon-add-fill-hover-copy"></i>');
            //                 }

            //             } else {
            //                 $("#newtopBanner").show();
            //                 $("#newtopBanner").append(thtml);
            //                 if (!topBannerData.isShowCloseTopIcon || topBannerData.isShowCloseTopIcon == 1) {
            //                     $("#newtopBanner").append('<i onclick=\'commonModule.closeNewtopBanner("' + topBannerId + '")\' title="关闭顶部通知" style="color:' + topBannerData.color02 + '" class="iconfont icon-add-fill-hover-copy"></i>');
            //                 }

            //                 $(".newVersionsLeftNav .index-wrap").css({ paddingTop: "60px" });
            //                 $(".newVersionsLeftNav").addClass("hasTopBanner");
            //             }

            //             $("#top_layui_header");
            //             if (topBannerData.adPics && topBannerData.adPics.length > 1 && topBannerData.changeAdTypeName == 0) {
            //                 common.slideshowAnimate("top_js_slider", "top_js_main_block", "top_slider_ctrl", 5000);
            //             }
            //         }
            //     }
            // });
            
        }

        try { 
            if (dailogBannerData) {   //页面中间弹窗广告
                if (dailogBannerData.length) {
                    var promiseMidDailogArry = []; //解决异步任务  Promise.all调用
                    var pathnameStr = window.location.pathname;

                    for (var i = 0; i < dailogBannerData.length; i++) {
                        var dailogBannerObj = dailogBannerData[i];
                        var userTel = $("#userInfo").children("s").text();  //用户名称
                        var dailogBannerId = userTel + dailogBannerObj.articleId;//用户名称+广告id

                        var frequencyNum = dailogBannerObj.frequencyNum;

                        var p = new Promise(function (resolve, reject) { //用Promise 有异步任务 load
                            if (frequencyNum) { 
                                resolve(null);
                            } else {

                                let setDailogBannerData = common.getStorage(dailogBannerId) || null; //新加的
                                resolve(setDailogBannerData);
                           
                                //common.LoadCommonSetting(dailogBannerId, true, function (rsp) { //是否展示此广告
                                //    resolve(rsp.Data);
                                //});

                            }
                        })

                        promiseMidDailogArry.push(p);
                    }

                    Promise.all(promiseMidDailogArry).then(function (resultData) {
                        for (var i = 0; i < resultData.length; i++) {
                            if (resultData[i] == null) {  //没有关闭的弹窗广告
                                var dailogBannerId = userTel + dailogBannerData[i].articleId;//用户名称+广告id
                                var getStorageMidADObj = common.getStorage(midADObjName(dailogBannerId)) || {};
                                var nowDate = new Date().Format("yyyy-MM-dd");
        
                                var frequencyNum = dailogBannerData[i].frequencyNum;
                                if (frequencyNum) {
                                    if (JSON.stringify(getStorageMidADObj) == "{}") {
                                        showMidDailog(dailogBannerData[i]);
                                        break;

                                    } else {
                                        if (nowDate != getStorageMidADObj.date) {
                                            showMidDailog(dailogBannerData[i]);
                                            break;

                                        } else {
                                            if (frequencyNum > getStorageMidADObj.clickNum) {
                                                showMidDailog(dailogBannerData[i]);
                                                break;

                                            }
                                        }
                                    }

                                } else {
                                    showMidDailog(dailogBannerData[i]);
                                    break;

                                }

                            }
                        }
                    })
                }

                function showMidDailog(dailogBannerData) {
                    var fendanWebs = dailogBannerData.fendanWebs;
                    var dailogBannerId = userTel + dailogBannerData.articleId;//用户名称+广告id
                    var aHtml = "";
                    if (dailogBannerData.changeAdTypeName == 1) {
                        var picUrl = dgjUrl_origin + dailogBannerData.articleUrl.replace(/\\/g, "/");
                        var advertisementUrl02 = dailogBannerData.advertisementUrl ? dailogBannerData.advertisementUrl : "#";
                        aHtml = '<a onclick=\'commonModule.addADClick("' + dailogBannerData.articleId + '")\'  href=' + advertisementUrl02 + ' target="_blank">';
                        aHtml += dailogBannerData.articlecontent;
                        aHtml += '</a>';
                    } else {
                        if (dailogBannerData.adPics.length > 1) {
                            aHtml += '<div class="w-slider dailog-slider" id="dailog_slider">';
                            aHtml += '<div class="slider">';
                            aHtml += '<div class="slider-main" id="dailog_main_block">';
                            for (var i = 0; i < dailogBannerData.adPics.length; i++) {
                                aHtml += '<div onclick=\'commonModule.addADClick("' + dailogBannerData.articleId + '","' + dailogBannerData.adPics[i].Id + '")\' class="slider-main-img">';
                                aHtml += '<a href="' + dailogBannerData.adPics[i].Url + '" target="_blank">';
                                aHtml += '<img src="' + dgjUrl_origin + dailogBannerData.adPics[i].FileContent + '" alt="">';
                                aHtml += '</a>';
                                aHtml += '</div>';
                            }
                            aHtml += '</div>';
                            aHtml += '<div class="slider-ctrl" id="dailog_slider_ctrl">';
                            aHtml += '<span class="slider-ctrl-prev"></span>';
                            aHtml += '<span class="slider-ctrl-next"></span>';
                            aHtml += '</div>';
                            aHtml += '</div>';
                            aHtml += '</div>';
                        } else {
                            aHtml = '<a onclick=\'commonModule.addADClick("' + dailogBannerData.articleId + '","' + dailogBannerData.adPics[0].Id + '")\' href=' + dailogBannerData.adPics[0].Url + ' target="_blank"><img style="width:600px;height:400px" src=' + dgjUrl_origin + dailogBannerData.adPics[0].FileContent + ' /></a>';
                        }
                    }

                    if (fendanWebs != "" || fendanWebs != null) {
                        if (fendanWebs.indexOf(pathnameStr) != -1) {
                            layer.open({
                                type: 1,
                                title: false,
                                shadeClose: false,
                                skin:'showMidAvDailog',
                                area: ['600px', '400px'],
                                content: aHtml,
                                success: function () {
                                    if (dailogBannerData.adPics.length > 1) {
                                        common.slideshowAnimate("dailog_slider", "dailog_main_block", "dailog_slider_ctrl", 2000);
                                    }

                                },
                                cancel: function () {

                                    //common.SaveCommonSetting(dailogBannerId, "1", function (rsp) { }); //关闭后不展示此广告
                                    common.setStorage(dailogBannerId, "1");

                                    var getStorageMidADObj = common.getStorage(midADObjName(dailogBannerId)) || {};
                                    var obj = {};
                                    if (JSON.stringify(getStorageMidADObj) == "{}") {
                                        obj.clickNum = 1;
                                        obj.date = new Date().Format("yyyy-MM-dd");
                                    } else {
                                        var nowDate = new Date().Format("yyyy-MM-dd");
                                        if (getStorageMidADObj.date == nowDate) {
                                            obj.clickNum = getStorageMidADObj.clickNum + 1;
                                            obj.date = nowDate;
                                        } else {
                                            obj.clickNum =1;
                                            obj.date = nowDate;
                                        }
                                    }
                                    common.setStorage(midADObjName(dailogBannerId), obj)

                                }
                            });
                        }
                    }
                }

                function midADObjName(dailogBannerId) {
                    if (!dailogBannerId) {
                        return "";
                    }
                    return "storageMidAD" + dailogBannerId;
                }
            }

        } catch (err) {}

    }
    common.closeNewtopBanner = function (topBannerId) {//关闭后不展示此广告

        common.setStorage(topBannerId,"1");
        $("#layadmin_flexible").remove();
        $("#newtopBanner").hide().html("");
        $(".newVersionsLeftNav").removeClass("hasTopBanner");

        // common.SaveCommonSetting(topBannerId, "1", function (rsp) {
        //     $("#layadmin_flexible").remove();
        //     $("#newtopBanner").hide().html("");
        //     $(".newVersionsLeftNav").removeClass("hasTopBanner");

        // }); 
    }

    //轮播图动画 slider为最外层盒子id  mainBlock:为内容区盒id   sliderCtrl:为控制图片切换id  speed:为速度
    common.slideshowAnimate = function (slider, mainBlock, sliderCtrl, speed) {
        function $$(id) {
            return document.getElementById(id)
        }
        var js_slider = $$(slider);
        var js_main_block = $$(mainBlock);
        var imgs = js_main_block.children;
        var slider_ctrl = $$(sliderCtrl);

        for (var i = 0; i < imgs.length; i++) {
            var span = document.createElement("span");
            span.innerHTML = (imgs.length - i); //作用数字从小到大
            span.className = "slider-ctrl-con";
            slider_ctrl.insertBefore(span, slider_ctrl.children[1]);
        }

        var spans = slider_ctrl.children; //得到所有的span

        spans[1].setAttribute("class", "slider-ctrl-con current");

        var scrollWidth = js_slider.clientWidth; //得到大盒子的宽度  也就是 后面动画要走的距离  310

        // 开始走
        // 刚开始，按第一张图留下  其它的人
        for (var i = 1; i < imgs.length; i++) {
            imgs[i].style.left = scrollWidth + "px"

        }
        //spans是8个按钮
        var iNow = 0; //用来控制播放张数的  当前索引号
        for (var k in spans) { //k是索引号   spans[k]获取元素
            spans[k].onclick = function () {
                if (this.className == "slider-ctrl-prev") { //点击左箭头

                    animate(imgs[iNow], {
                        left: scrollWidth
                    }) //当前那个图片 走到scrollWidth位置

                    iNow--; //当前的下一张

                    if (iNow < 0) {

                        iNow = imgs.length - 1;

                    }

                    imgs[iNow].style.left = -scrollWidth + "px"; //先走到：当前的下一张先快速走到右侧 和下面同一张

                    animate(imgs[iNow], {
                        left: 0
                    }) //再走到：   当前那个图片 走到scrollWidth位置

                    setSquare()

                } else if (this.className == "slider-ctrl-next") { //点击右箭头


                    //iNow=0
                    animate(imgs[iNow], {
                        left: -scrollWidth
                    }) //当前那个图片 走到scrollWidth位置

                    iNow++; //当前的下一张

                    if (iNow > imgs.length - 1) {
                        iNow = 0;

                    }

                    imgs[iNow].style.left = scrollWidth + "px"; //先走到：当前的下一张先快速走到右侧 和下面同一张

                    animate(imgs[iNow], {
                        left: 0
                    }) //再走到：   当前那个图片 走到scrollWidth位置

                    setSquare()

                } else { //点击下面几个小圆点

                    var that = this.innerHTML - 1;

                    if (that > iNow) {
                        animate(imgs[iNow], {
                            left: -scrollWidth
                        });

                        imgs[that].style.left = scrollWidth + "px";

                        animate(imgs[that], {
                            left: 0
                        });

                    } else if (that < iNow) {

                        animate(imgs[iNow], {
                            left: scrollWidth
                        });

                        imgs[that].style.left = -scrollWidth + "px";

                        animate(imgs[that], {
                            left: 0
                        });

                    }

                    iNow = that; //给当前的索引号
                    animate(imgs[iNow], {
                        left: 0
                    });
                    setSquare()
                }
            }
        }

        function setSquare() { //小横线着色

            //清除所有的span current 的样式  留下满足需要的

            for (var i = 1; i < spans.length - 1; i++) { //为什么要取i=1和i<spans.length-1开始，因为span比图片多出两个，有两个span是控制左右点击的

                spans[i].className = "slider-ctrl-con"

            }
            spans[iNow + 1].className = "slider-ctrl-con current"

        }


        //自动播放  定时器开始

        var timer = null;
        timer = setInterval(autoplay, speed) //和右侧按钮功能一样


        function autoplay() {
            //iNow=0
            animate(imgs[iNow], {
                left: -scrollWidth
            }) //当前那个图片 走到scrollWidth位置

            iNow++; //当前的下一张

            if (iNow > imgs.length - 1) {
                iNow = 0;

            }

            imgs[iNow].style.left = scrollWidth + "px"; //先走到：当前的下一张先快速走到右侧 和下面同一张

            animate(imgs[iNow], {
                left: 0
            }) //再走到：   当前那个图片 走到scrollWidth位置

            setSquare()

        }

        //清除自动播放定时器

        js_slider.onmouseover = function () {
            clearInterval(timer)
        }

        js_slider.onmouseout = function () {
            clearInterval(timer)
            timer = setInterval(autoplay, 4000)
        }

    }
    //顶部和中间弹窗的显示
    common.showStaticTopOrDailogAD = function () {
        var data = {
            "resultData": {
                "topBannerData": {
                    "articleId": "1637293581684",
                    "advertisementUrl": "https://docs.qq.com/doc/DQ3JTTUlzQ1NGWFZI",
                    "articleUrl": "https://img.dgjapp.com/fendan/letter-top.png?t=20210223",
                    "articletitle": "给用户的一封信"
                },
                "dailogBannerData": {
                    "articleId": "1637293655485",
                    "advertisementUrl": "https://docs.qq.com/doc/DQ3JTTUlzQ1NGWFZI",
                    "articleUrl": "https://img.dgjapp.com/fendan/letter-dialog.png",
                    "articletitle": "给用户的一封信"
                }
            }
        };
        var topBannerData = data.resultData.topBannerData;
        var dailogBannerData = data.resultData.dailogBannerData;

        if (topBannerData) {  //获取顶部广告

            var userTel = $("#userInfo").children("s").text();  //用户名称
            var topBannerId = userTel + topBannerData.articleId;//用户名称+广告id
            common.LoadCommonSetting(topBannerId, true, function (rsp) { //是否展示此广告
                if (rsp.Success && rsp.Data != "1") {
                    var articleUrl = topBannerData.articleUrl.replace(/\\/g, "/");
                    var imgSrc = 'url("' + articleUrl + '")';
                    $("#header_shanchu_ad").css({ display: "block" });
                    $("#layui_header").css("background-color", topBannerData.color01)
                    $("#layui_header").css("background-image", imgSrc);
                    $(".icon-shanchu.header-shanchu").css({ color: topBannerData.color02 });
                    $("#header_right").css({ color: topBannerData.color03 });
                    var advertisementUrl = topBannerData.advertisementUrl ? topBannerData.advertisementUrl : "#";
                    var aHtml = advertisementUrl != "#" ? $('<a onclick=\'commonModule.addADClick("' + topBannerData.articleId + '")\' id="header_top_pic">').attr("href", advertisementUrl).attr("target", "_blank").css({ flex: 1 }) : "";

                    $("#layadmin_flexible").append(aHtml);
                    $("#header_shanchu_ad").on("click", function () {
                        $("#layui_header").css("background-color", "")
                        $("#layui_header").css("background-image", "");
                        $(".icon-shanchu.header-shanchu").css("color", "");
                        $("#header_right").css("color", "");
                        $("#header_top_pic").remove();
                        $(this).css("display", "none");
                        common.SaveCommonSetting(topBannerId, "1", function (rsp) { }); //关闭后不展示此广告
                    })

                }
            });

        }
        if (dailogBannerData) {   //页面中间弹窗广告

            var userTel = $("#userInfo").children("s").text();  //用户名称
            var dailogBannerId = userTel + dailogBannerData.articleId;//用户名称+广告id
            common.LoadCommonSetting(dailogBannerId, true, function (rsp) { //是否展示此广告
                if (rsp.Success && rsp.Data != "1") {
                    var picUrl = dailogBannerData.articleUrl.replace(/\\/g, "/");
                    var advertisementUrl02 = dailogBannerData.advertisementUrl ? dailogBannerData.advertisementUrl : "#";
                    var aHtml = advertisementUrl02 != "#" ? ' <a onclick=\'commonModule.addADClick("' + dailogBannerData.articleId + '")\' href=' + advertisementUrl02 + ' target="_blank"><img style="width:100%;height:100%" src=' + picUrl + ' /></a>' : '<a onclick=\'commonModule.addADClick("' + dailogBannerData.articleId + '")\' href="javascript:;" ><img style="width:600px;height:400px" src=' + picUrl + ' /></a>'
                    layer.open({
                        type: 1,
                        title: false,
                        shadeClose: false,
                        area: ['440px', '400px'],
                        content: aHtml,
                        cancel: function () {
                            common.SaveCommonSetting(dailogBannerId, "1", function (rsp) { }); //关闭后不展示此广告
                        }
                    });
                }
            });
        }
    }

    //监听广告点击量
    common.addADClick = function (id, picId) {
        var obj = {};
        obj.articleId = id;
        obj.type = "addADClick";
        obj.picId = picId ? picId : "";
        $.ajax({
            url: dgjUrl_origin + 'distribution/distributionBanner',
            type: 'get',
            data: obj,
            dataType: 'json',
            success: function (data) { }
        })
    }
    ////functionName有三种值：shop店铺,supplier厂家,seller商家
    ////付费订购提示
    //common.noAvailableCountDialog = function (functionName) {
    //    var countInfo = common.AvailableCountInfo;
    //    var countString = "";
    //    if (countInfo && countInfo.AvailableCount)
    //        countString = "（" + countInfo.AvailableCount + "）";

    //    layer.alert('您的【' + functionName + '】数量已达上限，请联系我们升级版本');
    //}

    common.InitUserVersionInfo = function () {
        //common.getJSON("/Partner/GetUserVersionInfo", function (rsp) {
        //    common.UserVersionInfo = {};
        //    if (rsp.Data != null) {
        //        common.UserVersionInfo = rsp.Data;
        //        //初始化页面
        //        var endTime = common.UserVersionInfo.ServiceEndTimeString;
        //        var curLevel = common.UserVersionInfo.Level;
        //        var levelIcon = "";
        //        if (curLevel == 0 || curLevel == -1)
        //            levelIcon = "v0";
        //        if (curLevel == 1)
        //            levelIcon = "v1";
        //        if (curLevel == 2)
        //            levelIcon = "v2";
        //        if (curLevel == 3)
        //            levelIcon = "v3";
        //        if (curLevel == 4)
        //            levelIcon = "v4";
        //        if (curLevel > 4)
        //            levelIcon = "v4";

        //        var levelInfo = '<div class="level-iconWrap ' + levelIcon + '"><span class="level-iconWrap-left"></span><span class="level-iconWrap-right">' + common.UserVersionInfo.VersionName + '</span></div><span class="dateShow">' + endTime + '</span>';
        //        if (curLevel <= 0)
        //            levelInfo = '<div class="level-iconWrap ' + levelIcon + '"><span class="level-iconWrap-left"></span><span class="level-iconWrap-right">' + common.UserVersionInfo.VersionName + '</span></div>';
        //        $("#level-explain").html(levelInfo);

        //    }
        //});
    }

    common.contactOpenOrClose = function (isTrue) {
        if (isTrue) {
            $("#commonContactDailog").css({ display: "flex", "z-index": 29991015 })
        } else {
            $("#commonContactDailog").fadeOut(300)
        }

    }

    //续费的弹窗
    var newRenewDailogObj = null;
    common.latformRenewUrl = function (platformType, shopName, url, v, appName) {
        // 权限校验
        commonModule.FxPermission(function (p) {
            commonModule.CheckPermission(function (success) {
                if (success) {
                    thisFunc();
                }
                else return;
            }, p.RenewalShop);
        });

        var thisFunc = function () {
            if (!v || v == undefined)
                v = (commonModule.IsNewCorp && platformType == "WxVideo") ? "-v2" : "";

            newRenewDailogObj = layer.open({
                type: 1,
                title: "续费提醒",
                content: $('#newRenew_dialog_03'),
                area: ['1090', '700'], //宽高
                closebtn: true,
                skin: "wu-dailog renewal-reminder-dailog",
                shade: 0,
                offset: '100px',
                success: function () {
                    $(".renewal-reminder-dailog .layui-layer-content").css({
                        height: 'auto',
                        maxHeight: 'unset',
                        padding: '0px',
                        borderRadius: '0 0 10px 10px'
                    });
                    var imgSrc = " url(/Content/Images/renew/" + platformType + v + "-xufei.png)";

                    if (appName == "店管家_分销代发") {
                        imgSrc = " url(/Content/Images/renew/" + platformType + "_new.png)";
                    }
                    if (appName == "中恒分销代发_订单代发") {
                        imgSrc = " url(/Content/Images/renew/" + platformType + "_old.png)";
                    }

                    if (platformType == "Jingdong") {
                        //京东使用新的续费图片
                        imgSrc = " url(/Content/Images/renew/" + platformType + "New-xufei.png)";
                    }
                    if (platformType == "JingdongPurchase") {
                        //京东供销平台使用京东的续费图片
                        imgSrc = " url(/Content/Images/renew/JingdongNew-xufei.png)";
                    }
                    if (platformType == "togglePinduoduo") {
                        $(".newRenew-header-down").addClass('togglePinduoduoWarn').html('<span style="color:#f29a1a">订购提示:因配合平台调整，店管家分销代发暂不支持续费订购，请您订购店管家打单进行店铺授权。</span>');
                    }

                    //$("#Renew_dialog_02 .renewShopName").text(shopName);
                    //$("#renew_orderUrl_02").attr("href", url);
                    //$("#Renew_dialog_content_02").removeClass().addClass(platformType).addClass("Renew-dialog-content").css({ backgroundImage: imgSrc })
                    $("#newRenew_dialog_03 .newRenew-header-shopName").text(shopName);
                    $("#newRenew_dialog_03 .newRenew-name").text(shopName);
                    $("#newRenew_dialog_03 .newRenew-main").removeClass().addClass(platformType).addClass("newRenew-main").css({ backgroundImage: imgSrc })
                    $("#newRenew_dialog_03 #renew_orderUrl_03").attr("href", url);
                    var pathname = window.location.pathname;
                    if (pathname == '/Partner/Index') {
                        $("#newRenew_dialog_03 #renew_orderUrl_03").on("click", function () {
                            var isAddOk = layer.confirm('是否已完成续费?已完成,请重新授权以便更新服务时间。', { icon: 3, title: '确认', btn: ['确定', '取消'], skin: 'wu-dailog' },
                                function () {
                                    location.href = location.href;
                                }, function () {
                                    location.href = location.href;
                                }
                            );
                        });
                    }
                   

                }
            });
        }
    }
    //续费的弹窗,抖音(头条)专用的
    common.latformRenewUrlTouTiao = function (platformType, shopName, url, OldOrNew, IsFxListingApp) {
        // 权限校验
        commonModule.FxPermission(function (p) {
            commonModule.CheckPermission(function (success) {
                if (success) {
                    thisFunc();
                }
                else return;
            }, p.RenewalShop);
        });
        var thisFunc = function () {
            if (OldOrNew != undefined && OldOrNew != "null" && OldOrNew == "Old") {
                layer.open({
                    type: 1,
                    title: false, //不显示标题
                    content: '<div style="width:900px;height:383px"><span style="width: 247px;height: 34px;position: absolute;bottom: 73px;left: 463px;cursor:pointer;" onclick="commonModule.latformRenewUrlTouTiaoAA(\'' + url + '\')"></span><img src="/Content/Images/renew/toutiaoXufeiTip.gif"></div>',
                    area: '900', //宽高
                    success: function () {

                    },
                    yes: function () {
                        common.OpenNewTab(url);
                    }
                });
            } else {
                var newRenewIndex = layer.open({
                    type: 1,
                    title: "续费提醒", //不显示标题
                    content: $('#newRenew_dialog_03'),
                    area: ['1090', '700'], //宽高
                    closebtn: true,
                    skin: "wu-dailog renewal-reminder-dailog",
                    offset: '100px',
                    success: function (index) {
                        $(".renewal-reminder-dailog .layui-layer-content").css({
                            height: 'auto',
                            maxHeight: 'unset',
                            padding: '0px',
                            borderRadius: '0 0 10px 10px'
                        });
                        var imgSrc = "";
                        if (IsFxListingApp) {
                            imgSrc = " url(/Content/Images/renew/" + platformType + "-distribution-xufei.jpg)";
                        } else {
                            imgSrc = " url(/Content/Images/renew/" + platformType + "-xufei.png)";
                            if (OldOrNew != undefined && OldOrNew != "null") {
                                imgSrc = " url(/Content/Images/renew/" + platformType + OldOrNew + "-xufei.png)";
                            }
                        }
                        //$("#Renew_dialog_02 .renewShopName").text(shopName);
                        //$("#renew_orderUrl_02").attr("href", url);
                        //$("#Renew_dialog_content_02").removeClass().addClass(platformType).addClass("Renew-dialog-content").css({ backgroundImage: imgSrc })
                        $("#newRenew_dialog_03 .newRenew-header-shopName").text(shopName);
                        $("#newRenew_dialog_03 .newRenew-name").text(shopName);
                        $("#newRenew_dialog_03 .newRenew-main").removeClass().addClass(platformType).addClass("newRenew-main").css({ backgroundImage: imgSrc })
                        $("#newRenew_dialog_03 #renew_orderUrl_03").attr("href", url);
                        $("#newRenew_dialog_03 #renew_orderUrl_03").off();
                        var pathname = window.location.pathname;
                        if (pathname == '/Partner/Index') {
                            $("#newRenew_dialog_03 #renew_orderUrl_03").on("click", function () {
                                var isAddOk = layer.confirm('是否已完成续费?已完成,请重新授权以便更新服务时间。', { icon: 3, title: '确认', btn: ['确定', '取消'], skin: 'wu-dailog' },
                                    function () {
                                        location.href = location.href;
                                    }, function () {
                                        location.href = location.href;
                                    }
                                );
                            });
                        }
                        if (pathname == '/BaseProduct/PrepareDistribution') {
                            $("#newRenew_dialog_03 #renew_orderUrl_03").on("click", function () {
                                $("#newRenew_dialog_03").hide();
                                layer.close(newRenewIndex);
                                var html = '<div class="c06 f14" style="padding:16px;">完成授权后，即可选择该店铺进行铺货。</div>';
                                var authorizeIndex =  layer.open({
                                    type: 1,
                                    title: '是否已完成店铺授权？', //不显示标题
                                    content: html,
                                    area: '560px', //宽高
                                    skin: 'n-skin',
                                    success: function () { },
                                    shade: false,
                                    zIndex: 1000000,
                                    btn: ['未完成', '已完成'],
                                    btn2: function () {
                                        window.location.href = window.location.href;
                                    },
                                    cancel: function (index) {
                                        layer.close(authorizeIndex);
                                    }
                                });
                            });
                        }
                    }
                });
            }
        }
        
        
    }

    //续费的弹窗,抖音(头条)专用的2
    common.latformRenewUrlTouTiaoAA = function (url) {
        // 权限校验
        commonModule.FxPermission(function (p) {
            commonModule.CheckPermission(function (success) {
                if (success) {
                    thisFunc();
                }
                else return;
            }, p.RenewalShop);
        });

        var thisFunc = function () {
            common.OpenNewTab(url);
        }
    }



    //判断订单标签中是否包含某一个标签
    common.HasTag = function (tags, tagName, tagType) {
        var hasTag = false;
        if (tags && tags.length > 0) {
            for (var i = 0; i < tags.length; i++) {
                var tag = tags[i];
                if (tag.Tag == tagName && tag.TagType == tagType) {
                    hasTag = true;
                    break;
                }
            }
        }
        return hasTag;
    };

    common.HasTagValue = function (tags, tagName, tagValue, tagType) {
        var hasTag = false;
        if (tags && tags.length > 0) {
            for (var i = 0; i < tags.length; i++) {
                var tag = tags[i];
                if (tag.Tag == tagName && tag.TagValue == tagValue && tag.TagType == tagType) {
                    hasTag = true;
                    break;
                }
            }
        }
        return hasTag;
    }

    common.HasTagOiCode = function (tags, tagOiCode, tagName, tagType) {
        var hasTag = false;
        if (tags && tags.length > 0) {
            for (var i = 0; i < tags.length; i++) {
                var tag = tags[i];
                if (tag.OiCode == tagOiCode && tag.Tag == tagName && tag.TagType == tagType) {
                    hasTag = true;
                    break;
                }
            }
        }
        return hasTag;
    }
    common.FirstTagValue = function (tags, tagName, tagType) {
        var TagValue = "";
        if (tags && tags.length > 0) {
            for (var i = 0; i < tags.length; i++) {
                var tag = tags[i];
                if (tag.Tag == tagName && tag.TagType == tagType) {
                    return tag.TagValue
                }
            }
        }
        return TagValue;
    }

    var _firstTagValue = function (tags, tagName, tagType) {
        var TagValue = "";
        if (tags && tags.length > 0) {
            for (var i = 0; i < tags.length; i++) {
                var tag = tags[i];
                if (tag.Tag == tagName && tag.TagType == tagType) {
                    return tag.TagValue
                }
            }
        }
        return TagValue;
    }

    common.FirstTagValue = function (tags, tagName, tagType, oiCode) {
        if (oiCode==null||oiCode==undefined||oiCode=="") return _firstTagValue(tags, tagName, tagType);
        var TagValue = "";
        if (tags && tags.length > 0) {
            for (var i = 0; i < tags.length; i++) {
                var tag = tags[i];
                if (tag.Tag == tagName && tag.TagType == tagType && tag.OiCode == oiCode) {
                    return tag.TagValue
                }
            }
        }
        return TagValue;
    }

    //获取币种字符
    common.getCurrencyToCharacter = function (val) {
        var currstr = "";
        var currencySymbols = {
            'VND': '₫', // 越南盾
            'MYR': 'RM', // 马来西亚林吉特
            'SGD': 'S$', // 新加坡元
            'PHP': '₱', // 菲律宾比索
            'THB': '฿', // 泰铢
            'GBP': '£', // 英镑
            'USD': '$', // 美元
            'CNY': '¥',  // 人民币
            'MXN': 'MX$',  // 墨西哥
            'BRL': 'R$',  // 巴西
        };
        currstr = currencySymbols[val];
        return currstr;
    }

    common.getStorage = function (storageName) {  //获取localStorage历史记录，storageName为保存历史记录名称，有数据时返回值是一个对象，无数据返回空
        var historyListJson = localStorage.getItem(storageName) || null;
        if (historyListJson) {
            var historyData = JSON.parse(historyListJson);
            var endTime = historyData.endTime ? historyData.endTime : 0;
            var nowTime = new Date().getTime();
            if (endTime > 0) {
                if (endTime < nowTime) {  //保存时间和现在对比，小于时主题已过期
                    localStorage.removeItem(storageName);
                    return '';
                } else {
                    return historyData.data;
                }
            } else { //0时为没有设置时间，表示长期有交
                return historyData.data;
            }
        } else {
            return '';
        }
    };
    common.setStorage = function (storageName, value, time) {  //storageName保存记录的名称，value为保存值   time为分钟
        var obj = {};
        if (time) {
            var d = new Date();
            var st = 1000 * 60 * parseInt(time);
            obj.endTime = d.getTime() + st;//获取结束毫秒值
        } else { //endTime为0时，表示长期保存  上面getStorage方法有做处理
            obj.endTime = 0;
        }
        obj.data = value;
        localStorage.setItem(storageName, JSON.stringify(obj));
    };
    common.clearStorage = function (storageName) {  //storageName有名称删除指定名字，没有设置，清空所有
        if (storageName) {
            localStorage.removeItem(storageName);
        } else {
            localStorage.clear();
        }
    };

    common.changeVersions = function (v) {
        common.Ajax({
            url: '/Common/ChangeVersion',
            data: { isAuto: v },
            loading: true,
            loadingMessage: '切换中..',
            success: function (rsp) {
                if (v) {
                    common.JsLogToMongoDB("导航主题_切换新版按钮点击", v);
                } else {
                    common.JsLogToMongoDB("导航主题_切换旧版按钮点击", v);
                }
                window.localStorage.setItem("isNewAndOldVersionNav", v);
                if (rsp && rsp.Success) {
                    console.log(rsp.Data);
                    location.reload();
                }
            }
        });
    }


    common.RenameExpressName = function (expressName) {

        var newName = expressName;
        if (expressName.indexOf("邮政") != -1)
            newName = "邮政";
        //else if (expressName.indexOf("顺丰") != -1)
        //    newName = "";
        else if (expressName.indexOf("京东快递") != -1)
            newName = "京东快递单号查询";
        else if (expressName.indexOf("京东物流") != -1)
            newName = "京东物流";
        else if (expressName.indexOf("极兔") != -1 && expressName.indexOf("百世") != -1)
            newName = "极兔快递";
        return newName;
    }

    //绑定厂家-搜索框
    //elementId:元素Id,需要绑定元素Id
    //Suppliers :后端返回商家数组
    //公共绑定厂家搜索框,需要使用公共就直接使用    
    common.initSupplierSelectBox = function (elementId, Suppliers, selectAll) {
        var selectboxArr = [];
        for (var i = 0; i < Suppliers.length; i++) {
            var obj = {};
            obj.Value = Suppliers[i].FxUserId;
            obj.Text = Suppliers[i].UserName;
            if (Suppliers[i].IsTop != undefined && Suppliers[i].IsTop) {
                obj.Text = '<i class="iconfont icon-zhuangtai zhidingIcon"></i>' + Suppliers[i].UserName;
            }
            //只取IsFilter=false
            if (Suppliers[i].IsFilter == undefined || Suppliers[i].IsFilter == false) {
                selectboxArr.push(obj);
            }
        }

        var selectData = [];
        if (selectAll != undefined && selectAll == "1") {
            selectData = selectboxArr
        }

        var selectInit = {
            eles: '#' + elementId,
            emptyTitle: '全部厂家', //设置没有选择属性时，出现的标题
            data: selectboxArr,
            searchType: 1, //1出现搜索框，不设置不出现搜索框
            showWidth: '250px', //显示下拉的宽
            isRadio: false, //有设置，下拉框改为单选
            allSelect: true,
            selectData: selectData  //初始化数据
        };

        var selectBox = new selectBoxModule2();
        selectBox.initData(selectInit);
    }

    //绑定商家-搜索框
    //elementId:元素Id,需要绑定元素Id
    //Suppliers :后端返回商家数组
    //公共绑定厂家搜索框,需要使用公共就直接使用  
    common.initAgentSelectBox = function (elementId, Suppliers, selectAll, emptyTitle) {
        var selectboxArr = [];
        for (var i = 0; i < Suppliers.length; i++) {
            var obj = {};
            obj.Value = Suppliers[i].FxUserId;
            obj.Text = Suppliers[i].UserName;
            if (Suppliers[i].IsTop != undefined && Suppliers[i].IsTop) {
                obj.Text = '<i class="iconfont icon-zhuangtai zhidingIcon"></i>' + Suppliers[i].UserName;
            }
            selectboxArr.push(obj);
        }

        var selectData = [];
        if (selectAll != undefined && selectAll == "1") {
            selectData = selectboxArr
        }

        var selectInit = {
            eles: '#' + elementId,
            emptyTitle: emptyTitle || '全部商家', //设置没有选择属性时，出现的标题
            data: selectboxArr,
            searchType: 1, //1出现搜索框，不设置不出现搜索框
            showWidth: '250px', //显示下拉的宽
            isRadio: false, //有设置，下拉框改为单选
            allSelect: true,
            selectData: selectData,  //初始化数据
        };

        var selectBox = new selectBoxModule2();
        selectBox.initData(selectInit);
    }
    //判断发布环境  返回值为true为正式环境，false为灰度
    common.IsReleaseVersion = function () {
        var IsReleaseVersion = false;
        if (common.CurrShop.Version == null || common.CurrShop.Version == '' || common.CurrShop.Version == undefined) {
            IsReleaseVersion = true;
        }
        return IsReleaseVersion;
    }

    // 账号合并-绑定手机号弹窗
    common.openBindMobilePhoneDailog = function () {
        layer.open({
            type: 1,
            title: '<div class="user_register_login_title">绑定手机号</div>',
            content: $(".userBindMobilePhoneDailog"),
            area: '480px',
            maxHeight: 800,
            skin: 'wu-dailog bindMobilePhoneDailog',
            offset: '180px',
            success: function () {
                // 触发点击事件
                $("#showMobileCodeBtn").off('click');
                $("#showMobileCodeBtn").click();
            },
            cancel: function () {
                layer.closeAll();
                $("#showMobileCodeBtn").off('click');
            },
            btn: false
        });
    }

    common.CheckVirtualRegMobile = function () {
        var curShopId = commonModule.CurrShop.ShopId;
        //var curShopId = 'KuaiShou_1682058211';
        if (commonModule.isPhone(curShopId)) {
            // $(".SRcotainer").hide();
            layer.closeAll();
            return true;
        }
        else {
            // 显示注册
            //$(".SRcotainer").css({ display: "flex" });
            //$("#spannewaccount").click();
            //layer.msg("请您先完成系统注册", { time: 5000 });
            commonModule.openBindMobilePhoneDailog();
            // 相关按钮点击提示
            $(".CheckVirtualRegMobile").removeAttr("onclick");
            $(".CheckVirtualRegMobile").on("click", function () {
                // layer.msg("请您先完成系统注册", { time: 5000 });
                commonModule.openBindMobilePhoneDailog();
                return false;
            });
            return false;
        }
    }

    common.CheckVirtualRegMobileSubPage = function () {
        var curShopId = commonModule.CurrShop.ShopId;
        //var curShopId = 'KuaiShou_1682058211';
        if (commonModule.isPhone(curShopId)) {
            try {
                // $(window.parent.document).find(".SRcotainer").hide();
                layer.closeAll();
            }
            catch (e) {
                //向父级发送消息
                window.parent.postMessage({ isphone: '1' }, '*');
            }
            return true;
        }
        else {
            // 显示注册      
            try {
                //$(window.parent.document).find(".SRcotainer").css({ display: "flex" });
                //$(window.parent.document).find("#spannewaccount").click();
                commonModule.openBindMobilePhoneDailog();
            }
            catch (e) {
                //向父级发送消息
                window.parent.postMessage({ isphone: '0' }, '*');
            }
            //layer.msg("请您先完成系统注册", { time: 5000 });
            // 相关按钮点击提示            
            $(".CheckVirtualRegMobile").removeAttr("onclick");
            $(".CheckVirtualRegMobile").on("click", function () {
                // layer.msg("请您先完成系统注册", { time: 5000 });
                commonModule.openBindMobilePhoneDailog();
                return false;
            });
            return false;
        }
    }

    common.isShowStopTelWarn = function (isTrue) {
        var html = '<span class="shuidiDailog">只允许查询线下单</span>';
        if (isTrue) {
            $(this).after($(html));
        } else {
            $(this).nextAll(".shuidiDailog").remove();
        }
    }

    var showTouTiaoChangeCommonPlatformDailog = null;
    common.showTouTiaoChangeCommonPlatformDailog = function (type) {
        var html = '';
        html += '<div class="changePlatformWarn">';
        html += '<span class="changePlatformWarn-icon" onclick="commonModule.closeTouTiaoChangeCommonPlatformWarn()"></span>';
        html += '<span class="changePlatformWarn-icon" style="left:763px;" onclick="commonModule.closeTouTiaoChangeCommonPlatformWarn()"></span>';
        html += '<span class="changePlatformWarn-icon-01"  onclick="commonModule.closeTouTiaoChangeCommonPlatformWarn()"></span>';
        html += '<img src="/Content/images/noviceIntroPic/ssqhtz-2023-1-9-03.png" />'
        html += '</div>';
        showTouTiaoChangeCommonPlatformDailog = layer.open({
            type: 1,
            title: false, //不显示标题
            content: html,
            area: '900px', //宽高
            closeBtn: 0
        });
    }
    common.closeTouTiaoChangeCommonPlatformWarn = function () {
        layer.close(showTouTiaoChangeCommonPlatformDailog);
    }

    common.GetQueryString = function (name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]); return null;
    }


    //投诉 & 需求
    var edmitDemandDailog = null;
    common.edmitDemand = function () {
        $("#demand_tel").val("");
        $("#demand_content").val("");
        $("#demand_submitSuggest").val("");
        $("#edmitDemandDailog_type>li").removeClass("active");
        edmitDemandDailog = layer.open({
            type: 1,
            title: false,
            area: '650px',
            content: $("#edmitDemandDailog"),
            skin: 'newFXDailog'

        });
    }
    // 通知列表
    _noticeData = {
        TotalCount: 2,
        PageIndex: 1,
        PageSize: 20,
        Messages: [
            {
                /*
                id: 1,
                title: '商品',
                time: '2024/3/6 09:10:00',
                cont: '您的商家[商家id]给您推送了[数量]款商品，快去将商家推送过来的商品绑定您的货源吧。',
                status: 0, // 0未读，1已读
                */

                // 消息实际数据结构如下
                MessageCode: "666",
                MessageId: 722,
                Content: "我是消息内容 通过MessageTemplate+Message.TemplateParas生成",
                CreateTime: new Date().toTimeString(), // datetime
                FromUserId: 0, // 来源用户ID，为0表示系统消息
                ToUserId: 5, // 接收人用户ID，为0表示系统消息
                FirstType: "Order",
                SecondType: "二级类型",
                CloudPlatformType: "Alibaba",
                Tag: "订单",
                IsRead:false  // 是否已读
            },
            {
                // 消息实际数据结构如下
                MessageCode: "776",
                MessageId: 182,
                Content: "我是消息内容 通过MessageTemplate+Message.TemplateParas生成",
                CreateTime: new Date().toTimeString(), // datetime
                FromUserId: 0, // 来源用户ID，为0表示系统消息
                ToUserId: 5, // 接收人用户ID，为0表示系统消息
                FirstType: "Product",
                SecondType: "二级类型",
                CloudPlatformType: "Alibaba",
                Tag: "产品",
                IsRead: false  // 是否已读
            },
            {
                // 消息实际数据结构如下
                MessageCode: "711116",
                MessageId: 482,
                Content: "我是消息内容 通过MessageTemplate+Message.TemplateParas生成",
                CreateTime: new Date().toTimeString(), // datetime
                FromUserId: 0, // 来源用户ID，为0表示系统消息
                ToUserId: 5, // 接收人用户ID，为0表示系统消息
                FirstType: "Recommend",
                SecondType: "二级类型",
                CloudPlatformType: "Alibaba",
                Tag: "推荐",
                IsRead: false  // 是否已读
            },
            {
                // 消息实际数据结构如下
                MessageCode: "22226",
                MessageId: 13332,
                Content: "我是消息内容 通过MessageTemplate+Message.TemplateParas生成",
                CreateTime: new Date().toTimeString(), // datetime
                FromUserId: 0, // 来源用户ID，为0表示系统消息
                ToUserId: 5, // 接收人用户ID，为0表示系统消息
                FirstType: "Recommend",
                SecondType: "二级类型",
                CloudPlatformType: "Alibaba",
                Tag: "推荐",
                IsRead: false  // 是否已读
            },
        ]
    }
    
    // 获取通知列表
    common.notice = function () {
        noticeData = {};
        var mesage = {
            //Source : 0,
            //UserId: 5,
            //FirstType: "",
            PageIndex: 1,
            PageSize: 500,
            ReadStatus: 2 //未读
        };
        common.ajax({
            url: '/SiteMessage/GetMessageList',
            data: mesage,
            loading: true,
            type: 'POST',
            success: function (rsp) {
                if (rsp.Success && rsp.Data.IsSucc) {
                    _noticeData = rsp.Data;
                    noticeClassified(_noticeData.Messages, "Recommend");
                    var dialog = $.templates("#notice-dailog-tmpl");
                    //var html = dialog.render({ noticeData: noticeData, noticeItems: noticeData["Recommend"].messages });
                    var html = dialog.render({ noticeData: noticeData, noticeItems: noticeData["Recommend"]});
                    $('#noticePopUp').show();
                    $('#noticePopUp .noticePopUp-main').html(html);
                    /*noticeDailog = layer.open({
                        type: 1,
                        title: '通知中心',
                        content: html,
                        skin: 'newFXDailog',
                        id: 'notice-dailog',
                        shadeClose: true,
                        anim: -1,
                        type: 1,
                        scrollbar: false,
                        area: ['650px', '100%'],
                        offset: 'r'
                    });*/
                } else {
                    layer.msg(rsp.Message, { zIndex: 100000001 });
                }
            },
            error: function (rsp) {
                if (rsp.status == 401) {
                    layer.msg("暂无权限，请联系管理员", { zIndex: 100000001 });
                } else {
                    layer.msg(rsp.message, { zIndex: 100000001 });
                }
            }
        });  
    }

    // 接口获取通知按一级类型划分
    var noticeClassified = function (notices,type) {
        
        _noticeFirstType.forEach(function (ftype) {
            if (!noticeData[ftype]) {
                noticeData[ftype] = {
                    messages: [],
                    count: 0
                }
            }

        });

        if (!notices || notices.length < 1)
            return ;

        notices.forEach(function (notice) {

            //var mcode = notice.MessageCode;
            //var exits = false;

            /*noticeData[type].messages.forEach(function (mes) {
                if (mcode == mes.MessageCode) {
                    exits = true;
                    return 'break';
                }
            });
            if (!exits) {
                noticeData[type].messages.push(notice);
                noticeData[type].count++;
            }*/
            notice.TypeText = _noticeTypeTextDic[notice.SecondType];
            var paramsDic = {};
            try {
                var params = JSON.parse(notice.TemplateParas);
                //console.log(params);
                var lastKey = null;
                for (var key in params) {
                    if (params.hasOwnProperty(key)) {
                        var value = params[key];
                        if (!lastKey) {
                            paramsDic[value] = null;
                            lastKey = value;
                        } else {
                            paramsDic[lastKey] = value;
                            lastKey = null;
                        }
                    }
                }
                //console.log(paramsDic);
            } catch (error) {
                //console.error("无法将 JSON 字符串转换为对象:", error);
            }

            for (var key in paramsDic) {
                if (paramsDic.hasOwnProperty(key)) {
                    var value = paramsDic[key];
                    if (value) {
                        notice.Content = notice.Content.replace(new RegExp(key, "g"), value);
                    }
                }
            }

        });
        noticeData[type].messages = notices;
        noticeData[type].count = notices.length;
        //console.log(notices);
        return ;
    }

    // 切换通知栏目
    common.chooseNoticeTab = function (div) {
        var type = $(div).attr("data-type");
        if (noticeData && noticeData[type]) {
            noticeData[type] = {
                messages: [],
                count: 0
            };
        }
        var mesage = {
            //Source : 0,
            //UserId: 5,
            FirstType: type=="Recommend"? null : type,
            PageIndex: 1,
            PageSize: 500,
            ReadStatus: type == "Recommend" ? 2 : 0
        };
        common.ajax({
            url: '/SiteMessage/GetMessageList',
            data: mesage,
            loading: true,
            type: 'POST',
            success: function (rsp) {
                if (rsp.Success && rsp.Data.IsSucc) {
                    _noticeData = rsp.Data;
                    if (_noticeData.TotalCount > 0) {
                        noticeClassified(_noticeData.Messages, type);
                    }
                    var count = 0;
                    if (noticeData[type].messages && noticeData[type].messages.length) {
                        noticeData[type].messages.forEach(function (mes) {
                            if (mes.IsRead != true) {
                                count++;
                            }
                        });
                    }
                    var span = $(".layui-tab-title .layui-this").find("span:first");
                    if (count > 0) {
                        var countText = count.toString();
                        if (count > 99) {
                            countText = "99+";
                        }

                        if (span.length > 0) {
                            span.text(countText);
                            span.show();
                        } else {       
                            $(div).append("<span>" + countText + "</span>");
                        }
                    } else if (span.length > 0) {
                        span.hide();
                    }
                    var noticeTmpl = $.templates("#notice-items-tmpl");
                    var html = noticeTmpl.render({ noticeItems: noticeData[type] });
                    $("#noticeContent").html(html);
                } else {
                    layer.msg(rsp.Message, { zIndex: 1000000000 });
                }
            },
            error: function (rsp) {
                if (rsp.status == 401) {
                    layer.msg("暂无权限，请联系管理员", { zIndex: 1000000000});
                } else {
                    layer.msg(rsp.message, { zIndex: 1000000000 });
                }
            }
        });
    }                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           

    // 查看通知
    common.noticeLook = function (code, FirstType) {
        // 点击查看设为已读
        common.readedMark(code);
        // 跳转
        var tab = $(".noticeDailog-nav .layui-this");
        var type = tab.attr("data-type");
        var url = null;
        noticeData[type].messages.forEach(function (mes) {
            if (mes.MessageCode === code) {
                url = mes.JumpUrl;
                return 'break';
            }
        });
        if (FirstType === 'Public') {
            window.open(url, '_blank');
        } else {
            window.open(commonModule.rewriteUrl(url), '_blank');
        }
       
    }
    // 关闭通知 
    common.noticePopUp = function () {
        $('#noticePopUp').hide()
    }
    // 单条已读
    common.readedMark = function (code) {
        var codes = [];
        codes.push(code);
        common.allMark(undefined, codes);
    }

    // 全部已读
    common.allMark = function (type, codes) {
        
        var readedCodes = [];
        var isAll = false;
        var firstType = undefined;
        if (codes && codes.length > 0) { // 仅根据MessageCode设置已读 -- 单条已读
            readedCodes = codes;
        } else { // 根据FirstType设置已读  --批量设置
            if (!type) {
                type = $(".noticeDailog-nav .layui-this").attr("data-type");
            }
            if (type == "Recommend") { // 推荐列表已读 用codes
                noticeData[type].messages.forEach(function (mes) {
                    readedCodes.push(mes.MessageCode)
                });
            }else 
                firstType = type;
        }
        
        var mesage = {
            // Source: 0, // 0分单 1打单 2铺货
            //UserId: 5, // 用户ID，对应Message.ToUserId
            MessagesCodes: readedCodes,
            MessageFirstTypeName: firstType,
            IsAll:isAll
        }
        //console.log(mesage);
        common.ajax({
            url: "/SiteMessage/SetMessageReaded",
            data: mesage ,
            loadingMessage: "提交中。。",
            type:'POST',
            success: function (rsp) {
                //console.log(rsp);
                if (rsp.Success && rsp.Data.IsSucc) {
                    var successCodes = rsp.Data.SuccessCode;
                    var tab = $(".noticeDailog-nav .layui-this");
                    var type = tab.attr("data-type");

                    //console.log(noticeData[type]);

                    var newList = [];
                    var spanCount = 0;
                    if (type === "Recommend") {
                        // 推荐列表 全部已读后清除数据
                        newList = noticeData[type].messages.filter(function (m) {
                            return successCodes.indexOf(m.MessageCode) === -1;
                        });
                        noticeData[type].messages = newList;
                        spanCount = noticeData[type].count = newList.length;
                    }
                    else if (type) {
                        //console.log(type);
                        // 其他列表或单条已读 全部已读后清除未读标志
                        noticeData[type].messages.forEach(function (m) {
                            if (successCodes.indexOf(m.MessageCode) !== -1) {
                                m.IsRead = true;
                            }
                            if(!m.IsRead) spanCount++;
                        });
                    }
                    //console.log(spanCount);
                    var span = tab.find("span:first");
                    if (span.length) {
                        if (spanCount > 0) {
                            if(spanCount>99)
                                span.text("99+");
                            else
                                span.text(spanCount);
                            span.show();
                        } else {
                            span.text("");
                            span.hide();
                        }
                        
                    }

                    var noticeTmpl = $.templates("#notice-items-tmpl");
                    var html = noticeTmpl.render({ noticeItems: noticeData[type] });
                    $("#noticeContent").html(html);

                    // 消息按钮更新未读
                    common.getUnReadCount();
                    common.chooseNoticeTab(tab);
                } else {
                    layer.msg(rsp.Message, { zIndex: 1000000000 });
                    console.log(rsp);
                }
                
            },
            error: function (rsp) {
                if (rsp.status == 401) {
                    layer.msg("暂无权限，请联系管理员", { zIndex: 1000000000 });
                } else {
                    layer.msg(rsp.message, { zIndex: 1000000000 });
                }
            }
        });
    }

    // 未读消息数
    common.getUnReadCount = function () {
        common.FxPermission(function (p) {
            common.CheckPermission(function (success) {
                if (success) {
                    thisFunc();
                }
                else return;
            }, p.SiteMessage,null,true);
        });

        var thisFunc = function () {
            var mesage = {
            };
            common.ajax({
                url: '/SiteMessage/GetUnreadCount',
                data: mesage,
                loading: false,
                type: 'POST',
                success: function (rsp) {
                    //console.log(rsp);
                    if (rsp.Success && rsp.Data.IsSucc) {
                        var count = rsp.Data.TotalUnreadCount;

                        if (count > 0) {
                            if (count > 99) {
                                $('.notice-unreadcount').text("99+");
                            } else {
                                $('.notice-unreadcount').text(count);
                            }

                            $('.notice-unreadcount').show();
                        } else {
                            $('.notice-unreadcount').hide();
                        }


                    } else {
                        console.warn(rsp.Message, { zIndex: 1000000000 });
                    }
                },
                error: function (rsp) {
                    if (rsp.status == 401) {
                        layer.msg("暂无权限，请联系管理员", { zIndex: 1000000000 });
                    } else {
                        layer.msg(rsp.message, { zIndex: 1000000000 });
                    }
                }
            });
        }
    }

    subscribeData = {
        "IsEnabled": true,
        "MessageTypeSwitch": [
            {
                "MessageTypeName": "Public",
                "MessageSubTypeName": undefined,
                "IsEnabled":true
            },
            {
                "MessageTypeName": "Order",
                "MessageSubTypeName": "ExceptionOrder",
                "IsEnabled": true
            },
            {
                "MessageTypeName": "Order",
                "MessageSubTypeName": "AfterSaleNotification",
                "IsEnabled": true
            },
            {
                "MessageTypeName": "Order",
                "MessageSubTypeName": "Shipments",
                "IsEnabled": true
            },
            {
                "MessageTypeName": "Bill",
                "MessageSubTypeName": undefined,
                "IsEnabled": true
            },
            {
                "MessageTypeName": "Cooperation",
                "MessageSubTypeName": undefined,
                "IsEnabled": true
            },
            {
                "MessageTypeName": "Product",
                "MessageSubTypeName": undefined,
                "IsEnabled": true
            },
            {
                "MessageTypeName": "SkuChanged",
                "MessageSubTypeName": null,
                "IsEnabled": true
            }

        ]
    };

    var subscribeDataConvert = function (data) {
        var result = JSON.parse(JSON.stringify(data["MessageTypeSwitch"]));;

        result.forEach(function (o) {
            switch (o["FirstType"]) {
                case "Public":
                    o["FirstTypeText"] = "公告";
                    o["Description"] = "系统公告通知";
                    break;
                case "Order":
                    o["FirstTypeText"] = "订单";
                    o["Description"] = "订单下单、推单等通知";
                    break;
                case "Product":
                    o["FirstTypeText"] = "商品";
                    o["Description"] = "商品推送、绑定成功等通知";
                    break;
                case "Bill":
                    o["FirstTypeText"] = "账单";
                    o["Description"] = "账单相关通知";
                    break;
                case "Cooperation":
                    o["FirstTypeText"] = "合作";
                    o["Description"] = "分销关系建立、取消等通知";
                    break;
                default:
                    break;
            }

            switch (o["SecondType"]) {
                case "AfterSaleNotification":
                    o["SecondTypeText"] = "售后通知";
                    o["Description"] = "未发货仅退款、已发货售后相关通知";
                    break;
                case "ExceptionOrder":
                    o["SecondTypeText"] = "异常订单";
                    o["Description"] = "异常订单相关通知";
                    break;

                default:
                    break;
            }
        });

        function groupBy(array, key) {
            return array.reduce(function (result, currentValue) {
                (result[currentValue[key]] = result[currentValue[key]] || []).push(currentValue);
                return result;
            }, {});
        }

        result = groupBy(result, "FirstType");
        var list = [];
        for (var key in result) {
            list.push({
                title: result[key][0]["FirstTypeText"],
                data: result[key]
            })
        }
        return list;
    };

    // 订阅设置弹窗
    common.subscribe = function () {
        common.ajax({
            url: '/Common/LoadMessageSetting',
            type: 'GET',
            loading: true,
            success: function (rsp) {
                if (rsp.Success) {
                    subscribeData = JSON.parse(rsp.Data);
                    console.log(subscribeData);

                    var tmp = $.templates("#subscribe-dailog-tmpl");
                    var html = tmp.render({ subscribeData: subscribeDataConvert(subscribeData) });
                    noticeDailog = layer.open({
                        type: 1,
                        title: '消息订阅设置',
                        area: '650px',
                        content: html,
                        skin: 'newFXDailog',
                        id: 'subscribe-dailog',
                        shadeClose: true,
                        zIndex: 100000002,
                    });
                } else {
                    layer.msg("系统繁忙，请稍后再试", { zIndex: 100200000 });
                    console.log('/Common/LoadMessageSetting 接口出错：'+rsp.Message);
                }
            },
            error: function () {
                layer.msg("系统繁忙，请稍后再试", { zIndex: 100200000 });
            }
        });
        
    }

    // 订阅功能
    common.subscribeStatus = function (firstType, secondType) {

        var newSetting = subscribeData["MessageTypeSwitch"];
        var isEnable = true;
        for (var i = 0; i < newSetting.length; i++) {
            var o = newSetting[i];
            if (secondType) {
                if (o.SecondType && o.SecondType === secondType) {
                    isEnable = o.IsEnabled = !o.IsEnabled;
                    break;
                }
            } else if (firstType) {
                if (o.FirstType && !o.SecondType && o.FirstType === firstType) {
                    isEnable = o.IsEnabled = !o.IsEnabled;
                    break;
                }
            }
        }
        //layer.msg("订阅成功");
        //subscribeData["MessageTypeSwitch"] = newSetting;
        //common.subscribeRender();
        //subscribeData.MessageTypeSwitch = null;
        common.ajax({
            url: "/Common/SaveMessageSetting",
            data: { settingValue: JSON.stringify(subscribeData) },
            loading: true,
            type: 'POST',
            success: function (rsp) {
                if (rsp.Success) {
                    if(isEnable){
                        layer.msg("订阅成功", { zIndex: 100200000 });
                    } else {
                        layer.msg("取消订阅成功", { zIndex: 100200000 });
                    }
                    subscribeData["MessageTypeSwitch"] = newSetting;
                    common.subscribeRender();
                } else {
                    layer.msg(rsp.Message, { zIndex: 100200000 });
                    //console.log('/Common/SaveMessageSetting 接口出错：' + rsp.Message);
                }
            },
            error: function () {
                layer.msg("系统繁忙，请稍后再试", { zIndex: 100200000 });
            }
        });
    }

    common.subscribeRender = function () {
        var tmp = $.templates("#subscribe-dailog-tmpl");
        var html = tmp.render({ subscribeData: subscribeDataConvert(subscribeData) });
        $('#subscribe-dailog').html(html);
    }

    common.submitSuggest = function () {
        var demandTel = $("#demand_tel").val().trim();
        var demandContent = $("#demand_content").val().trim();
        var demandSubmitSuggest = $("#demand_submitSuggest").val().trim();
        if (!demandTel) {
            layer.msg("请输入您的联系方式");
            return;
        }
        if (!checkPhone(demandTel)) {
            layer.msg("请输入您的正确联系方式");
            return;
        }
        //if (!demandContent && !demandSubmitSuggest) {
        //    layer.msg("请输入业务问题或建议");
        //    return;
        //}
        if (demandContent.length >= 800 || demandSubmitSuggest.length >= 800) {
            layer.msg("请输入内容为800字以内");
            return;
        }
        if ($("#edmitDemandDailog_type>li.active").length == 0) {
            layer.msg("请选择类型");
            return;
        }
        var userContent02 = '';
        $("#edmitDemandDailog_type>li.active").each(function (item) {
            userContent02 += $(this).html() + '-';
        })
        userContent02 = userContent02.substr(0, userContent02.length - 1);

        var userInfo = $("#userInfo s").text();
        var obj = {};
        obj.systemType = "fenxiao";
        obj.userInfo = userInfo;
        obj.userContact = demandTel;
        obj.userContent = demandContent;
        obj.userContent01 = demandSubmitSuggest;
        obj.userContent02 = userContent02;
        obj.currShop = common.CurrShop;
        obj.id = userInfo + "-" + new Date().getTime();

        $.ajax('https://www.dgjapp.com/distribution/distributionSuggest', {
            data: obj,
            dataType: 'json', //服务器返回json格式数据
            type: 'post', //HTTP请求类型
            success: function (result) {
                if (result.success) {
                    var title = '<div>留言成功，感谢您的反馈，我们非常需要您真诚的建议<br/>店管家有幸能与您共同进步</div>'
                    layer.msg(title, { icon: 1, time: 5000, skin: 'edmitDemandDailogSkin' });
                    layer.close(edmitDemandDailog);
                }
            },
            error: function () {

            }
        });


        function checkPhone(val) {  //匹配手机号，正确返回true 错误返回false
            if (!(/^1(3|4|5|7|8|9)\d{9}$/.test(val))) {
                return false;
            }
            return true;
        }

    }

    //删除字符串特殊字符
    common.DelSpecialChar = function (data) {
        if (!data) return '';
        return data.replace(/[<>&\'\"\,\\\/\b\f\n\r\t]/g, '');
    }

    //限制提示
    common.LimitTimeDailg = function (type, hopeTime) {
        var html = '<div class="checkMigrateDailog" style="width:600px">';
        html += '<div class="checkMigrate-main">';
        html += '<div class="checkMigrate-main-text"  style="padding-top:5px;max-height:unset">';
        if (type == "settlement") {
            //对账结算弹框文案
            html += '<span style="font-size:20px;font-weight:700;text-align:center;margin:10px 0 20px 0;color:#000;">出账任务排队提醒</span>';
            html += '<span style="font-size:16px;color:#666">因大促期间订单量激增，为保证系统正常使用，</span>';
            html += '<span style="font-size:16px;color:#666">您的出账账单将逾期至<b style="color:#3aadff;">' + hopeTime + '</b>执行出账任务，</span>';
            html += '<span style="font-size:16px;color:#666">已为您安排出账任务排队，请您耐心等待完成再下载查看账单等操作</span>';
        }
        else if (type == "export") {
            //其他导出弹框文案
            html += '<span style="font-size:20px;font-weight:700;text-align:center;margin:10px 0 20px 0;color:#000;">导出任务排队提醒</span>';
            html += '<span style="font-size:16px;color:#666">因大促期间订单量激增，为保证系统正常使用，</span>';
            html += '<span style="font-size:16px;color:#666">您的导出任务将逾期至<b style="color:#3aadff;">' + hopeTime + '</b>执行，</span>';
            html += '<span style="font-size:16px;color:#666">已为您安排导出任务排队，请您耐心等待完成再下载表格操作</span>';
        }
        else {
            html += '<span style="font-size:20px;font-weight:700;text-align:center;margin:10px 0 20px 0;color:#000;">当前功能暂停使用说明</span>';
            html += '<span style="font-size:16px;color:#666">因大促期间订单量激增，为保证系统正常使用，</span>';
            html += '<span style="font-size:16px;color:#666">当前订单状态限制该查询条件操作，大促结束后，将恢复正常使用</span>';
        }
        html += '<span style="font-size:16px;color:#666">很抱歉影响您的正常使用，非常感谢您的理解与配合。</span>';
        html += '</div>';
        html += '</div>'
        html += '</div>'

        var checkDyYunWarnDailg02 = layer.open({
            type: 1,
            title: false, //不显示标题
            content: html,
            area: 600, //宽高
            skin: 'adialog-Shops-skin',
            btn: false,
        });
    }

    //限制提示
    common.PageDepthControlDailg = function (tipStr) {
        var html = '<div class="checkMigrateDailog" style="width:348px">';
        html += '<div class="checkMigrate-main">';
        html += '<div class="checkMigrate-main-text"  style="padding-top:5px;max-height:unset">';
        html += '<span style="font-size:16px;color:#666">' + tipStr + '</span>';
        html += '</div>';
        html += '</div>'
        html += '</div>'
        var checkDyYunWarnDailg02 = layer.open({
            type: 1,
            title: false, //不显示标题
            content: html,
            area: 400, //宽高
            skin: 'adialog-Shops-skin',
            btn: false,
        });
    }
    //公共 点击出现水没弹窗
    common.clickPopover = function () {
        event.stopPropagation();
        $(this).children(".popoverCommon-warn").css({ display: "block" });
        var that = this;
        $(document).on("click", function () {
            $(that).children(".popoverCommon-warn").css({ display: "none" });
        })
    }

    common.showMySelect02 = function (tarThis) {
        event.stopPropagation();
        $(tarThis).closest(".my-select-02-inputWrap").addClass("active");

        $(document).click('click', function () {
            $(tarThis).closest(".my-select-02-inputWrap").removeClass("active");
            $(tarThis).closest(".my-select-02-inputWrap").find(".my-select-02-expressTemplateWrap>li").removeClass("hide");
            $(tarThis).closest(".my-select-02-inputWrap").find(".my-select-02-inputWrap-input").val("");

        });
    }

    common.showMySelect02_input = function (tarThis) {
        var $val = $(tarThis).val().trim().toLocaleUpperCase();
        var $lis = $(tarThis).closest(".my-select-02-inputWrap").find(".my-select-02-expressTemplateWrap>li");
        if ($val) {
            $lis.each(function (i, item) {
                var expressTemplateWrapTitle = $(item).find(".my-select-02-expressTemplateWrap-title").text().toLocaleUpperCase();
                if (expressTemplateWrapTitle.indexOf($val) == -1) {
                    $(item).addClass("hide");
                } else {
                    $(item).removeClass("hide");

                }
            })
        } else {
            $lis.removeClass("hide");
        }
    }

    //返回平台对应小logo
    common.showPintaiIcon = function (pt) {
        var iconHtml = '';
        if (pt != "other") {
            iconHtml += '<i class="wu-pintaiIcon wu-small ' + pt + '"></i>';
        }
        return iconHtml;
    }

    //清除历史多余的模板类型文案，显示小图标
    common.ReplaceOldTemplateName = function (oldName) {
        if (oldName) {
            oldName = oldName.replace("【菜鸟】", "");
            oldName = oldName.replace("【菜鸟官方】", "");
            oldName = oldName.replace("【菜鸟快运】", "");
            oldName = oldName.replace("【拼多多】", "");
            oldName = oldName.replace("【无界(云打印)】", "");
            oldName = oldName.replace("【京东(云打印)】", "");
            oldName = oldName.replace("【抖店(云打印)】", "");
            oldName = oldName.replace("【快手】", "");
            oldName = oldName.replace("【京东】", "");
            oldName = oldName.replace("【京东无界】", "");
            oldName = oldName.replace("【抖店】", "");
            oldName = oldName.replace("【抖店快运】", "");
            oldName = oldName.replace("【小红书】", "");
            oldName = oldName.replace("【视频号】", "");
        }
        return oldName;
    }
    common.moveRightDiv = function (elemId) {   //移动div  定位在右侧的
        if (!elemId) return;

        var ele = document.getElementById(elemId);
        if (ele != undefined) {
            ele.onmousedown = function (event) {
                var event = event || window.event;
                var y = event.clientY;  //记录当前盒子的y位置
                var x = event.clientX;

                var right = ele.style.right ? parseInt(ele.style.right) : 0;
                var top = ele.style.top ? parseInt(ele.style.top) : 0;
                document.onmousemove = function (event) {
                    event.preventDefault();
                    ele.onclick = null;
                    window.getSelection().removeAllRanges();//禁止文字被选中的问题 
                    var event = event || window.event;

                    var moveRight = right + (x - event.clientX);
                    var moveTop = top - (y - event.clientY);

                    ele.style.right = moveRight + "px";
                    ele.style.top = moveTop + "px";
                }
                document.onmouseup = function () {  //鼠标谈起，不操作
                    document.onmousemove = null;

                }
            }
        }
    }

    //跨境 发往国家
    common.LoadToCountry = function () {
        return [
            { Value: 'VN', Text: '越南' }, 
            { Value: 'TH', Text: '泰国' }, 
            { Value: 'SG', Text: '新加坡' }, 
            { Value: 'MY', Text: '马来西亚' }, 
            { Value: 'PH', Text: '菲律宾' },
            { Value: 'ID', Text: '印度尼西亚' },
            { Value: 'UK', Text: '英国' }, 
            { Value: 'US', Text: '美国' },
            { Value: 'MX', Text: '墨西哥' },
            { Value: 'BR', Text: '巴西' }
        ]
    }

    // 公共弹窗数据
    // type 0：商家、1：厂家
    // 商家弹窗：厂家同意预付申请  Pop1 = 1,
    // 商家弹窗：厂家拒绝预付申请  Pop2 = 2,
    // 商家弹窗：厂家主动开启预付[主动开启、绑定店铺]  Pop3 = 3,
    // 厂家弹窗：商家发起预付申请  Pop4 = 4,
    // 厂家弹窗：厂家主动开启预付[主动开启、绑定店铺]自提示  Pop5 = 5,
    // 
    //  子账号权限校验，type=0 商家时，需要校验权限ApplyPrepay，type=1 厂家时，需要校验权限SetPrePay
    common.PopPrePayData = function (type, popType, isAll) {
        //console.log("PopPrePayData" + popType);
        common.FxPermission(function (p) {
            var perm = p.SetPrePay;
            if (type == 0)
                perm = p.ApplyPrepay;
            common.CheckPermission(function (success) {
                if (success) {
                    thisFunc();
                }
                else return;
            }, perm,null,true);
        });
        var thisFunc = function () {
            common.ajax({
                url: '/Partner/PopPrePayData',
                data: { type: type, popType: popType, isAll: isAll },
                success: function (rsp) {
                    //console.log("窗口数据获取成功", rsp)
                    if (rsp.Success == true) {
                        var data = rsp.Data.List || [];
                        data.forEach(function (item) {
                            if (item.PopType == 1) {
                                var data = item.PopData
                                var mobile = data.map((a) => { return a.Mobile })
                                var mobileStr = mobile.join(',')
                                if (data.length > 0) {
                                    var lay = layer.open({
                                        title: "担保交易申请结果",
                                        area: "480px",
                                        content: "<div><div>您合作的厂家：</div><div>" + mobileStr + "已同意使用1688平台担保交易，线下代发存在风险，请尽快前往下单设置页面，完成授权卖家下单账号授权</div></div>",
                                        btn: ['查看如何下单付款视频', '去设置'],
                                        skin: 'wu-dailog',
                                        yes: function () {
                                            window.open('https://www.yuque.com/xiangying-len/zhy7ft/qgr8wgakl2sassmf?singleDoc#', '_blank');
                                        },
                                        btn2: function () {
                                            window.open(commonModule.rewriteUrl('/SupplySet1688/DistributorSet?'), '_self');
                                        }
                                    });
                                }
                            }
                            if (item.PopType == 2) {
                                var data = item.PopData
                                var mobile = data.map((a) => { return a.Mobile })
                                var mobileStr = mobile.join(',')
                                if (data.length > 0) {
                                    var lay = layer.open({
                                        title: "担保交易申请结果",
                                        area: "480px",
                                        content: "<div><div>您合作的厂家：</div><div>" + mobileStr + "已拒绝使用1688平台担保交易，线下代发存在风险，请与厂家沟通，建议使用平台担保交易</div></div>",
                                        btn: ['查看如何下单付款视频', '立即查看详情'],
                                        skin: 'wu-dailog',
                                        yes: function () {
                                            window.open('https://www.yuque.com/xiangying-len/zhy7ft/qgr8wgakl2sassmf?singleDoc#', '_blank');
                                        },
                                        btn2: function () {
                                            window.open(commonModule.rewriteUrl('/Partner/MySupplier?'), '_self');
                                        }
                                    });
                                }
                            }
                            if (item.PopType == 3) {

                                var data = item.PopData
                                var mobile = data.map((a) => { return a.Mobile })
                                var mobileStr = mobile.join(',');
                                var popType = item.PopType;
                                if (data.length > 0) {

                                    var lay = layer.open({
                                        type: 1,
                                        title: false, //不显示标题
                                        content: distributionCooperationRenderHtml(popType, mobile, []),
                                        area: '635px', //宽高
                                        skin: 'adialog-Shops-skin',
                                        /* closeBtn: false,*/
                                        btn: false,
                                    });

                                    //var lay = layer.open({
                                    //    title: "代发订单线上付款邀请函",
                                    //    area: "500px",                      
                                    //    content: "<div><div>您的1688供货厂家：</div><div>" + mobileStr + "邀请您使用线上代发付款，平台担保交易，货款更安全</div></div>",
                                    //    btn: ['查看详情介绍', '查看付款教程'],
                                    //    yes: function () {
                                    //        layer.close(lay)
                                    //    },
                                    //    btn1: function () {
                                    //    }
                                    //});

                                }
                            }

                            if (item.PopType == 4) {
                                console.log("分销商数据")
                                console.log(item.PopType)
                                console.log(item.PopData)

                                var data = item.PopData
                                var mobile = data.map((a) => { return a.Mobile })
                                var fxUserIds = data.map((a) => { return a.FxUserId })
                                var mobileStr = mobile.join(',');
                                var popType = item.PopType;

                                if (data.length > 0) {

                                    var lay = layer.open({
                                        type: 1,
                                        title: false, //不显示标题
                                        content: distributionCooperationRenderHtml(popType, mobile, fxUserIds),
                                        area: '635px', //宽高
                                        skin: 'adialog-Shops-skin',
                                        /* closeBtn: false,*/
                                        btn: false,
                                    });



                                    //layer.open({
                                    //    title: "您有一个合作通知",
                                    //    offset: "100px",
                                    //    area: ["500px", "180px"],
                                    //    icon: 1,
                                    //    content: "<h1>您的下游分销商：" + mobileStr + "邀请您使用线上待付款平台担保交易，货款更安全</h1>",
                                    //    btn: ['暂不接受', '同意线上付款收单'],
                                    //    btn: function () {

                                    //        layer.open({
                                    //            type: 1,
                                    //            title: false,
                                    //            content: "线下货款转账有风险，财务出账很麻烦，建议使用平台担保交易",
                                    //            area: '600px',
                                    //            skin: 'adialog-Shops-skin',
                                    //            btn: ['确认拒绝', '同意线上支付'],
                                    //            btn: function (index, layero) {
                                    //                commmon.Ajax({
                                    //                    url: '/Partner/ConfirmPrePay',
                                    //                    loading: true,
                                    //                    data: { "fxUserId": fxUserIds, "isPrePay": 0 },
                                    //                    type: 'POST',
                                    //                    success: function (rsp) {
                                    //                        if (rsp.Success) {
                                    //                            layer.open({
                                    //                                type: 1,
                                    //                                title: '',
                                    //                                content: '您已拒绝，温馨提示线下代发有风险，建议使用1688平台交易',
                                    //                                area: '500',
                                    //                                btn: [],
                                    //                                yes: function () {
                                    //                                    layer.msg("设置成功");
                                    //                                    layer.closeAll();
                                    //                                },
                                    //                                cancel: function () { }
                                    //                            });
                                    //                        }
                                    //                        else {
                                    //                            if (rsp.ErrorCode != undefined && rsp.ErrorCode != "") {
                                    //                                showTipDailog(rsp.ErrorCode);
                                    //                            }
                                    //                        }
                                    //                    }
                                    //                });
                                    //            },
                                    //            btn2: function (index, layero) {
                                    //                commmon.Ajax({
                                    //                    url: '/Partner/SetPrePay',
                                    //                    loading: true,
                                    //                    data: { "fxUserId": fxUserIds, "isPrePay": 1 },
                                    //                    type: 'POST',
                                    //                    success: function (rsp) {
                                    //                        layer.closeAll();
                                    //                        if (rsp.Success) {
                                    //                            layer.open({
                                    //                                type: 1,
                                    //                                title: '同意推单付款申请',
                                    //                                content: '您已开启担保交易线上收款，该分销商货款会支付到你的1688店铺，请前往代发品列表进行分销确认',
                                    //                                area: '500',
                                    //                                btn: ['查看操作视频教程', '设置单人单品价'],
                                    //                                btn: function () {
                                    //                                    layer.closeAll();
                                    //                                    //查看操作视频教程
                                    //                                },
                                    //                                btn1: function () {
                                    //                                    //设置单人单品价
                                    //                                }
                                    //                            });
                                    //                        }
                                    //                        else {
                                    //                            if (rsp.ErrorCode != undefined && rsp.ErrorCode != "") {
                                    //                                showTipDailog(rsp.ErrorCode);
                                    //                            }
                                    //                        }
                                    //                    }
                                    //                });
                                    //            }
                                    //        });
                                    //    },
                                    //    btn1: function () {

                                    //        commmon.Ajax({
                                    //            url: '/Partner/SetPrePay',
                                    //            loading: true,
                                    //            data: { "fxUserId": [fxUserIds], "isPrePay": 1 },
                                    //            type: 'POST',
                                    //            success: function (rsp) {
                                    //                layer.closeAll();
                                    //                if (rsp.Success) {
                                    //                    layer.open({
                                    //                        type: 1,
                                    //                        title: '同意推单付款申请',
                                    //                        content: '您已开启担保交易线上收款，该分销商货款会支付到你的1688店铺，请前往代发品列表进行分销确认',
                                    //                        area: '500',
                                    //                        btn: ['查看操作视频教程', '设置单人单品价'],
                                    //                        btn: function () {
                                    //                            layer.closeAll();
                                    //                            //查看操作视频教程
                                    //                        },
                                    //                        btn1: function () {
                                    //                            //设置单人单品价
                                    //                        }
                                    //                    });
                                    //                }
                                    //                else {
                                    //                    if (rsp.ErrorCode != undefined && rsp.ErrorCode != "") {
                                    //                        showTipDailog(rsp.ErrorCode);
                                    //                    }
                                    //                }
                                    //            }
                                    //        });

                                    //    }
                                    //});



                                }
                            }



                            if (item.PopType == 5) {
                                var data = item.PopData
                                var mobile = data.map((a) => { return a.Mobile })
                                var mobileStr = mobile.join(',')
                                if (data.length > 0) {
                                    var lay = layer.open({
                                        title: "同意推单付款申请",
                                        area: '560px', // 宽高
                                        content: "<div class='wu-f14 wu-c09'><div>您已开启担保交易线上收款，</div><div class='wu-mT4'>" + mobileStr + "该分销商的货款会按下单流程支付到您的1688店铺，</div><div class='wu-mT4'>请前往代发品列表进行分销价确认</div></div>",
                                        btn: ['设置单人单品价', '查看操作视频教程'],
                                        skin: 'wu-dailog',
                                        btn1: function (index) {
                                            layer.close(index);
                                            // 设置单人单品价
                                            window.open(common.rewriteUrl('/DistributionProduct/ListBySupplier'), '_blank');
                                        },
                                        btn2: function (index) {
                                            layer.close(index);
                                            // 查看操作视频教程
                                            window.open('https://www.yuque.com/xiangying-len/zhy7ft/giqllry0n16c5wzg?singleDoc#', '_blank');
                                        },
                                    });
                                }
                            }
                        });
                    }
                }
            });
        }

        function distributionCooperationRenderHtml(type, mobile, fxUserIds) {

            html = '';
            html += '<div class="distributionCooperationNotice" >';
            html += '<div class="distributionCooperationNotice-up">';
            if (type == 3) {
                html += '<img src="/Content/images/svg/aliIntroduce-icons-2024-1-24-04-111.png" alt="" style="width:635px;height:180px;">';
            } else if (type == 4) {
                html += '<img src="/Content/images/svg/aliIntroduce-icons-2024-1-24-04-11.png" alt="" style="width:635px;height:107px;">';
            }
            html += '</div>';
            html += '<div class="distributionCooperationNotice-main" style="padding-right: 15px;">';
            if (type == 3) {
                html += '<div class="distributionCooperationNotice-main-title">您的l688供货厂家:</div>';
            } else if (type == 4) {
                html += '<div class="distributionCooperationNotice-main-title">您的下游分销商:</div>';
            }
            html += '<ul class="distributionCooperationNotice-main-ul">';
            for (var i = 0; i < mobile.length; i++) {
                html += '<li>' + mobile[i] + '</li>';
            }
            html += '</ul>';
            html += '</div>';
            html += '<div class="distributionCooperationNotice-down">';


            if (type == 3) {
                html += '<div class="distributionCooperationNotice-down-footer">';
                html += '<span onclick="commonModule.distributionCooperationTarUrl()"  class="layui-btn layui-btn-normal layui-btn35" style="background-color: #39592b;width: 148px;margin-right: 25px;" >查看详情介绍</span>';
                html += '<a target="_blank" href="https://www.yuque.com/xiangying-len/zhy7ft/qgr8wgakl2sassmf?singleDoc#" class="layui-btn layui-btn-normal layui-btn35" style="background-color: #f59a23;">查看付款视频教程</a>';
                html += '</div>';
            } else if (type == 4) {
                html += '<div class="distributionCooperationNotice-down-footer" style="display: flex;flex-direction: column;align-items: center;position:relative">';
                html += '<div style="position: absolute;top:-30px;font-size: 25px;font-weight: 400;color: #39592b;left:128px;">订单回流店铺 , 提升店铺销量权重</div>'
                html += '<div style="margin-top: 20px;">';
                html += '<span class="layui-btn layui-btn-normal layui-btn35" style="background-color: #39592b;width: 148px;margin-right: 25px;" onclick=\'commonModule.cancelDistributionCooperation("' + fxUserIds + '")\'>暂不接受</span>';
                html += '<span class="layui-btn layui-btn-normal layui-btn35" style="background-color: #f59a23;" onclick=\'commonModule.sureDistributionCooperation("' + fxUserIds + '")\'>同意线上付款收单</span>';
                html += '</div>';
                html += '</div>';
            }


            html += '<div class="distributionCooperationNotice-down-warn">';
            html += '<span style="margin-bottom: 10px;">温馨提示:</span>';
            if (type == 3) {
                html += '<span class="tColor">您的代发订单已关闭自动代发推送，需先付款再推送到厂家账户，请前往订单列表-待审核列表查看详情</span>';
            } else if (type == 4) {
                html += '<span class="tColor">线上收单需关联下游商品到您的1688店铺，对方才能正常付款下单到您的店铺请快速操作设置，避免下游客户货款无法及时支付</span>';
            }

            html += '</div>';
            html += '</div>';
            html += '</div>';
            return html;
        }

    }

    //common.ajax = common.Ajax;
    common.cancelDistributionCooperation = function (fxUserIds) {
        common.FxPermission(function (p) {
            common.CheckPermission(function (success) {
                if (success) {
                    thisFunc();
                }
                else return;
            }, p.SetPrePay);
        });
        var thisFunc = function () {
            layer.open({
                type: 1,
                title: false,
                content: "<div style='padding:25px;font-size:16px'>线下货款转账有风险，财务出账很麻烦，建议使用平台担保交易</div>",
                area: '600px',
                skin: 'adialog-Shops-skin setPrePaySkin',
                btn: ['确认拒绝', '同意线上支付'],
                yes: function (index, layero) {
                    common.ajax({
                        url: '/Partner/ConfirmPrePay',
                        loading: true,
                        data: { "fxUserId": fxUserIds, "isPrePay": 0 },
                        type: 'POST',
                        success: function (rsp) {
                            if (rsp.Success) {

                                var html01 = '';
                                html01 += '<div class="distributionCooperationResult">';
                                html01 += '<div class="distributionCooperationResult-up">';
                                html01 += '<span class="distributionCooperationResult-up-icon"></span>';
                                html01 += '<span class="distributionCooperationResult-up-title">您已拒绝</span>';
                                html01 += '</div>';
                                html01 += '<div class="distributionCooperationResult-down" > ';
                                html01 += '<span class="sColor" > 温馨提示: 线下代发有风险，建议使用1688平台担保交易</span > ';
                                html01 += '</div>';
                                html01 += '</div>';


                                layer.open({
                                    type: 1,
                                    title: false, //不显示标题
                                    content: html01,
                                    area: '400px', //宽高
                                    skin: 'adialog-Shops-skin',
                                    btn: false,
                                });
                                window.location.href = window.location.href;

                                //layer.open({
                                //    type: 1,
                                //    title: '',
                                //    content: '您已拒绝，温馨提示线下代发有风险，建议使用1688平台交易',
                                //    area: '500',
                                //    btn: [],
                                //    yes: function () {
                                //        layer.msg("设置成功");
                                //        layer.closeAll();
                                //    },
                                //    cancel: function () { }
                                //});

                            }
                            else {
                                if (rsp.ErrorCode != undefined && rsp.ErrorCode != "") {
                                    showTipDailog(rsp.ErrorCode);
                                }
                            }
                        }
                    });
                },
                btn2: function (index, layero) {
                    common.ajax({
                        url: '/Partner/ConfirmPrePay',
                        loading: true,
                        data: { "fxUserId": fxUserIds, "isPrePay": 1 },
                        type: 'POST',
                        success: function (rsp) {
                            layer.closeAll();
                            if (rsp.Success) {
                                layer.open({
                                    type: 1,
                                    title: '同意推单付款申请',
                                    content: '<div class="wu-f14 wu-c09"><div>您已开启担保交易线上收款，</div><div class="wu-mT4">该分销商货款会支付到你的1688店铺，</div><div class="wu-mT4">请前往代发品列表进行分销确认</div></div>',
                                    area: '560px',
                                    btn: ['设置单人单品价', '查看操作视频教程'],
                                    skin: 'wu-dailog',
                                    btn1: function () {
                                        // 设置单人单品价
                                        window.open(common.rewriteUrl('/DistributionProduct/ListBySupplier'), '_blank');
                                        layer.closeAll();
                                    },
                                    btn2: function () {
                                        // 查看操作视频教程
                                        window.open('https://www.yuque.com/xiangying-len/zhy7ft/giqllry0n16c5wzg?singleDoc#', '_blank');
                                        window.location.href = window.location.href;
                                        layer.closeAll();
                                    },
                                });
                            }
                            else {
                                if (rsp.ErrorCode != undefined && rsp.ErrorCode != "") {
                                    console.log(rsp)
                                    layer.errMsg("rsp.ErrorCode")
                                }
                            }
                        }
                    });
                }
            });
        }
        
    }

    common.sureDistributionCooperation = function (fxUserIds) {
        common.FxPermission(function (p) {
            common.CheckPermission(function (success) {
                if (success) {
                    thisFunc();
                }
                else return;
            }, p.SetPrePay);
        });
        var thisFunc = function () {
            common.ajax({
                url: '/Partner/ConfirmPrePay',
                loading: true,
                data: { "fxUserId": fxUserIds, "isPrePay": 1 },
                type: 'POST',
                success: function (rsp) {
                    layer.closeAll();
                    if (rsp.Success) {
                        layer.open({
                            type: 1,
                            title: '同意推单付款申请',
                            content: '<div class="wu-f14 wu-c09"><div>您已开启担保交易线上收款，</div><div class="wu-mT4">该分销商货款会支付到你的1688店铺，</div><div class="wu-mT4">请前往代发品列表进行分销确认</div></div>',
                            area: '560px',
                            btn: ['设置单人单品价', '查看操作视频教程'],
                            skin: 'wu-dailog',
                            btn1: function () {
                                layer.closeAll();
                                // 设置单人单品价
                                window.open(common.rewriteUrl('/DistributionProduct/ListBySupplier'), '_blank');
                            },
                            btn2: function () {
                                layer.closeAll();
                                // 查看操作视频教程
                                window.open('https://www.yuque.com/xiangying-len/zhy7ft/giqllry0n16c5wzg?singleDoc#', '_blank');
                                window.location.href = window.location.href;
                            }
                        });
                    }
                    else {
                        if (rsp.ErrorCode != undefined && rsp.ErrorCode != "") {
                            layer.message(rsp.ErrorCode)
                        }
                    }
                }
            });
        }
    }

    common.distributionCooperationTarUrl = function () {
        window.open(common.rewriteUrl('/GeneralizeIndex/aliDistributionIntroduce?fromUrl=distributionCooperation'), '_blank');

    }

    //common.getPlatformAndArea = function () {

    //    var platformName = $("#cloudplatform_nva_ul>li.active", parent.document).attr("data-cloud");
    //    var areaName = $(".changeArea-wrap>li.layui-this", parent.document).attr("id");

    //    var obj = {};
    //    obj.platformName = platformName ? platformName : '';
    //    obj.areaName = areaName ? areaName : '';
    //    return obj;

    //}
    common.isAllChinese = function (str) {
        //中文 {} () （）可通过
        str = str || "";
        var ignoreChars = ['{', '}', '(', ')', '（', '）', '㙍', '㙦', '〇', '䢺']; // 部分特殊字符需要忽略不应该拦截
        for (var i = 0; i < ignoreChars.length; i++) {
            str = str.replaceAll(ignoreChars[i], '');
        }
        if (str == "")
            return true;
        return /^[\u4E00-\u9FA5]+$/.test(str)
    }

    common.checkMoveAndTel = function (mobile, type) { // 匹配手机号和固话，正确返回true 错误返回false
        if (typeof mobile == 'number') {              //  type 取值move为验证手机，type 取值tel为固定电话  其它值为验证两都符合一种都返回true
            mobile = mobile.toString();
        }
        var tel = /^0\d{2,3}-?\d{7,8}$/;
        var phone = /^1(3|4|5|7|8|9)\d{9}$/;
        var isTrue = false;

        if (type == "move") {
            if (phone.test(mobile)) {
                isTrue = true;
            }
        } else if (type == "tel") {
            if (tel.test(mobile)) {
                isTrue = true;
            }
        } else {
            if (mobile.length == 11) { //手机号码
                if (phone.test(mobile)) {
                    isTrue = true;
                }
            } else if (mobile.length == 12 && mobile.indexOf("-") != -1 || mobile.length == 13 && mobile.indexOf("-") != -1) { //电话号码
                if (tel.test(mobile)) {
                    isTrue = true;
                }
            }
        }

        return isTrue;
    }
    
    // 任务中心跳转：设置默认选中模块
    common.initTaskCenterNavSelect = function (type, target) {
        var value = null;
        switch (type) {
            case "AllOrder": value = '9'; break;
            case "WaitOrder": value = '10'; break;
            case "OfflineOrder": value = '12'; break;
            case "WaybillCodeList": value = '11'; break;
            case "ErpWaybill": value = '16'; break;
            case "PrintHistory": value = '22'; break;
            case "SendOrder": value = '18'; break;
            case "AfterSale": value = '19'; break;
            case "StockDetail": value = '20'; break;
            default: value = null;
        }
        if (value != null && value != undefined && value != "") {
            console.log("模块value" + value);
            /*common.setStorage('exportTaskType', value);*/
            common.transferUrlToMainDomain('/ExportTask/Index?exportTaskType=' + value, '_blank');
        }
    }

    // 任务中心跳转：设置默认选中模块
    common.togglePddXuFei = function (authType) {  //拼多多续费选择
        if (authType == undefined || authType == "") {
            authType = "3";
        }
        var html = "";
        html += '<div class="togglePlatformWrap"  style="width:620px">';
        html += '<div class="togglePlatform" style="padding: 0;">';
        // html += '<div class="togglePlatform-title">请选择店铺已订购的应用类型</div>';
        html += '<ul class="togglePlatform-content" id="togglePlatform_pdd_xufei" style="margin-top: 0;padding: 0;">';
        html +=
            '<li class="togglePlatform-content-item" data-appkey="1004884" data-status="http://pdd2.dgjapp.com/default.aspx?AuthType=' + authType + '&SuccToUrl=http%3a%2f%2fpdd10.dgjapp.com%2fauth%2fauthsuccess">';
        html +=
            '<span class="togglePlatform-content-item-img" style="background-image:url(/Content/images/printapp-platform.png)"></span>';
        html += '<span class="togglePlatform-content-item-title">店管家打单</span>';
        html += '</li>';
        html += '<li class="togglePlatform-content-item" data-appkey="5382198" data-status="http://testauth.dgjapp.com/fxauth/pdd?AuthType=' + authType + '&SuccToUrl=http%3a%2f%2fpdd10.dgjapp.com%2fauth%2fauthsuccess">';
        html += '<span class="togglePlatform-content-item-img" style="background-image:url(/Content/images/fxapp-platform.png)"></span>';
        html += '<span class="togglePlatform-content-item-title">店管家_分销代发</span>';
        html += '</li>';
        html += '</ul>';
        html += '</div>';
        html += '</div>';
        layer.open({
            type: 1,
            title: '请选择店铺已订购的应用类型',
            // closeBtn: 0,
            content: html,
            area: '528px',
            btn: ['确定', '取消'],
            skin: 'wu-dailog',
            success: function () {
                $$.navActive('#togglePlatform_pdd_xufei', function () { });
            },
            btn1: function () {
                var authurl = ""; //续费链接
                var firstUrl = "";
                var urlCount = 0;
                $("#togglePlatform_pdd_xufei .togglePlatform-content-item").each(function (index,
                    item) {
                    if ($(item).hasClass("active")) {
                        authurl = $(item).attr("data-status");
                        urlCount++;
                    }
                })
                if (urlCount == 1) {
                    var token = common.GetQueryString("token");
                    var authurl = authurl;
                    if (authurl.indexOf("?") > 0)
                        authurl += "&rp=" + token;
                    else
                        authurl += "?rp=" + token;
                    common.OpenNewTab(authurl);
                    var isAddOk = layer.confirm('是否授权成功？', {
                        icon: 3,
                        title: '授权结果确认',
                        btn: ['确定', '取消'],
                        skin: 'wu-dailog'
                    },
                        function () {
                            layer.closeAll();
                            window.location.href = window.location.href;
                        },
                        function () {
                            layer.close(isAddOk);
                            window.location.href = window.location.href;

                        }
                    );

                } else if (authurl == "") {
                    layer.msg("请选择应用类型！")
                }
            },
            cancel: function () { }
        });
    }


    common.transformImgSrc = function (src) {
        var newSrc = '';
        if ((src && (src.indexOf('aftersale') > -1 || src.indexOf('baseproduct') > -1)) && src.indexOf('&platform=') <= -1) {
            newSrc = '/Common/GetObjectStorage?objectKey=' + src + '&platform=' + (common.CloudPlatformType || 'Alibaba');
        } else {
            newSrc = src
        }           
        return newSrc;
    }
    common.newTransformImgSrc = function (src, urlParam) {
        var newSrc = '';
        if (src != null && src != undefined && src !="//.") {
            if (!src || (src.indexOf('http') > -1 || src.indexOf('https') > -1)) {
                newSrc = src
            } else {
                var token = $("#token_input").val() ? $("#token_input").val() : '';
                urlParam = urlParam || ''
                newSrc = src + '&platform=' + commonModule.CloudPlatformType + '&token=' + token + urlParam;
            }
        }
        return newSrc;
    }

    common.newReplaceImgSrc = function (src, urlParam) {
        var newSrc = '';
        if (src != null && src != undefined && src != "//.") {
            if (!src || (src.indexOf('http') > -1 || src.indexOf('https') > -1)) {
                newSrc = src
            } else {
                var vars = src.split("&");
                var token = $("#token_input").val() ? $("#token_input").val() : '';
                for (var i = 0; i < vars.length; i++) {
                    var pair = vars[i].split("=");
                    if (pair[0] == 'token') {
                        var regex = /(token=)([^&]*)/i;
                        src = src.replace(regex, '&token=' + token);
                    }
                }
                urlParam = urlParam || ''
                newSrc = src;
            }
        }
        return newSrc;
    }

    common.transformNewImgSrc = function (src) {
        var token = $("#token_input").val() ? $("#token_input").val() : '';
        var imgSrc =src + '&platform=' + commonModule.CloudPlatformType + '&token=' + token;
        return imgSrc;
    }

    var w_alert_id = "";
    common.w_alert = function (options) {
        $(".show-w-mgs").remove();
        var html = "";
        var type = options.type ? options.type : 1;
        var content = options.content;
        var skin = options.skin ? options.skin : "";
        var className = '';
        if (type == 1) {
            className = "icon-a-heart-filled1x";
        } else if (type == 2) {
            className = "icon-a-error-circle-filled1x";
        } else if (type == 3) {
            className = "icon-a-error-circle-filled1x";
        } else if (type == 4) {
            className = "icon-a-check-circle-filled1x";
        } else if (type == 5) {
            className = "icon-a-error-circle-filled1x n-dColor";
            type = 0;
        }
        w_alert_id = 'alert' + new Date().getTime();
        if (true) {
            var skinClass = "w-msg show-w-mgs wu-alertWrap";
            if (skin) {
                skinClass = "w-msg show-w-mgs "+skin;
            }
            html += '<div class="' + skinClass + '" id=' + w_alert_id + ' onclick="commonModule.del_w_alert()">';
            html += '<div class="n-toast n-toast-0' + type + '" ><i class="iconfont ' + className +'"></i>' + content +'</div>';
            html+='</div>'
            $("body").append(html);
        }
        var times = options.times ? options.times : 3000;
        setTimeout(function () {
            $("#" + w_alert_id).remove();
        }, times)
    }
    common.del_w_alert = function () {
        $("#" + w_alert_id).remove();
    }


    //20240411 基于淘宝旗帜，其他平台&全局旗帜统一配置
    common.NewRemarkFlagSetting = function (pt) {
        
        var systemFlags = [
            // { "Id": "11", "Value": "-1", "Text": "无旗帜", "Color": "", "newText": "无旗帜" },
            { "Id": "0", "Value": "0", "Text": "灰色/无旗帜", "Color": "#999999", "newText": "灰旗/无旗帜" },
            { "Id": "1", "Value": "1", "Text": "红色", "Color": "#ea3d3d", "newText": "红旗" },
            { "Id": "4", "Value": "4", "Text": "黄色", "Color": "#ffc60b", "newText": "黄旗" },
            { "Id": "3", "Value": "3", "Text": "绿色", "Color": "#3acb36", "newText": "绿旗" },
            { "Id": "2", "Value": "2", "Text": "蓝色", "Color": "#0664f0", "newText": "蓝旗" },
            { "Id": "5", "Value": "5", "Text": "紫色", "Color": "#ba2fff", "newText": "紫旗" },
            { "Id": "6", "Value": "6", "Text": "橙色", "Color": "#f48804", "newText": "无标签" },
            { "Id": "7", "Value": "7", "Text": "浅蓝", "Color": "#41b4fa", "newText": "无标签" },
            { "Id": "8", "Value": "8", "Text": "浅粉", "Color": "#e5b6b6", "newText": "无标签"},
            { "Id": "9", "Value": "9", "Text": "深绿", "Color": "#9ba217", "newText": "无标签" },
            { "Id": "10", "Value": "10", "Text": "桃红", "Color": "#e4248e", "newText": "无标签" }
        ];
        var platformType = commonModule.PlatformType;
        var isCustomerOrder = commonModule.isCustomerOrder();
        var ptflagIds = ['0', '1', '2', '3', '4', '5','6','7','8','9','10'];
        if (platformType == "Alibaba") {
            ptflagIds = ['0', '1', '2', '3', '4'];
        }
        if (platformType == "Taobao") {
            ptflagIds = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10'];
        }
        //自由打印没有灰旗
        if (isCustomerOrder) {
            ptflagIds = ['1', '2', '3', '4', '5'];
        }
        //部分场景不显示新旗帜
        if (common.CloudPlatformType != "Alibaba" || (pt && pt != "Taobao"))
            ptflagIds = ["0", '1', '2', '3', '4', '5'];

        var flagList = [];
        for (var i = 0; i < systemFlags.length; i++) {
            var systemflag = systemFlags[i];
            if (ptflagIds.indexOf(systemflag.Value) != -1) {
                flagList.push(systemflag);
            }
        }
        return flagList;
    }
     //查询物流轨迹
    common.ViewLogisticTraces = function (logisticsNo, expressName) {
        if (_logisticsTraceList.length == 0) {
            common.Ajax({
                url: '/Common/GetLogisticsTrace',
                loading: true,
                success: function (rsp) {
                    if (rsp.Success == false) {
                        layer.msg(rsp.Message, { icon: 2 });
                        return;
                    }

                    _logisticsTraceList = rsp.Data;
                    common.MatchLogisticTraces(logisticsNo, expressName);

                }
            });
        } else {
            common.MatchLogisticTraces(logisticsNo, expressName);
        }
    }

    //匹配物流轨迹平台
    common.MatchLogisticTraces = function (logisticsNo, expressName) {
        if (_logisticsTraceList.length > 0) {
            var url = '';

            //先取对应的物流平台
            for (var i = 0; i < _logisticsTraceList.length; i++) {
                if (expressName.indexOf(_logisticsTraceList[i].LogisticsName) > -1 && _logisticsTraceList[i].Type == 0) {
                    url = _logisticsTraceList[i].Url;
                    break;
                }
            }

            if (url == '') {
                //没有对应的物流平台，取通用
                for (var i = 0; i < _logisticsTraceList.length; i++) {
                    if (_logisticsTraceList[i].LogisticsName == '通用' && _logisticsTraceList[i].Type == 1) {
                        url = _logisticsTraceList[i].Url;
                        break;
                    }
                }
            }

            window.open(url.replace('{0}', logisticsNo));
        } else {
            layer.msg("查询失败，请联系客服", { icon: 2 })
        }
       
    }

    common.changePtName = function (pt) {
        pt = pt.toLocaleUpperCase();
        var PtName = "精选";
        if (pt == "alibaba") {
            PtName = "精选";
        } else if (pt =="pinduoduo") {
            PtName = "拼多多";
        } else if (pt == "jingdong") {
            PtName = "京东";
        } else if (pt == "toutiao") {
            PtName = "抖店";
        }
        return PtName;
    }

    common.allPlatformData = [   //id不能改动
        { Id: 1, Title: '抖店', Name: 'TouTiao', Offset: '0 0', bigOffset:'0 0',IsCheck: false, Sort: 1 },
        { Id: 2, Title: '拼多多', Name: 'Pinduoduo', Offset: '0 0', bigOffset: '0 0', IsCheck: false, Sort: 2 },
        { Id: 3, Title: '快手小店', Name: 'KuaiShou', Offset: '0 0', bigOffset: '0 0', IsCheck: false, Sort: 3 },
        { Id: 4, Title: '1688', Name: 'Alibaba', Offset: '0 0', bigOffset: '0 0', IsCheck: false, Sort: 4 },
        { Id: 5, Title: '淘宝', Name: 'Taobao', Offset: '0 0', bigOffset: '0 0', IsCheck: false, Sort: 5 },
        { Id: 6, Title: '京东', Name: 'Jingdong', Offset: '0 0', bigOffset: '0 0', IsCheck: false, Sort: 6 },
        { Id: 7, Title: '微信小商店', Name: 'WxXiaoShangDian', Offset: '0 0', bigOffset: '0 0', IsCheck: false, Sort: 7 },
        { Id: 8, Title: '视频号小店', Name: 'WxVideo', Offset: '0 0', bigOffset: '0 0', IsCheck: false, Sort: 8 },
        { Id: 9, Title: '有赞', Name: 'YouZan', Offset: '0 0', bigOffset: '0 0', IsCheck: false, Sort: 9 },
        { Id: 10, Title: '微盟', Name: 'WeiMeng', Offset: '0 0', bigOffset: '0 0', IsCheck: false, Sort: 10 },
        { Id: 11, Title: '淘工厂', Name: 'AlibabaC2M', Offset: '0 0', bigOffset: '0 0', IsCheck: false, Sort: 11 },
        { Id: 12, Title: '度小店', Name: 'DuXiaoDian', Offset: '0 0', bigOffset: '0 0', IsCheck: false, Sort: 12 },
        { Id: 13, Title: '蘑菇街', Name: 'MoGuJie', Offset: '0 0', bigOffset: '0 0', IsCheck: false, Sort: 13 },
        { Id: 14, Title: '美丽说', Name: 'MeiLiShuo', Offset: '0 0', bigOffset: '0 0', IsCheck: false, Sort: 14 },
        { Id: 15, Title: '魔筷星选', Name: 'MoKuai', Offset: '0 0', bigOffset: '0 0', IsCheck: false, Sort: 15 },
        { Id: 16, Title: '微店', Name: 'WeiDian', Offset: '0 0', bigOffset: '0 0', IsCheck: false, Sort: 16 },
        { Id: 17, Title: '苏宁易购', Name: 'SuNing', Offset: '0 0', bigOffset: '0 0', IsCheck: false, Sort: 17 },
        { Id: 18, Title: '小红书', Name: 'XiaoHongShu', Offset: '0 0', bigOffset: '0 0', IsCheck: false, Sort: 18 },
        { Id: 19, Title: '团好货', Name: 'TuanHaoHuo', Offset: '0 0', bigOffset: '0 0', IsCheck: false, Sort: 19 },
        { Id: 20, Title: '唯品会', Name: 'VipShop', Offset: '0 0', bigOffset: '0 0', IsCheck: false, Sort: 20 },
        { Id: 21, Title: '快团团', Name: 'KuaiTuanTuan', Offset: '0 0', bigOffset: '0 0', IsCheck: false, Sort: 21 },
    ]
    common.allExpressBillData = [
        {
            Id: 1,
            ExpressBillType: "TouTiao",
            ExpressBillName: "抖音面单",
            IsCheck: false
        },
        {
            Id: 2,
            ExpressBillType: "Pinduoduo",
            ExpressBillName: "拼多多面单",
            IsCheck: false
        },
        {
            Id: 3,
            ExpressBillType: "KuaiShou",
            ExpressBillName: "快手面单",
            IsCheck: false
        },
        {
            Id: 4,
            ExpressBillType: "Jingdong",
            ExpressBillName: "京东面单",
            IsCheck: false
        },
        {
            Id: 5,
            ExpressBillType: "XiaoHongShu",
            ExpressBillName: "小红书面单",
            IsCheck: false
        },
        {
            Id: 6,
            ExpressBillType: "WxVideo",
            ExpressBillName: "视频号面单",
            IsCheck: false
        },
        {
            Id: 7,
            ExpressBillType: "Cainiaolink",
            ExpressBillName: "菜鸟link面单",
            IsCheck: false
        },
        {
            Id: 8,
            ExpressBillType: "Taobao",
            ExpressBillName: "1688/淘宝菜鸟面单",
            IsCheck: false
        }
    ]

    common.checkPwdNew = function(pwd) { //匹配密码，正确返回true 错误返回false
        //var pwd = $(ele).val();
        //console.log(pwd);
        var typeCount = 0;
        if (pwd.length < 8 || pwd.length > 20) {
            return {
                success: false,
                msg: "密码长度需要为8-20位"
            };
        }
        if (/[a-z]/.test(pwd)) { //有小写
            typeCount++;
        }
        if (/[A-Z]/.test(pwd)) { //有大写
            typeCount++;
        }
        if (/[0-9]/.test(pwd)) { //有数字
            typeCount++;
        }
        if (/[#?!@$%^&*.]/.test(pwd)) { // 有特殊符号
            typeCount++;
        }
        if (!/^[a-zA-Z0-9#?!@$%^&*.]+$/.test(pwd)) { // 校验密码特殊符号，只允许包含 #?!@$%^&*.
            return {
                success: false,
                msg: "可选的特殊符号为 # ? ! @ $ % ^ & * . ",
                times:5000
            };
        }
        if (typeCount >= 3)
            return {
                success: true,
                msg: ""
            };
        else
            return {
                success: false,
                msg: "密码同时必须包含大写字母、小写字母、数字及特殊字符中的三种以上字符",
                times:5000
            };
    }


    // 发货仓地址/售后地址/经营地址公用弹窗
    common.showAddressDailog = function (addressInfo, callBack, title, type) {
        //私有方法-省市区联动相关
        function commonSelectCallBack(control) {
            var deep = control.attr('deep');
            if (deep > 1) {
                var dataValue = control.attr("data-value");
                var isExistsVal = control.find("option[value='" + dataValue + "']").length;
                if (isExistsVal > 0)
                    control.val(dataValue).trigger('change');
            }
        };


        var title = title ? title : '编辑地址';
        var type = type ? type : 'warehouseAddress';
        var content = '';
        if (type === 'warehouseAddress' || type === 'afterSalesAddress') {
            var tplt = $.templates("#SendAddressBody");
            var html1 = tplt.render();
            content = html1;
        } else {
            var tplt = $.templates("#BusinessAddressBody");
            var html2 = tplt.render();
            content = html2;
        }
        layer.open({
            type: 1,
            title: title, //不显示标题
            content: content,
            skin: 'n-skin',
            area: '560px', //宽高
            zIndex: 1000000000,
            btn: ['取消', '保存'],
            success: function () {
                if (type === 'warehouseAddress') {
                    $("#show_street_address_select").hide(); // 隐藏街道选择
                }

                if (type === 'afterSalesAddress') {
                    $("#show_street_address_select").show(); // 显示街道选择
                }
                
                commonModule.LoadAreaInfoToControl('n-province-select', 1, function () { }, commonSelectCallBack, "name");
                console.log("_areaCodeInfoList", _areaCodeInfoList);

                $("#n-AddressName").val(addressInfo.Name ? addressInfo.Name : '');
                $("#n-AddressMobile").val(addressInfo.Mobile ? addressInfo.Mobile : '');
                $("#n-AddressPhone").val(addressInfo.Phone ? addressInfo.Phone : '');


                $("#n-address-textarea").val(addressInfo.Address ? addressInfo.Address : '');
                if (addressInfo.Province && addressInfo.City && addressInfo.County) {
                    $("#n-city-select").attr("data-value", addressInfo.City).val(addressInfo.City);
                    $("#n-pcounty-select").attr("data-value", addressInfo.County).val(addressInfo.County);
                    $('#n-street-select').attr("data-value", addressInfo.Street).val(addressInfo.Street);

                    $("#n-province-select").attr("data-value", addressInfo.Province).val(addressInfo.Province).change();
                    //$("#n-city-select").attr("data-value", addressInfo.City).val(addressInfo.City).change();
                    //$("#n-pcounty-select").attr("data-value", addressInfo.County).val(addressInfo.County).change();
                }
            },
            yes: function (index) {
                layer.close(index);
            },
            btn2: function () {
                var mobileReg = /^(86-[1][0-9]{10})|(86[1][0-9]{10})|([1][0-9]{10})$/;  //手机正则
                var phoneReg = /^(0\d{2,3}-?\d{7,8})|((（|\()0\d{2,3}(\)|）)\d{7,8})$/; //电话正则
                var Province = $('#n-province-select').val();
                var City = $('#n-city-select').val();
                var Area = $('#n-pcounty-select').val();
                var DetailAddr = $('#n-address-textarea').val();
                var Street = $('#n-street-select').val();
                if (type === 'warehouseAddress' || type === 'afterSalesAddress') {
                    var Name = $('#n-AddressName').val().trim();
                    var Mobile = $('#n-AddressMobile').val().trim();
                    var Phone = $('#n-AddressPhone').val().trim();
                    if (Name == '') {
                        wuFormModule.wu_toast({ type: 3, content: '请填写发件人姓名' });
                        return false;
                    }
                    if (Mobile == '' && Phone == '') {
                        wuFormModule.wu_toast({ type: 3, content: '发件人手机和固话必填一个' });
                        return false;
                    }
                    if (Mobile != '' && !mobileReg.test(Mobile)) {
                        wuFormModule.wu_toast({ type: 3, content: '手机号格式不对' });
                        return false;
                    }
                    if (Phone != '' && !phoneReg.test(Phone)) {
                        wuFormModule.wu_toast({ type: 3, content: '电话号码格式不对' });
                        return false;
                    }
                }
                var Addr = '';
                if (Province && Province != "0") {
                    Addr += Province;
                }
                else {
                    wuFormModule.wu_toast({ type: 3, content: '请选择省份' });
                    return false;
                }
                if (City && City != "0") {
                    Addr += City;
                }
                else {
                    wuFormModule.wu_toast({ type: 3, content: '请选择城市' });
                    return false;
                }

                if (Area && Area != "0") {
                    Addr += Area;
                }
                else {
                    wuFormModule.wu_toast({ type: 3, content: '请选择区域' });
                    return false;
                }
                // 售后地址-街道必填
                if (type === 'afterSalesAddress') {
                    if (Street && Street != "0") {
                        Addr += Street;
                    } else {
                       wuFormModule.wu_toast({ type: 3, content: '请选择街道' });
                       return false;
                    }
                }

                if (DetailAddr == '') {
                    wuFormModule.wu_toast({ type: 3, content: '请填写详情地址' });
                    return false;
                }
                Addr += DetailAddr;
                
                var allAddress = '';
                if (type === 'warehouseAddress' || type === 'afterSalesAddress') {
                    allAddress = Name + ',' + (Mobile ? Mobile : Phone) + "," + Addr;
                } else {
                    allAddress = Addr;
                }
                var addressObj = {};
                if (type === 'warehouseAddress' || type === 'afterSalesAddress') {
                    addressObj.Name = Name;
                    addressObj.Mobile = Mobile;
                    addressObj.Phone = Phone;
                }
                addressObj.Province = Province;
                addressObj.City = City;
                addressObj.County = Area;
                addressObj.Street = Street;
                addressObj.Address = DetailAddr;
                addressObj.allAddress = allAddress;


                if (typeof callBack == "function") {
                    callBack(addressObj);
                }


            }
        });

    }
    // 新老应用订购提示
    common.orderingTips = function (data) {
        var titleTips = !data.title? "订购提示" : data.title;
        var titleContent = !data.content? "您当前的店铺暂未订购店管家分销代发，无法开启更多功能，如需代发更高效，请加购店管家分销代发。" : data.content;
        var viewVersionUrl = "https://xtx0o8yn7x.feishu.cn/docx/YqjFdkjauoRKW2xf3hXcHPEqnTe"; // 查看版本功能区分
        var orderingTipsLayer = null;
        var html = "";
        html += '<div class="togglePlatformWrap" style="padding: 16px;">';
        html += '<div style="margin-bottom: 16px;color: rgba(0, 0, 0, 0.6);font-size: 14px;line-height: 20px;">' + titleContent + '</div>';
        html += '<div style="display: flex;justify-content: space-between;align-items: center;">';
        html += '<a class="n-font5 hover" href="' + viewVersionUrl + '" target="_blank" style="color:#0888FF;">查看版本功能区分</a>';
        html += '<div>';
        html += '<div class="n-mButton" id="goOrderingBtn">前往订购</div>';
        html += '<div class="n-mButton n-sActive" style="margin-left: 12px;display: none;" id="ShowBtn">仅铺货，晚点订购</div>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
        orderingTipsLayer = layer.open({
            type: 1,
            title: titleTips,
            content: html,
            offset: 'auto',
            area: '560px',
            skin: 'n-skin',
            success: function () {
                $(".n-skin").find(".layui-layer-content").css({
                    padding: 0,
                });
                if (data.title === "订单服务权限提示") {
                    $("#ShowBtn").show();
                    $("#goOrderingBtn").text("订购自动推单");
                    $("#ShowBtn").on('click', function () {
                        layer.close(orderingTipsLayer);
                    });
                } else {
                    $("#ShowBtn").hide();
                }
                $('#goOrderingBtn').on('click', function () {
                    layer.close(orderingTipsLayer);
                    var url = "https://fuwu.jinritemai.com/detail?btm_ppre=a0254.b6901.c8954.d4082_2&btm_pre=a0254.b9825.c7579.d6976_1&page_from=1gr0710iq_b9825&pre_show_id=f7cd2d5b-41d3-40c4-a248-a862d79465a3&searchKey=%E5%BA%97%E7%AE%A1%E5%AE%B6&service_id=24069";
                    window.open(common.rewriteUrl(url), "_blank");
                });
            },
        });
    }
    // 异常商品变更明细抽屉
    common.AbnormalProductChangeDetail = function () {
        $('body').find("#AbnormalProductIframeWrap").remove();
        var html = "";
        html += '<div class="new-full-mask" id="AbnormalProductIframeWrap" style="z-index: 1000000000;">';
        html += '<div class="full-mask-back"></div>';
        html += '<div class="full-mask-content-wrapper full-mask-right" style="height:100vh; width: 888px;">';
        html += '<iframe id="AbnormalProductIframe" style="width: 100%; height: 100%;" frameborder="0"></iframe>';
        html += '</div>';
        html += '</div>';
        $('body').append(html);
        var url = common.rewriteUrlToMainDomain('/AbnormalProductChange/Index');
        $("#AbnormalProductIframe").attr({ src: url });
        $("#AbnormalProductIframeWrap").addClass("active");
        $('body').css({ overflow: 'hidden' });
    }
    common.tarDistribution = function (type, UidStr, parame, targetShopId, TargetShopName, fromTargetPage) {
        $('body').find("#PrepareDistributionIframeWrap").remove();
        var html = "";
        html += '<div class="new-full-mask" id="PrepareDistributionIframeWrap">';
        //html += '<div class="full-mask-back" onclick="commonModule.closeistribution()"></div>';
        html += '<div class="full-mask-back" ></div>';

        html += '<div class="full-mask-content-wrapper full-mask-right" style="width: 1100px !important;height:100vh;">';
        html += '<iframe id="PrepareDistributionIframe" style=" width: 100%; height: 100%;" frameborder="0"></iframe>';
        html += '</div>';
        html += '</div>';
        $('body').append(html);

        var url = common.rewriteUrlMoreArea('/BaseProduct/PrepareDistribution') + '&CreateFrom=edmit&baseproductuid=' + (UidStr || '');
        if (parame && parame.FxUserId) {
            url = url + '&FxUserId=' + parame.FxUserId;
        }
        if (parame &&parame.FromCode) {
            url = url + '&FromCode=' + parame.FromCode;
        }
        if (parame &&parame.PlatformType) {
            url = url + '&PlatformType=' + parame.PlatformType;
        }
        if (parame &&parame.FromType) {
            url = url + '&FromType=' + parame.FromType;
        }
        if (parame && parame.FromType==0) {
            url = url + '&FromType=0';
        }

        if (targetShopId) {  //重新铺货带的参数
            url = url + '&TargetShopId=' + targetShopId + '&TargetShopName=' + TargetShopName;
        }
        // 铺货日志-重新铺货入口
        if (fromTargetPage) {
            url = url + '&fromTargetPage=' + fromTargetPage;
        }

        $("#PrepareDistributionIframe").attr({ src: url });
        $("#PrepareDistributionIframeWrap").addClass("active");
        $('body').css({ overflow: 'hidden' });
    }
    common.newTarDistribution = function () {
        $(".show-w-mgs").remove();
        $("#PrepareDistributionIframeWrap").addClass("active");
        $('body').css({ overflow: 'hidden' });
    }

    common.tarSkuRelation = function (ProductCode, SkuCode, UpFxUserId) {
        $('body').find("#PrepareDistributionIframeWrap").remove();
        var html = "";
        html += '<div class="new-full-mask" id="PrepareDistributionIframeWrap">';
        //html += '<div class="full-mask-back" onclick="commonModule.closeistribution()"></div>';
        html += '<div class="full-mask-back" ></div>';

        html += '<div class="full-mask-content-wrapper full-mask-right" style="width: 888px !important;height:100vh;">';
        html += '<iframe id="PrepareDistributionIframe" style=" width: 100%; height: 100%;" frameborder="0"></iframe>';
        html += '</div>';
        html += '</div>';
        $('body').append(html);

        var url = common.rewriteUrlMoreArea('/BaseProduct/BaseOfPtSkuRelationEqualProducts');
        if (ProductCode) {
            url = url + '&ProductCode=' + ProductCode + '&SkuCode=' + SkuCode;
        }
        if (UpFxUserId !== null) {
            console.log("用户id", UpFxUserId);
            url = url + '&ProductCode=' + ProductCode + '&SkuCode=' + SkuCode + '&UpFxUserId=' + UpFxUserId;
        }
        $("#PrepareDistributionIframe").attr({ src: url });
        $("#PrepareDistributionIframeWrap").addClass("active");
        $('body').css({ overflow: 'hidden' });
        var backDom = document.getElementById('PrepareDistributionIframeWrap').getElementsByClassName('full-mask-back')[0];
        var closeIframe = function (e) {
            e.preventDefault();
            $("#PrepareDistributionIframeWrap").removeClass('active');
            if (!window.tarSkuRelationListWrap) {
                $('body').css({ overflow: 'auto' });
            }
            backDom.removeEventListener('click', closeIframe);
        }
        backDom.addEventListener('click', closeIframe)
        
    }

    common.closeistribution = function () {
        var iframe = document.getElementById('PrepareDistributionIframe');
        var iframeWindow = iframe.contentWindow;
        iframeWindow.postMessage('closePrepareDistributionIframe', '*');
    }
    common.tarSkuRelationList = function (_this, list, operateType) {
        window.tarSkuRelationListWrap = true;
        $('body').find("#tarSkuRelationListWrap").remove();
        var html = "";
        html += '<div class="new-full-mask" id="tarSkuRelationListWrap">';
        //html += '<div class="full-mask-back" onclick="commonModule.closeistribution()"></div>';
        html += '<div class="full-mask-back" ></div>';

        html += '<div class="full-mask-content-wrapper full-mask-right" style="width: 888px !important;height:100vh;">';
        html += '<iframe id="tarSkuRelationListIframe" style=" width: 100%; height: 100%;" frameborder="0"></iframe>';
        html += '</div>';
        html += '</div>';
        $('body').append(html);
        var url = common.rewriteUrlMoreArea('/BaseProduct/BaseOfPtSkuUnrelatedList');
        if (operateType) {
            url += '&operateType=' + operateType;
        }
        $("#tarSkuRelationListIframe").attr({ src: url });
        $("#tarSkuRelationListWrap").addClass("active");
        $('body').css({ overflow: 'hidden' });
        var iframeWarp = document.getElementById('tarSkuRelationListIframe').contentWindow;
        // 使用postMessage向iframe发送数据
        iframeWarp.postMessage( list, '*');
        var backDom = document.getElementById('tarSkuRelationListWrap').getElementsByClassName('full-mask-back')[0];
        window.addEventListener('message', function (e) {
            if (e.data.operateType && e.data.operateType == "skuRelationList") {   //ifarme关联商品
                if (e.data.type != 'refresh') {
                    $("#tarSkuRelationListWrap").removeClass('active');

                    if (window.tarSkuRelationListWrap) {
                        delete window.tarSkuRelationListWrap
                        $('body').css({ overflow: 'auto' });
                    }
                }
                if (e.data.type == 'complete') {
                    window.tarSkuRelationListWrapStatus = e.data.type;
                    layer.closeAll('dialog');
                    if (_this == 'ExportCheckedOrder') { // 导出已选订单
                        exportOrderModule.ExportCheckedOrder()
                    } else if (_this == 'ExportToExcel') { // 导出所有订单
                        orderPrintModule.ExportToExcel()
                    } else {
                        _this.click();
                    }
                    
                }
            }
        })
        var closeIframe = function (e) {
            e.preventDefault();
            $("#tarSkuRelationListWrap").removeClass('active');
            $('body').css({ overflow: 'auto' });
            backDom.removeEventListener('click', closeIframe);
        }
        backDom.addEventListener('click', closeIframe)
    }
    common.alertDistribution = function (resultData,isTrue) {
        var resultData = JSON.parse(resultData);
        var html = "";
        html += '<div class="DistributionStatusWrap f14 c09">';
        html += '<div class="DistributionStatusWrap-content" style="padding-top:16px;font-size: 14px;">';
        html += '<span class="content-span" style="margin-right: 0;">提交成功<i class="n-dColor">' + resultData.TotalCount +'</i>，</span>';
        html += '<span class="content-span" style="margin-right: 0;">提交失败<i class="n-sColor">0</i></span>';
        html += '<span class="content-span">，商品已在铺货中，可前往“铺货日志”查看结果。</span> ';
        html += '</div>';

        //html += '<div class="DistributionStatusWrap-btnWrap">';
        //html += '<div class="DistributionStatusWrap-btnWrap-left"></div>';
        //html += '<div class="DistributionStatusWrap-btnWrap-right">';
        //html += '<span class="n-mButton n-sActive" >关闭</span>';
        //html += '<span class="n-mButton">前往铺货日志</span>';
        //html += '</div>';
        //html += '</div>';
        html += '</div>';
        var btns = ['关闭', '前往铺货日志']
        if (isTrue) {
            btns=['关闭', '确定']
        }

        layer.open({
            type: 1,
            title: '铺货任务已提交', //不显示标题
            content: html,
            area: '560px', //宽高
            skin: 'n-skin distributionStatusWrapSkin',
            success: function () { },
            btn: btns,
            btn2: function () {
                if (isTrue) {
                    window.open(common.rewriteUrlToMainDomain('/BaseProduct/DistributionLog'), '_self');
                } else {
                    window.open(common.rewriteUrlToMainDomain('/BaseProduct/DistributionLog'), '_blank');
                }
            }
        });
    }

    common.alertBatchDistribution = function (resultData) {
        var resultData = JSON.parse(resultData);
        var FailCount = resultData.FailCount;
        var SuccessCount = resultData.SuccessCount;
        var html = "";
        html += '<div class="f14 c09">提交成功<span class="n-dColor">' + SuccessCount + '</span>，提交失败 <span class="n-sColor">' + FailCount + '</span>，';
        html += '商品已在铺货中，可前往“铺货日志”查看结果。</div > ';
        layer.open({
            type: 1,
            title: '标题', //不显示标题
            content: html,
            area: '560px', //宽高
            skin: 'n-skin',
            id:'DistributionLog',
            success: function () { },
            btn: ['关闭', '前往铺货日志'],
            btn2: function () {
                var href = commonModule.rewriteUrl('/BaseProduct/DistributionLog');
                window.open(href, '_blank');
            }
        });
    }

    //数组项调整位置  array数组， fieldName 调整位置名，fieldValue 条件 type:frist  last 
    common.moveToFristOrLast = function (array, fieldName, fieldValue, Type, callBack) {  
        var index = 0;
        var obj = {};
        var isHas = false;
        array.forEach(function (item, i) {
            if (item[fieldName] == fieldValue) {
                index = i;
                obj = item;
                isHas = true;
            }
        })
        if (isHas) {
            if (index !== -1) {
                array.splice(index, 1); // 移除元素
                if (Type =="frist") {
                    array.unshift(obj); // 添加到数组首
                } else {
                    array.push(obj); // 添加到数组尾
                }
                if (typeof callBack == "function") {
                    callBack();
                }
            }
        }

    }
    common.uniqueArray = function(arr, key) {
        for (var i = 0, len = arr.length; i < len; i++) {
            for (var j = i + 1, len = arr.length; j < len; j++) {
                if (!key) {
                    if (arr[i] === arr[j]) {
                        arr.splice(j, 1);
                        j--;
                        len--;
                    }
                } else {
                    if (arr[i][key] == arr[j][key]) {
                        arr.splice(j, 1);
                        j--;
                        len--;
                    }
                }
            }
        }
        return arr;
    }
    //判断是否相同值存在
    common.isHadSameFieldArray = function (arry, type) {
        var isHaveSame = false;
        for (var i = 0; i < arry.length; i++) {
            for (var j = i + 1; j < arry.length; j++) {
                if (arry[i][type] == arry[j][type]) {
                    isHaveSame = true;
                    break;
                }
            }
            if (isHaveSame) break;
        }
        return isHaveSame;
    }
    //字符串转 相应长度 1个汉字长度为2字符   1个字母或符号长度1个字符
    common.getByteLen = function (val) {
        if (!val) return
        var len = 0;
        for (var i = 0; i < val.length; i++) {
            if (val[i].match(/[^\x00-\xff]/ig) != null) //全角
                len += 2;
            else
                len += 1;
        }
        return len;
    }

    /*
    * 防止精度丢失运算
    * @param a {number} 运算数1
    * @param b {number} 运算数2
    * @param digits {number} 精度，保留的小数点数，比如 2, 即保留为两位小数
    * @param op {string} 运算类型，有加减乘除（add/subtract/multiply/divide）
    *
    */
    common.operation = function(a, b, digits, op) {
        var o1 = toInteger(a)
        var o2 = toInteger(b)
        var n1 = o1.num
        var n2 = o2.num
        var t1 = o1.times
        var t2 = o2.times
        var max = t1 > t2 ? t1 : t2
        var result = null
        switch (op) {
            case 'add' :
                if (t1 === t2) { // 两个小数位数相同
                    result = n1 + n2
                } else if (t1 > t2) { // o1 小数位 大于 o2
                    result = n1 + n2 * (t1 / t2)
                } else { // o1 小数位 小于 o2
                    result = n1 * (t2 / t1) + n2
                }
                return (result / max).toFixed(digits)
            case 'subtract' :
                if (t1 === t2) {
                    result = n1 - n2
                } else if (t1 > t2) {
                    result = n1 - n2 * (t1 / t2)
                } else {
                    result = n1 * (t2 / t1) - n2
                }
                return (result / max).toFixed(digits)
            case 'multiply' :
                result = (n1 * n2) / (t1 * t2)
                return result.toFixed(digits)
            case 'divide' :
                result = (n1 / n2) * (t2 / t1)
                return result.toFixed(digits)
        }
    }
    /*
     * 判断obj是否为一个整数
     */
    function isInteger(obj) {
        return Math.floor(obj) === obj
    }
    /*
     * 将一个浮点数转成整数，返回整数和倍数。如 3.14 >> 314，倍数是 100
     * @param floatNum {number} 小数
     * @return {object}
     *   {times:100, num: 314}
     */
    function toInteger(floatNum) {
        var ret = {times: 1, num: 0}
        var isNegative = floatNum < 0
        if (isInteger(floatNum)) {
            ret.num = floatNum
            return ret
        }
        var strfi  = floatNum + ''
        var dotPos = strfi.indexOf('.')
        var len    = strfi.substr(dotPos+1).length
        var times  = Math.pow(10, len)
        var intNum = parseInt(Math.abs(floatNum) * times + 0.5, 10)
        ret.times  = times
        if (isNegative) {
            intNum = -intNum
        }
        ret.num = intNum
        return ret
    }

    /*
    * 1688标题搜同款
    * @param title {string} 标题
    *
    */
    common.open1688TitleSearch = function(title) {
        if(!title) return
        var paramTitle = decodeURIComponent(title)
        window.open("https://p4psearch.1688.com/page.html?hpageId=old-sem-pc-list&keywords="+paramTitle)
    }
    /*
    * 1688图片搜同款
    * @param title {string} 标题
    *
    */
    common.open1688ImgSearch = function(item) {
        if(!item) return
        var reqData = {
            Type: item.Type || '0', 
            Link: item.Link || '', 
            Coent: item.Coent || '', 
        };

        commonModule.Ajax({
            loadingMessage: "搜索中",
            url: '/api/CollectApi/FindPictureID',
            showMasker: true,
            loading: true,
            async: true,
            data: JSON.stringify(reqData),
            contentType: 'application/json',
            success: (res)=>{
                if (!res.Success) {
                    commonModule.w_alert({type: '3', content: res.Message})
                    return;
                }
                window.open("https://s.1688.com/youyuan/index.htm?tab=imageSearch&imageId=" + res.Data)
            }
        })
    }


    // 20240712 新旗帜颜色-代发留言的
    common.NewDropRemarkFlag = function () {
        var systemFlags = [
            { "Id": "11", "Value": "-1", "Text": "无旗帜", "Color": "", "newText": "无旗帜" },
            { "Id": "0", "Value": "0", "Text": "灰色", "Color": "#999999", "newText": "灰旗" },
            { "Id": "1", "Value": "1", "Text": "红色", "Color": "#ea3d3d", "newText": "红旗" },
            { "Id": "4", "Value": "4", "Text": "黄色", "Color": "#ffc60b", "newText": "黄旗" },
            { "Id": "3", "Value": "3", "Text": "绿色", "Color": "#3acb36", "newText": "绿旗" },
            { "Id": "2", "Value": "2", "Text": "蓝色", "Color": "#0664f0", "newText": "蓝旗" },
            { "Id": "5", "Value": "5", "Text": "紫色", "Color": "#ba2fff", "newText": "紫旗" },
        ];
        return systemFlags;
    }

    // 预览多张图片
    common.onShowPreviewImages = function (currentIndex, srcList, imageType) {
        $("body").css("overflow", "hidden");
        //遮罩层
        var overlayStyle = 'position: fixed;top: 0%;left: 0%;width: 100%; height: 100%;background-color: #3B3B3B;z-index: 99999999;-moz-opacity: 0.8;opacity: .80; '
        var overlay = '<div class="overlay" style="' + overlayStyle + '"></div>'
        //1.先将遮罩层加到body
        $('body').append(overlay)

        // 处理图片地址
        if (!imageType) {
            srcList.forEach(function (item, index) {
                if (item.indexOf('/Common/GetImageFile?objectKey=') == -1) {
                    srcList[index] = '/Common/GetImageFile?objectKey=' + item;
                }
            });
        }
        
        //图片css
        var imgContainerMaxWidth = 640 //图片允许的最大宽度（单位：px）
        var imgContainerMaxHeight = 640 //图片允许的最大高度（单位：px）
        var imgStyle = 'max-height:' + imgContainerMaxHeight + 'px;max-width:' + imgContainerMaxWidth
            + 'px;position:fixed;top: 0;left: 0;right: 0;bottom: 0;margin: auto;'
            + 'background-color:white;z-index: 999999999;border-bottom:1px solid #000;border-radius: 5px;'
        var img = '<div class="pre-img" style="' + imgStyle + '">'
            + '<div class="pre-close" style="height:30px;">'
            + '<div style="cursor:pointer;margin-left:30px;width:30px;height:100%;border-radius: 50%;background-color:#E6E6FA;"><a href="javascript:void(0)" style="opacity: 0.3;"  class="close"></a></div></div>'
            + '<img class="detail-img" src="' + srcList[currentIndex] + '" style="height:100%;width:100%" />'
            + '</div>'
        //2.再将整个图片容器加到body
        $('body').append(img)

        //关闭按钮css（由于伪元素不可用js设置，所以在head创建一个style用,关闭窗口时移除）
        var closeStyle =
            ' .close{opacity: 0.6;}'
            + ' .close:hover{opacity: 1;}'
            + ' .close:before, .close:after {float: right;position: relative;right: 15px;top: 5px;content: "  ";height: 20px;width: 1px;background-color: #333;}'
            + '.close:before{transform: rotate(45deg);}'
            + '.close:after{ transform: rotate(-45deg); }'
        //3.将关闭按钮的样式加到body
        $('head').append($('<style id="closeSytle">').text(closeStyle))

        //左边（前一张）按钮
        var preLeft = '<div class="pre-left" style="cursor:pointer;z-index: 999999999;border-radius: 50%; background-color: #E6E6FA; top: 50%; position: fixed; width: 50px; height: 50px ">'
        '</div>'
        //4.将左边按钮加到body
        $('body').append(preLeft)
        $($('.pre-left')[0]).append('<a style="transform: translate3d(0, -50%, 0) rotate(-135deg);position: absolute;top:52%;left:35%;width: 1.5rem;height: 1.5rem;border-top: 0.4rem solid black;border-right: 0.4rem solid black;box-shadow: 0 0 0 lightgray;transition: all 200ms ease;z-index: 889;"></a>')

        //右边(下一张)按钮
        var preRight = '<div class="pre-right" style="right:0;cursor:pointer;z-index: 999999999;border-radius: 50%; background-color: #E6E6FA; top: 50%; position: fixed; width: 50px; height: 50px ">'
        '</div>'
        //5.将右边按钮加到body
        $('body').append(preRight)
        $($('.pre-right')[0]).append('<a style="transform: translate3d(0, -50%, 0) rotate(45deg);position: absolute;top:52%;right:35%;width: 1.5rem;height: 1.5rem;border-top: 0.4rem solid black;border-right: 0.4rem solid black;box-shadow: 0 0 0 lightgray;transition: all 200ms ease;z-index: 889;"></a>')

        //图片加载完成后才获取宽和高，不然同步执行可能获取不到，同时此后每当图片变化，需要重新加载就会
        //执行到这里
        $($('.pre-img')[0]).find('img')[0].addEventListener("load", function () {
            //图片原始宽高
            var imgHeight = this.naturalHeight
            var imgWidth = this.naturalWidth

            //图片经过计算后的宽高
            var newHeight
            var newWidth

            if (imgHeight < imgContainerMaxHeight && imgWidth < imgContainerMaxWidth) { //宽和高都没超过最大值
                newHeight = imgHeight
                newWidth = imgWidth
            } else if (imgHeight < imgContainerMaxHeight && imgWidth > imgContainerMaxWidth) {  //宽 超过最大值
                var rWidth = imgWidth / imgContainerMaxWidth
                newHeight = imgHeight / rWidth
                newWidth = imgContainerMaxWidth
            } else if (imgHeight > imgContainerMaxHeight && imgWidth < imgContainerMaxWidth) {  //高 超过最大值
                var rHeight = imgHeight / imgContainerMaxHeight
                newHeight = imgContainerMaxHeight
                newWidth = imgWidth / rHeight
            } else if (imgHeight > imgContainerMaxHeight && imgWidth > imgContainerMaxWidth) { //宽和高都超过最大值
                var rHeight = imgHeight / imgContainerMaxHeight
                var rWidth = imgWidth / imgContainerMaxWidth
                //rHeight 表示原图高度需要缩小 rHeight 倍才能到达允许的最大高度
                //rWidth  表示原图宽度需要缩小 rWidth  倍才能到达允许的最大宽度
                //由于这时 宽和高是要同时缩小的，所以假设宽和高要同时缩小n倍才能使缩小后宽和高都不超过  
                //最大值，那么 n必然取 rHeight 和 rWidth 之中的最大值
                if (rWidth > rHeight) {
                    newWidth = imgContainerMaxWidth
                    newHeight = imgHeight / rWidth
                } else {
                    newHeight = imgContainerMaxHeight
                    newWidth = imgWidth / rHeight
                }
            }

            //图片设置经过计算后的宽和高
            $($($('.pre-img')[0]).find('img')[0]).css({ 'height': newHeight, 'width': newWidth })

            //整个容器（div class="pre-img"）的高度是=关闭按钮的高度+图片的高度，
            //所以图片高度变化时，容器的高度也要变化
            $($('.pre-img')[0]).css({ 'height': newHeight + $($($('.pre-img')[0]).find('pre-close')[0]).height(), 'width': newWidth })

            //关闭按钮的位置
            $($('.pre-close')[0]).find('div').css({ 'margin-left': (newWidth - $($('.pre-close')[0]).find('div').width()) + 'px' })
        })

        //关闭按钮css
        $($('.pre-close')[0]).find('div').hover(function () {
            $($($('.pre-close')[0]).find('a')[0]).css('opacity', 1)
        })
        $($('.pre-close')[0]).find('div').mouseleave(function () {
            $($($('.pre-close')[0]).find('a')[0]).css('opacity', 0.6)
        })

        //关闭按钮点击 => 关闭
        $($('.pre-close')[0]).find('div').click(function () {
            close()
        })

        //遮罩层点击 => 关闭
        $('.overlay').click(function () {
            close()
        })

        //下一页点击
        $($('.pre-right')[0]).click(function () {
            next()
        })

        //上一页点击
        $($('.pre-left')[0]).click(function () {
            pre()
        })

        /**
         * 关闭
         * */
        function close() {
            $('.overlay').remove()
            $('.pre-img').remove()
            $('#closeSytle').remove()
            $($('.pre-left')[0]).remove()
            $($('.pre-right')[0]).remove()
            $("body").css("overflow", "auto");
        }

        /**
         * 前一张
         * */
        function pre() {
            if (currentIndex > 0) {
                currentIndex--
            }
            $($('.detail-img')[0]).attr('src', srcList[currentIndex])
        }

        /**
         * 下一张
         * */
        function next() {
            if (currentIndex < srcList.length - 1) {
                currentIndex++
            }
            $($('.detail-img')[0]).attr('src', srcList[currentIndex])
        }
    }
    //判断是否相同值存在
    common.isHadSameFieldArray = function (arry, type) {
        var isHaveSame = false;
        for (var i = 0; i < arry.length; i++) {
            for (var j = i + 1; j < arry.length; j++) {
                if (arry[i][type] == arry[j][type]) {
                    isHaveSame = true;
                    break;
                }
            }
            if (isHaveSame) break;
        }
        return isHaveSame;
    }
    // 平台图片地址格式化
    common.listTransformImgSrc = function (list) {
        for (var listIndex = 0; listIndex < list.length; listIndex++) {
            var listItem = list[listIndex];
            if (listItem.IsRelationBaseProduct) {
                listItem.ImgUrl = commonModule.newTransformImgSrc(listItem.ImgUrl, '&businessType=baseproduct');
                if (listItem.BaseProductInfo && listItem.BaseProductInfo.ProductImgUrl) {
                    listItem.BaseProductInfo.ProductImgUrl = commonModule.newTransformImgSrc(listItem.BaseProductInfo.ProductImgUrl, '&businessType=baseproduct');
                }
                if (listItem.PtProductInfo && listItem.PtProductInfo.ProductImgUrl) {
                    listItem.PtProductInfo.ProductImgUrl = commonModule.newTransformImgSrc(listItem.PtProductInfo.ProductImgUrl, '&businessType=baseproduct');
                }
                listItem.ProductImgUrl = commonModule.newTransformImgSrc(listItem.ProductImgUrl, '&businessType=baseproduct');
            }
        }
        return list
    }

    // 输入框，输入长度限制，最大可输入值为9999999.99，只能输入正整数或者小数，最多只能小数点后两位
    common.validateInputNumLen = function (value) {
        var regex = /^([0-9]{1,7}(\.[0-9]{0,2})?)$/;
        return regex.test(value);
    }


    // 监听页面鼠标点击、键盘输入、页面滚动、触屏操作等交互行为 
    common.monitoringWithoutOperation = function (s) {
        var timer = null;
        var refreshDailog = null;
        document.addEventListener('click',throttle(onOperation,1000))
        document.addEventListener('keydown',throttle(onOperation,1000))
        document.addEventListener('scroll',throttle(onOperation,1000))
        document.addEventListener('touchstart',throttle(onOperation,1000))
        document.addEventListener('mousemove',throttle(onOperation,1000))
        
        // 页面操作监听
        var onOperation = function () {
            var s = 1000 * 60 * 15; // 15分钟
            if (timer != null) clearTimeout(timer);
            timer = setTimeout(function () {
                onRefresh(); // 刷新页面
                clearTimeout(timer);
                timer = null;
            }, s);
        }
        onOperation(); // 页面加载时执行一次
        function throttle(fn, interval) {
            var lastTime = 0;
            return function() {
              var nowTime = new Date().getTime();
              var remainTime = interval - (nowTime - lastTime);
              if (remainTime <= 0) {
                onOperation();
                lastTime = nowTime;
              }
            };
        }
        
        // 检测用户是否长时间停留在页面提示
        function onRefresh() {
            common.LoadCommonSetting("/ErpWeb/SetInfo/ExpiredReminderKey", true, function (rsp) {
                rsp.Data = rsp.Data === null || (rsp.Data && rsp.Data == 'true') ? 'true' : false;
                if (rsp.Success && rsp.Data && rsp.Data == 'true') {
                    if (refreshDailog || $('#refreshDailog').length > 0){
                        layer.close(refreshDailog);
                        refreshDailog = null;
                    }
                    var html = '<div class="wu-f14 wu-c09">您的页面停留时间较长，数据可能已更新，刷新页面可获取最新订单信息。</div>';
                    refreshDailog = layer.open({
                        type: 1,
                        title: '是否刷新页面？', //不显示标题
                        content: html,
                        area: '500px', //宽高
                        skin: 'wu-dailog',
                        id: 'refreshDailog',
                        success: function () { },
                        btn: ['刷新页面', '不刷新'],
                        success: function (index, layero) {
                        },
                        btn2: function (index, layero) {
                            layer.close(index); //如果设定了yes回调，需进行手工关闭
                        },
                        btn1: function (index, layero) {
                            window.location.reload();
                        },
                    });
                }
            })
        }
    }
    // 删除浏览器地址栏参数
    common.removeQueryParam = function(paramKey, url = window.location.href) {
        var urlParam = url.substring(url.indexOf("?"),url.length );   //页面参数
        var beforeUrl = url.substr(0, url.indexOf("?"));   //页面主地址（参数之前地址）
        var nextUrl = "";

        var arr = new Array();
        if (urlParam != "") {
            var urlParamArr = urlParam.split("&"); //将参数按照&符分成数组
            for (var i = 0; i < urlParamArr.length; i++) {
                var paramArr = urlParamArr[i].split("="); //将参数键，值拆开
                //如果键雨要删除的不一致，则加入到参数中
                if (paramArr[0] != paramKey) {
                    arr.push(urlParamArr[i]);
                }
            }
        }
        if (arr.length > 0) {
            nextUrl = "?" + arr.join("&");
        }
        url = beforeUrl + nextUrl;
        return url;
    }
    
    //新旧面单切换强引导需求弹窗
    common.CheckNewXiaoHongShuWindow = function () {
        var nowDate = new Date();
        //04-10后不再弹框
        if (nowDate > new Date('2025-04-10'))
            return;
        var updatelocalStorage = function (obj) {
            localStorage.setItem("CheckNewXiaoHongShuWindowTip", JSON.stringify(obj));
        }


        var isNeedCheck = true;
        var localData = localStorage.getItem("CheckNewXiaoHongShuWindowTip") || null;
        if (localData) {
            var localJson = JSON.parse(localData);
            //一天只弹出1次或者勾选了不再提醒，永久不提醒
            if (localJson.IsNeverShow || new Date(localJson.setTime).Format("yyyy-MM-dd") == nowDate.Format("yyyy-MM-dd")) {
                isNeedCheck = false;
            }
        }
        //用户已经选择了不提示，则不需要检查，不弹框
        if (isNeedCheck == false) return;

        var openWindow = function () {
            var contentHtml = '<div style="margin: 20px 15px;font-size: 14px;">';
            contentHtml += '<p style="padding-bottom: 25px;"><i style="display: inline-block;height:26px;width:26px;border-radius:26px;text-align: center;line-height:26px;background-color: #ff5b28;color: #fff;font-size: 18px;margin-right: 3px;">!</i><span style="font-weight: 800;font-size: 16px;">小红书旧版电子面单下线【重要通知】</span></p>';
            contentHtml += '<p style="font-weight: 600;">切换要求</p>';
            contentHtml += '<ul style="margin-left: 45px;">';
            contentHtml += '<li style="list-style:circle">请所有合作发货小红书订单的厂家以及小红书商家<span style="font-weight: 600;">务必于2025年4月1日前</span>完成新版电子面单系统的切换配置。</li>';
            contentHtml += '<li style="list-style:circle">代发商家需同步转告合作发货厂家，确保双方系统对接顺利过渡。</li>';
            contentHtml += '</ul>';
            contentHtml += '<p style="font-weight: 600;">影响程度</p>';
            contentHtml += '<ul style="margin-left: 45px;">';
            contentHtml += '<li style="list-style:circle">逾期未完成切换的打单用户，将无法通过旧版接口获取电子面单号，可能导致<span style="font-weight: 600;">发货延迟或取号失败</span>。</li>';
            contentHtml += '<li style="list-style:circle">因未及时切换导致的发货超时问题，电商平台将<span style="font-weight: 600;">照常执行罚款正常且不予申诉</span>,相关责任由商家自行承担。</li>';
            contentHtml += '</ul>';
            contentHtml += '<div style="padding-top:45px;padding-bottom:15px;"><label><input type="checkbox" id="checkXhsTemplateNotip" style="margin-right: 7px;">不再提醒</label>';
            contentHtml += '<span style="float:right;padding: 3px 8px;border: 1px solid #3aadff;margin-right: 5px;width: 145px;align-items: center;display: grid;justify-content: space-evenly;border-radius: 3px;background-color: #3751d7;"><a style="color: #fff;" href="https://school.xiaohongshu.com/lesson/normal/ba4395753d134b2c95b2c9b0db0f715f?jumpFrom=school&uba_pre=8.xhsschool_search_list.school_search_card.1734486745919&uba_ppre=8.school_rule_detail..1734486729715&uba_index=3" target="_blank" style="color:#3aadff">查看新版面单教程</a></span>';
            contentHtml += '<span style="float:right;padding: 3px 8px;border: 1px solid #3aadff;margin-right: 25px;width: 145px;align-items: center;display: grid;justify-content: space-evenly;border-radius: 3px;"><a href="https://wpa1.qq.com/biYkihFL?_type=wpa&qidian=true" target="_blank">联系客服咨询</a></span>';
            contentHtml += '</div>'
            contentHtml += '</div> ';

            var newXhsCheckTip = layer.open({
                type: 1,
                title: "重要通知",
                content: contentHtml,
                area: ['1090', '700'],
                success: function () {
                    //弹框后 更新localStorage，今天内只弹出一次 
                    var obj = null;
                    if (localData) {
                        obj = JSON.parse(localData);
                        obj.setTime = new Date();
                    }
                    if (obj == null) {

                        var obj = {
                            setTime: new Date(),
                            Name: "CheckNewXiaoHongShuWindowTip"
                        };
                    }
                    updatelocalStorage(obj);
                },
                cancel: function () {
                    //关闭按钮动作，检查【不再提醒】
                    var ck = $("#checkXhsTemplateNotip").is(":checked");
                    if (ck) {
                        var obj = {
                            setTime: new Date(),
                            Name: "CheckNewXiaoHongShuWindowTip",
                            IsNeverShow: true
                        };
                        updatelocalStorage(obj);
                    }
                    layer.close(newXhsCheckTip);
                }

            });
        }
        //检查用户数据
        common.Ajax({
            url: '/api/Common/CheckNewXiaoHongShuTemplate',
            type: "POST",
            //loading: true,
            success: function (rsp) {
                if (rsp.Success) {
                    var isNeedShow = rsp.Data;
                    if (isNeedShow) {
                        openWindow();
                    }
                }
            }
        });
        //openWindow();
    }
    return common;
}(commonModule || {}, jQuery, layer));


/*---------------String原型扩展 BEGIN ---------------*/
String.prototype.trim = function () {
    var str = this.replace(/(^\s*)|(\s*$)/g, '');
    return str;
}

//去除字符串头部空格或指定字符
String.prototype.trimStartDgj = function (c) {
    if (c == null || c == "") {
        var str = this.replace(/^\s*/, '');
        return str;
    }
    else {
        var rg = new RegExp("^" + c + "*");
        var str = this.replace(rg, '');
        return str;
    }
}
//去除字符串尾部空格或指定字符
String.prototype.trimEndDgj = function (c) {
    if (c == null || c == "") {
        var str = this;
        var rg = /\s/;
        var i = str.length;
        while (rg.test(str.charAt(--i)));
        return str.slice(0, i + 1);
    }
    else {
        var stripscript = function (c) {
            var ts = ['^', '$', '.', '*', '+', '-', '?', '=', '!', ':', '|', '\\', '/', '(', ')', '[', ']', '{', '}']
            //var pattern = new RegExp("^$.*+-?=!:|\/()[]{}")
            var rs = "";
            for (var i = 0; i < c.length; i++) {
                var char = c.substr(i, 1);
                for (var j = 0; j < ts.length; j++) {
                    if (char == ts[j])
                        char = '\\' + char;
                }
                rs += char;
            }
            return rs;
        }
        var str = this;
        c = stripscript(c);
        var rg = new RegExp(c);
        var i = str.length;
        while (rg.test(str.charAt(--i)));
        return str.slice(0, i + 1);
    }
}

//去除字符串尾部空格或指定字符
String.prototype.trimEndArrDgj = function (arr) {
    var str = this;
    if (!arr || arr.length == 0)
        return str;
    for (var i = 0; i < arr.length; i++) {
        var c = arr[i];
        if (typeof c == "string") {
            str = str.trimEndDgj(c);
        }
    }
    return str;
}
//去除字符串尾部空格或指定字符
String.prototype.trimEndArrOnceDgj = function (arr) {
    var str = this;
    if (!arr || arr.length == 0)
        return str;
    for (var i = 0; i < arr.length; i++) {
        var c = arr[i];
        if (typeof c == "string" && str.indexOf(c) != -1) {
            str = str.trimEndDgj(c);
            break;
        }
    }
    return str;
}
String.prototype.mineTrimStart = function (trimStr) {
    if (!trimStr) { return this; }
    var temp = this;
    while (true) {
        if (temp.substr(0, trimStr.length) != trimStr) {
            break;
        }
        temp = temp.substr(trimStr.length);
    }
    return temp;
};
String.prototype.mineTrimEnd = function (trimStr) {
    if (!trimStr) { return this; }
    var temp = this;
    while (true) {
        if (temp.substr(temp.length - trimStr.length, trimStr.length) != trimStr) {
            break;
        }
        temp = temp.substr(0, temp.length - trimStr.length);
    }
    return temp;
};

// 正则替换所有字符
//String.prototype.replaceAll = function (oldstr, newstr) {
//    var reg = new RegExp(oldstr, "g"); //创建正则RegExp对象   
//    if (oldstr == ".")
//        reg = new RegExp(/\./, "g");
//    else if (oldstr == "$")
//        reg = new RegExp(/\$/, "g");
//    else if (oldstr == "*")
//        reg = new RegExp(/\*/, "g");
//    else if (oldstr == "+")
//        reg = new RegExp(/\+/, "g");
//    else if (oldstr == "[")
//        reg = new RegExp(/\[/, "g");
//    else if (oldstr == "]")
//        reg = new RegExp(/\]/, "g");
//    else if (oldstr == "?")
//        reg = new RegExp(/\?/, "g");
//    else if (oldstr == "\\")
//        reg = new RegExp(/\\/, "g");
//    else if (oldstr == "^")
//        reg = new RegExp(/\^/, "g");
//    else if (oldstr == "{")
//        reg = new RegExp(/\{/, "g");
//    else if (oldstr == "}")
//        reg = new RegExp(/\}/, "g");
//    else if (oldstr == "|")
//        reg = new RegExp(/|}/, "g");
//    return this.replace(reg, newstr);
//}

// 正则替换所有字符
String.prototype.replaceAll = function (oldstr, newstr) {
    return this.split(oldstr).join(newstr);
}

//计算字符串长度(英文占1个字符，中文汉字占2个字符)
String.prototype.gblen = function () {
    var len = 0;
    for (var i = 0; i < this.length; i++) {
        if (this.charCodeAt(i) > 127 || this.charCodeAt(i) == 94) {
            len += 2;
        } else {
            len++;
        }
    }
    return len;
}

//多单号查询分隔符号转换[，；; \t\r\n]-->[,]
String.prototype.multipleQueryFormatString = function () {
    var v = this;
    v = v.replace(/[，；; \t\r\n]/g, ",");
    v = v.replace(/,+/g, ",").trimStartDgj(',').trimEndDgj(',');
    return v;
}
/*--------------- String原型扩展 END ---------------*/


/*---------------Date原型扩展 BEGIN ---------------*/

/*日期时间脚本库方法列表：
Date.prototype.isLeapYear 判断闰年
Date.prototype.Format 日期格式化
Date.prototype.Format2 日期格式化
Date.prototype.DateAdd 日期计算
Date.prototype.DateDiff 比较日期差
Date.prototype.toString 日期转字符串
Date.prototype.toArray 日期分割为数组
Date.prototype.DatePart 取日期的部分信息
Date.prototype.MaxDayOfDate 取日期所在月的最大天数
Date.prototype.WeekNumOfYear 判断日期所在年的第几周
StringToDate 字符串转日期型
IsValidDate 验证日期有效性
CheckDateTime 完整日期时间检查
daysBetween 日期天数差
*/

//---------------------------------------------------
// 对Date的扩展，将 Date 转化为指定格式的String   
// 月(M)、日(d)、小时(h)、分(m)、秒(s)、季度(q) 可以用 1-2 个占位符，   
// 年(y)可以用 1-4 个占位符，毫秒(S)只能用 1 个占位符(是 1-3 位的数字)   
// 例子：   
// (new Date()).Format("yyyy-MM-dd hh:mm:ss.S") ==> 2006-07-02 08:09:04.423   
// (new Date()).Format("yyyy-M-d h:m:s.S")      ==> 2006-7-2 8:9:4.18  
//---------------------------------------------------
Date.prototype.Format = function (fmt) { //author: meizz   
    fmt = !fmt ? "yyyy-MM-dd hh:mm:ss" : fmt;
    var o = {
        "M+": this.getMonth() + 1,               //月份   
        "d+": this.getDate(),                    //日   
        "h+": this.getHours(),                   //小时   
        "m+": this.getMinutes(),                 //分   
        "s+": this.getSeconds(),                 //秒   
        "q+": Math.floor((this.getMonth() + 3) / 3), //季度   
        "S": this.getMilliseconds()             //毫秒   
    };
    if (/(y+)/.test(fmt))
        fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
    for (var k in o)
        if (new RegExp("(" + k + ")").test(fmt))
            fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
    return fmt;
}

//---------------------------------------------------
// 日期格式化
// 格式 YYYY/yyyy/YY/yy 表示年份
// MM/M 月份
// W/w 星期
// dd/DD/d/D 日期
// hh/HH/h/H 时间
// mm/m 分钟
// ss/SS/s/S 秒
//---------------------------------------------------
Date.prototype.Format2 = function (formatStr) {
    var str = formatStr;
    var Week = ['日', '一', '二', '三', '四', '五', '六'];

    str = str.replace(/yyyy|YYYY/, this.getFullYear());
    str = str.replace(/yy|YY/, (this.getYear() % 100) > 9 ? (this.getYear() % 100).toString() : '0' + (this.getYear() % 100));

    str = str.replace(/MM/, this.getMonth() > 9 ? this.getMonth().toString() : '0' + this.getMonth());
    str = str.replace(/M/g, this.getMonth());

    str = str.replace(/w|W/g, Week[this.getDay()]);

    str = str.replace(/dd|DD/, this.getDate() > 9 ? this.getDate().toString() : '0' + this.getDate());
    str = str.replace(/d|D/g, this.getDate());

    str = str.replace(/hh|HH/, this.getHours() > 9 ? this.getHours().toString() : '0' + this.getHours());
    str = str.replace(/h|H/g, this.getHours());
    str = str.replace(/mm/, this.getMinutes() > 9 ? this.getMinutes().toString() : '0' + this.getMinutes());
    str = str.replace(/m/g, this.getMinutes());

    str = str.replace(/ss|SS/, this.getSeconds() > 9 ? this.getSeconds().toString() : '0' + this.getSeconds());
    str = str.replace(/s|S/g, this.getSeconds());

    return str;
}

//---------------------------------------------------
// 判断闰年
//---------------------------------------------------
Date.prototype.isLeapYear = function () {
    return (0 == this.getYear() % 4 && ((this.getYear() != 0) || (this.getYear() == 0)));
}

////+---------------------------------------------------
////| 求两个时间的天数差 日期格式为 YYYY-MM-dd
////+---------------------------------------------------
//function daysBetween(DateOne, DateTwo) {
//    var OneMonth = DateOne.substring(5, DateOne.lastIndexOf('-'));
//    var OneDay = DateOne.substring(DateOne.length, DateOne.lastIndexOf('-') + 1);
//    var OneYear = DateOne.substring(0, DateOne.indexOf('-'));

//    var TwoMonth = DateTwo.substring(5, DateTwo.lastIndexOf('-'));
//    var TwoDay = DateTwo.substring(DateTwo.length, DateTwo.lastIndexOf('-') + 1);
//    var TwoYear = DateTwo.substring(0, DateTwo.indexOf('-'));

//    var cha = ((Date.parse(OneMonth + '/' + OneDay + '/' + OneYear) - Date.parse(TwoMonth + '/' + TwoDay + '/' + TwoYear)) / 86400000);
//    return Math.abs(cha);
//}


//+---------------------------------------------------
//| 日期计算
//+---------------------------------------------------
Date.prototype.DateAdd = function (strInterval, Number) {
    var dtTmp = this;
    switch (strInterval) {
        case 's': return new Date(Date.parse(dtTmp) + (1000 * Number));
        case 'n': return new Date(Date.parse(dtTmp) + (60000 * Number));
        case 'h': return new Date(Date.parse(dtTmp) + (3600000 * Number));
        case 'd': return new Date(Date.parse(dtTmp) + (86400000 * Number));
        case 'w': return new Date(Date.parse(dtTmp) + ((86400000 * 7) * Number));
        case 'q': return new Date(dtTmp.getFullYear(), (dtTmp.getMonth()) + Number * 3, dtTmp.getDate(), dtTmp.getHours(), dtTmp.getMinutes(), dtTmp.getSeconds());
        case 'm': return new Date(dtTmp.getFullYear(), (dtTmp.getMonth()) + Number, dtTmp.getDate(), dtTmp.getHours(), dtTmp.getMinutes(), dtTmp.getSeconds());
        case 'y': return new Date((dtTmp.getFullYear() + Number), dtTmp.getMonth(), dtTmp.getDate(), dtTmp.getHours(), dtTmp.getMinutes(), dtTmp.getSeconds());
    }
}

//+---------------------------------------------------
//| 比较日期差 dtEnd 格式为日期型或者 有效日期格式字符串
//+---------------------------------------------------
Date.prototype.DateDiff = function (strInterval, dtEnd) {
    var dtStart = this;
    if (typeof dtEnd == 'string')//如果是字符串转换为日期型
    {
        dtEnd = commonModule.StringToDate(dtEnd);
    }
    switch (strInterval) {
        case 's': return parseInt((dtEnd - dtStart) / 1000);
        case 'n': return parseInt((dtEnd - dtStart) / 60000);
        case 'h': return parseInt((dtEnd - dtStart) / 3600000);
        case 'd': return parseInt((dtEnd - dtStart) / 86400000);
        case 'w': return parseInt((dtEnd - dtStart) / (86400000 * 7));
        case 'm': return (dtEnd.getMonth() + 1) + ((dtEnd.getFullYear() - dtStart.getFullYear()) * 12) - (dtStart.getMonth() + 1);
        case 'y': return dtEnd.getFullYear() - dtStart.getFullYear();
    }
}

//+---------------------------------------------------
//| 日期合法性验证
//| 格式为：YYYY-MM-DD或YYYY/MM/DD
//+---------------------------------------------------
//function IsValidDate(DateStr) {
//    var sDate = DateStr.replace(/(^\s+|\s+$)/g, ''); //去两边空格;
//    if (sDate == '') return true;
//    //如果格式满足YYYY-(/)MM-(/)DD或YYYY-(/)M-(/)DD或YYYY-(/)M-(/)D或YYYY-(/)MM-(/)D就替换为''
//    //数据库中，合法日期可以是:YYYY-MM/DD(2003-3/21),数据库会自动转换为YYYY-MM-DD格式
//    var s = sDate.replace(/[\d]{ 4,4 }[\-/]{ 1 }[\d]{ 1,2 }[\-/]{ 1 }[\d]{ 1,2 }/g, '');
//    if (s == '') //说明格式满足YYYY-MM-DD或YYYY-M-DD或YYYY-M-D或YYYY-MM-D
//    {
//        var t = new Date(sDate.replace(/\-/g, '/'));
//        var ar = sDate.split(/[-/:]/);
//        if (ar[0] != t.getYear() || ar[1] != t.getMonth() + 1 || ar[2] != t.getDate()) {
//            //alert('错误的日期格式！格式为：YYYY-MM-DD或YYYY/MM/DD。注意闰年。');
//            return false;
//        }
//    }
//    else {
//        //alert('错误的日期格式！格式为：YYYY-MM-DD或YYYY/MM/DD。注意闰年。');
//        return false;
//    }
//    return true;
//}

////+---------------------------------------------------
////| 日期时间检查
////| 格式为：YYYY-MM-DD HH:MM:SS
////+---------------------------------------------------
//function CheckDateTime(str) {
//    var reg = /^(\d+)-(\d{ 1,2 })-(\d{ 1,2 }) (\d{ 1,2 }):(\d{ 1,2 }):(\d{ 1,2 })$/;
//    var r = str.match(reg);
//    if (r == null) return false;
//    r[2] = r[2] - 1;
//    var d = new Date(r[1], r[2], r[3], r[4], r[5], r[6]);
//    if (d.getFullYear() != r[1]) return false;
//    if (d.getMonth() != r[2]) return false;
//    if (d.getDate() != r[3]) return false;
//    if (d.getHours() != r[4]) return false;
//    if (d.getMinutes() != r[5]) return false;
//    if (d.getSeconds() != r[6]) return false;
//    return true;
//}

//+---------------------------------------------------
//| 把日期分割成数组
//+---------------------------------------------------
Date.prototype.toArray = function () {
    var myDate = this;
    var myArray = Array();
    myArray[0] = myDate.getFullYear();
    myArray[1] = myDate.getMonth();
    myArray[2] = myDate.getDate();
    myArray[3] = myDate.getHours();
    myArray[4] = myDate.getMinutes();
    myArray[5] = myDate.getSeconds();
    return myArray;
}

//+---------------------------------------------------
//| 取得日期数据信息
//| 参数 interval 表示数据类型
//| y 年 m月 d日 w星期 ww周 h时 n分 s秒
//+---------------------------------------------------
Date.prototype.DatePart = function (interval) {
    var myDate = this;
    var partStr = '';
    var Week = ['日', '一', '二', '三', '四', '五', '六'];
    switch (interval) {
        case 'y': partStr = myDate.getFullYear(); break;
        case 'm': partStr = myDate.getMonth() + 1; break;
        case 'd': partStr = myDate.getDate(); break;
        case 'w': partStr = Week[myDate.getDay()]; break;
        case 'ww': partStr = myDate.WeekNumOfYear(); break;
        case 'h': partStr = myDate.getHours(); break;
        case 'n': partStr = myDate.getMinutes(); break;
        case 's': partStr = myDate.getSeconds(); break;
    }
    return partStr;
}

//+---------------------------------------------------
//| 取得当前日期所在月的最大天数
//+---------------------------------------------------
Date.prototype.MaxDayOfDate = function () {
    var myDate = this;
    var ary = myDate.toArray();
    var date1 = (new Date(ary[0], ary[1] + 1, 1));
    var date2 = date1.dateAdd(1, 'm', 1);
    var result = dateDiff(date1.Format('yyyy-MM-dd'), date2.Format('yyyy-MM-dd'));
    return result;
}

//+---------------------------------------------------
//| 取得当前日期所在周是一年中的第几周
//+---------------------------------------------------
Date.prototype.WeekNumOfYear = function () {
    var myDate = this;
    var ary = myDate.toArray();
    var year = ary[0];
    var month = ary[1] + 1;
    var day = ary[2];
    document.write('< script language=VBScript\> \n');
    document.write('myDate = DateValue("' + month + ' - ' + day + ' - ' + year + '") \n');
    document.write('result = DatePart("ww", myDate) \n');
    document.write(' \n');
    return result;
}
//获取下一周（周一凌晨）的日期
Date.prototype.getNextMonday = function () {
    const today = this;
    const day = today.getDay(); // 获取当前星期（0-6，0代表周日）

    // 计算到下周一的偏移天数（如果今天是周一，则偏移7天；其他情况按公式计算）
    const offset = day === 1 ? 7 : (1 - day + 7) % 7;

    const nextMonday = new Date(today);
    nextMonday.setDate(today.getDate() + offset); // 设置日期偏移
    nextMonday.setHours(0, 0, 0, 0); // 可选：重置时间为00:00:00

    return nextMonday;
}

////+---------------------------------------------------
////| 字符串转成日期类型
////| 格式 MM/dd/YYYY MM-dd-YYYY YYYY/MM/dd YYYY-MM-dd
////+---------------------------------------------------
//function StringToDate(DateStr) {

//    var converted = Date.parse(DateStr);
//    var myDate = new Date(converted);
//    if (isNaN(myDate)) {
//        //var delimCahar = DateStr.indexOf('/')!=-1?'/':'-';
//        var arys = DateStr.split('-');
//        myDate = new Date(arys[0], --arys[1], arys[2]);
//    }
//    return myDate;
//}

/*--------------- Date原型扩展 END ---------------*/


//Object.prototype.clone = function () { var newObj = {}; for (var i in this) { if (typeof (this[i]) == 'object' || typeof (this[i]) == 'function') { newObj[i] = this[i].clone(); } else { newObj[i] = this[i]; } } return newObj; };

//Array.prototype.clone = function () { var newArray = []; for (var i = 0; i < this.length; i++) { if (typeof (this[i]) == 'object' || typeof (this[i]) == 'function') { newArray[i] = this[i].clone(); } else { newArray[i] = this[i]; } } return newArray; };

//Function.prototype.clone = function () { var that = this; var newFunc = function () { return that.apply(this, arguments); }; for (var i in this) { newFunc[i] = this[i]; } return newFunc; };


