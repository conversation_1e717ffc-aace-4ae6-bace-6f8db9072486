var newLeftNavModule = (function (module, common, $, ly) {


    var dgjLeftNavs = common.FenDanSystemNavs;


    var quickLeftNavs = common.NavQuickEntry; //快捷入口数据
    var initQuickLeftNavs = JSON.parse(JSON.stringify(quickLeftNavs));
    var quickLeftSort = commonModule.NavQuickEntrySort;//排序
    var _version = dgjLeftNavs[0].Version;

   
    function quickLeftNavsSort() {
        var newQuickLeftNavs = [];
        for (var i = 0; i < quickLeftSort.length; i++) {
            for (var j = 0; j < quickLeftNavs.length; j++) {
                if (quickLeftSort[i] == quickLeftNavs[j].NavCode) {
                    newQuickLeftNavs.push(quickLeftNavs[j]);
                }
            }
        }
        quickLeftNavs = newQuickLeftNavs;
        return quickLeftNavs;
    }
    var operateQuickLeftNavs = quickLeftNavsSort();
    

    module.renderLeftNavs = function (dgjLeftNavs) {  //渲染左边导航
        var dgjLeftNavs = dgjLeftNavs.sort(function (a, b) {
            return a.Sort - b.Sort;
        });
        for (var s = 0; s < dgjLeftNavs.length; s++) {
            var items = dgjLeftNavs[s].Children;
            items = items.sort(function (a, b) {
                return a.Sort - b.Sort;
            });
        }
        $("#old_dgjLeftNav").remove();
        $("#old_goBackNew").remove();
        renderLeftNavHtml(dgjLeftNavs, menuActive);
    }

    module.initRenderLeftNavs = function (dgjLeftNavs) {
        module.renderLeftNavs(dgjLeftNavs);
    }

    function renderLeftNavHtml(dgjLeftNavs, id) {
        //debugger;
        $("#dgjLeftNav_wrap .newChangeNav").remove();
        var activeChildren = [];
        var showChildren = [];
        //$("#layui_layout_admin").removeClass().addClass("layui-layout layui-layout-admin newVersionsLeftNav " + id);
        var html = "";
        html += '<div class="newChangeNav">';
        html += '<div class="newChangeNav-left">';
        html += '<ul class="newChangeNav-left-ul" id="newChangeNav_left_ul">';
        for (var i = 0; i < dgjLeftNavs.length; i++) {
            var url = common.rewriteUrl(dgjLeftNavs[i].Url);
            url = common.dbnameToAjaxUrl(url);//dbname
            html +=
                `<li onmouseenter="newLeftNavModule.onmouseoverShowNav.bind(this)()" data-id=${dgjLeftNavs[i].ClassName} class=${dgjLeftNavs[i].ClassName == id ? "active" : ""}>
									<a href="${url}">
										<i class="iconfont ${dgjLeftNavs[i].Icon}" style="font-size:${dgjLeftNavs[i].IconSize}"></i>
										<span>${dgjLeftNavs[i].Title}</span>
									</a>
								</li>`;
            if (dgjLeftNavs[i].Active) {
                activeChildren = dgjLeftNavs[i].Children;
            }
        }
        html += '</ul>';
        html += module.renderQuickLeftNav(quickLeftNavs);//渲染快捷入口导航
        html += '<div class="newChangeNav-left-goBack" onclick="commonModule.changeVersions(false)">切换旧版本</div>';
        html += '</div>';
        html += '</div>';
        $("#dgjLeftNav_wrap").append(html);
        $("#newChangeNav_left_down_navs ." + menuId).addClass("active");

        $("#dgjLeftNav_wrap").mouseleave(function () {
            $("#newChangeNav_right_active").remove();

        })


        $("#dgjLeftNav_wrap .newChangeNav-left-down").mouseover(function () {
            $("#newChangeNav_right_active").remove();
        })

        $("#dgjLeftNav_wrap .newChangeNav-left-ul").mouseover(function () {
            event.stopPropagation();
        })
        $("#dgjLeftNav_wrap #newChangeNav_right_active").mouseover(function () {
            event.stopPropagation();
        })
        $("#dgjLeftNav_wrap .newChangeNav-left-ul>li").each(function (index, item) {
            $(item).on("click", function () {
                var id = $(item).attr("data-id");
                for (var n = 0; n < dgjLeftNavs.length; n++) {
                    if (dgjLeftNavs[n].className == id) {
                        dgjLeftNavs[n].active = true;
                        for (var k = 0; k < dgjLeftNavs[n].children.length; k++) {
                            if (k == 0) {
                                dgjLeftNavs[n].children[k].active = true;
                            } else {
                                dgjLeftNavs[n].children[k].active = false;
                            }
                        }
                    } else {
                        dgjLeftNavs[n].active = false;
                    }
                }
                renderLeftNavHtml(dgjLeftNavs, id);
            })
        })

    }

    module.changeChildrenNav = function (className, childrenClassName) {
        for (var i = 0; i < dgjLeftNavs.length; i++) {
            if (dgjLeftNavs[i].className == className) {
                dgjLeftNavs[i].active = true;
                for (var j = 0; j < dgjLeftNavs[i].children.length; j++) {
                    if (dgjLeftNavs[i].children[j].className == childrenClassName) {
                        dgjLeftNavs[i].children[j].active = true
                    } else {
                        dgjLeftNavs[i].children[j].active = false;
                    }
                }
            } else {
                dgjLeftNavs[i].active = false;
            }
        }
        renderLeftNavHtml(dgjLeftNavs, className);
    }

    function renderDailogUpQuickNav() { //渲染快捷入口弹窗 上部分所有导航
        var html = "";
        /*var newDgjLeftNavs = dgjLeftNavs.filter(function (item, i) {
            return item.ClassName != "Workbench";
        })*/
        var newDgjLeftNavs = dgjLeftNavs;
        //console.log("newDgjLeftNavs", newDgjLeftNavs)


        for (var i = 0; i < newDgjLeftNavs.length; i++) {
            html += '<li class="quickNav-main-content-item ' + newDgjLeftNavs[i].ClassName + '">';
            html += '<span class="quickNav-main-content-item-tile">' + newDgjLeftNavs[i].Title + '</span>';
            html += '<ul class="quickNav-main-content-item-content">';
            for (var j = 0; j < newDgjLeftNavs[i].Children.length; j++) {
                html += '<li class="' + newDgjLeftNavs[i].Children[j].ClassName + '"><label><input class="checkbox-leftNav" data-id="' + newDgjLeftNavs[i].Children[j].NavCode + '" data-className="' + newDgjLeftNavs[i].Children[j].ClassName + '" type="checkbox" onchange=\'newLeftNavModule.changeQuickNav.bind(this)("' + newDgjLeftNavs[i].Children[j]
                    .ClassName + '")\'><span>' + newDgjLeftNavs[i].Children[j].Title + '</span></label></li>'
            }
            html += '</ul>';
            html += '</li>'
        }
        $("#quickNav_content").html(html);
        $("#quickNav_content .SystemSettings .PrintSetting").hide();

    }

    function renderDailogDownQuickNav(className) {
        var className = className ? className : "";
        var html = "";
        for (var i = 0; i < operateQuickLeftNavs.length; i++) {
            var active = "";
            if (operateQuickLeftNavs[i].ClassName == className) {
                active = "active";
            }
            html += '<li class="' + active + '" data-id="' + operateQuickLeftNavs[i].NavCode + '" draggable="true" ondragstart = "newLeftNavModule.onDragStart(event)" ondragover = "newLeftNavModule.onDragOver(event)" ondrop = "newLeftNavModule.onDrop(event)">';
            //html += '<i class="iconfont icon-down-fill leftIcon" onclick=\'newLeftNavModule.switchQuickNav("' + operateQuickLeftNavs[i].ClassName + '",false)\'></i>';
            html += '<span>' + operateQuickLeftNavs[i].Title + '</span>';
            //html += '<i class="iconfont icon-down-fill rightIcon" onclick=\'newLeftNavModule.switchQuickNav("' + operateQuickLeftNavs[i].ClassName + '",true)\'></i>';
            html += '</li>';
        }
        $("#quickNav_changede").html(html);

    }


    // 存放拖拽的元素
    var dragElement = null;


    module.onDragStart = function(e) {
        // 获取当前拖拽元素
        dragElement = e.currentTarget
    }

    module.onDragOver = function(e) {
        // 默认的当你dragover的时候会阻止你做drop的操作，所以需要取消这个默认
        e.preventDefault()
    }

    module.onDrop = function (e) {
        // 当拖动结束的时候，给拖动div所在的位置下面的div做drop事件
        let dropElement = e.currentTarget
        if (dragElement != null && dragElement != dropElement) {
            let wrapper = document.querySelector('#quickNav_changede');
            // 临时 div 用于存储 box
            let temp = document.createElement('li');
            // 添加 temp 到父元素 wrapper 中
            wrapper.appendChild(temp);
            // 交换
            wrapper.replaceChild(temp, dropElement);
            wrapper.replaceChild(dropElement, dragElement);
            wrapper.replaceChild(dragElement, temp);
            $("#quickNav_changede>li").removeClass("active");
            $(dragElement).addClass("active");
        }
    }




    module.changeQuickNav = function (className) { //渲染快捷入口弹窗 下部分所有导航
        var checked = this.checked;
        if (checked) {
            if (operateQuickLeftNavs.length > 7) {
                layer.msg("快捷入口最多设置8个常用功能点,已达到8个。")
                this.checked = false;
                return;
            }
            for (var i = 0; i < dgjLeftNavs.length; i++) {
                for (var j = 0; j < dgjLeftNavs[i].Children.length; j++) {
                    var hasQuickLeftNav = false;
                    for (var n = 0; n < operateQuickLeftNavs.length; n++) {
                        if (operateQuickLeftNavs[n].ClassName == className) {
                            hasQuickLeftNav = true;
                        }
                    }
                    if (dgjLeftNavs[i].Children[j].ClassName == className && !hasQuickLeftNav) {
                        operateQuickLeftNavs.push(dgjLeftNavs[i].Children[j]);
                    }
                }
            }
        } else {
            for (var k = 0; k < operateQuickLeftNavs.length; k++) {
                if (operateQuickLeftNavs[k].ClassName == className) {
                    operateQuickLeftNavs.splice(k, 1);
                }
            }
        }
        renderDailogDownQuickNav();
    }

    module.switchQuickNav = function (className, isTrue) {
        var index = 0;
        var switchIndex = 0;
        var tarObj = null;
        var switchObj = null;
        for (var i = 0; i < operateQuickLeftNavs.length; i++) {
            if (operateQuickLeftNavs[i].ClassName == className) {
               index = i;
                if (isTrue) {
                    if (i == operateQuickLeftNavs.length - 1) {
                        return;
                    }
                    switchIndex = i - 0 + 1;
                } else {
                    if (index == 0) {
                        return;
                    }
                    switchIndex = i - 1;
                }
            }
        }
        operateQuickLeftNavs[index] = operateQuickLeftNavs.splice(switchIndex, 1, operateQuickLeftNavs[index])[0];
        renderDailogDownQuickNav(className);
    }


    var isPromise = true;
    module.quickNavDailog = function () {
        if (!isPromise) {
            return;
        }       
        renderDailogUpQuickNav();
        renderDailogDownQuickNav();
        var quickNavDailog = layer.open({
            type: 1,
            title: "<span>快捷入口设置<i style='color:#f7a52f;font-size:14px;margin-left: 5px;'>(为方便操作，快捷入口最多可设置8个常用功能点)</i></span>", //不显示标题
            content: $('#quickNav-dailog'),
            area: '750', //宽高
            btn: ['确定', '取消'],
            success: function () {
                $("#quickNav_content .checkbox-leftNav").each(function (index, item) {
                    var id = $(item).attr("data-id");
                    for (var i = 0; i < quickLeftSort.length; i++) {
                        if (id == quickLeftSort[i]) {
                            item.checked = true;
                        }
                    }
                })
                isPromise = false;
            },
            yes: function () {
                isPromise = true;
                var navIds = [];
                $("#quickNav_changede>li").each(function (index, item) {
                    navIds.push($(item).attr("data-id"));
                })
                quickLeftSort = navIds
                var newSubmitQuickLeftNavs = [];
                for (var i = 0; i < navIds.length; i++) {
                    for (var j = 0; j < quickLeftNavs.length; j++) {
                        if (navIds[i] == quickLeftNavs[j].Id) {
                            newSubmitQuickLeftNavs.push(quickLeftNavs[j]);
                        }
                    }
                }
                operateQuickLeftNavs = newSubmitQuickLeftNavs;
                $("#newChangeNav_left_down").remove();
                $(".newChangeNav-left").append(module.renderQuickLeftNav(newSubmitQuickLeftNavs));
                $("#dgjLeftNav_wrap .newChangeNav-left-down").hover(function () {
                    $("#newChangeNav_right_active").remove();
                })
                
                var navCodes = [];

                navIds.forEach(function (o) {
                    navCodes.push(o +'-'+ _version);

                });
                //console.log(navCodes);
                //记录顺序
                common.Ajax({
                    url: '/Common/SeveQuickSort',
                    data: { "navIds": navCodes },
                    loading: true,
                    success: function (rsp) {
                        if (rsp && rsp.Success) {
                            layer.close(quickNavDailog);
                            location.reload();
                        } else {
                            layer.msg(rsp.Data || rsp.Message);
                        }
                    }
                });
            },
            cancel: function () {
                isPromise = true;
                operateQuickLeftNavs = quickLeftNavsSort();
            },
            btn2: function () {
                isPromise = true;
                operateQuickLeftNavs = quickLeftNavsSort();
            }
        });

    }

    module.renderQuickLeftNav = function (quickLeftNavs) {
        
        var operateType = commonModule.getQueryVariable('operateType') || '';
        if (operateType == 'profitStatement') {
            menuId = "ProfitStatisticsView_ProfitStatementIndex"
        } else if (operateType == 'CollectProduct') {
            menuId = "Product_Collect"
        } else if (operateType == 'CollectIndex') {
            menuId = "Product_box"
        } else if (operateType == 'GlobalProduct') {
            menuId = "Product_Global"
        }
        var html = "";
        html +=
            `<div class="newChangeNav-left-down" id="newChangeNav_left_down">
						<div class="newChangeNav-left-down-title">
						<img src="/Content/images/newLeftNav_moreNav.png" /><div class="newChangeNav-left-down-title-content">
                <span>快捷入口</span>`
        if (quickLeftNavs.length == 0) {
            html += `<i id="newChangeNav_left_icon" class="iconfont icon-jia-copy1" onclick="newLeftNavModule.quickNavDailog();commonModule.JsLogToMongoDB(\'导航主题_快捷入口+号点击\');"></i>`;
        } else {
            html += `<i id="newChangeNav_left_icon" class="iconfont icon-shezhi2" onclick="newLeftNavModule.quickNavDailog()"></i>`;
        }
        html +=`</div></div>
            <ul class="newChangeNav-left-down-navs" id="newChangeNav_left_down_navs">`;
        for (var j = 0; j < quickLeftNavs.length; j++) {
            var url = common.rewriteUrl(quickLeftNavs[j].Url);
            url = common.dbnameToAjaxUrl(url);//dbname
            var classActive = quickLeftNavs[j].ClassName == menuId ? " active" : "";
                if (quickLeftNavs[j].ClassName != "inviteDailog") {
                    html += `<li class="${quickLeftNavs[j].ClassName + classActive}" onclick="newLeftNavModule.changeQuickLeftNav.bind(this)()"><a href=${url}><i class="iconfont icon-zhuangtai"></i><s>${j + 1}</s><span title="${quickLeftNavs[j].Title}">${quickLeftNavs[j].Title}</span></a></li>`;
                } else {
                    html += `<li class="${quickLeftNavs[j].ClassName + classActive}"><a href="javascript:;" onclick="newLeftNavModule.inviteDailogShow()"><i class="iconfont icon-zhuangtai"></i><s>${j + 1}</s><span title="${quickLeftNavs[j].Title}">${quickLeftNavs[j].Title}</span></a></li>`;

                }

            }    
        html += `</ul></div>`;
        return html;
    }
    module.changeQuickLeftNav = function () {
        $("#newChangeNav_left_down_navs>li").removeClass("active");
        $("#newChangeNav_left_ul>li").removeClass("active");
        $("#newChangeNav_right_active li").removeClass("active");
        for (var i = 0; i < dgjLeftNavs.length; i++) {
            dgjLeftNavs[i].Active = false;
            for (var j = 0; j < dgjLeftNavs[i].Children.length; j++) {
                dgjLeftNavs[i].Children[j].Active = false;
            }
        }
        $(this).addClass("active");
    }


    module.onmouseoverShowNav = function () { //移上去出现的二级导航
        var hasActiveMouse = false;
        var id = $(this).attr("data-id");
        var showChildren = [];
        for (var m = 0; m < dgjLeftNavs.length; m++) {
            if (dgjLeftNavs[m].ClassName == id) {
                showChildren = dgjLeftNavs[m].Children;
                if (dgjLeftNavs[m].Active) {
                    hasActiveMouse = true
                } else {
                    hasActiveMouse = false
                }
            }
        }
        var hoverHtml = ""; 
        var activeHtml = "";

        var operateType = commonModule.getQueryVariable('operateType') || '';
        if (operateType == 'profitStatement') {
            menuId = "ProfitStatisticsView_ProfitStatementIndex"
        } else if (operateType == 'CollectProduct') {
            menuId = "Product_Collect"
        } else if (operateType == 'CollectIndex') {
            menuId = "Product_box"
        } else if (operateType == 'GlobalProduct') {
            menuId = "Product_Global"
        }

        hoverHtml += '<div class="newChangeNav-right newChangeNav-right-active" id="newChangeNav_right_active">';
        hoverHtml += '<ul class="newChangeNav-right-ul">';
        for (var n = 0; n < showChildren.length; n++) {
            var url = common.rewriteUrl(showChildren[n].Url);
            url = common.dbnameToAjaxUrl(url);//dbname
            if (showChildren[n].ClassName != "inviteDailog") {
                if (hasActiveMouse) {
                    var className = showChildren[n].active ? "active" : "";
                    hoverHtml += '<li onclick=\'newLeftNavModule.changeChildrenNav("' + id + '","' + showChildren[n].ClassName + '")\' class=' +
                        className + '><a href="' + url + '"><span>' + showChildren[n].Title +
                        '</span><i class="iconfont icon-down-fill"></i></a></li>';
                } else {
                    if (showChildren[n].ClassName == "ShopVideo_Index") {
                        hoverHtml += '<li class="' + showChildren[n].ClassName + '" onclick=\'newLeftNavModule.changeChildrenNav("' + id + '","' + showChildren[n].ClassName +
                            '")\'><a href="' + url + '"><span style="position:relative">' + showChildren[n].Title +
                            '<img class="new-iconHot" style="top:-10px;right:-15px;" src="/Content/images/hot-icon-2023-4-21.png" /></span><i class="iconfont icon-down-fill"></i></a></li>';

                    } else if (showChildren[n].ClassName == "ExportExcelIndex"){
                        hoverHtml += '<li onclick=\'commonModule.clearStorage("exportTaskType")\' class="' + showChildren[n].ClassName + '" onclick=\'newLeftNavModule.changeChildrenNav("' + id + '","' + showChildren[n].ClassName +
                            '")\'><a  href="' + url + '"><span>' + showChildren[n].Title +
                            '</span><i class="iconfont icon-down-fill"></i></a></li>';
                    } else if (showChildren[n].ClassName == "PrintSetting") {
                        hoverHtml += '<li class="' + showChildren[n].ClassName + '" onclick=\'newLeftNavModule.changeChildrenNav("' + id + '","' + showChildren[n].ClassName +
                            '")\'><a href="' + common.rewriteUrl(commonModule.PrintSettingUrl) + '"><span>' + showChildren[n].Title +
                            '</span><i class="iconfont icon-down-fill"></i></a></li>';
                    } else if (showChildren[n].ClassName == "ProfitStatisticsView_Index" || showChildren[n].ClassName == "ProfitStatisticsView_ProfitStatementIndex") {
                        hoverHtml += '<li class="' + showChildren[n].ClassName + '" onclick=\'newLeftNavModule.changeChildrenNav("' + id + '","' + showChildren[n].ClassName +
                            '")\'><a href="' + url + '"><span style="position:relative">' + showChildren[n].Title +
                            '<img class="layui-nav-item-free-icon" src="/Content/images/limited-time-free-1025.png" /></span><i class="iconfont icon-down-fill"></i></a></li>';
                    }
                    else {
                        hoverHtml += '<li class="' + showChildren[n].ClassName + '" onclick=\'newLeftNavModule.changeChildrenNav("' + id + '","' + showChildren[n].ClassName +
                            '")\'><a href="' + url + '"><span>' + showChildren[n].Title +
                            '</span><i class="iconfont icon-down-fill"></i></a></li>';
                    }
                }
            } else {
                hoverHtml += '<li onclick="newLeftNavModule.inviteDailogShow()" class="invite-item">' + showChildren[n].Title +'</li>';
            }
        }
        hoverHtml += '</ul>';
        hoverHtml += '</div>';
        if (id != "Workbench_none") {
            $("#dgjLeftNav_wrap .newChangeNav .newChangeNav-right-active").remove();
            $("#dgjLeftNav_wrap .newChangeNav").append(hoverHtml);
            $("#newChangeNav_right_active").css({ top: this.offsetTop + 40 });
            $("#newChangeNav_right_active ." + menuId).addClass("active");
        } else {
            $("#dgjLeftNav_wrap .newChangeNav .newChangeNav-right-active").remove();
        }
    }

    module.inviteDailogShow = function () {

        var checkResult = commonModule.CheckVirtualRegMobile();
        if (!checkResult) {
            return;
        }

        layer.open({
            type: 1,
            title: "扫码邀请注册，自动绑定合作",
            btn: ["关闭"],
            shadeClose: true,
            area: '500px',
            content: $(".inviteDailogWrap"),
            success: function () {
                $("#inviterQrCode_myAgentWrap").show();
                $("#inviterQrCode_mySupplierWrap").show();
            },
            cancel: function () {
            }
        });
    }



    return module;
}(newLeftNavModule || {}, commonModule, jQuery, layer));
$(function () {
    
    if (window.isIframePage != true && (menuId == "AllOrder" || menuId == "WaitOrder" || menuId == "Partner" || menuId == "WaybillCodeList")) {
        IfShopIsAuthExpiredThenAlert();
    }
    function IfShopIsAuthExpiredThenAlert(timeout) {
        if (!timeout || timeout <= 0)
            timeout = 1000;
        setTimeout(function () {
            // 当前时间
            var myDate = new Date();
            var cuurTimeStr = myDate.toLocaleDateString();
            var localStorageKey = "no-show-extime-" + commonModule.ShopName;
            var storageJson = localStorage.getItem(localStorageKey) || "";
            if (storageJson != '') {
                var extimeInfo = JSON.parse(storageJson)
                var days = ((Date.parse(cuurTimeStr) - Date.parse(extimeInfo.checkTime)) / (1 * 24 * 60 * 60 * 1000));
                var chaCount = Math.abs(days);
                if (chaCount <= 1) {
                    return;
                }
            }
            commonModule.LoadExpiredShop({
                shoptype: $("#ulShopType .active").attr("data-shopType"),
            }, function (rsp) {
                if (rsp.Success) {
                    var shops = JSON.parse(rsp.Data.expiredShops) || [];
                    if (rsp.Data.aboutCount && rsp.Data.aboutCount > 0 && menuId == "Partner") {
                        $("#layui_warnin").css({ display: "flex" });
                        $("#warnin_mun").text(rsp.Data.aboutCount);
                    }

                    if (commonModule && commonModule.IEBrowserVersion() != "-1") {
                        layer.closeAll();
                        if ($("#a_open_apppayurl").length > 0)
                            return;
                        var html = $("#not-support-ie-tmpl").html();
                        //layer.closeAll();
                        layer.open({
                            title: "提示：请更换浏览器",
                            offset: { "top": "100px" },
                            area: ["650px"],
                            shadowClose: false,
                            skin: 'wu-dailog',
                            content: html,
                            btn: []
                        });
                    }
                    else if (shops && shops.length > 0) {
                        var dialog = $.templates("#expired-shop-dialog-tmpl");
                        var html = dialog.render({ shops: shops });
                        var isExisitDialog = $("#auth-expired-dialog").length > 0;
                        if (isExisitDialog)
                            return;
                        if (commonModule.isMigrating)
                            return;
                        if ($("#a_open_apppayurl").length > 0)
                            return;
                        var exTimeDialog = layer.open({
                            type: 1,
                            title: "提示",
                            //offset: { "top": "100px" },
                            //closeBtn: 1,
                            btn: ["不再提示", "关闭"],
                            shadeClose: true,
                            area: ['620px', '380px'],
                            content: html,
                            skin: 'wu-dailog',
                            success: function () {
                                //$(".layui-layer-btn0").css({
                                //    "border-color": "#ecf0f5",
                                //    "background-color": "#FFF",
                                //    "color": "#000"
                                //});
                            },
                            yes: function () {
                                var obj = { "checkTime": cuurTimeStr, "bindCount": shops.length, "ShopName": commonModule.ShopName };
                                localStorage.setItem(localStorageKey, JSON.stringify(obj));
                                layer.close(exTimeDialog);
                            },
                            cancel: function () {

                            }
                        });

                        ////过期店铺为当前店铺，自动跳转到授权链接
                        //if (shops.length == 1 && shops[0].ShopName == commonModule.ShopName) {
                        //    var shop = shops[0];
                        //    setTimeout(function () {
                        //        //if (confirm("当前店铺【" + shop.ShopName + "】授权已过期\r\n为了不影响您的使用，请重新授权\r\n\r\确认重新登录授权吗？")) {
                        //        //    if (commonModule.IsTouTiaoXi()) {
                        //        //        reAuthorization(0);
                        //        //    }
                        //        //    else {
                        //        //        window.location.href = commonModule.CurrentPlatformAuthEntry + '?rp=' + commonModule.Token;
                        //        //    }
                        //        //}
                        //        var isExisitDialog = $("#auth-expired-dialog").length > 0;
                        //        if (isExisitDialog)
                        //            return;
                        //        if ($("#a_open_apppayurl").length > 0)
                        //            return;
                        //        if (commonModule.isMigrating)
                        //            return;
                        //        if (shop.IsAuthUrl) {
                        //            var authExprie = layer.confirm("当前店铺【" + shop.NickName + "】授权已过期，为了不影响您的使用，请重新授权。<div id='auth-expired-dialog'></div>", { icon: 3, title: '授权过期', btn: ['前往授权', '取消'] }, function () {
                        //                window.location.href = commonModule.CurrentPlatformAuthEntry + '?rp=' + commonModule.Token;
                        //                //if (commonModule.IsTouTiaoXi()) {
                        //                //    reAuthorization(0);
                        //                //}
                        //                //else {
                        //                //    window.location.href = commonModule.CurrentPlatformAuthEntry + '?rp=' + commonModule.Token;
                        //                //}
                        //            }, function () {
                        //                layer.close(authExprie);
                        //            });
                        //        }
                        //    }, timeout);
                        //}
                        //else {
                        //var dialog = $.templates("#expired-shop-dialog-tmpl");
                        //var html = dialog.render({ shops: shops });
                        //setTimeout(function () {
                        //    var isExisitDialog = $("#auth-expired-dialog").length > 0;
                        //    if (isExisitDialog)
                        //        return;
                        //    if (commonModule.isMigrating)
                        //        return;
                        //    if ($("#a_open_apppayurl").length > 0)
                        //        return;
                        //    layer.open({
                        //        type: 1,
                        //        title: "提示",
                        //        offset: { "top": "100px" },
                        //        //closeBtn: 1,
                        //        btn: ["不再提示", "关闭"],
                        //        shadeClose: true,
                        //        area: ['450px', '350px'],
                        //        content: html,
                        //        success: function () {
                        //            $(".layui-layer-btn0").css({
                        //                "border-color": "#ecf0f5",
                        //                "background-color": "#FFF",
                        //                "color": "#000"
                        //            });
                        //        },
                        //        yes: function () {
                        //            $.cookie('no-show-extime-dialog-tips', '1', { expires: 1 });
                        //            layer.closeAll();
                        //        },
                        //        cancel: function () {
                        //            layer.closeAll();
                        //        }
                        //    });
                        //}, timeout);
                        //}
                    }
                }
            });
        }, timeout);
    }
})