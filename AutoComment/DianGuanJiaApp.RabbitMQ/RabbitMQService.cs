using DianGuanJiaApp.RabbitMQ;
using System;
using RabbitMQ.Client.Events;
using System.Threading.Tasks;
using RabbitMQ.Client;
using DianGuanJiaApp.Utility;
using System.Collections.Generic;
using System.Threading;
using System.Linq;
using DianGuanJiaApp.Utility.Extension;
using System.ServiceProcess;
using DianGuanJiaApp.RabbitMQ.Config;
using System.Text;
using System.Collections.Concurrent;

namespace DianGuanJiaApp.RabbitMQ
{
    /// <summary>
    /// 
    /// </summary>
    public static class RabbitMQService
    {
        /// <summary>
        /// 1688订单消息描述
        /// </summary>
        public static MessageDescription AlibabaOrderMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "alibaba.order.message",
                    QueueName = "alibaba.order.message"
                };
            }
        }

        /// <summary>
        /// 1688订单消息描述-分单应用
        /// </summary>
        public static MessageDescription AlibabaFxOrderMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "alibaba.fxorder.message",
                    QueueName = "alibaba.fxorder.message"
                };
            }
        }

        /// <summary>
        /// 1688订单买家消息描述
        /// </summary>
        public static MessageDescription AlibabaBuyerFxOrderMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "alibaba.buyer.fxorder.message",
                    QueueName = "alibaba.buyer.fxorder.message"
                };
            }
        }

        /// <summary>
        /// 1688订单自动售后消息描述（厂家视角）
        /// </summary>
        public static MessageDescription AlibabaFxAutoAfterSaleMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "alibaba.fxautoaftersale.message",
                    QueueName = "alibaba.fxautoaftersale.message"
                };
            }
        }

        /// <summary>
        /// 分单系统-自动回流消息描述（发货人视角）
        /// </summary>
        public static MessageDescription FxSendHistoryReturnMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "fx.sendhistoryreturn.message",
                    QueueName = "fx.sendhistoryreturn.message"
                };
            }
        }

        /// <summary>
        /// 1688订单消息轻应用描述
        /// </summary>
        public static MessageDescription AlibabaQingOrderMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "alibaba.qingorder.message",
                    QueueName = "alibaba.qingorder.message"
                };
            }
        }

        /// <summary>
        /// 1688产品消息描述
        /// </summary>
        public static MessageDescription AlibabaProductMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "alibaba.product.message",
                    QueueName = "alibaba.product.message"
                };
            }
        }

        /// <summary>
        /// 1688产品消息描述-分单应用
        /// </summary>
        public static MessageDescription AlibabaFxProductMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "alibaba.fxproduct.message",
                    QueueName = "alibaba.fxproduct.message"
                };
            }
        }

        /// <summary>
        /// 1688采购金预充值支付结果
        /// </summary>
        public static MessageDescription AlibabaFxBatchFundChargeMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "alibaba.fx.batchfundcharge.message",
                    QueueName = "alibaba.fx.batchfundcharge.message"
                };
            }
        }

        /// <summary>
        /// 1688采购金预充值支付退款结果
        /// </summary>
        public static MessageDescription AlibabaFxRefundChargeMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "alibaba.fx.refundcharge.message",
                    QueueName = "alibaba.fx.refundcharge.message"
                };
            }
        }

        /// <summary>
        /// 1688订单批量支付结果消息
        /// </summary>
        public static MessageDescription AlibabaZkOrderBatchPayResultMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "alibaba.zkorder.batchpay.message",
                    QueueName = "alibaba.zkorder.batchpay.message"
                };
            }
        }

        #region 基础商品（分单系统）
        /// <summary>
        /// 基础商品相关消息-分单系统
        /// </summary>
        public static MessageDescription FxBaseProductMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "fx.baseproduct.message",
                    QueueName = "fx.baseproduct.message"
                };
            }
        }

        /// <summary>
        /// 基础商品-处理失败队列01
        /// 延迟5分钟消费
        /// </summary>
        public static MessageDescription FxBaseProductDelayMessageDescription01
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "fx.baseproduct.delay.message01",
                    QueueName = "fx.baseproduct.delay.message01",
                    IsDelayQueue = true,
                    XMessageTTL = 300000, //5分钟 = 5 * 60 * 1000ms = 300000ms
                    XDeadLetterExchange = "fx.baseproduct.message",
                    XDeadLetterRoutingKey = "fx.baseproduct.message"
                };
            }
        }

        /// <summary>
        /// 基础商品-处理失败队列02
        /// 延迟10分钟消费
        /// </summary>
        public static MessageDescription FxBaseProductDelayMessageDescription02
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "fx.baseproduct.delay.message02",
                    QueueName = "fx.baseproduct.delay.message02",
                    IsDelayQueue = true,
                    XMessageTTL = 600000, //10分钟 = 10 * 60 * 1000ms = 600000ms
                    XDeadLetterExchange = "fx.baseproduct.message",
                    XDeadLetterRoutingKey = "fx.baseproduct.message"
                };
            }
        }

        /// <summary>
        /// 基础商品-处理失败队列03
        /// 延迟360分钟消费
        /// </summary>
        public static MessageDescription FxBaseProductDelayMessageDescription03
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "fx.baseproduct.delay.message03",
                    QueueName = "fx.baseproduct.delay.message03",
                    IsDelayQueue = true,
                    XMessageTTL = ********, //360分钟 = 360 * 60 * 1000ms = ********ms
                    XDeadLetterExchange = "fx.baseproduct.message",
                    XDeadLetterRoutingKey = "fx.baseproduct.message"
                };
            }
        }

        /// <summary>
        /// 基础商品-处理失败队列04
        /// 延迟720分钟消费
        /// </summary>
        public static MessageDescription FxBaseProductDelayMessageDescription04
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "fx.baseproduct.delay.message04",
                    QueueName = "fx.baseproduct.delay.message04",
                    IsDelayQueue = true,
                    XMessageTTL = ********, //720分钟 = 360 * 60 * 1000ms = ********ms
                    XDeadLetterExchange = "fx.baseproduct.message",
                    XDeadLetterRoutingKey = "fx.baseproduct.message"
                };
            }
        }

        /// <summary>
        /// 基础商品-处理失败队列05
        /// 延迟1440分钟消费
        /// </summary>
        public static MessageDescription FxBaseProductDelayMessageDescription05
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "fx.baseproduct.delay.message05",
                    QueueName = "fx.baseproduct.delay.message05",
                    IsDelayQueue = true,
                    XMessageTTL = ********, //1440分钟 = 1440 * 60 * 1000ms = ********ms
                    XDeadLetterExchange = "fx.baseproduct.message",
                    XDeadLetterRoutingKey = "fx.baseproduct.message"
                };
            }
        }

        /// <summary>
        /// 基础商品-死信队列
        /// </summary>
        public static MessageDescription FxBaseProductDeadLetterMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "fx.baseproduct.deadletter.message",
                    QueueName = "fx.baseproduct.deadletter.message"
                };
            }
        }

        #endregion

        #region 利润统计（分单系统）
        /// <summary>
        /// 利润统计相关消息-分单系统
        /// </summary>
        public static MessageDescription FxProfitStatisticsMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "fx.profitstatistics.message",
                    QueueName = "fx.profitstatistics.message"
                };
            }
        }

        /// <summary>
        /// 利润统计-处理失败队列01
        /// 延迟5分钟消费
        /// </summary>
        public static MessageDescription FxProfitStatisticsMessageDescription01
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "fx.profitstatistics.delay.message01",
                    QueueName = "fx.profitstatistics.delay.message01",
                    IsDelayQueue = true,
                    XMessageTTL = 300000, //5分钟 = 5 * 60 * 1000ms = 300000ms
                    XDeadLetterExchange = "fx.profitstatistics.message",
                    XDeadLetterRoutingKey = "fx.profitstatistics.message"
                };
            }
        }

        /// <summary>
        /// 利润统计-处理失败队列02
        /// 延迟10分钟消费
        /// </summary>
        public static MessageDescription FxProfitStatisticsMessageDescription02
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "fx.profitstatistics.delay.message02",
                    QueueName = "fx.profitstatistics.delay.message02",
                    IsDelayQueue = true,
                    XMessageTTL = 600000, //10分钟 = 10 * 60 * 1000ms = 600000ms
                    XDeadLetterExchange = "fx.profitstatistics.message",
                    XDeadLetterRoutingKey = "fx.profitstatistics.message"
                };
            }
        }

        /// <summary>
        /// 利润统计-处理失败队列03
        /// 延迟360分钟消费
        /// </summary>
        public static MessageDescription FxProfitStatisticsMessageDescription03
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "fx.profitstatistics.delay.message03",
                    QueueName = "fx.profitstatistics.delay.message03",
                    IsDelayQueue = true,
                    XMessageTTL = ********, //360分钟 = 360 * 60 * 1000ms = ********ms
                    XDeadLetterExchange = "fx.profitstatistics.message",
                    XDeadLetterRoutingKey = "fx.profitstatistics.message"
                };
            }
        }

        /// <summary>
        /// 利润统计-处理失败队列04
        /// 延迟720分钟消费
        /// </summary>
        public static MessageDescription FxProfitStatisticsMessageDescription04
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "fx.profitstatistics.delay.message04",
                    QueueName = "fx.profitstatistics.delay.message04",
                    IsDelayQueue = true,
                    XMessageTTL = ********, //720分钟 = 360 * 60 * 1000ms = ********ms
                    XDeadLetterExchange = "fx.profitstatistics.message",
                    XDeadLetterRoutingKey = "fx.profitstatistics.message"
                };
            }
        }

        /// <summary>
        /// 利润统计-处理失败队列05
        /// 延迟1440分钟消费
        /// </summary>
        public static MessageDescription FxProfitStatisticsMessageDescription05
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "fx.profitstatistics.delay.message05",
                    QueueName = "fx.profitstatistics.delay.message05",
                    IsDelayQueue = true,
                    XMessageTTL = ********, //1440分钟 = 1440 * 60 * 1000ms = ********ms
                    XDeadLetterExchange = "fx.profitstatistics.message",
                    XDeadLetterRoutingKey = "fx.profitstatistics.message"
                };
            }
        }

        /// <summary>
        /// 利润统计-死信队列
        /// </summary>
        public static MessageDescription FxProfitStatisticsDeadLetterMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "fx.profitstatistics.deadletter.message",
                    QueueName = "fx.profitstatistics.deadletter.message"
                };
            }
        }

        #endregion

        ///// <summary>
        ///// 1688采购单订单轻应用消息描述
        ///// </summary>
        //public static MessageDescription AlibabaQingOrderPayMessageDescription
        //{
        //    get
        //    {
        //        return new MessageDescription
        //        {
        //            ExchangeName = "alibaba.qingorder.pay.message",
        //            QueueName = "alibaba.qingorder.pay.message"
        //        };
        //    }
        //}

        /// <summary>
        /// 1688订单消息描述
        /// </summary>
        public static MessageDescription AlibabaZkOrderMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "alibaba.zkorder.message",
                    QueueName = "alibaba.zkorder.message"
                };
            }
        }

        /// <summary>
        /// 1688产品消息描述
        /// </summary>
        public static MessageDescription AlibabaZkProductMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "alibaba.zkproduct.message",
                    QueueName = "alibaba.zkproduct.message"
                };
            }
        }
        /// <summary>
        /// 有赞订单消息描述
        /// </summary>
        public static MessageDescription YouZanOrderMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "youzan.order.message",
                    QueueName = "youzan.order.message"
                };
            }
        }
        /// <summary>
        /// 有赞产品消息描述
        /// </summary>
        public static MessageDescription YouZanProductMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "youzan.product.message",
                    QueueName = "youzan.product.message"
                };
            }
        }

        /// <summary>
        /// 1688 c2m订单消息描述
        /// </summary>
        public static MessageDescription AlibabaC2MOrderMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "alibaba.c2m.order.message",
                    QueueName = "alibaba.c2m.order.message"
                };
            }
        }
        
        /// <summary>
        /// 1688 c2m订单消息描述（分单）
        /// </summary>
        public static MessageDescription AlibabaC2MFxOrderMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "alibaba.c2m.fxorder.message",
                    QueueName = "alibaba.c2m.fxorder.message"
                };
            }
        }

        /// <summary>
        /// 拼多多物流轨迹消息
        /// </summary>
        public static MessageDescription PinduoduoTraceMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "pdd.trace.message",
                    QueueName = "pdd.trace.message"
                };
            }
        }

        /// <summary>
        /// 拼多多订单消息
        /// </summary>
        public static MessageDescription PinduoduoOrderMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "pdd.order.message",
                    QueueName = "pdd.order.message"
                };
            }
        }

        /// <summary>
        /// 拼多多代打订单消息描述
        /// </summary>
        public static MessageDescription PddFDSOrderMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "pddfds.order.message",
                    QueueName = "pddfds.order.message"
                };
            }
        }

        /// <summary>
        /// 拼多多订单消息
        /// </summary>
        public static MessageDescription PddFxOrderDelayMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "pdd.fxorder.delay.message",
                    QueueName = "pdd.fxorder.delay.message",
                    IsDelayQueue = true,
                    XMessageTTL = 30000, //30s
                    XDeadLetterExchange = "pdd.fxorder.message",
                    XDeadLetterRoutingKey = "pdd.fxorder.message"
                };
            }
        }

        /// <summary>
        /// 拼多多分单系统订单消息描述
        /// </summary>
        public static MessageDescription PddFxOrderMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "pdd.fxorder.message",
                    QueueName = "pdd.fxorder.message"
                };
            }
        }
        /// <summary>
        /// 拼多多分单系统服务承诺消息描述
        /// </summary>
        public static MessageDescription PddFxChatMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "pdd.fxchat.message",
                    QueueName = "pdd.fxchat.message"
                };
            }
        }

        /// <summary>
        /// 1688 c2m产品消息描述
        /// </summary>
        public static MessageDescription AlibabaC2MProductMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "alibaba.c2m.product.message",
                    QueueName = "alibaba.c2m.product.message"
                };
            }
        }

        /// <summary>
        /// 团好货订单消息描述
        /// </summary>
        public static MessageDescription TuanHaoHuoOrderMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "tuanhaohuo.order.message",
                    QueueName = "tuanhaohuo.order.message"
                };
            }
        }

        /// <summary>
        /// 团好货产品消息描述
        /// </summary>
        public static MessageDescription TuanHaoHuoProductMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "tuanhaohuo.product.message",
                    QueueName = "tuanhaohuo.product.message"
                };
            }
        }

        #region 头条打印应用的消息
        /// <summary>
        /// 头条订单消息描述
        /// </summary>
        public static MessageDescription TouTiaoOrderMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "toutiao.order.new.message",
                    QueueName = "toutiao.order.new.message"
                };
            }
        }

        /// <summary>
        /// 1688采购单订单轻应用消息描述（头条消费）
        /// </summary>
        public static MessageDescription TouTiaoQingOrderPayMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "toutiao.qingorder.pay.message",
                    QueueName = "toutiao.qingorder.pay.message"
                };
            }
        }

        /// <summary>
        /// 头条订单标签消息
        /// </summary>
        public static MessageDescription TouTiaoOrderTagMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "toutiao.orderTag.message",
                    QueueName = "toutiao.orderTag.message"
                };
            }
        }

        /// <summary>
        /// 头条修改收件人消息描述
        /// </summary>
        public static MessageDescription TouTiaoModifyReceiverMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "toutiao.modifyreciver.message",
                    QueueName = "toutiao.modifyreciver.message"
                };
            }
        }

        /// <summary>
        /// 头条产品消息描述
        /// </summary>
        public static MessageDescription TouTiaoProductMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "toutiao.product.message",
                    QueueName = "toutiao.product.message"
                };
            }
        }

        /// <summary>
        /// 售后小助手
        /// 头条未发货前申请退款消息
        /// </summary>
        public static MessageDescription TouTiaoNoSendRefundMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "toutiao.nosendrefund.message",
                    QueueName = "toutiao.nosendrefund.message"
                };
            }
        }
        #endregion

        #region 头条分单应用的消息
        /// <summary>
        /// 头条订单消息描述
        /// </summary>
        public static MessageDescription TouTiaoFxOrderMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "toutiao.fxorder.message",
                    QueueName = "toutiao.fxorder.message"
                };
            }
        }
        /// <summary>
        /// 头条订单消息描述
        /// </summary>
        public static MessageDescription TouTiaoZkOrderMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "toutiao.zkorder.message",
                    QueueName = "toutiao.zkorder.message"
                };
            }
        }

        /// <summary>
        /// 头条订单标签消息
        /// </summary>
        public static MessageDescription TouTiaoFxOrderTagMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "toutiao.fxordertag.message",
                    QueueName = "toutiao.fxordertag.message"
                };
            }
        }

        /// <summary>
        /// 头条修改收件人消息描述
        /// </summary>
        public static MessageDescription TouTiaoFxModifyReceiverMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "toutiao.fxmodifyreciver.message",
                    QueueName = "toutiao.fxmodifyreciver.message"
                };
            }
        }

        /// <summary>
        /// 头条产品消息描述
        /// </summary>
        public static MessageDescription TouTiaoFxProductMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "toutiao.fxproduct.message",
                    QueueName = "toutiao.fxproduct.message"
                };
            }
        }

        /// <summary>
        /// 售后小助手
        /// [分销]头条未发货前申请退款消息
        /// </summary>
        public static MessageDescription TouTiaoFxNoSendRefundMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "toutiao.fxnosendrefund.message",
                    QueueName = "toutiao.fxnosendrefund.message"
                };
            }
        }
        #endregion

        #region 头条厂家代打应用的消息
        /// <summary>
        /// 头条厂家代打订单消息描述
        /// </summary>
        public static MessageDescription TouTiaoFFOrderMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "toutiao.fforder.message",
                    QueueName = "toutiao.fforder.message"
                };
            }
        }
        /// <summary>
        /// 头条厂家代打订单取消分配消息描述
        /// </summary>
        public static MessageDescription TouTiaoFFDistributionCancelMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "toutiao.fforder.distribution.cancel.message",
                    QueueName = "toutiao.fforder.distribution.cancel.message"
                };
            }
        }

        /// <summary>
        /// 头条厂家代打订单回传/取消回传消息描述
        /// </summary>
        public static MessageDescription TouTiaoFFOrderReturnStatusMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "toutiao.fforder.return.status.message",
                    QueueName = "toutiao.fforder.return.status.message"
                };
            }
        }
        #endregion

        #region 快手打单应用的消息
        /// <summary>
        /// 快手厂商订单消息描述
        /// </summary>
        public static MessageDescription KuaiShouSupplierOrderMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "kuaishousupplier.order.message",
                    QueueName = "kuaishousupplier.order.message"
                };
            }
        }
        /// <summary>
        /// 快手订单消息描述
        /// </summary>
        public static MessageDescription KuaiShouOrderMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "kuaishou.order.message",
                    QueueName = "kuaishou.order.message"
                };
            }
        }

        /// <summary>
        /// 快手产品消息描述
        /// </summary>
        public static MessageDescription KuaiShouProductMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "kuaishou.product.message",
                    QueueName = "kuaishou.product.message"
                };
            }
        }

        /// <summary>
        /// 快手订单地址变更描述
        /// </summary>
        public static MessageDescription KuaiShouOrderAddressChangeMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "kuaishou.order.address.change.message",
                    QueueName = "kuaishou.order.address.change.message"
                };
            }
        }
        #endregion

        #region 快手分单 分销管理应用的消息
        /// <summary>
        /// 快手订单消息描述-分单分销管理
        /// 打单发货类目
        /// </summary>
        public static MessageDescription KuaiShouFxOrderMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "kuaishou.fxorder.message",
                    QueueName = "kuaishou.fxorder.message"
                };
            }
        }

        /// <summary>
        /// 快手产品消息描述-分单分销管理
        /// 打单发货类目
        /// </summary>
        public static MessageDescription KuaiShouFxProductMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "kuaishou.fxproduct.message",
                    QueueName = "kuaishou.fxproduct.message"
                };
            }
        }
        #endregion

        #region 快手分单 分销代发应用的消息
        /// <summary>
        /// 快手订单消息描述-分销代发应用
        /// 厂商代发类目
        /// </summary>
        public static MessageDescription KuaiShouNewFxOrderMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "kuaishou.newfxorder.message",
                    QueueName = "kuaishou.newfxorder.message"
                };
            }
        }

        /// <summary>
        /// 快手产品消息描述-分销代发应用
        /// 厂商代发类目
        /// </summary>
        public static MessageDescription KuaiShouNewFxProductMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "kuaishou.newfxproduct.message",
                    QueueName = "kuaishou.newfxproduct.message"
                };
            }
        }
        /// <summary>
        /// 快手订单地址变更描述
        /// </summary>
        public static MessageDescription KuaiShouFxOrderAddressChangeMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "kuaishou.fxorder.address.change.message",
                    QueueName = "kuaishou.fxorder.address.change.message"
                };
            }
        }
        #endregion

        #region 快手主客应用的消息
        /// <summary>
        /// 快手订单消息描述
        /// </summary>
        public static MessageDescription KuaiShouZkOrderMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "kuaishou.zkorder.message",
                    QueueName = "kuaishou.zkorder.message"
                };
            }
        }

        /// <summary>
        /// 快手产品消息描述
        /// </summary>
        public static MessageDescription KuaiShouZkProductMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "kuaishou.zkproduct.message",
                    QueueName = "kuaishou.zkproduct.message"
                };
            }
        }
        #endregion

        #region 小红书消息

        /// <summary>
        /// 小红书订单消息描述
        /// </summary>
        public static MessageDescription XiaoHongShuOrderMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "xiaohongshu.order.message",
                    QueueName = "xiaohongshu.order.message"
                };
            }
        }

        /// <summary>
        /// 小红书产品消息描述
        /// </summary>
        public static MessageDescription XiaoHongShuProductMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "xiaohongshu.product.message",
                    QueueName = "xiaohongshu.product.message"
                };
            }
        }

        /// <summary>
        /// 小红书订单消息描述
        /// </summary>
        public static MessageDescription XiaoHongShuFxOrderMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "xiaohongshu.fxorder.message",
                    QueueName = "xiaohongshu.fxorder.message"
                };
            }
        }

        /// <summary>
        /// 小红书产品消息描述
        /// </summary>
        public static MessageDescription XiaoHongShuFxProductMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "xiaohongshu.fxproduct.message",
                    QueueName = "xiaohongshu.fxproduct.message"
                };
            }
        }

        #endregion

        /// <summary>
        /// 京东订单消息描述
        /// </summary>
        public static MessageDescription JingDongOrderMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "jingdong.order.message",
                    QueueName = "jingdong.order.message"
                };
            }
        }

        #region 微信视频号应用的消息

        /// <summary>
        /// 微信视频号订单其他消息
        /// </summary>
        public static MessageDescription WxVideoOrderextMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "wxvideo.order.extmessage",
                    QueueName = "wxvideo.order.extmessage"
                };
            }
        }

        /// <summary>
        /// 微信视频号订单其他消息 [分单]
        /// </summary>
        public static MessageDescription WxVideoOrderextFxMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "wxvideo.fxorder.extmessage",
                    QueueName = "wxvideo.fxorder.extmessage"
                };
            }
        }
        #endregion

        #region 淘宝 应用的消息
        /// <summary>
        /// 淘宝 处理打单的订单消息
        /// </summary>
        public static MessageDescription TaobaoOrderMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "taobao.order.message",
                    QueueName = "taobao.order.message"
                };
            }
        }
        /// <summary>
        /// 淘宝 处理分单的订单消息
        /// </summary>
        public static MessageDescription TaobaoFxOrderMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "taobao.fxorder.message",
                    QueueName = "taobao.fxorder.message"
                };
            }
        }
        /// <summary>
        /// 订购记录订单相关
        /// </summary>
        public static MessageDescription TaobaoFuwuOrderMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "taobao.fuwu.order.message",
                    QueueName = "taobao.fuwu.order.message"
                };
            }
        }
        #endregion
        /// <summary>
        /// 淘宝买菜订单消息描述
        /// </summary>
        public static MessageDescription TaobaoMaiCaiOrderMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "taobaomaicai.order.message",
                    QueueName = "taobaomaicai.order.message"
                };
            }
        }
        #region 物流轨迹消息
        /// <summary>
        /// 中通轨迹消息描述
        /// </summary>
        public static MessageDescription ZTOTracesMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "zto.traces.message",
                    QueueName = "zto.traces.message"
                };
            }
        }

        /// <summary>
        /// 申通轨迹消息描述
        /// </summary>
        public static MessageDescription STOTracesMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "sto.traces.message",
                    QueueName = "sto.traces.message",
                };
            }
        }

        /// <summary>
        /// 圆通轨迹消息描述
        /// </summary>
        public static MessageDescription YTOTracesMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "yto.traces.message",
                    QueueName = "yto.traces.message",
                };
            }
        }

        /// <summary>
        /// 百世快递轨迹消息描述
        /// </summary>
        public static MessageDescription HTKYTracesMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "htky.traces.message",
                    QueueName = "htky.traces.message",
                };
            }
        }

        /// <summary>
        /// 韵达快递轨迹消息描述
        /// </summary>
        public static MessageDescription YunDaTracesMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "yunda.traces.message",
                    QueueName = "yunda.traces.message",
                };
            }
        }

        /// <summary>
        /// 德邦快递轨迹消息描述
        /// </summary>
        public static MessageDescription DBKDTracesMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "dbkd.traces.message",
                    QueueName = "dbkd.traces.message",
                };
            }
        }

        /// <summary>
        /// 众邮快递轨迹消息描述
        /// </summary>
        public static MessageDescription ZYKDTracesMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "zykd.traces.message",
                    QueueName = "zykd.traces.message",
                };
            }
        }

        /// <summary>
        /// 极兔快递轨迹消息描述
        /// </summary>
        public static MessageDescription JTKDTracesMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "jtkd.traces.message",
                    QueueName = "jtkd.traces.message",
                };
            }
        }

        /// <summary>
        /// 物流中心往各个站点推送的轨迹消息描述
        /// </summary>
        public static MessageDescription LogisticCenterPushTracesMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "logistic.traces.message",
                    QueueName = "logistic.traces.message"
                };
            }
        }

        #endregion

        /// <summary>
        /// 分单“数据库执行”日志
        /// </summary>
        public static MessageDescription FxDbExecuteLogMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "fx.dbexecutelog.message",
                    QueueName = "fx.dbexecutelog.message"
                };
            }
        }

        /// <summary>
        /// 打单“数据库执行”日志
        /// </summary>
        public static MessageDescription DbExecuteLogMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "dbexecutelog.message",
                    QueueName = "dbexecutelog.message"
                };
            }
        }

        #region 1688采购单发货队列

        /// <summary>
        /// 采购单发货的队列
        /// </summary>
        public static MessageDescription PurchaseOrderSendMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "purchaseordersend.message",
                    QueueName = "purchaseordersend.message"
                };
            }
        }

        /// <summary>
        /// 采购单发货死信队列
        /// </summary>
        public static MessageDescription PurchaseOrderSendDeadLetterMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "purchaseordersend.deadletter.message",
                    QueueName = "purchaseordersend.deadletter.message"
                };
            }
        }

        /// <summary>
        /// 1688采购订单发货失败队列01
        /// 延迟5分钟消费
        /// </summary>
        public static MessageDescription PurchaseOrderSendDelayMessageDescription01
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "purchaseordersend.delay.message01",
                    QueueName = "purchaseordersend.delay.message01",
                    IsDelayQueue = true,
                    XMessageTTL = 300000, //5分钟 = 60000s * 5 = 300000s
                    XDeadLetterExchange = "purchaseordersend.message",
                    XDeadLetterRoutingKey = "purchaseordersend.message"
                };
            }
        }

        /// <summary>
        /// 1688采购订单发货失败队列02
        /// 延迟10分钟消费
        /// </summary>
        public static MessageDescription PurchaseOrderSendDelayMessageDescription02
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "purchaseordersend.delay.message02",
                    QueueName = "purchaseordersend.delay.message02",
                    IsDelayQueue = true,
                    XMessageTTL = 600000, //10分钟 = 60000s * 10 = 600000s
                    XDeadLetterExchange = "purchaseordersend.message",
                    XDeadLetterRoutingKey = "purchaseordersend.message"
                };
            }
        }

        /// <summary>
        /// 1688采购订单发货失败队列03
        /// 延迟30分钟消费
        /// </summary>
        public static MessageDescription PurchaseOrderSendDelayMessageDescription03
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "purchaseordersend.delay.message03",
                    QueueName = "purchaseordersend.delay.message03",
                    IsDelayQueue = true,
                    XMessageTTL = 1800000, //30分钟 = 60000s * 30 = 1800000s
                    XDeadLetterExchange = "purchaseordersend.message",
                    XDeadLetterRoutingKey = "purchaseordersend.message"
                };
            }
        }

        /// <summary>
        /// 1688采购订单发货失败队列04
        /// 延迟60分钟消费
        /// </summary>
        public static MessageDescription PurchaseOrderSendDelayMessageDescription04
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "purchaseordersend.delay.message04",
                    QueueName = "purchaseordersend.delay.message04",
                    IsDelayQueue = true,
                    XMessageTTL = 3600000, //60分钟 = 60000s * 60 = 3600000s
                    XDeadLetterExchange = "purchaseordersend.message",
                    XDeadLetterRoutingKey = "purchaseordersend.message"
                };
            }
        }

        /// <summary>
        /// 1688采购订单发货失败队列05
        /// 延迟120分钟消费
        /// </summary>
        public static MessageDescription PurchaseOrderSendDelayMessageDescription05
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "purchaseordersend.delay.message05",
                    QueueName = "purchaseordersend.delay.message05",
                    IsDelayQueue = true,
                    XMessageTTL = 7200000, //120分钟 = 60000s * 120 = 7200000s
                    XDeadLetterExchange = "purchaseordersend.message",
                    XDeadLetterRoutingKey = "purchaseordersend.message"
                };
            }
        }


        /// <summary>
        /// TikTok订单取消消息描述
        /// </summary>
        public static MessageDescription TikTokOrderCancelledMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "tiktok.order.Cancelled.message",
                    QueueName = "tiktok.order.Cancelled.message"
                };
            }
        }

        /// <summary>
        /// TikTok订单主动同步消息
        /// </summary>
        public static MessageDescription TikTokOrderActiveSyncDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "tiktok.order.ActiveSync.message",
                    QueueName = "tiktok.order.ActiveSync.message"
                };
            }
        }

        /// <summary>
        /// TikTok订单发票状态=>仅针对巴西订单
        /// </summary>
        public static MessageDescription TikTokOrderInvoiceStatusChange
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "tiktok.order.InvoiceStatus.message",
                    QueueName = "tiktok.order.InvoiceStatus.message"
                };
            }
        }

        #endregion


        #region 更新1688采购订单状态队列
        /// <summary>
        /// 更新采购订单状态队列
        /// </summary>
        public static MessageDescription UpdatePurchaseStatusMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "updatepurchasestatus.message",
                    QueueName = "updatepurchasestatus.message"
                };
            }
        }
        #endregion

        #region 更新1688代销商品采购结算价队列
        /// <summary>
        /// 更新采购订单状态队列
        /// </summary>
        public static MessageDescription UpdatePurchaseSettlementPriceMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "alibaba.fxproduct.updatesettlementprice",
                    QueueName = "alibaba.fxproduct.updatesettlementprice"
                };
            }
        }
        #endregion


        #region 售后单处理库存消息
        /// <summary>
        /// 售后单处理库存消息
        /// </summary>
        public static MessageDescription AfterSaleOrderStockMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "fx.aftersaleorder.stock.message",
                    QueueName = "fx.aftersaleorder.stock.message"
                };
            }
        }
        #endregion

        #region 分单店铺授权消息
        public static MessageDescription FxShopAuthMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "fx.shop.auth.message",
                    QueueName = "fx.shop.auth.message"
                };
            }
        }
        #endregion

        #region 平台资料同步到货盘
        /// <summary>
        /// 平台资料同步到货盘队列
        /// </summary>
        public static MessageDescription FxPtProductInfoDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "fx.ptproductinfo.message",
                    QueueName = "fx.ptproductinfo.message"
                };
            }
        }
        #endregion

        #region 铺货完成后发送的消息
        /// <summary>
        /// 铺货完成后发送的消息
        /// </summary>
        public static MessageDescription FxListingCompleteDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "fx.listing.complete.message",
                    QueueName = "fx.listing.complete.message"
                };
            }
        }
        #endregion

        #region 爆品分析

        /// <summary>
        /// 爆品分析-分单系统
        /// </summary>
        public static MessageDescription FxHotProductMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "fx.statistics.ordersend.message",
                    QueueName = "fx.statistics.ordersend.message"
                };
            }
        }

        /// <summary>
        /// 爆品分析汇总消息-分单系统
        /// </summary>
        public static MessageDescription FxHotProductSummaryMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "fx.statistics.senddetail.insert",
                    QueueName = "fx.statistics.senddetail.insert"
                };
            }
        }
        #endregion 爆品分析

        #region 导出任务队列

        /// <summary>
        /// 正式环境——导出任务队列
        /// </summary>
        public static MessageDescription ExportTaskMessageReleaseDescription =>
            new MessageDescription
            {
                ExchangeName = "fendan.export.task.release",
                QueueName = "fendan.export.task.release"
            };

        /// <summary>
        /// 灰度1环境——导出任务队列
        /// </summary>
        public static MessageDescription ExportTaskMessageVersion1Description =>
            new MessageDescription
            {
                ExchangeName = "fendan.export.task.version1",
                QueueName = "fendan.export.task.version1"
            };

        /// <summary>
        /// 灰度3环境——导出任务队列
        /// </summary>
        public static MessageDescription ExportTaskMessageVersion3Description =>
            new MessageDescription
            {
                ExchangeName = "fendan.export.task.version3",
                QueueName = "fendan.export.task.version3"
            };

        /// <summary>
        /// 灰度6环境——导出任务队列
        /// </summary>
        public static MessageDescription ExportTaskMessageVersion6Description =>
            new MessageDescription
            {
                ExchangeName = "fendan.export.task.version6",
                QueueName = "fendan.export.task.version6"
            };

        /// <summary>
        /// 测试任务——导出任务队列
        /// </summary>
        public static MessageDescription ExportTaskMessageTestDescription =>
            new MessageDescription
            {
                ExchangeName = "fendan.export.task.test",
                QueueName = "fendan.export.task.test"
            };

        /// <summary>
        /// 根据版本获取导出任务队列
        /// </summary>
        /// <param name="version"></param>
        /// <returns></returns>
        public static MessageDescription GetExportTaskDesc(string version)
        {
            switch (version)
            {
                case "release":
                    return ExportTaskMessageReleaseDescription;
                case "1":
                case "version1":
                    return ExportTaskMessageVersion1Description;
                case "3":
                case "version3":
                    return ExportTaskMessageVersion3Description;
                case "6":
                case "version6":
                    return ExportTaskMessageVersion6Description;
                case "t":
                case "test":
                    return ExportTaskMessageTestDescription;
                default:
                    return ExportTaskMessageReleaseDescription;
            }
        }

        /// <summary>
        /// 获取导出程序的延迟队列描述
        /// </summary>
        /// <param name="version">版本号（如release、version1、version3、version6）</param>
        public static MessageDescription GetExportDelayMessageDescription(string version)
        {
            switch (version)
            {
                case "1": version = "version1"; break;
                case "3": version = "version3"; break;
                case "6": version = "version6"; break;
                case "t": version = "test"; break;
                default: version = "release"; break;
            }

            var queueName = $"fendan.export.task.{version}";
            return new MessageDescription
            {
                ExchangeName = "fendan.export.task.delay",
                QueueName = "fendan.export.task.delay",
                IsDelayQueue = true,
                XMessageTTL = 1800000, // 30分钟 = 30 * 60 * 1000ms = 1800000ms
                XDeadLetterExchange = queueName,
                XDeadLetterRoutingKey = queueName
            };
        }

        #endregion

        #region 对账程序队列

        public static MessageDescription FxSettlementTaskMessageReleaseeDescription =>
            new MessageDescription
            {
                ExchangeName = "fendan.settlement.task.release",
                QueueName = "fendan.settlement.task.release"
            };

        public static MessageDescription FxSettlementTaskMessageVersion1Description =>
            new MessageDescription
            {
                ExchangeName = "fendan.settlement.task.version1",
                QueueName = "fendan.settlement.task.version1"
            };

        public static MessageDescription FxSettlementTaskMessageVersion3Description =>
            new MessageDescription
            {
                ExchangeName = "fendan.settlement.task.version3",
                QueueName = "fendan.settlement.task.version3"
            };

        public static MessageDescription FxSettlementTaskMessageVersion6Description =>
            new MessageDescription
            {
                ExchangeName = "fendan.settlement.task.version6",
                QueueName = "fendan.settlement.task.version6"
            };

        public static MessageDescription FxSettlementMessageTestDescription =>
            new MessageDescription
            {
                ExchangeName = "fendan.settlement.task.test",
                QueueName = "fendan.settlement.task.test"
            };

        /// <summary>
        /// 根据版本获取对账任务队列
        /// </summary>
        /// <param name="version"></param>
        /// <returns></returns>
        public static MessageDescription GetFxSettlementTaskDesc(string version)
        {
            switch (version)
            {
                case "release":
                    return FxSettlementTaskMessageReleaseeDescription;
                case "1":
                case "version1":
                    return FxSettlementTaskMessageVersion1Description;
                case "3":
                case "version3":
                    return FxSettlementTaskMessageVersion3Description;
                case "6":
                case "version6":
                    return FxSettlementTaskMessageVersion6Description;
                case "t":
                case "test":
                    return FxSettlementMessageTestDescription;
                default:
                    return FxSettlementTaskMessageReleaseeDescription;
            }
        }

        /// <summary>
        /// 获取对账程序的延迟队列描述
        /// </summary>
        /// <param name="version">版本号（如release、version1、version3、version6）</param>
        public static MessageDescription GetFxSettlementDelayMessageDescription(string version)
        {
            switch (version)
            {
                case "1": version = "version1"; break;
                case "3": version = "version3"; break;
                case "6": version = "version6"; break;
                case "t": version = "test"; break;
                default: version = "release"; break;
            }

            string queueName = $"fendan.settlement.task.{version}";
            return new MessageDescription
            {
                ExchangeName = "fendan.settlement.task.delay",
                QueueName = "fendan.settlement.task.delay",
                IsDelayQueue = true,
                XMessageTTL = 1800000, // 30分钟 = 30 * 60 * 1000ms = 1800000ms
                XDeadLetterExchange = queueName,
                XDeadLetterRoutingKey = queueName
            };
        }

        #endregion

        #region 对账任务状态检查延迟队列

        public static MessageDescription FxSettlementTaskReviewMessageReleaseeDescription =>
          new MessageDescription
          {
              ExchangeName = "fendan.settlement.task.review.release",
              QueueName = "fendan.settlement.task.review.release",
          };
        public static MessageDescription FxSettlementTaskReviewMessageVersion1Description =>
           new MessageDescription
           {
               ExchangeName = "fendan.settlement.task.review.version1",
               QueueName = "fendan.settlement.task.review.version1",
           };
        public static MessageDescription FxSettlementTaskReviewMessageVersion3Description =>
           new MessageDescription
           {
               ExchangeName = "fendan.settlement.task.version3",
               QueueName = "fendan.settlement.task.review.version3",
           };
        public static MessageDescription FxSettlementTaskReviewMessageVersion6Description =>
           new MessageDescription
           {
               ExchangeName = "fendan.settlement.task.review.version6",
               QueueName = "fendan.settlement.task.review.version6",
           };
        public static MessageDescription FxSettlementTaskReviewMessageTestDescription =>
          new MessageDescription
          {
              ExchangeName = "fendan.settlement.task.review.test",
              QueueName = "fendan.settlement.task.review.test",
          };

        /// <summary>
        /// 根据版本获取对账任务检查队列
        /// </summary>
        /// <param name="version"></param>
        /// <returns></returns>
        public static MessageDescription GetFxSettlementTaskReviewDesc(string version)
        {
            switch (version)
            {
                case "release":
                    return FxSettlementTaskReviewMessageReleaseeDescription;
                case "1":
                case "version1":
                    return FxSettlementTaskReviewMessageVersion1Description;
                case "3":
                case "version3":
                    return FxSettlementTaskReviewMessageVersion3Description;
                case "6":
                case "version6":
                    return FxSettlementTaskReviewMessageVersion6Description;
                case "t":
                case "test":    
                    return FxSettlementTaskReviewMessageTestDescription;
                default:
                    return FxSettlementTaskReviewMessageReleaseeDescription;
            }
        }

        /// <summary>
        /// 根据版本获取对账任务延迟检查队列
        /// </summary>
        /// <param name="version"></param>
        /// <returns></returns>
        public static MessageDescription GetFxSettlementTaskDeadReviewDesc(string version)
        {
            switch (version)
            {
                case "1": version = "version1"; break;
                case "3": version = "version3"; break;
                case "6": version = "version6"; break;
                case "t": version = "test"; break;
                default: version = "release"; break;
            }

            var queueName = $"fendan.settlement.task.review.{version}";
            return new MessageDescription
            {
                ExchangeName = "fendan.settlement.task.dead.review",
                QueueName = "fendan.settlement.task.dead.review",
                IsDelayQueue = true,
                XMessageTTL = ********, // 3小时 =3 * 60 * 60 * 1000ms = ********ms
                // XMessageTTL = 60000, 本地测试用
                XDeadLetterExchange = queueName,
                XDeadLetterRoutingKey = queueName
            };
        }
        #endregion

        #region 站内信
        /// <summary>
        /// 站内信队列
        /// </summary>
        public static MessageDescription SiteMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "site.message",
                    QueueName = "site.message"
                };
            }
        }
        /// <summary>
        /// 站内信死信队列
        /// </summary>
        public static MessageDescription SiteMessageDeadLetterDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "site.deadletter.message",
                    QueueName = "site.deadletter.message"
                };
            }
        }

        /// <summary>
        /// 站内信推送失败队列01
        /// 延迟5分钟消费
        /// </summary>
        public static MessageDescription SiteMessageDeadLetterDescription01
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "site.delay.message01",
                    QueueName = "site.delay.message01",
                    IsDelayQueue = true,
                    XMessageTTL = 300000, //5分钟 = 60000s * 5 = 300000s
                    XDeadLetterExchange = "site.message",
                    XDeadLetterRoutingKey = "site.message"
                };
            }
        }

        /// <summary>
        /// 站内信推送失败队列02
        /// 延迟10分钟消费
        /// </summary>
        public static MessageDescription SiteMessageDeadLetterDescription02
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "site.delay.message02",
                    QueueName = "site.delay.message02",
                    IsDelayQueue = true,
                    XMessageTTL = 600000, //10分钟 = 60000s * 10 = 600000s
                    XDeadLetterExchange = "site.message",
                    XDeadLetterRoutingKey = "site.message"
                };
            }
        }

        /// <summary>
        /// 站内信推送失败队列03
        /// 延迟30分钟消费
        /// </summary>
        public static MessageDescription SiteMessageDeadLetterDescription03
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "site.delay.message03",
                    QueueName = "site.delay.message03",
                    IsDelayQueue = true,
                    XMessageTTL = 1800000, //30分钟 = 60000s * 30 = 1800000s
                    XDeadLetterExchange = "site.message",
                    XDeadLetterRoutingKey = "site.message"
                };
            }
        }

        /// <summary>
        /// 站内信推送失败队列04
        /// 延迟60分钟消费
        /// </summary>
        public static MessageDescription SiteMessageDeadLetterDescription04
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "site.delay.message04",
                    QueueName = "site.delay.message04",
                    IsDelayQueue = true,
                    XMessageTTL = 3600000, //60分钟 = 60000s * 60 = 3600000s
                    XDeadLetterExchange = "site.message",
                    XDeadLetterRoutingKey = "site.message"
                };
            }
        }

        /// <summary>
        /// 站内信推送失败队列05
        /// 延迟120分钟消费
        /// </summary>
        public static MessageDescription SiteMessageDeadLetterDescription05
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "site.delay.message05",
                    QueueName = "site.delay.message05",
                    IsDelayQueue = true,
                    XMessageTTL = 7200000, //120分钟 = 60000s * 120 = 7200000s
                    XDeadLetterExchange = "site.message",
                    XDeadLetterRoutingKey = "site.message"
                };
            }
        }
        #endregion


        /// <summary>
        /// 禾量平台订单消息描述
        /// </summary>
        public static MessageDescription HeliangOrderMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "heliang.order.message",
                    QueueName = "heliang.order.message"
                };
            }
        }

        /// <summary>
        /// 哔哩哔哩订单消息描述
        /// </summary>
        public static MessageDescription BiliBiliFxOrderMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "bilibili.fxorder.message",
                    QueueName = "bilibili.fxorder.message"
                };
            }
        }

        /// <summary>
        /// 发送消息
        /// </summary>
        /// <param name="message"></param>
        /// <param name="description"></param>
        /// <param name="tryCount"></param>
        /// <returns></returns>
        public static bool SendMessage(object message, MessageDescription desc, int tryCount = 1)
        {
            var isSend = RabbitMQChannelQueuePool.SendMessage(message, desc);
            if (isSend)
                return true;
            Log.WriteWarning($"消息队列池未找到，请检查，desc:{desc.QueueName}");
            return false;
        }

        public static bool SendMessage(object message, MessageDescription desc, MessageLinkService ms)
        {
            try
            {
                using (var im = RabbitMqClientFactory.CreateModel())
                {
                    desc.InitIModel(im);
                    var prs = im.CreateBasicProperties();
                    //序列化消息
                    var msg = Encoding.UTF8.GetBytes(message.ToJson());
                    SendMessageConfirm(desc, im, prs, msg, 0, ms);
                    im.BasicPublish(desc.ExchangeName, desc.RouterName, prs, msg);
                    var queueDeclare = im.QueueDeclarePassive(desc.QueueName);
                    var messageCount = queueDeclare.MessageCount;
                }
                return true;
            }
            catch (Exception ex)
            {
                ms.Production(ex);
                //Log.WriteError($"发送消息失败，队列名：{desc.QueueName}：消息内容：{message.ToJson()}\r\n 错误详情：{ex} \r\nInnerException：{ex?.InnerException}");
                return false;
            }
        }

        /// <summary>
        /// 确认发送消息
        /// </summary>
        /// <param name="desc"></param>
        /// <param name="channel"></param>
        /// <param name="prs"></param>
        /// <param name="msg"></param>
        /// <param name="trycount"></param>
        /// <param name="ms"></param>
        public static void SendMessageConfirm(MessageDescription desc, IModel channel, IBasicProperties prs, byte[] msg, int trycount, MessageLinkService ms)
        {
            ms.SetQueue(desc.QueueName);
            channel.ConfirmSelect();
            void cleanOutstandingConfirms(ulong sequenceNumber, bool multiple)
            {
                if (multiple)
                {
                    var confirmed = desc.outstandingConfirms.Where(k => k.Key <= sequenceNumber);
                    foreach (var entry in confirmed)
                        desc.outstandingConfirms.TryRemove(entry.Key, out _);
                }
                else
                    desc.outstandingConfirms.TryRemove(sequenceNumber, out _);
            }
            channel.BasicAcks += (sender, ea) =>
            {
                ms.Production();
                cleanOutstandingConfirms(ea.DeliveryTag, ea.Multiple);
            };
            channel.BasicNacks += (sender, ea) =>
            {
                if (ea.Multiple)
                {
                    var confirmed = desc.outstandingConfirms.Where(k => k.Key <= ea.DeliveryTag);
                    foreach (var entry in confirmed)
                    {
                        if (desc.outstandingConfirms.TryGetValue(entry.Key, out MessageDescription.ConfirmsModel nackbody))
                        {
                            if (nackbody.retry < trycount)
                            {
                                desc.outstandingConfirms[entry.Key].retry++;
                                channel.BasicPublish(desc.ExchangeName, desc.RouterName, prs, nackbody.msg);
                            }
                            else
                            {
                                cleanOutstandingConfirms(entry.Key, false);
                                ms.Production($"发送消息失败-重试后依然出错,队列名:{desc.QueueName}");
                                //Log.WriteError($"发送消息失败-重试后依然出错,队列名:{desc.QueueName},消息内容:{Encoding.UTF8.GetString(nackbody.msg)}");
                            }
                        }
                    }
                }
                else
                {
                    if (desc.outstandingConfirms.TryGetValue(ea.DeliveryTag, out MessageDescription.ConfirmsModel nackbody))
                    {
                        if (nackbody.retry < trycount)
                        {
                            desc.outstandingConfirms[ea.DeliveryTag].retry++;
                            channel.BasicPublish(desc.ExchangeName, desc.RouterName, prs, nackbody.msg);
                        }
                        else
                        {
                            cleanOutstandingConfirms(ea.DeliveryTag, false);
                            ms.Production($"发送消息失败-重试后依然出错,队列名:{desc.QueueName}");
                            //Log.WriteError($"发送消息失败-重试后依然出错,队列名:{desc.QueueName},消息内容:{Encoding.UTF8.GetString(nackbody.msg)}");
                        }
                    }
                }
            };
            desc.outstandingConfirms.TryAdd(channel.NextPublishSeqNo, new MessageDescription.ConfirmsModel { retry = 0, msg = msg });


        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="desc"></param>
        /// <param name="callback">入参：消息体，返回：处理是否成功</param>
        public static void AddListening(MessageDescription desc, Func<string, bool> callback)
        {
            try
            {
                var channel = RabbitMqClientFactory.CreateModel();
                desc.InitIModel(channel);
                var consumer = new EventingBasicConsumer(channel);
                consumer.Received += (ch, ea) =>
                {
                    var body = ea.Body;
                    var msg = Encoding.UTF8.GetString(body);
                    Log.Debug($"接收到消息：{msg}");
                    try
                    {
                        var isOk = callback(msg);
                        channel.BasicAck(ea.DeliveryTag, false);
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"消息处理失败，消息内容：{msg}：{ex}");
                    }
                };
            }
            catch (Exception ex)
            {
                Log.WriteError($"监听消息发生错误：{ex}");
            }
        }

        public static void PullMessage(MessageDescription desc, Func<List<string>, int, bool> callback)
        {
            try
            {
                var ch = RabbitMqClientFactory.CreateModel();
                desc.InitIModel(ch);
                var task = Task.Factory.StartNew((state) =>
                {
                    var channel = state as IModel;
                    var noAck = false;
                    var msgs = new List<string>();
                    var batchSize = 1;
                    BasicGetResult lastMessage = null;
                    while (true)
                    {
                        try
                        {
                            if (channel.IsClosed)
                            {
                                Log.WriteError($"监听队列时，连接意外关闭，队列：{desc.QueueName}，详细异常信息：{channel?.CloseReason?.ToJson()}");
                                channel = RabbitMqClientFactory.CreateModel();
                            }
                            lastMessage = TryGetMessage(desc, callback, channel, noAck, msgs, batchSize, lastMessage);
                        }
                        catch (Exception ex)
                        {
                            Thread.Sleep(new TimeSpan(0, 0, 2));
                            Log.WriteError($"尝试获取消息时发生异常，队列：{desc.QueueName}，详细异常信息：{ex}");
                        }
                    }
                }, ch, TaskCreationOptions.LongRunning);

            }
            catch (Exception ex)
            {
                Log.WriteError($"监听消息发生错误：{ex}");
            }
        }

        /// <summary>
        /// 消费者监听消息
        /// </summary>
        /// <typeparam name="TMessage"></typeparam>
        /// <param name="desc"></param>
        /// <param name="callback"></param>
        public static void ConsumerListenMessage<TMessage>(MessageDescription desc,
            Func<List<TMessage>, int, bool> callback)
            where TMessage : class
        {
            try
            {
                var ms = new MessageLinkService(desc.QueueName.Substring(0, desc.QueueName.IndexOf('.')), "");
                var ch = RabbitMqClientFactory.CreateModel();
                desc.InitIModel(ch);
                var consumer = new EventingBasicConsumer(ch);
                ch.BasicQos(0, 10, false);
                consumer.Received += (sender, arg) =>
                {
                    var ebc = sender as EventingBasicConsumer;
                    var body = arg.Body;
                    var msg = Encoding.UTF8.GetString(body);
                    ms.SetMessage(msg);
                    ms.SetQueue(desc.QueueName);
                    try
                    {
                        var result = callback(new List<TMessage> { msg.ToObject<TMessage>() },
                            (int)ebc.Model.MessageCount(desc.QueueName));
                        if (result)
                            ms.ConsumeAsyn();
                        else
                            ms.ConsumeAsyn("消费失败");
                        ebc.Model.BasicAck(arg.DeliveryTag, false);
                    }
                    catch (Exception ex)
                    {
                        //日志
                        Log.WriteError($"在消息事件中处理消息时发生错误，消息内容：{msg} 错误信息：{ex}",
                            $"MessageReceivedError_{DateTime.Now:yyyy-MM-dd}.log");
                        ms.ConsumeAsyn(ex);
                        ebc.Model.BasicAck(arg.DeliveryTag, false);
                    }
                    //desc.ResetEvent.WaitOne();
                };
                var flag = ch.BasicConsume(desc.QueueName, false, consumer);
                desc.ConsumerFlag = flag;
                consumer.Shutdown += (sender, arg) =>
                {
                    ms.ConsumeAsyn("消息接收被中断");
                    Log.WriteError($"消息接收被中断，发送者：{sender?.ToJson()}，参数：{arg?.ToJson()}");
                };
                //单元测试时使用
                //Console.ReadLine();
            }
            catch (Exception ex)
            {
                Log.WriteError($"ListenMessage监听消息发生错误：{ex}");
            }
        }

        public static void ListenMessage(MessageDescription desc, Func<List<string>, int, bool> callback, ushort prefetchCount = 10)
        {
            try
            {
                MessageLinkService ms = new MessageLinkService(desc.QueueName.Substring(0, desc.QueueName.IndexOf('.')), "");
                var ch = RabbitMqClientFactory.CreateModel();
                desc.InitIModel(ch);
                var consumer = new EventingBasicConsumer(ch);
                ch.BasicQos(0, prefetchCount, false);
                consumer.Received += (sender, arg) =>
                {
                    var ebc = sender as EventingBasicConsumer;
                    var body = arg.Body;
                    var msg = Encoding.UTF8.GetString(body);
                    ms.SetMessage(msg);
                    ms.SetQueue(desc.QueueName);
                    try
                    {
                        var result = callback(new List<string>() { msg }, (int)ebc.Model.MessageCount(desc.QueueName));
                        if (result)
                            ms.ConsumeAsyn();
                        else
                            ms.ConsumeAsyn("消费失败");
                        ebc.Model.BasicAck(arg.DeliveryTag, false);
                    }
                    catch (Exception ex)
                    {
                        ms.ConsumeAsyn(ex);
                        ebc.Model.BasicAck(arg.DeliveryTag, false);
                        //Log.WriteError($"在消息事件中处理消息时发生错误：{ex}");
                    }
                    //desc.ResetEvent.WaitOne();
                };
                var flag = ch.BasicConsume(desc.QueueName, false, consumer);
                desc.ConsumerFlag = flag;
                consumer.Shutdown += (sender, arg) =>
                {
                    ms.ConsumeAsyn("消息接收被中断");
                    Log.WriteError($"消息接收被中断，发送者：{sender?.ToJson()}，参数：{arg?.ToJson()}");
                };
            }
            catch (Exception ex)
            {
                Log.WriteError($"ListenMessage监听消息发生错误：{ex}");
            }
        }

        public static void ListenMessage(MessageDescription desc, Func<string, int, int> callback, ushort prefetchCount = 10)
        {
            try
            {
                MessageLinkService ms = new MessageLinkService(desc.QueueName.Substring(0, desc.QueueName.IndexOf('.')), "");
                var ch = RabbitMqClientFactory.CreateModel();
                desc.InitIModel(ch);
                var consumer = new EventingBasicConsumer(ch);
                ch.BasicQos(0, prefetchCount, false);
                consumer.Received += (sender, arg) =>
                {
                    var ebc = sender as EventingBasicConsumer;
                    var body = arg.Body;
                    var msg = Encoding.UTF8.GetString(body);
                    ms.SetMessage(msg);
                    ms.SetQueue(desc.QueueName);
                    try
                    {
                        var result = callback(msg, (int)ebc.Model.MessageCount(desc.QueueName));

                        if (result == 1)
                            ms.ConsumeAsyn();
                        else if (result == 0)
                            ms.ConsumeAsyn("消费失败");

                        if (result == 0 || result == 1) // result == 2/-1 时 不消费 忽略，且不应答
                            ch.BasicAck(arg.DeliveryTag, false);
                    }
                    catch (Exception ex)
                    {
                        ms.ConsumeAsyn(ex);
                        ebc.Model.BasicAck(arg.DeliveryTag, false);
                        //Log.WriteError($"在消息事件中处理消息时发生错误：{ex}");
                    }
                    //desc.ResetEvent.WaitOne();
                };
                var flag = ch.BasicConsume(desc.QueueName, false, consumer);
                desc.ConsumerFlag = flag;
                consumer.Shutdown += (sender, arg) =>
                {
                    ms.ConsumeAsyn("消息接收被中断");
                    Log.WriteError($"消息接收被中断，发送者：{sender?.ToJson()}，参数：{arg?.ToJson()}");
                };
            }
            catch (Exception ex)
            {
                Log.WriteError($"ListenMessage监听消息发生错误：{ex}");
            }
        }

        /// <summary>
        /// 增加了判断维护时间的监听
        /// </summary>
        /// <param name="desc"></param>
        /// <param name="callback"></param>
        /// <param name="maintenanceTimeCallBack"></param>
        public static void ListenMessageByMaintenanceTime(MessageDescription desc, Func<List<string>, int, bool> callback, Func<DateTime> maintenanceTimeCallBack)
        {
            try
            {
                MessageLinkService ms = new MessageLinkService(desc.QueueName.Substring(0, desc.QueueName.IndexOf('.')), "");
                var ch = RabbitMqClientFactory.CreateModel();
                desc.InitIModel(ch);
                var consumer = new EventingBasicConsumer(ch);
                ch.BasicQos(0, 10, false);
                consumer.Received += (sender, arg) =>
                {
                    var ebc = sender as EventingBasicConsumer;
                    var body = arg.Body;
                    var msg = Encoding.UTF8.GetString(body);
                    ms.SetMessage(msg);
                    ms.SetQueue(desc.QueueName);
                    try
                    {
                        var time = maintenanceTimeCallBack();
                        if (time > DateTime.Now)
                        {
                            // 如果维护时间大于当前时间，记录日志表示因处于维护时间暂停消费此消息
                            Log.WriteError($"即将维护，暂停消费消息，消息内容：{msg}");
                            return;
                        }
                        var result = callback(new List<string>() { msg }, (int)ebc.Model.MessageCount(desc.QueueName));
                        if (result)
                            ms.ConsumeAsyn();
                        else
                            ms.ConsumeAsyn("消费失败");
                        ebc.Model.BasicAck(arg.DeliveryTag, false);
                    }
                    catch (Exception ex)
                    {
                        ms.ConsumeAsyn(ex);
                        ebc.Model.BasicAck(arg.DeliveryTag, false);
                        //Log.WriteError($"在消息事件中处理消息时发生错误：{ex}");
                    }
                    //desc.ResetEvent.WaitOne();
                };
                var flag = ch.BasicConsume(desc.QueueName, false, consumer);
                desc.ConsumerFlag = flag;
                consumer.Shutdown += (sender, arg) =>
                {
                    ms.ConsumeAsyn("消息接收被中断");
                    Log.WriteError($"消息接收被中断，发送者：{sender?.ToJson()}，参数：{arg?.ToJson()}");
                };
            }
            catch (Exception ex)
            {
                Log.WriteError($"ListenMessage监听消息发生错误：{ex}");
            }
        }


        private static BasicGetResult TryGetMessage(MessageDescription desc, Func<List<string>, int, bool> callback, IModel channel, bool noAck, List<string> msgs, int batchSize, BasicGetResult lastMessage)
        {
            desc.ResetEvent.WaitOne();
            BasicGetResult result = null;
            if (msgs.Count() < batchSize)
                result = channel.BasicGet(desc.QueueName, noAck);
            if (result != null)
                lastMessage = result;
            if (result == null || msgs.Count() == batchSize)
            {
                //没有消息了，休息一会
                if (!msgs.Any())
                {
                    Thread.Sleep(500);
                }
                else
                {
                    var isok = callback(msgs, (int)lastMessage.MessageCount);
                    channel.BasicAck(lastMessage.DeliveryTag, true);
                    msgs.Clear();
                }
            }
            else
            {
                IBasicProperties props = result.BasicProperties;
                var body = result.Body;
                var msg = Encoding.UTF8.GetString(body);
                msgs.Add(msg);
            }

            return lastMessage;
        }

        /// <summary>
        /// 获取队列消息个数
        /// </summary>
        /// <param name="desc"></param>
        public static long GetQueueMessageCount(MessageDescription desc)
        {
            long count = -1;
            try
            {
                using (var channel = RabbitMqClientFactory.CreateModel())
                {
                    desc.InitIModel(channel);
                    var decalre = channel.QueueDeclarePassive(desc.QueueName);
                    count = decalre.MessageCount;
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"监听消息发生错误：{ex}");
            }
            return count;
        }

    }


    /// <summary>
    /// 消息描述信息
    /// </summary>
    public class MessageDescription
    {
        //存储发送但未收到ack回复的记录
        public ConcurrentDictionary<ulong, ConfirmsModel> outstandingConfirms = new ConcurrentDictionary<ulong, ConfirmsModel>();
        public class ConfirmsModel
        {
            public int retry { get; set; }
            public byte[] msg { get; set; }
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        public MessageDescription(string exchangeTypeName = null)
        {
            if (string.IsNullOrEmpty(exchangeTypeName))
                ExchangeTypeName = ExchangeType.Direct;
            else
                ExchangeTypeName = exchangeTypeName;
            ResetEvent = new ManualResetEvent(true);
        }

        private IModel _model;

        /// <summary>
        /// 交换类型名，发送类型
        /// </summary>
        public string ExchangeName { get; set; }
        /// <summary>
        /// 队列名
        /// </summary>
        public string QueueName { get; set; }
        /// <summary>
        /// 队列名
        /// </summary>
        public string RouterName
        {
            get
            {
                return QueueName;
            }
        }

        /// <summary>
        /// 
        /// </summary>
        public string ExchangeTypeName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="im"></param>
        /// <returns></returns>
        public IModel InitIModel(IModel im)
        {
            im.ExchangeDeclare(this.ExchangeName, this.ExchangeTypeName, true, false);
            if (IsDelayQueue)
            {
                var dict = new Dictionary<string, object>
                {
                    { "x-message-ttl", XMessageTTL },
                    { "x-dead-letter-exchange", XDeadLetterExchange },
                    { "x-dead-letter-routing-key", XDeadLetterRoutingKey }
                };
                im.QueueDeclare(this.QueueName, true, false, false, dict);
            }
            else
                im.QueueDeclare(this.QueueName, true, false, false);
            im.QueueBind(this.QueueName, this.ExchangeName, this.RouterName);
            _model = im;
            return im;
        }

        /// <summary>
        /// 线程中用于暂停和继续
        /// </summary>
        public ManualResetEvent ResetEvent { get; set; }

        public string ConsumerFlag { get; set; }

        public void CancelConsumer()
        {
            if (_model != null && !string.IsNullOrEmpty(ConsumerFlag))
            {
                _model.BasicCancel(ConsumerFlag);
            }
        }

        /// <summary>
        /// 是否支持消息过期
        /// </summary>
        public bool IsDelayQueue { get; set; }

        /// <summary>
        /// 消息过期时间
        /// 毫秒
        /// </summary>
        public long XMessageTTL { get; set; }

        /// <summary>
        /// 消息过期未消费时转发消息的交换机
        /// </summary>
        public string XDeadLetterExchange { get; set; }

        /// <summary>
        /// 消息过期未消费时转发消息的路由
        /// </summary>
        public string XDeadLetterRoutingKey { get; set; }

        /// <summary>
        /// 获取队列的消息数量
        /// </summary>
        /// <returns></returns>
        public long GetMessageCount()
        {
            var count = RabbitMQService.GetQueueMessageCount(this);
            return count;
        }
    }
}
