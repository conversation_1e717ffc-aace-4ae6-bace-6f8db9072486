using DianGuanJiaApp.Data.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Services.Model
{
    public class ListingProductEditResultModel
    {
        /// <summary>
        /// 标题
        /// </summary>
        public string Title { get; set; }

        public string ProductImage { get; set; }


        public string ProductId { get; set; }

        /// <summary>
        /// 类目
        /// </summary>
        public string CatePath { get; set; }

        public List<ListingProductEditResultModelByCate> CatePathList { get; set; }


        /// <summary>
        /// 类目属性
        /// </summary>
        public List<ListingProductEditResultModelByCateProps> CateProps { get; set; }

        /// <summary>
        /// 销售属性
        /// </summary>
        public List<ListingProductEditResultModelBySalesProps> SalesProps { get; set; }



        //public List<AlibabaSupplierProductSku> Skus { get; set; }
        /// <summary>
        /// sku
        /// </summary>
        public List<ListingProductEditResultModelBySku> Skus { get; set; }








        #region 提交时，多出来的Model
        public string SupplierProductCode { get; set; }

        /// <summary>
        /// 货源商品id，和上面的ProductId一样
        /// </summary>
        public string SupplierProductId { get; set; }
        public string SourcePlatform { get; set; }

        public int ShopId { get; set; }

        public string PlatformType { get; set; }

        public string ListingTaskCode { get; set; }

        public string Source { get; set; }

        /// <summary>
        /// 是否立即铺货
        /// </summary>
        public bool IsPuHuo { get; set; }


        #endregion

        /// <summary>
        /// sku分类填写
        /// </summary>
        public SkuClassificationRule SkuClassificationRule { get; set; }

        public string Status { get; set; }

        public bool IsPreview
        {
            get
            {
                return Status == "Preview";
            }
        }

        /// <summary>
        /// 是否更新类目
        /// </summary>
        public bool IsChangeCategory { get; set; }
    }


    /// <summary>
    /// 类目属性
    /// </summary>
    public class ListingProductEditResultModelByCateProps
    {
        public string FieldType { get; set; }
        public string Id { get; set; }
        public string Name { get; set; }
        public string Value { get; set; }
        public string ValueString { get; set; }
        public string Ext1 { get; set; }
        public string Ext2 { get; set; }
        public List<string> Options { get; set; }

        public ListingProductEditResultModelByMeasureTemplates MeasureTemplates { get; set; }

        public ListingProductEditResultModelByRule Rule { get; set; }

        public int SetValueByDiy { get; set; }

    }
    public class ListingProductEditResultModelByCate
    {
        public string CateId { get; set; }
        public string CateName { get; set; }
    }
    /// <summary>
    /// 销售属性
    /// </summary>
    public class ListingProductEditResultModelBySalesProps
    {
        public string FieldType { get; set; }
        public string Name { get; set; }
        public List<string> Options { get; set; }
        public List<string> Value { get; set; }
        public List<TouTiaoMeasureTemplates> measure_templates { get; set; }
        public List<SpecInfoValueMeasureInfo> measure_info { get; set; }
    }

    /// <summary>
    /// sku
    /// </summary>
    public class ListingProductEditResultModelBySku
    {
        public string SkuId { get; set; }

        public decimal Price { get; set; }
        public string PicUrl { get; set; }
        public int AmountOnSale { get; set; }

        public string ColorSize { get; set; }

        public List<string> AttributesList { get; set; }

        /// <summary>
        /// 头条sku标识
        /// </summary>
        public string SkuClassificationType { get; set; }

        /// <summary>
        /// 原价格
        /// </summary>
        public decimal? SourcePrice { get; set; }

        /// <summary>
        /// 原库存
        /// </summary>
        public int? SourceAmountOnSale { get; set; }
    }




    public class ListingProductEditResultModelByRule
    {
        public string MinLength { get; set; }
        public string MaxLength { get; set; }
        public string NumMinRange { get; set; }
        public string NumMaxRange { get; set; }

        public bool IsCascade { get; set; }
        public bool IsRequired { get; set; }
        public long DiyType { get; set; }
    }

    public class ListingProductEditResultModelByMeasureTemplates
    {
        public string LeftValue { get; set; }

        public string RightValue { get; set; }


        public List<ListingProductEditByMeasureTemplateOptions> LeftOptions { get; set; }

        public List<ListingProductEditByMeasureTemplateOptions> RightOptions { get; set; }


        public List<ListingProductEditByMultiValueMeasure> MultiValueMeasureList { get; set; }
    }


    public class ListingProductEditByMeasureTemplateOptions
    {
        public bool IsSelect { get; set; }

        public string Value { get; set; }
    }

    public class ListingProductEditByMultiValueMeasure
    {
        /// <summary>
        /// 序号，用于排序和前端赋值
        /// </summary>
        public int Index { get; set; }

        //属性
        public string Name { get; set; }

        //属性百分比
        public string Value { get; set; }
    }

    /// <summary>
    /// 规格销售属性
    /// </summary>
    public class SkuSellProperty
    {
        /// <summary>
        /// Id
        /// </summary>
        public string sell_property_id { get; set; }

        //属性名称
        public string sell_property_name { get; set; }

        /// <summary>
        /// 规格样式
        /// </summary>
        public string value_display_style { get; set; }

        //值
        public string property_values { get; set; }
        
        /// <summary>
        /// 是否必填
        /// </summary>
        public bool is_required { get; set; }
        /// <summary>
        /// 是否支持备注
        /// </summary>
        public bool support_remark { get; set; }
        /// <summary>
        /// 是否可以自定义规格值
        /// </summary>
        public bool support_diy { get; set; }

        /// <summary>
        /// 度量衡模板列表
        /// </summary>
        public List<TouTiaoMeasureTemplates> measure_templates { get; set; }
    }
}
