using DianGuanJiaApp.Data;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.FxModel.Listing;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Model.BaseProduct;
using DianGuanJiaApp.Data.Model.LogModel;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Services.Services.DataEventTracking;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.Helpers;
using MySql.Data.MySqlClient;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Services
{
    public partial class PlatformCategorySupplierService : BaseService<PlatformCategory>
    {
        private PlatformCategorySupplierRepository _platformCateRep;

        public const string LOGFILENAME = "PlatformCategorySupplierCate.txt";

        public PlatformCategorySupplierService()
        {
            _platformCateRep = new PlatformCategorySupplierRepository();
        }

        /// <summary>
        /// 分单系统货盘-同步类目-类目属性-类目规则
        /// </summary>
        /// <param name="shopId">店铺Id</param>
        public void SyncFnDdCateGory(int shopId)
        {
            try
            {
                #region 业务日志1，处理前的数据
                var businessLogs = new List<BusinessLogModel>()
                {
                    new BusinessLogModel
                    {
                        MethodName ="SyncFnDdCateGory",
                        BatchId = "",
                        SourceType = "",
                        PlatformType = "TouTiao",
                        CreatorId = 0,
                        FxUserId = 0,
                        ShopId = shopId,
                        BusinessType = BusinessTypes.ListingTask.ToString(),
                        SubBusinessType = SubBusinessTypes.SyncCatesGory.ToString(),
                        BusinessId = shopId.ToString(),
                        Content = $"货盘类目数据同步开始 {DateTime.Now}",
                    }
                };

                //上传日志
                BusinessLogDataEventTrackingService.Instance.WriteLog(businessLogs);
                #endregion

                Log.WriteLine($"货盘类目数据同步开始 {DateTime.Now}", LOGFILENAME);
                List<PlatformCategory> lastLevelCate = new List<PlatformCategory>();
                var shopSvc = new ShopService(PlatformAppScene.listing);
                var targetShops = shopSvc.GetShopByIds(new List<int>() { shopId });
                var shop = targetShops.FirstOrDefault();

                if (shop.PlatformType == PlatformType.TouTiao.ToString())
                {
                    // 还需要再查询1次，抖店跨云查询，保证token最准
                    shop = shopSvc.GetTouTiaoShopByIds(shopId).FirstOrDefault();

                    // 1.同步店铺的类目
                    var platformService = PlatformFactory.GetPlatformService(shop, scene: PlatformAppScene.listing);
                    lastLevelCate = SyncFdCates(shop, platformService);
                    Log.WriteLine($"抖店类目拉取完成 {DateTime.Now}", LOGFILENAME);

                    Task propTask = Task.Run(() =>
                    {
                        // 2.抖店类目属性和属性值
                        SyncFnDdCategoryProp(shop, lastLevelCate);
                        Log.WriteLine($"抖店属性拉取完成 {DateTime.Now}", LOGFILENAME);
                    });

                    Task ruleTask = Task.Run(() =>
                    {
                        // 3.抖店类目-发布规则
                        SyncFnDdCategoryPublishRule(shop, lastLevelCate);
                        Log.WriteLine($"抖店发布规则拉取完成 {DateTime.Now}", LOGFILENAME);
                    });
                    Task.WaitAll(propTask, ruleTask);
                    Task.Run(() =>
                    {
                        // 【抖店】sku导航属性 这个就不用等待了
                        SyncNavigationCateProp(shop);
                        Log.WriteLine($"抖店sku导航属性完成 {DateTime.Now}", LOGFILENAME);
                    });
                }

                Log.WriteLine($"货盘类目数据同步完成 {DateTime.Now}", LOGFILENAME);

                #region 业务日志2，处理后的数据
                var businessLogs2 = new List<BusinessLogModel>()
                {
                    new BusinessLogModel
                    {
                        MethodName ="SyncFnDdCateGory",
                        BatchId = "",
                        SourceType = "",
                        PlatformType = "TouTiao",
                        CreatorId = 0,
                        FxUserId = 0,
                        ShopId = shopId,
                        BusinessType = BusinessTypes.ListingTask.ToString(),
                        SubBusinessType = SubBusinessTypes.SyncCatesGory.ToString(),
                        BusinessId = shopId.ToString(),
                        Content = $"货盘类目数据同步完成 {DateTime.Now}",
                    }
                };

                //上传日志
                BusinessLogDataEventTrackingService.Instance.WriteLog(businessLogs2);
                #endregion
            }
            catch (Exception e)
            {
                var ex = e;
                if (e.InnerException != null)
                    ex = e.InnerException;

                ExceptionLogDataEventTrackingService.Instance.WriteLog(e, $"{shopId} 同步类目-属性-规则错误：{ex.Message}");
                Log.WriteError($"{shopId} 同步类目-类目属性-属性规则错误：{ex.Message} 【堆栈】：{ex.StackTrace}", LOGFILENAME);
                // HttpUtility.PostToRemind($"同步类目-类目属性-属性规则失败:{shopId.ToJson()}", ex);
            }
        }

        /// <summary>
        ///  【抖店】 类目
        /// </summary>
        /// <param name="shop">店铺</param>
        /// <param name="platformService">平台类</param>
        /// <returns></returns>
        private List<PlatformCategory> SyncFdCates(Shop shop, IPlatformService platformService)
        {
            if (shop.PlatformType != PlatformType.TouTiao.ToString())
                return new List<PlatformCategory>();

            Stopwatch sw = Stopwatch.StartNew();
            // 1. 拉取Api数据
            var apiCates = new DouDianPlatformCateService().GetNewCateByDouDianByShop(platformService);
            // var apiCates = new PlatformCategoryRepository().GetPlatformCategoriesByShopId(shop.Id, "TouTiao");//  本机测试，免的耗时太久
            sw.Stop();
            Log.WriteLine($"{DateTime.Now.ToString("HH:mm:ss")} 获取抖店类目耗时：{sw.ElapsedMilliseconds} 毫秒", LOGFILENAME);
            sw.Restart();

            // 2.区分出增删改
            var dbCates = _platformCateRep.GetShopCates(shop);

            // 去重
            var newApicates = apiCates.GroupBy(x => x.Code).Select(x => x.FirstOrDefault()).ToList();

            Log.WriteError($"获取抖店类目，apiCates:{apiCates.Count}个 去重后:{newApicates.Count}个 ", LOGFILENAME);

            var tuple = CompareOldCatesV2(PlatformType.TouTiao.ToString(), newApicates, dbCates, true);

            // 3.处理增删改--(系统的类目)
            if (tuple.Item1.Count > 0 || tuple.Item2.Count > 0 || tuple.Item3.Count > 0)//如果类目没变化，则不用处理，下面的判断也是
                BulkMulti(tuple.Item1, tuple.Item2, tuple.Item3);

            // 4.更新类目和店铺的关系表
            var cateShopRelations = newApicates.Select(f => new PlatformCateShopRelation()
            {
                CateId = f.CateId,
                RelationCode = f.Code,
                ShopId = shop.Id,
                CreateTime = DateTime.Now,
            }).ToList();

            if (cateShopRelations.Any())
            {
                // 获取当前店铺的头条类目信息有多少个，和现在同步下来的是一样的数量就不添加
                var shopCateNumber = _platformCateRep.GetCateShopRelationsNumber(shop.Id);
                if (shopCateNumber != cateShopRelations.Count)
                    _platformCateRep.SaveCateShopRelation(shop.Id, cateShopRelations);
            }

            Log.WriteLine($"{DateTime.Now.ToString("HH:mm:ss")} 【成功同步】抖店类目耗时：{sw.ElapsedMilliseconds} 毫秒", LOGFILENAME);
            var lastLevelCate = newApicates.Where(a => !a.IsParent).ToList();
            Log.WriteLine($"{DateTime.Now.ToString("HH:mm:ss")} 找到本次抖店类目:{apiCates.Count}个 末级类目：{lastLevelCate.Count}个", LOGFILENAME);
            return lastLevelCate;
        }

        /// <summary>
        /// 区分出插入，更新，删除
        /// 对比的是系统类目
        /// </summary>
        /// <param name="apiCates">api新拉的数据</param>
        /// <param name="dbCates">Db旧数据</param>
        /// <param name="isDelNotExists">api新拉数据和db旧数据求差集并删除</param>
        /// <returns>1.插入 2.更新 3.删除</returns>
        public Tuple<List<PlatformCategory>, List<PlatformCategory>, List<PlatformCategory>> CompareOldCatesV2(string platformType,
            List<PlatformCategory> apiCates, List<PlatformCategory> dbCates, bool isDelNotExists = false)
        {
            if (apiCates == null && apiCates.Any() == false)
                throw new LogicException("未传入新类目数据比较");

            var addList = new List<PlatformCategory>();
            var delList = new Dictionary<string, PlatformCategory>();
            var upList = new List<PlatformCategory>();
            //当前时间
            var cuurTime = DateTime.Now;
            //查询数据库的旧类目数据

            var oldCates = dbCates.ToDictionary(key => key.CateId, value => value);
            //接口和旧数据比较,数据库存在的话判断名字和上一级类目id是否一致,不一致则删除,一致就更新, 数据库不存在就插入
            apiCates.OrderBy(d => d.Level).ToList().ForEach(c =>
            {
                if (delList.Any(a => a.Key == c.CateId))
                    return;

                var dbcate = oldCates.FirstOrDefault(o => o.Key == c.CateId).Value;
                if (dbcate != null)
                {
                    //存在数据,判断父级是否一致
                    if (dbcate.ParentId == c.ParentId)
                    {
                        //一致在判断名字是否一致,不一致则更新
                        if (dbcate.Name != c.Name || dbcate.Level != c.Level)
                        {
                            dbcate.Name = c.Name;
                            dbcate.Level = c.Level;
                            dbcate.UpdateTime = cuurTime;
                            upList.Add(dbcate);
                        }
                    }
                    else
                    {
                        //不一致删除
                        //delList.Add(dbcate.CateId, dbcate);
                        if (!delList.ContainsKey(dbcate.CateId))
                            delList.Add(dbcate.CateId, dbcate);
                        //找出所有子级
                        var chiCates = ForCates(oldCates, dbcate);
                        foreach (var item in chiCates)
                        {
                            if (!delList.ContainsKey(item.Key))
                                delList.Add(item.Key, item.Value);
                        }
                    }
                }
                else
                {
                    c.CreateTime = cuurTime;
                    addList.Add(c);
                }
            });

            if (isDelNotExists == true)
            {
                var apiCateLookup = apiCates.ToLookup(s => s.CateId);
                foreach (var item in oldCates)
                {
                    if (delList.ContainsKey(item.Key))
                        continue;
                    if (apiCateLookup[item.Key].FirstOrDefault() == null)
                    {
                        delList.Add(item.Key, item.Value);
                    }
                }
            }

            Log.WriteLine($"平台：{platformType},新增类目:{addList.Count}个,更新类目:{upList.Count}个,删除类目:{delList.Count}个，接口类目数据;{apiCates.Count}个,db:{oldCates.Count}个", LOGFILENAME);
            return Tuple.Create(addList, upList, delList.Values.ToList());
        }

        private void BulkMulti(List<PlatformCategory> addlist, List<PlatformCategory> uplist, List<PlatformCategory> dellist)
        {
            try
            {
                if (dellist != null && dellist.Any())
                    _platformCateRep.BulkDelete(dellist);

                if (uplist != null && uplist.Any())
                    _platformCateRep.BulkUpdate(uplist);

                if (addlist != null && addlist.Any())
                    _platformCateRep.BulkAdd(addlist);
            }
            catch (MySqlException ex) when (ex.Number == 1062) // 错误码1062代表Duplicate entry
            {
                Log.WriteLine($"捕获到唯一性约束冲突异常:{ex.Message}", LOGFILENAME);
            }
            catch (Exception ex)
            {
                ExceptionLogDataEventTrackingService.Instance.WriteLog(ex, $"BulkMulti类目执行插入，更新，删除失败,错误信息：{ex.Message}");

                // HttpUtility.PostToRemind($"BulkMulti类目执行插入，更新，删除失败,错误信息：\r\n{ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 寻找子级类目
        /// </summary>
        /// <param name="AllCates">所有类目</param>
        /// <param name="currCate">当前类吗</param>
        /// <param name="isDeletd">是否删除</param>
        /// <returns></returns>
        private Dictionary<string, PlatformCategory> ForCates(Dictionary<string, PlatformCategory> AllCates, PlatformCategory currCate, bool isDeletd = false)
        {
            if (!currCate.IsParent)
                return new Dictionary<string, PlatformCategory>();
            var cates = GetSubCates(AllCates, currCate, new Dictionary<string, PlatformCategory>(), isDeletd);
            return cates;
        }

        private Dictionary<string, PlatformCategory> GetSubCates(Dictionary<string, PlatformCategory> AllCates, PlatformCategory currCate, Dictionary<string, PlatformCategory> currCates, bool isDeletd = false)
        {
            var cates = AllCates.Where(db => db.Value.ParentId == currCate.CateId).ToDictionary(key => key.Key, value => value.Value);
            foreach (var item in cates)
            {
                currCates.Add(item.Key, item.Value);
            }
            foreach (var item in cates)
            {
                if (isDeletd)
                {
                    item.Value.Status = "deleted";
                    item.Value.UpdateTime = DateTime.Now;
                }
                var cCates = GetSubCates(AllCates, item.Value, currCates, isDeletd);
            }
            return currCates;
        }

        /// <summary>
        /// 【抖店】 属性
        /// </summary>
        /// <param name="shop">店铺</param>
        /// <param name="cates">类目</param>
        public void SyncFnDdCategoryProp(Shop shop, List<PlatformCategory> cates)
        {
            if (shop.PlatformType != PlatformType.TouTiao.ToString())
                return;

            var zdPlatformSvc = new ZhiDianNewPlatformService(shop);
            Stopwatch sw = Stopwatch.StartNew();
            int current = 0;
            Parallel.ForEach(cates, new ParallelOptions() { MaxDegreeOfParallelism = 4 }, pitem =>
            {
                Interlocked.Increment(ref current);
                try
                {
                    var catePropList = zdPlatformSvc.GetCatePropInfoNew(pitem.CateId);
                    var conlist = new List<PlatformCategoryProp>();
                    foreach (var p in catePropList)
                    {
                        var de = new PlatformCategoryProp();
                        de.PlatformType = shop.PlatformType;
                        de.Name = p.property_name;
                        de.CateId = p.category_id.ToString();
                        de.Content = p.ToJson();
                        de.PropertyId = p.property_id.ToString();
                        de.Name = p.property_name;
                        de.CreateTime = DateTime.Now;
                        de.UpdateTime = DateTime.Now;
                        de.SetUnionCode();
                        conlist.Add(de);
                    }

                    _platformCateRep.SaveCategoryProp2(pitem.CateId, conlist, shop.PlatformType); //保存
                }
                catch (LogicException ex) // 如果是 Exception 异常，还是得抛出，由上层去处理
                {
                    Log.WriteError($"{pitem.CateId}-{shop.ShopName} 请求抖店属性接口报错:{ex.Message}", LOGFILENAME);

                    // 如果是授权到期，抛出异常，终止循环
                    if (ex.Message.Contains("授权过期"))
                        throw;
                }

                Thread.Sleep(500);
            });
            sw.Stop();
            Log.WriteLine($"{DateTime.Now.ToString("HH:mm:ss")} 抖店类目属性成功，耗时：{sw.ElapsedMilliseconds} 毫秒", LOGFILENAME);
        }

        /// <summary>
        /// 【抖店】类目发布规则
        /// </summary>
        /// <param name="shop">店铺</param>
        /// <param name="cates">类目</param>
        public void SyncFnDdCategoryPublishRule(Shop shop, List<PlatformCategory> cates)
        {
            if (shop.PlatformType != PlatformType.TouTiao.ToString())
                return;

            // var conlist = new ConcurrentBag<PlatformCategoryPublishRule>();
            var zdPlatformSvc = new ZhiDianNewPlatformService(shop);
            Stopwatch sw = Stopwatch.StartNew();
            int current = 0;
            Parallel.ForEach(cates, new ParallelOptions() { MaxDegreeOfParallelism = 4 }, pitem =>
            {
                Interlocked.Increment(ref current);

                try
                {
                    long l_cateId = long.Parse(pitem.CateId.ToString());
                    var puRulemodel = zdPlatformSvc.GetAddProductRuleByCateId(l_cateId);
                    // 规则删除了，会导致 末级类目没有对应规则，前端规格转换报错
                    // if (puRulemodel == null)
                    // {
                    //     _platformCateRep.DelCategoryPublishRule(pitem.CateId); // 平台方删除，DB同步删除
                    // }else
                    if (puRulemodel != null)
                    {
                        var de = new PlatformCategoryPublishRule();
                        de.PlatformType = shop.PlatformType;
                        de.CateId = pitem.CateId;
                        de.Content = puRulemodel.ToJson();
                        de.CreateTime = DateTime.Now;
                        de.UpdateTime = DateTime.Now;
                        de.SetUnionCode();

                        _platformCateRep.SaveCategoryPublishRule3(de); //保存
                        // conlist.Add(de);
                    }
                    Thread.Sleep(500);
                }
                catch (LogicException ex) // 如果是 Exception 异常，还是得抛出，由上层去处理
                {
                    Log.WriteError($"{pitem.CateId}-{shop.ShopName} 请求抖店规则接口报错:{ex.Message}", LOGFILENAME);

                    // 如果是授权到期，抛出异常，终止循环
                    if (ex.Message.Contains("授权过期"))
                        throw;
                }
            });
            sw.Stop();
            Log.WriteLine($"{DateTime.Now.ToString("HH:mm:ss")} 获取抖店类目类目发布规则耗时：{sw.ElapsedMilliseconds} 毫秒", LOGFILENAME);

            // _platformCateRep.SaveCategoryPublishRule2(conlist.ToList(), shop.PlatformType); //保存
        }

        /// <summary>
        /// 获取商品的审核状态
        /// </summary>
        public ListingProductStatusEnum? GetProductAuditList(int shopId, string product_id, int productStatus)
        {
            if (productStatus == 0 || string.IsNullOrWhiteSpace(product_id)) return null;

            if (!Enum.TryParse(productStatus.ToString(), out ListingProductStatusEnum statusEnum)) return null;

            if (statusEnum != ListingProductStatusEnum.PutStashAudit && statusEnum != ListingProductStatusEnum.ReadyForSaleAudit)
            {
                return null;
            }

            var targetShops = new ShopService(PlatformAppScene.listing).GetShopByIds(new List<int>() { shopId });
            var shop = targetShops.FirstOrDefault();
            PlatformFactory.GetPlatformService(shop, scene: PlatformAppScene.listing);

            // 指定审核状态返回商品列表：0-审核中 1-审核通过 2-审核拒绝
            var zdPlatformSvc = new ZhiDianNewPlatformService(shop);

            Stopwatch stopwatch = Stopwatch.StartNew();
            var check_Pass = zdPlatformSvc.GetProductAuditList(product_id, "1"); // 查询审核通过状态
            stopwatch.Stop();

            Log.Debug(() => $"{DateTime.Now.ToString("HH:mm:ss")} 查询({product_id})审核通过状态耗时：{stopwatch.ElapsedMilliseconds} 毫秒 结果是{check_Pass.Count}", "GetProductAuditList.txt");
            if (check_Pass.Any())
            {
                switch (statusEnum)
                {
                    case ListingProductStatusEnum.PutStashAudit: return ListingProductStatusEnum.PutStashPass;
                    case ListingProductStatusEnum.ReadyForSaleAudit: return ListingProductStatusEnum.ReadyForSalePass;
                    default: break;
                }
            }
            else
            {
                stopwatch.Restart();
                var check_SalePass = zdPlatformSvc.GetProductAuditList(product_id, "2"); // 查询审核驳回的状态
                Log.Debug(() => $"{DateTime.Now.ToString("HH:mm:ss")} 查询({product_id})审核驳回状态耗时：{stopwatch.ElapsedMilliseconds} 毫秒 结果是{check_SalePass.Count}", "GetProductAuditList.txt");
                if (check_SalePass.Any())
                {
                    switch (statusEnum)
                    {
                        case ListingProductStatusEnum.PutStashAudit: return ListingProductStatusEnum.PutStashReject;
                        case ListingProductStatusEnum.ReadyForSaleAudit: return ListingProductStatusEnum.ReadyForSaleReject;
                        default: break;
                    }
                }
            }

            return null;
        }

        /// <summary>
        /// 店铺类目发布权限校验
        /// </summary>
        public CategoryCheckAuth CheckShopCateAuth(CategoryCheckAuth model)
        {
            var redisKey = CacheKeys.ShopCateAuth.Replace("{Sid}", model.Shop.ShopId).Replace("{Cid}", model.CateId);
            if (RedisHelper.Exists(redisKey))
            {
                model.IsAuth = RedisHelper.Get<bool>(redisKey);
            }
            else
            {
                var res = new ZhiDianNewPlatformService(model.Shop).PublishPreCheck(model.CateId);
                if (res.HasValue)
                {
                    model.IsAuth = res.Value;
                    RedisHelper.Set(redisKey, model.IsAuth, TimeSpan.FromHours(new Random().Next(2, 5)));
                }
                else
                {
                    model.IsAuth = false;
                }
            }
            return model;
        }

        /// <summary>
        /// 店铺类目发布权限校验
        /// </summary>
        public bool ClearShopCateAuth(CategoryCheckAuth model)
        {
            var redisKey = CacheKeys.ShopCateAuth.Replace("{Sid}", model.Shop.ShopId).Replace("{Cid}", model.CateId);
            RedisHelper.Del(new List<string> { redisKey }.ToArray());
            return true;
        }



        /// <summary>
        /// 【抖店】商品类目预测-店铺调用记录
        /// </summary>
        /// <param name="subject">商品标题</param>
        /// <param name="shopIds">目标店铺</param>
        /// <returns></returns>
        public CategoryPredictionResult ProductCateForecastByShop(string subject, List<int> shopIds)
        {
            if (string.IsNullOrWhiteSpace(subject) || shopIds == null || shopIds.Count == 0) return null;

            CategoryPredictionResult result = null;
            var shopList = new ShopService(PlatformAppScene.listing).GetShopByIds(shopIds);
            foreach (var itemShop in shopList)
            {
                PlatformFactory.GetPlatformService(itemShop, scene: PlatformAppScene.listing);
                try
                {
                    result = new ZhiDianNewPlatformService(itemShop).GetRecommendCategory("category_infer", subject, string.Empty);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"类目预测调用记录失败：{ex.Message}. 堆栈：{ex.StackTrace}");
                }
            }
            return result;
        }

        /// <summary>
        /// 【抖店】商品类目预测
        /// </summary>
        /// <param name="subject">商品标题</param>
        /// <param name="picurl">商品主图</param>
        /// <returns></returns>
        public CategoryPredictionResult ProductCateForecast(string subject,int shopId, string picurl)
        {
            Shop shop = null;
            int targetShopId = shopId;
            bool islocal = CustomerConfig.IsDebug && CustomerConfig.IsLocalDbDebug;
            if (shopId == 0 && !islocal) // 使用系统级店铺
            {
                shop = new ShopService(PlatformAppScene.listing).GetShopNotExpTokenTouTiaoByFxUser(SiteContext.Current.CurrentFxUserId).FirstOrDefault();
            }
            else
            {
                if (islocal) targetShopId = 1687; // 本地调试，使用店铺

                shop = new ShopService(PlatformAppScene.listing).GetShopByIds(new List<int>() { targetShopId }).FirstOrDefault();
                if (shop == null) throw new LogicException("没有找到该店铺");
                PlatformFactory.GetPlatformService(shop, scene: PlatformAppScene.listing);
            }
            
            if (shop == null)
            {
                Log.Debug(() => $"【类目预测】当前用户:{SiteContext.Current.CurrentFxUserId} 没有找到有权限的抖音店铺");
                return new CategoryPredictionResult();
            }

            var zdPlatformSvc = new ZhiDianNewPlatformService(shop);

            try
            {
                /*
                // 当前云一定是在 精选云
                if (!picurl.StartsWith("http"))
                {
                    picurl = CustomerConfig.AlibabaFenFaSystemUrl + picurl.Replace("Common/GetImageFile", "ImageFile/Get");
                }
                var material_name = picurl.ToShortMd5();

                // 1.上传到素材库
                var folder_id = zdPlatformSvc.SearchFolder();
                var materialModel = zdPlatformSvc.UploadImageSync(folder_id, picurl, $"{material_name}.jpg", 2, true);

                // 2.查询图片 materil_name
                var imgModel = zdPlatformSvc.QueryMaterialDetail(materialModel.material_id);
                var resultList = zdPlatformSvc.GetRecommendCategory("category_infer", subject, imgModel.audit_status == 3 ? imgModel.byte_url : string.Empty);
                */

                // 3. 类目预测
                var categoryPredictionResult = zdPlatformSvc.GetRecommendCategory("category_infer", subject, string.Empty);
                return categoryPredictionResult;
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("授权过期")) throw;
                Log.WriteError($"类目预测失败：{ex.Message}. 堆栈：{ex.StackTrace}");
                return new CategoryPredictionResult();
            }
        }

        /// <summary>
        /// 【抖店】同步计算sku导航属性
        /// </summary>
        /// <param name="shop">店铺</param>
        public void SyncNavigationCateProp(Shop shop)
        {
            try
            {
                var list = _platformCateRep.GetList();

                var zdPlatformSvc = new ZhiDianNewPlatformService(shop);
                foreach (var item in list)
                {
                    if (!string.IsNullOrWhiteSpace(item.Content))
                    {
                        var model = item.Content.ToObject<ProductUpdateRuleInfoModel>();
                        var navigationCateProp = GetNavigationCateProp(model, item.CateId, zdPlatformSvc);
                        if (navigationCateProp != null && navigationCateProp.Any())
                        {
                            // var pp3 = navigationCateProp.Select(p => p.Code).ToList();
                            // var pp4 = navigationCateProp.GroupBy(x => x.Code).Select(a => a.FirstOrDefault()).Count();

                            foreach (PlatformCategoryNavigation nav in navigationCateProp)
                            {
                                _platformCateRep.SaveNavigationCateProp(nav);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"{shop.ShopName} 同步计算sku导航属性异常:{ex.Message} {ex.StackTrace}", LOGFILENAME);
            }
        }

        /// <summary>
        /// 计算sku导航属性的逻辑
        /// </summary>  
        public List<PlatformCategoryNavigation> GetNavigationCateProp(ProductUpdateRuleInfoModel productUpdateRuleInfo, string cateId, ZhiDianNewPlatformService service)
        {
            List<PlatformCategoryNavigation> navCateValues = new List<PlatformCategoryNavigation>();
            var displayStyle = new string[] { "cascader", "cascader_multi_select" };

            foreach (var item in productUpdateRuleInfo.product_spec_rule.required_spec_details)
            {
                if (displayStyle.Contains(item.value_display_style) && item.navigation_properties != null && item.navigation_properties.Any())
                {
                    var property_id = item.navigation_properties.FirstOrDefault()?.property_id;
                    if (property_id.HasValue)
                    {
                        // 获取导航样式属性
                        DyCascadeValueProperty property_values = service.GetCategoryPropertyValue(cateId, property_id.Value.ToString());
                        foreach (DyCascadeValuePropertyValue propval in property_values.property_values)
                        {
                            var listingnav = new PlatformCategoryNavigation(property_values.property_id, propval.property_value_id, propval.property_value_name, PlatformType.TouTiao.ToString());
                            listingnav.CateId = cateId;
                            listingnav.LowerLevelAttr = GetLowerLevelAttrData(cateId, property_values.property_id.ToString(), propval, service, 0);
                            navCateValues.Add(listingnav);
                        }
                    }
                }
            }

            return navCateValues;
        }

        /// <summary>
        /// 计算sku导航属性的逻辑->获取下一级属性值
        /// </summary>
        /// <param name="cateId">类目id</param>
        /// <param name="property_id">属性Id</param>
        /// <param name="propval">属性值模型</param>
        /// <param name="service"></param>
        /// <param name="cascade_id">级联id</param>
        /// <returns></returns>
        private List<LowerLevelAttr> GetLowerLevelAttrData(string cateId, string property_id, DyCascadeValuePropertyValue propval, ZhiDianNewPlatformService service, long cascade_id)
        {
            // 获取导航样式属性值
            DyCascadeValue dyCascade = service.GetCascadeValue(cateId, property_id, new List<Tuple<string, string, string>>
                {
                    Tuple.Create(propval.property_value_id.ToString(), propval.property_value_name.ToString(), cascade_id.ToString())
                });

            List<LowerLevelAttr> resultList = new List<LowerLevelAttr>();
            foreach (var response in dyCascade.properties)
            {
                // 是否有下级
                if (response.has_sub_property)
                {
                    foreach (var property in response.property_values)
                    {
                        LowerLevelAttr lowerLevelAttr = new LowerLevelAttr(property.property_value_id, property.property_value_name);
                        resultList.Add(lowerLevelAttr);
                        if (property.cascade_id > 0)
                        {
                            var list = GetLowerLevelAttrData(cateId, property_id, propval, service, property.cascade_id);
                            resultList.AddRange(list);
                        }
                    }
                }
                else
                {
                    foreach (var property in response.property_values)
                    {
                        LowerLevelAttr lowerLevelAttr = new LowerLevelAttr(property.property_value_id, property.property_value_name);
                        resultList.Add(lowerLevelAttr);
                    }
                    return resultList;
                }
            }

            return resultList;
        }
    }
}
