using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility.Extension;
using System.Collections.Concurrent;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Data.FxModel;
using DianGuanJiaApp.Services.Services.SettingsService;
using System.Data.Common;

namespace DianGuanJiaApp.Services
{
    public partial class FxUserShopService : BaseService<Data.Entity.FxUserShop>
    {
        private FxUserShopRepository _repository = new FxUserShopRepository();
        private FxUserForeignShopRepository _fxUserForeignShopRepository = new FxUserForeignShopRepository();
        public FxUserShopService()
        {
            _baseRepository = new Data.Repository.BaseRepository<FxUserShop>(CustomerConfig.ConfigureDbConnectionString);
        }

        /// <summary>
        /// 获取绑定店铺(需要判断是否移除虚拟店铺)
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="rmVirtualShop">是否包含虚拟店铺</param>
        /// <param name="filterExpiredFlag">是否过滤未授权店铺</param>
        /// <returns></returns>
        public List<FxUserShop> GetShopsByFxUserId(int fxUserId, bool rmVirtualShop, bool filterExpiredFlag = false)
        {
            //如果是跨境站点，则获取当前用户的店铺，是从另外一个表查询的
            if (CustomerConfig.IsCrossBorderSite)
            {
                var list = _fxUserForeignShopRepository.GetShopsByFxUserId(fxUserId);
                return list.Select(f => f as FxUserShop).ToList();
            }
            var shopList = _repository.GetShopsByFxUserId(fxUserId, rmVirtualShop);
            if (filterExpiredFlag)
            {
                // 再过滤一下未授权和授权过期的店铺
                shopList = shopList.Where(x => !x.IsExpire).ToList();
            }
            return shopList;
        }

        /// <summary>
        /// 分页获取集合
        /// 返回元组格式,Item1(总数量),Item2(集合)
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="key"></param>
        /// <param name="status"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="onlyGetCurDb"></param>
        /// <returns></returns>
        //public Tuple<int, List<FxUserShop>> GetShopList(int fxUserId, bool rmVirtualShop, string key, AgentBingSupplierStatus? status, int pageIndex, int pageSize, bool onlyGetCurDb = false)
        //{
        //    var list = _repository.GetShopList(fxUserId, rmVirtualShop);
        //    return list;
        //}

        public List<FxUserShop> GetCloudPlatformTypeShopsByFxUserId(int fxUserId)
        {
            //如果是跨境站点，则获取当前用户的店铺，是从另外一个表查询的
            if (CustomerConfig.IsCrossBorderSite)
            {
                var fxShops = _fxUserForeignShopRepository.GetShopsByFxUserId(fxUserId);
                var myShop = new List<FxUserShop>();
                if (fxShops.Any())
                {
                    myShop = fxShops.Select(f => f as FxUserShop).ToList();
                }
                return myShop;
            }
            else
            {
                var myShop = GetShopsByFxUserId(fxUserId, true);
                var cloudPlatformType = CustomerConfig.CloudPlatformType;
                if (cloudPlatformType == CloudPlatformType.Pinduoduo.ToString())
                    myShop = myShop.Where(t => CustomerConfig.FxPinduoduoCloudPlatformTypes.Contains(t.PlatformType)).ToList();
                else if (cloudPlatformType == CloudPlatformType.Jingdong.ToString())
                    myShop = myShop.Where(t => CustomerConfig.FxJingDongCloudPlatformTypes.Contains(t.PlatformType))
                        .ToList();
                else if (cloudPlatformType == CloudPlatformType.TouTiao.ToString())
                    myShop = myShop.Where(t => CustomerConfig.FxDouDianCloudPlatformTypes.Contains(t.PlatformType)).ToList();
                else
                    myShop = myShop.Where(t =>
                        CustomerConfig.FxJingDongCloudPlatformTypes.Contains(t.PlatformType) == false &&
                        CustomerConfig.FxPinduoduoCloudPlatformTypes.Contains(t.PlatformType) == false &&
                        CustomerConfig.FxDouDianCloudPlatformTypes.Contains(t.PlatformType) == false).ToList();

                return myShop;

                
            }
        }

        public Dictionary<int, string> GetDictionaryShopsByFxUserId(int fxUserId, bool rmVirtualShop)
        {
            return _repository.GetDictionaryShopsByFxUserId(fxUserId, rmVirtualShop);
        }

        /// <summary>
        /// 根据ShopId查询
        /// </summary>
        /// <param name="shopIds"></param>
        /// <param name="strField"></param>
        /// <returns></returns>
        public List<FxUserShop> GetUserIdByShopId(List<int> shopIds, string strField = "*")
        {
            return _repository.GetUserIdByShopId(shopIds, strField);
        }

        /// <summary>
        /// 获取系统店铺
        /// </summary>
        /// <param name="fxIds"></param>
        /// <returns></returns>
        public List<FxUserShop> GetFxUserShopIds(List<int> fxIds)
        {
            return _repository.GetFxUserShopIds(fxIds);
        }
        public void AddFxUserShop(FxUserShop _newfx)
        {
            //1.判断是否存在，存在则修改状态为正常，修改授权时间
            var data = _repository.GetFxUserShop(_newfx);
            if (data != null)
            {
                //2.更新状态和授权时间
                data.NickName = _newfx.NickName;
                data.AuthTime = _newfx.AuthTime;
                data.Status = _newfx.Status;
                _repository.Update(data);
            }
            else
            {
                //3.新增关联关系
                _repository.Add(_newfx);
                TryInitSellerBaseVersion(_newfx.FxUserId);
            }

            //缓存性能优化:直接清除缓存--再次使用时刷新
            FxCaching.RefeshCache(FxCachingType.FxShopSelf, _newfx.FxUserId);
        }

        public void TryInitSellerBaseVersion(int fxUserId)
        {
            try
            {
                var isShouldPay = new CommonSettingService().IsFxUserShouldPay(fxUserId);
                if (isShouldPay)
                    ExecuteScalarStoredProcedure("sp_IFUserIsGuestThenTurnToV1", new Dictionary<string, object> { { "@fxUserId", fxUserId } });
            }
            catch (Exception ex)
            {
                Log.WriteError($"尝试执行将游客转换为商家基础版的存储过程时发生错误sp_IFUserIsGuestThenTurnToV1,fxUserId:{fxUserId}，错误详情：{ex}");
            }
        }

        /// <summary>
        /// 分页获取集合
        /// 返回元组格式,Item1(总数量),Item2(集合)
        /// </summary>
        /// <param name="_reqModel"></param>
        /// <returns></returns>
        public Tuple<int, List<FxUserShop>> GetList(FxUserShopQueryModel _reqModel)
        {
            if (_reqModel.FxUserId <= 0)
                _reqModel.FxUserId = SiteContext.Current.CurrentFxUserId;
            var tuoles = _repository.GetList(_reqModel);
            return tuoles;
        }

        /// <summary>
        /// 分页获取集合
        /// 返回元组格式,Item1(总数量),Item2(集合)
        /// </summary>
        /// <param name="_reqModel"></param>
        /// <returns></returns>
        public Tuple<int, List<FxUserShop>> GetListOfFinancialSettlement(FxUserShopQueryModel _reqModel)
        {
            var tuoles = _repository.GetListOfFinancialSettlement(_reqModel);
            return tuoles;
        }


        /// <summary>
        /// 根据系统用户ID获取系统店铺ID
        /// </summary>
        /// <param name="fxUserIds">用户ID</param>
        /// <returns></returns>
        public List<FxUserShop> GetFxUserIdMapping(List<int> fxUserIds)
        {
            return _repository.GetFxUserIdMapping(fxUserIds);
        }

        public List<FxUserShop> GetList(int fxUserId = 0, int shopId = 0, List<int> shopIds = null)
        {
            var fxUserShops = _repository.GetList(fxUserId, shopId, shopIds);
            var fxUserForeignShops = _fxUserForeignShopRepository.GetList(fxUserId, shopId, shopIds);
            if (fxUserForeignShops.Any())
                fxUserShops.AddRange(fxUserForeignShops);
            return fxUserShops;
        }

        public FxUserShop GetFxUserShop(string shopId, string platformType)
        {
            return _repository.GetFxUserShop(shopId, platformType);
        }

        /// <summary>
        /// 是否只订购了分单铺货应用
        /// </summary>
        /// <param name="sId"></param>
        /// <param name="pt"></param>
        /// <returns></returns>
        public bool IsOnlyListingApp(int sId, string pt)
        {
            //是否分单铺货应用
            var isFxListingApp = false;
            if (CustomerConfig.FxListingSupportPlatformTypes.Contains(pt))
            {
                var shopExtensions = new ShopExtensionService().GetShopAppKey(new List<int> { sId });
                if (shopExtensions != null && shopExtensions.Count == 1 && CustomerConfig.FxListingAppKeys.Contains(shopExtensions.First().AppKey))
                    isFxListingApp = true;
            }
            return isFxListingApp;
        }


        /// <summary>
        /// 根据平台订单店铺Id，获取关联此店铺的商家的虚拟店铺Id
        /// </summary>
        /// <param name="orderShopId"></param>
        /// <returns></returns>
        public FxUserShop GetFxUserSystemShop(int orderShopId)
        {
            return _repository.GetFxUserSystemShop(orderShopId);
        }

        public List<int> GetShopIdByFxUserId(int fxUserId)
        {
            return _repository.GetShopIdByFxUserId(fxUserId);
        }

        public void UpdateShopStatus(int fxUserId, int shopId, int status)
        {
            _repository.UpdateShopStatus(fxUserId, shopId, status);
        }

        public void UpdateAuthTime(int shopId, DateTime newAuthTime)
        {
            _repository.UpdateAuthTime(shopId, newAuthTime);
        }
        public void UpdateListingOwnerServiceTime(int shopId, DateTime newAuthTime)
        {
            _repository.UpdateListingOwnerServiceTime(shopId, newAuthTime);
        }

        /// <summary>
        /// item1->获取当前(授权,服务)过期的商家绑定店铺
        /// item2->服务即将到期或已经到期
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="queryType">查询类型：top_invalid_shop=首页顶部过期，空=其他</param>
        /// <param name="myshopId"></param>
        ///  <param name="findShopId">指定查询店铺id</param>
        /// <returns></returns>
        public Tuple<List<FxUserShop>, List<FxUserShop>> GetCurrentExpireShopByFxUserId(int fxUserId, string queryType, int myshopId, int findShopId = 0)
        {
            var item1 = new ConcurrentBag<FxUserShop>();
            var item2 = new List<FxUserShop>();
            //订单失效店铺过滤条件，跳过部分店铺
            var skipIds = new List<int>();
            if (queryType == "top_invalid_shop")
            {
                var shopId = myshopId;
                var val = new CommonSettingService().Get("/System/Config/Fendan/FilterInvalidShopId", shopId)?.Value;
                if (!string.IsNullOrWhiteSpace(val))
                {
                    skipIds = val.Split(',').Select(x => int.Parse(x)).ToList();
                }
            }

            bool isCheckAuth = false;
            var newResult = _repository.GetExpireShopByFxUserId(fxUserId, skipIds, findShopId);
            var userShopIds = newResult.Select(s => s.ShopId).ToList();
            var userShops = new ShopService().GetShopsAndShopExtensionFunc(userShopIds, isCheckAuth);

            //查询订购记录
            var _userService = new UserService();
            var serviceAppOrders = new List<ServiceAppOrder>();
            var appOrderShop = userShops.Where(x =>
                x.PlatformType == PlatformType.TouTiao.ToString() ||
                x.PlatformType == PlatformType.TouTiaoSaleShop.ToString()).ToList();
            var appShopIds = appOrderShop.Select(x => x.Id).Distinct().ToList();    //订购消息的店铺Id
            var ptShopsIds = userShops.Where(x =>
                    x.PlatformType == PlatformType.TouTiao.ToString() ||
                    x.PlatformType == PlatformType.TouTiaoSaleShop.ToString())
                .Select(x => x.ShopId).ToList();
            var ptUids = userShops.Where(x => x.PlatformType == PlatformType.KuaiShou.ToString()).Select(x => x.ShopId).ToList();

            var serviceAppIds = userShops.Where(x => string.IsNullOrWhiteSpace(x.ShopExtension?.AppKey) == false).Select(x => x.ShopExtension.AppKey).Distinct().ToList();
            if (appShopIds.Count > 0 && ptShopsIds.Count > 0)
                serviceAppOrders = _userService.GetSuccessServiceOrdersByShopIds(appShopIds, ptShopsIds, serviceAppIds)?.OrderByDescending(x => x.PayTime).ToList();
            if (ptUids.Count > 0)
            {
                var ksAppOrders = _userService.GetLastEndOrdersWhere(appShopIds, ptUids, PlatformType.KuaiShou.ToString());
                if (ksAppOrders != null && ksAppOrders.Any())
                    serviceAppOrders.AddRange(ksAppOrders);
            }

            //拼多多是否使用打单应用
            var isUsePrintSystemApp = new CommonSettingService().PddIsUsePrintSystemApp();

            #region 获取当前(授权,服务)过期
            //需要更新的集合
            var uplist = new ConcurrentBag<FxUserShop>();
            Parallel.ForEach(newResult, new ParallelOptions { MaxDegreeOfParallelism = 10 }, (result) =>
            {
                var itemShop = userShops.FirstOrDefault(e => e.Id == result.ShopId);
                if (itemShop == null)
                    return;
                //因为没有走接口校验，需要使用同步任务表消息来校验是否不是过期
                if (!isCheckAuth)
                    itemShop.LastSyncMessage = result.LastSyncMessage;

                result.PddIsUsePrintSystemApp = isUsePrintSystemApp;

                if (itemShop.IsAuthExpired)
                {
                    //如果原状态没过期就加入更新集合
                    if (result.Status != FxUserShopStatus.AuthExpired)
                    {
                        result.Status = FxUserShopStatus.AuthExpired;
                        uplist.Add(new FxUserShop()
                        {
                            Id = result.Id,
                            Status = result.Status
                        });
                    }
                    if (!item1.Any(u => u.Id == result.Id))
                    {
                        item1.Add(result);
                    }
                }

                //var appKey = itemShop.ShopExtension?.AppKey ?? "";
                //var ps = PlatformFactory.GetPlatformService(itemShop);
                //var appservice = AppServiceFactory.GetAppService(itemShop.PlatformType);
                //var fxApp = appservice?.getAppModel(appKey);
                //if (fxApp != null)
                //{
                //    result.AuthUrl = fxApp.AuthUrl;
                //    if (appKey.IsNullOrEmpty())
                //        appKey = fxApp.Appkey;
                //}
                //var marketinfo = ps.GetPlatformMarket();
                //if(marketinfo != null)
                //{
                //    result.PlatformAuthUrl = marketinfo.AuthApi;                    
                //}
                if (!string.IsNullOrEmpty(result.PlatformPayUrl))
                {

                    if (itemShop == null)
                        return;
                    //// 分单系统使用分单过期时间（区分打单和分单应用平台）
                    //if (CustomerConfig.DiffAppPlatformType().Contains(itemShop.PlatformType))
                    //    itemShop.ExpireTime = result.AuthTime;
                    //旧的过期时间,状态
                    var oldExTime = result.AuthTime;
                    var oldStatus = result.Status;
                    //// 分单系统使用分单过期时间
                    //itemShop.ExpireTime = result.AuthTime;

                    // 应用授权服务时间和授权地址
                    GetAppServiceModel(fxUserId, result, userShops, serviceAppOrders);
                    if (result.Status == FxUserShopStatus.AuthExpired)
                    {
                        if (!item1.Any(u => u.Id == result.Id))
                        {
                            item1.Add(result);
                        }
                    }

                    //新的到期时间比较数据库的到期时间
                    var isEqual = result.AuthTime != oldExTime;
                    //当前用户的服务到期时间是否过期
                    var isExpire = result.AuthTime < DateTime.Now;
                    if (isEqual || isExpire)
                    {
                        if (isExpire)
                        {
                            if (!item1.Any(u => u.Id == result.Id))
                            {
                                item1.Add(result);
                            }
                        }

                        if (!uplist.Any(u => u.Id == result.Id))
                        {
                            var newStatus = isExpire ? FxUserShopStatus.AuthExpired : FxUserShopStatus.Binded;
                            if (isEqual || oldStatus != newStatus)
                            {
                                uplist.Add(new FxUserShop()
                                {
                                    Id = result.Id,
                                    AuthTime = result.AuthTime,
                                    Status = newStatus
                                });
                            }
                        }
                        else
                        {
                            foreach (var up in uplist)
                            {
                                if (up.Id == result.Id)
                                {
                                    up.AuthTime = result.AuthTime;
                                    if (isExpire)
                                        up.Status = FxUserShopStatus.AuthExpired;
                                }
                            }
                        }
                    }
                }
                else
                {
                    //如果商品没有服务市场到期时间,同步也没有过期消息,就需要把状态改回去
                    if (!uplist.Any(u => u.Id == result.Id))
                    {
                        if (result.Status != FxUserShopStatus.Binded)
                        {
                            uplist.Add(new FxUserShop()
                            {
                                Id = result.Id,
                                Status = FxUserShopStatus.Binded
                            });
                        }
                    }
                }
            });

            //更新状态,过期时间
            //if (uplist.Count > 0)
            //{
            //    ThreadPool.QueueUserWorkItem(state =>
            //    {
            //        try
            //        {
            //            _repository.UpdateAuthTimeOrStatus(uplist.ToList());
            //        }
            //        catch (Exception ex)
            //        {
            //            Log.WriteError($"GetAboutToExpireOrExpiredShopByFxUserId执行更新状态/时间时发生错误：{ex}");
            //        }
            //    });
            //}
            #endregion

            #region 服务即将到期或已经到期
            var day = CustomerConfig.EndServiceDay;
            newResult.ForEach(f =>
            {
                if (!string.IsNullOrEmpty(Convert.ToString(f.AuthTime)))
                {
                    TimeSpan leaveData = Convert.ToDateTime(f.AuthTime) - DateTime.Now;
                    double datanum = leaveData.TotalDays;
                    var endServiceDateNum = ((int)Math.Ceiling(datanum)).ToString();

                    if (Convert.ToDouble(endServiceDateNum) < day)
                    {
                        if (!string.IsNullOrEmpty(f.PlatformPayUrl))
                        {
                            item2.Add(f);
                        }
                    }
                }
            });
            #endregion

            return Tuple.Create(item1.ToList(), item2);
        }

        public void GetAppServiceModel(int fxUserId, FxUserShop r, List<Shop> shops, List<ServiceAppOrder> serviceAppOrders)
        {
            if (r.PlatformType == PlatformType.Virtual.ToString() || r.PlatformType == PlatformType.System.ToString())
            {
                r.AppName = "-";
                return;
            }
            var itemShop = shops.FirstOrDefault(s => s.Id == r.ShopId);
            if (itemShop == null)
                return;

            try
            {
                var appservice = AppServiceFactory.GetAppService(r.PlatformType);
                if (appservice == null)
                    return;
                var appKey = itemShop?.ShopExtension?.AppKey;
                var lastapp = appservice.getAppModel(appKey);
                if (appKey.IsNullOrEmpty() && lastapp != null)
                    appKey = lastapp.Appkey;

                ShopExtension tempShopExtension = null;
                if (itemShop.ShopExtension != null)
                    tempShopExtension = itemShop.ShopExtension.ToJson().ToObject<ShopExtension>();

                var ps = PlatformFactory.GetPlatformServiceByShop(itemShop);
                //重新赋值
                itemShop.ShopExtension = tempShopExtension;

                var lastorder = serviceAppOrders.Where(x => x.ServiceAppId == appKey && (x.ShopId == itemShop.Id || x.PlatformShopId == itemShop.ShopId || x.PlatformShopId == itemShop.Uid)).OrderByDescending(x => x.ServiceEnd).FirstOrDefault();
                DateTime? authTime = null;
                //itemShop.ExpireTime = r.AuthTime;
                if (itemShop.ShopExtension != null)
                {
                    // 存在分销应用的平台过期时间需要先更新成AuthTime，平台类中会取店铺的过期时间判断是否需要调接口更新分销过期时间
                    // 头条续费都使用分销应用(只用于前端跳转到分销应用)
                    if (itemShop.PlatformType == PlatformType.TouTiao.ToString())
                    {
                        // 头条有2个分单应用，根据订购截止时间取剩余时间最长的应用，有混合订购记录提前2天显示续费按钮，只有新应用显示续费按钮
                        var showRechargeTime = DateTime.Now.AddDays(2);
                        if (itemShop.ShopExtension.AppKey == CustomerConfig.TouTiaoFxNewAppKey)
                        {
                            appKey = CustomerConfig.TouTiaoFxNewAppKey;
                            lastapp = appservice.getAppModel(appKey);
                            lastorder = serviceAppOrders.Where(x => x.ServiceAppId == appKey && (x.ShopId == itemShop.Id || x.PlatformShopId == itemShop.ShopId)).OrderByDescending(x => x.ServiceEnd).FirstOrDefault();
                            //authTime = lastorder?.ServiceEnd ?? r.AuthTime;
                            r.PlatformPayUrl2 = lastapp?.MarketUrl; // 配合前端显示续费按钮
                        }
                        else if (itemShop.ShopExtension.AppKey == CustomerConfig.TouTiaoFxListingAppKey)
                        {
                            appKey = CustomerConfig.TouTiaoFxListingAppKey;//分单铺货应用2024.07.04
                            lastapp = appservice.getAppModel(appKey);
                            lastorder = serviceAppOrders.Where(x => x.ServiceAppId == appKey && (x.ShopId == itemShop.Id || x.PlatformShopId == itemShop.ShopId)).OrderByDescending(x => x.ServiceEnd).FirstOrDefault();
                            //authTime = lastorder?.ServiceEnd ?? r.AuthTime;
                            r.PlatformPayUrl2 = lastapp?.MarketUrl; // 配合前端显示续费按钮
                        }
                        else
                        {
                            appKey = CustomerConfig.TouTiaoFxAppKey;
                            lastapp = appservice.getAppModel(appKey);
                            r.PlatformPayUrl2 = lastapp?.MarketUrl; // 配合前端显示续费按钮
                            lastorder = serviceAppOrders.Where(x => x.ServiceAppId == appKey && (x.ShopId == itemShop.Id || x.PlatformShopId == itemShop.ShopId)).OrderByDescending(x => x.ServiceEnd).FirstOrDefault();
                            if (lastorder == null || lastorder.ServiceEnd < showRechargeTime)
                            {
                                // 铺货代发应用订购时间长，且服务到期时间不超过2天时，则显示续费按钮使用新应用续费
                                appKey = CustomerConfig.TouTiaoFxNewAppKey;
                                lastapp = appservice.getAppModel(appKey);
                                //authTime = lastorder?.ServiceEnd ?? r.AuthTime;
                                r.PlatformPayUrl2 = lastapp?.MarketUrl; // 旧应用到期配合前端显示续费按钮
                            }
                            else
                            {
                                // 铺货代发应用订购时间长，且存在分销订购服务截止时间超过2天不显示续费按钮
                                r.PlatformPayUrl2 = "";
                            }
                        }

                        //获取接口的到期时间
                        //itemShop.IsSyncServierTime = true;
                    }
                    else if (itemShop.PlatformType == PlatformType.TouTiaoSaleShop.ToString())
                    {
                        appKey = CustomerConfig.FxTouTiaoSaleShopAppKey;
                        lastapp = appservice.getAppModel(appKey);
                        lastorder = serviceAppOrders.Where(x => x.ServiceAppId == appKey && (x.ShopId == itemShop.Id || x.PlatformShopId == itemShop.ShopId)).OrderByDescending(x => x.ServiceEnd).FirstOrDefault();
                        r.PlatformPayUrl2 = lastapp?.MarketUrl;
                    }
                    else if (itemShop.PlatformType == PlatformType.Alibaba.ToString())
                    {
                        r.PlatformPayUrl2 = lastapp?.MarketUrl;
                        r.TouTiaoOldOrNew = "Fx";
                    }
                    else if (itemShop.PlatformType == PlatformType.KuaiShou.ToString())
                    {
                        r.PlatformPayUrl2 = lastapp?.MarketUrl;
                        //authTime = lastorder?.ServiceEnd ?? r.AuthTime;
                        //itemShop.IsSyncServierTime = true;
                        if (itemShop.ShopExtension.AppKey == CustomerConfig.KuaiShouNewFxAppKey)
                        {
                            r.TouTiaoOldOrNew = "New";
                        }
                    }
                    // else if (itemShop.PlatformType == PlatformType.WxVideo.ToString())
                    // {
                    //     if (appKey == CustomerConfig.Fx_WxComponentNewAppId)
                    //     {
                    //         var tempKey = CustomerConfig.Fx_WxShopNewAppId;
                    //         var tempApp = appservice.getAppModel(tempKey);
                    //         r.PlatformPayUrl2 = tempApp?.MarketUrl; // 配合前端显示续费按钮
                    //     }
                    // }

                    authTime = itemShop?.ShopExtension?.ExpireTime ?? lastorder?.ServiceEnd;
                }
                else
                {
                    //lastorder = serviceAppOrders.Where(x => x.ShopId == itemShop.Id || x.PlatformShopId == itemShop.ShopId).OrderByDescending(x => x.ServiceEnd).FirstOrDefault();
                    if (itemShop.PlatformType == PlatformType.TouTiao.ToString())
                    {
                        // 订购老应用服务到期后跳转到新应用
                        if (lastorder == null || lastorder.ServiceEnd < DateTime.Now)
                        {
                            lastapp = appservice.getAppModel(CustomerConfig.TouTiaoFxNewAppKey);
                            r.PlatformPayUrl2 = lastapp?.MarketUrl; // 旧应用到期配合前端显示续费按钮
                            //authTime = lastorder?.ServiceEnd ?? r.AuthTime; //切换成新应用不能通过平台类获取服务时间，需要取订购服务截止时间
                            authTime = lastorder?.ServiceEnd ?? itemShop.ExpireTime;
                        }
                    }
                    else
                    {
                        appKey = lastapp.Appkey;
                        authTime = lastorder?.ServiceEnd ?? itemShop.ExpireTime;
                    }
                }

                //if (r.IsAuthExpired == false || itemShop.IsAuthExpired == false)
                //没有前置校验授权的话，itemShop.LastSyncMessage上的消息是不准的，要以同步表上为准
                if (r.IsAuthExpired == false)
                {
                    r.Status = FxUserShopStatus.Binded;
                    try
                    {
                        // 取平台过期时间会抛异常导致不显示续费、重新授权
                        authTime = authTime ?? (ps.GetExpiredTime() ?? lastorder?.ServiceEnd ?? r.AuthTime);
                    }
                    catch (Exception)
                    {
                        authTime = itemShop?.ShopExtension?.ExpireTime ?? lastorder?.ServiceEnd ?? itemShop.ExpireTime ?? r.AuthTime;
                    }
                }
                else
                {
                    authTime = itemShop?.ShopExtension?.ExpireTime ?? lastorder?.ServiceEnd ?? itemShop.ExpireTime ?? r.AuthTime;
                    r.Status = FxUserShopStatus.AuthExpired;
                }

                r.NickName = r.NickName;
                r.AppKey = appKey.ToString2();
                r.AuthTime = authTime.Value;
                r.AuthUrl = lastapp?.AuthUrl;
                r.PlatformAuthUrl = lastapp?.AuthUrl;
                r.PlatformPayUrl2 = r.PlatformPayUrl2.ToString2();
                r.AppName = lastapp?.Name;

                //if (r.PlatformType != PlatformType.KuaiShou.ToString())
                //{
                //    var oldAppKey = itemShop?.ShopExtension?.AppKey ?? "";
                //    if (CustomerConfig.FxSystemAppKeyNameDict.ContainsKey(oldAppKey))
                //        r.AppName = CustomerConfig.FxSystemAppKeyNameDict[oldAppKey];
                //    else
                //        r.AppName = "店管家打单";
                //}
            }
            catch (LogicException logex)
            {
                if (logex.Message.Contains("授权过期") || logex.Message.Contains("授权已过期"))
                {
                    r.Status = FxUserShopStatus.AuthExpired;
                }
            }
            catch (Exception ex)
            {
                var exShop = new
                {
                    FxUserId = fxUserId,
                    Id = itemShop.Id,
                    ShopId = itemShop.ShopId,
                    AccessToken = itemShop.AccessToken,
                    RefreshToken = itemShop.RefreshToken,
                    AppKey = itemShop.ShopExtension.AppKey ?? "打单AppKey",
                    AppSecret = itemShop.ShopExtension.AppSecret ?? "打单AppSecret"
                };
                Log.WriteError($"查询店铺列表请求接口失败,当前账号信息:{exShop.ToJson()},错误消息:{ex}");
            }
        }

        public List<Shop> GetBindShops(int fxId)
        {
            return _repository.GetBindShops(fxId);
        }

        /// <summary>
        /// 已绑定的店铺（指定用户+指定店铺Id）
        /// </summary>
        /// <param name="fxId"></param>
        /// <param name="sids"></param>
        /// <returns></returns>
        public List<Shop> GetBindShops(int fxId, List<int> sids)
        {
            return _repository.GetBindShops(fxId, sids);
        }

        public List<FxUserShop> GetBindFxShops(List<int> sids)
        {
            var exceptionHandler = new ExceptionHandler("FxUserShopService.GetBindFxShops");
            var models = exceptionHandler.ExceptionRetryHandler(() => _repository.GetBindFxShops(sids));
            return models;
        }

        public List<Shop> GetSystemShops(List<int> fxIds)
        {
            return _repository.GetSystemShops(fxIds);
        }

        public UserFunctionAvailableCountCheckResultModel IsFxUserIsHaveAvaliableCount(int fxUserId, ServiceVersionTypeEnum serviceVersionType)
        {
            //检查白名单是否开启
            var isShouldPay = new CommonSettingService().IsFxUserShouldPay(fxUserId);
            if (isShouldPay)
                return _repository.CheckFxUserIsHaveAvaliableCount(fxUserId, serviceVersionType);
            else
                return new UserFunctionAvailableCountCheckResultModel { ExisitCount = 0, AvailableCount = 1, FxUserId = fxUserId };
        }

        public void UpdateNickName(Dictionary<int, string> shopIdNickNameDict)
        {
            _repository.UpdateNickName(shopIdNickNameDict);
        }

        /// <summary>
        /// 查询指定平台类型的商铺，只返回Id,ShopId,FxUserId
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <param name="platformType"></param>
        /// <param name="shopIds">指定ShopIds</param>
        /// <returns></returns>
        public List<FxUserShop> GetShopListByPlatformType(List<int> fxUserIds, string platformType, List<int> shopIds)
        {
            return _repository.GetShopListByPlatformType(fxUserIds, platformType, shopIds);
        }

        /// <summary>
        /// 获取当前用户1688店铺，并确认是否开通分销
        /// </summary>
        /// <param name="isOpenDistribution">是否开通分销</param>
        /// <param name="isOpenDistribution">isOpenWangshangPay</param>
        /// <returns></returns>
        public List<Shop> GetCurrentUserShopsByAlibaba(bool isOpenDistribution = true, bool isOpenWangshangPay = false)
        {
            var shopRepository = new ShopRepository();
            var shops = shopRepository.GetAlibabaQingShopsByFxUserId(SiteContext.Current.CurrentFxUserId);
            //判空处理
            if (shops == null || !shops.Any())
            {
                return new List<Shop>();
            }

            if (isOpenDistribution == true || isOpenWangshangPay == true)
            {
                Parallel.ForEach(shops, new ParallelOptions { MaxDegreeOfParallelism = 5 }, (shop) =>
                {
                    try
                    {
                        var service = new AlibabaQingPlatformService(shop);

                        string str = "";
                        if (isOpenDistribution == true)
                        {
                            var hasJoinDistribution = service.HasJoinDistribution();
                            str += hasJoinDistribution ? "，已开通分销" : "，未开通分销";
                            shop.Has1688JoinDistribution = hasJoinDistribution;
                        }
                        if (isOpenWangshangPay == true)
                        {
                            var hasOpenWangShangPay = service.HasOpenWangShangPay();
                            str += hasOpenWangShangPay.Item1 ? "，已开通采购金支付" : "，未开通采购金支付";
                            shop.Has1688OpenWangShangPay = hasOpenWangShangPay.Item1;
                        }
                        str = str.TrimStart("，");
                        shop.NickName += $"（{str}）";
                    }
                    catch
                    {
                        // ignored
                    }
                });
            }
            return shops;
        }

        /// <summary>
        /// 获取1688下单店铺店铺名
        /// </summary>
        /// <returns></returns>
        public string GetCurrentUserShopNickNameByAlibaba()
        {
            var alibabaOpenShop = GetCurrentUserShopsByAlibaba(false);
            var settings = new BusinessSettingsService().GetsByCurrentUser(new List<string> { BusinessSettingKeys.SupplyBy1688.ShopBy1688 });

            string synShopName = "";
            if (!alibabaOpenShop.IsNullOrEmptyList() && !settings.IsNullOrEmptyList())
            {
                synShopName = alibabaOpenShop.Where(p => p.Id.ToString() == settings[0].Value).OrderByDescending(p => p.CreateTime).FirstOrDefault()?.NickName ?? "";
            }
            return synShopName;
        }

        /// <summary>
        /// 查询店铺是否迁移至抖店云了
        /// </summary>
        /// <param name="shopIds">指定ShopIds</param>
        /// <returns></returns>
        public List<int> GetMigratedToTouTiaoCloudShopIds(List<int> shopIds)
        {
            return _repository.GetMigratedToTouTiaoCloudShopIds(shopIds);
        }
        public List<FxUserShop> GetShopsByFxUserIds(List<int> fxUserIds, List<int> shopIds = null, List<string> fields = null, bool rmVirtualShop = false)
        {
            return _repository.GetShopsByFxUserIds(fxUserIds, shopIds, fields, rmVirtualShop);
        }

        /// <summary>
        /// 获取用户所属店铺信息
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="platformType"></param>
        /// <returns></returns>
        public List<FxUserWithShopIdModel> GetFxUserWithShopIds(int fxUserId, string platformType)
        {
            return _repository.GetFxUserWithShopIds(fxUserId, platformType);
        }

        /// <summary>
        /// 通过单号分享关系Id获取ToId对应的FxUserId
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public List<FxUserWithDbNameIdModel> GetFxUserIdByBranchShareRelationId(List<int> ids)
        {
            if (ids == null || ids.Any() == false)
                return new List<FxUserWithDbNameIdModel>();

            var list = new List<FxUserWithDbNameIdModel>();
            var batchSize = 500;
            var count = Math.Ceiling(ids.Count * 1.0 / batchSize);
            for (var i = 0; i < count; i++)
            {
                var batchCodes = ids.Skip(i * batchSize).Take(batchSize).ToList();
                var batchList = _repository.GetFxUserIdByBranchShareRelationId(batchCodes);
                if (batchList != null)
                    list.AddRange(batchList);
            }
            return list;
        }
        /// <summary>
        /// 通过SystemShopId获取对应的用户
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public List<FxUserWithDbNameIdModel> GetFxUserIdBySystemShopId(List<int> ids)
        {
            if (ids == null || ids.Any() == false)
                return new List<FxUserWithDbNameIdModel>();

            var list = new List<FxUserWithDbNameIdModel>();
            var batchSize = 500;
            var count = Math.Ceiling(ids.Count * 1.0 / batchSize);
            for (var i = 0; i < count; i++)
            {
                var batchCodes = ids.Skip(i * batchSize).Take(batchSize).ToList();
                var batchList = _repository.GetFxUserIdBySystemShopId(batchCodes);
                if (batchList != null)
                    list.AddRange(batchList);
            }
            return list;
        }

        public void UpdateFxUserShopInfo(List<FxUserShop> fxUserShops)
        {
            _repository.UpdateFxUserShopInfo(fxUserShops);
        }

        /// <summary>
        /// 是否有指定平台类型的店铺
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="platformTypes"></param>
        /// <returns></returns>
        public bool IsHasShop(int fxUserId, List<string> platformTypes)
        {
            if (fxUserId < 0 || platformTypes == null || platformTypes.Any() == false)
                return false;
            return _repository.IsHasShop(fxUserId, platformTypes);
        }

        /// <summary>
        /// 支持铺货平台的店铺订购普通应用的情况
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public List<ShopSubscribeAppView> GetSubscribeNormalAppShops(ShopSubscribeAppQuery model)
        {
            //支持铺货的平台
            var platformTypes = CustomerConfig.FxListingSupportPlatformTypes;
            model.SupportPlatformTypes = platformTypes;
            if (model.FxUserId < 0 || platformTypes == null || platformTypes.Any() == false)
                return new List<ShopSubscribeAppView>();


            if (model.ShopIds != null && model.ShopIds.Any())
            {
                var pathFlowList = new PathFlowService().GetPathFlowByFxUserId(model.FxUserId);
                //与当前用户相关的店铺
                var shopIds = pathFlowList.Where(f => f.SourceShopId > 0).Select(f => f.SourceShopId).Distinct()
                    .ToList();

                model.ShopIds = model.ShopIds.Where(a => shopIds.Contains(a)).ToList();
                if (model.ShopIds.Any() == false)
                    return new List<ShopSubscribeAppView>();
            }

            var result = _repository.GetSubscribeNormalAppShops(model);
            //店铺名称处理
            result.ForEach(x =>
            {
                if (x.SourceFxUserId != model.FxUserId)
                {
                    x.ShopName = string.Empty;
                    x.NickName = string.Empty;
                }
            });
            return result;
        }

        ///// <summary>
        ///// 是否有指定平台类型的店铺(通过系统店铺id判断)
        ///// </summary>
        ///// <param name="fxUserId"></param>
        ///// <param name="platformTypes"></param>
        ///// <returns></returns>
        //public bool IsHasShopBySystemShopId(int systemShopId, List<string> platformTypes)
        //{
        //    if (systemShopId < 0 || platformTypes.IsNullOrEmptyList())
        //        return false;
        //    return _repository.IsHasShopBySystemShopId(systemShopId, platformTypes);
        //}
        
        /// <summary>
        /// 生成新的店铺绑定关系并生成对应的同步状态和同步任务
        /// </summary>
        /// <param name="model"></param>
        public void CreateNewFxUserShop(FxUserShop model)
        {
            var syncStatusService = new SyncStatusService();
            var syncTaskService = new SyncTaskService();
            
            // 新增用户店铺
            AddFxUserShop(model);
            
            // 新增订单同步状态
            syncStatusService.AddSyncStatus(new SyncStatus
            {
                FxUserId = model.FxUserId,
                ShopId = model.ShopId,
                SyncType = ShopSyncType.Order,
                CreateTime = DateTime.Now,
                Source = OwnerSource.FenDanSystem.ToString()
            });
            
            // 新增商品同步状态
            syncStatusService.AddSyncStatus(new SyncStatus
            {
                FxUserId = model.FxUserId,
                ShopId = model.ShopId,
                SyncType = ShopSyncType.Product,
                CreateTime = DateTime.Now,
                Source = OwnerSource.FenDanSystem.ToString()
            });
            
            // 新增售后同步状态
            syncStatusService.AddSyncStatus(new SyncStatus
            {
                FxUserId = model.FxUserId,
                ShopId = model.ShopId,
                SyncType = ShopSyncType.AfterSale,
                CreateTime = DateTime.Now,
                Source = OwnerSource.FenDanSystem.ToString()
            });
            
            // 新增订单同步任务
            syncTaskService.AddTask(new SyncTask
            {
                FxUserId = model.FxUserId,
                ShopId = model.ShopId,
                TaskType = SyncTaskType.Order,
                Status = SyncTaskStatus.Wait,
                CreateTime = DateTime.Now,
                Source = OwnerSource.FenDanSystem.ToString()
            });

            // 新增售后同步任务
            syncTaskService.AddTask(new SyncTask
            {
                FxUserId = model.FxUserId,
                ShopId = model.ShopId,
                TaskType = SyncTaskType.AfterSale,
                Status = SyncTaskStatus.Wait,
                CreateTime = DateTime.Now,
                Source = OwnerSource.FenDanSystem.ToString()
            });
        }
    }
}
