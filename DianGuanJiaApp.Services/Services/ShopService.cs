using System;
using System.Collections.Generic;
using System.Linq;
using Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Services.PlatformService;
using System.Threading;
using System.Threading.Tasks;
using DianGuanJiaApp.Data.Dapper;
using DianGuanJiaApp.Data;
using System.Web;
using DianGuanJiaApp.Data.FxModel;
using DianGuanJiaApp.Services.ServicesExtension;
using DianGuanJiaApp.Trace.Services.Services;
using DianGuanJiaApp.Trace.ViewModels.Models;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Data.FxModel.Tools;

namespace DianGuanJiaApp.Services
{

    public partial class ShopService : BaseService<Data.Entity.Shop>
    {
        /// <summary>
        /// 场景
        /// </summary>
        private string _scene = string.Empty;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="scene">场景：listing表示铺货场景，all表示全部，其他值表示常规场景</param>
        public ShopService(string scene = "")
        {
            _repository = new ShopRepository(scene);
            this._baseRepository = _repository;
            _scene = scene;
        }
        public ShopService()
        {
            _repository = new ShopRepository();
            this._baseRepository = _repository;
        }

        #region 私有变量
        private readonly SyncStatusService _syncStatusService = new SyncStatusService();
        private ShopRepository _repository;
        protected IPlatformService _service;
        private CommonSettingService _commonSettingService = new CommonSettingService();
        #endregion

        public List<Shop> GetAllShops()
        {
            return SiteContext.Current.AllShops;
        }

        /// <summary>
        /// 设置店铺的增量同步状态
        /// </summary>
        /// <param name="shopIds">店铺ID</param>
        /// <param name="syncStatus">同步状态</param>
        /// <param name="synCompleteTime">同步截止时间</param>
        public void SetShopStatus(List<int> shopIds, Data.Enum.ShopSyncStatusType syncStatus, DateTime? synCompleteTime = null, string lastSyncMessage = "", bool isUpdateFullSyncStatus = false, bool isEnabledSyncProcess = false)
        {
            //设置店铺同步状态，如果失败，重试三次
            for (var i = 0; i < 3; i++)
            {
                try
                {
                    _repository.SetShopStatus(shopIds, syncStatus, synCompleteTime, lastSyncMessage, isUpdateFullSyncStatus);
                    //if (isEnabledSyncProcess)
                    //{
                    //    if (syncStatus == ShopSyncStatusType.Syncing)
                    //        _repository.ResetSyncInfo(shopIds.FirstOrDefault());
                    //    else
                    //        _repository.FinishSyncInfo(shopIds.FirstOrDefault(), syncStatus, lastSyncMessage);
                    //}
                    break;
                }
                catch (Exception ex)
                {
                    System.Threading.Thread.Sleep(2000);
                    //_repository.SetShopStatus(shopIds, syncStatus, synCompleteTime, lastSyncMessage, isUpdateFullSyncStatus);
                    //Log.WriteError($"设置店铺同步状态时发生错误：店铺ID:{shopIds.ToJson()},同步状态：{syncStatus},同步完成时间：{synCompleteTime},同步消息：{lastSyncMessage},错误信息：{ex}");
                }
            }
        }

        /// <summary>
        /// 设置店铺的增量同步状态
        /// </summary>
        /// <param name="shopIds">店铺ID</param>
        /// <param name="syncStatus">同步状态</param>
        /// <param name="synCompleteTime">同步截止时间</param>
        public void SetShopStatus(Shop shop, ShopSyncStatusType syncStatus, SyncOrderParametersModel syncParameter, string lastSyncMessage = "", bool isUpdateFullSyncStatus = false, bool isEnabledSyncProcess = false)
        {
            //设置店铺同步状态，如果失败，重试三次
            for (var i = 0; i < 3; i++)
            {
                var shopIds = new List<int> { shop.Id };
                try
                {
                    //拼多多厂家用户，如果进入的是代发订单列表，则同步时间及状态保存到配置
                    if (shop.IsPddFactorer == true && syncParameter?.IsSyncPddFdsOrder == true)
                    {
                        (new CommonSettingService()).SetOrderSyncStatus(shop.Id, syncStatus, syncParameter?.StartTime, syncParameter?.EndTime, lastSyncMessage, isUpdateFullSyncStatus);
                    }
                    else if (shop.IsTaoaoBigShop)
                    {
                        InitTaoBigShopSyncStatus(shop);
                        _syncStatusService.SetShopSyncStatus(shop, syncStatus, syncParameter, lastSyncMessage, isUpdateFullSyncStatus, isEnabledSyncProcess, 11);
                    }
                    else
                    {
                        _repository.SetShopStatus(shopIds, syncStatus, syncParameter?.EndTime, lastSyncMessage, isUpdateFullSyncStatus);
                    }
                    //if (isEnabledSyncProcess)
                    //{
                    //    if (syncStatus == ShopSyncStatusType.Syncing)
                    //        _repository.ResetSyncInfo(shopIds.FirstOrDefault());
                    //    else
                    //        _repository.FinishSyncInfo(shopIds.FirstOrDefault(), syncStatus, lastSyncMessage);
                    //}
                    break;
                }
                catch (Exception ex)
                {
                    System.Threading.Thread.Sleep(2000);
                    //_repository.SetShopStatus(shopIds, syncStatus, synCompleteTime, lastSyncMessage, isUpdateFullSyncStatus);
                    //Log.WriteError($"设置店铺同步状态时发生错误：店铺ID:{shopIds.ToJson()},同步状态：{syncStatus},同步完成时间：{synCompleteTime},同步消息：{lastSyncMessage},错误信息：{ex}");
                }
            }
        }




        /// <summary>
        /// 设置店铺的增量同步状态
        /// </summary>
        /// <param name="shopIds">店铺ID</param>
        /// <param name="syncStatus">同步状态</param>
        /// <param name="synCompleteTime">同步截止时间</param>
        public void SetShopHotOrderSyncStatus(List<int> shopIds, Data.Enum.ShopSyncStatusType syncStatus, DateTime? synCompleteTime = null, string lastSyncMessage = "", bool isUpdateFullSyncStatus = false, bool isFullSync = false)
        {
            //设置店铺同步状态，如果失败，重试三次
            for (var i = 0; i < 3; i++)
            {
                try
                {
                    _repository.SetShopHotOrderSyncStatus(shopIds, syncStatus, synCompleteTime, lastSyncMessage, isUpdateFullSyncStatus, isFullSync);
                    break;
                }
                catch (Exception ex)
                {
                    System.Threading.Thread.Sleep(2000);
                    //_repository.SetShopStatus(shopIds, syncStatus, synCompleteTime, lastSyncMessage, isUpdateFullSyncStatus);
                    //Log.WriteError($"设置店铺同步状态时发生错误：店铺ID:{shopIds.ToJson()},同步状态：{syncStatus},同步完成时间：{synCompleteTime},同步消息：{lastSyncMessage},错误信息：{ex}");
                }
            }
        }

        public HotDataSyncStatus CreateHotDataSyncStatus(int shopId)
        {
            return _repository.CreateHotDataSyncStatus(shopId);
        }


        public Shop GetByOnlyCode(string onlyCode)
        {
            var shopModel = _repository.Get(" WITH(NOLOCK) WHERE OnlyCode=@OnlyCode;", new { OnlyCode = onlyCode }).FirstOrDefault();
            return shopModel;
        }
        public List<ShopRelation> GetShopRelations(int shopId)
        {
            var db = _repository.DbConnection;
            var rs = db.Query<ShopRelation>($"select * from P_ShopRelation WITH(NOLOCK) where ShopId={shopId}").ToList();
            return rs;
        }
        /// <summary>
        /// 设置退款订单增量同步状态
        /// </summary>
        /// <param name="shopIds">店铺ID</param>
        /// <param name="syncStatus">同步状态</param>
        /// <param name="synCompleteTime">同步截止时间</param>
        public void SetShopRefundOrderSyncStatus(List<int> shopIds, Data.Enum.ShopSyncStatusType syncStatus, DateTime? startTime = null, string lastSyncMessage = "")
        {
            //设置店铺同步状态，如果失败，重试三次
            for (var i = 0; i < 3; i++)
            {
                try
                {
                    _repository.SetShopRefundOrderSyncStatus(shopIds, syncStatus, startTime, lastSyncMessage);
                    break;
                }
                catch
                {
                    System.Threading.Thread.Sleep(2000);
                    //_repository.SetShopRefundOrderSyncStatus(shopIds, syncStatus, startTime, lastSyncMessage);
                    //Log.WriteError($"设置店铺同步状态时发生错误：店铺ID:{shopIds.ToJson()},同步状态：{syncStatus},同步完成时间：{synCompleteTime},同步消息：{lastSyncMessage},错误信息：{ex}");
                }
            }
        }

        public void UpdateAccessToken(Shop shop)
        {
            //var sameShop = _repository.GetByAccessToken(shop.Id, shop.AccessToken, shop.RefreshToken);
            //if (sameShop != null)
            //    _repository.AddSameAccessTokenLog("UpdateAccessToken", shop, sameShop);
            //else
            if (string.IsNullOrEmpty(shop.AccessToken))
                return;
            _repository.UpdateAccessToken(shop.Id, shop.AccessToken);
        }
        public void UpdateAccessToken(int shopId, string accessToken, string refreshToken = "", DateTime? time = null)
        {
            if (string.IsNullOrEmpty(accessToken) && string.IsNullOrEmpty(refreshToken))
                return;
            _repository.UpdateAccessToken(shopId, accessToken, refreshToken, time);
        }

        public void UpdateShopAccessToken(int shopId, string appkey, string accessToken, string refreshToken = "", DateTime? time = null)
        {
            if (string.IsNullOrEmpty(accessToken) && string.IsNullOrEmpty(refreshToken))
                return;
            if (string.IsNullOrEmpty(appkey))
            {
                _repository.UpdateAccessToken(shopId, accessToken, refreshToken, time);
            }
            else
            {
                new ShopExtensionRepository().UpdateAccessToken(appkey, shopId, accessToken, refreshToken);
            }
        }

        public void UpdateExpiredTime(int shopId, DateTime time)
        {
            _repository.UpdateExpireTime(shopId, time);
        }

        public void UpdateExpiredTimeAndSystemVersion(int shopId, DateTime expiredTime, string systemVersion)
        {
            _repository.UpdateExpiredTimeAndSystemVersion(shopId, expiredTime, systemVersion);
        }
        public void UpdateSystemVersion(int shopId, string systemVersion)
        {
            _repository.UpdateSystemVersion(shopId, systemVersion);
        }
        public bool UpdateLastSyncTime(int shopId, DateTime time)
        {
            return _repository.UpdateLastSyncTime(shopId, time);
        }

        public void UpdateLastSyncMessage(int shopId, string lastSyncMessage)
        {
            _repository.UpdateLastSyncMessage(shopId, lastSyncMessage);
        }

        public void UpdateOrderSyncStatusLastSyncMessage(int shopId, string lastSyncMessage)
        {
            _repository.UpdateOrderSyncStatusLastSyncMessage(shopId, lastSyncMessage);
        }

        /// <summary>
        /// 验证是否是淘大店
        /// </summary>
        /// <param name="shop"></param>
        /// <returns></returns>
        public bool CheckTaopBigShop(Shop shop)
        {
            const string key = "/User/Shop/CheckTaopBigShop";
            string cacheValue = RedisHelper.Get(key);
            if (cacheValue.IsNotNullOrEmpty())
            {
                bool.TryParse(cacheValue, out var result);
                return result;
            }

            var checkResult = new AlibabaPlatformService(shop).IsOpenTaoBigShopService();

            RedisHelper.Set(key, checkResult.ToString(), TimeSpan.FromHours(2));

            return checkResult;
        }

        /// <summary>
        /// 初始化淘大店铺同步状态
        /// </summary>
        /// <param name="shop"></param>
        public void InitTaoBigShopSyncStatus(Shop shop, Action<SyncStatus> action = null)
        {
            if (shop.PlatformType != PlatformType.Alibaba.ToString())
            {
                return;
            }

            if (!shop.IsTaoaoBigShop)
            {
                var isOpen = new AlibabaPlatformService(shop).IsOpenTaoBigShopService();

                if (!isOpen)
                {
                    return;
                }

            }

            var status = _syncStatusService.Get(shop.FxUserIds, shop.Id, ShopSyncType.TaoBigShopOrder);

            if (status != null)
            {
                return;
            }

            var date = DateTime.Now.AddDays(-3);

            var syncStatus = new SyncStatus()
            {
                FullSyncStatus = "Finished",
                FullSyncCompleteTime = date,
                LastSyncMessage = "",
                StartSyncTime = date,
                CreateTime = DateTime.Now,
                FxUserId = shop.FxUserIds,
                ShopId = shop.Id,
                SyncType = ShopSyncType.TaoBigShopOrder,
                LastSyncTime = date,
                Source = "FenDanSystem"
            };

            action?.Invoke(syncStatus);

            _syncStatusService.Add(syncStatus);

            shop.SyncStatusList.Add(syncStatus);
        }

        /// <summary>
        /// 设置：是否开启拼多多跨境单
        /// </summary>
        /// <param name="shopId"></param>
        /// <param name="isOpenCrossBorder"></param>
        public void UpdateIsOpenCrossBorder(int shopId, int isOpenCrossBorder, int fxUserId)
        {
            if (isOpenCrossBorder == 1)
            {
                //处理P_SyncStatus
                var _syncStatusService = new SyncStatusService();
                _syncStatusService.AddSyncStatus(new SyncStatus()
                {
                    FxUserId = fxUserId,
                    ShopId = shopId,
                    SyncType = ShopSyncType.PddCrossBorderOrder,
                    CreateTime = DateTime.Now,
                    FullSyncStatus = ShopSyncStatusType.Finished.ToString(),
                    LastSyncTime = null,
                    FullSyncCompleteTime = DateTime.Now.AddDays(-45)
                });
            }

            var _commonSettingService = new CommonSettingService();
            var key = "/ErpWeb/IsOpenCrossBorder";
            _commonSettingService.Set(key, isOpenCrossBorder.ToString(), shopId);
        }

        /// <summary>
        /// 获取是否开启跨境单配置
        /// </summary>
        /// <param name="shops"></param>
        public void GetIsOpenCrossBorder(IEnumerable<Shop> shops)
        {
            var _commonSettingService = new CommonSettingService();
            //获取配置信息
            var shopIds = shops?.Select(a => a.Id).ToList();
            var key = "/ErpWeb/IsOpenCrossBorder";
            var commonSettings = _commonSettingService.GetSettingByShopIds(key, shopIds);
            commonSettings?.ForEach(commonSet =>
            {
                var shop = shops.FirstOrDefault(a => a.Id == commonSet.ShopId);
                if (commonSet.Value == "1" && shop != null)
                    shop.IsOpenCrossBorder = true;
            });

        }

        public bool UpdateShopVersion(int shopId, string version)
        {
            return _repository.UpdateShopVersion(shopId, version);
        }


        /// <summary>
        /// 相关厂家加入TikTok白名单
        /// </summary>
        /// <param name="systemShopId">当前用户的系统店铺</param>
        /// <param name="supplierFxUserIds"></param>
        public void SetTikTokWhite(int systemShopId, List<int> supplierFxUserIds)
        {
            if (systemShopId <= 0 || supplierFxUserIds == null || supplierFxUserIds.Any() == false)
                return;
            var cs = new CommonSettingService();
            //默认版本
            var key = "/FxSystem/TikTok/Version";
            var version = cs.Get(key, 0)?.Value ?? "";
            //获取厂家的系统店铺以及分单系统用户ID
            var supplierSysShops = new ShopService().GetFxSystemShopByFxIdV1(supplierFxUserIds, "s.Id,fus.FxUserId AS FxUserIds");
            supplierSysShops?.ForEach(sysShop =>
            {
                if (string.IsNullOrEmpty(version) == false)
                {
                    ///将当前跨境白名单移入对应的版本
                    UpdateShopVersion(sysShop.Id, version);
                    Log.WriteLine($"SetTikTokVersion.fxUserId={sysShop.FxUserIds}，SystemShopId={sysShop.Id}版本设为{version}");
                    //强清理缓存
                    FxCaching.ForeRefeshCache(FxCachingType.SystemShops, sysShop.FxUserIds.ToString());
                    FxCaching.ForeRefeshCache(FxCachingType.FxUser, sysShop.FxUserIds.ToString());
                }
            });
            //用户维度相关厂家加入TikTok白名单
            new CommService().AddOrRemoveWhiteUser(supplierFxUserIds, true, false, UserFxRepository.CrossBorderUserFlag);
        }

        /// <summary>
        /// 设置TikTok用户版本+加入白名单
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="systemShopId">系统店铺Id</param>
        public void SetTikTokVersionAndJoinWhite(int fxUserId, int systemShopId)
        {
            //添加用户维度 跨境白名单
            new CommService().AddOrRemoveWhiteUser(new List<int> { fxUserId }, true, false, UserFxRepository.CrossBorderUserFlag);
            //默认版本
            var key = "/FxSystem/TikTok/Version";
            var version = new CommonSettingService().Get(key, 0)?.Value ?? "";
            if (string.IsNullOrEmpty(version) == false)
            {
                UpdateShopVersion(systemShopId, version);
                Log.WriteLine($"SetTikTokVersion.fxUserId={fxUserId}，SystemShopId={systemShopId}版本设为{version}");

                //强清理缓存
                FxCaching.ForeRefeshCache(FxCachingType.SystemShops, fxUserId.ToString());
                FxCaching.ForeRefeshCache(FxCachingType.FxUser, fxUserId.ToString());
            }
        }

        /// <summary>
        /// 针对1688代销相关用户，设置版本（已无效，注释掉了）
        /// </summary>
        public void SetAboutFx1688Version(int fxUserId, int shopId)
        {
            //2024-04-07取消指定版本
            return;

            var key = "/FxSystem/About1688/Version";
            var cs = new CommonSettingService();
            var version = cs.Get(key, 0)?.Value ?? "";
            if (string.IsNullOrEmpty(version) == false)
            {
                UpdateShopVersion(shopId, version);
                Log.WriteLine($"fxUserId={fxUserId}，SystemShopId={shopId}版本设为{version}");

                //强清理缓存
                FxCaching.ForeRefeshCache(FxCachingType.SystemShops, fxUserId.ToString());
                FxCaching.ForeRefeshCache(FxCachingType.FxUser, fxUserId.ToString());
            }
        }

        /// <summary>
        /// 设置分单铺货用户版本
        /// </summary>
        public void SetListingVersion(int fxUserId, int shopId)
        {
            var key = "/FxSystem/Listing/Version";
            var cs = new CommonSettingService();
            var version = cs.Get(key, 0)?.Value ?? "";
            if (string.IsNullOrEmpty(version) == false)
            {
                UpdateShopVersion(shopId, version);
                Log.WriteLine($"SetListingVersion.fxUserId={fxUserId}，SystemShopId={shopId}版本设为{version}");

                //强清理缓存
                FxCaching.ForeRefeshCache(FxCachingType.SystemShops, fxUserId.ToString());
                FxCaching.ForeRefeshCache(FxCachingType.FxUser, fxUserId.ToString());
            }
        }

        public bool UpdateShopVersion(List<int> shopIds, string version)
        {
            return _repository.UpdateShopVersion(shopIds, version);
        }
        /// <summary>
        /// 更新店铺指定字段信息
        /// </summary>
        /// <param name="shopId"></param>
        /// <param name="fieldVals"></param>
        public void UpdateShop(int id, Dictionary<string, string> fieldVals)
        {
            _repository.UpdateShop(id, fieldVals);
        }
        public void UpdateAccessTokenAndExpireTime(int shopId, string accessToken, DateTime time)
        {
            _repository.UpdateAccessTokenAndExpireTime(shopId, accessToken, time);
        }

        public bool UpdateShopOnlyCode(int shopId, string onlyCode)
        {
            return _repository.UpdateShopOnlyCode(shopId, onlyCode);
        }

        public Shop UpdateShopShareCode(int shopId)
        {
            var newShareCode = (Guid.NewGuid().ToString("N") + DateTime.Now.ToString("yyyyMMddHHmmssfff") + shopId).ToMd5().ToUpper();
            return _repository.UpdateShopShareCode(shopId, newShareCode);
        }

        public Shop GetTouTiaoShop(AlibabaMemberToken token)
        {
            DynamicParameters parameters = new DynamicParameters();
            var sql = " where (ShopId=@memberId OR VenderId=@VenderId OR (AccessToken=@AccessToken AND RefreshToken=@RefreshToken)) AND PlatformType='TouTiao' ORDER BY LastSyncTime DESC ";
            parameters.Add("memberId", token.MemberId);
            parameters.Add("AccessToken", token.Access_Token);
            parameters.Add("RefreshToken", token.Refresh_Token);
            parameters.Add("VenderId", token.VenderId);

            var shop = _repository.Get(sql, parameters).FirstOrDefault();
            return shop;
        }

        public Shop AddShop(AlibabaMemberToken token, PlatformType platform, bool isFxAppAuth = false, bool needFullSync = true)
        {
            DynamicParameters parameters = new DynamicParameters();
            var sql = " where ShopId=@memberId AND PlatformType=@ptype";
            parameters.Add("ptype", platform.ToString());
            parameters.Add("memberId", token.MemberId);

            //if (platform == PlatformType.TouTiao || platform == PlatformType.YunJi)
            //{
            //    // 头条系ShopId从订单详情获取，为获取到ShopId则通过 AccessToken 和 RefreshToken 判断唯一性
            //    sql = " where (ShopId=@memberId OR VenderId=@VenderId OR (AccessToken=@AccessToken AND RefreshToken=@RefreshToken)) AND PlatformType=@ptype ORDER BY LastSyncTime DESC ";
            //    parameters.Add("AccessToken", token.Access_Token);
            //    parameters.Add("RefreshToken", token.Refresh_Token);
            //    parameters.Add("VenderId", token.VenderId);
            //}
            Shop shop = null;
            if (platform == PlatformType.Taobao)
            {
                //淘宝先根据uid去查询是否存在店铺，没有再根据shopId
                sql = " where Uid=@userId AND PlatformType=@ptype";
                parameters.Add("ptype", platform.ToString());
                parameters.Add("userId", token.UserId);
                var allShops = _repository.Get(sql, parameters);
                if (allShops != null)
                {
                    //根据uid查询出多个店铺，授权进来旧店铺名称就用旧的，新的就用新的
                    //天猫转淘宝 MemberId还需要时间段过度才会拿到最新的
                    if (allShops.Count() == 1)
                    {
                        shop = allShops.FirstOrDefault();
                    }
                    else if (allShops.Count() > 1)
                    {
                        //兼容两个店铺
                        shop = allShops.FirstOrDefault(a => a.ShopId == token.MemberId);
                    }
                }
            }
            if (shop == null)
                shop = _repository.Get(sql, parameters).FirstOrDefault();
            //var expiredTime = DateTime.Now.AddYears(1);
            //if (!string.IsNullOrEmpty(token.Mail_Id))
            //    expiredTime = DateConverter.ConvertToUTC(token.Mail_Id)??expiredTime;

            var isNewShop = false;
            if (shop == null)
            {
                string version = null; //默认正式版
                version = (new CommonSettingService()).Get<string>($"/System/Config/DefaultVersion/{platform.ToString()}", 0);
                int verionNumber = version.ToInt();
                if (verionNumber <= 0 || verionNumber > 10)
                    version = null;

                isNewShop = true;
                shop = new Shop
                {
                    NickName = token.Resource_Owner,
                    ShopName = token.UserId ?? token.Resource_Owner,
                    ShopId = token.MemberId,
                    AccessToken = token.Access_Token,
                    RefreshToken = token.Refresh_Token,
                    PlatformType = platform.ToString(),
                    AuthTime = DateTime.Now,
                    OnlyCode = (CommUtls.GetOnlyCode()),
                    CreateTime = DateTime.Now,
                    //ExpireTime = expiredTime,
                    FullSyncStatus = needFullSync ? (isFxAppAuth ? "Waiting" : "Pending") : "",
                    Uid = token.UserId,
                    VenderId = token.VenderId,
                    LastRefreshTokenTime = DateTime.Now,
                    ExpireTime = DateTime.Now.AddDays(7),
                    Version = version
                };

                if (platform == PlatformType.TouTiao || platform == PlatformType.YunJi)
                {
                    shop.SubPlatformType = token.PlatformType;
                    ////头条新授权方式进入的，默认进入灰度环境
                    //if (platform == PlatformType.TouTiao && shop.AccessToken.Contains("-"))
                    //    shop.Version = CustomerConfig.TouTiaoVersion;
                    if (DateTime.Now < new DateTime(2020, 11, 1))
                        shop.ExpireTime = DateTime.Now.AddDays(30);
                }
                else if (platform == PlatformType.KuaiShou)
                {
                    shop.ShopName = token.Resource_Owner;
                }
                //拼多多店铺默认进入多多云
                if (shop.PlatformType == PlatformType.Pinduoduo.ToString())
                {
                    shop.Version = "";
                }
                else if (platform == PlatformType.BeiBei)
                {
                    shop.NickName = token.UserId;
                    shop.ShopName = token.Resource_Owner;
                }
                else if (platform == PlatformType.Other_Heliang || platform == PlatformType.Other_JuHaoMai || platform == PlatformType.Other_HaoYouDuo)
                {
                    shop.ExpireTime = null;
                }
                else if (platform == PlatformType.OwnShop)
                {
                    shop.ExpireTime = null;
                }
                shop.Id = _repository.Add(shop);
            }
            else
            {
                shop.PreAccessToken = shop.AccessToken;
                shop.PreRefreshToken = shop.PreRefreshToken;

                shop.ShopName = token.Resource_Owner.IsNullOrEmpty() ? shop.ShopName : token.Resource_Owner;
                if (isFxAppAuth == false)
                {
                    //分单系统不更新 店铺上的token
                    if (string.IsNullOrWhiteSpace(token.Refresh_Token) == false)
                        shop.RefreshToken = token.Refresh_Token;

                    if (string.IsNullOrWhiteSpace(token.Access_Token) == false)
                        shop.AccessToken = token.Access_Token;
                }
                shop.Uid = token.UserId;
                shop.VenderId = token.VenderId;
                shop.LastSyncMessage = "";//清空同步错误消息，以免提示授权过期
                shop.AuthTime = DateTime.Now;
                if (!isFxAppAuth)
                {
                    shop.LastRefreshTokenTime = DateTime.Now;
                }
                //shop.ExpireTime = expiredTime;
                //添加日志检测
                //var sameShop = _repository.GetByAccessToken(shop.Id, shop.AccessToken, shop.RefreshToken);
                //if (sameShop != null)
                //    _repository.AddSameAccessTokenLog("UpdateShopAuth", shop, sameShop);
                //else

                if (isFxAppAuth == false && shop.FullSyncStatus == "Waiting")
                    shop.FullSyncStatus = "Pending";

                if (string.IsNullOrWhiteSpace(shop.OnlyCode) == true)
                    shop.OnlyCode = CommUtls.GetOnlyCode();

                if (platform == PlatformType.KuaiShou)
                {
                    shop.ShopName = shop.ShopId == shop.ShopName && shop.NickName != shop.ShopName ? shop.NickName : shop.ShopName;
                    shop.NickName = shop.ShopName;
                }
                else if (platform == PlatformType.TouTiao || platform == PlatformType.YunJi)
                {
                    if (shop.ShopId != token.MemberId && !token.MemberId.IsNullOrEmpty())
                        shop.ShopId = token.MemberId;
                    shop.NickName = shop.ShopName;
                    var tokenPlatformType = token.PlatformType.ToString2();
                    var subPlatformType = shop.SubPlatformType.ToString2();
                    var subPts = subPlatformType.Split(",".ToArray(), StringSplitOptions.RemoveEmptyEntries).ToList();
                    if (!subPts.Contains(tokenPlatformType))
                        subPts.Add(tokenPlatformType);
                    if (subPts.Contains("false"))
                        subPts.Remove("false");
                    shop.SubPlatformType = string.Join(",", subPts.Distinct().ToList());
                    if (!token.Resource_Owner.IsNullOrEmpty() && shop.NickName != token.Resource_Owner)
                    {
                        Log.WriteLine($"头条修改店铺名称：{shop.NickName}==>{token.Resource_Owner}");
                        shop.NickName = token.Resource_Owner;
                        shop.ShopName = shop.NickName;
                        shop.Uid = shop.NickName;
                        shop.VenderId = shop.NickName;
                    }
                    ////头条新授权方式进入的，默认进入灰度环境
                    //if (platform == PlatformType.TouTiao && shop.AccessToken.Contains("-"))
                    //    shop.Version = CustomerConfig.TouTiaoVersion;
                }
                else if (platform == PlatformType.WxXiaoShangDian || platform == PlatformType.WxVideo)
                {
                    shop.NickName = shop.ShopName;
                    if (token.AppKey == CustomerConfig.WxXiaoShangDianV2AppId)
                    {
                        if (shop.VenderId != "V2")
                        {
                            shop.VenderId = "V2";
                            //防止旧店铺切换新应用时，订单数据缺失，同步时间往前挪
                            if (shop.LastSyncTime != null)
                            {
                                shop.LastSyncTime = shop.LastSyncTime?.AddHours(-1);
                            }
                        }
                    }
                }
                if (isFxAppAuth == false)
                    TryResetSyncStatus(shop);
                _repository.Update(shop);
                //if (shop.PlatformType == PlatformType.Pinduoduo.ToString())
                //    ServicesExtension.SystemApiQueueService.RefreshShopCache(shop.Id);

                if (isFxAppAuth == true)
                {
                    //分单系统没有更新token，所以店铺更新完后，再把token复制到店铺上，外面要用
                    if (string.IsNullOrWhiteSpace(token.Refresh_Token) == false)
                        shop.RefreshToken = token.Refresh_Token;

                    if (string.IsNullOrWhiteSpace(token.Access_Token) == false)
                        shop.AccessToken = token.Access_Token;
                }

            }
            TryToCreateDbConfig(shop);
            if (platform == PlatformType.Alibaba || platform == PlatformType.Taobao)
            {
                //插入一条迁移记录，防止回到旧版本
                var ms = _repository.DbConnection.Query<MigrateShop>($"SELECT * FROM dbo.P_MigrateShop {WithNoLockSql} WHERE MemberId=@mid AND PlatformType=@pt", new { mid = shop.ShopId, pt = shop.PlatformType })?.FirstOrDefault();
                if (ms == null)
                {
                    _repository.DbConnection.Insert(new MigrateShop
                    {
                        MemberId = shop.ShopId,
                        PlatformType = shop.PlatformType,
                        CreateTime = DateTime.Now,
                        MigrateStatus = "Finished",
                        LastMigrateMessage = "新用户进入，默认添加"
                    });
                }
            }

            //若是淘宝店铺添加到推送库
            if (platform == PlatformType.Taobao)
            {
                try
                {
                    var rdsName = TryToCreateTaobaoPushDbConfig(shop.Id, isNewShop);
                    var pt = new TaobaoPlatformService(shop);
                    pt.PushToJushita(rdsName);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"将淘宝店铺【{shop?.ShopId}】添加到聚石塔时发生错误：{ex}");
                }
            }
            return shop;
        }

        public void TryToCreateDbConfig(Shop shop)
        {
            for (int i = 0; i < 3; i++)
            {
                try
                {
                    var cs = new CommonSettingService();
                    var dbNameConfigId = cs.Get($"/SystemSetting/DefaultDbNameConfigId/{shop.PlatformType}", 0)?.Value?.ToInt() ?? 0;
                    if (dbNameConfigId > 0)
                        _repository.TryToCreateDbConfig(shop.Id, dbNameConfigId);
                    return;
                }
                catch (Exception ex)
                {
                    Log.WriteError($"TryToCreateDbConfig创建DbConfig时发生错误，店铺ID:{shop?.Id}，错误详情：{ex}");
                    Thread.Sleep(500);
                }
            }
        }

        /// <summary>
        /// 针对各个平台创建不同的数据库配置信息
        /// </summary>
        /// <param name="shop"></param>
        /// <param name="tag"></param>
        public void TryToCreateCloudDbConfig(Shop shop, string tag = null)
        {
            //var cloudPts = new List<string> { CloudPlatformType.Alibaba.ToString(), CloudPlatformType.Pinduoduo.ToString(), CloudPlatformType.Jingdong.ToString() };
            var cloudPts = new List<string> { CloudPlatformType.Alibaba.ToString(), CloudPlatformType.Pinduoduo.ToString(), CloudPlatformType.Jingdong.ToString(), CloudPlatformType.TouTiao.ToString() };

            //跨境平台 业务库初始化，根据全局开关和白名单单来控制
            if (_commonSettingService.IsShowCrossBorder(shop.FxUserIds) || tag == "register")
            {
                //增加TikTok的云平台
                cloudPts.Add(CloudPlatformType.ChinaAliyun.ToString());
            }

            foreach (var cpt in cloudPts)
            {
                //分单新注册用户，添加抖店云新版配置
                if ((cpt == CloudPlatformType.TouTiao.ToString() || cpt == CloudPlatformType.Pinduoduo.ToString() ||
                     cpt == CloudPlatformType.Alibaba.ToString() || cpt == CloudPlatformType.ChinaAliyun.ToString())
                    && shop.PlatformType == PlatformType.System.ToString() && shop.FxUserIds > 0)
                {
                    //拼多多，暂时只支持注册，等全部迁移完成，再打开登录
                    if (cpt == CloudPlatformType.Alibaba.ToString() && tag != null &&
                        tag.Equals("login", StringComparison.OrdinalIgnoreCase))
                    {
                        continue;
                    }

                    var fxDbConfig = new FxDbConfig
                    {
                        FxUserId = shop.FxUserIds,
                        SystemShopId = shop.Id,
                        DbCloudPlatform = cpt,
                        FromFxDbConfig = 1,
                        Status = tag.IsNotNullOrEmpty() ? tag : "register"
                    };

                    new FxDbConfigService().TryToCreateCloudFxDbConfig(new List<FxDbConfig> { fxDbConfig },
                        new List<string> { cpt });
                    continue;
                }

                var key = $"/SystemSetting/DefaultDbNameConfigId/{cpt}Cloud/{shop.PlatformType}";

                for (int i = 0; i < 3; i++)
                {
                    try
                    {
                        var dbNameConfigId = 0;
                        var cs = new CommonSettingService();
                        dbNameConfigId = cs.Get(key, 0)?.Value?.ToInt() ?? 0;
                        if (dbNameConfigId > 0)
                            _repository.TryToCreateCloudDbConfig(shop.Id, dbNameConfigId, cpt, shop.FxUserIds);
                        break;
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"第{i + 1}次尝试：TryToCreateCloudDbConfig创建DbConfig时发生错误，店铺ID:{shop?.Id}，错误详情：{ex}");
                        if (ex.Message.Contains("PRIMARY KEY"))
                            break;
                        Thread.Sleep(500);
                    }
                }
            }
        }


        /// <summary>
        /// 预分配指定云平台业务库
        /// </summary>
        /// <param name="shop"></param>
        /// <param name="cpt"></param>
        /// <param name="dbNameConfigId"></param>
        /// <param name="fromFxDbConfig"></param>
        public void TryToAddCloudDbConfig(Shop shop, string cpt, int dbNameConfigId)
        {
            for (int i = 0; i < 3; i++)
            {
                try
                {
                    if (dbNameConfigId > 0)
                        _repository.TryToCreateCloudDbConfig(shop.Id, dbNameConfigId, cpt, shop.FxUserIds);
                    break;
                }
                catch (Exception ex)
                {
                    Log.WriteError($"第{i + 1}次尝试：TryToAddCloudDbConfig创建DbConfig时发生错误，云平台:{cpt}，店铺ID:{shop?.Id}，错误详情：{ex}");
                    if (ex.Message.Contains("PRIMARY KEY"))
                        break;
                    Thread.Sleep(500);
                }
            }
        }


        /// <summary>
        /// 用户很久没有使用需尝试重置店铺同步状态
        /// </summary>
        /// <param name="shop"></param>
        public void TryResetSyncStatus(Shop shop)
        {
            var dontNeedSyncOrderPlatforms = new List<string> { PlatformType.Offline.ToString() };
            if (dontNeedSyncOrderPlatforms.Contains(shop.PlatformType))
                return;
            if (shop.StartSyncTime == null || shop.LastSyncTime == null || shop.StartFullSyncTime == null)
                return;
            //30天未使用系统
            var deadlineTime = DateTime.Now.AddDays(-30);
            if (shop.StartSyncTime < deadlineTime || shop.LastSyncTime < deadlineTime)
            {
                shop.StartSyncTime = null;
                shop.LastSyncTime = null;
                shop.FullSyncCompleteTime = null;
                shop.FullSyncErrorTimes = -1;//标记下是重置过来的
                shop.FullSyncStatus = "Pending";
                shop.StartFullSyncTime = null;
                //DefaultQueryDateVal重置查询时间的记住天数
                var settingService = new CommonSettingService();
                settingService.DeleteByKey("DefaultQueryDateVal", shop.Id);
            }
        }

        public List<DbConfig> GetDbConfigList(List<int> list, string platformType)
        {

            return _repository.GetDbConfigList(list, platformType);
        }

        public List<DataMigrateTask> GetDataMigrateTaskList(int shopId, string platformType)
        {

            return _repository.GetDataMigrateTaskList(shopId, platformType);
        }


        public string SaveOneDataMigrateTask(int shopId, int sourceDbNameConfigId, int targetDbNameConfigId, string platformType)
        {

            return _repository.SaveOneDataMigrateTask(shopId, sourceDbNameConfigId, targetDbNameConfigId, platformType);
        }


        public string TryToCreateTaobaoPushDbConfig(int shopId, bool isNewShop)
        {
            var cs = new CommonSettingService();
            var s = cs.CreateTaobaoPushDbSetting(shopId, isNewShop);
            return s?.RdsName;
        }

        /// <summary>
        /// 初始化头条店铺推送库
        /// </summary>
        /// <param name="shop"></param>
        public string TryCreateTouTiaoPushDb(Shop shop)
        {
            var pushDbId = string.Empty;
            try
            {
                //前提：推送库是以应用为准的，所以配置都是按应用来配置的
                //1.先判断应用是否开启了推送库
                //2.应用开启推送库：通过接口查询店铺的推送库信息，如果接口响应店铺未绑定推送库，则以应用的默认推送库初始化店铺的推送库配置。
                //3.如果接口响应店铺有绑定推送库：
                //3.1.判断店铺的推送库是否过期，如果过期，则重新绑定大推送库。
                //3.2.对比接口是否和数据库配置的推送库是否一样，如果接口的推送库和配置的不一样，则以接口的为准，以接口推送库来覆盖用户的推送库配置。

                var commonSettingService = new CommonSettingService();
                //查询店铺授权的应用是否开启了推送库
                var appIsEablePushDb = commonSettingService.GetBool($"/TouTiao/IsEnabeldPushDbInit/{shop.AppKey}", 0);
                if (appIsEablePushDb == false)
                    return null;  //应用没有开启推送库初始化

                //通过接口查询用户推送库信息
                var ptService = PlatformFactory.GetPlatformService(shop) as ZhiDianNewPlatformService;
                TouTiaoPushDbInfo pushDbInfoFromApi = null;
                try
                {
                    pushDbInfoFromApi = ptService.QueryPushDbInfo();
                }
                catch (Exception iex)
                {
                    var msg = $"警告：头条店铺【{shop.NickName} {shop.Id}】，获取推送库信息时发生错误：{iex.Message} ";
                    Log.WriteError(msg + iex);
                    Utility.Net.HttpUtility.PostToDingDing(msg, new List<string> { "13065187972", "18026946819" });
                }

                //店铺未绑定推送库，则绑定推送库
                if (pushDbInfoFromApi == null)
                {
                    //获取应用的默认推送库
                    var appDefaultPushDbId = commonSettingService.GetString($"/TouTiao/PushDb/Default/{shop.AppKey}", 0);
                    if (string.IsNullOrWhiteSpace(appDefaultPushDbId))
                        Utility.Net.HttpUtility.PostToDingDing($"头条应用【{shop.AppKey}】默认推送库未设置，店铺【{shop.Id}】初始化推送库配置失败！", new List<string> { "13065187972", "18026946819" });
                    else
                    {
                        //店铺绑定推送库
                        var isEnabled = ptService.EnablePushDb(appDefaultPushDbId);
                        if (isEnabled)
                        {
                            //绑定成功，初始化用户的推送库配置
                            commonSettingService.Set($"/TouTiao/PushDb/{shop.AppKey}", appDefaultPushDbId, shop.Id);
                            //记录用户开通推送库的时间
                            commonSettingService.Set($"/TouTiao/PushDb/OpenTime/{shop.AppKey}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), shop.Id);
                        }
                    }
                    pushDbId = appDefaultPushDbId;
                }
                else
                {
                    //判断推送库是否失效
                    if (pushDbInfoFromApi.Status != 1) //推送库不等于运行中，则重新绑定推送库
                    {
                        ptService.EnablePushDb(pushDbInfoFromApi.RdsId);
                        //更新用户开通推送库的时间
                        commonSettingService.Set($"/TouTiao/PushDb/OpenTime/{shop.AppKey}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), shop.Id);
                    }

                    //获取店铺的推送库配置
                    var shopPushDbId = commonSettingService.GetString($"/TouTiao/PushDb/{shop.AppKey}", shop.Id);
                    //店铺未配置推送库，或者店铺配置的推送库和接口不一样，则以接口的推送库覆盖店铺的推送库配置
                    if (string.IsNullOrWhiteSpace(shopPushDbId) || shopPushDbId != pushDbInfoFromApi.RdsId)
                    {
                        //以接口的推送库来覆盖配置
                        commonSettingService.Set($"/TouTiao/PushDb/{shop.AppKey}", pushDbInfoFromApi.RdsId, shop.Id);
                    }
                    pushDbId = pushDbInfoFromApi.RdsId;
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"头条店铺【{shop?.ShopId}】初始化推送库报错：{ex}");
            }
            return pushDbId;
        }


        /// <summary>
        /// 初始化头条店铺推送库-临时方法，仅供单元测试使用
        /// </summary>
        /// <param name="shop"></param>
        public string TryCreateTouTiaoPushDbTemp(CommonSettingService commonSettingService, ZhiDianNewPlatformService ptService, Shop shop, string appKey, string shopPushDbKey, string appDefaultPushDbId)
        {
            var pushDbId = string.Empty;
            try
            {
                //前提：推送库是以应用为准的，所以配置都是按应用来配置的
                //1.先判断应用是否开启了推送库
                //2.应用开启推送库：通过接口查询店铺的推送库信息，如果接口响应店铺未绑定推送库，则以应用的默认推送库初始化店铺的推送库配置。
                //3.如果接口响应店铺有绑定推送库：
                //3.1.判断店铺的推送库是否过期，如果过期，则重新绑定大推送库。
                //3.2.对比接口是否和数据库配置的推送库是否一样，如果接口的推送库和配置的不一样，则以接口的为准，以接口推送库来覆盖用户的推送库配置。

                //通过接口查询用户推送库信息
                TouTiaoPushDbInfo pushDbInfoFromApi = null;
                try
                {
                    pushDbInfoFromApi = ptService.QueryPushDbInfo(shop.ShopId);
                }
                catch (Exception iex)
                {
                    var msg = $"警告：头条店铺【{shop.NickName} {shop.Id}】，获取推送库信息时发生错误：{iex.Message} ";
                    Log.WriteError(msg + iex);
                    Utility.Net.HttpUtility.PostToDingDing(msg, new List<string> { "13065187972", "18026946819" });
                }

                //店铺未绑定推送库，则绑定推送库
                if (pushDbInfoFromApi == null)
                {
                    //获取应用的默认推送库
                    //店铺绑定推送库
                    var isEnabled = ptService.EnablePushDb(appDefaultPushDbId, 0, shop.ShopId);
                    if (isEnabled)
                    {
                        //绑定成功，初始化用户的推送库配置
                        commonSettingService.SetAndSyncToTouTiao(shopPushDbKey, appDefaultPushDbId, shop.Id);
                        //记录用户开通推送库的时间
                        commonSettingService.SetAndSyncToTouTiao($"/TouTiao/PushDb/OpenTime/{appKey}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), shop.Id);
                        Log.WriteLine($"头条店铺【{shop?.ShopId}】初始化推送成功");
                    }
                    else
                        Log.WriteLine($"头条店铺【{shop?.ShopId}】初始化推送失败");
                    pushDbId = appDefaultPushDbId;
                }
                else
                {
                    //判断推送库是否失效
                    if (pushDbInfoFromApi.Status != 1) //推送库不等于运行中，则重新绑定推送库
                    {
                        ptService.EnablePushDb(pushDbInfoFromApi.RdsId);
                        //更新用户开通推送库的时间
                        commonSettingService.SetAndSyncToTouTiao($"/TouTiao/PushDb/OpenTime/{appKey}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), shop.Id);
                    }

                    //获取店铺的推送库配置
                    var shopPushDbId = commonSettingService.GetString(shopPushDbKey, shop.Id);
                    //店铺未配置推送库，或者店铺配置的推送库和接口不一样，则以接口的推送库覆盖店铺的推送库配置
                    if (string.IsNullOrWhiteSpace(shopPushDbId) || shopPushDbId != pushDbInfoFromApi.RdsId)
                    {
                        //以接口的推送库来覆盖配置
                        commonSettingService.SetAndSyncToTouTiao(shopPushDbKey, pushDbInfoFromApi.RdsId, shop.Id);
                    }
                    pushDbId = pushDbInfoFromApi.RdsId;
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"头条店铺【{shop?.ShopId}】初始化推送库报错：{ex}");
            }
            return pushDbId;
        }


        public Shop GetShop(int userId, string shopId, string platform)
        {
            var shop = _repository.Query("SELECT s.* FROM dbo.P_Shop s WITH(NOLOCK) INNER JOIN dbo.P_UserShopRelation sr WITH(NOLOCK) ON s.Id = sr.ShopId WHERE sr.UserId=@userId AND s.ShopId=@shopId AND s.PlatformType=@ptype", new { userId, shopId, ptype = platform }).FirstOrDefault();
            return shop;
        }

        public Shop AddShop(AlibabaMemberToken token, string platform, int penPlatformAppId = 0)
        {
            Shop shop = null;
            if (penPlatformAppId > 0)
                shop = _repository.Get(" WITH(NOLOCK) where ShopId=@memberId AND PlatformType=@ptype AND PlatformType='OpenV1' AND OpenPlatformAppId=@penPlatformAppId", new { memberId = token.MemberId, ptype = platform.ToString(), penPlatformAppId }).FirstOrDefault();
            else
                shop = _repository.Get(" WITH(NOLOCK) where ShopId=@memberId AND PlatformType=@ptype", new { memberId = token.MemberId, ptype = platform.ToString() }).FirstOrDefault();
            //var expiredTime = DateTime.Now.AddYears(1);
            //if (!string.IsNullOrEmpty(token.Mail_Id))
            //    expiredTime = DateConverter.ConvertToUTC(token.Mail_Id)??expiredTime;
            if (shop == null)
            {
                shop = new Shop
                {
                    NickName = token.Resource_Owner,
                    ShopName = token.UserId ?? token.Resource_Owner,
                    ShopId = token.MemberId,
                    AccessToken = token.Access_Token,
                    RefreshToken = token.Refresh_Token,
                    PlatformType = platform.ToString(),
                    AuthTime = DateTime.Now,
                    OnlyCode = (CommUtls.GetOnlyCode()),
                    CreateTime = DateTime.Now,
                    //ExpireTime = expiredTime,
                    FullSyncStatus = "Pending",
                    Uid = token.UserId,
                    VenderId = token.VenderId,
                    OpenPlatformAppId = penPlatformAppId,
                    LastRefreshTokenTime = DateTime.Now
                };
                shop.Id = _repository.Add(shop);
            }
            else
            {
                shop.NickName = token.Resource_Owner;
                shop.ShopName = token.Resource_Owner;
                if (string.IsNullOrWhiteSpace(token.Refresh_Token) == false)
                    shop.RefreshToken = token.Refresh_Token;
                shop.AccessToken = token.Access_Token;
                shop.Uid = token.UserId;
                shop.VenderId = token.VenderId;
                shop.LastSyncMessage = "";//清空同步错误消息，以免提示授权过期
                shop.AuthTime = DateTime.Now;
                shop.LastRefreshTokenTime = DateTime.Now;
                //shop.ExpireTime = expiredTime;
                //添加日志检测
                //var sameShop = _repository.GetByAccessToken(shop.Id, shop.AccessToken, shop.RefreshToken);
                //if (sameShop != null)
                //    _repository.AddSameAccessTokenLog("UpdateShopAuth", shop, sameShop);
                //else

                if (string.IsNullOrWhiteSpace(shop.OnlyCode) == true)
                    shop.OnlyCode = CommUtls.GetOnlyCode();

                _repository.Update(shop);
            }
            TryToCreateDbConfig(shop);
            return shop;
        }

        public Shop AddOrUpdateShopByUid(AlibabaMemberToken token, PlatformType platform)
        {
            var shop = _repository.Get(" WITH(NOLOCK) where Uid=@userId AND PlatformType=@ptype", new { userId = token.UserId, ptype = platform.ToString() }).FirstOrDefault();
            //var expiredTime = DateTime.Now.AddYears(1);
            //if (!string.IsNullOrEmpty(token.Mail_Id))
            //    expiredTime = DateConverter.ConvertToUTC(token.Mail_Id)??expiredTime;
            if (shop == null)
            {
                shop = new Shop
                {
                    NickName = token.Resource_Owner,
                    ShopName = token.UserId ?? token.Resource_Owner,
                    ShopId = token.MemberId,
                    AccessToken = token.Access_Token,
                    RefreshToken = token.Refresh_Token,
                    PlatformType = platform.ToString(),
                    AuthTime = DateTime.Now,
                    OnlyCode = (CommUtls.GetOnlyCode()),
                    CreateTime = DateTime.Now,
                    Uid = token.UserId,
                    //ExpireTime = expiredTime,
                    FullSyncStatus = "Pending",
                };
                shop.Id = _repository.Add(shop);
            }
            else
            {
                shop.ShopName = token.Resource_Owner;
                shop.RefreshToken = token.Refresh_Token;
                shop.AccessToken = token.Access_Token;
                shop.LastSyncMessage = "";//清空同步错误消息，以免提示授权过期
                shop.AuthTime = DateTime.Now;
                //shop.ExpireTime = expiredTime;
                //添加日志检测
                //var sameShop = _repository.GetByAccessToken(shop.Id, shop.AccessToken, shop.RefreshToken);
                //if (sameShop != null)
                //    _repository.AddSameAccessTokenLog("UpdateShopAuth", shop, sameShop);
                //else

                if (string.IsNullOrWhiteSpace(shop.OnlyCode) == true)
                    shop.OnlyCode = CommUtls.GetOnlyCode();

                _repository.Update(shop);
            }
            return shop;
        }

        public Shop UpdateShop(Shop shop)
        {
            _repository.Update(shop);
            return shop;
        }

        public int TryAddMigrateShop(string memberId, string platformType, bool isSetHopeMigrateTime = false, bool isAll = false, bool isInvite = false)
        {
            //默认升级区间：00:00至05:00
            DateTime? time = null;
            if (isSetHopeMigrateTime)
            {
                var addDays = 1;
                if (DateTime.Now.Hour <= 4)
                    addDays = 0;
                time = DateTime.Now.AddDays(addDays).ToString("yyyy-MM-dd").toDateTime().AddHours(2);
            }
            var db = _repository.GetConfigureDb();
            var old = db.GetList<MigrateShop>(" WITH(NOLOCK) where MemberId=@mid AND PlatformType=@pt", new { mid = memberId, pt = platformType })?.FirstOrDefault();
            if (old != null)
            {
                //old.MigrateStatus = null;
                //old.LastMigrateTime = null;
                //old.LastMigrateMessage = null;
                //db.Update(old);
                return old.Id;
            }
            //if (isInvite)
            //{
            //    db.Add(new InvitedMigrateShop
            //    {
            //        MemberId = memberId,
            //        PlatformType = platformType,
            //        CreateTime = DateTime.Now,
            //        Status = "InvitedBySystem",
            //    });
            //}
            var id = db.Insert(new MigrateShop
            {
                MemberId = memberId,
                PlatformType = platformType,
                CreateTime = DateTime.Now,
                HopeMigrateTime = time,
                IsNeedAllData = isAll
            });
            return id ?? 0;
        }

        public MigrateShop GetMigrateShop(string shopId, string platformType)
        {
            var db = _repository.GetConfigureDb();
            var old = db.GetList<MigrateShop>(" WITH(NOLOCK) where MemberId=@mid AND PlatformType=@pt", new { mid = shopId, pt = platformType })?.FirstOrDefault();
            return old;
        }

        public int TryAddInviteMigrateShop(string memberId, string platformType)
        {
            var db = _repository.GetConfigureDb();
            var old = db.GetList<InvitedMigrateShop>(" WITH(NOLOCK) where MemberId=@mid", new { mid = memberId })?.FirstOrDefault();
            if (old != null)
            {
                return 0;
            }
            var id = db.Insert(new InvitedMigrateShop
            {
                MemberId = memberId,
                PlatformType = platformType,
                CreateTime = DateTime.Now,
                Status = "Waiting"
            });
            return id ?? 0;
        }

        public string TryAddMigrateShop(List<string> wangwangs, string platformType, bool isSetHopeMigrateTime = false)
        {
            var db = _repository.GetConfigureDb();
            var memberids = ParseWangwangToMemberId(wangwangs, platformType);
            if (memberids == null || !memberids.Any())
                return "没有查询到对应的店铺";
            var mids = new List<string>();
            memberids.ForEach(m =>
            {
                if (!string.IsNullOrEmpty(m))
                {
                    var old = db.GetList<MigrateShop>(" where MemberId=@mid", new { mid = m })?.FirstOrDefault();
                    if (old != null)
                        mids.Add($"店铺【{m}】已存在于迁移任务中，迁移状态：【{old.MigrateStatus}】，若要重新迁移，请调用/Auth/ResetMany 重新迁移");
                    else
                    {
                        var id = TryAddMigrateShop(m, platformType, isSetHopeMigrateTime);
                        mids.Add(id.ToString());
                    }
                }
            });
            return mids.ToJson();
        }

        public string TryAddMigrateShopOneByOne(string wangwang, string platformType, bool isSetHopeMigrateTime = false, bool isAll = false, bool isInvite = false)
        {
            var db = _repository.GetConfigureDb();
            var memberids = ParseWangwangToMemberId(new List<string> { wangwang }, platformType);
            if (memberids == null || !memberids.Any())
                return $"店铺【{wangwang}】：无法查询到，请确认账号是否有误";
            var mids = new List<string>();
            memberids.ForEach(m =>
            {
                if (!string.IsNullOrEmpty(m))
                {
                    var old = db.GetList<MigrateShop>(" where MemberId=@mid AND PlatformType=@pt", new { mid = m, pt = platformType })?.FirstOrDefault();
                    if (old != null)
                        mids.Add($"店铺【{wangwang} ({m})】：已存在于迁移任务中，迁移状态：【{old.MigrateStatus ?? "等待中"}】，迁移时间：{old.LastMigrateTime}");
                    else
                    {
                        var oldShop = db.GetList<Shop>(" WITH(NOLOCK) where ShopId=@mid AND PlatformType=@pt", new { mid = m, pt = platformType })?.FirstOrDefault();
                        if (oldShop != null)
                            mids.Add($"店铺【{wangwang} ({m})】：已存在新系统中，不需要升级。");
                        else
                        {
                            var id = TryAddMigrateShop(m, platformType, isSetHopeMigrateTime, isAll, isInvite);
                            mids.Add($"店铺【{wangwang} ({m})】：添加成功");
                        }
                    }
                }
            });
            return mids.ToJson();
        }

        public string TryAddMongoShopOneByOne(string wangwang, string platformType)
        {
            var db = _repository.GetConfigureDb();
            var shops = db.Query<Shop>("select * from P_Shop WITH(NOLOCK) where ShopName IN @names AND PlatformType=@pt", new { names = wangwang.Split(','), pt = platformType }).ToList();
            //var memberids = ParseWangwangToMemberId(new List<string> { wangwang }, platformType);
            if (shops == null || !shops.Any())
                return $"店铺【{wangwang}】：无法查询到，请确认账号是否有误";
            var mids = new List<string>();
            shops.ForEach(m =>
            {
                var old = db.GetList<DatabaseConfig>(" WITH(NOLOCK) where ShopId=@sid", new { sid = m.Id })?.FirstOrDefault();
                if (old != null)
                    mids.Add($"店铺【{wangwang} ({m.Id})】：已存在于迁移任务中，数据迁移状态：【{old.Status ?? "等待中"}】，信息：{old.Message}");
                else
                {
                    db.Insert<DatabaseConfig>(new DatabaseConfig
                    {
                        ShopId = m.Id,
                        CreateTime = DateTime.Now,
                        Status = "Pending",
                        DatabaseType = DatabaseTypeEnum.MongoDB,
                        Message = "等待处理"
                    });
                    mids.Add($"店铺【{wangwang} ({m})】：添加成功");
                }
            });
            return mids.ToJson();
        }


        public string TryReturnShopOneByOne(string wangwang, string platformType)
        {
            var db = _repository.GetConfigureDb();
            var memberids = ParseWangwangToMemberId(new List<string> { wangwang }, platformType);
            if (memberids == null || !memberids.Any())
                return $"店铺【{wangwang}】：无法查询到，请确认账号是否有误";
            var mids = new List<string>();
            memberids.ForEach(m =>
            {
                if (!string.IsNullOrEmpty(m))
                {
                    var old = db.GetList<MigrateShop>(" WITH(NOLOCK) where MemberId=@mid AND PlatformType=@pt", new { mid = m, pt = platformType })?.FirstOrDefault();
                    if (old != null)
                    {
                        old.MigrateStatus = "Close";
                        db.Update(old);
                        mids.Add($"店铺【{wangwang} ({m})】：返回成功");
                    }
                    var oldShop = db.GetList<Shop>(" WITH(NOLOCK) where (ShopId=@mid OR ShopName=@mid) AND PlatformType=@pt", new { mid = m, pt = platformType })?.FirstOrDefault();
                    if (oldShop != null)
                    {
                        var oldDb = _repository.GetOldDb(oldShop.PlatformType);
                        try
                        {
                            var appKey = "";
                            if (oldShop.PlatformType == "Pinduoduo")
                                appKey = "afabb0e68b3443b9a0dda11c1442a042";
                            else if (oldShop.PlatformType == "Alibaba")
                                appKey = CustomerConfig.AlibabaAppKey;
                            else if (oldShop.PlatformType == "Taobao")
                                appKey = CustomerConfig.TaobaAppKey2;
                            oldDb.ExecuteScalar($"INSERT INTO dbo.P_MemberToken(Resource_Owner,MemberId,Access_Token,Refresh_Token,GetDateTime,AddDate,AppKey)VALUES(   @ShopName, @ShopId, @AccessToken,@RefreshToken,GETDATE(),GETDATE(),'{appKey}')", oldShop);
                            mids.Add($"店铺【{wangwang} ({m})】：返回成功");
                        }
                        catch (Exception ex)
                        {
                            mids.Add($"店铺【{wangwang} ({m})】：返回失败：{ex.Message}");
                        }
                    }
                    else
                        mids.Add($"店铺【{wangwang} ({m})】：不存在于新系统，无法返回");
                }
            });
            return mids.ToJson();
        }


        public List<string> ParseWangwangToMemberId(List<string> wangwangs, string platformType)
        {
            var oldDb = _repository.GetOldDb(platformType);
            if (oldDb == null)
                throw new LogicException($"无法获取{platformType}老版数据库连接，请检查web.config");
            var memberids = oldDb.Query<string>("SELECT MemberId FROM P_MemberToken WITH(NOLOCK) WHERE Resource_Owner IN @wangwangs", new { wangwangs })?.ToList();
            if (memberids == null || !memberids.Any())
                memberids = _repository.GetConfigureDb().Query<string>("SELECT ShopId FROM P_Shop WITH(NOLOCK) WHERE ShopName IN @wangwangs", new { wangwangs })?.ToList();
            return memberids?.Select(s => s.TrimEnd("_$OverDateDelted$"))?.ToList() ?? new List<string>();
        }

        public void ResetShopData(Shop shop)
        {
            //var db = _repository.GetDb(shop.PlatformType);
            var db = DbUtility.GetConnection(CustomerConfigExt.GetConnectString(shop));
            var configureDb = _repository.GetConfigureDb();
            try { db.DeleteList<AreaCodeInfo>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<CommonSetting>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<CustomColumnExcel>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<CustomerColumnMapping>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<CustomerOrder>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<CustomerOrderItem>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<ExpressCodeMapping>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<ExpressCompany>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<ExpressSend>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<LastCodeBuild>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<OpenSecrecySeller>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<Order>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<OrderCategory>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<OrderFilter>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<OrderItem>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<OrderModified>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<Preordain>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<PrintControl>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<PrinterBind>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<PrintHistory>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<PrintHistoryOrder>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<PrintOrderProduct>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<PrintTemplate>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<Product>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<ProductSku>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<ProductSkuAttribute>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<ProductStatus>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<PurchaseHistory>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<ReciverInfo>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<SellerInfo>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<SendGoodTemplate>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<SendHistory>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<SendHistoryOrder>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<SendOrderProduct>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<SkuAttribute>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<Supplier>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<SynUserInfo>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<SysConfig>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<TemplateLogisticsServices>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<TemplateRelationAuthInfo>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<User>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<WaybillCode>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<WaybillCodeCheck>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<WaybillCodeChild>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { db.DeleteList<WaybillCodeOrder>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { configureDb.DeleteList<WaybillCustomArea>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { configureDb.DeleteList<ShopRelation>(" where RelatedShopId=@sid OR ShopId=@sid", new { sid = shop.Id }); } catch { }
            try { configureDb.DeleteList<StapleTemplate>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { configureDb.DeleteList<UserSiteInfo>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { configureDb.DeleteList<Agent>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { configureDb.DeleteList<CaiNiaoAuthInfo>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { configureDb.DeleteList<CainiaoAuthOwner>(" where ShopId=@sid ", new { sid = shop.Id }); } catch { }
            try { configureDb.DeleteList<MigrateShop>(" where MemberId=@sid AND PlatformType=@pt", new { sid = shop.ShopId, pt = shop.PlatformType }); } catch { }
            try { configureDb.DeleteList<Shop>(" where Id=@sid ", new { sid = shop.Id }); } catch { }
        }

        public Shop Get(string shopId, PlatformType platformType)
        {
            return _repository.Query("select * from P_Shop WITH(NOLOCK) where ShopId=@shopId AND PlatformType=@platformType", new { shopId, platformType = platformType.ToString() })?.FirstOrDefault();
        }

        public Shop GetByShopId(string shopId, string platformType)
        {
            return _repository.Query("select * from P_Shop WITH(NOLOCK) where ShopId=@shopId AND PlatformType=@platformType", new { shopId, platformType })?.FirstOrDefault();
        }

        /// <summary>
        /// 根据平台ShopId获取店铺
        /// </summary>
        /// <param name="ptShopIds"></param>
        /// <param name="platformType"></param>
        /// <returns></returns>
        public List<Shop> GetByShopId(List<string> ptShopIds, PlatformType platformType)
        {
            return _repository.Query("select * from P_Shop WITH(NOLOCK) where ShopId IN @ptShopIds AND PlatformType=@platformType", new { ptShopIds, platformType = platformType.ToString() }).ToList();
        }

        /// <summary>
        /// 获取未全量同步的店铺
        /// </summary>
        /// <param name="platformType">所要包含或排除的平台类型，多个逗号隔开，排除的平台前面加!</param>
        /// <param name="currentCloudPlatformType">当前的云平台类型：Alibaba、Pinduoduo、Jingdong，仅能同步一个云平台店铺的数据</param>
        /// <returns></returns>
        public List<Shop> GetUnFullSyncedOld(string platformType, string currentCloudPlatformType)
        {
            var pftCondition = string.Empty;
            if (string.IsNullOrWhiteSpace(platformType) == false)
            {
                if (platformType?.ToLower() == "all" || platformType?.ToLower() == "*")
                {
                }
                else if (platformType.StartsWith("!"))
                {
                    var pts = string.Join(",", platformType.Split(',').Select(s => $"'{s.Trim('!')}'"));
                    pftCondition = $" s.PlatformType not in({pts}) AND ";
                }
                else if (platformType.Contains(","))
                {
                    var platforms = platformType.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                    pftCondition = $" s.PlatformType in ('{string.Join("','", platforms)}') AND ";
                }
                else
                {
                    pftCondition = $" s.PlatformType='{platformType}' AND ";
                }
            }
            if (!string.IsNullOrEmpty(currentCloudPlatformType))
            {
                pftCondition += $" (ds.Location='{currentCloudPlatformType}' OR ds.Location IS NULL) AND ";
                //阿里平台不能同步多多云平台的店铺（多多云平台的店铺Version是大于2）
                if (currentCloudPlatformType == PlatformType.Alibaba.ToString())
                    pftCondition += " s.PlatformType NOT IN('Pinduoduo','KuaiTuanTuan') AND ";// $" (s.Version IS NULL OR s.Version<3 ) AND ";
                else if (currentCloudPlatformType == PlatformType.Pinduoduo.ToString())
                    pftCondition += $" s.PlatformType IN('Pinduoduo','KuaiTuanTuan') AND ";
                else if (currentCloudPlatformType == PlatformType.TouTiao.ToString())
                    pftCondition += $" s.PlatformType IN('TouTiao') AND ";
            }
            var sql = $@"
SELECT s.*
FROM P_Shop s WITH(NOLOCK)
    LEFT JOIN P_DbConfig d WITH(NOLOCK)
        ON s.Id = d.ShopId
    LEFT JOIN P_DbNameConfig dn WITH(NOLOCK)
        ON dn.Id = d.DbNameConfigId
    LEFT JOIN P_DbServerConfig ds WITH(NOLOCK)
        ON dn.DbServerConfigId = ds.Id
WHERE
    {pftCondition}
    (
          ds.Location IS NULL
          OR ds.Location = 'Alibaba'
      )
      AND s.FullSyncCompleteTime IS NULL
      AND
      (
          s.FullSyncStatus = 'Pending'
          OR
          (
              s.FullSyncStatus = 'Error'
              AND s.FullSyncErrorTimes < 5
          )
      )
ORDER BY s.id DESC; ";
            var shops = _repository.Query(sql, null);
            return shops;
        }


        /// <summary>
        /// 获取未全量同步的店铺
        /// </summary>
        /// <param name="platformType">所要包含或排除的平台类型，多个逗号隔开，排除的平台前面加!</param>
        /// <param name="currentCloudPlatformType">当前的云平台类型：Alibaba、Pinduoduo、Jingdong，仅能同步一个云平台店铺的数据</param>
        /// <returns></returns>
        public List<Shop> GetUnFullSynced(string platformType, string currentCloudPlatformType)
        {
            var pftCondition = string.Empty;
            if (string.IsNullOrWhiteSpace(platformType) == false)
            {
                if (platformType?.ToLower() == "all" || platformType?.ToLower() == "*")
                {
                }
                else if (platformType.StartsWith("!"))
                {
                    var pts = string.Join(",", platformType.Split(',').Select(s => $"'{s.Trim('!')}'"));
                    pftCondition = $" s.PlatformType not in({pts}) AND ";
                }
                else if (platformType.Contains(","))
                {
                    var platforms = platformType.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                    pftCondition = $" s.PlatformType in ('{string.Join("','", platforms)}') AND ";
                }
                else
                {
                    pftCondition = $" s.PlatformType='{platformType}' AND ";
                }
            }
            var withSql = "WITH(INDEX=IX_FullSyncQuery,NOLOCK)";
            //头条配置库指定索引会报错
            if (CustomerConfig.CloudPlatformType.Contains("TouTiao"))
                withSql = "WITH(NOLOCK)";
            //            var sql = $@"
            //SELECT s.*
            //FROM P_Shop s {withSql}
            //WHERE
            //    {pftCondition}
            //      s.FullSyncCompleteTime IS NULL
            //      AND
            //      (
            //          s.FullSyncStatus = 'Pending'
            //          OR
            //          (
            //              s.FullSyncStatus = 'Error'
            //              AND s.FullSyncErrorTimes < 5
            //          )
            //      ) "; 
            var sql = $@"SELECT s.* FROM P_Shop s {withSql} WHERE {pftCondition} s.FullSyncStatus IN(null,'Pending') ";
            var shops = _repository.Query(sql, null)?.OrderBy(x => x.Id)?.ToList();
            return shops;
        }


        public List<Shop> GetHotUnFullSynced(string platformType)
        {
            var pftCondition = string.Empty;
            if (string.IsNullOrWhiteSpace(platformType) == false)
            {
                if (platformType?.ToLower() == "all" || platformType?.ToLower() == "*")
                {
                }
                else if (platformType.StartsWith("!"))
                {
                    pftCondition = $" s.PlatformType != '{platformType.Trim('!')}' AND ";
                }
                else if (platformType.Contains(","))
                {
                    var platforms = platformType.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                    pftCondition = $" s.PlatformType in ('{string.Join("','", platforms)}') AND ";
                }
                else
                {
                    pftCondition = $" s.PlatformType='{platformType}' AND ";
                }
            }
            var sql = $@"SELECT s.*
FROM dbo.P_DbConfig d WITH(NOLOCK)
    INNER JOIN dbo.P_DbNameConfig dn WITH(NOLOCK)
        ON d.DbNameConfigId = dn.Id
    INNER JOIN dbo.P_Shop s WITH(NOLOCK)
        ON d.ShopId = s.Id
    LEFT JOIN dbo.P_HotDataSyncStatus h WITH(NOLOCK)
        ON d.ShopId = h.ShopId
WHERE 
    {pftCondition}        
    d.IsHotTableEnabled = 1
      AND
      (
          h.FullSyncStatus IS NULL
          OR h.FullSyncStatus = 'Pending'
      )
      AND 
	  (
		  s.FullSyncStatus = 'Complete'
		  OR s.FullSyncStatus='Close'
	  )
";
            var shops = _repository.Query(sql, null);
            return shops;
        }

        /// <summary>
        /// 获取未全量同步的店铺（针对抖店做了二次过滤）
        /// </summary>
        /// <param name="platformType">所要包含或排除的平台类型，多个逗号隔开，排除的平台前面加!</param>
        /// <param name="currentCloudPlatformType">当前的云平台类型：Alibaba、Pinduoduo、Jingdong、TouTiao，仅能同步一个云平台店铺的数据</param>
        /// <returns></returns>
        public List<Shop> GetFxOrderSyncedList(string platformType, string currentCloudPlatformType, SyncTaskType taskType, int queueMaxSize, int shopId = 0)
        {
            var pts_no_in = new List<string>();
            var pts_in = new List<string>();
            if (string.IsNullOrWhiteSpace(platformType) == false)
            {
                if (platformType?.ToLower() == "all" || platformType?.ToLower() == "*")
                {
                }
                else if (platformType.StartsWith("!"))
                {
                    pts_no_in = platformType.Split(',').Select(s => $"{s.Trim('!')}").ToList();
                }
                else if (platformType.Contains(","))
                {
                    pts_in = platformType.Split(',').Select(s => $"{s.Trim('!')}").ToList();
                }
                else
                {
                    pts_in.Add(platformType);
                }
            }
            if (!string.IsNullOrEmpty(currentCloudPlatformType))
            {
                //阿里平台不能同步多多云平台的店铺（多多云平台的店铺Version是大于2）
                if (currentCloudPlatformType == PlatformType.Alibaba.ToString())
                {
                    pts_no_in.Add("Pinduoduo");
                    pts_no_in.Add("KuaiTuanTuan");
                }
                //抖店云，只取相关的店铺平台类型
                else if (currentCloudPlatformType == PlatformType.TouTiao.ToString())
                    pts_in = CustomerConfig.UseTouTiaoCloudPlatformTypes;
            }
            var shops = _repository.GetFxOrderSyncedList(pts_in, pts_no_in, taskType, queueMaxSize, shopId).ToList();
            shops?.ForEach(shop =>
            {
                shop.FxUserIds = shop.SyncStatusList.FirstOrDefault()?.FxUserId ?? 0;
            });

            //精选平台，针对抖店做二次过滤
            shops = FilterTouTiaoShop(shops, currentCloudPlatformType);

            return shops;
        }

        /// <summary>
        /// 针对抖店做二次过滤
        /// </summary>
        /// <param name="shops"></param>
        /// <param name="curCloudPlatformType"></param>
        public List<Shop> FilterTouTiaoShop(List<Shop> shops, string curCloudPlatformType)
        {
            curCloudPlatformType = curCloudPlatformType.ToLower();
            //精选平台或抖店云，针对抖店做二次过滤
            if (shops != null && shops.Any() && (curCloudPlatformType == CloudPlatformType.Alibaba.ToString().ToLower() || curCloudPlatformType == CloudPlatformType.TouTiao.ToString().ToLower()))
            {
                var ttFxUserIds = shops.Where(a => CustomerConfig.UseTouTiaoCloudPlatformTypes.Contains(a.PlatformType))?.Select(a => a.FxUserIds).Distinct().ToList();
                if (ttFxUserIds != null && ttFxUserIds.Any())
                {
                    //查询抖店云业务库配置
                    var fxDbConfigs = new FxDbConfigRepository().GetDbConfigs(ttFxUserIds, CloudPlatformType.TouTiao.ToString());
                    var useNewDbConfigFxUserIds = fxDbConfigs.Where(a => a.FromFxDbConfig == 1).Select(a => a.UserId).ToList();
                    if (useNewDbConfigFxUserIds.Any())
                    {
                        if (curCloudPlatformType == CloudPlatformType.Alibaba.ToString().ToLower())
                        {
                            //精选平台，排除使用抖店云新配置的店铺
                            shops = shops.Where(a => (!CustomerConfig.UseTouTiaoCloudPlatformTypes.Contains(a.PlatformType)) || (a.PlatformType == PlatformType.TouTiao.ToString() && !useNewDbConfigFxUserIds.Contains(a.FxUserIds))).ToList();

                        }
                        else
                        {
                            //抖店云，只取使用新配置的店铺
                            shops = shops.Where(a => CustomerConfig.UseTouTiaoCloudPlatformTypes.Contains(a.PlatformType) && useNewDbConfigFxUserIds.Contains(a.FxUserIds)).ToList();
                        }
                    }
                    else if (curCloudPlatformType == CloudPlatformType.TouTiao.ToString().ToLower())
                    {
                        //抖店云，无店铺可同步
                        shops = new List<Shop>();
                    }
                }
            }
            return shops;
        }

        /// <summary>
        /// 检查头条所在云平台是否正确
        /// </summary>
        /// <param name="fxUserId"></param>
        public void CheckTouTiaoShopCloudPlatformType(int fxUserId)
        {
            var curCloudPlatformType = CustomerConfig.CloudPlatformType.ToLower();
            //查询抖店云业务库配置情况
            var fxDbConfig = new FxDbConfigRepository().GetByFxUserId(fxUserId, CloudPlatformType.TouTiao.ToString());
            if (fxDbConfig != null && fxDbConfig.FromFxDbConfig == 1 && curCloudPlatformType == CloudPlatformType.Alibaba.ToString().ToLower())
            {
                throw new Exception($"已迁移到抖店云，请到抖店云同步订单，FxUserId={fxUserId}");
            }
            else if ((fxDbConfig == null || fxDbConfig.FromFxDbConfig != 1) && curCloudPlatformType == CloudPlatformType.TouTiao.ToString().ToLower())
            {
                throw new Exception($"未迁移到抖店云，请到精选云同步订单，FxUserId={fxUserId}");
            }

        }

        /// <summary>
        /// 检查所在云平台是否正确
        /// </summary>
        /// <param name="platformType">订单所属平台类型</param>
        /// <param name="shopId"></param>
        public void CheckShopCloudPlatformType(string platformType, int shopId)
        {
            if (platformType == PlatformType.Virtual.ToString())
            {
                return;
            }
            var curCloudPlatformType = CustomerConfig.CloudPlatformType.ToLower();
            //不在精选云的平台
            var notInAlibabaPlatformTypes = CustomerConfig.FxDouDianCloudPlatformTypes
                .Concat(CustomerConfig.FxPinduoduoCloudPlatformTypes)
                .Concat(CustomerConfig.FxJingDongCloudPlatformTypes);

            if (notInAlibabaPlatformTypes.Contains(platformType) && 
                curCloudPlatformType == CloudPlatformType.Alibaba.ToString().ToLower() &&
                CustomerConfig.IsDebug == false)
            {
                throw new Exception($"非精选云店铺ShopId={shopId}，platformType={platformType}，请到其他云同步");
            }

            //抖店云只能同步抖店店铺
            if (CustomerConfig.FxDouDianCloudPlatformTypes.Contains(platformType) == false &&
                     curCloudPlatformType == CloudPlatformType.TouTiao.ToString().ToLower())
            {
                throw new Exception($"非抖店店铺ShopId={shopId}，PlatformType={platformType}，请到其他云同步");
            }
            else if (CustomerConfig.FxPinduoduoCloudPlatformTypes.Contains(platformType) == false && curCloudPlatformType == CloudPlatformType.Pinduoduo.ToString().ToLower())
            {
                throw new Exception($"非拼多多店铺ShopId={shopId}，PlatformType={platformType}，请到其他云同步");
            }
            // 非京东平台不能同步京东供销数据
            else if (platformType == PlatformType.JingdongPurchase.ToString() && curCloudPlatformType != CloudPlatformType.Jingdong.ToString().ToLower())
            {
                throw new Exception($"京东供销店铺ShopId={shopId}，PlatformType={platformType}，请到京东云同步");
            }
            // 非京东平台不能同步京东数据
            else if (platformType == PlatformType.Jingdong.ToString() && curCloudPlatformType != CloudPlatformType.Jingdong.ToString().ToLower())
            {
                throw new Exception($"京东店铺ShopId={shopId}，PlatformType={platformType}，请到京东云同步");
            }
        }

        /// <summary>
        /// 检查指定店铺数据是否存储在多多云上
        /// </summary>
        /// <param name="shopId">店铺id</param>
        /// <returns></returns>
        public Shop GetShopWhereInPinduoduoCloudWithDbConfig(string shopId)
        {
            return _repository.GetShopWithDbConfig(new List<string> { shopId }, PlatformType.Pinduoduo.ToString(), PlatformType.Pinduoduo.ToString())?.FirstOrDefault();
        }

        public List<Shop> GetShopWithDbConfig(List<string> shopIds, string pt)
        {
            return _repository.GetShopWithDbConfig(shopIds, pt);
        }

        public Shop GetShopWithDbConfig(int shopId)
        {
            return _repository.GetShopWithDbConfig(shopId);
        }



        public void RefreshToken(LoginAuthToken model)
        {
            if (model != null)
            {
                model.RefreshExpiredTime();
                _repository.UpdateLoginToken(model);
            }
        }

        public void UpdateLoginToken(LoginAuthToken model)
        {
            if (model != null)
            {
                _repository.UpdateLoginToken(model);
            }
        }

        public LoginAuthToken GetToken(int id)
        {
            return _repository.GetToken(id);
        }

        public LoginAuthToken ExpainToken(string token, string clientSign = "")
        {
            if (string.IsNullOrEmpty(token))
                return null;
            try
            {
                var tid = DES.DecryptUrl(token, CustomerConfig.LoginCookieEncryptKey);
                var model = GetToken(tid.ToInt());
                //if (model != null)
                //    model.ClientSign = Utility.Net.HttpUtility.GetClientMAC(Request.UserHostAddress).GetHashCode().ToString();
                if (model != null && clientSign.IsNotNullOrEmpty())
                    model.ClientSign = clientSign;
                return model;
            }
            catch (Exception)
            {
                return null;
            }
        }

        public LoginAuthToken GetTokenByShopId(int shopId)
        {
            return _repository.GetTokenByShopId(shopId);
        }


        public Shop GetShopByToken(string token)
        {
            var tid = 0;
            try
            {
                tid = int.Parse(DES.DecryptUrl(token, CustomerConfig.LoginCookieEncryptKey));
            }
            catch (Exception ex)
            {
                Log.WriteError($"Token:{token}解析失败!错误:{ex.Message}");
            }
            var model = this.GetToken(tid);
            return this.Get(model?.ShopId ?? 0);
        }


        public Shop GetShopByAccessToken(string accessToken, PlatformType pty)
        {
            return this.Get(" WITH(NOLOCK) WHERE AccessToken=@token AND PlatformType=@pty ", new { token = accessToken, pty = pty.ToString() }).FirstOrDefault();
        }

        /// <summary>
        /// 检查token是否过期
        /// </summary>
        /// <param name="id">token的ID</param>
        /// <returns></returns>
        public bool IsTokenExpired(int id)
        {
            var token = _repository.GetToken(id);
            if (token == null || token.IsExpired)
                return true;
            return false;
        }

        public LoginAuthToken ExpainToken(string token)
        {
            if (string.IsNullOrEmpty(token))
                return null;
            try
            {
                var tid = DES.DecryptUrl(token, CustomerConfig.LoginCookieEncryptKey);
                var model = GetToken(tid.ToInt());
                return model;
            }
            catch (Exception)
            {
                return null;
            }
        }
        /// <summary>
        /// 检查token是否合法
        /// </summary>
        /// <param name="model">model</param>
        /// <param name="sign">token的签名</param>
        /// <param name="md5sign">token的签名</param>
        /// <param name="isFromWeixin">是否来自微信小程序的请求，若是，不验证签名</param>
        /// <param name="ip">客户端请求IP</param>
        /// <param name="isOpenDoubleAuth">是否双重验证：0:关闭；1:开启，不验证签名；-1:白名单</param>
        /// <returns>true：合法，false 非法</returns>
        public LoginAuthToken IsTokenValid(LoginAuthCookieModel model, string sign, string md5sign, bool isFromWeixin, string ip, string md5signForWechat = "", int isOpenDoubleAuth = 0, bool isIgnoreDoubleAuth = false)
        {
            var token = _repository.GetToken(model.Id);
            if (token != null)
            {
                model.ShopId = token.ShopId;
                //if (CustomerConfig.IsDebug)
                //    return token;
                if (string.IsNullOrEmpty(md5signForWechat))
                    md5signForWechat = md5sign;
                var isRefererOk = CheckRefererIsValid(token);
                var isNewToken = token.CreateTime > DateTime.Now.AddMinutes(-2);
                var isSameIp = !string.IsNullOrEmpty(token.IP) && token.IP == ip;
                //更新为md5 token，兼容先前的哈希值，哈希值不同服务器生成的可能不一样
                //新的token，防止浏览器自动切换模式时导致token不一样导致登录过期
                if (isNewToken && token.Sign != md5sign && isSameIp)
                {
                    token.Sign = md5sign;
                    UpdateLoginToken(token);
                }
                //将数据库老的token更新为md5的，避免hashcode发生变化引起异常
                else if (token.Sign == sign && isSameIp)
                {
                    token.Sign = md5sign;
                    UpdateLoginToken(token);
                }
                if (isIgnoreDoubleAuth)
                {
                    return token;
                }
                if (token != null && !token.IsExpired && (token.Sign == sign || token.Sign == md5sign || token.Sign == md5signForWechat || isFromWeixin || isSameIp || isOpenDoubleAuth == 1))//&& token.ExpiredTime > DateTime.Now)
                {
                    model.ShopId = token.ShopId;
                    return token;
                }
                else
                {
                    Log.WriteLine($"token验证失败，请求：sign：{sign}，md5Sign:{md5sign}，isRefererOk：{isRefererOk}，isSameIp：{isSameIp}\r\n数据库Token:{token?.ToJson()}");
                }
            }
            return null;
        }

        public bool CheckRefererIsValid(LoginAuthToken model)
        {
            var isValid = false;
            var referrer = System.Web.HttpContext.Current?.Request?.UrlReferrer?.ToString();
            if (!string.IsNullOrEmpty(referrer))
            {
                if (referrer.Contains("dgjapp.com"))
                    return true;
                var tokenIndex = referrer.IndexOf("token=");
                if (tokenIndex >= 0 && tokenIndex + 6 <= referrer.Length)
                {
                    var token = referrer.Substring(tokenIndex + 6);
                    if (!string.IsNullOrEmpty(token))
                    {
                        try
                        {
                            DES.DecryptUrl(token, CustomerConfig.LoginCookieEncryptKey);
                            isValid = true;
                        }
                        catch
                        {
                        }
                    }
                }

            }
            return isValid;
        }

        //[Obsolete("请使用CreateFxToken")]
        //public LoginAuthToken CreateToken(int shopId, string sign, bool isQuickLink = false, bool isFromParent = false, int userId = 0, int fromId = 0, int subUserId = 0, string ip = "")
        //{
        //    var token = _repository.CreateToken(shopId, sign, isQuickLink, isFromParent, userId, fromId, subUserId, ip);
        //    return token;
        //}

        /// <summary>
        /// 分销系统用户登录创建Token
        /// </summary>
        /// <param name="shopId"></param>
        /// <param name="sign"></param>
        /// <param name="isQuickLink"></param>
        /// <param name="isFromParent"></param>
        /// <param name="userId"></param>
        /// <param name="fromId"></param>
        /// <param name="subUserId"></param>
        /// <param name="ip"></param>
        /// <returns></returns>
        public LoginAuthToken CreateFxToken(int fxUserId, string sign, int shopId = 0, bool isQuickLink = false, bool isFromParent = false, int fromId = 0, int subUserId = 0, string ip = "")
        {
            var token = _repository.CreateFxToken(fxUserId, sign, shopId, isQuickLink, isFromParent, fromId, subUserId, ip);
            return token;
        }

        public LoginAuthToken UpdateFxToken(LoginAuthToken token)
        {
            var res = _repository.UpdateFxToken(token);
            return res;
        }
        /// <summary>
        /// 作废分销系统当前登录token 以外的其他token
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="sign"></param>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public bool ExpiredFxToken(int fxUserId, int subfxUserId, string sign, int shopId, int tokenId)
        {
            var res = _repository.ExpiredFxToken(fxUserId, subfxUserId, sign, shopId, tokenId);
            return res;
        }

        public bool ExpiredFxTokenAll(int fxUserId, int subfxUserId, int shopId)
        {
            var res = _repository.ExpiredFxTokenAll(fxUserId, subfxUserId, shopId);
            return res;
        }

        /// <summary>
        /// 分销系统用户登录创建Token
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public LoginAuthToken GetFxToken(int fxUserId)
        {
            var token = _repository.GetTokenByFxUserId(fxUserId);
            return token;
        }

        /// <summary>
        /// 获取用户Token
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public List<LoginAuthToken> GetTokenByFxUserIds(List<int> fxUserIds)
        {
            if (fxUserIds.IsNullOrEmptyList())
                return new List<LoginAuthToken>();
            return _repository.GetTokenByFxUserIds(fxUserIds);
        }

        /// <summary>
        /// 分销系统用户根据FxUserId获取SystemShopId
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="selectFields"></param>
        /// <returns></returns>
        public FxUserShop GetFxUserShopByFxUserId(int fxUserId, string selectFields = "*")
        {
            var model = _repository.GetFxUserShopByFxUserId(fxUserId, selectFields);
            return model;
        }

        public LoginAuthToken CreateSubUserToken(int shopId, string sign, bool isQuickLink = false, bool isFromParent = false, int subUserId = 0)
        {
            var authToken = new LoginAuthToken
            {
                ShopId = shopId,
                Sign = sign,
                IsQuickLink = isQuickLink,
                IsFromParent = isFromParent,
                SubUserId = subUserId
            };
            var token = _repository.CreateToken(authToken);
            return token;
        }

        public MigrateShop UpdateMigrateShop(MigrateShop model)
        {
            return _repository.UpdateMigrateShop(model);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="pt"></param>
        /// <param name="mid"></param>
        /// <returns></returns>
        public InvitedMigrateShop GetInvitedMigrateShop(string pt, string mid)
        {
            return _repository.GetInvitedMigrateShop(pt, mid);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="pt"></param>
        /// <param name="mid"></param>
        /// <returns></returns>
        public InvitedMigrateShop UpdateInvitedMigrateShop(InvitedMigrateShop migrateShop)
        {
            var model = _repository.UpdateInvitedMigrateShop(migrateShop);
            if (migrateShop.Status == "Agree")
            {
                TryAddMigrateShop(migrateShop.MemberId, migrateShop.PlatformType, true);
            }
            return model;
        }
        public string TryAddInviteMigrateShop(List<string> wangwangs, string platformType)
        {
            var db = _repository.GetConfigureDb();
            var memberids = ParseWangwangToMemberId(wangwangs, platformType);
            if (memberids == null || !memberids.Any())
                return "根据旺旺号没有查询到对应的MemberId";
            var mids = new List<string>();
            memberids.ForEach(m =>
            {
                if (!string.IsNullOrEmpty(m))
                {
                    var old = db.GetList<InvitedMigrateShop>(" WITH(NOLOCK) where MemberId=@mid AND PlatformType=@pt", new { mid = m, pt = platformType })?.FirstOrDefault();
                    if (old != null)
                        mids.Add($"店铺【{m}】已存在于邀请队列中，邀请状态：【{old.Status}】");
                    else
                    {
                        var id = TryAddInviteMigrateShop(m, platformType);
                        mids.Add(id.ToString());
                    }
                }
            });
            return mids.ToJson();
        }
        /// <summary>
        /// 获取需要增量同步的店铺ID
        /// </summary>
        /// <returns></returns>
        public List<int> GetNeedSyncShopIds()
        {
            var db = _repository.GetConfigureDb();
            var minutes = 120;
            var time = DateTime.Now.AddMinutes(-minutes);
            var stime = DateTime.Now.AddDays(-7);
            var atime = DateTime.Now.AddMonths(-1).ToString("yyyy-MM-dd");
            var shops = db.Query<int>($"SELECT Id FROM dbo.P_Shop WITH(NOLOCK) where LastSyncTime<@time and LastSyncTime>'{stime}' and AuthTime>'{atime}' and LastSyncMessage Not like '%】授权过期，%' order by LastSyncTime asc", new { time }).ToList();
            return shops;
        }

        public List<Shop> GetShopByMemberId(List<string> memberIds, string platformType)
        {
            return _repository.GetShopByMemberId(memberIds, platformType);
        }


        public List<Shop> GetShopByVenderId(List<string> venderIds, string platformType)
        {
            return _repository.GetShopByVenderId(venderIds, platformType);
        }


        #region 老系统数据操作


        /// <summary>
        /// 查询老系统的用户
        /// </summary>
        /// <param name="memberIds"></param>
        /// <param name="platformType"></param>
        /// <returns></returns>
        public List<Shop> GetOldShopByMemberId(List<string> memberIds, string platformType)
        {
            var _sourceDb = baseRepository.GetOldDb(platformType);
            var memberInfo = _sourceDb.Query<Shop>(@"SELECT  Resource_Owner AS NickName ,
                                                            Resource_Owner AS ShopName ,
                                                            MemberId AS ShopId ,
                                                            Access_Token AS AccessToken ,
                                                            Refresh_Token AS RefreshToken ,
                                                            GetDateTime AS AuthTime ,
                                                            AddDate AS CreateTime
                                                    FROM    dbo.P_MemberToken
                                                    WHERE MemberId IN @MemberIds", new { MemberIds = memberIds });
            return memberInfo.Select(f =>
            {
                f.PlatformType = platformType; //老系统不同平台用户是分开存储的，所以shop并没有标记platform，这里需赋值一下
                return f;
            }).ToList();
        }

        /// <summary>
        /// 从老系统中获取RefreshToken
        /// </summary>
        /// <param name="shop"></param>
        /// <returns></returns>
        public string GetOldShopRefreshToken(Shop shop)
        {
            var _sourceDb = baseRepository.GetOldDb(shop.PlatformType);
            var token = _sourceDb.ExecuteScalar("SELECT TOP 1 Refresh_Token from P_MemberToken where memberid=@mid ", new { mid = shop.ShopId })?.ToString();
            return token;
        }

        /// <summary>
        /// 根据joincode获取老店铺信息
        /// </summary>
        /// <param name="shop"></param>
        /// <returns></returns>
        public OldMemberToken GetOldShopByJoinCode(string code, string platformType)
        {
            if (platformType?.ToLower() != PlatformType.Alibaba.ToString().ToLower()
                && platformType?.ToLower() != PlatformType.Taobao.ToString().ToLower())
                return null;
            var _sourceDb = baseRepository.GetOldDb(platformType);
            var token = _sourceDb.Query<OldMemberToken>("SELECT TOP 1 * from P_MemberToken where JoinCode=@code ", new { code })?.FirstOrDefault();
            return token;
        }
        /// <summary>
        /// 根据membeerid获取店铺关联信息
        /// </summary>
        /// <param name="mid">memberId</param>
        /// <param name="platformType">关联平台类型</param>
        /// <returns></returns>
        public List<OldMemberRelation> GetOldRelations(string mid, string platformType)
        {
            if (platformType == "Alibaba")
                platformType = "1688";
            else if (platformType == "Pinduoduo")
                platformType = "pdd";
            else if (platformType == "Taobao")
                platformType = "TB";
            var configureDb = baseRepository.GetOldConfigureDb();
            //处理子店铺，将子店铺添加进来，但不进行数据迁移
            var sql1 = @"select  * from P_MemberRelation  where Pri_MemberId=@Pri_MemberId order by AddDate asc";//and Pri_Channels=@Pri_Channels
            var sql = @"SELECT *
FROM P_MemberRelation
WHERE Pri_MemberId =
(
    SELECT Pri_MemberId
    FROM P_MemberRelation AS P_MemberRelation_1
    WHERE (Rel_MemberId = @Rel_MemberId)
          --AND (Rel_Channels = @Rel_Channels)
)
      AND Pri_Channels =
      (
          SELECT Pri_Channels
          FROM P_MemberRelation AS P_MemberRelation_2
          WHERE (Rel_MemberId = @Rel_MemberId)
                --AND (Rel_Channels = @Rel_Channels)
      )
ORDER BY AddDate ASC;";
            var rels = configureDb.Query<OldMemberRelation>(sql1, new { Pri_MemberId = mid }).ToList();
            if (rels == null || !rels.Any())
                rels = configureDb.Query<OldMemberRelation>(sql, new { Rel_MemberId = mid }).ToList();
            return rels;
        }


        #endregion

        #region 数据库配置
        /// <summary>
        /// 查询店铺数据库配置信息
        /// </summary>
        /// <param name="shopIds">店铺ID列表</param>
        /// <returns></returns>
        public List<DatabaseConfig> GetDatabaseConfigs(List<int> shopIds)
        {
            var db = _repository.GetConfigureDb();
            var dcs = db.GetList<DatabaseConfig>(" where ShopId IN @sids", new { sids = shopIds }).ToList();
            return dcs;
        }

        public void UpdateDatabaseConfig(DatabaseConfig config)
        {
            var db = _repository.GetConfigureDb();
            db.Update(config);
        }

        #endregion

        #region 微商版
        public Shop GetByUserId(int userId)
        {
            return GetsByUserId(userId)?.FirstOrDefault();
            //var db = _repository.GetConfigureDb();
            //var shop = db.Query<Shop>("SELECT top 1 s.* FROM dbo.P_Shop s INNER JOIN P_UserShopRelation usr ON s.Id = usr.ShopId WHERE usr.UserId=@userId", new { userId }).FirstOrDefault();
            //return shop;
        }

        public List<Shop> GetsByUserId(int userId, bool isAll = true)
        {
            var db = _repository.GetConfigureDb();
            if (isAll)
                return db.Query<Shop>("SELECT s.* FROM dbo.P_Shop s WITH(NOLOCK) INNER JOIN P_UserShopRelation usr WITH(NOLOCK) ON s.Id = usr.ShopId WHERE usr.UserId=@userId", new { userId }).ToList();
            else
                return db.Query<Shop>("SELECT s.* FROM dbo.P_Shop s WITH(NOLOCK) INNER JOIN P_UserShopRelation usr WITH(NOLOCK) ON s.Id = usr.ShopId WHERE usr.UserId=@userId AND usr.IsDeleted=0 ", new { userId }).ToList();
        }

        /// <summary>
        /// 通过openId获取店铺信息
        /// </summary>
        /// <param name="openId"></param>
        /// <param name="isAdd">为空是否添加</param>
        /// <returns></returns>
        public Shop GetShopByOpenId(string openId, bool isAdd)
        {
            var db = _repository.GetConfigureDb();
            var shops = db.Query<Shop>("SELECT s.* FROM dbo.P_Shop s WITH(NOLOCK)  WHERE s.ShopId=@openId", new { openId }).FirstOrDefault();

            if (shops == null && isAdd)
            {
                var shop = new Shop
                {
                    ShopId = openId,
                    ShopName = openId,
                    AccessToken = "",
                    RefreshToken = "",
                    AuthTime = DateTime.Now,
                    CreateTime = DateTime.Now,
                    ExpireTime = null,
                    PlatformType = PlatformType.Offline.ToString(),
                    FullSyncStatus = "Close",
                    OnlyCode = CommUtls.GetOnlyCode()
                };

                shops = _repository.Add(shop) > 0 ? db.Query<Shop>("SELECT s.* FROM dbo.P_Shop s WITH(NOLOCK)  WHERE s.ShopId=@openId", new { openId }).FirstOrDefault() : null;

            }

            return shops;
        }

        public Shop GetShopById(int id)
        {
            var db = _repository.GetConfigureDb();
            var shops = db.Query<Shop>("SELECT s.* FROM dbo.P_Shop s WITH(NOLOCK)  WHERE s.Id=@id", new { id }).FirstOrDefault();

            return shops;
        }




        public List<Shop> GetShopByIds(List<int> ids)
        {
            return _repository.GetShopByIds(ids);
        }


        public List<Shop> GetShopByIds(List<int> ids, string fields)
        {
            return _repository.GetShopByIds(ids, fields);
        }


        public Shop GetAnyShopByPlatformType(string platformType, int exceptShopId = 0)
        {
            //return _repository.Get("WHERE PlatformType=@platformtype order by authtime desc;", new { platformtype = platformType }).FirstOrDefault();

            var shop = _repository.Query($@"select top 1 * from p_shop WITH(NOLOCK) 
                                            WHERE PlatformType=@platformtype AND Id!={exceptShopId} AND LastSyncStatus='Finished' AND LastSyncMessage NOT LIKE '%授权过期%' 
                                            ORDER BY LastSyncTime DESC,LastRefreshTokenTime DESC;",
                                            new { platformtype = platformType }).FirstOrDefault();
            if (shop == null)
                shop = _repository.Query($@"select top 1 * from p_shop WITH(NOLOCK)  
                                            WHERE PlatformType=@platformtype AND Id!={exceptShopId} AND LastSyncStatus='Finished'
                                            ORDER BY LastSyncTime DESC,LastRefreshTokenTime DESC;",
                                            new { platformtype = platformType }).FirstOrDefault();
            return shop;
        }

        /// <summary>
        /// 通过平台类型和token，找到是哪个店铺，用于贝贝消息推送
        /// </summary>
        /// <param name="platformType"></param>
        /// <param name="refreshToken"></param>
        /// <returns></returns>
        public Shop GetShopByPlatformTypeToken(string platformType, string refreshToken)
        {
            return _repository.Get(" WITH(NOLOCK) WHERE PlatformType=@platformtype and RefreshToken=refreshToken order by authtime desc;", new { platformtype = platformType, refreshToken = refreshToken }).FirstOrDefault();
        }

        public List<Shop> GetShopByPlatformType(string platformType)
        {
            return _repository.Get(" WITH(NOLOCK) WHERE PlatformType=@platformtype order by authtime desc;", new { platformtype = platformType }).ToList();
        }

        public List<Shop> GetShopByPlatformType_test()
        {
            var db = _repository.GetConfigureDb();
            var shop = db.Query<Shop>("SELECT t.LastSyncTime,* FROM dbo.P_Shop t WITH(NOLOCK) WHERE PlatformType='pinduoduo' AND t.LastSyncTime IS NOT NULL AND t.LastSyncTime > '2020-01-01' AND t.shopName=t.nickName ORDER BY t.LastSyncTime desc").ToList();
            return shop;
        }

        public Shop Get(int userId, int shopId)
        {
            var db = _repository.GetConfigureDb();
            var shop = db.Query<Shop>("SELECT top 1 s.* FROM dbo.P_Shop s WITH(NOLOCK) INNER JOIN P_UserShopRelation usr WITH(NOLOCK) ON s.Id = usr.ShopId WHERE usr.UserId=@userId and usr.ShopId=@shopId", new { userId, shopId }).FirstOrDefault();
            return shop;
        }
        #endregion

        #region 京东日志

        public void AddJdRequestLog(jos_sdk_net.JdApiLogModel model)
        {
            _repository.AddJdRequestLog(model);
        }

        #endregion

        ///// <summary>
        ///// 获取单个店铺的同步记录信息
        ///// </summary>
        ///// <param name="shopId">店铺ID</param>
        ///// <returns></returns>
        //public SyncOrderProcess GetSyncInfo(int shopId)
        //{
        //    return _repository.GetSyncInfo(shopId);
        //}

        ///// <summary>
        ///// 获取多个店铺的同步进度信息
        ///// </summary>
        ///// <param name="shopIds"></param>
        ///// <returns></returns>
        //public List<SyncOrderProcess> GetSyncInfo(List<int> shopIds)
        //{
        //    return _repository.GetSyncInfo(shopIds);
        //}

        public void UpdateSyncInfoStep(int shopId, string step)
        {
            //_repository.UpdateSyncInfoStep(shopId, step);
        }

        public Shop GetShopByShareCode(string shareCode)
        {
            var db = _repository.GetConfigureDb();
            var sql = "Select * from P_shop WITH(NOLOCK) where ShareCode=@shareCode";
            return db.Query<Shop>(sql, new { shareCode })?.FirstOrDefault();
        }

        public List<Shop> GetShopByName(string name)
        {
            var db = _repository.GetConfigureDb();
            var sql = "Select * from P_shop WITH(NOLOCK) where NickName =@name Or ShopName =@name";
            return db.Query<Shop>(sql, new { name })?.ToList();
        }

        public List<int> GetShopIdByShopIdAndPlatformType(string shopId, string platformType)
        {
            var db = _repository.GetConfigureDb();
            var sql = "select Id from P_shop WITH(NOLOCK) where ShopId =@shopId AND PlatformType =@platformType";
            return db.Query<int>(sql, new { shopId, platformType })?.ToList();
        }

        public List<OrderQuantityInfo> GetOrderQuantityInfo(List<Shop> shops)
        {
            return _repository.GetOrderQuantityInfo(shops);
        }

        public void UpadateVenderId(Dictionary<int, string> shopIdVenderIdDict)
        {
            var sql = "Update p_shop set VenderId=@venderId where id=@id";
            foreach (var dict in shopIdVenderIdDict)
            {
                var result = _repository.GetConfigureDb().Execute(sql, new
                {
                    venderId = dict.Value,
                    id = dict.Key
                });
                //缓存性能优化:删除原来缓存，实时更新
                FxCaching.RefeshCache(FxCachingType.FxShopSelf, dict.Key);
            }
        }

        public void UpdateNickName(Dictionary<int, string> shopIdNickNameDict)
        {
            var sql = "Update p_shop set nickname=@nickName where id=@id";
            foreach (var dict in shopIdNickNameDict)
            {
                var result = _repository.GetConfigureDb().Execute(sql, new
                {
                    nickName = dict.Value,
                    id = dict.Key
                });
            }
        }

        public List<Shop> GetShopByLastSyncTime(string datetime)
        {
            return _repository.GetShopByLastSyncTime(datetime);
        }
        public string GetShopSyncStatus(int shopId)
        {
            var db = _repository.GetConfigureDb();
            var sql = "Select LastSyncStatus from P_shop WITH(NOLOCK) where Id=@shopId";
            return db.Query<string>(sql, new { shopId })?.FirstOrDefault();
        }


        public List<Shop> Query(string sql)
        {
            var db = _repository.GetConfigureDb();
            var list = db.Query<Shop>(sql).ToList();
            return list;
        }

        public bool IsExistNotFinishedDataMigrateTask(int shopId)
        {
            var db = _repository.GetConfigureDb();
            var sql = $"SELECT top 1 Id FROM dbo.P_DataMigrateTask WITH(NOLOCK) WHERE ShopId={shopId} AND (MigrateStatus IS NULL OR MigrateStatus='Doing')";
            return db.Query<int>(sql, new { shopId })?.FirstOrDefault() > 0;
        }

        //public void CheckIsNeedMigrateData(string onlyCode, int shopId = 0)
        //{
        //    //这里不考虑不同云平台配置库不一样的问题，这个会在迁移程序上做处理。
        //    Shop shop = null;
        //    if (shopId > 0)
        //        shop = Get(shopId);
        //    if (string.IsNullOrEmpty(onlyCode) == false)
        //        shop = GetByOnlyCode(onlyCode);
        //    if (shop == null)
        //        throw new LogicException("未查询到店铺");
        //    //分平台处理
        //    var isPdd = shop.PlatformType == PlatformType.Pinduoduo.ToString();
        //    //db不一样
        //    var db = DbApiAccessUtility.GetConfigureDb();
        //    if (isPdd)
        //        db = DbApiAccessUtility.GetPddConfigureDb();
        //    var masterShop = db.Query<Shop>($"SELECT s.* FROM dbo.P_Shop s WITH(NOLOCK) INNER JOIN dbo.P_ShopRelation sr WITH(NOLOCK) ON s.Id = sr.ShopId WHERE sr.RelatedShopId={shop.Id}").FirstOrDefault();
        //    if (masterShop == null)
        //        throw new LogicException("未查询到主店铺");
        //    var masterId = masterShop.Id;
        //    var shops = db.Query<Shop>($"SELECT s.Id,s.PlatformType FROM dbo.P_Shop s WITH(NOLOCK) INNER JOIN dbo.P_ShopRelation sr WITH(NOLOCK) ON s.Id = sr.RelatedShopId WHERE sr.ShopId={masterId} AND s.PlatformType='{shop.PlatformType}'");
        //    if (shops != null && masterShop.PlatformType == shop.PlatformType)
        //        shops.Add(masterShop);
        //    if (shops == null || shops.Count() <= 1)
        //        throw new LogicException("该平台仅一个店铺，不需要迁移");
        //    var pt = shop.PlatformType;
        //    shop = shops.FirstOrDefault(x => x.Id == shop.Id);
        //    //重新给店铺的DbConfig赋值，不同云平台的DbConfig数据不一样
        //    var allShopIds = new List<int>();
        //    allShopIds.Add(shop.Id);
        //    allShopIds.AddRange(shops.Select(x => x.Id));
        //    var shopDbConfigs = db.Query<DbConfig>($"select * from P_DbConfig WITH(NOLOCK) where ShopId IN({string.Join(",", allShopIds)})");
        //    if (shopDbConfigs == null || shopDbConfigs.Any() == false)
        //        throw new LogicException("没有查询到店铺数据库配置信息，无法迁移");
        //    //获取所有店铺的数据库配置信息
        //    var dbNameConfigs = db.Query<DbNameConfig>($"select * from P_DbNameConfig WITH(NOLOCK) where Id IN({string.Join(",", shopDbConfigs.Select(x => x.DbNameConfigId).Distinct())})");
        //    if (dbNameConfigs == null || dbNameConfigs.Any() == false)
        //        throw new LogicException("没有查询到店铺数据库配置信息，无法迁移");
        //    var dbServerConfigs = db.Query<DbServerConfig>($"select * from P_DbServerConfig WITH(NOLOCK) where Id IN({string.Join(", ", dbNameConfigs.Select(x => x.DbServerConfigId).Distinct())})");
        //    if (dbNameConfigs == null || dbNameConfigs.Any() == false)
        //        throw new LogicException("没有查询到店铺数据库配置信息，无法迁移");
        //    foreach (var temp in shops)
        //    {
        //        var config = shopDbConfigs.FirstOrDefault(x => x.ShopId == temp.Id);
        //        if (config == null)
        //            throw new LogicException($"没有查询到店铺【{temp.Id}】数据库配置信息，迁移判断逻辑终止");
        //        var dc = new Data.Model.DbConfigModel
        //        {
        //            DbConfig = config,
        //        };
        //        dc.DbNameConfig = dbNameConfigs.FirstOrDefault(x => x.Id == config.DbNameConfigId);
        //        if (config == null)
        //            throw new LogicException($"没有查询到店铺【{temp.Id}】数据库配置信息，迁移判断逻辑终止");
        //        dc.DbServer = dbServerConfigs.FirstOrDefault(x => x.Id == dc.DbNameConfig.DbServerConfigId);
        //        temp.DbConfig = dc;
        //    }
        //    var dbConfigs = shops.Select(x => x.DbConfig).ToList();
        //    if (dbConfigs == null || dbConfigs.Any() == false)
        //        throw new LogicException("未查询到店铺数据库配置信息");
        //    var isSame = dbConfigs.GroupBy(d => d.Identity).Count() == 1;
        //    if (isSame)
        //        throw new LogicException("相同数据库，不需要迁移");
        //    var shopDbConfig = dbConfigs.FirstOrDefault(d => d.DbConfig.ShopId == shop.Id);
        //    if (shopDbConfig == null)
        //        throw new LogicException("当前店铺无数据库配置");
        //    var dbConfigGroups = dbConfigs.GroupBy(d => d.Identity).OrderByDescending(g => g.Count()).ThenByDescending(x => x.Key);
        //    if (dbConfigGroups.First().Any(x => x.DbConfig.ShopId == shop.Id))
        //        dbConfigGroups = dbConfigs.Where(x => x.DbConfig.ShopId != shop.Id).GroupBy(d => d.Identity).OrderByDescending(g => g.Count());
        //    var bestDbConfig = dbConfigGroups.FirstOrDefault()?.FirstOrDefault();
        //    var notSameAsBestDbConfig = dbConfigs.Where(x => x.DbConfig.DbNameConfigId != bestDbConfig.DbNameConfig.Id).ToList();
        //    if (notSameAsBestDbConfig != null && notSameAsBestDbConfig.Any())
        //    {
        //        foreach (var cur in notSameAsBestDbConfig)

        //        {
        //            shop = new Shop { Id = cur.DbConfig.ShopId, PlatformType = pt, DbConfig = cur };
        //            var task = new DataMigrateTask
        //            {
        //                SourceDbNameConfigId = cur.DbNameConfig.Id,
        //                TargetDbNameConfigId = bestDbConfig.DbNameConfig.Id,
        //                ShopId = shop.Id,
        //                CreateTime = DateTime.Now,
        //            };
        //            if (task.SourceDbNameConfigId == task.TargetDbNameConfigId)
        //                throw new LogicException("目标库不能和源库一致");
        //            //判断当前店铺是有待迁移任务
        //            var sql = $"SELECT top 1 Id FROM dbo.P_DataMigrateTask WITH(NOLOCK) WHERE ShopId={shop.Id} AND (MigrateStatus IS NULL OR MigrateStatus='Doing' OR MigrateStatus='')";
        //            var isExistOldTask = db.Query<int>(sql, new { shop.Id })?.FirstOrDefault() > 0;
        //            if (isExistOldTask)
        //                throw new LogicException("已存在待迁移的任务");
        //            //查询店铺订单数量
        //            try
        //            {
        //                var orderCount = Data.Extension.DbPolicyExtension.GetShopOrderCount(shop);
        //                var maxOrderCount = new CommonSettingService().GetMaxOrderCountToMigrateInTime();
        //                if (orderCount > maxOrderCount)
        //                {
        //                    var hopeTime = DateTime.Now.AddDays(1);
        //                    task.HopeMigrateTime = new DateTime(hopeTime.Year, hopeTime.Month, hopeTime.Day, 2, 0, 0);
        //                }
        //            }
        //            catch (Exception ex)
        //            {
        //                Log.WriteError($"判断关联店铺->获取关联店铺订单数量时发生错误，店铺ID:{shop.Id}，数据库配置：{shop.DbConfig?.ToJson()}，异常信息：{ex}");
        //            }

        //            var insertSql = $"INSERT INTO dbo.P_DataMigrateTask(SourceDbNameConfigId,TargetDbNameConfigId,ShopId) VALUES({task.SourceDbNameConfigId},{task.TargetDbNameConfigId},{task.ShopId}) SELECT @@IDENTITY AS Id";
        //            if (task.HopeMigrateTime != null)
        //                insertSql = $"INSERT INTO dbo.P_DataMigrateTask(SourceDbNameConfigId,TargetDbNameConfigId,HopeMigrateTime,ShopId) VALUES({task.SourceDbNameConfigId},{task.TargetDbNameConfigId},'{task.HopeMigrateTime.Value.ToString("yyyy-MM-dd HH:mm:ss")}',{task.ShopId}) SELECT @@IDENTITY AS Id;";
        //            Log.WriteLine($"{shop.PlatformType}店铺【{shop.Id}】迁移成功：" + insertSql);
        //            //var taskId = 1;
        //            var taskId = db.ExecuteScalar(insertSql).ToInt();
        //        }
        //    }
        //}
        public OperatorBlockSetting GetOperatorBlockSetting(int shopId)
        {
            return _repository.GetOperatorBlockSetting(shopId);
        }

        public void AddRefreshTokenLog(RefreshTokenLog model)
        {
            if (model == null)
                return;
            try
            {
                var cs = new CommonSettingService();
                var isWrite = cs.GetIsWriteRefreshTokenLog();
                if (isWrite)
                    _repository.AddRefreshTokenLog(model);
            }
            catch (Exception ex)
            {
                Log.WriteError($"添加刷新授权token时发生错误：{ex}\n{model.ToJson()}");
            }
        }

        /// <summary>
        /// 获取店铺和状态信息
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public Shop GetShopWithSyncStatus(int fxUserId, int shopId)
        {
            var shop = _repository.GetShopWithSyncStatusNew(fxUserId, shopId);

            if (shop != null)
            {
                shop.FxUserIds = fxUserId;
            }

            return shop;
        }

        /// <summary>
        /// 获取店铺
        /// </summary>
        /// <param name="fxUserId">用户ID</param>
        /// <param name="shopId">店铺ID</param>
        /// <param name="syncType"></param>
        /// <param name="isIncludeSyncStatusData">是否包含同步状态信息（默认值：包含，且同步状态没有，则店铺信息无法NULL）</param>
        /// <returns></returns>
        public Shop GetShopByFxUserWithShopId(int fxUserId, int shopId, int? syncType = 1,
            bool isIncludeSyncStatusData = true)
        {
            return _repository.GetShopByFxUserWithShopId(fxUserId, shopId, syncType, isIncludeSyncStatusData);
        }

        /// <summary>
        /// （针对抖店做了二次过滤）
        /// 抖店已全部迁到抖店云，不再需要二次过滤 2023-10-12
        /// </summary>
        /// <param name="agentIds"></param>
        /// <param name="ids"></param>
        ///  <param name="syncType">状态(1.订单同步，2.商品同步，3.商品路径流，4.拼多多跨境单同步，10.售后单同步)</param>
        /// <returns></returns>
        public List<Shop> GetShopAndSyncStatusById(IEnumerable<int> agentIds, IEnumerable<int> ids, ShopSyncType syncType)
        {
            var shops = _repository.GetShopAndSyncStatusById(agentIds, ids, syncType);
            shops?.ForEach(shop =>
            {
                shop.FxUserIds = shop.SyncStatusList.FirstOrDefault()?.FxUserId ?? 0;
            });
            //针对抖店做二次过滤
            //抖店已全部迁到抖店云，不再需要二次过滤 2023-10-12
            //shops = FilterTouTiaoShop(shops, CustomerConfig.CloudPlatformType);
            return shops;
        }

        /// <summary>
        /// 获取店铺信息 按用户ID列表，店铺ID列表（拆分查询）
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <param name="shopIds"></param>
        /// <param name="syncType"></param>
        /// <param name="isIncludeSyncStatusData"></param>
        /// <returns></returns>
        public List<Shop> GetShopsByFxUserWithShopIds(List<int> fxUserIds, List<int> shopIds, int? syncType = 1,
            bool isIncludeSyncStatusData = true)
        {
            return _repository.GetShopsByFxUserWithShopIds(fxUserIds, shopIds, syncType, isIncludeSyncStatusData);
        }

        /// <summary>
        /// 用于新线程更新未授权或订购到期店铺缓存
        /// </summary>
        /// <param name="diff"></param>
        /// <param name="platformType"></param>
        /// <returns></returns>
        public List<string> GetUnAuthShopIds(int diff, PlatformType platformType)
        {
            var key = $"/System/ShopFilterConfig/{platformType.ToString()}";
            var serviceShopIds = new List<string>();
            var shops = _repository.GetShopsByPlatformType(platformType, new List<string> { "Id", "ShopId", "CreateTime", "ExpireTime", "LastSyncTime", "LastSyncMessage" });
            if (shops == null || !shops.Any())
                serviceShopIds = new List<string>();
            else
            {
                //N天内未同步或授权已过期店铺
                var usedShops = new List<Shop>();
                shops.ForEach(x =>
                {
                    if (x.IsAuthExpired || x.LastSyncTime < DateTime.Now.AddDays(-diff) || (x.LastSyncTime == null && x.CreateTime < DateTime.Now.AddDays(-diff)))
                        serviceShopIds.Add(x.ShopId);
                    else
                        usedShops.Add(x);
                });

                //N天内有同步且授权未过期，但订购已过期店铺
                new PayService().GetShopServiceExpireTime(usedShops);
                usedShops.ForEach(s =>
                {
                    if (s.ExpireTime.IsNullOrEmpty() || s.ExpireTime.Value < DateTime.Now)
                        serviceShopIds.Add(s.ShopId);
                });
            }
            HttpRuntime.Cache.Insert(key, serviceShopIds, null, DateTime.Now.AddMinutes(15), System.Web.Caching.Cache.NoSlidingExpiration);
            return serviceShopIds;
        }

        /// <summary>
        /// 过滤N天未使用店管家的指定平台店铺Id
        /// </summary>
        /// <returns></returns>
        public List<string> GetUnAuthShopIdByCache(int diff, PlatformType platformType)
        {
            var key = $"/System/ShopFilterConfig/{platformType.ToString()}";
            var cache = HttpRuntime.Cache[key] as List<string>;
            if (cache != null)
                return cache;
            else
                cache = new List<string>();
            return cache;
        }

        public Shop GetFxSystemShopByFxId(int fxId)
        {
            return _repository.GetFxSystemShopByFxId(fxId);
        }

        /// <summary>
        /// 需要额外取P_FxUserShop字段用此方法，其他情况用GetFxSystemShopByFxId
        /// </summary>
        /// <param name="userfxIds"></param>
        /// <param name="fields">P_FxUserShop别名fus，P_Shop别名s</param>
        /// <returns></returns>
        public List<Shop> GetFxSystemShopByFxIdV1(List<int> userfxIds, string fields)
        {
            return ProcessInBatches(userfxIds, ids => _repository.GetFxSystemShopByFxIdV1(ids, fields));
        }

        public List<Shop> GetFxSystemShopByFxId(List<int> userfxIds, string fields)
        {
            return ProcessInBatches(userfxIds, ids => _repository.GetFxSystemShopByFxId(ids, fields));
        }

        /// <summary>
        /// 通过店铺LoginTokenId获取数据
        /// </summary>
        /// <param name="loginTokenId"></param>
        /// <returns></returns>
        public FxUserShop GetFxUserShopByLoginTokenId(int loginTokenId)
        {
            return _repository.GetFxUserShopByLoginTokenId(loginTokenId);
        }

        //public Tuple<bool, List<EndServiceDayModel>> GetEndService(int fxUserId,List<int> myShopIds)
        //{
        //    var endServiceDay = CustomerConfig.EndServiceDay;
        //    bool isShow = false;
        //    string endServiceDateNum = "";
        //    List<EndServiceDayModel> listModel = new List<EndServiceDayModel>();
        //    var fxUserBindAllShops = GetShopByIds(myShopIds);
        //    var fxUserPlatform = CustomerConfig.GetFxUserPlatform();
        //    try
        //    {
        //        fxUserBindAllShops.ForEach(o =>
        //        {
        //            _service = PlatformFactory.GetPlatformService(o);
        //            var time = _service.GetExpiredTime();
        //            if (!string.IsNullOrEmpty(Convert.ToString(time)))
        //            {
        //                TimeSpan leaveData = Convert.ToDateTime(time) - DateTime.Now;
        //                double datanum = leaveData.TotalDays;
        //                endServiceDateNum = ((int)Math.Ceiling(datanum)).ToString();
        //                //开始更新数据库[P_FxUserShop]的过期时间和状态
        //                if (((int)Math.Ceiling(datanum)) < 1)
        //                {
        //                    //Status  1.正常  3.过期
        //                    new FxUserShopService().UpdateShopStatus(fxUserId, o.Id, 3);
        //                }
        //                if (Convert.ToDouble(endServiceDateNum) < endServiceDay)
        //                {
        //                    var currShop = fxUserPlatform.FirstOrDefault(x => x.PlatformType == o.PlatformType);
        //                    if (!string.IsNullOrEmpty(currShop.AuthUrl))
        //                    {
        //                        listModel.Add(new EndServiceDayModel
        //                        {
        //                            ShopId = o.Id,
        //                            ShopName = o.NickName,
        //                            EndServiceDateNum = endServiceDateNum,
        //                            GetAppPayUrl = currShop.AuthUrl,
        //                            PlatformTypeName = currShop.PlatformName
        //                        });
        //                        isShow = true;
        //                    }
        //                }
        //            }
        //        });
        //    }
        //    catch (Exception ex)
        //    {
        //        isShow = false;
        //    }
        //    return Tuple.Create(isShow, listModel);
        //}

        public Shop FindShopIdByUID(string uid)
        {
            var sql = " WITH(NOLOCK) where Uid=@uid AND PlatformType='KuaiShou'";
            var shop = _repository.Get(sql, new { uid }).FirstOrDefault();
            return shop;
        }

        public Shop FindShopIdByUIDAndPlatformType(string uid, string platformType)
        {
            var sql = " WITH(NOLOCK) where Uid=@uid AND PlatformType=@platformType";
            var shop = _repository.Get(sql, new { uid, platformType }).FirstOrDefault();
            return shop;
        }

        public List<Shop> GetShopsAndShopExtension(List<int> sIds)
        {
            return _repository.GetShopsAndShopExtension(sIds);
        }

        /// <summary>
        /// 获取Shop信息以及ShopExtension授权信息
        /// </summary>
        /// <param name="sId"></param>
        /// <returns></returns>
        public Shop GetShopAndShopExtension(int sId)
        {
            return _repository.GetShopAndShopExtension(sId);
        }

        public Shop GetShopAndShopExtension(int sId, string appKey)
        {
            return _repository.GetShopAndShopExtension(sId, appKey);
        }

        //public string GetLastAccessToken(int shopid)
        //{
        //    var shop = GetShopAndShopExtension(shopid);
        //    if (shop.ShopExtension != null && shop.ShopExtension.LastRefreshTokenTime > shop.LastRefreshTokenTime)
        //        shop.AccessToken = shop.ShopExtension.AccessToken;
        //    return shop.AccessToken;
        //}

        /// <summary>
        /// 判断店铺是否存在主客铺货系统
        /// </summary>
        /// <param name="sid"></param>
        /// <returns></returns>
        public bool ShopExistsZhuKeSystem(int sid)
        {
            return _repository.ShopExistsZhuKeSystem(sid);
        }

        public Shop GetNewestShopWithShopExtension(string pt)
        {
            return _repository.GetNewestShopWithShopExtension(pt);
        }

        /// <summary>
        /// 只获取自己绑定的店铺
        /// </summary>
        /// <param name="fxuserid"></param>
        /// <returns></returns>
        public List<Shop> GetShopSelf(int fxuserid, string platformType = "")
        {
            return _repository.GetShopSelf(fxuserid, platformType);
        }

        public List<Shop> GetShopBySourceType(List<int> sids, List<string> appKeys)
        {
            return _repository.GetShopBySourceType(sids, appKeys);
        }

        /// <summary>
        /// 检查店铺P_SyncStatus,P_SyncTask是否存在，不存在新增
        /// </summary>
        /// <param name="model"></param>
        public void CheckShopSyncInfo(CheckShopSyncInfoModel model)
        {
            if (model.FxUserId <= 0 || model.ShopIds == null || !model.ShopIds.Any())
                return;

            SyncStatusService _syncStatusService = new SyncStatusService();
            SyncTaskService _syncTaskService = new SyncTaskService();

            var needInsertSyncStatus = new List<SyncStatus>();
            var needInsertSyncTask = new List<SyncTask>();
            var fxUserId = model.FxUserId;
            var dtNow = DateTime.Now;
            var shopSyncType = (ShopSyncType)model.TaskType;
            var syncTaskType = (SyncTaskType)model.TaskType;
            bool isSyncTask = false;
            if (model.TaskType == 1 || model.TaskType == 2 || model.TaskType == 10)
                isSyncTask = true;

            var checkList = _repository.QueryShopSyncInfo(model);
            if (checkList != null && checkList.Any())
            {
                //只取需要同步平台的店铺
                var needShopIds = checkList.Where(a => a.SyncType == "shop").Select(b => b.ShopId).ToList();
                var addShopIds = model.ShopIds.Where(shopId => needShopIds.Contains(shopId))?.ToList();

                addShopIds?.ForEach(shopId =>
                {
                    //在P_FxUserShop里查到的店铺才能添加
                    var existShop = checkList.FirstOrDefault(a => a.SyncType == "shop" && a.ShopId == shopId);
                    var exist = checkList.FirstOrDefault(a => a.SyncType == "status" && a.ShopId == shopId);
                    if (existShop != null && exist == null)
                    {
                        needInsertSyncStatus.Add(new SyncStatus
                        {
                            FxUserId = existShop.FxUserId,
                            ShopId = shopId,
                            SyncType = shopSyncType,
                            CreateTime = dtNow,
                            Source = OwnerSource.FenDanSystem.ToString()
                        });
                    }
                    if (existShop != null && isSyncTask)
                    {
                        exist = checkList.FirstOrDefault(a => a.SyncType == "task" && a.ShopId == shopId);
                        if (exist == null)
                        {
                            needInsertSyncTask.Add(new SyncTask
                            {
                                FxUserId = existShop.FxUserId,
                                ShopId = shopId,
                                TaskType = syncTaskType,
                                Status = SyncTaskStatus.Wait,
                                CreateTime = dtNow,
                                Source = OwnerSource.FenDanSystem.ToString()
                            });
                        }
                    }
                });
            }

            _syncStatusService.BulkInsert(needInsertSyncStatus);
            _syncTaskService.BulkInsert(needInsertSyncTask);
        }

        public int ClearFxLoginHistory(string mobile)
        {
            return _repository.ClearFxLoginHistory2(mobile);
        }

        /// <summary>
        /// 根据平台ShopId和平台类型 获取Shop信息以及ShopExtension授权信息
        /// </summary>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public Shop GetShopAndShopExtension(string ptshopId, string platformType, string appKey = "")
        {
            return _repository.GetShopAndShopExtension(ptshopId, platformType, appKey);
        }


        public Shop ChoonseApp(Shop shop)
        {
            if (shop.ShopExtension == null)
                return shop;
            //if ((shop.ShopExtension.LastRefreshTokenTime ?? DateTime.Now.AddYears(-100)) < (shop.LastRefreshTokenTime ?? DateTime.Now.AddYears(-100))) 
            //    return shop;
            shop.AppKey = shop.ShopExtension.AppKey;
            shop.AppSecret = shop.ShopExtension.AppSecret;
            shop.AccessToken = shop.ShopExtension.AccessToken;
            shop.RefreshToken = shop.ShopExtension.RefreshToken;
            return shop;
        }

        /// <summary>
        /// 对于抖音(头条)店铺, 给定ShopId获取shop和平台之间相关的参数,采取的是最后授权的app        
        /// </summary>
        /// <param name="shopid"></param>
        /// <returns>返回null表示结果无效</returns>
        public ShopAppModel ShopAboutDouYin(int shopid)
        {
            ShopAppModel shopapp = new ShopAppModel();
            shopapp.ShopId = shopid;
            var tuple = _repository.GetShopAndAllShopExtension(shopid);
            var shopinfo = tuple.Item1;
            var extentioninfo = tuple.Item2;
            if (shopinfo == null) throw new Exception("未找到店铺信息");
            if (shopinfo.PlatformType.ToLower() != "toutiao") throw new Exception("非抖音平台类型不能走抖音逻辑");
            shopapp.PlatformType = shopinfo.PlatformType;
            shopapp.PlatformShopId = shopinfo.ShopId;
            DateTime? last_auth_time = shopinfo.LastRefreshTokenTime;
            string last_auth_appkey = CustomerConfig.TouTiaoAppKey;
            if (extentioninfo != null && extentioninfo.Count > 0)
            {
                foreach (var ext in extentioninfo)
                {
                    if (last_auth_time == null)
                    {
                        last_auth_time = ext.LastRefreshTokenTime;
                        last_auth_appkey = ext.AppKey;
                    }
                    else if (ext.LastRefreshTokenTime != null)
                    {
                        if (last_auth_time.Value < ext.LastRefreshTokenTime.Value)
                        {
                            last_auth_time = ext.LastRefreshTokenTime;
                            last_auth_appkey = ext.AppKey;
                        }
                    }
                }
            }
            //查询订购记录
            var apps = _repository.GetShopValidAppService(shopid, last_auth_appkey);
            if (apps == null || apps.Count < 1)
            {
                //未找到订购记录,则一律使用新的app方式走
                shopapp.AppKey = CustomerConfig.TouTiaoFxNewAppKey;
                shopapp.AuthTime = null;
                shopapp.AuthUrl = "https://auth.dgjapp.com/auth/douyinfxnew?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3";
                shopapp.PayUrl = $"https://fuwu.jinritemai.com/detail?service_id=24069&from=isv.detail";
                shopapp.OldOrNew = "New";
                return shopapp;
            }
            shopapp.AppKey = last_auth_appkey;
            shopapp.AuthUrl = "https://auth.dgjapp.com/auth/douyin?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3";
            foreach (var app in apps)
            {
                if (shopapp.AuthTime == null)
                {
                    shopapp.AuthTime = app.ServiceEnd;
                }
                else if (app.ServiceEnd.Value > shopapp.AuthTime.Value)
                {
                    shopapp.AuthTime = app.ServiceEnd;
                }
            }
            string serviceid = "24069";
            if (shopapp.AppKey == CustomerConfig.TouTiaoFxNewAppKey)
            {
                serviceid = "24069";
                shopapp.AuthUrl = "https://auth.dgjapp.com/auth/douyinfxnew?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3";
            }
            else if (shopapp.AppKey == CustomerConfig.TouTiaoFxAppKey)
            {
                serviceid = "19552";
                shopapp.AuthUrl = "https://auth.dgjapp.com/auth/douyinfx?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3";
            }
            else if (shopapp.AppKey == CustomerConfig.TouTiaoAppKey)
            {
                serviceid = "7";
                shopapp.AuthUrl = "https://auth.dgjapp.com/auth/douyin?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3";
            }
            shopapp.PayUrl = $"https://fuwu.jinritemai.com/detail?service_id=24069&from=isv.detail";
            if (serviceid == "24069")
            {
                shopapp.OldOrNew = "New";
            }
            else if (shopapp.AuthTime > DateTime.Now && shopapp.AuthTime < DateTime.Now.AddDays(1))
            {
                shopapp.OldOrNew = "Old";
            }
            else if (shopapp.AuthTime < DateTime.Now)
            {
                shopapp.OldOrNew = "New";
            }
            else
            {
                shopapp.PayUrl = "";
            }
            //if (CustomerConfig.IsDebug)
            //{                
            //    shopapp.AuthUrl = shopapp.AuthUrl.Replace("https://auth.dgjapp.com", "https://testauth1.dgjapp.com");
            //}
            return shopapp;

        }

        /// <summary>
        /// 通过ShopId获取FxUserId
        /// </summary>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public int GetFxUserIdByShopId(int shopId,bool useCache = true)
        {
            return _repository.GetFxUserIdByShopId(shopId,useCache);

        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="maxId"></param>
        /// <param name="topNum"></param>
        /// <returns></returns>
        public List<PhShop> GetListByMaxId(int maxId, int topNum)
        {
            return _repository.GetListByMaxId(maxId, topNum);
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="shopIds"></param>
        /// <returns></returns>
        public List<Shop> GetListByShopIds(List<int> shopIds)
        {
            return _repository.GetListByShopIds(shopIds);
        }

        public List<Shop> GetFxShopListByShopIds(List<int> shopIds)
        {
            return _repository.GetFxShopListByShopIds(shopIds);
        }

        /// <summary>
        /// 获取跨境相关
        /// </summary>
        /// <param name="shopIds"></param>
        /// <returns></returns>
        public List<Shop> GetCrossBorderFxShopListByShopIds(List<int> shopIds)
        {
            return _repository.GetCrossBorderFxShopListByShopIds(shopIds);
        }

        /// <summary>
        /// 获取跨境全球店铺
        /// </summary>
        /// <param name="shopIds"></param>
        /// <returns></returns>
        public Shop GetGlobalShop(string shopId)
        {
            return _repository.GetGlobalShop(shopId);
        }



        public void TestTraceCollect()
        {
            var service = new TraceDataCollectionService();
            var lists = new List<TraceDataModel<TraceDataMetaDataModel>>();
            for (var i = 0; i < 100; i++)
            {
                lists.Add(new TraceDataModel<TraceDataMetaDataModel>
                {
                    BatchId = Guid.NewGuid().ToString(),
                    BusinessId = $"PO{i}",
                    CreateTime = DateTime.Now,
                    CloudPlatformType = "Alibaba",
                    PlatformType = "TouTiao",
                    OperationType = "InvokeOrderApi",
                    ShopId = 259,
                    MetaData = new TraceDataMetaDataModel()
                });
            }
            var returned = service.Collect(lists);
        }

        /// <summary>
        /// <param name="pageIndex"></param>
        /// <param name="platformType"></param>
        /// <returns></returns>
        public List<FxUserIdAndShopId> GetFxUserIdAndShopId(int pageSize, int pageIndex, string platformType, int maxId)
        {
            return _repository.GetFxUserIdAndShopId(pageSize, pageIndex, platformType, maxId);
        }

        /// <summary>
        /// 查出用户Id+所在源库Id（表WaitMigrateTouTiaoShop）
        /// </summary>
        /// <param name="pageSize"></param>
        /// <param name="pageIndex"></param>
        /// <param name="maxId"></param>
        /// <returns></returns>
        public List<FxUserIdAndShopId> GetFxUserIdInWaitMigrateTouTiaoShop(int pageSize, int pageIndex, int maxId)
        {
            return _repository.GetFxUserIdInWaitMigrateTouTiaoShop(pageSize, pageIndex, maxId);
        }


        /// <summary>
        ///  查询是否有对应的应用店铺授权
        /// </summary>
        /// <returns></returns>
        public bool GetAppShopAuth(int fxUserId, int ShopId, List<string> appkeys)
        {
            return _repository.GetAppShopAuth(fxUserId, ShopId, appkeys);
        }

        /// <summary>
        ///  查询是否开通应用的店铺
        /// </summary>
        /// <returns></returns>
        public bool GetOpenAppShops(int fxUserId, string platformType, List<string> appkeys)
        {
            return _repository.GetOpenAppShops(fxUserId, platformType, appkeys);
        }


        /// <summary>
        /// 获取阿里店铺开通电子面单情况
        /// </summary>
        /// <param name="loginFxShopId"></param>
        /// <param name="platformType"></param>
        /// <returns></returns>
        public List<AliEbillOpenStatus> GetAliOpenEbillInfo(int loginFxShopId, string platformType)
        {
            return _repository.GetAliOpenEbillInfo(loginFxShopId, platformType);
        }

        /// <summary>
        /// 获取店铺信息及多应用授权&校验授权
        /// </summary>
        /// <param name="sIds"></param>
        /// <param name="isCheckAuth">是否校验授权&同步服务到期时间</param>
        /// <param name="isProcessPddShopExtension">是否处理拼多多授权扩展，默认true</param>
        /// <returns></returns>
        public List<Shop> GetShopsAndShopExtensionFunc(List<int> sIds, bool isCheckAuth = true, bool isProcessPddShopExtension = true)
        {
            //切一层做其他处理，是否校验授权&同步服务到期时间
            var shops = _repository.GetShopsAndShopExtensionFunc(sIds, (dbShops) =>
            {
                if (isCheckAuth)
                {
                    var whereDbShops = dbShops.Where(w => w.PlatformType != PlatformType.Virtual.ToString() && w.PlatformType != PlatformType.System.ToString());
                    Parallel.ForEach(whereDbShops, new ParallelOptions { MaxDegreeOfParallelism = 10 }, (dbShop) =>
                    {
                        try
                        {
                            dbShop.SyncType = "NotUpdateLastSyncMessage"; //防止平台类内部修改LastSyncMessage
                            var _platformService = PlatformFactory.GetPlatformServiceByShop(dbShop);
                            var isOk = _platformService.Ping();    //检查授权
                            if (!isOk)
                            {
                                throw new LogicException($"店铺【{dbShop.NickName}】授权已过期，请从店铺后台重新进入，或重新授权。", "auth_expires");
                            }
                            else
                            {
                                dbShop.LastSyncMessage = null;
                            }

                            if (!string.IsNullOrWhiteSpace(dbShop.PayUrl))
                            {
                                var apiTime = _platformService.GetExpiredTime();
                                if (apiTime != null)
                                {
                                    if (dbShop.ShopExtension != null)
                                    {
                                        dbShop.ShopExtension.ExpireTime = apiTime;
                                        dbShop.ShopExtension.LastRefreshExpireTime = DateTime.Now;
                                    }
                                    else
                                        dbShop.ExpireTime = apiTime;
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            if (ex.Message.Contains("授权过期") || ex.Message.Contains("授权已过期"))
                            {
                                dbShop.LastSyncMessage = ex.Message;
                            }
                        }
                    });
                }
                return true;
            }, isProcessPddShopExtension);
            return shops;
        }

        /// <summary>
        /// 手动同步店铺的服务到期时间
        /// </summary>
        /// <param name="shopId"></param>
        /// <param name="appKey"></param>
        /// <returns></returns>
        /// <exception cref="Exception">请求获取最新的过期时间异常，</exception>
        public DateTime? HandSyncShopExpireTime(int shopId, string appKey)
        {
            Shop needSyncShop = null;
            if (string.IsNullOrWhiteSpace(appKey))
                needSyncShop = _repository.Get(shopId);
            else
                needSyncShop = _repository.GetShopAndShopExtension(shopId, appKey);

            if (needSyncShop == null)
            {
                throw new LogicException("未找到该店铺授权信息");
            }
            var oldExpireTime = needSyncShop.ShopExtension == null ? needSyncShop.ExpireTime : needSyncShop.ShopExtension.ExpireTime;
            if (needSyncShop.ShopExtension != null)
                needSyncShop.ExpireTime = needSyncShop.ShopExtension.ExpireTime;
            try
            {
                needSyncShop.IsSyncServierTime = true;  //强制请求接口
                needSyncShop.SyncType = "NotUpdateLastSyncMessage"; //防止平台类内部修改LastSyncMessage
                var _platformService = PlatformFactory.GetPlatformServiceByShop(needSyncShop);

                var needPingPt = new List<string>() { "toutiao", "jingdong", "kuaishou", "taobao" };
                if (needPingPt.Contains(needSyncShop.PlatformType.ToLower()))
                {
                    //预检查
                    var isOk = _platformService.Ping();
                    if (!isOk)
                    {
                        throw new LogicException($"店铺【{needSyncShop.NickName}】授权已过期，请从店铺后台重新进入，或重新授权。", "auth_expires");
                    }
                }

                oldExpireTime = _platformService.GetExpiredTime(); //获取最新时间
            }
            catch (LogicException lex)
            {
                //提示过期，重新通过订购记录计算一下到期时间
                RemedyShopExpireTime(needSyncShop);
                Log.WriteError($"执行HandSyncShopExpireTime失败，参数{shopId}|{appKey}，逻辑异常：{lex}");
                throw lex;
            }
            catch (Exception ex)
            {
                Log.WriteError($"执行HandSyncShopExpireTime失败，参数{shopId}|{appKey}，系统异常：{ex}");
                throw ex;
            }
            return oldExpireTime;
        }

        /// <summary>
        /// 获取用户版本信息
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <returns></returns>
        public List<FxUserSystemVersionModel> GetFxUserSystemVersions(List<int> fxUserIds)
        {
            //判空处理
            if (fxUserIds == null || !fxUserIds.Any())
            {
                return new List<FxUserSystemVersionModel>();
            }
            //返回
            return _repository.GetFxUserSystemVersions(fxUserIds);
        }
        /// <summary>
        /// 通过订购记录更新过期时间 (兜底方案)
        /// 订购记录不全/不准确的话就会导致过期时间有问题
        /// </summary>
        /// <param name="needRemedyShop"></param>
        /// <returns></returns>
        private void RemedyShopExpireTime(Shop needRemedyShop)
        {
            /* 先开放几个平台，其他平台后续看情况，开放一个平台需要研究一下订购记录数据结构
             * 1.淘宝&京东只有一个应用，不需要通过订购记录来补救
             * 2.dbo.AppOrderList表存储了，分单拼多多，主客阿里，除了前面两个应用这个表存储一些以前的老应用，后面新应用都存在下面的表
             * 3.dbo.ServiceAppOrder表存储了，分单阿里，打单/分单/主客头条，快手等平台
             */
            var needRemedyPts = new List<string>() {
                PlatformType.TouTiao.ToString(),
                PlatformType.TouTiaoSaleShop.ToString(),
                PlatformType.KuaiShou.ToString(),
                PlatformType.Alibaba.ToString()
            };

            if (needRemedyShop.ShopExtension != null && needRemedyPts.Contains(needRemedyShop.PlatformType))
            {
                try
                {
                    // 只需要处理ShopExtension 有授权情况， 
                    var appService = new AppOrderListService();
                    var userService = new UserService();
                    var shopExtensionService = new ShopExtensionService();

                    var shopId = needRemedyShop.Id;
                    var ptShopId = needRemedyShop.ShopId;
                    var platformType = needRemedyShop.PlatformType;
                    var appkey = needRemedyShop.ShopExtension.AppKey;
                    var oldExpireTime = needRemedyShop.ShopExtension.ExpireTime;
                    DateTime? newExpireTime = null;
                    if (platformType == PlatformType.Alibaba.ToString())
                    {
                        var lastOrder = appService.GetLastEndOrdersWhere(appkey, ptShopId);
                        if (lastOrder != null)
                            newExpireTime = lastOrder.GmtServiceEnd;
                    }
                    else
                    {
                        if (platformType == PlatformType.KuaiShou.ToString())
                            ptShopId = needRemedyShop.Uid;

                        var lastOrder = userService.GetLastEndOrdersWhere(appkey, ptShopId);
                        if (lastOrder != null)
                            newExpireTime = lastOrder.ServiceEnd.Value;
                    }

                    if (oldExpireTime == null && newExpireTime != null)
                        shopExtensionService.UpdateExpireTime(appkey, shopId, newExpireTime.Value);
                    else if (newExpireTime != null && newExpireTime != oldExpireTime)
                        shopExtensionService.UpdateExpireTime(appkey, shopId, newExpireTime.Value);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"RemedyShopExpireTime处理失败异常信息：{ex}");
                }
            }
        }

        /// <summary>
        /// 修改店铺名称
        /// </summary>
        /// <param name="shopId"></param>
        /// <param name="shopName"></param>
        public void UpdateShopName(int shopId, string shopName)
        {
            _repository.UpdateShopName(shopId, shopName);
        }

        /// <summary>
        /// 获取开放平台店铺
        /// </summary>
        /// <param name="shopId"></param>
        /// <param name="appKey"></param>
        /// <returns></returns>
        public Shop GetOpenPlatformShop(string shopId, string appKey)
        {
            return _repository.GetShopAndShopExtension(shopId, PlatformType.OwnShop.ToString(), appKey);
        }

        /// <summary>
        /// 获取开放平台店铺
        /// </summary>
        /// <param name="shopId"></param>
        /// <param name="appKey"></param>
        /// <returns></returns>
        public Shop GetOpenPlatformShop(int id, string appKey)
        {
            return _repository.GetShopAndShopExtension(id, PlatformType.OwnShop.ToString(), appKey);
        }

        public List<Shop> GetOpenPlatformShops(int fxUserId)
        {
            return _repository.GetOpenPlatformShops(fxUserId);
        }

        /// <summary>
        /// 获取店铺
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="shopIds"></param>
        /// <param name="platformType"></param>
        /// <returns></returns>
        public List<Shop> GetShops(int fxUserId, List<int> shopIds, string platformType)
        {
            return _repository.GetShops(fxUserId, shopIds, platformType);
        }

        /// <summary>
        /// 获取授权没有过期的店铺
        /// </summary>
        /// <returns></returns>
        public List<Shop> GetShopNotExpTokenTouTiao()
        {
            var shopList = _repository.GetShopNotExpTokenTouTiao(CustomerConfig.TouTiaoFxListingAppKey);
            if (!shopList.Any())
            {
                shopList = _repository.GetShopNotExpTokenTouTiao(CustomerConfig.TouTiaoFxAppKey);
            }

            foreach (var shop in shopList)
            {
                // 这里扩展信息提前通过sql查询映射到 Shop 中，赋值后获取平台类时无需再次查询扩展表
                shop.ShopExtension = new ShopExtension()
                {
                    AppKey = shop.AppKey,
                    AppSecret = shop.AppSecret,
                    AccessToken = shop.AccessToken,
                    RefreshToken = shop.RefreshToken,
                    LastRefreshTokenTime = shop.LastRefreshTokenTime.Value
                };
            }

            return shopList;
        }

        public List<Shop> GetShopNotExpTokenTouTiaoByFxUser(int fxuserid)
        {
            bool isdd = CustomerConfig.CloudPlatformType == "TouTiao";
            var shopList = _repository.GetShopNotExpTokenTouTiaoByFxUser(CustomerConfig.TouTiaoFxListingAppKey, fxuserid, isdd);
            foreach (var shop in shopList)
            {
                // 这里扩展信息提前通过sql查询映射到 Shop 中，赋值后获取平台类时无需再次查询扩展表
                shop.ShopExtension = new ShopExtension()
                {
                    AppKey = shop.AppKey,
                    AppSecret = shop.AppSecret,
                    AccessToken = shop.AccessToken,
                    RefreshToken = shop.RefreshToken,
                    LastRefreshTokenTime = shop.LastRefreshTokenTime.Value
                };
            }

            return shopList;
        }

        /// <summary>
        /// 获取头条的店铺
        /// </summary>
        /// <param name="shopids"></param>
        /// <returns></returns>
        public List<Shop> GetTouTiaoShopByIds(params int[] shopids)
        {
            var shopList = _repository.GetTouTiaoShopByIds(shopids);
            foreach (var shop in shopList)
            {
                // 这里扩展信息提前通过sql查询映射到 Shop 中，赋值后获取平台类时无需再次查询扩展表
                shop.ShopExtension = new ShopExtension()
                {
                    AppKey = shop.AppKey,
                    AppSecret = shop.AppSecret,
                    AccessToken = shop.AccessToken,
                    RefreshToken = shop.RefreshToken,
                    LastRefreshTokenTime = shop.LastRefreshTokenTime.Value
                };
            }

            return shopList;
        }

        #region
        public List<Shop> GetListByParentShopId(string parentShopId, string platformType)
        {
            return _repository.GetListByParentShopId(parentShopId, platformType);
        }
        
        /// <summary>
        /// 根据主店铺Id获取站点店铺
        /// </summary>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public List<Shop> GetSiteShopsByManShopId(int fxUserId, int shopId, string pt)
        {
            var manShop = _repository.GetCrossBorderShops(fxUserId, shopId);
            if (manShop == null)
                return new List<Shop>();
            return GetListByParentShopId(manShop.ShopId, pt);
        }

        /// <summary>
        /// 跨境的店铺根据ShopId查询
        /// </summary>
        /// <param name="shopIds"></param>
        /// <param name="strField"></param>
        /// <returns></returns>
        public List<Shop> GetTkUserIdByShopId(List<int> shopIds, string strField = "*")
        {
            ShopService shopService = new ShopService();
            List<Shop> shops = _repository.GetShopByIds(shopIds, strField);
            List<Shop> result = new List<Shop>();
            foreach (var shop in shops)
            {
                //只取全球店铺
                if (string.IsNullOrEmpty(shop.ParentShopId))
                {
                    result.Add(shop);
                }
                else
                {
                    Shop parentShop = shopService.GetGlobalShop(shop.ParentShopId);
                    if (parentShop != null)
                    {
                        result.Add(parentShop);
                    }
                }
            }
            //Id去重
            result = result.GroupBy(s => s.Id).Select(g => g.First()).ToList();
            return result;
        }

        /// <summary>
        /// 列表查询获取跨境的全部shop信息，不过滤站点店铺
        /// </summary>
        /// <param name="shopIds"></param>
        /// <param name="strField"></param>
        /// <returns></returns>
        public List<Shop> GetAllTkUserIdByShopId(List<int> shopIds, string strField = "*")
        {
            if (shopIds == null || shopIds.Any() == false)
                return null;
            return _repository.GetShopByIds(shopIds, strField);
        }
        #endregion

        /// <summary>
        /// 检查店铺推送库授权状态
        /// </summary>
        public void CheckShopPushDbAuth(PushDbAuthCheckMessageModel messageModel, string platformType)
        {
            try
            {
                Log.WriteLine($"开始检测店铺推送库授权状态:{messageModel.ToJson()}，platformType：{platformType}", "CheckShopPushDbAuth.txt");

                var shopInfo = GetShopById(messageModel.ShopId);
                if (shopInfo == null)
                {
                    Log.WriteLine($"未查询到店铺信息", "CheckShopPushDbAuth.txt");
                    return;
                }
                if (shopInfo.PlatformType != platformType)
                {
                    Log.WriteLine($"查询店铺的平台类型与缓存Key的类型不一致，店铺信息：{shopInfo.ToJson()}", "CheckShopPushDbAuth.txt");
                    return;
                }

                if (!Enum.TryParse<PlatformType>(shopInfo.PlatformType, out var platformTypeResult))
                {
                    Log.WriteLine($"店铺的平台类型转换枚举失败，店铺信息：{shopInfo.ToJson()}", "CheckShopPushDbAuth.txt");
                    return;
                }
                switch (platformTypeResult)
                {
                    case PlatformType.TouTiao:
                        CheckPushDbByToutiao(shopInfo);
                        break;
                    case PlatformType.Taobao:
                        CheckPushDbByTaobao(shopInfo);
                        break;
                    default: return;
                }
            }
            catch (Exception e)
            {
                Log.WriteError($"检查店铺推送库授权状态出现异常：{messageModel.ToJson()},ex:{e}");
            }
        }

        /// <summary>
        /// 抖店推送库检查
        /// </summary>
        /// <param name="shopInfo"></param>
        private void CheckPushDbByToutiao(Shop shopInfo)
        {
            if (CustomerConfig.CloudPlatformType != PlatformType.TouTiao.ToString())
            {
                Log.WriteLine($"抖店平台只能在抖店云处理，当前平台：{CustomerConfig.CloudPlatformType}", "CheckShopPushDbAuth.txt");
                return;
            }

            var platformService = new ZhiDianNewPlatformService(shopInfo);

            //查询推送库数据
            var pushDbInfoApi = platformService.QueryPushDbInfo();
            if (pushDbInfoApi == null)
            {
                //平台推送库不存在，不处理
                Log.WriteLine($"未查询到店铺的平台推送库:{shopInfo.ToJson()}", "CheckShopPushDbAuth.txt");
                return;
            }

            var pushDbKey = $"/TouTiao/PushDb/{shopInfo.AppKey}";
            var openTimeKey = $"/TouTiao/PushDb/OpenTime/{shopInfo.AppKey}";

            //店铺推送库授权配置
            var systemPushDbSettingValue = _commonSettingService.GetString(pushDbKey, shopInfo.Id);

            //店铺未配置推送库，则添加到系统配置，并初始化推送库开启时间
            if (string.IsNullOrWhiteSpace(systemPushDbSettingValue))
            {
                //添加到系统配置
                _commonSettingService.Set(pushDbKey, pushDbInfoApi.RdsId, shopInfo.Id);

                //初始化推送库开启时间,初始化前需要检查一下店铺ID是否存在记录，存在则需要比对一下时间，数据库时间大于检测时间，则不设置，小于或不存在则初始化推送库开启时间
                var openTimeValue = _commonSettingService.GetString(openTimeKey, shopInfo.Id);
                if (string.IsNullOrWhiteSpace(openTimeValue) || openTimeValue.toDateTime() < DateTime.Now)
                {
                    _commonSettingService.Set(openTimeKey, DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), shopInfo.Id);
                }

                Log.WriteLine($"店铺未配置推送库，则添加到系统配置，并初始化推送库开启时间：rdsID={pushDbInfoApi.RdsId}", "CheckShopPushDbAuth.txt");
            }
            else
            {
                //如果平台推送库授权存在，系统店铺推送库授权配置也存在，推送库 RdsID 相同，则退出检测，不做处理。
                if (systemPushDbSettingValue == pushDbInfoApi.RdsId)
                {
                    Log.WriteLine($"推送库 RdsID 相同：rdsID={pushDbInfoApi.RdsId}", "CheckShopPushDbAuth.txt");
                    return;
                }

                //推送库 RdsID 不相同，则以平台RdsId 为准，更新系统推送库授权配置，重新初始化推送库开启时间为检测时间，同时需要将该店铺最后同步时间减15天。
                _commonSettingService.Set(pushDbKey, pushDbInfoApi.RdsId, shopInfo.Id);

                //初始化推送库开启时间,初始化前需要检查一下店铺ID是否存在记录，存在则需要比对一下时间，数据库时间大于检测时间，则不设置，小于则设置成检测时间
                var openTimeValue = _commonSettingService.GetString(openTimeKey, shopInfo.Id);
                if (string.IsNullOrWhiteSpace(openTimeValue) || openTimeValue.toDateTime() < DateTime.Now)
                {
                    _commonSettingService.Set(openTimeKey, DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), shopInfo.Id);
                }

                //店铺最后同步时间减15天
                var syncStatusService = new SyncStatusService();
                //最后同步时间减15天
                syncStatusService.UpdateLastSyncTime(shopInfo.Id, DateTime.Now.AddDays(-15));
                Log.WriteLine($"推送库 RdsID 不相同：rdsID={pushDbInfoApi.RdsId}", "CheckShopPushDbAuth.txt");
            }
        }

        /// <summary>
        /// 获取淘宝推送库配置 按 RdsName
        /// </summary>
        /// <param name="rdsName"></param>
        /// <returns></returns>
        private TaobaoPushDbSetting GetTaobaoPushDbSettingByRdsName(string rdsName)
        {
            switch (rdsName)
            {
                case "jrdsitvmm3nj":
                    return new TaobaoPushDbSetting
                    {
                        ConfigName = "TaobaoSynData",
                        RdsName = rdsName,
                        DbType = "SqlServer"
                    };
                case "rm-k2j8b2e5w0730vz46":
                    return new TaobaoPushDbSetting
                    {
                        ConfigName = "TaobaoMysqlPushDb",
                        RdsName = rdsName,
                        DbType = "MySql"
                    };
                case "rm-k2j7u9o64j3yzllk1":
                    return new TaobaoPushDbSetting
                    {
                        ConfigName = "TaobaoMysqlPushDb2",
                        RdsName = rdsName,
                        DbType = "MySql"
                    };
                default:
                    var defaultConfig =
                        _commonSettingService.Get<TaobaoPushDbSetting>("/System/Config/Taobao/DefaultPushDbName", -1);
                    defaultConfig.RdsName = rdsName;
                    return defaultConfig;
            }
        }

        /// <summary>
        /// 淘宝推送库检查
        /// </summary>
        /// <param name="shopInfo"></param>
        private void CheckPushDbByTaobao(Shop shopInfo)
        {
            var logName = $"CheckShopPushDbAuthByTaobao-{DateTime.Now:yyyy-MM-dd}.log";
            if (CustomerConfig.CloudPlatformType != CloudPlatformType.Alibaba.ToString())
            {
                Log.WriteLine($"淘宝平台只能在精选云处理，当前平台：{CustomerConfig.CloudPlatformType}", logName);
                return;
            }

            var platformService = new TaobaoPlatformService(shopInfo);
            // 检查系统中店铺名称和平台店铺接口店铺名称是否一致，不一致，取平台接口店铺名称，更新到系统
            var systemShopName = shopInfo.ShopName;
            var user = platformService.GetUserSellerInfo();
            if (user == null)
            {
                Log.WriteLine($"店铺ID：{shopInfo.Id}，获取平台店铺信息为空，不做处理", logName);
                return;
            }
            var platformShopName = user.Nick;
            //同步状态
            var syncStatusService = new SyncStatusService();
            //日志
            Log.WriteLine($"店铺ID：{shopInfo.Id}，检测店铺名称，系统店铺名称：{systemShopName}，平台店铺名称：{platformShopName}", logName);
            if (string.IsNullOrWhiteSpace(platformShopName) == false && systemShopName != platformShopName)
            {
                shopInfo.ShopId = platformShopName;
                shopInfo.ShopName = platformShopName;
                shopInfo.NickName = platformShopName;
                //更新店铺名称
                new ExceptionHandler().ExceptionRetryHandler(() =>
                {
                    _repository.UpdateShop(shopInfo.Id,
                        new Dictionary<string, string>
                        {
                            { "ShopName", platformShopName },
                            { "NickName", platformShopName },
                            { "ShopId", platformShopName }
                        });
                    return true;
                }, true);
                //更新用户店铺关系
                new FxUserShopService().UpdateNickName(
                    new Dictionary<int, string> { { shopInfo.Id, platformShopName } });

                //最后同步时间减7天
                syncStatusService.UpdateLastSyncTime(shopInfo.Id, DateTime.Now.AddDays(-15));
            }
            // 检查淘宝平台推送库授权
            const string pushKey = "/System/Config/Taobao/DefaultPushDbName";
            TaobaoPushDbSetting pushDbValue = null;
            try
            {
                pushDbValue = _commonSettingService.Get<TaobaoPushDbSetting>(pushKey, shopInfo.Id);
            }
            catch (Exception e)
            {

            }
            //平台推送库RDS
            var platformPushDbRds = platformService.GetJushitaRdsName();
            //系统推送库RDS
            var systemPushDbRds = pushDbValue?.RdsName;
            //日志
            Log.WriteLine($"店铺ID：{shopInfo.Id}，检测推送库，系统推送库：{systemPushDbRds}，平台推送库：{platformPushDbRds}", logName);
            // 如果平台推送库授权不存在，但是系统存在淘宝推送库配置，则需要调用平台推送库授权接口授权推送库，将系统推送库配置添加到平台
            if (string.IsNullOrWhiteSpace(platformPushDbRds) && string.IsNullOrWhiteSpace(systemPushDbRds) == false)
            {
                // 调用平台推送库授权接口授权推送库
                var result = platformService.PushToJushita(systemPushDbRds);
                if (result == false)
                {
                    //设置成百年以后
                    _commonSettingService.Set(SystemSettingKeys.TaobaoPushDbOpenTimeKey,
                        DateTime.Now.AddYears(200).ToString("yyyy-MM-dd HH:mm:ss"), shopInfo.Id);
                    //最后同步时间减15天
                    syncStatusService.UpdateLastSyncTime(shopInfo.Id, DateTime.Now.AddDays(-15));
                }
                Log.WriteLine($"店铺ID：{shopInfo.Id}，平台推送库授权为空，添加系统推送库授权到平台，推送库：{systemPushDbRds}，授权结果：{result}", logName);
            }
            // 店铺推送库为空，平台推送库不为空，更新系统推送库授权配置
            else if (string.IsNullOrWhiteSpace(systemPushDbRds) && string.IsNullOrWhiteSpace(platformPushDbRds) == false)
            {
                Log.WriteLine($"店铺ID：{shopInfo.Id}，系统推送库授权为空，保存平台推送库授权到系统，推送库：{platformPushDbRds}", logName);
                var platformPushDb = GetTaobaoPushDbSettingByRdsName(platformPushDbRds);
                _commonSettingService.Set(pushKey, platformPushDb.ToJson(), shopInfo.Id);
                var openTimeValue = _commonSettingService.GetString(SystemSettingKeys.TaobaoPushDbOpenTimeKey, shopInfo.Id);
                // 当前时间+2小时
                var time = DateTime.Now.AddHours(2).ToString("yyyy-MM-dd HH:mm:ss");
                if (string.IsNullOrWhiteSpace(openTimeValue) || openTimeValue.toDateTime() < DateTime.Now.AddHours(2))
                {
                    _commonSettingService.Set(SystemSettingKeys.TaobaoPushDbOpenTimeKey, time, shopInfo.Id);
                }
            }
            // 如果平台推送库不存在，系统推送库配置也不存在，则获取默认推送库配置
            // 授权到平台，需要验证是否授权成功（重新查询一次平台推送库接口）
            // 成功则添加到系统推送库配置，并初始化开通推送库时间。否则不做处理
            else if (string.IsNullOrWhiteSpace(platformPushDbRds) && string.IsNullOrWhiteSpace(systemPushDbRds))
            {
                // 默认推送库配置
                var defaultPushDbValue = _commonSettingService.Get<TaobaoPushDbSetting>(pushKey, -1);
                if (defaultPushDbValue == null)
                {
                    Log.WriteLine($"推送库授权失败，店铺ID：{shopInfo.Id}，店铺名称：{shopInfo.ShopName}，未获取到默认推送库配置", logName);
                    return;
                }
                //日志
                Log.WriteLine($"店铺ID：{shopInfo.Id}，系统推送库授权为空，平台推送库授权也为空，添加默认推送库授权到平台，推送库：{defaultPushDbValue}",
                    logName);
                // 调用平台推送库授权接口授权推送库
                platformService.PushToJushita(defaultPushDbValue.RdsName);
                var rdsName = platformService.GetJushitaRdsName();
                if (string.IsNullOrWhiteSpace(rdsName) == false && rdsName == defaultPushDbValue.RdsName)
                {
                    // 授权成功，添加到系统推送库配置，并初始化开通推送库时间
                    _commonSettingService.Set(pushKey, defaultPushDbValue.ToJson(), shopInfo.Id);
                    _commonSettingService.Set(SystemSettingKeys.TaobaoPushDbOpenTimeKey, DateTime.Now.AddHours(2).ToString("yyyy-MM-dd HH:mm:ss"), shopInfo.Id);
                    //店铺最后同步时间减2天
                    syncStatusService.UpdateLastSyncTime(shopInfo.Id, DateTime.Now.AddDays(-2));
                }
                else
                {
                    Log.WriteLine($"添加默认推送库授权到平台失败，店铺ID：{shopInfo.Id}，店铺名称：{shopInfo.ShopName}", logName);
                }
            }
            else if (string.IsNullOrWhiteSpace(platformPushDbRds) == false &&
                     string.IsNullOrWhiteSpace(systemPushDbRds) == false &&
                     string.Equals(systemPushDbRds, platformPushDbRds) == false)
            {
                //平台推送库存在，系统推送库存在，但是不相同，则起平台推送库为准
                //日志
                Log.WriteLine($"店铺ID：{shopInfo.Id}，检测推送库，系统推送库：{systemPushDbRds} 与 平台推送库：{platformPushDbRds} 不同", logName);
                var platformPushDb = GetTaobaoPushDbSettingByRdsName(platformPushDbRds);
                _commonSettingService.Set(pushKey, platformPushDb.ToJson(), shopInfo.Id);
                var openTimeValue = _commonSettingService.GetString(SystemSettingKeys.TaobaoPushDbOpenTimeKey, shopInfo.Id);
                // 当前时间+2小时
                var time = DateTime.Now.AddHours(2).ToString("yyyy-MM-dd HH:mm:ss");
                if (string.IsNullOrWhiteSpace(openTimeValue) || openTimeValue.toDateTime() < DateTime.Now.AddHours(2))
                {
                    _commonSettingService.Set(SystemSettingKeys.TaobaoPushDbOpenTimeKey, time, shopInfo.Id);
                }
            }
        }

        /// <summary>
        /// 通过商品Id查询源商家Id，并前推商品所在店铺的上次同步完成时间
        /// </summary>
        /// <param name="lastProductIdList">商品Id，未查询到的，会返回</param>
        /// <param name="agentId">上游商家Id，不一定是商品的源商家，但保证此商家也在商品的路径流中</param>
        /// <param name="day">上次同步完成时间需要前推的天数</param>
        /// <returns>Item1：失败的原因列表，Item2：源商家的系统店铺Id列表</returns>

        public Tuple<List<string>,List<int>> GetAgentIdsByProductIds(ref List<string> lastProductIdList,int agentId,int day)
        {
            var failedReasons = new HashSet<string>();
            var fxUserId = SiteContext.Current.CurrentFxUserId;

            Log.Debug(() => $"{fxUserId}用户通过商品Id查询源商家Id，开始遍历用户相关库", ModuleFileName.ManualSyncOrder);

            // 遍历用户相关库以查询商品数据
            var connectionStringList = SiteContext.Current.CurrentDbAreaConfig.Select(c => c.ConnectionString).Distinct().ToList();
            var tempProducts = new List<ProductFx>();
            foreach (var connectionStr in connectionStringList)
            {
                Log.Debug(() => $"{fxUserId}用户通过商品Id查询源商家Id，用户库连接字符串：{connectionStr}", ModuleFileName.ManualSyncOrder);

                tempProducts.AddRange(new ProductFxService(connectionStr).GetProductsForAgentSync(lastProductIdList, fxUserId, agentId));
                var tempPlatformIds = tempProducts.Select(p => p.PlatformId).Distinct().ToList();
                lastProductIdList = lastProductIdList.Except(tempPlatformIds).ToList();
                if (lastProductIdList.IsNullOrEmptyList())
                    break;
            }
            Log.Debug(() => $"{fxUserId}用户通过商品Id查询源商家Id，查询出的商品：{tempProducts.ToJson()}", ModuleFileName.ManualSyncOrder);


            // 商品的源店铺和源商家，这些店铺需要前推同步时间
            var tempShopAndAgentDict = tempProducts.GroupBy(p => p.ShopId)
                .ToDictionary(p => p.Key, p => p.FirstOrDefault()?.SourceUserId ?? 0);
            var tempShopIdList = tempShopAndAgentDict.Keys.ToList();
            // 商品的源商家
            var tempAgentId = tempShopAndAgentDict.Select(d => d.Value).ToList();

            // 查询店铺及其同步状态
            var tempAgentShops = GetShopAndSyncStatusById(tempAgentId, tempShopIdList, ShopSyncType.Order).ToList();
            Log.Debug(() => $"{fxUserId}用户通过商品Id查询源商家Id，查询出的店铺：{tempAgentShops.ToJson()}", ModuleFileName.ManualSyncOrder);

            var filterShops = new HashSet<int>();

            // 排除服务过期、授权失败、无法同步的店铺
            tempAgentShops.ForEach(s =>
            {
                //if (CustomerConfig.IsLocalDbDebug)
                //    return;
                //if (s.IsServiceEnd)
                //{
                //    Log.Debug(() => $"GetAgentIdsByProductIds 店铺服务到期，shopId：{s.Id}，payUrl：{s.PayUrl},expiredTime：{s.ExpireTime}", ModuleFileName.ManualSyncOrder);

                //    // 服务到期，有可能是数据库没更新，查一下扩展信息
                //    s = GetShopsAndShopExtensionFunc(new List<int>() { s.Id }, true, false).FirstOrDefault();

                //    if (s == null || (s.PayUrl.IsNotNullOrEmpty() && s.ShopExtension?.ExpireTime < DateTime.Now))
                //    {
                //        filterShops.Add(s.Id);
                //        failedReasons.Add("商品ID关联的店铺已过期，请联系商家进行续费。");
                //        return;
                //    }

                //}

                if (s.OrderSyncStatus?.ShopIsExpired ?? false)
                {
                    filterShops.Add(s.Id);
                    failedReasons.Add("商品ID关联的店铺授权已失效，无法进行订单同步，请联系商家重新授权。");
                    return;
                }

                if (s.OrderSyncStatus?.IsCanSyncOrder != true)
                {
                    filterShops.Add(s.Id);
                    failedReasons.Add("商家店铺正在同步数据，期间无法手动同步，请稍候重试");
                    return;
                }
            });

            Log.Debug(() => $"{fxUserId}用户通过商品Id查询源商家Id，需要排除的店铺：{filterShops.ToJson()}", ModuleFileName.ManualSyncOrder);

            tempAgentShops = tempAgentShops.Where(tas=>!filterShops.Contains(tas.Id)).ToList();
            Log.Debug(() => $"{fxUserId}用户通过商品Id查询源商家Id，过滤后的店铺：{tempAgentShops.Select(s=>s.Id).ToJson()}", ModuleFileName.ManualSyncOrder);

            if (tempAgentShops.IsNullOrEmptyList())
                return Tuple.Create(failedReasons.ToList(),new List<int>()) ;

            // 前推店铺同步时间
            var tempSyncStatusList = tempAgentShops.Select(s => s.OrderSyncStatus).ToList();
            var successIds = new SyncStatusService().UpdateLastSyncTimeByList(tempSyncStatusList, -1 * day);
            tempShopIdList = tempSyncStatusList.Where(ts => successIds.Contains(ts.Id)).Select(ts => ts.ShopId).ToList();

            Log.Debug(() => $"{fxUserId}用户通过商品Id查询源商家Id，前推同步时间成功的店铺：{tempShopIdList.ToJson()}", ModuleFileName.ManualSyncOrder);
            if (tempShopIdList.IsNullOrEmptyList())
                return Tuple.Create(failedReasons.ToList(), new List<int>());

            // 获取源商家的用户Id
            tempAgentId = tempShopAndAgentDict.Where(d => tempShopIdList.Contains(d.Key)).Select(d => d.Value).ToList();
            //var sysShopIds = new ShopService().GetFxSystemShopByFxId(tempAgentId, "s.Id").Select(s => s.Id).ToList();
            return Tuple.Create(failedReasons.ToList(), tempAgentId);
        }

        /// <summary>
        /// 获取平台店铺信息，并缓存
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public List<Shop> GetPlatformShopInfosByIdsWithCache(List<int> ids)
        {
            return _repository.GetPlatformShopInfosByIdsWithCache(ids);
        }
        
        /// <summary>
        /// 获取有效的店铺
        /// </summary>
        /// <param name="platformType"></param>
        /// <returns></returns>
        public List<Shop> GetValidIdByPlatform(string platformType)
        {
            return _repository.GetValidByPlatform(platformType);
        }
    }
}
