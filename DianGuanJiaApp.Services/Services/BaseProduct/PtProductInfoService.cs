using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using DianGuanJiaApp.Data.Entity.BaseProduct;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Data.Repository.BaseProduct;
using DianGuanJiaApp.Data.Model.BaseProduct;
using Newtonsoft.Json;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Services.Services.SupplierProduct.ListingProduct;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Services.Services.SupplierProduct;
using DianGuanJiaApp.Services.Model;
using DianGuanJiaApp.Data.Entity.SupplierProduct;
using DianGuanJiaApp.Utility.Other;
using DianGuanJiaApp.Data.FxModel.SupplierProduct;
using DianGuanJiaApp.Services.Services.BaseProduct;
using DianGuanJiaApp.Data.FxModel.CategoryProduct;
using DianGuanJiaApp.Data.Enum;
using System.Collections.Concurrent;
using System.Text.RegularExpressions;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Data.Model.Tools;
using Top.Api.Domain;
using static DianGuanJiaApp.Data.Model.GetRecommendCategoryResult;
using DianGuanJiaApp.Data.FxModel.Listing;
using Org.BouncyCastle.Asn1.X509;
using Org.BouncyCastle.Ocsp;
using DianGuanJiaApp.Data.FxModel.Listing;
using MySql.Data.MySqlClient;
using System.Data.Common;
using System.Diagnostics;

namespace DianGuanJiaApp.Services.BaseProduct
{
    /// <summary>
    /// PtProductInfo 相关服务/逻辑处理
    /// </summary>
    public partial class PtProductInfoService : BaseProductBaseService<PtProductInfo>
    {
        private PtProductInfoRepository _repository;
        private readonly PtImgService _ptImgService = new PtImgService();
        private readonly MemberLevelService _memberLevelService = new MemberLevelService();
        //private BaseProductSkuSupplierConfigService _baseSkuSupplierService = new BaseProductSkuSupplierConfigService();
        //private BaseProductSkuService _baseProductSkuService = new BaseProductSkuService();

        public const int BATCH_SIZE = 500;//每批处理数量

        /// <summary>
        /// 默认使用当前登录账号的FxUserId [基础商品库]
        /// </summary>
        public PtProductInfoService()
        {
            _repository = new PtProductInfoRepository();
            _baseRepository = _repository;
        }

        /// <summary>
        /// 指定fxUserId [基础商品库]
        /// </summary>
        /// <param name="fxUserId"></param>
        public PtProductInfoService(int fxUserId)
        {
            _repository = new PtProductInfoRepository(fxUserId);
            _baseRepository = _repository;
        }

        /// <summary>
        /// 指定连接字符串
        /// </summary>
        /// <param name="connectionString"></param>
        public PtProductInfoService(string connectionString, bool isMySQL = false)
        {
            _repository = new PtProductInfoRepository(connectionString, isMySQL);
            _baseRepository = _repository;
        }

        /// <summary>
        /// 基础商品库及货盘库都有的表，用此构造函数
        /// </summary>
        /// <param name="dbFlag">supplierproduct=货盘，business=普通业务库，其他值=基础商品库</param>
        /// <param name="fxUserId">dbFlag为基础商品库时有效，是否指定用户，为0时未指定，用当前登录用户Id</param>
        public PtProductInfoService(string dbFlag, int fxUserId = 0)
        {
            _repository = new PtProductInfoRepository(dbFlag, fxUserId);
            _baseRepository = _repository;
        }

        /// <summary>
        /// 添加（含扩展数据）
        /// </summary>
        /// <param name="model"></param>
        public new void Add(PtProductInfo model)
        {
            if (model == null)
                return;
            if (string.IsNullOrEmpty(model.UniqueCode))
            {
                model.UniqueCode = Guid.NewGuid().ToString().ToShortMd5();
                if (model.Ext != null)
                    model.Ext.PtProductUniqueCode = model.UniqueCode;
            }

            if (model.CreateTime < Convert.ToDateTime("2000-01-01"))
                model.CreateTime = DateTime.Now;
            if (model.UpdateTime < Convert.ToDateTime("2000-01-01"))
                model.UpdateTime = DateTime.Now;
            if (model.Ext != null)
            {
                model.Ext.PtProductUniqueCode = model.UniqueCode;
                if (model.Ext.CreateTime < Convert.ToDateTime("2000-01-01"))
                    model.Ext.CreateTime = DateTime.Now;
                if (model.Ext.UpdateTime == null || model.Ext.UpdateTime < Convert.ToDateTime("2000-01-01"))
                    model.Ext.UpdateTime = DateTime.Now;
            }

            _repository.Add(model);

            // 同步数据到货盘库
            Task.Run(() =>
            {
                try
                {
                    new PtProductInfoRepository("supplierproduct", model.FxUserId).Add(model);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"添加同步数据到货盘库异常：{ex.ToJson()}");
                }
            });

            // //发送MQ
            // var message = new PtProductInfoMessage
            // {
            //     FxUserId = model.FxUserId,
            //     UniqueCodes = new List<string> { model.UniqueCode },
            //     PlatformType = model.PlatformType,
            // };
            // RabbitMQService.SendMessage(message, RabbitMQService.FxPtProductInfoDescription);
        }

        /// <summary>
        /// 批量添加（含扩展数据）
        /// </summary>
        /// <param name="models"></param>
        public void BatchAdd(List<PtProductInfo> models)
        {
            if (models == null || models.Any() == false)
                return;
            models.ForEach(model =>
            {
                if (string.IsNullOrEmpty(model.UniqueCode))
                {
                    model.UniqueCode = Guid.NewGuid().ToString().ToShortMd5();
                    if (model.Ext != null)
                        model.Ext.PtProductUniqueCode = model.UniqueCode;
                }

                if (model.CreateTime < Convert.ToDateTime("2000-01-01"))
                    model.CreateTime = DateTime.Now;
                if (model.UpdateTime < Convert.ToDateTime("2000-01-01"))
                    model.UpdateTime = DateTime.Now;
                if (model.Ext != null)
                {
                    model.Ext.PtProductUniqueCode = model.UniqueCode;
                    if (model.Ext.CreateTime < Convert.ToDateTime("2000-01-01"))
                        model.Ext.CreateTime = DateTime.Now;
                    if (model.Ext.UpdateTime == null || model.Ext.UpdateTime < Convert.ToDateTime("2000-01-01"))
                        model.Ext.UpdateTime = DateTime.Now;
                }
            });
            _repository.BatchAdd(models);

            // 同步数据到货盘库
            Task.Run(() =>
            {
                try
                {
                    // 根据models的FxUserId分组
                    var groups = models.GroupBy(p => p.FxUserId).ToList();
                    foreach (var group in groups)
                    {
                        new PtProductInfoRepository("supplierproduct", group.Key).BatchAdd(group.ToList());
                    }
                }
                catch (Exception ex)
                {
                    Log.WriteError($"批量添加同步数据到货盘库异常：{ex.ToJson()}");
                }
            });

            // 同步数据
            // var groups = models.GroupBy(p => new { p.FxUserId, p.PlatformType }).ToList();
            // foreach (var group in groups)
            // {
            //     var message = new PtProductInfoMessage
            //     {
            //         FxUserId = group.Key.FxUserId,
            //         UniqueCodes = group.Select(p => p.UniqueCode).ToList(),
            //         PlatformType = group.Key.PlatformType,
            //     };
            //     RabbitMQService.SendMessage(message, RabbitMQService.FxPtProductInfoDescription);
            // }
        }

        /// <summary>
        /// 更新（含扩展数据）
        /// </summary>
        /// <param name="model"></param>
        public new void Update(PtProductInfo model)
        {
            if (model == null)
                return;
            _repository.Update(model);

            // 同步数据到货盘库
            Task.Run(() =>
            {
                try
                {
                    new PtProductInfoRepository("supplierproduct", model.FxUserId).Update(model);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"更新同步数据到货盘库异常：{ex.ToJson()}");
                }
            });
        }

        /// <summary>
        /// 批量更新
        /// </summary>
        /// <param name="models"></param>
        public void BatchUpdate(List<PtProductInfo> models)
        {
            _repository.BatchUpdate(models);

            // 同步数据到货盘库
            Task.Run(() =>
            {
                try
                {
                    // 根据models的FxUserId分组
                    var groups = models.GroupBy(p => p.FxUserId).ToList();
                    foreach (var group in groups)
                    {
                        new PtProductInfoRepository("supplierproduct", group.Key).BatchUpdate(group.ToList());
                    }
                }
                catch (Exception ex)
                {
                    Log.WriteError($"批量更新同步数据到货盘库异常：{ex.ToJson()}");
                }
            });
            //
            // // 同步数据
            // var groups = models.GroupBy(p => new { p.FxUserId, p.PlatformType }).ToList();
            // foreach (var group in groups)
            // {
            //     var message = new PtProductInfoMessage
            //     {
            //         FxUserId = group.Key.FxUserId,
            //         UniqueCodes = group.Select(p => p.UniqueCode).ToList(),
            //         PlatformType = group.Key.PlatformType,
            //     };
            //     RabbitMQService.SendMessage(message, RabbitMQService.FxPtProductInfoDescription);
            // }
        }

        /// <summary>
        /// 批量更新
        /// </summary>
        /// <param name="models"></param>
        public void BatchUpdateByParam(List<PtProductInfo> models, List<string> param)
        {
            _repository.BulkUpdateByParam(models, param);

            // 同步数据到货盘库
            Task.Run(() =>
            {
                // 根据models的FxUserId分组
                var groups = models.GroupBy(p => p.FxUserId).ToList();
                foreach (var group in groups)
                {
                    new PtProductInfoRepository("supplierproduct", group.Key).BulkUpdateByParam(group.ToList(), param);
                }
            });
            //
            // // 同步数据
            // var groups = models.GroupBy(p => new { p.FxUserId, p.PlatformType }).ToList();
            // foreach (var group in groups)
            // {
            //     var message = new PtProductInfoMessage
            //     {
            //         FxUserId = group.Key.FxUserId,
            //         UniqueCodes = group.Select(p => p.UniqueCode).ToList(),
            //         PlatformType = group.Key.PlatformType,
            //     };
            //     RabbitMQService.SendMessage(message, RabbitMQService.FxPtProductInfoDescription);
            // }
        }

        /// <summary>
        /// 获取单个（含扩展表数据）
        /// </summary>
        /// <param name="code">UniqueCode</param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public PtProductInfo GetByCode(string code, int fxUserId = 0)
        {
            return _repository.GetByCode(code, fxUserId);
        }

        /// <summary>
        /// 是否存在平台资料
        /// </summary>
        /// <param name="uid"></param>
        /// <param name="fxUserId"></param>
        /// <param name="platformType">指定平台</param>
        /// <param name="shopId">指定店铺Id，0表示公共</param>
        /// <returns></returns>
        public bool IsExistByUid(long uid, int fxUserId, string platformType, int shopId = 0)
        {
            return _repository.IsExistByUid(uid, fxUserId, platformType, shopId);
        }

        /// <summary>
        /// 根据uid获取信息
        /// </summary>
        /// <param name="uid"></param>
        /// <returns></returns>
        public List<PtProductInfo> GetByProductUid(long uid, int fxUserId)
        {
            return _repository.GetByProductUid(uid, fxUserId);
        }

        /// <summary>
        /// 根据uid获取信息
        /// </summary>
        /// <param name="uid"></param>
        /// <returns></returns>
        public List<PtProductInfo> GetByProductUids(List<long> uids, int fxUserId)
        {
            return _repository.GetByProductUids(uids, fxUserId);
        }

        /// <summary>
        /// 获取平台资料
        /// </summary>
        /// <param name="uid"></param>
        /// <returns></returns>
        public PtProductInfoModel GetPtProductInfo(PtInfoReqModel req)
        {
            PtProductInfo ptProductInfo = GetPtProductInfoByCondition(req);
            //Log.Debug($"1.type={type},productUid={productUid},platformType={platformType},ptProductInfo={ptProductInfo.ToJson()}", "ptProductInfo.txt");

            // 如果尚未有平台资料，则先将基础资料一部分转化成平台资料返回给前端
            if (ptProductInfo == null)
            {
                PtProductInfoModel newPtProductModel = null;
                if (req.FromType == 1 || req.FromType == 0)
                {
                    newPtProductModel = GeneratePtProductFormBaseProduct(req.ProductUid, req.FxUserId, req.PlatformType);
                }
                else
                {
                    newPtProductModel = GeneratePtProductFormSupplier(req.ProductUid, req.FxUserId, req.PlatformType, req.FromType);
                }

                // 智能类目推荐使用：1.平台资料初始化、2.单品铺货资料不来源于平台资料
                GetAutoCategorysV2(newPtProductModel,1); // 平台资料
                //try
                //{
                //    newPtProductModel.IsInit = true;
                //    newPtProductModel.AutoCategoryInfos = GetAutoCategorys(newPtProductModel, req.Token);// 平台资料
                //    if(newPtProductModel.AutoCategoryInfos == null)
                //        newPtProductModel.IsInit = false;

                //}
                //catch (Exception ex)
                //{
                //    newPtProductModel.IsInit = false;
                //    Log.WriteError($"智能类目获取异常：{ex.ToJson()}");
                //}

                // 输出给前端时，按照笛卡尔积，系统会强制填充sku
                newPtProductModel.ProductInfoSkus = TranSkuFission(newPtProductModel.ProductInfoSkus);
                return newPtProductModel;
            }

            var model = TranPtModelFromPtEntity(ptProductInfo);

            // 输出给前端时，按照笛卡尔积，系统会强制填充sku
            model.ProductInfoSkus = TranSkuFission(model.ProductInfoSkus);

            //Log.Debug($"2.type={type},productUid={productUid},platformType={platformType},model={model.ToJson()}", "ptProductInfo.txt");
            #region 数据组装
            try
            {
                var baseProductSkuService = new BaseProductSkuService();
                var baseProduct = baseProductSkuService.GetBaseProductDetail(new BaseProductDetailReqModel { BaseProductUid = ptProductInfo.FromBaseProductUid }, req.FxUserId);
                //var ptSkus = ExchangeSku(true, model.ProductInfoSkus, baseProduct.Skus, null);
                //model.ProductInfoSkus = ptSkus;
                #region 相关的图片地址可能不能直接访问，需要进行一定的转换
                List<string> descriptionList = new List<string>();
                model.DescriptionStr?.ForEach(x => { descriptionList.Add(ImgHelper.ChangeImgUrl(x)); });
                model.ProductImages?.ForEach(x => { x.ImageUrl = ImgHelper.ChangeImgUrl(x.ImageUrl); });
                model.ProductInfoSkus?.ForEach(x =>
                {
                    var baseProductSku = baseProduct.Skus.FirstOrDefault(p => p.SkuCode == x.SkuCode);
                    x.StockCount = baseProductSku?.StockCount ?? 0; // 统一取基础资料sku库存填充，没有默认为0。多商品铺货需求

                    var tranUrl = ImgHelper.ChangeImgUrl(x.ImageUrl);
                    x.ImageUrl = tranUrl;
                    if (x.Attribute != null)
                    {
                        x.Attribute.ValueUrl = tranUrl;
                        x.Attribute.ValueUrlKey = tranUrl;
                    }
                });
                model.DescriptionStr = descriptionList;
                model.AttributeTypes?.ForEach(x =>
                {
                    x?.AttributeValues.ForEach(v =>
                    {
                        v.ValueUrl = ImgHelper.ChangeImgUrl(v.ValueUrl);
                    });
                });
                #endregion
                var content = new PlatformCategoryService().GetPlatformCategoryPublishRule(req.PlatformType, ptProductInfo.CategoryId) ?? string.Empty;
                //var rules = JsonConvert.DeserializeObject<ProductUpdateRuleInfoModel>(content);
                var rules = content.ToObject<ProductUpdateRuleInfoModel>();

                var productAttribute = model.ProductInfoSkus.Select(x => x.Attribute).ToList();

                var specs = rules?.product_spec_rule?.required_spec_details;

                // 可能会有历史原因导致的或者Sku不一致时需要生成新的AttributeTypes
                if (model.AttributeTypes == null || model.AttributeTypes.Count == 0)
                {
                    var ptAttributeTypesResult = GetPtAttributeTypes(productAttribute, specs);
                    model.ProductInfoSkus.ForEach(x => x.Attributes = PtProductSkuAttributeModel.FromClass(x.Attribute)); // 2024-11-26：规格值如果采用了平台侧的规格值，Attributes 需要重新赋值

                    if (ptAttributeTypesResult != null && ptAttributeTypesResult.Count > 0)
                    {
                        if (model.AttributeTypes == null)
                        {
                            model.AttributeTypes = new List<PtAttributeTypeModel>();
                        }
                        model.AttributeTypes.AddRange(ptAttributeTypesResult);
                    }
                }
                #region 平台属性类型-->匹配已保存的SellPropertyValues值
                var attributeTypesJson = ptProductInfo.Ext?.AttributeTypesJson;
                if (string.IsNullOrEmpty(attributeTypesJson) == false)
                {
                    var attrTypes = attributeTypesJson.ToObject<List<PtAttributeTypeModel>>();
                    if (attrTypes != null && attrTypes.Any() && model.AttributeTypes != null && model.AttributeTypes.Any())
                    {
                        model.AttributeTypes.ForEach(at =>
                        {
                            var existAt = attrTypes.FirstOrDefault(a => string.IsNullOrEmpty(a.SellPropertyId) == false && a.SellPropertyId != "0" && a.SellPropertyId == at.SellPropertyId);
                            if (existAt == null)
                                existAt = attrTypes.FirstOrDefault(a => a.AttributeName == at.AttributeName);

                            if (existAt != null && at.AttributeValues != null && at.AttributeValues.Any())
                            {
                                at.AttributeValues.ForEach(av =>
                                {
                                    var existAv = existAt.AttributeValues.FirstOrDefault(a => a.SellPropertyValues != null && a.SellPropertyValues.Any(b => b.sell_property_value_name == av.Value || (b.values.IsNotNullOrEmpty() && b.values == av.Value)));
                                    av.SellPropertyValues = existAv?.SellPropertyValues;
                                });
                            }
                        });
                    }
                }
                #endregion

                //Log.Debug($"3.type={type},productUid={productUid},isSameSku={isSameSku},model={model.ToJson()}", "ptProductInfo.txt");

                model.ProductInfoSkus.ForEach(x =>
                {
                    // 前端保存Sku值是在Attribute中，Attributes可能还是显示之前的，故由Attribute重新生成Attributes
                    if (x.Attribute != null)
                    {
                        x.Attributes = PtProductSkuAttributeModel.FromClass(x.Attribute);
                    }
                });
                model.SellPropertyJson = specs?.ToJson();

                /*导航属性查询 */
                model.NavCateValues = GetNavPropList(req.PlatformType, specs);

                // 代表来自于厂家平台资料
                if (req.FromType == 3 && model.IsPublic)
                {
                    model.CreateFrom = "SupplierProduct";
                }
                model.CategoryId = string.IsNullOrWhiteSpace(ptProductInfo.CategoryId) ? model.CategoryInfoList.Where(x => x.Level == model.CategoryInfoList.Max(y => y.Level)).FirstOrDefault()?.CateId : ptProductInfo.CategoryId;
            }
            catch (Exception ex)
            {
                Log.WriteError($"{nameof(GetByProductUid)}-{nameof(model.AttributeTypes)}异常：{ex.Message}");
            }
            #endregion

            return model;
        }

        private List<PlatformCategoryNavRes> GetNavPropList(string platformType, List<RequiredSpecDetail> specs)
        {
            var resList = new List<PlatformCategoryNavRes>();
            if (specs == null || !specs.Any())
                return resList;

            var displayStyle = new string[] { "cascader", "cascader_multi_select" };
            var platformCategoryService = new PlatformCategoryService();
            foreach (var item in specs)
            {
                if (displayStyle.Contains(item.value_display_style))
                {
                    var property_prop_first = item.navigation_properties.FirstOrDefault();
                    var navs = new PlatformCategoryNavRes(property_prop_first.property_id, property_prop_first.property_name);

                    var navigationList = platformCategoryService.GetNavigationCateProp(platformType, property_prop_first.property_id.ToString());
                    navs.ChildNodes = navigationList.Select(a => new PlatformCategoryNavigationSimlpe(a.PropertyValueId, a.PropertyValueName, a.LowerLevelAttrData)).ToList();
                    resList.Add(navs);
                }
            }

            return resList;
        }

        /// <summary>
        /// 获取平台资料根据条件
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private PtProductInfo GetPtProductInfoByCondition(PtInfoReqModel req)
        {
            PtProductInfo ptProductInfo = null;
            if (string.IsNullOrWhiteSpace(req.FromCode)) // code为空，代表是从基础资料/铺货第一次切换到平台资料
            {
                // 从基础资料进入到平台资料或从基础资料进行铺货
                if (req.FromType == 1 || req.FromType == 0)
                {
                    ptProductInfo = _repository.GetByProductUid(req.ProductUid, req.FxUserId, req.PlatformType);
                    if (ptProductInfo != null)
                    {
                        //数据源重置为自己
                        ptProductInfo.FromFxUserId = ptProductInfo.FxUserId;
                        //ptProductInfo.CreateFrom = "SelfSupplierProduct";
                        ptProductInfo.CreateFrom = "SelfBaseProduct";
                        ptProductInfo.FromBaseProductUid = req.ProductUid;
                        ptProductInfo.FromSupplierProductUid = null;
                    }
                }
                // 从自己货盘/厂商货盘进入到平台资料
                else
                {
                    var fxUserIdTemp = SiteContext.GetCurrentFxUserId();
                    // 代表是从厂商平台资料取,fxUserId即为厂商用户Id
                    if (req.FromType == 3)
                    {
                        fxUserIdTemp = req.FxUserId;
                    }
                    // 获取在铺货时创建的平台资料
                    ptProductInfo = _repository.GetBySupplierProductUid(req.ProductUid, fxUserIdTemp, req.FxUserId, req.PlatformType);
                    // 如果没有铺货时生成的平台资料，则去获取基础资料生成的平台资料
                    if (ptProductInfo == null)
                    {
                        SupplierProductService supplierProductService = new SupplierProductService();
                        var supplierProduct = supplierProductService.GetSupplierProductByUid(req.ProductUid, fxUserIdTemp);
                        // 考虑下极端情况
                        if (supplierProduct != null)
                        {
                            ptProductInfo = _repository.GetByProductUid(supplierProduct.FromProductUid, fxUserIdTemp, req.PlatformType);
                            // 从进入到平台资料
                            if (req.FromType == 3 && ptProductInfo != null && !ptProductInfo.IsPublic)
                            {
                                // 厂商平台资料非公开
                                ptProductInfo = null;
                            }
                            if (ptProductInfo != null)
                            {
                                ptProductInfo.FromBaseProductUid = supplierProduct.FromProductUid;
                                ptProductInfo.FromSupplierProductUid = supplierProduct.Uid;
                            }
                        }
                    }

                    //if (req.FromType != 3 && ptProductInfo != null)
                    //{
                    //    //数据源重置为自己
                    //    ptProductInfo.FromFxUserId = ptProductInfo.FxUserId;
                    //    ptProductInfo.CreateFrom = "SelfSupplierProduct";
                    //    ptProductInfo.FromBaseProductUid = req.ProductUid;
                    //    ptProductInfo.FromSupplierProductUid = null;
                    //}
                    if(ptProductInfo != null)
                    {
                        // 多店铺铺货，数据源重置为自己
                        ptProductInfo.FromFxUserId = ptProductInfo.FxUserId;
                        ptProductInfo.FxUserId = fxUserIdTemp;
                        ptProductInfo.CreateFrom = req.FromType != 3 ? "SelfSupplierProduct" : "SupplierProduct";

                        // 2024-10-25：铺自己的货盘时，注意 FromBaseProductUid 赋值为自己的基础资料Id
                        if (req.FromType == 2)
                            ptProductInfo.FromBaseProductUid = ptProductInfo.BaseProductUid;

                    }
                }
            }
            else
            {
                ptProductInfo = _repository.GetByCode(req.FromCode, req.FxUserId);
            }
            return ptProductInfo;
        }


        /// <summary>
        /// 获取不同场景下的平台资料
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        private PtProductInfo GetPtProductInfoByCondition2(PtInfoReqModel req)
        {
            PtProductInfo ptProductInfo = null;
            // 从基础资料进入到平台资料或从基础资料进行铺货
            if (req.FromType == 1 || req.FromType == 0)
            {
                ptProductInfo = _repository.GetByProductUid(req.ProductUid, req.FxUserId, req.PlatformType);
                if (ptProductInfo == null)
                    return null;

                ptProductInfo.FromFxUserId = ptProductInfo.FxUserId; //数据源重置为自己
                ptProductInfo.CreateFrom = "SelfBaseProduct";
                ptProductInfo.FromBaseProductUid = req.ProductUid;
                ptProductInfo.FromSupplierProductUid = null;
            }
            // 从自己货盘/厂商货盘进入到平台资料
            else
            {
                // 1.先获取在铺货时创建的平台资料
                var currentFxUserId = SiteContext.GetCurrentFxUserId();
                if (req.FromType == 3)
                {
                    ptProductInfo = _repository.GetBySupplierProductUid(req.ProductUid, currentFxUserId, req.FxUserId, req.PlatformType);
                }
                else
                {
                    ptProductInfo = _repository.GetBySupplierProductUid(req.ProductUid, currentFxUserId, currentFxUserId, req.PlatformType);
                }

                // 2.如果没有铺货时生成的平台资料，则去获取由基础资料生成的平台资料
                if (ptProductInfo == null)
                {
                    var supplierProduct = new SupplierProductService().GetSupplierProductByUid(req.ProductUid, currentFxUserId);
                    if (supplierProduct == null)
                        return null;

                    ptProductInfo = _repository.GetByProductUid(supplierProduct.FromProductUid, req.FxUserId, req.PlatformType);
                    if (ptProductInfo == null)
                        return null;

                    // 厂商平台资料非公开
                    if (req.FromType == 3 && !ptProductInfo.IsPublic)
                        return null;

                    if (req.FromType == 3)
                        ptProductInfo.FromBaseProductUid = supplierProduct.FromProductUid;
                    else
                        ptProductInfo.FromBaseProductUid = ptProductInfo.BaseProductUid;

                    ptProductInfo.FromSupplierProductUid = supplierProduct.Uid;
                    ptProductInfo.FromFxUserId = ptProductInfo.FxUserId;
                    ptProductInfo.FxUserId = currentFxUserId;
                    ptProductInfo.CreateFrom = req.FromType == 3 ? "SupplierProduct" : "SelfSupplierProduct";
                }
            }

            return ptProductInfo;
        }

        /// <summary>
        /// 转换成前端需要的model
        /// </summary>
        /// <param name="ptProductInfo"></param>
        /// <returns></returns>
        public PtProductInfoModel TranPtModelFromPtEntity(PtProductInfo ptProductInfo)
        {
            var model = new PtProductInfoModel()
            {
                UniqueCode = ptProductInfo.UniqueCode,
                Subject = ptProductInfo.Subject,
                PlatfromType = ptProductInfo.PlatformType,
                BaseProductUid = ptProductInfo.BaseProductUid.ToString2(),
                ProductImages = DeserializeObj<PtProductInfoImageModel>(ptProductInfo.Ext.MainImageJson, ptProductInfo.UniqueCode),
                DescriptionStr = string.IsNullOrWhiteSpace(ptProductInfo.Ext.Description) ? new List<string>() : ptProductInfo.Ext.Description.SplitToList(",")?.Select(a => ImgHelper.ChangeImgUrl(a)).ToList(),
                CategoryInfoList = DeserializeObj<PtCategoryInfo>(ptProductInfo.Ext.CategoryJson, ptProductInfo.UniqueCode),
                AttributeTypes = DeserializeObj<PtAttributeTypeModel>(ptProductInfo.Ext.AttributeTypesJson, ptProductInfo.UniqueCode),//new List<PtAttributeTypeModel>(),
                ProductInfoSkus = DeserializeObj<PtProductInfoSkuModel>(ptProductInfo.Ext.SkuJson, ptProductInfo.UniqueCode),
                CategoryAttribute = ptProductInfo.Ext.CategoryAttributes,
                FxUserId = ptProductInfo.FxUserId,
                IsPublic = ptProductInfo.IsPublic,
                FromFxUserId = ptProductInfo.FromFxUserId,
                FromBaseProductUid = ptProductInfo.FromBaseProductUid.ToString2(),
                FromSupplierProductUid = ptProductInfo.FromSupplierProductUid.ToString2(),
                CreateFrom = ptProductInfo.CreateFrom,
                SpuCode = ptProductInfo.SpuCode,
                RootNodeFxUserId = ptProductInfo.RootNodeFxUserId,
                SharePathCode = ptProductInfo.SharePathCode,
                PathNodeDeep = ptProductInfo.PathNodeDeep,
                IsWaitSyncBaseProduct = ptProductInfo.IsWaitSyncBaseProduct,
                CategoryId = ptProductInfo.CategoryId,
                NeedUpdate = ptProductInfo.NeedUpdate,
                UserListingSetting = ptProductInfo.Ext.SettingJson.ToObject<ListingSetting>(),
                ShopId = ptProductInfo.ShopId,
                RecommendCategory = new ProductRecommendCategory() { RecommendIds = ptProductInfo.Ext.RecommendIds },
            };
            return model;
        }
        /// <summary>
        /// 根据类目转换属性和规格(仅适用基础资料)
        /// </summary>
        /// <param name="productUid"></param>
        /// <param name="categoryId"></param>
        /// <param name="fxUserId"></param>
        /// <param name="platformType"></param>
        /// <returns></returns>
        public TranAttributeAndSkuResult TranAttributeAndSku(long productUid, string categoryId, string platformType)
        {
            var tranResult = new TranAttributeAndSkuResult();
            // 获取类目属性
            var platformCategoryServic = new PlatformCategoryService();
            var props = platformCategoryServic.GetCategoryProp(new Data.FxModel.CategoryProduct.GetCategoryPropReqModel { PlatformType = platformType, EndCateId = categoryId });
            string propsJson = props.ToJson();//JsonConvert.SerializeObject(props);

            // 获取类目规格并进行匹配
            var baseProductSkuService = new BaseProductSkuService();
            //var baseProductSkus = baseProductSkuService.GetBaseProductSkuList(productUid);

            var baseProduct = baseProductSkuService.GetBaseProductDetail(new BaseProductDetailReqModel { BaseProductUid = productUid }, SiteContext.GetCurrentFxUserId());
            if (baseProduct == null) throw new LogicException("基础资料查找出错！");
            var baseSkusSort = BaseSkuSort(baseProduct.Skus, baseProduct.AttributeTypes);

            var tranSourceSkus = TranBaseSkuToPtSku(baseSkusSort);
            if (baseProduct.SkuModeType == 1)
            {
                // tranSourceSkus = TranPlatformSkuFromSelfDefineSku(tranSourceSkus);
                tranSourceSkus = TranSkuFission(tranSourceSkus);
            }
            DyTranService dyTranService = new DyTranService();
            var platformCategoryService = new PlatformCategoryService();
            var ruleContent = platformCategoryService.GetPlatformCategoryPublishRule(platformType, categoryId);
            var primevalPropList = platformCategoryService.GetPrimevalCategoryProps(new Data.FxModel.CategoryProduct.GetCategoryPropReqModel { PlatformType = platformType, EndCateId = categoryId });
            ProductUpdateRuleInfoModel rule = null;
            // 前端需要的Sku
            List<PtProductInfoSkuModel> ptskus = null;
            // 前端需要的sku说明
            var ptAttributeTypes = new List<PtAttributeTypeModel>();
            // 前端需要的属性值
            string propsResult = string.Empty;
            // 销售属性
            var sellPropertys = new List<RequiredSpecDetail>();

            // 前端需要的类目说明
            List<PtCategoryInfo> ptcategories = new List<PtCategoryInfo>();
            try
            {
                rule = ruleContent.ToObject<ProductUpdateRuleInfoModel>();//JsonConvert.DeserializeObject<ProductUpdateRuleInfoModel>(ruleContent);
                if (rule != null)
                {
                    ptskus = dyTranService.TranBaseProductSpec(rule, tranSourceSkus) ?? new List<PtProductInfoSkuModel>();
                    ptskus.ForEach(x =>
                    {
                        x.Attributes = PtProductSkuAttributeModel.FromClass(x.Attribute);
                        x.Attribute.ValueUrl = x.ImageUrl;
                        x.Attribute.ValueUrlKey = x.ImageUrl;
                    });
                }

                var matchCatProps = dyTranService.MatchToutiaoAttr(propsJson, primevalPropList);
                propsResult = matchCatProps.ToJson();//JsonConvert.SerializeObject(matchCatProps);
                #region 类型数据组装
                var productAttribute = ptskus.Select(x => x.Attribute).ToList();
                var specs = rule.product_spec_rule?.required_spec_details;
                var ptAttributeTypesResult = GetPtAttributeTypes(productAttribute, specs);
                ptskus.ForEach(x => x.Attributes = PtProductSkuAttributeModel.FromClass(x.Attribute)); // 2024-11-26：规格值如果采用了平台侧的规格值，Attributes 需要重新赋值

                ptAttributeTypes.AddRange(ptAttributeTypesResult);
                #endregion

                #region 类目说明

                var categories = platformCategoryService.GetPlatformCategory(new Data.FxModel.CategoryProduct.GetCategoryPropReqModel { EndCateId = categoryId, PlatformType = platformType });
                if (categories.Count > 0)
                {
                    ptcategories.AddRange(categories.OrderBy(x => x.Level).Select(x => new PtCategoryInfo { CateId = x.CateId, Level = x.Level, Name = x.Name, ParentId = x.ParentId }));
                }
                #endregion

                //销售属性
                sellPropertys = specs;
            }
            catch (Exception ex)
            {
                Log.WriteError($"反序列化发布规则失败：{ex.Message}");
                throw new LogicException("转换失败");
            }
            //return Tuple.Create(propsResult, ptskus, ptAttributeTypes, ptcategories);

            tranResult.CategoryAttribute = propsResult;
            tranResult.ProductInfoSkus = ptskus;
            tranResult.AttributeTypes = ptAttributeTypes;
            tranResult.CategoryInfoList = ptcategories;
            tranResult.SellPropertyJson = sellPropertys?.ToJson();

            return tranResult;
        }
        /// <summary>
        /// 根据类目转换属性和规格
        /// </summary>
        /// <returns></returns>
        public TranAttributeAndSkuResult TranAttributeAndSku(List<PtProductInfoSkuModel> sourceSku, string sourceProps, string categoryId, string platformType,int skuModeType = 0)
        {
            if (sourceProps == null)
            {
                sourceProps = string.Empty;
            }
            var tranResult = new TranAttributeAndSkuResult();
            // 获取类目属性
            var platformCategoryServic = new PlatformCategoryService();
            //var props = platformCategoryServic.GetCategoryProp(new Data.FxModel.CategoryProduct.GetCategoryPropReqModel { PlatformType = platformType, EndCateId = categoryId });
            //string platformTypePropsJson = JsonConvert.SerializeObject(props);

            //// 获取类目规格并进行匹配
            //var baseProductSkuService = new BaseProductSkuService();
            //var baseProductSkus = baseProductSkuService.GetBaseProductSkuList(productUid);


            //var tranSourceSkus = GetTranSourceSkus(baseProductSkus);

            DyTranService dyTranService = new DyTranService();
            var platformCategoryService = new PlatformCategoryService();
            var content = platformCategoryService.GetPlatformCategoryPublishRule(platformType, categoryId);
            var primevalPropList = platformCategoryService.GetPrimevalCategoryProps(new Data.FxModel.CategoryProduct.GetCategoryPropReqModel { PlatformType = platformType, EndCateId = categoryId });
            ProductUpdateRuleInfoModel rules = null;
            // 前端需要的Sku
            List<PtProductInfoSkuModel> ptskus = null;
            // 前端需要的sku说明
            var ptAttributeTypes = new List<PtAttributeTypeModel>();
            // 前端需要的属性值
            string propsResult = string.Empty;
            // 销售属性
            var sellPropertys = new List<RequiredSpecDetail>();
            // 前端需要的类目说明
            List<PtCategoryInfo> ptcategories = new List<PtCategoryInfo>();
            rules = content.ToObject<ProductUpdateRuleInfoModel>();
            if (rules == null)
            {
                Log.WriteError($"类目={categoryId}-获取发布规则失败");
                //throw new LogicException("获取发布规则失败");
                return default;
            }
            #region 匹配属性
            try
            {
                var matchCatProps = dyTranService.MatchToutiaoAttr(sourceProps, primevalPropList);
                propsResult = matchCatProps?.ToJson();
            }
            catch (Exception ex)
            {
                Log.Debug(() => $"primevalPropList={primevalPropList.ToJson()};  sourceProps={sourceProps}", "TranAttributeAndSku.txt");
                Log.WriteError($"TranAttributeAndSku-匹配属性失败：{ex.Message},");
            }
            #endregion
            try
            {
                //rules = JsonConvert.DeserializeObject<ProductUpdateRuleInfoModel>(content);
                ptskus = dyTranService.TranBaseProductSpec(rules, sourceSku, skuModeType) ?? new List<PtProductInfoSkuModel>();
                ptskus.ForEach(x =>
                {
                    if (x.Attribute != null)
                    {
                        x.Attributes = PtProductSkuAttributeModel.FromClass(x.Attribute);
                        x.Attribute.ValueUrl = x.ImageUrl;
                        x.Attribute.ValueUrlKey = x.ImageUrl;
                    }
                });

                var specs = rules.product_spec_rule?.required_spec_details;

                /*导航属性查询*/
                tranResult.NavCateValues = GetNavPropList(platformType, specs);

                var productAttribute = ptskus.Select(x => x.Attribute).ToList();
                var ptAttributeTypesResult = GetPtAttributeTypes(productAttribute, specs); // 类目转换匹配接口
                ptskus.ForEach(x => x.Attributes = PtProductSkuAttributeModel.FromClass(x.Attribute)); // 2024-11-26：规格值如果采用了平台侧的规格值，Attributes 需要重新赋值

                ptAttributeTypes.AddRange(ptAttributeTypesResult);


                #region 类目说明

                var categories = platformCategoryService.GetPlatformCategory(new Data.FxModel.CategoryProduct.GetCategoryPropReqModel { EndCateId = categoryId, PlatformType = platformType });
                if (categories.Count > 0)
                {
                    ptcategories.AddRange(categories.OrderBy(x => x.Level).Select(x => new PtCategoryInfo { CateId = x.CateId, Level = x.Level, Name = x.Name, ParentId = x.ParentId }));
                }
                #endregion

                //销售属性
                sellPropertys = specs;
            }
            catch (Exception ex)
            {
                Log.WriteError($"反序列化发布规则失败：{ex.Message}");
                throw new LogicException("转换失败");
            }
            //return Tuple.Create(propsResult, ptskus, ptAttributeTypes, ptcategories);

            tranResult.CategoryAttribute = propsResult;
            tranResult.ProductInfoSkus = ptskus;
            tranResult.AttributeTypes = ptAttributeTypes;
            tranResult.CategoryInfoList = ptcategories;
            tranResult.SellPropertyJson = sellPropertys?.ToJson();

            return tranResult;
        }

        #region 内部用方法
        /// <summary>
        /// 反序列化字符串为指定对象
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="json"></param>
        /// <param name="uniqueCode"></param>
        /// <returns></returns>
        private List<T> DeserializeObj<T>(string json, string uniqueCode)
        {
            if (string.IsNullOrWhiteSpace(json))
            {
                return new List<T>();
            }
            try
            {
                return JsonConvert.DeserializeObject<List<T>>(json);
            }
            catch (Exception ex)
            {
                Log.WriteError($"平台资料({uniqueCode})反序列化异常：{ex.Message}");
            }
            return new List<T>();
        }

        private List<PtAttributeTypeModel> GetPtAttributeTypes(List<PtProductSkuAttributeModel> productAttributes, List<RequiredSpecDetail> specs)
        {
            var attributeCodes1 = new HashSet<string>();
            var attributeCodes2 = new HashSet<string>();
            var attributeCodes3 = new HashSet<string>();
            foreach (var item in productAttributes)
            {
                if (!string.IsNullOrWhiteSpace(item.AttributeCode1)) attributeCodes1.Add(item.AttributeCode1);
                if (!string.IsNullOrWhiteSpace(item.AttributeCode2)) attributeCodes2.Add(item.AttributeCode2);
                if (!string.IsNullOrWhiteSpace(item.AttributeCode3)) attributeCodes3.Add(item.AttributeCode3);
            }

            var baseProductSkuAttribute = new BaseProductSkuAttributeRepository().GetListByAttributeCodes(attributeCodes1.ToList(), attributeCodes2.ToList(), attributeCodes3.ToList());

            productAttributes.Select(a => a.AttributeCode1);
            var type1 = productAttributes
                .Where(a => !string.IsNullOrWhiteSpace(a.AttributeName1))
                .GroupBy(a => a.AttributeName1)
                .Select(a => new PtAttributeTypeModel
                {
                    AttributeName = a.Key,
                    AttributeValues = a.Select(b => new PtAttributeValueModel
                    {
                        ImgAttributeValueNo = b.ImgAttributeValueNo,
                        Value = b.AttributeValue1,
                        ValueUrl = b.ValueUrl,
                        ValueUrlKey = b.ValueUrlKey,
                        ImageObjectId = b.ImageObjectId,
                        UniCode = b.AttributeCode1,
                        OldValue = baseProductSkuAttribute.FirstOrDefault(att => att.AttributeCode1 == b.AttributeCode1)?.AttributeValue1 ?? ""
                    }).GroupBy(m => m.Value).Select(m => m.FirstOrDefault()).ToList(),
                    IsRequired = specs?.FirstOrDefault(s => s.sell_property_name == a.Key)?.is_required ?? false,
                    SupportDiy = specs?.FirstOrDefault(s => s.sell_property_name == a.Key)?.support_diy ?? false,
                    SupportRemark = specs?.FirstOrDefault(s => s.sell_property_name == a.Key)?.support_remark ?? false,
                    SellPropertyId = specs?.FirstOrDefault(s => s.sell_property_name == a.Key)?.sell_property_id.ToString2() ?? "",
                }).ToList();
            var type2 = productAttributes
                .Where(a => !string.IsNullOrWhiteSpace(a.AttributeName2))
                .GroupBy(a => a.AttributeName2)
                .Select(a => new PtAttributeTypeModel
                {
                    AttributeName = a.Key,
                    AttributeValues = a.Select(b => new PtAttributeValueModel
                    {
                        ImgAttributeValueNo = b.ImgAttributeValueNo,
                        Value = b.AttributeValue2,
                        ValueUrl = b.ValueUrl,
                        ValueUrlKey = b.ValueUrlKey,
                        ImageObjectId = b.ImageObjectId,
                        UniCode = b.AttributeCode2,
                        OldValue = baseProductSkuAttribute.FirstOrDefault(att => att.AttributeCode2 == b.AttributeCode2)?.AttributeValue2 ?? ""
                    }).GroupBy(m => m.Value).Select(m => m.FirstOrDefault()).ToList(),
                    IsRequired = specs?.FirstOrDefault(s => s.sell_property_name == a.Key)?.is_required ?? false,
                    SupportDiy = specs?.FirstOrDefault(s => s.sell_property_name == a.Key)?.support_diy ?? false,
                    SupportRemark = specs?.FirstOrDefault(s => s.sell_property_name == a.Key)?.support_remark ?? false,
                    SellPropertyId = specs?.FirstOrDefault(s => s.sell_property_name == a.Key)?.sell_property_id.ToString2() ?? "",
                }).ToList();
            var type3 = productAttributes
                .Where(a => !string.IsNullOrWhiteSpace(a.AttributeName3))
                .GroupBy(a => a.AttributeName3)
                .Select(a => new PtAttributeTypeModel
                {
                    AttributeName = a.Key,
                    AttributeValues = a.Select(b => new PtAttributeValueModel
                    {
                        ImgAttributeValueNo = b.ImgAttributeValueNo,
                        Value = b.AttributeValue3,
                        ValueUrl = b.ValueUrl,
                        ValueUrlKey = b.ValueUrlKey,
                        ImageObjectId = b.ImageObjectId,
                        UniCode = b.AttributeCode3,
                        OldValue = baseProductSkuAttribute.FirstOrDefault(att => att.AttributeCode3 == b.AttributeCode3)?.AttributeValue3 ?? ""
                    }).GroupBy(m => m.Value).Select(m => m.FirstOrDefault()).ToList(),
                    IsRequired = specs?.FirstOrDefault(s => s.sell_property_name == a.Key)?.is_required ?? false,
                    SupportDiy = specs?.FirstOrDefault(s => s.sell_property_name == a.Key)?.support_diy ?? false,
                    SupportRemark = specs?.FirstOrDefault(s => s.sell_property_name == a.Key)?.support_remark ?? false,
                    SellPropertyId = specs?.FirstOrDefault(s => s.sell_property_name == a.Key)?.sell_property_id.ToString2() ?? "",
                }).ToList();
            var attributeTypes = type1.Concat(type2).Concat(type3).ToList();

            if (specs == null)
                return attributeTypes;

            //设置默认的销售属性值
            SetDefaultSellPropertyValues(attributeTypes, specs);

            // 2024-11-26：基础资料规格值匹配平台规格值成功后，取值会采用平台的规格值，平台资料sku的规格值，也需一同修改
            foreach (PtProductSkuAttributeModel ptmodel in productAttributes)
                foreach (PtAttributeValueModel pv in attributeTypes.SelectMany(a => a.AttributeValues))
                {
                    if (!string.IsNullOrWhiteSpace(ptmodel.AttributeValue1) && ptmodel.AttributeValue1.Equals(pv.Value, StringComparison.CurrentCultureIgnoreCase))
                        ptmodel.AttributeValue1 = pv.Value;

                    if (!string.IsNullOrWhiteSpace(ptmodel.AttributeValue2) && ptmodel.AttributeValue2.Equals(pv.Value, StringComparison.CurrentCultureIgnoreCase))
                        ptmodel.AttributeValue2 = pv.Value;

                    if (!string.IsNullOrWhiteSpace(ptmodel.AttributeValue3) && ptmodel.AttributeValue3.Equals(pv.Value, StringComparison.CurrentCultureIgnoreCase))
                        ptmodel.AttributeValue3 = pv.Value;
                }

            return attributeTypes;
        }

        /// <summary>
        /// 设置默认的销售属性值
        /// </summary>
        /// <param name="model"></param>
        public void SetDefaultSellPropertyValues(List<PtAttributeTypeModel> attributeTypes, List<RequiredSpecDetail> specs)
        {
            if (attributeTypes != null && attributeTypes.Any() && specs != null && specs.Any())
            {
                attributeTypes.ForEach(at =>
                {
                    var existAt = specs.FirstOrDefault(a => a.sell_property_id > 0 && a.sell_property_id.ToString() == at.SellPropertyId);
                    if (existAt != null && at.AttributeValues != null && at.AttributeValues.Any())
                    {
                        at.AttributeValues.ForEach(av =>
                        {
                            SetDefaultSellPropertyValue(av, specs, existAt.sell_property_id); // 设置销售属性值
                        });
                    }
                });
            }
        }

        /// <summary>
        /// 基础资料规格值与平台资料规格值匹配
        /// 注意：2024-11-26 匹配成功后，基础资料规格值会采用平台规格值
        /// </summary>
        private void SetDefaultSellPropertyValue(PtAttributeValueModel ptAttributeValue, List<RequiredSpecDetail> spec, long sellPropertyId)
        {
            if (spec == null || spec.Count == 0) return;
            var existAt = spec.Where(x => x.sell_property_id == sellPropertyId).FirstOrDefault();
            if (existAt == null) return;
            var spvm = new SellPropertyValueModel();
            spvm.sell_property_id = existAt.sell_property_id.ToString();
            spvm.sell_property_name = existAt.sell_property_name;
            spvm.support_diy = existAt.support_diy;
            spvm.support_remark = existAt.support_remark;
            spvm.value_display_style = existAt.value_display_style;
            spvm.values = ptAttributeValue.Value;
            //是否匹配到值
            if (existAt.property_values != null && existAt.property_values.Any(a => a.sell_property_value_name.Equals(ptAttributeValue.Value, StringComparison.CurrentCultureIgnoreCase)))
            {
                var pv = existAt.property_values.First(a => a.sell_property_value_name.Equals(ptAttributeValue.Value, StringComparison.CurrentCultureIgnoreCase));
                spvm.sell_property_value_id = pv.sell_property_value_id.ToString();
                spvm.sell_property_value_name = pv.sell_property_value_name;
                //spvm.values = ptAttributeValue.Value;

                // 2024-11-26：当基础资料规格值与平台规格值匹配成功后，要采用平台的规格值。ptAttributeValue 修改了，但是 sku 里面的 attrbute 还是旧的，需要前端重新保存才是新的
                ptAttributeValue.Value = pv.sell_property_value_name;
                spvm.values = pv.sell_property_value_name;
            }
            else if (existAt.value_display_style == "measure")
            {
                //度量衡，提示=true
                ptAttributeValue.IsRequiredTip = true;
            }
            else
            {
                // 支持自定义
                if (!existAt.support_diy)
                {
                    ptAttributeValue.IsRequiredTip = true;
                }
            }
            ptAttributeValue.SellPropertyValues = new List<SellPropertyValueModel> { spvm };
        }

        /// <summary>
        /// 获取规格销售属性
        /// </summary>
        /// <param name="specs"></param>
        /// <returns></returns>
        private List<SkuSellProperty> GetSellPropertys(List<RequiredSpecDetail> specs)
        {
            if (specs == null || specs.Any() == false)
                return null;

            var sellProps = new List<SkuSellProperty>();
            foreach (var spec in specs)
            {
                var curSellProp = TranSingleSellProperty(spec);
                sellProps.Add(curSellProp);
            }

            return sellProps;
        }

        /// <summary>
        /// 转换单个销售属性
        /// </summary>
        /// <param name="spec"></param>
        /// <returns></returns>
        private SkuSellProperty TranSingleSellProperty(RequiredSpecDetail spec)
        {
            var sellProp = new SkuSellProperty();

            sellProp.sell_property_id = spec.sell_property_id.ToString2();
            sellProp.sell_property_name = spec.sell_property_name;
            sellProp.is_required = spec.is_required;
            sellProp.support_diy = spec.support_diy;
            sellProp.support_remark = spec.support_remark;
            sellProp.value_display_style = spec.value_display_style;
            sellProp.measure_templates = spec.measure_templates;

            return sellProp;
        }

        private List<PtProductInfoSkuModel> TranBaseSkuToPtSku(List<BaseProductSku> sourceBaseSkus)
        {
            var resultList = new List<PtProductInfoSkuModel>();
            foreach (var x in sourceBaseSkus)
            {
                var model = new PtProductInfoSkuModel();
                model.FromBaseProductSkuUid = x.Uid.ToString2();
                model.FromSupplierProductSkuUid = "0";
                model.SkuCode = x.SkuCode;
                model.Subject = x.Subject;
                model.Attributes = x.Attributes;
                model.Attribute = PtProductSkuAttributeModel.FromJson(x.Attributes);
                model.DistributePrice = x.DistributePrice ?? 0;
                model.SettlePrice = x.SettlePrice ?? 0;
                model.SalePrice = 0;
                model.StockCount = x.StockCount ?? 0;
                model.ImageUrl = ImgHelper.ChangeImgUrl(x.ImageUrl);
                model.ImageObjectId = x.ImageObjectId.ToString2();
                model.OtherJson = string.Empty;
                model.RootNodeFxUserId = x.RootNodeFxUserId;
                model.SharePathCode = x.SharePathCode;
                model.PathNodeDeep = x.PathNodeDeep;
                resultList.Add(model);
            }
            return resultList;
        }
        private List<PtProductInfoSkuModel> TranSupplierSkuToPtSku(List<SupplierProductSku> sourceSupplierSkus, int fxUserId)
        {
            // 2024.10 这里的采购价，取厂家基础资料的分销价
            var rep = new BaseProductSkuRepository(fxUserId);
            return sourceSupplierSkus.Select(x =>
            {
                var baseProductSku = rep.GetByUid(x.FromProductSkuUid);
                var n = new PtProductInfoSkuModel
                {
                    FromBaseProductSkuUid = x.FromProductSkuUid.ToString2(),
                    FromSupplierProductSkuUid = x.Uid.ToString2(),
                    SkuCode = x.SkuCode,
                    Subject = x.Subject,
                    Attributes = x.Attributes,
                    Attribute = PtProductSkuAttributeModel.FromJson(x.Attributes),
                    DistributePrice = x.DistributePrice ?? 0,
                    SalePrice = 0,
                    StockCount = 0,
                    ImageUrl = ImgHelper.ChangeImgUrl(x.ImageUrl),
                    ImageObjectId = string.Empty,
                    OtherJson = string.Empty,
                    RootNodeFxUserId = x.RootNodeFxUserId,
                    SharePathCode = x.SharePathCode,
                    PathNodeDeep = x.PathNodeDeep,
                    // 取厂家基础资料的分销价
                    SettlePrice = baseProductSku?.DistributePrice ?? 0
                };
                return n;
            }).ToList();
        }
        /// <summary>
        /// 调整Sku的顺序（前端是根据AttributeTypes进行排序的，基础资料的AttributeTypes会保存在数据库，可能顺序与其对应的BaseProductSku顺序不一致）
        /// </summary>
        /// <returns></returns>
        private List<BaseProductSku> BaseSkuSort(List<BaseProductSku> baseskus, List<AttributeTypeModel> attributeTypes)
        {
            if (baseskus == null || baseskus.Count == 0 || attributeTypes == null || attributeTypes.Count == 0) return baseskus;
            List<List<string>> sequences = new List<List<string>>();
            attributeTypes.ForEach(x =>
            {
                if (x.AttributeValues != null && x.AttributeValues.Count > 0)
                {
                    sequences.Add(x.AttributeValues.Select(a => a.Value).ToList());
                }
            });
            var cartesians = Cartesian(sequences);
            List<BaseProductSku> temp = new List<BaseProductSku>();
            foreach (var cartesian in cartesians)
            {
                foreach (var item in baseskus)
                {
                    if (item.AttributeModel == null)
                        continue;
                    List<string> valueList = new List<string> { item.AttributeModel.AttributeValue1, item.AttributeModel.AttributeValue2, item.AttributeModel.AttributeValue3 }.Where(x => !x.IsEmpty()).ToList();
                    if (cartesian.SequenceEqual(valueList))
                    {
                        temp.Add(item);
                        break;
                    }
                }
            }
            if (temp.Count != baseskus.Count)
            {
                Log.WriteError($"{nameof(BaseSkuSort)}排序出错！");
                return baseskus;
            }
            return temp;
        }
        /// <summary>
        /// 求集合的笛卡尔积
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="sequences"></param>
        /// <returns></returns>
        private IEnumerable<IEnumerable<T>> Cartesian<T>(IEnumerable<IEnumerable<T>> sequences)
        {
            IEnumerable<IEnumerable<T>> temp = new[] { Enumerable.Empty<T>() };
            return sequences.Aggregate(temp,
               (accumulator, sequence) => from accseq in accumulator
                                          from item in sequence
                                          select accseq.Concat(new[] { item })
                                          );
        }
        #endregion
        /// <summary>
        /// 保存或更新
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public bool SaveOrUpdate(PtProductInfoModel model)
        {
            var baseProductService = new BaseProductEntityService();
            foreach (var skuModel in model.ProductInfoSkus)
            {
                #region 平台资料Sku规格图处理
                if (!string.IsNullOrWhiteSpace(skuModel.ImageUrl) && !skuModel.ImageUrl.StartsWith("http"))  // 子字符串没有http的，说明是新上传的图片
                {
                    var path = ImgHelper.GetRealPath(skuModel.ImageUrl);
                    var pathStr = ImgHelper.SplitImageUrl(path);
                    var pathModel = JsonExtension.ToObject<BaseProductImageModel>(pathStr);

                    if (pathModel != null)
                    {
                        var suffix = pathModel.Suffix.IsNullOrEmpty() ? string.Empty : $".{pathModel.Suffix}";
                        skuModel.ImageUrl = $"{pathModel.Domain}/{pathModel.Url}/{pathModel.Name}{suffix}";

                        foreach (PtAttributeTypeModel item in model.AttributeTypes)
                        {
                            var attributeValues_model = item.AttributeValues.FirstOrDefault(c => c.Value == skuModel.Attribute.AttributeValue1
                                                            || c.Value == skuModel.Attribute.AttributeValue2
                                                            || c.Value == skuModel.Attribute.AttributeValue3);

                            if (attributeValues_model != null)
                            {
                                attributeValues_model.ValueUrlKey = skuModel.ImageUrl;
                                attributeValues_model.ValueUrl = ImgHelper.ChangeImgUrl(skuModel.ImageUrl);
                                break;
                            }
                        }
                    }
                }
                #endregion
            }

            if (model == null)
            {
                Log.WriteError($"添加平台资料失败：model 为null");
                return false;
            }
            var entity = TransferToPtProductInfo(model);
            // 新增
            if (string.IsNullOrWhiteSpace(model.UniqueCode))
            {
                // 保险的一步
                if (_repository.IsExistByUid(long.Parse(model.FromBaseProductUid), SiteContext.GetCurrentFxUserId(), model.PlatfromType))
                {
                    Log.WriteError($"添加平台资料失败：{model.FromBaseProductUid} - {SiteContext.GetCurrentFxUserId()}");
                    return false;
                }
                Add(entity);

            }
            // 更新
            else
            {
                var ptProductInfo = _repository.GetByCode(model.UniqueCode);
                if (ptProductInfo == null)
                {
                    return false;
                }
                //ptProductInfo.UniqueCode = ptProductInfo.UniqueCode,
                ptProductInfo.Subject = model.Subject;
                ptProductInfo.CategoryId = model.CategoryId;//model.CategoryInfoList.FirstOrDefault(c => c.Level == model.CategoryInfoList.Max(x => x.Level)).CateId;
                ptProductInfo.IsPublic = model.IsPublic;
                ptProductInfo.UpdateTime = DateTime.Now;
                ptProductInfo.IsWaitSyncBaseProduct = model.IsWaitSyncBaseProduct;

                //ptProductInfo.Ext.PtProductUniqueCode = entity.UniqueCode;
                ptProductInfo.Ext.CategoryJson = entity.Ext.CategoryJson;//JsonConvert.SerializeObject(model.CategoryInfoList);
                ptProductInfo.Ext.MainImageJson = JsonConvert.SerializeObject(model.ProductImages);
                ptProductInfo.Ext.Description = model.DescriptionStr == null ? string.Empty : string.Join(",", model.DescriptionStr);
                ptProductInfo.Ext.CategoryAttributes = model.CategoryAttribute;
                ptProductInfo.Ext.SkuJson = JsonConvert.SerializeObject(model.ProductInfoSkus);
                ptProductInfo.Ext.AttributeTypesJson = entity.Ext.AttributeTypesJson;
                ptProductInfo.Ext.UpdateTime = DateTime.Now;
                ptProductInfo.Ext.SettingJson = model.UserListingSetting.ToJson();
                ptProductInfo.NeedUpdate = model.NeedUpdate;
                //_repository.Update(ptProductInfo);
                Update(ptProductInfo);
            }
            return true;
        }

        /// <summary>
        /// 新增或者是更新入库
        /// </summary>
        /// <returns></returns>
        public PtProductInfo AddOrUpdate(PtProductInfoModel parameter)
        {
            if (parameter == null)
                return null;
            var entity = TransferToPtProductInfo(parameter, false);

            var ptProductInfo = _repository.GetPtInfoDrafts(entity.FromBaseProductUid, SiteContext.GetCurrentFxUserId(), entity.PlatformType);
            if (ptProductInfo == null)
            {
                entity.ShopId = -1;
                Add(entity);
            }
            else
            {
                entity.Ext.Id = ptProductInfo.Ext.Id;
                entity.Ext.PtProductUniqueCode = ptProductInfo.Ext.PtProductUniqueCode;
                entity.Id = ptProductInfo.Id;
                entity.ShopId = -1;
                entity.UniqueCode = ptProductInfo.UniqueCode;
                Update(entity);
            }

            return entity;
        }

        /// <summary>
        /// 转为平台资料
        /// </summary>
        /// <param name="model"></param>
        /// <param name="check_salePrice">是否要对售价进行检查</param>
        /// <returns></returns>
        public PtProductInfo TransferToPtProductInfo(PtProductInfoModel model, bool check_salePrice = true)
        {
            if (model == null)
                return null;
            string categoryJson = string.Empty;
            if ((model.CategoryInfoList == null || model.CategoryInfoList.Count == 0) && !string.IsNullOrWhiteSpace(model.CategoryId))
            {
                var platformCategoryService = new PlatformCategoryService();
                var categories = platformCategoryService.GetPlatformCategory(new Data.FxModel.CategoryProduct.GetCategoryPropReqModel { EndCateId = model.CategoryId, PlatformType = model.PlatfromType });
                if (categories.Count > 0)
                {
                    categoryJson = categories.OrderBy(x => x.Level).Select(x => new PtCategoryInfo { CateId = x.CateId, Level = x.Level, Name = x.Name, ParentId = x.ParentId })?.ToJson();
                }
            }
            else
            {
                categoryJson = model.CategoryInfoList?.ToJson();
            }
            model.ProductInfoSkus?.ForEach(x =>
            {
                // 前端保存Sku值是在Attribute中，Attributes可能还是显示之前的，故由Attribute重新生成Attributes
                if (x.Attribute != null)
                {
                    x.Attributes = PtProductSkuAttributeModel.FromClass(x.Attribute);
                }
                if (!x.IsDefaultPadding && x.SalePrice == 0 && !x.IsCopy && check_salePrice) throw new LogicException("售价不能为0");
            });
            #region 将图片转成真实的地址再存数据库
            List<string> descriptionList = new List<string>();
            model.DescriptionStr?.ForEach(x => { descriptionList.Add(ImgHelper.GetRealPath(x)); });
            model.ProductImages?.ForEach(x => { x.ImageUrl = ImgHelper.GetRealPath(x.ImageUrl); });
            model.ProductInfoSkus?.ForEach(x => { x.ImageUrl = ImgHelper.GetRealPath(x.ImageUrl); });
            model.DescriptionStr = descriptionList;
            model.AttributeTypes?.ForEach(x =>
            {
                x?.AttributeValues.ForEach(v =>
                {
                    v.ValueUrl = ImgHelper.GetRealPath(v.ValueUrl);
                });
            });
            #endregion
            PtProductInfo entity = new PtProductInfo();
            entity.Ext = new PtProductInfoExt();

            var fxUserId = model.FxUserId;
            if (fxUserId <= 0)
                fxUserId = SiteContext.GetCurrentFxUserId();
            var fromFxUserId = model.FromFxUserId;
            if (fromFxUserId <= 0)
                fromFxUserId = SiteContext.GetCurrentFxUserId();

            // 主
            //entity.UniqueCode = Guid.NewGuid().ToString().ToShortMd5();
            entity.PlatformType = model.PlatfromType;
            entity.Subject = model.Subject;
            entity.BaseProductUid = long.TryParse(model.BaseProductUid, out long parseProductUid) ? parseProductUid : 0;
            entity.FxUserId = fxUserId;
            entity.ShopId = 0;
            entity.CreateFrom = model.CreateFrom;
            entity.FromFxUserId = fromFxUserId;
            entity.FromBaseProductUid = model.FromBaseProductUid.ToLong();
            entity.FromSupplierProductUid = model.FromSupplierProductUid.ToLong();
            entity.SpuCode = model.SpuCode;
            entity.CategoryId = model.CategoryId;
            //entity.CategoryId = model.CategoryInfoList.FirstOrDefault(x => x.Level == model.CategoryInfoList.Max(y => y.Level)).CateId.ToString();
            entity.FreightId = 0;
            entity.IsWaitSyncBaseProduct = model.IsWaitSyncBaseProduct;
            entity.CreateTime = DateTime.Now;
            entity.Status = 1;
            entity.IsPublic = model.IsPublic;
            entity.RootNodeFxUserId = model.RootNodeFxUserId;
            entity.SharePathCode = model.SharePathCode;
            entity.PathNodeDeep = model.PathNodeDeep;

            entity.ListingShopIds = model.ListingShopIds;
            entity.UserListingSetting = model.UserListingSetting;
            entity.ListingConfig = model.ListingConfig;

            entity.FromCode = model.FromCode;
            entity.FromType = model.FromType;
            entity.NeedUpdate = model.NeedUpdate;

            //扩展
            //entity.Ext.PtProductUniqueCode = entity.UniqueCode;
            entity.Ext.CategoryJson = categoryJson;
            entity.Ext.MainImageJson = model.ProductImages.ToJson();
            entity.Ext.Description = model.DescriptionStr == null ? string.Empty : string.Join(",", model.DescriptionStr);
            entity.Ext.CategoryAttributes = model.CategoryAttribute;
            entity.Ext.SkuJson = model.ProductInfoSkus.ToJson();// JsonConvert.SerializeObject(model.ProductInfoSkus);
            entity.Ext.AttributeTypesJson = model.AttributeTypes.ToJson();// JsonConvert.SerializeObject(model.AttributeTypes);
            entity.Ext.CreateTime = DateTime.Now;
            entity.Ext.SettingJson = model.UserListingSetting.ToJson();
            entity.Ext.RecommendIds = model.RecommendCategory?.RecommendIds ?? "";
            return entity;
        }

        /// <summary>
        /// 获取指定平台资料信息
        /// </summary>
        /// <param name="baseProductUid"></param>
        /// <param name="platformType"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public PtProductInfo GetDefaultPtInfos(long baseProductUid, string platformType, int fxUserId)
        {
            return _repository.GetDefaultPtInfos(baseProductUid, platformType, fxUserId);
        }


        private Dictionary<string, string> TranImg(List<string> imgUrls, int typeFlag, string platform = "Alibaba")
        {
            if (imgUrls == null || imgUrls.Count == 0) return new Dictionary<string, string>();
            // key原始地址 value裁剪之后的地址
            ConcurrentDictionary<string, string> urlDic = new ConcurrentDictionary<string, string>();
            Parallel.ForEach(imgUrls, new ParallelOptions { MaxDegreeOfParallelism = 3 }, img =>
            {
                string cutImgResult = GetCutImgResult(img, typeFlag, platform);
                if (!cutImgResult.IsEmpty() && !urlDic.ContainsKey(img))
                {
                    urlDic.TryAdd(img, cutImgResult);
                }
            });
            return urlDic.ToDictionary(x => x.Key, x => x.Value);
        }

        private string GetCutImgResult(string imgStr, int typeFlag, string platform)
        {
            try
            {
                string tranUrl = string.Empty;
                byte[] imageBytes = null;
                if (string.IsNullOrEmpty(imgStr))
                    return string.Empty;

                string tempImg = ImgHelper.ChangeImgUrl(imgStr);
                if (tempImg.Trim('/').StartsWith("Common/GetImageFile"))
                {
                    string objectKey = Regex.Match(tempImg, @"(?<=objectKey=)\w+").Value;
                    if (!objectKey.IsEmpty())
                    {
                        //解密
                        var decryptObjectKey = DES.DecryptUrl(objectKey, CustomerConfig.LoginCookieEncryptKey);
                        imageBytes = CloudStorageUploaderFactory.GetObjectStorage(platform, decryptObjectKey);
                    }
                    else
                    {
                        throw new LogicException($"取objectKey失败！");
                    }
                }
                else
                {
                    imageBytes = ImgHelper.DownloadImageProcess(tempImg);
                    if (imageBytes == null) throw new LogicException("下载图片失败！");
                }
                if (imageBytes != null)
                {

                    var rsultBytes = GetCutImgBytes(imageBytes, typeFlag);
                    // 字节数组长度不变，可以从假定是没经过裁剪，是符合要求的
                    if (rsultBytes.Length == imageBytes.Length)
                    {
                        return tempImg;
                        //if (!urlDic.ContainsKey(img.Key))
                        //{
                        //    urlDic.TryAdd(img.Key, tempImg);
                        //    return;
                        //}
                    }
                    if (rsultBytes != null)
                    {
                        var result = CloudStorageUploaderFactory.Uploader(platform, "BaseProduct", SiteContext.Current.CurrentFxUserId.ToString(), $"{DateTime.Now:yyyy-MM-dd HH;mm:ss}-{Guid.NewGuid()}.{ImgHelper.GetImageFormat(rsultBytes)}", rsultBytes);
                        if (result != null)
                        {
                            return ImgHelper.GetImgeTransitUrl(result.Data);
                            //if (!tranUrl.IsEmpty() && !urlDic.ContainsKey(img.Key))
                            //{
                            //    urlDic.TryAdd(img.Key, tranUrl);

                            //}
                        }
                    }
                }
            }
            catch (LogicException le)
            {
                Log.WriteError($"{imgStr}=>裁剪异常：{le.Message}");
            }
            catch (Exception ex)
            {
                Log.WriteError($"{imgStr}=>裁剪异常：{ex.Message}");
            }
            return string.Empty;
        }

        #region 货盘和基础资料转平台资料时图片需裁剪
        public byte[] GetCutImgBytes(byte[] imageBytes, int typeFlag)
        {
            switch (typeFlag)
            {
                case 1:
                    imageBytes = PtMainImg(imageBytes);
                    break;
                case 2:
                    imageBytes = PtDescImg(imageBytes);
                    break;
                case 3:
                    imageBytes = PtSkuImg(imageBytes);
                    break;
                default:
                    return imageBytes;
            }
            if (imageBytes == null) throw new LogicException($"平台图片裁切失败！");
            // 稳一手，大于5M的需要压缩一下，还压缩不成功的话建议用户上传小一点的图片
            if ((imageBytes.Length / 1024.0 / 1024.0) > 5)
            {
                imageBytes = ImgHelper.CompressionImage(imageBytes, 3);
                if (imageBytes == null)
                {
                    throw new LogicException($"图片压缩异常，建议上传小于5M的图片！");
                }
            }
            return imageBytes;
        }

        /// <summary>
        /// 裁剪主图
        /// </summary>
        /// <param name="imageBytes"></param>
        /// <returns></returns>
        /// <exception cref="LogicException"></exception>
        private byte[] PtMainImg(byte[] imageBytes)
        {
            if (!IsAllowUploadFormat(imageBytes)) throw new LogicException("图片文件格式不支持！");
            return CutMainImg(imageBytes);
        }
        /// <summary>
        /// 裁剪详情图
        /// </summary>
        /// <param name="imageBytes"></param>
        /// <returns></returns>
        /// <exception cref="LogicException"></exception>
        private byte[] PtDescImg(byte[] imageBytes)
        {
            if (!IsAllowUploadFormat(imageBytes)) throw new LogicException("图片文件格式不支持！");
            return CutDescImg(imageBytes);
        }
        /// <summary>
        /// 裁剪Sku图
        /// </summary>
        /// <param name="imageBytes"></param>
        /// <returns></returns>
        /// <exception cref="LogicException"></exception>
        private byte[] PtSkuImg(byte[] imageBytes)
        {
            if (!IsAllowUploadFormat(imageBytes)) throw new LogicException("图片文件格式不支持！");
            return CutSkuImg(imageBytes);
        }
        private bool IsAllowUploadFormat(byte[] imageBytes)
        {
            var imageFormat = ImgHelper.GetImageFormat(imageBytes);
            if (imageFormat != null)
            {
                if (imageFormat.ToString().ToLower().Equals("jpeg") || imageFormat.ToString().ToLower().Equals("png"))
                {
                    return true;
                    //throw new LogicException($"{imageFormat}图片文件格式不支持！");
                }
            }
            return false;
        }
        private byte[] CutMainImg(byte[] imageBytes)
        {
            // 主图
            var mainImg = ImgHelper.CuttingImage(imageBytes, (w, h) =>
            {
                if (w < 600 || h < 600)
                {
                    return Tuple.Create(true, 600, 600);
                }
                if (w != h)
                {
                    return Tuple.Create(true, w < h ? w : h, w < h ? w : h);
                }
                return Tuple.Create(false, w, h);
            });
            return mainImg;
        }
        private byte[] CutDescImg(byte[] imageBytes)
        {
            // 详情图
            var descImg = ImgHelper.CuttingImage(imageBytes, (w, h) =>
            {
                if (w > 5000)
                {
                    return Tuple.Create(true, 5000, h < 20000 ? h : 20000);
                }
                if (h > 20000)
                {
                    return Tuple.Create(true, w > 5000 ? 5000 : w, 2000);
                }
                return Tuple.Create(false, w, h);
            });
            return descImg;
        }
        private byte[] CutSkuImg(byte[] imageBytes)
        {
            // 详情图
            var descImg = ImgHelper.CuttingImage(imageBytes, (w, h) =>
            {
                if (w != h)
                {
                    return Tuple.Create(true, w < h ? w : h, w < h ? w : h);
                }
                return Tuple.Create(false, w, h);
            });
            return descImg;
        }
        #endregion

        /// <summary>
        /// 从基础资料生成平台资料
        /// </summary>
        /// <returns></returns>
        private PtProductInfoModel GeneratePtProductFormBaseProduct(long productUid, int fxUserId, string platformType)
        {
            #region 自己的基础资料
            // 查询数据
            //var baseProductService = new BaseProductEntityService();
            var baseProductSkuService = new BaseProductSkuService();
            //var dyTranService = new DyTranService();
            //var baseProduct = baseProductService.GetByUid(productUid);
            var baseProduct = baseProductSkuService.GetBaseProductDetail(new BaseProductDetailReqModel { BaseProductUid = productUid }, fxUserId);
            if (baseProduct == null || baseProduct.Id == 0) throw new LogicException("基础资料查找出错！");
            var baseSkusSort = BaseSkuSort(baseProduct.Skus, baseProduct.AttributeTypes);
            var ptSkus = TranBaseSkuToPtSku(baseSkusSort);
            if(baseProduct.SkuModeType == 1)
            {
                // ptSkus = TranPlatformSkuFromSelfDefineSku(ptSkus);
            }
            List<PtAttributeTypeModel> ptAttributeTypes = new List<PtAttributeTypeModel>();
            #region 转换时进行图片的裁剪
            var skuImgs = ptSkus?.Select(x => x.ImageUrl).ToList();
            var skuImgsDic = TranImg(skuImgs, 3);

            var mainImgs = baseProduct?.Images?.Select(x => x.FullUrl).ToList();
            var mainImgsDic = TranImg(mainImgs, 1);

            var descImgs = string.IsNullOrWhiteSpace(baseProduct.Description) ? new List<string>() : baseProduct.Description.SplitToList(",")?.Select(a => ImgHelper.ChangeImgUrl(a)).ToList();
            var descImgsDic = TranImg(descImgs, 2);
            #endregion
            ptSkus?.ForEach(s =>
            {
                if (!s.ImageUrl.IsEmpty())
                {
                    s.ImageUrl = skuImgsDic.ContainsKey(s.ImageUrl) ? skuImgsDic[s.ImageUrl] : ImgHelper.ChangeImgUrl(s.ImageUrl);
                    if (s.Attribute != null)
                    {
                        s.Attribute.ValueUrl = s.ImageUrl;
                        s.Attribute.ValueUrlKey = s.ImageUrl;
                    }
                }
            });
            var productAttributes = ptSkus?.Select(x => x.Attribute).ToList() ?? new List<PtProductSkuAttributeModel>();
            var ptAttributeTypesTemp = GetPtAttributeTypes(productAttributes, null);
            if (ptAttributeTypesTemp != null && ptAttributeTypesTemp.Count > 0)
            {
                ptAttributeTypes.AddRange(ptAttributeTypesTemp);
            }
            return new PtProductInfoModel()
            {
                UniqueCode = string.Empty,
                Subject = baseProduct.Subject,
                PlatfromType = platformType,
                BaseProductUid = productUid.ToString2(),
                ProductImages = baseProduct.Images.Select(x => new PtProductInfoImageModel { ImageObjectId = x.ImageObjectId, ImageUrl = mainImgsDic.ContainsKey(x.FullUrl) ? mainImgsDic[x.FullUrl] : ImgHelper.ChangeImgUrl(x.FullUrl), IsMain = true }).ToList(),
                DescriptionStr = descImgs.Select(x => descImgsDic.ContainsKey(x) ? descImgsDic[x] : ImgHelper.ChangeImgUrl(x)).ToList(),//string.IsNullOrWhiteSpace(baseProduct.Description) ? new List<string>() : baseProduct.Description.SplitToList(",")?.Select(a => ImgHelper.ChangeImgUrl(a)).ToList(),
                CategoryInfoList = new List<PtCategoryInfo>(),
                AttributeTypes = ptAttributeTypes,
                ProductInfoSkus = ptSkus,
                CategoryAttribute = string.Empty,
                FxUserId = SiteContext.GetCurrentFxUserId(),
                IsPublic = true,
                FromFxUserId = fxUserId,
                FromBaseProductUid = productUid.ToString2(),
                FromSupplierProductUid = "0",
                CreateFrom = "SelfBaseProduct",
                SpuCode = baseProduct.SpuCode,
                RootNodeFxUserId = baseProduct.RootNodeFxUserId,
                SharePathCode = baseProduct.SharePathCode,
                PathNodeDeep = baseProduct.PathNodeDeep,
                CategoryId = string.Empty,
                SkuModeType = baseProduct.SkuModeType,
                NeedUpdate = false
            };
            #endregion
        }
        /// <summary>
        /// 从货盘生成平台资料
        /// </summary>
        /// <returns></returns>
        private PtProductInfoModel GeneratePtProductFormSupplier(long productUid, int fxUserId, string platformType,int type)
        {
            #region 自己/厂商的货盘资料
            SupplierProductService supplierProductService = new SupplierProductService();
            var supplierProduct = supplierProductService.GetSupplierProductByUid(productUid, SiteContext.GetCurrentFxUserId());
            // 因selfSupplierProduct.ProductSkus里不含有RootNodeFxUserId

            int fuid = type == 3 ? supplierProduct.FxUserId : fxUserId;
            var supplierProductSku = supplierProductService.GetSkuByUid(productUid, fuid);
            List<PtProductInfoSkuModel> ptSkus = TranSupplierSkuToPtSku(supplierProductSku, fxUserId);
            if (supplierProduct.SkuModeType == 1)
            {
                // ptSkus = TranPlatformSkuFromSelfDefineSku(ptSkus);
            }

            #region 转换时进行图片的裁剪
            var skuImgs = ptSkus?.Select(x => x.ImageUrl).ToList();
            var skuImgsDic = TranImg(skuImgs, 3);

            var mainImageObjs = supplierProduct.MainImageJson?.ToObject<List<SupplierProductImgModel>>().Select(x => new PtProductInfoImageModel { ImageObjectId = x.ImageObjectId, ImageUrl = x.FullUrl, IsMain = true }).ToList() ?? new List<PtProductInfoImageModel>();
            var mainImgs = mainImageObjs?.Select(x => x.ImageUrl).ToList();
            var mainImgsDic = TranImg(mainImgs, 1);
            mainImageObjs.ForEach(x => {
                x.ImageUrl = mainImgsDic.ContainsKey(x.ImageUrl) ? mainImgsDic[x.ImageUrl] : ImgHelper.ChangeImgUrl(x.ImageUrl);
            });

            var descImgs = new List<string>();
            var descImgsDic = new Dictionary<string, string>();
            if (!string.IsNullOrWhiteSpace(supplierProduct.Description))
            {
                descImgs = supplierProduct.Description?.Split(',').Select(a => a).ToList() ?? new List<string>();
                descImgsDic = TranImg(descImgs, 2);
            }
            #endregion
            //var ptSkus = TranSupplierSkuToPtSku(supplierProductSku);
            List<PtAttributeTypeModel> ptAttributeTypes = new List<PtAttributeTypeModel>();
            if (ptSkus != null)
            {
                ptSkus.ForEach(s =>
                {
                    if (!s.ImageUrl.IsEmpty())
                    {
                        s.ImageUrl = skuImgsDic.ContainsKey(s.ImageUrl) ? skuImgsDic[s.ImageUrl] : ImgHelper.ChangeImgUrl(s.ImageUrl);// ImgHelper.ChangeImgUrl(s.ImageUrl);
                                                                                                                                      //s.ImageUrl = TranImg(s.ImageUrl, 3);// ImgHelper.ChangeImgUrl(s.ImageUrl);
                        if (s.Attribute != null)
                        {
                            s.Attribute.ValueUrl = s.ImageUrl;
                            s.Attribute.ValueUrlKey = s.ImageUrl;
                        }
                    }
                });
                var productAttribute = ptSkus.Select(x => x.Attribute).ToList();
                var ptAttributeTypesTemp = GetPtAttributeTypes(productAttribute, null);
                if (ptAttributeTypesTemp != null && ptAttributeTypesTemp.Count > 0)
                {
                    ptAttributeTypes.AddRange(ptAttributeTypesTemp);
                }
            }
            if (supplierProduct.Description == string.Empty)
            {
                supplierProduct.Description = null;
            }
            return new PtProductInfoModel()
            {
                UniqueCode = string.Empty,
                Subject = supplierProduct.Subject,
                PlatfromType = platformType,
                BaseProductUid = type == 2 ? supplierProduct.FromProductUid.ToString2() : "0",
                ProductImages = mainImageObjs,
                DescriptionStr = descImgs.Select(x => descImgsDic.ContainsKey(x) ? descImgsDic[x] : ImgHelper.ChangeImgUrl(x)).ToList(),
                CategoryInfoList = new List<PtCategoryInfo>(),
                AttributeTypes = ptAttributeTypes,
                ProductInfoSkus = ptSkus,
                CategoryAttribute = supplierProduct.CategoryAttributes,
                FxUserId = SiteContext.GetCurrentFxUserId(),
                IsPublic = true,
                FromFxUserId = fxUserId,
                FromBaseProductUid = supplierProduct.FromProductUid.ToString2(),
                FromSupplierProductUid = productUid.ToString2(),
                CreateFrom = type == 2 ? "SelfSupplierProduct" : "SupplierProduct",
                SpuCode = supplierProduct.SpuCode,
                RootNodeFxUserId = supplierProduct.RootNodeFxUserId,
                SharePathCode = supplierProduct.SharePathCode,
                PathNodeDeep = supplierProduct.PathNodeDeep,
                CategoryId = string.Empty,
                SkuModeType = supplierProduct.SkuModeType,
                NeedUpdate = false
            };
            #endregion
        }
        /// <summary>
        /// 从基础资料自定义规格模式转成平台规格模式（按产品定义的规则来）
        /// </summary>
        /// <param name="baseProduct"></param>
        private List<PtProductInfoSkuModel> TranPlatformSkuFromSelfDefineSku(List<PtProductInfoSkuModel> ptSkus)
        {
            if (ptSkus == null || ptSkus.Count == 0) return new List<PtProductInfoSkuModel>();

            var skuValues1 = new List<string>();//baseProduct.Skus.Select(s => s.AttributeValue?.Split(';', '；')[0]).Distinct().ToList();
            var skuValues2 = new List<string>();
            var skuValueSplits = new Dictionary<string,(string,string)>();
            var skuImgUrlDic = new Dictionary<string, (string, string)>();
            ptSkus.ForEach(s => {
                if (s.Attribute.AttributeValue1.IsNullOrEmpty()) return;

                var splitResult = SplitSkuNameResult(s.Attribute.AttributeValue1);
                if(splitResult.Item1.IsNullOrEmpty()) return;

                if (!skuValues1.Any(x => splitResult.Item1.Equals(x, StringComparison.CurrentCultureIgnoreCase)))
                {
                    skuValues1.Add(splitResult.Item1);
                }
                if (!s.ImageUrl.IsNullOrEmpty() && !skuImgUrlDic.ContainsKey(splitResult.Item1))
                {
                    skuImgUrlDic.Add(splitResult.Item1, (s.ImageUrl, s.ImageObjectId));
                }
                if (splitResult.Item2.IsNotNullOrEmpty() && !skuValues2.Any(x => splitResult.Item2.Equals(x, StringComparison.CurrentCultureIgnoreCase))) skuValues2.Add(splitResult.Item2);

                if (!skuValueSplits.ContainsKey(s.SkuCode))
                {
                    skuValueSplits.Add(s.SkuCode, (splitResult.Item1.ToLower(), splitResult.Item2.ToLower()));
                }
            });

            // 生成笛卡尔积，即返回给前端多少条数据
            var skuCartesianProduct = new List<(string x, string y)>();
            if(skuValues2.Count > 0)
            {
                skuCartesianProduct = skuValues1.SelectMany(x => skuValues2.Select(y => (x, y))).ToList();
            }
            else
            {
                skuCartesianProduct = skuValues1.Select(x => (x,string.Empty)).ToList();
            }
            List<PtProductInfoSkuModel> newPtSkus = new List<PtProductInfoSkuModel>();
            skuCartesianProduct.ForEach(x =>
            {
                var ptsku = ptSkus.Where(s => skuValueSplits.ContainsKey(s.SkuCode) && skuValueSplits[s.SkuCode] == (x.Item1.ToLower(),x.Item2.ToLower())).FirstOrDefault();
                if (ptsku == null)
                {
                    ptsku = new PtProductInfoSkuModel();
                    ptsku.Attribute = new PtProductSkuAttributeModel();
                    ptsku.IsDefaultPadding = true;
                }
                ptsku.Attribute.AttributeName1 = "规格名称1";
                ptsku.Attribute.AttributeValue1 = x.Item1;
                //ptsku.Attribute.ValueUrl = skuImgUrlDic.ContainsKey(x.Item1) ? skuImgUrlDic[x.Item1].Item1 : string.Empty;
                ptsku.ImageUrl = skuImgUrlDic.ContainsKey(x.Item1) ? skuImgUrlDic[x.Item1].Item1 : string.Empty;
                ptsku.ImageObjectId = skuImgUrlDic.ContainsKey(x.Item1) ? skuImgUrlDic[x.Item1].Item2 : string.Empty;
                if (!x.Item2.IsNullOrEmpty())
                {
                    ptsku.Attribute.AttributeName2 = "规格名称2";
                    ptsku.Attribute.AttributeValue2 = x.Item2;
                }
                ptsku.Attributes = PtProductSkuAttributeModel.FromClass(ptsku.Attribute);
                newPtSkus.Add(ptsku);
            });
            return newPtSkus;
        }

        /// <summary>
        /// 生成符合笛卡尔积的sku列表
        /// </summary>
        /// <param name="ptSkus"></param>
        /// <returns>只有自定义模式下，目前是两层</returns>
        public List<PtProductInfoSkuModel> TranSkuFission(List<PtProductInfoSkuModel> ptSkus)
        {
            var result = new List<PtProductInfoSkuModel>();

            Dictionary<string, PtProductInfoSkuModel> dic1 = new Dictionary<string, PtProductInfoSkuModel>();
            Dictionary<string, PtProductInfoSkuModel> dic2 = new Dictionary<string, PtProductInfoSkuModel>();
            foreach (var item in ptSkus)
            {
                if (!string.IsNullOrWhiteSpace(item.Attribute.AttributeValue1) && !dic1.TryGetValue(item.Attribute.AttributeValue1, out _))
                    dic1.Add(item.Attribute.AttributeValue1, item);

                if (!string.IsNullOrWhiteSpace(item.Attribute.AttributeValue2) && !dic2.TryGetValue(item.Attribute.AttributeValue2, out _))
                    dic2.Add(item.Attribute.AttributeValue2, item);
            }

            if (!dic1.Any() || !dic2.Any())
            {
                return ptSkus; // 数据不能为空，无法计算
            }

            if (ptSkus.Count == dic1.Count * dic2.Count)
            {
                return ptSkus; // 当前sku裂变符合笛卡尔积，无需再处理
            }

            foreach (var item in dic1)
                foreach (var item2 in dic2)
                {
                    var defalut_sku = new PtProductInfoSkuModel();
                    defalut_sku.Attribute = new PtProductSkuAttributeModel();

                    defalut_sku.Attribute.AttributeName1 = "规格1";
                    defalut_sku.Attribute.AttributeValue1 = item.Key;
                    defalut_sku.Attribute.AttributeCode1 = item.Value.Attribute.AttributeCode1;

                    defalut_sku.Attribute.AttributeName2 = "规格2";
                    defalut_sku.Attribute.AttributeValue2 = item2.Key;
                    defalut_sku.Attribute.AttributeCode2 = item2.Value.Attribute.AttributeCode2;

                    defalut_sku.Attributes = PtProductSkuAttributeModel.FromClass(defalut_sku.Attribute);
                    result.Add(defalut_sku);
                }

            foreach (var item in result)
            {
                string val1val2 = (item.Attribute.AttributeValue1 ?? "") + (item.Attribute.AttributeValue2 ?? "");
                var dbsku = ptSkus.FirstOrDefault(p =>
                               !string.IsNullOrWhiteSpace(p.Attribute.AttributeValue1)
                            && !string.IsNullOrWhiteSpace(p.Attribute.AttributeValue2)
                            && p.Attribute.AttributeValue1 + p.Attribute.AttributeValue2 == val1val2);

                if (dbsku == null)
                {
                    item.IsDefaultPadding = true; // 系统填充的
                    item.SkuCode = string.Empty; // Guid.NewGuid().ToString().ToShortMd5();
                }
                else
                {
                    item.FromBaseProductSkuUid = dbsku.FromBaseProductSkuUid;
                    item.FromSupplierProductSkuUid = dbsku.FromSupplierProductSkuUid;
                    item.SkuCode = dbsku.SkuCode;
                    item.Subject = dbsku.Subject;
                    // item.Attributes = dbsku.Attributes;
                    item.DistributePrice = dbsku.DistributePrice;
                    item.SalePrice = dbsku.SalePrice;
                    item.SettlePrice = dbsku.SettlePrice;
                    item.StockCount = dbsku.StockCount;
                    item.ImageObjectId = dbsku.ImageObjectId;
                    item.ImageUrl = dbsku.ImageUrl;
                    item.OtherJson = dbsku.OtherJson;
                    item.RootNodeFxUserId = dbsku.RootNodeFxUserId;
                    item.SharePathCode = dbsku.SharePathCode;
                    item.PathNodeDeep = dbsku.PathNodeDeep;
                    // item.IsDefaultPadding = false;
                    item.IsDefaultPadding = dbsku.IsDefaultPadding;
                    item.IsCopy = dbsku.IsCopy;
                }
            }

            return result;
        }

        private (string,string) SplitSkuNameResult(string skuSelfDefineValue)
        {
            if (skuSelfDefineValue.IsNullOrEmpty()) return (string.Empty,string.Empty);

            var split2 = string.Empty;
            var splits = skuSelfDefineValue.Split(';', '；');

            if (splits.Length > 1)
            {
                int index = skuSelfDefineValue.IndexOf(';');
                var chineseSign = skuSelfDefineValue.IndexOf('；');
                if (index < 0)
                {
                    if (chineseSign == -1)
                        index = 0;
                    else
                        index = chineseSign;
                }
                else
                {
                    if (chineseSign > 0)
                        index = Math.Min(index, chineseSign);
                }
                split2 = skuSelfDefineValue.Substring(index).Trim(';').Trim('；').Trim(';');
            }

            if (string.IsNullOrWhiteSpace(split2))
                split2 = "无规格";

            return (splits[0], split2);
        }

        /// <summary>
        /// 获取级联属性（仅限抖店）
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        public DyCascadeValue GetCascadeValue(DyCascadeValueReqModel req)
        {
            // 获取类目属性
            var platformCategoryServic = new PlatformCategoryService();

            // 从现有的数据取出相关的数据
            var props = platformCategoryServic.GetCategoryPropByCateId(PlatformType.TouTiao.ToString(), req.CategoryId);

            var propResult = props.FirstOrDefault(x => x.Name == req.PropertyName);
            if (propResult == null)
            {
                Log.WriteError($"类目({req.CategoryId})找不到属性-{req.PropertyName}");
                throw new LogicException("获取级联属性失败");
            }
            var propId = propResult.PropertyId;
            var attr = propResult.Content.ToObject<DyCategoryPropInfoV2Model>();
            var attrResult = attr.options?.FirstOrDefault(x => x.name == req.PropertyValue);
            if (attrResult == null)
            {
                Log.WriteError($"类目({req.CategoryId})-属性（{req.PropertyName}）-找不到属性值-{req.PropertyValue}");
                throw new LogicException("获取级联属性失败");
            }
            var propValueId = attrResult.value_id;

            // 类目可能会对应多个店铺，取其中一个有效的店铺即可
            var shopIds = platformCategoryServic.GetShopIdByCateId(req.CategoryId);

            var shopSvc = new ShopService(PlatformAppScene.listing);
            var shops = shopSvc.GetShopByIds(shopIds);
            if (shops == null || shops.Count == 0)
            {
                Log.WriteError($"类目({req.CategoryId})-属性（{req.PropertyName}）-获取店铺失败");
                throw new LogicException("获取级联属性失败");
            }
            DyCascadeValue dyCascade = null;
            foreach (var shop in shops)
            {
                try
                {
                    var newZhiService = new ZhiDianNewPlatformService(shop);
                    dyCascade = newZhiService.GetCascadeValue(req.CategoryId, propId, new List<Tuple<string, string, string>> { Tuple.Create(propValueId.ToString(), req.PropertyValue, req.CascadeId.ToString()) });
                    if(dyCascade != null)
                    {
                        break;
                    }
                }
                catch (Exception ex)
                {
                    Log.WriteError($"类目({req.CategoryId})-属性（{req.PropertyName}）-店铺{shop.ShopName}获取级联属性异常:{ex.Message}");
                }
            }
            return dyCascade;
        }

        /// <summary>
        /// 根据基础商品Uid获取平台资料
        /// </summary>
        /// <param name="baseProductUidsDic">Key：基础商品ProUid，Value：货盘商品ProUid</param>
        /// <returns></returns>
        public Dictionary<long, List<PtProductInfo>> GetListByBaseProductUids(Dictionary<long, long> baseProductUidsDic)
        {
            return _repository.GetListByBaseProductUids(baseProductUidsDic);
        }

        /// <summary>
        /// 比较SkuCode
        /// </summary>
        /// <param name="results"></param>
        /// <param name="baseProductUids"></param>
        public void CompareSkuCode(Dictionary<long, List<PtProductInfo>> results, List<long> baseProductUids)
        {
            if (results == null || results.Count == 0 || baseProductUids == null || baseProductUids.Count == 0) return;

            // 获取对应的基础商品Sku
            var baseProductSkuService = new BaseProductSkuService();
            var baseProductSkus = baseProductSkuService.GetDicSkuCodeByProductUids(baseProductUids);

            // 获取平台资料的sku
            foreach (var group in results)
            {
                // 参数校验
                if (group.Value == null) continue;
                var ptProductInfoList = group.Value;

                ptProductInfoList.ForEach(ptProductInfo =>
                {
                    var productInfoSkus = DeserializeObj<PtProductInfoSkuModel>(ptProductInfo.Ext.SkuJson, ptProductInfo.UniqueCode);
                    if (productInfoSkus == null || productInfoSkus.Count == 0) return;

                    // 此处应命中
                    if (baseProductSkus.TryGetValue(group.Key, out var skuCode))
                    {
                        // 先比较数量
                        if (skuCode.Count != productInfoSkus.Count)
                        {
                            // 需要合并
                            ptProductInfo.IsNeedCombine = true;
                            return;
                        }

                        // 比较SkuCode
                        var ptSkuCodes = productInfoSkus.Select(x => x.SkuCode).ToList();
                        if (!ptSkuCodes.OrderBy(x => x).SequenceEqual(skuCode.OrderBy(x => x)))
                        {
                            ptProductInfo.IsNeedCombine = true;
                        }
                    }
                    else ptProductInfo.IsNeedCombine = true;
                });
            }
        }

        /// <summary>
        /// 将平台资料转换为平台资料添加模型
        /// </summary>
        /// <param name="ptProductInfos">平台资料</param>
        /// <param name="baseProductList">基础商品</param>
        /// <returns></returns>
        public List<bool> ModelMapAndAdd(Dictionary<long, List<PtProductInfo>> ptProductInfos, List<BaseProductEntity> baseProductList)
        {
            // 生成货品与基础商品Sku的关系
            var skuRelationDic = baseProductList
                .SelectMany(x => x.Skus)
                .ToDictionary(x => x.Uid, x => x.UpSkuUid);

            // 并发处理
            var successCount = new ConcurrentBag<bool>();
            Parallel.ForEach(ptProductInfos, new ParallelOptions { MaxDegreeOfParallelism = 5 }, ptProductInfo =>
            {
                if (ptProductInfo.Value == null) return;
                ptProductInfo.Value.ForEach(ptInfo =>
                {
                    var baseProduct = baseProductList.FirstOrDefault(b => b.UpBaseProductUid.ToLong() == ptProductInfo.Key);
                    if (baseProduct == null) return;

                    var model = TranPtModelFromPtEntity(ptInfo);
                    if (ptInfo.IsNeedCombine)
                    {
                        // 需要替换为基础商品SKu的信息
                        var baseProductSku = baseProduct.Skus;
                        if (baseProductSku == null || baseProductSku.Count == 0) return;

                        var temp = TranBaseSkuToPtSku(baseProduct.Skus);
                        var tranResult = TranAttributeAndSku(temp, string.Empty, model.CategoryId, model.PlatfromType);
                        if (tranResult != null)
                        {
                            model.ProductInfoSkus = tranResult.ProductInfoSkus;
                            model.AttributeTypes = new List<PtAttributeTypeModel>();

                            var skuImgList = model.ProductInfoSkus?.Select(x => x.ImageUrl).ToList();
                            var skuImgDic = TranImg(skuImgList, 3);
                            model.ProductInfoSkus?.ForEach(s =>
                            {
                                s.IsCopy = true;
                                if (s.ImageUrl.IsEmpty()) return;

                                s.ImageUrl = skuImgDic.TryGetValue(s.ImageUrl, out var value)
                                    ? value
                                    : ImgHelper.ChangeImgUrl(s.ImageUrl);

                                if (s.Attribute == null) return;
                                s.Attribute.ValueUrl = s.ImageUrl;
                                s.Attribute.ValueUrlKey = s.ImageUrl;
                            });
                        }
                        model.NeedUpdate = true;
                    }
                    else
                    {
                        // 将SkuCode替换为基础商品SKuCode
                        model.ProductInfoSkus?.ForEach(s =>
                        {
                            skuRelationDic.TryGetValue(s.FromBaseProductSkuUid.ToLong(), out var skuUid);
                            if (string.IsNullOrEmpty(skuUid)) return;

                            // 找到对应的基础商品Sku
                            var baseProductSku = baseProduct.Skus.FirstOrDefault(x => x.Uid.ToString() == skuUid);
                            if (baseProductSku == null) return;

                            s.SkuCode = baseProductSku.SkuCode;
                            s.FromBaseProductSkuUid = baseProductSku.Uid.ToString();
                            s.FromSupplierProductSkuUid = "0";
                            s.PathNodeDeep = baseProductSku.PathNodeDeep;
                            s.SharePathCode = baseProductSku.SharePathCode;
                        });
                    }

                    model.FromFxUserId = model.FxUserId;
                    model.FxUserId = baseProduct.FxUserId;
                    model.IsPublic = true;
                    model.FromBaseProductUid = baseProduct.Uid.ToString(); // 来源自己的基础商品Uid
                    model.FromSupplierProductUid = baseProduct.FromProductUid; // 来源厂家货盘Uid
                    model.BaseProductUid = baseProduct.Uid.ToString();
                    model.CreateFrom = "Copy";
                    model.RootNodeFxUserId = baseProduct.RootNodeFxUserId;
                    model.PathNodeDeep = baseProduct.PathNodeDeep;
                    model.SharePathCode = baseProduct.SharePathCode;
                    model.UniqueCode = default;

                    successCount.Add(SaveOrUpdate(model));
                });
            });

            return successCount.ToList();
        }

        /// <summary>
        /// 基础商品规格发生变化时，平台资料的规格也要随之变化
        /// </summary>
        /// <param name="baseProductUid"></param>
        /// <exception cref="NotImplementedException"></exception>
        public void UpdatePtProductInfoFromBaseProduct(BaseProductEntity oldBaseProduct)
        {
            // 新增两个标签：需更新（当有层级变化时）和需完善（两者仅存一个，优先需更新）
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var skuService = new BaseProductSkuService();
            var model = new BaseProductDetailReqModel();
            model.BaseProductUid = oldBaseProduct.Uid;
            model.Type = false;
            // 获取当前数据
            var newBaseProduct = skuService.GetBaseProductDetail(model, fxUserId);

            // 平台资料(可能有多个平台)
            var ptProdutInfos = _repository.GetByProductUid(oldBaseProduct.Uid, fxUserId).ToList();
            if (ptProdutInfos.Count == 0)
            {
                return;
            }

            // 新增或删除规格层数
            var oldSkuLevel = oldBaseProduct.Skus.FirstOrDefault()?.Attributes?.ToObject<List<object>>()?.Count;
            var newSkuLevel = newBaseProduct.Skus.FirstOrDefault()?.Attributes?.ToObject<List<object>>()?.Count;
            // 当前是平台录入模式，规格层级发生变更。就会变成自定义模式。就不存在以下的场景所以注释
            // // 规格层级有变化，则打个标签：需更新  2024-10-22 补：当前是平台录入模式，才有更新标签的逻辑
            // if (oldSkuLevel != newSkuLevel && newBaseProduct.SkuModeType == 0)
            // {
            //     ptProdutInfos.ForEach(info => info.IsWaitSyncBaseProduct = true);
            //     // 更新货盘资料更新状态
            //     _repository
            //         .BulkUpdateByParam(ptProdutInfos, new List<string> { "IsWaitSyncBaseProduct" });
            //     return;
            // }
            // else
            {
                var tempPtProducts = new List<PtProductInfo>();
                ptProdutInfos.ForEach((ptProductInfo) =>
                {
                    var temp = _repository.GetByProductUid(ptProductInfo.BaseProductUid, fxUserId, ptProductInfo.PlatformType);
                    tempPtProducts.Add(temp);
                });
                ptProdutInfos = tempPtProducts;
                // 新增、删除、修改规格值，假如规格值有度量衡的情况，且不能匹配度量衡的值，则打个标签：需完善
                Dictionary<string, BaseProductSku> sku_Image_Dic = new Dictionary<string, BaseProductSku>();
                Dictionary<string, decimal?> sku_DisPriceChange_Dic = new Dictionary<string, decimal?>();
                Dictionary<string, decimal?> sku_SettlePrice_Dic = new Dictionary<string, decimal?>();
                Dictionary<string, string> sku_Code_Dic = new Dictionary<string, string>();
                Dictionary<string, List<(string, string)>> sku_Value_Dic = new Dictionary<string, List<(string, string)>>();
                // 删除的SkuCode，平台资料的AttributeTypes也需要进行一定的删除
                List<string> skuDeleteCodes = new List<string>();
                // item1=规格，item2=规格值
                List<(string, string)> skuDeleteAttrTypesList = new List<(string, string)>();

                var oldSkuUids = oldBaseProduct.Skus?.Select(x => x.Uid).ToList() ?? new List<long>();
                var newSkuUids = newBaseProduct.Skus?.Select(x => x.Uid).ToList() ?? new List<long>();

                //var addSkuUids = newBaseProduct.Skus.Select(x => x.Uid).Except(oldBaseProduct.Skus?.Select(o => o.Uid)??new List<long>()).ToList();
                var addSkus = newBaseProduct.Skus?.Where(n => !oldSkuUids.Contains(n.Uid)).ToList();

                oldBaseProduct.Skus.ForEach(o =>
                {
                    var newBaseProductSku = newBaseProduct.Skus?.SingleOrDefault(n => n.Uid == o.Uid);
                    // 看看是否是新增的规格值
                    if (newBaseProductSku != null)
                    {
                        // 修改了规格值
                        if (o.Attributes != newBaseProductSku.Attributes)
                        {
                            var oldAttr = BaseProductUtils.FromJson(o.Attributes);
                            var newAttr = BaseProductUtils.FromJson(newBaseProductSku.Attributes);
                            List<(string, string)> changeSkuValue = new List<(string, string)>();
                            if (oldAttr.AttributeValue1.IsNotNullOrEmpty() && newAttr.AttributeValue1.IsNotNullOrEmpty() && oldAttr.AttributeValue1 != newAttr.AttributeValue1)
                            {
                                changeSkuValue.Add(("attr1", newAttr.AttributeValue1));
                            }
                            if (oldAttr.AttributeValue2.IsNotNullOrEmpty() && newAttr.AttributeValue2.IsNotNullOrEmpty() && oldAttr.AttributeValue2 != newAttr.AttributeValue2)
                            {
                                changeSkuValue.Add(("attr2", newAttr.AttributeValue2));
                            }
                            if (oldAttr.AttributeValue3.IsNotNullOrEmpty() && newAttr.AttributeValue3.IsNotNullOrEmpty() && oldAttr.AttributeValue3 != newAttr.AttributeValue3)
                            {
                                changeSkuValue.Add(("attr3", newAttr.AttributeValue3));
                            }
                            if (!sku_Value_Dic.ContainsKey(o.SkuCode) && !addSkus.Any(a => a.SkuCode == o.SkuCode)) // [addSkus]新增的规格，不需要判断规格值是否有修改
                            {
                                sku_Value_Dic.Add(o.SkuCode, changeSkuValue);
                            }
                        }
                        // 新增/删除/修改规格图片，直接更新，不用提醒
                        if (o.ImageUrl != newBaseProductSku.ImageUrl)
                        {
                            if (!sku_Image_Dic.ContainsKey(o.SkuCode))
                            {
                                sku_Image_Dic.Add(o.SkuCode, newBaseProductSku);
                            }
                        }
                        // 修改SKU编码，直接更新，不用提醒
                        if (o.SkuCode != newBaseProductSku.SkuCode)
                        {
                            if (!sku_Code_Dic.ContainsKey(o.SkuCode))
                            {
                                sku_Code_Dic.Add(o.SkuCode, newBaseProductSku.SkuCode);
                            }
                        }
                        // 修改分销价，直接更新，不用提醒
                        if (o.DistributePrice != newBaseProductSku.DistributePrice)
                        {
                            if (!sku_DisPriceChange_Dic.ContainsKey(o.SkuCode))
                            {
                                sku_DisPriceChange_Dic.Add(o.SkuCode, newBaseProductSku.DistributePrice);
                            }
                        }
                        // 修改采购价，直接更新，不用提醒
                        if (o.SettlePrice != newBaseProductSku.SettlePrice)
                        {
                            if (!sku_SettlePrice_Dic.ContainsKey(o.SkuCode))
                            {
                                sku_SettlePrice_Dic.Add(o.SkuCode, newBaseProductSku.SettlePrice);
                            }
                        }
                    }
                    else
                    {
                        skuDeleteCodes.Add(o.SkuCode);
                    }
                });

                var platformCategoryService = new PlatformCategoryService();

                ptProdutInfos.ForEach(pt =>
                {
                    var ruleContent = platformCategoryService.GetPlatformCategoryPublishRule(pt.PlatformType, pt.CategoryId);
                    var rules = ruleContent.ToObject<ProductUpdateRuleInfoModel>();
                    var specs = rules?.product_spec_rule?.required_spec_details;

                    var ptSkus = pt.Ext?.SkuJson.ToObject<List<PtProductInfoSkuModel>>() ?? new List<PtProductInfoSkuModel>();

                    // key=原规格值 value=新规格值
                    Dictionary<string, string> attrTypeValueChangeDic = new Dictionary<string, string>();
                    // key=原规格值 value=新ImgUrl
                    Dictionary<string, string> attrTypeImgChangeDic = new Dictionary<string, string>();
                    ptSkus.ForEach(s =>
                    {
                        //  新增/删除/修改规格图片，直接更新，不用提醒
                        if (sku_Image_Dic.ContainsKey(s.SkuCode))
                        {
                            var imageUrl = sku_Image_Dic[s.SkuCode].ImageUrl;
                            if (imageUrl.IsNotNullOrEmpty())
                            {
                                var skuImgsDic = TranImg(new List<string> { imageUrl }, 3);
                                s.ImageObjectId = "";
                                imageUrl = ImgHelper.GetRealPath(skuImgsDic.ContainsKey(imageUrl) ? skuImgsDic[imageUrl] : imageUrl);
                                s.ImageUrl = imageUrl;
                                s.Attribute.ValueUrl = imageUrl;
                                s.Attribute.ValueUrlKey = imageUrl;
                            }
                            else
                            {
                                s.ImageObjectId = "";
                                s.ImageUrl = string.Empty;
                                s.Attribute.ValueUrl = string.Empty;
                                s.Attribute.ValueUrlKey = string.Empty;
                            }
                            if (!attrTypeImgChangeDic.ContainsKey(s.Attribute.AttributeValue1))
                            {
                                attrTypeImgChangeDic.Add(s.Attribute.AttributeValue1, imageUrl);
                            }
                        }
                        // 修改采购价，直接更新，不用提醒
                        if (sku_SettlePrice_Dic.ContainsKey(s.SkuCode))
                            s.SettlePrice = sku_SettlePrice_Dic[s.SkuCode] ?? 0;

                        // 修改分销价，直接更新，不用提醒
                        if (sku_DisPriceChange_Dic.ContainsKey(s.SkuCode))
                            s.DistributePrice = sku_DisPriceChange_Dic[s.SkuCode] ?? 0;

                        // 规格值
                        if (sku_Value_Dic.ContainsKey(s.SkuCode))
                        {
                            var changAttr = sku_Value_Dic[s.SkuCode];
                            changAttr.ForEach(a =>
                            {
                                if (a.Item1 == "attr1")
                                {
                                    // 存一份规格值改变的副本，用于改变AttributeTypesJson
                                    if (!attrTypeValueChangeDic.ContainsKey(s.Attribute.AttributeValue1))
                                    {
                                        attrTypeValueChangeDic.Add(s.Attribute.AttributeValue1, a.Item2);
                                    }
                                    s.Attribute.AttributeValue1 = a.Item2;
                                }
                                if (a.Item1 == "attr2")
                                {
                                    // 存一份规格值改变的副本，用于改变AttributeTypesJson
                                    if (!attrTypeValueChangeDic.ContainsKey(s.Attribute.AttributeValue2))
                                    {
                                        attrTypeValueChangeDic.Add(s.Attribute.AttributeValue2, a.Item2);
                                    }
                                    s.Attribute.AttributeValue2 = a.Item2;
                                }
                                if (a.Item1 == "attr3")
                                {
                                    // 存一份规格值改变的副本，用于改变AttributeTypesJson
                                    if (!attrTypeValueChangeDic.ContainsKey(s.Attribute.AttributeValue3))
                                    {
                                        attrTypeValueChangeDic.Add(s.Attribute.AttributeValue3, a.Item2);
                                    }
                                    s.Attribute.AttributeValue3 = a.Item2;
                                }
                            });
                        }

                        // 修改SKU编码，直接更新，不用提醒
                        if (sku_Code_Dic.ContainsKey(s.SkuCode))
                            s.SkuCode = sku_Code_Dic[s.SkuCode];
                    });

                    var attrTypes = pt.Ext?.AttributeTypesJson.ToObject<List<PtAttributeTypeModel>>() ?? new List<PtAttributeTypeModel>();
                    // 证明有规格值改变了
                    attrTypes.ForEach(t =>
                    {
                        t.AttributeValues.ForEach(a =>
                        {
                            if (attrTypeImgChangeDic.ContainsKey(a.Value))
                            {
                                a.ValueUrl = attrTypeImgChangeDic[a.Value];
                                a.ValueUrlKey = a.ValueUrl;
                            }
                            if (attrTypeValueChangeDic.ContainsKey(a.Value))
                            {
                                a.Value = attrTypeValueChangeDic[a.Value];
                                a.OldValue = a.Value; // 修改了规格值，平台资料的源规格值也要更新
                                SetDefaultSellPropertyValue(a, specs, t.SellPropertyId.ToLong());
                                if (a.IsRequiredTip)
                                {
                                    pt.NeedUpdate = true;
                                }
                            }
                        });
                    });
                    // 添加的规格值
                    if (addSkus.Count > 0)
                    {
                        var addPtSkus = TranBaseSkuToPtSku(addSkus);

                        var sellAttr1 = ptSkus.FirstOrDefault().Attribute?.AttributeName1;
                        var sellAttr2 = ptSkus.FirstOrDefault().Attribute?.AttributeName2;
                        var sellAttr3 = ptSkus.FirstOrDefault().Attribute?.AttributeName3;
                        addPtSkus.ForEach(x =>
                        {
                            string val1val2 = (x.Attribute.AttributeValue1 ?? "") + (x.Attribute.AttributeValue2 ?? "");
                            var dbsku = ptSkus.FirstOrDefault(p => p.IsDefaultPadding
                                    && !string.IsNullOrWhiteSpace(p.Attribute.AttributeValue1)
                                    && !string.IsNullOrWhiteSpace(p.Attribute.AttributeValue2)
                                    && p.Attribute.AttributeValue1 + p.Attribute.AttributeValue2 == val1val2);

                            if (dbsku != null)
                                ptSkus.Remove(dbsku); // 如果检测到系统填充的的，基础资料刚好完善了这个填充的，删除掉

                            x.Attribute.AttributeName1 = sellAttr1;
                            x.Attribute.AttributeName2 = sellAttr2;
                            x.Attribute.AttributeName3 = sellAttr3;

                            var attrTypes1 = attrTypes.Where(a => a.AttributeName == sellAttr1).FirstOrDefault();
                            var attrTypes2 = attrTypes.Where(a => a.AttributeName == sellAttr2).FirstOrDefault();
                            var attrTypes3 = attrTypes.Where(a => a.AttributeName == sellAttr3).FirstOrDefault();

                            // 如果基础资料的sku值和平台资料的sku值有关系关系（code一样），增加的sku值应该是平台资料sku值。替换过来
                            var value1 = attrTypes1?.AttributeValues.FirstOrDefault(a => a.UniCode == x.Attribute.AttributeCode1);
                            if (value1 != null) x.Attribute.AttributeValue1 = value1.Value;
                            if (attrTypes1?.AttributeValues.Any(a => a.Value == x.Attribute.AttributeValue1) == false && value1 == null)
                            {
                                SetPtAttributeValueModel(pt, x, specs, x.Attribute.AttributeCode1, x.Attribute.AttributeValue1, attrTypes1);
                            }

                            var value2 = attrTypes2?.AttributeValues.FirstOrDefault(a => a.UniCode == x.Attribute.AttributeCode2);
                            if (value2 != null) x.Attribute.AttributeValue2 = value2.Value;
                            if (attrTypes2?.AttributeValues.Any(a => a.Value == x.Attribute.AttributeValue2) == false && value2 == null)
                            {
                                SetPtAttributeValueModel(pt, x, specs, x.Attribute.AttributeCode2, x.Attribute.AttributeValue2, attrTypes2);
                            }

                            var value3 = attrTypes3?.AttributeValues.FirstOrDefault(a => a.UniCode == x.Attribute.AttributeCode3);
                            if (value3 != null) x.Attribute.AttributeValue3 = value3.Value;
                            if (attrTypes3?.AttributeValues.Any(a => a.Value == x.Attribute.AttributeValue3) == false && value3 == null)
                            {
                                SetPtAttributeValueModel(pt, x, specs, x.Attribute.AttributeCode3, x.Attribute.AttributeValue3, attrTypes3);
                            }

                            x.Attributes = PtProductSkuAttributeModel.FromClass(x.Attribute);
                        });
                        ptSkus.AddRange(addPtSkus);
                    }

                    // 删除的规格值
                    if (skuDeleteCodes.Count > 0)
                    {
                        var noDelSkus = ptSkus.Where(x => !skuDeleteCodes.Contains(x.SkuCode)).ToList();

                        var attrValues2 = noDelSkus.GroupBy(x => x.Attribute.AttributeName2, (name, values) => new { Name = name, Values = values.Select(x => x.Attribute.AttributeValue2).ToList() }).FirstOrDefault();
                        var attrValues3 = noDelSkus.GroupBy(x => x.Attribute.AttributeName3, (name, values) => new { Name = name, Values = values.Select(x => x.Attribute.AttributeValue3).ToList() }).FirstOrDefault();
                        var attrValues1 = noDelSkus.GroupBy(x => x.Attribute.AttributeName1, (name, values) => new { Name = name, Values = values.Select(x => x.Attribute.AttributeValue1).ToList() }).FirstOrDefault();

                        var attrTypes1 = attrTypes.Where(a => a.AttributeName == attrValues1.Name).FirstOrDefault();
                        var attrTypes2 = attrTypes.Where(a => a.AttributeName == attrValues2.Name).FirstOrDefault();
                        var attrTypes3 = attrTypes.Where(a => a.AttributeName == attrValues3.Name).FirstOrDefault();

                        attrTypes.ForEach(x =>
                        {
                            var attributeValues = x.AttributeValues.Select(a => a.Value).ToList();
                            if (attrValues1.Name.IsNotNullOrEmpty() && x.AttributeName == attrValues1.Name)
                            {
                                var delSellAttrValues = attributeValues.Except(attrValues1.Values).ToList();
                                if (delSellAttrValues.Any())
                                {
                                    x.AttributeValues.RemoveAll(d => delSellAttrValues.Contains(d.Value));
                                }
                            }
                            if (attrValues2.Name.IsNotNullOrEmpty() && x.AttributeName == attrValues2.Name)
                            {
                                var delSellAttrValues = attributeValues.Except(attrValues2.Values).ToList();
                                if (delSellAttrValues.Any())
                                {
                                    x.AttributeValues.RemoveAll(d => delSellAttrValues.Contains(d.Value));
                                }
                            }
                            if (attrValues3.Name.IsNotNullOrEmpty() && x.AttributeName == attrValues3.Name)
                            {
                                var delSellAttrValues = attributeValues.Except(attrValues3.Values).ToList();
                                if (delSellAttrValues.Any())
                                {
                                    x.AttributeValues.RemoveAll(d => delSellAttrValues.Contains(d.Value));
                                }
                            }
                        });

                        ptSkus.RemoveAll(x => skuDeleteCodes.Contains(x.SkuCode));
                    }
                    pt.Ext.AttributeTypesJson = attrTypes.ToJson();
                    pt.Ext.SkuJson = ptSkus.ToJson();
                });

                // 更新
                _repository.BatchUpdate(ptProdutInfos);
            }
        }

        private void SetPtAttributeValueModel(PtProductInfo pt, PtProductInfoSkuModel x, List<RequiredSpecDetail> specs, string uniCode, string value, PtAttributeTypeModel attrTypes)
        {
            PtAttributeValueModel ptAttributeValueModel = new PtAttributeValueModel();
            ptAttributeValueModel.Value = value;
            ptAttributeValueModel.ValueUrl = x.ImageUrl;
            ptAttributeValueModel.ValueUrlKey = x.ImageUrl;
            ptAttributeValueModel.ImageObjectId = x.ImageObjectId.ToLong();
            ptAttributeValueModel.UniCode = uniCode;
            ptAttributeValueModel.OldValue = value;

            SetDefaultSellPropertyValue(ptAttributeValueModel, specs, attrTypes.SellPropertyId.ToLong());
            if (ptAttributeValueModel.IsRequiredTip)
            {
                pt.NeedUpdate = true;
            }

            // 2024-11-26：基础资料规格值匹配平台规格值成功后，取值会采用平台的规格值，平台资料sku的规格值，也需一同修改
            if (!string.IsNullOrWhiteSpace(x.Attribute.AttributeValue1) && x.Attribute.AttributeValue1.Equals(ptAttributeValueModel.Value, StringComparison.CurrentCultureIgnoreCase))
                x.Attribute.AttributeValue1 = ptAttributeValueModel.Value;

            if (!string.IsNullOrWhiteSpace(x.Attribute.AttributeValue2) && x.Attribute.AttributeValue2.Equals(ptAttributeValueModel.Value, StringComparison.CurrentCultureIgnoreCase))
                x.Attribute.AttributeValue2 = ptAttributeValueModel.Value;

            if (!string.IsNullOrWhiteSpace(x.Attribute.AttributeValue3) && x.Attribute.AttributeValue3.Equals(ptAttributeValueModel.Value, StringComparison.CurrentCultureIgnoreCase))
                x.Attribute.AttributeValue3 = ptAttributeValueModel.Value;

            attrTypes.AttributeValues.Add(ptAttributeValueModel);
        }

        /// <summary>
        ///（基础资料-自定义规则模式下）当基础资料sku删除，平台资料sku也要同步删除
        /// </summary>
        public void DeletePtSku(BaseProductSku baseProductSku, int fxUserId)
        {
            // 平台资料(可能有多个平台)
            List<PtProductInfo> ptProdutInfos = _repository.GetByProductUid(baseProductSku.BaseProductUid, fxUserId).ToList();
            if (ptProdutInfos == null || !ptProdutInfos.Any())
                return;

            foreach (PtProductInfo pt in ptProdutInfos)
            {
                var temp = _repository.GetByProductUid(pt.BaseProductUid, fxUserId, pt.PlatformType);
                pt.Ext = temp.Ext;

                var attrTypes = pt.Ext?.AttributeTypesJson.ToObject<List<PtAttributeTypeModel>>() ?? new List<PtAttributeTypeModel>();
                var ptSkus = pt.Ext?.SkuJson.ToObject<List<PtProductInfoSkuModel>>() ?? new List<PtProductInfoSkuModel>();

                var del_sku = ptSkus.FirstOrDefault(a => a.SkuCode == baseProductSku.SkuCode);
                var noDelSkus = ptSkus.Where(x => x.SkuCode != del_sku.SkuCode).ToList();

                var attrValues2 = noDelSkus.GroupBy(x => x.Attribute.AttributeName2, (name, values) => new { Name = name, Values = values.Select(x => x.Attribute.AttributeValue2).ToList() }).FirstOrDefault();
                var attrValues3 = noDelSkus.GroupBy(x => x.Attribute.AttributeName3, (name, values) => new { Name = name, Values = values.Select(x => x.Attribute.AttributeValue3).ToList() }).FirstOrDefault();
                var attrValues1 = noDelSkus.GroupBy(x => x.Attribute.AttributeName1, (name, values) => new { Name = name, Values = values.Select(x => x.Attribute.AttributeValue1).ToList() }).FirstOrDefault();

                attrTypes.ForEach(x =>
                {
                    var attributeValues = x.AttributeValues.Select(a => a.Value).ToList();
                    if (attrValues1.Name.IsNotNullOrEmpty() && x.AttributeName == attrValues1.Name)
                    {
                        var delSellAttrValues = attributeValues.Except(attrValues1.Values).ToList();
                        if (delSellAttrValues.Any())
                        {
                            x.AttributeValues.RemoveAll(d => delSellAttrValues.Contains(d.Value));
                        }
                    }
                    if (attrValues2.Name.IsNotNullOrEmpty() && x.AttributeName == attrValues2.Name)
                    {
                        var delSellAttrValues = attributeValues.Except(attrValues2.Values).ToList();
                        if (delSellAttrValues.Any())
                        {
                            x.AttributeValues.RemoveAll(d => delSellAttrValues.Contains(d.Value));
                        }
                    }
                    if (attrValues3.Name.IsNotNullOrEmpty() && x.AttributeName == attrValues3.Name)
                    {
                        var delSellAttrValues = attributeValues.Except(attrValues3.Values).ToList();
                        if (delSellAttrValues.Any())
                        {
                            x.AttributeValues.RemoveAll(d => delSellAttrValues.Contains(d.Value));
                        }
                    }
                });

                // ptSkus.RemoveAll(x => x.SkuCode == baseProductSku.SkuCode);
                pt.Ext.AttributeTypesJson = attrTypes.ToJson();
                pt.Ext.SkuJson = noDelSkus.ToJson();
                _repository.BatchUpdate(ptProdutInfos); // 更新
            }
        }

        #region 多商品铺货草稿
        /// <summary>
        /// 铺货草稿
        /// </summary>
        /// <param name="PtProductUniqueCodes"></param>
        /// <param name="fxUserId"></param>
        /// <param name="platformType"></param>
        /// <returns></returns>
        public List<PtProductInfo> GetPtInfoDrafts(List<string> PtProductUniqueCodes, int fxUserId, string platformType)
        {
            return _repository.GetPtInfoDrafts(PtProductUniqueCodes, fxUserId, platformType);
        }

        /*
        /// <summary>
        /// 智能类目推荐
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>

        public List<PtCategoryInfo> GetAutoCategorys(PtProductInfoModel model,string token)
        {
            var res = new List<PtCategoryInfo>();
            var checkTitle = string.Empty;
            var checkImageUrl = string.Empty;
            var checkImageTransitUrl = string.Empty;
            var preUrl = "Common/GetImageFile?objectKey";

            checkTitle = model.Subject;
            if (model.ProductImages.Count > 0)
            {
                checkImageUrl = model.ProductImages.FirstOrDefault()?.ImageUrl;
            }
            else if (model.DescriptionStr.Count > 0)
            {
                checkImageUrl = model.DescriptionStr.FirstOrDefault();
            }
            if (checkImageUrl.IsNotNullOrEmpty())
            {
                checkImageTransitUrl = checkImageUrl.Trim('/').StartsWith(preUrl) ?
                    $"{CustomerConfig.DefaultFenFaSystemUrl}{checkImageUrl}&platform={CustomerConfig.CloudPlatformType}&token={token}" :
                    $"{checkImageUrl}";
            }
            var platformCategorySupplierService = new PlatformCategorySupplierService();
            var autoCategoryInfo = platformCategorySupplierService.ProductCateForecast(
                checkTitle,
                checkImageTransitUrl).FirstOrDefault();

            if(autoCategoryInfo != null)
            {
                var categoryInfo = autoCategoryInfo.category_detail;
                if (categoryInfo != null)
                {
                    // 一级类目转换
                    if (categoryInfo.first_cid.IsNotNullOrEmpty() && categoryInfo.first_cid != "0")
                    {
                        var data = new PtCategoryInfo();
                        data.Level = 1;
                        data.Name = categoryInfo.first_cname;
                        data.CateId = categoryInfo.first_cid;
                        data.ParentId = null;
                        res.Add(data);
                    }
                    // 二级类目转换
                    if (categoryInfo.second_cid.IsNotNullOrEmpty() && categoryInfo.second_cid != "0")
                    {
                        var data = new PtCategoryInfo();
                        data.Level = 2;
                        data.Name = categoryInfo.second_cname;
                        data.CateId = categoryInfo.second_cid;
                        data.ParentId = categoryInfo.first_cid;
                        res.Add(data);
                    }
                    // 三级类目转换
                    if (categoryInfo.third_cid.IsNotNullOrEmpty() && categoryInfo.third_cid != "0")
                    {
                        var data = new PtCategoryInfo();
                        data.Level = 3;
                        data.Name = categoryInfo.third_cname;
                        data.CateId = categoryInfo.third_cid;
                        data.ParentId = categoryInfo.second_cid;
                        res.Add(data);
                    }
                    // 四级类目转换
                    if (categoryInfo.fourth_cid.IsNotNullOrEmpty() && categoryInfo.fourth_cid != "0")
                    {
                        var data = new PtCategoryInfo();
                        data.Level = 4;
                        data.Name = categoryInfo.fourth_cname;
                        data.CateId = categoryInfo.fourth_cid;
                        data.ParentId = categoryInfo.third_cid;
                        res.Add(data);
                    }
                }
            }
            return res;
        }
        */

        /// <summary>
        /// 类目预测记录
        /// </summary>
        /// <param name="subject">商品标题</param>
        /// <param name="shopIds">目标店铺</param>
        public CategoryPredictionResult CateForecastRecord(string subject, string platfromType, List<int> shopIds)
        {
            CategoryPredictionResult result = null;
            try
            {
                if (platfromType == PlatformType.TouTiao.ToString())
                {
                    if (shopIds != null && shopIds.Count > 0)
                    {
                        result = new PlatformCategorySupplierService().ProductCateForecastByShop(subject, shopIds);// 只调用平台接口，用来做记录
                    }
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"类目预测记录失败-CateForecastRecord：{ex.Message}. 堆栈：{ex.StackTrace}");
            }
            return result;
        }

        /// <summary>
        /// 类目预测     设置 categoryPredictionResult 和 IsInit
        /// </summary>
        /// <param name="model">入参</param>
        /// <param name="source">1：平台资料  2： 多商品铺货-编辑回显 3：接口调用</param>
        public void GetAutoCategorysV2(PtProductInfoModel model,int source)
        {
            try
            {
                if (model.PlatfromType == PlatformType.TouTiao.ToString())
                {
                    // model.AutoCategoryInfos = new List<CategoryResult>();
                    var categoryPredictionResult = new PlatformCategorySupplierService().ProductCateForecast(model.Subject, model.ShopId, "");
                    model.RecommendCategory = new ProductRecommendCategory(categoryPredictionResult.recommend_id);
                    
                    foreach (GetRecommendCategoryResult autoCategoryInfo in categoryPredictionResult.categoryDetails)
                    {
                        var categoryInfo = autoCategoryInfo.category_detail;
                        var categoryResult = new CategoryResult { QualificationStatus = autoCategoryInfo.qualification_status };

                        // 一级类目转换
                        if (categoryInfo.first_cid.IsNotNullOrEmpty() && categoryInfo.first_cid != "0")
                        {
                            var data = new PtCategoryInfo();
                            data.Level = 1;
                            data.Name = categoryInfo.first_cname;
                            data.CateId = categoryInfo.first_cid;
                            data.ParentId = null;
                            categoryResult.List.Add(data);
                        }
                        // 二级类目转换
                        if (categoryInfo.second_cid.IsNotNullOrEmpty() && categoryInfo.second_cid != "0")
                        {
                            var data = new PtCategoryInfo();
                            data.Level = 2;
                            data.Name = categoryInfo.second_cname;
                            data.CateId = categoryInfo.second_cid;
                            data.ParentId = categoryInfo.first_cid;
                            categoryResult.List.Add(data);
                        }
                        // 三级类目转换
                        if (categoryInfo.third_cid.IsNotNullOrEmpty() && categoryInfo.third_cid != "0")
                        {
                            var data = new PtCategoryInfo();
                            data.Level = 3;
                            data.Name = categoryInfo.third_cname;
                            data.CateId = categoryInfo.third_cid;
                            data.ParentId = categoryInfo.second_cid;
                            categoryResult.List.Add(data);
                        }
                        // 四级类目转换
                        if (categoryInfo.fourth_cid.IsNotNullOrEmpty() && categoryInfo.fourth_cid != "0")
                        {
                            var data = new PtCategoryInfo();
                            data.Level = 4;
                            data.Name = categoryInfo.fourth_cname;
                            data.CateId = categoryInfo.fourth_cid;
                            data.ParentId = categoryInfo.third_cid;
                            categoryResult.List.Add(data);
                        }
                        model.RecommendCategory.Results.Add(categoryResult);
                        // model.AutoCategoryInfos.Add(categoryResult);
                    }
                    //model.IsInit = model.AutoCategoryInfos.Any();
                    model.IsInit = model.RecommendCategory.Results.Any();
                }
            }
            catch (Exception ex)
            {
                if (source == 3 && ex.Message.Contains("授权过期")) throw;

                model.IsInit = false;
                Log.WriteError($"类目预测失败-GetAutoCategorysV2：{ex.Message}. 堆栈：{ex.StackTrace}");
            }
        }

        /// <summary>
        /// 库存修改
        /// </summary>
        /// <param name="model"></param>
        /// <param name="req"></param>
        /// <returns></returns>
        public bool UpdatePtInfoDraftStock(PtProductInfo model, UpdatePtProductInfoDraftStockCountModel req)
        {
            var extInfo = model.Ext;
            var skuInfo = extInfo.SkuJson;
            var skus = DeserializeObj<PtProductInfoSkuModel>(skuInfo, model.UniqueCode);
            if (skus.Count>0)
            {
                var stockCount = 0;
                var stockAdd = false;
                // 统一修改
                if (req.StockType == StockTypeEnum.CommonChange)
                {
                    stockCount = req.StockCommonCount ?? 0;
                    stockAdd = true;
                }
                // 其他修改
                else if (req.StockType == StockTypeEnum.OtherChange)
                {
                    stockCount = req.StockChangeCount ?? 0;

                    if (req.StockChange == 0)
                    {
                        stockAdd = true;
                    }
                    else
                    {
                        stockAdd = false;
                    }
                }
                foreach (var sku in skus)
                {
                    if (sku.IsDefaultPadding) continue;

                    if (req.StockType == StockTypeEnum.CommonChange)
                    {
                        sku.StockCount = stockCount;
                    }
                    else
                    {
                        if (stockAdd)
                        {
                            sku.StockCount = sku.StockCount + stockCount;
                        }
                        else
                        {
                            sku.StockCount = sku.StockCount - stockCount;
                        }
                    }

                    sku.StockCount = sku.StockCount < 0 ? 0 : sku.StockCount;
                }
            }
            model.Ext.SkuJson = skus.ToJson();
            BatchUpdate(new List<PtProductInfo> { model });
            return true;
        }


        /// <summary>
        /// 价格修改
        /// </summary>
        /// <param name="model"></param>
        /// <param name="req"></param>
        /// <returns></returns>
        public bool UpdatePtInfoDraftPrice(PtProductInfo model, UpdatePtProductInfoDraftModel req)
        {
            var skuInfo = model.Ext.SkuJson;
            var skus = DeserializeObj<PtProductInfoSkuModel>(skuInfo, model.UniqueCode);
            int? priceCornerFen = null;
            if (req.PriceUnitType == 1)
            {
                // 单位保留角分
                priceCornerFen = req.PriceCornerFen;
            }
            else
            {
                // 单位统一修改
                priceCornerFen = null;
            }
            if (skus.Count > 0)
            {
                // 公式修改
                if (req.PriceUpdateType == 1)
                {
                    var pricePercentage = (decimal)(req.PricePercentage.Value);
                    var priceIncrement = req.PriceIncrement.Value;
                    foreach (var sku in skus)
                    {
                        if (sku.IsDefaultPadding) continue;

                        decimal finalSalePrice = 0;
                        if (req.PriceType == 0)
                        {
                            // 售卖价
                            finalSalePrice = sku.SalePrice * pricePercentage / 100 + priceIncrement;
                            finalSalePrice = PriceTranslate(finalSalePrice, priceCornerFen);
                        }
                        if (req.PriceType == 1)
                        {
                            // 采购价
                            finalSalePrice = sku.SettlePrice * pricePercentage / 100 + priceIncrement;
                            finalSalePrice = PriceTranslate(finalSalePrice, priceCornerFen);
                        }
                        if (req.PriceType == 2)
                        {
                            // 分销价
                            finalSalePrice = sku.DistributePrice * pricePercentage / 100 + priceIncrement;
                            finalSalePrice = PriceTranslate(finalSalePrice, priceCornerFen);
                        }
                        sku.SalePrice = finalSalePrice; // 最终售价赋值
                    }
                }
                // 统一修改
                else
                {
                    var priceCommon = req.PriceCommon.Value;
                    foreach (var sku in skus)
                    {
                        if (sku.IsDefaultPadding) continue;

                        sku.SalePrice = PriceTranslate(priceCommon, priceCornerFen); // 最终售价赋值
                    }
                }
                model.Ext.SkuJson = skus.ToJson();
                BatchUpdate(new List<PtProductInfo> { model });
            }
            return true;
        }

        /// <summary>
        /// 保存或更新
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public bool UpdatePtInfoDraft(PtProductInfoModel model,int fxUserId)
        {
            var entity = TransferToPtProductInfo(model);
            var ptProductInfo = _repository.GetByCode(model.UniqueCode, fxUserId);
            if (ptProductInfo == null)
            {
                return false;
            }
            ptProductInfo.Subject = model.Subject;
            ptProductInfo.CategoryId = model.CategoryId;
            ptProductInfo.IsPublic = model.IsPublic;
            ptProductInfo.UpdateTime = DateTime.Now;
            ptProductInfo.IsWaitSyncBaseProduct = model.IsWaitSyncBaseProduct;
            ptProductInfo.Ext.CategoryJson = entity.Ext.CategoryJson;
            ptProductInfo.Ext.MainImageJson = JsonConvert.SerializeObject(model.ProductImages);
            ptProductInfo.Ext.Description = model.DescriptionStr == null ? string.Empty : string.Join(",", model.DescriptionStr);
            ptProductInfo.Ext.CategoryAttributes = model.CategoryAttribute;
            ptProductInfo.Ext.SkuJson = JsonConvert.SerializeObject(model.ProductInfoSkus);
            ptProductInfo.Ext.AttributeTypesJson = entity.Ext.AttributeTypesJson;
            ptProductInfo.Ext.UpdateTime = DateTime.Now;
            ptProductInfo.Ext.SettingJson = model.UserListingSetting.ToJson();
            ptProductInfo.NeedUpdate = false;
            //_repository.Update(ptProductInfo);
            Update(ptProductInfo);
            return true;
        }

        /// <summary>
        ///  铺货草稿同步到平台资料（铺自己）
        /// </summary>
        /// <param name="model"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public bool SyncPtProductInfo(PtProductInfoModel model,int fxUserId)
        {
            // 平台资料查询
            var baseProductUid = long.Parse(model.FromBaseProductUid);
            var entity = TransferToPtProductInfo(model);
            entity.IsWaitSyncBaseProduct = false;
            entity.NeedUpdate = false;

            var ptProductInfo = _repository.GetByProductUid(baseProductUid, fxUserId, model.PlatfromType);
            if (ptProductInfo == null)
            {
                // 新增
                Add(entity);
            }
            else
            {
                // 修改
                ptProductInfo.Subject = model.Subject;
                ptProductInfo.CategoryId = model.CategoryId;
                ptProductInfo.IsPublic = model.IsPublic;
                ptProductInfo.UpdateTime = DateTime.Now;
                ptProductInfo.IsWaitSyncBaseProduct = model.IsWaitSyncBaseProduct;
                ptProductInfo.Ext.CategoryJson = entity.Ext.CategoryJson;
                ptProductInfo.Ext.MainImageJson = JsonConvert.SerializeObject(model.ProductImages);
                ptProductInfo.Ext.Description = model.DescriptionStr == null ? string.Empty : string.Join(",", model.DescriptionStr);
                ptProductInfo.Ext.CategoryAttributes = model.CategoryAttribute;
                ptProductInfo.Ext.SkuJson = JsonConvert.SerializeObject(model.ProductInfoSkus);
                ptProductInfo.Ext.AttributeTypesJson = entity.Ext.AttributeTypesJson;
                ptProductInfo.Ext.UpdateTime = DateTime.Now;
                ptProductInfo.Ext.SettingJson = model.UserListingSetting.ToJson();
                ptProductInfo.IsWaitSyncBaseProduct = false;
                ptProductInfo.NeedUpdate = false;
                _repository.Update(ptProductInfo);
            }
            return true;
        }

        /// <summary>
        /// 价格角分指定
        /// </summary>
        /// <param name="price"></param>
        /// <returns></returns>
        public decimal PriceTranslate(decimal price, int? priceCornerFen)
        {
            if(priceCornerFen == null)
            {
                return price;
            }
            else
            {
                int integerPart = (int)price;
                decimal newDecimalPart = decimal.Parse("0." + priceCornerFen);
                return integerPart + newDecimalPart;
            }
        }
        #endregion

        /// <summary>
        /// 生成平台资料草稿
        /// </summary>
        /// <param name="setReq"></param>
        public List<PtProductInfoModel> BatchCreatePtInfoDrafts(BatchListingSetReq setReq)
        {
            Stopwatch stopwatch = Stopwatch.StartNew();
            var ptProductResult = BuildPtInfoModelTemp(setReq);

            stopwatch.Stop();
            Log.Debug(() => $"2.生成草稿 耗时：{stopwatch.ElapsedMilliseconds}ms");
            stopwatch.Restart();

            #region 根据配置进行计算售价
            if (setReq.UserListingSetting != null && setReq.UserListingSetting.BatchSettingValue != null)
            {
                var batchSettingValue = setReq.UserListingSetting.BatchSettingValue;
                var sortValues = batchSettingValue.PriceSetting.Split(','); // 价格设置的顺序 ByDouyinPrice, ByPurChasePrice, ByDistributorPrice

                // 铺厂家的商品时，默认按照ByPurChasePrice（不管是否为true）进行计算售价（但值取的是厂家对应基础资料的分销价）
                if (setReq.FromType == 3)
                    sortValues = new string[] { "ByDistributorPrice" };

                ListingSetting listingSetting = new ListingSetting() { BatchSettingValue = setReq.UserListingSetting.BatchSettingValue };
                var baseProductSkus = new List<BaseProductSku>();
                if (sortValues.Contains("ByPurChasePrice") || sortValues.Contains("ByDistributorPrice"))
                {
                    var baseProductUids = ptProductResult.Select(x => x.FromBaseProductUid.ToLong()).ToList();
                    var baseProductSkuService = new BaseProductSkuService();
                    var skuCodes = baseProductSkuService.GetDicSkuCodeByProductUids(baseProductUids)?.SelectMany(x => x.Value).ToList() ?? new List<string>();
                    baseProductSkus = baseProductSkuService.GetList(skuCodes, "Uid,SkuCode,CostPrice,SettlePrice,DistributePrice");
                }

                foreach (var item in ptProductResult)
                {
                    item.UserListingSetting = listingSetting;
                    item.UniqueCode = string.Empty;
                    item.FxUserId = SiteContext.GetCurrentFxUserId();

                    // 抖音资料的价格，先保存一份，抖音价格如果放到最后，用来取值
                    var PtPriceDic = item.ProductInfoSkus.ToDictionary(key => key.SkuCode, val => val.SalePrice);
                    int i = 1;

                    item.ProductInfoSkus = item.ProductInfoSkus.Where(s => !s.IsDefaultPadding).ToList();
                    foreach (string value in sortValues)
                    {
                        if (value == "ByDouyinPrice" && batchSettingValue.ByDouyinPrice) // 取抖音资料价格
                        {
                            // i=1，取值顺序把平台价格放到第一位，就判断全部sku的价格是否为0，如果为0，则继续取下一个价格设置。i!=1 代表平台价格设置不是第一位，分销价和采购价都没有取到值，这时需要使用平台价格
                            if (!(i == 1 && item.ProductInfoSkus.Count(a => a.SalePrice == 0) == item.ProductInfoSkus.Count))
                            {
                                foreach (var sku in item.ProductInfoSkus)
                                    sku.SalePrice = PtPriceDic[sku.SkuCode];

                                // 这里采用平台资料。走过循环设置之后sku售价，全部都是0，还是需要取下一个价格设置。除非是里面sku售价有不等于0的才 break
                                if (item.ProductInfoSkus.Count(a => a.SalePrice == 0) != item.ProductInfoSkus.Count)
                                {
                                    break;
                                }
                            }
                        }

                        if (value == "ByPurChasePrice" && batchSettingValue.ByPurChasePrice) // 取商品采购价
                        {
                            if (CalculateSellingPrice(batchSettingValue, baseProductSkus, item, "ByPurChasePrice"))
                                break;
                        }

                        if (value == "ByDistributorPrice" && batchSettingValue.ByDistributorPrice) // 取商品分销价
                        {
                            if (CalculateSellingPrice(batchSettingValue, baseProductSkus, item, "ByDistributorPrice"))
                                break;
                        }
                        i++;
                    }

                    foreach (var sku in item.ProductInfoSkus)
                    {
                        // 角分处理
                        if (!string.IsNullOrWhiteSpace(setReq.UserListingSetting.BatchSettingValue.MinuteOfArc) && sku.SalePrice > 0)
                        {
                            decimal integerPart = Math.Floor(sku.SalePrice);   // 获取整数部分

                            sku.SalePrice = integerPart + Convert.ToDecimal($"0.{setReq.UserListingSetting.BatchSettingValue.MinuteOfArc}"); // 构造新的值
                        }
                        // 库存处理
                        if (string.IsNullOrWhiteSpace(setReq.UserListingSetting.BatchSettingValue.Repertory))
                        {
                            // 和源库存保持一致
                            var stocks = new WareHouseService().GetStockBySkuCode(new List<string>() { sku.SkuCode });
                            sku.StockCount = stocks.FirstOrDefault()?.StockCount ?? 0;
                        }
                        else
                        {
                            sku.StockCount = int.Parse(setReq.UserListingSetting.BatchSettingValue.Repertory);
                        }
                    }
                }
            }
            #endregion

            stopwatch.Stop();
            Log.Debug(() => $"3.计算售价 耗时：{stopwatch.ElapsedMilliseconds}ms");
            return ptProductResult;
        }

        // 计算售价：循环所有sku，当能拿到价格时则跳出循环。此价格作为价格设置的结果。返回false：sku里面的价格都是0
        private bool CalculateSellingPrice(BatchListingSettingValue batchSettingValue, List<BaseProductSku> baseProductSkus, PtProductInfoModel item, string value)
        {
            int iteration = 0;
            foreach (var sku in item.ProductInfoSkus)
            {
                decimal salePrice_result = 0;
                if (value == "ByPurChasePrice") // 取商品采购价
                {
                    var calcPriced = sku.SettlePrice;
                    if (calcPriced == 0) salePrice_result = 0;
                    else
                    {
                        int.TryParse(batchSettingValue.ByPurChasePrice_Percent, out int percent);
                        salePrice_result = calcPriced * (percent / 100).ToDecimal() + batchSettingValue.ByPurChasePrice_Num;
                    }
                }

                if (value == "ByDistributorPrice") // 取商品分销价
                {
                    var calcPriced = sku.DistributePrice;
                    if (calcPriced == 0) salePrice_result = 0;
                    else
                    {
                        int.TryParse(batchSettingValue.ByDistributorPrice_Percent, out int percent);
                        salePrice_result = calcPriced * (percent / 100).ToDecimal() + batchSettingValue.ByDistributorPrice_Num;
                    }
                }

                if (salePrice_result == 0)
                    System.Threading.Interlocked.Increment(ref iteration);

                sku.SalePrice = salePrice_result;
            }

            if (item.ProductInfoSkus.Count == iteration)
                return false;

            return true;
        }

        public List<PtProductInfoModel> BuildPtInfoModelTemp(BatchListingSetReq setReq)
        {
            var productInfoDrafts = new List<PtProductInfoModel>();
            var supplierIds = new List<int>();
            foreach (long productUid in setReq.ProductUids)
            {
                if (setReq.FromType == 3)
                {
                    // 铺货厂家时，前端没有传递厂家的 FxUserId，所以需要根据 货盘Id拿到厂家FxUserId
                    var supplierProduct = new SupplierProductService().GetSupplierProductByUid(productUid, SiteContext.GetCurrentFxUserId());
                    if (supplierProduct == null)
                        continue;
                    setReq.FxUserId = supplierProduct.FxUserId;
                    supplierIds.Add(supplierProduct.FxUserId);
                }
                else
                {
                    setReq.FxUserId = SiteContext.GetCurrentFxUserId();
                }

                var req = new PtInfoReqModel { FromType = setReq.FromType, FxUserId = setReq.FxUserId, PlatformType = setReq.PlatformType, ProductUid = productUid };
                PtProductInfo ptProduct = GetPtProductInfoByCondition2(req);
                if (req.FromType == 0) /* 基础资料铺货*/
                {
                    PtProductInfoModel newPtProductModel = null;
                    if (ptProduct == null)
                    {
                        // 基础资料商品数据 + 基础资料sku数据
                        newPtProductModel = GeneratePtProductFormBaseProduct(req.ProductUid, req.FxUserId, req.PlatformType);
                        newPtProductModel.NeedUpdate = true; // 无平台资料时，应该是 未完善的状态（多商品铺货需求）
                        productInfoDrafts.Add(newPtProductModel);
                    }
                    else
                    {
                        bool combination = CalculateTags(ptProduct);
                        if (combination)
                        {
                            // 平台资料商品数据 + 基础资料sku数据
                            var baseProduct = new BaseProductSkuService().GetBaseProductDetail(new BaseProductDetailReqModel { BaseProductUid = productUid }, setReq.FxUserId);
                            var ptsku = TranBaseSkuToPtSku(baseProduct.Skus);
                            ptProduct.Ext.SkuJson = ptsku.ToJson();
                        }

                        var temp = TranPtModelFromPtEntity(ptProduct);
                        productInfoDrafts.Add(temp);
                    }
                }
                else if (req.FromType == 2)  /* 小站资料铺货*/
                {
                    if (ptProduct == null)
                    {
                        // 小站资料商品数据 + 基础资料sku数据
                        PtProductInfoModel newPtProductModel = GeneratePtProductFormSupplier(req.ProductUid, req.FxUserId, req.PlatformType, req.FromType);
                        newPtProductModel.NeedUpdate = true; // 无平台资料时，应该是 未完善的状态（多商品铺货需求）
                        long.TryParse(newPtProductModel.FromBaseProductUid, out long v);
                        var baseProduct = new BaseProductSkuService().GetBaseProductDetail(new BaseProductDetailReqModel { BaseProductUid = v }, setReq.FxUserId);
                        newPtProductModel.ProductInfoSkus = TranBaseSkuToPtSku(baseProduct.Skus);

                        var productAttribute = newPtProductModel.ProductInfoSkus.Select(x => x.Attribute).ToList();
                        newPtProductModel.AttributeTypes = GetPtAttributeTypes(productAttribute, null);

                        productInfoDrafts.Add(newPtProductModel);
                    }
                    else
                    {
                        bool combination = CalculateTags(ptProduct);
                        if (combination)
                        {
                            // 平台资料商品数据 + 基础资料sku数据
                            var baseProduct = new BaseProductSkuService().GetBaseProductDetail(new BaseProductDetailReqModel { BaseProductUid = productUid }, setReq.FxUserId);
                            var ptsku = TranBaseSkuToPtSku(baseProduct.Skus);
                            ptProduct.Ext.SkuJson = ptsku.ToJson();
                        }
                        var temp = TranPtModelFromPtEntity(ptProduct);
                        productInfoDrafts.Add(temp);
                    }
                }
                else if (req.FromType == 3)  /* 厂家货盘铺货 */
                {
                    if (ptProduct == null)
                    {
                        // 厂家货盘商品数据 + 厂家货盘sku数据
                        PtProductInfoModel newPtProductModel = GeneratePtProductFormSupplier(req.ProductUid, req.FxUserId, req.PlatformType, req.FromType);
                        newPtProductModel.NeedUpdate = true; // 无平台资料时，应该是 未完善的状态（多商品铺货需求）
                        productInfoDrafts.Add(newPtProductModel);
                    }
                    else
                    {
                        bool combination = CalculateTags(ptProduct);
                        if (combination)
                        {
                            // 平台资料商品数据 + 厂家货盘sku数据
                            SupplierProductService supplierProductService = new SupplierProductService();
                            var supplierProductSku = supplierProductService.GetSkuByUid(productUid, req.FxUserId);
                            var ptsku = TranSupplierSkuToPtSku(supplierProductSku, req.FxUserId);
                            ptProduct.Ext.SkuJson = ptsku.ToJson();
                        }

                        var temp = TranPtModelFromPtEntity(ptProduct);
                        productInfoDrafts.Add(temp);
                    }
                }
            }

            #region 等级分销价处理

            // 241128肖：新增等级分销价逻辑
            if (setReq.FromType == 3 && supplierIds.Any())
            {
                // 获取厂家的换算规则
                var supplierRuleModels = new MemberLevelService().GetSupplierRuleModel(supplierIds, SiteContext.Current.CurrentFxUserId);
                if (supplierRuleModels?.Count > 0)
                {
                    productInfoDrafts.ForEach(pt =>
                    {
                        if (supplierRuleModels.TryGetValue(pt.FromFxUserId, out var supplierRuleModel))
                        {
                            pt.ProductInfoSkus.ForEach(sku =>
                            {
                                var (newPrice, isChanged) = MemberLevelService.DistributePriceChange(sku.SettlePrice, supplierRuleModel.PriceRule, supplierRuleModel.FinalDistributePriceCorrectRule);
                                if (!isChanged) return;
                                sku.SettlePrice = newPrice ?? sku.SettlePrice;
                                sku.DistributePrice = newPrice ?? sku.DistributePrice;
                            });
                        }
                    });
                }
            }

            #endregion

            return productInfoDrafts;
        }

        // 计算标签。返回：true:需要组合资料。false：直接使用 ptProduct 资料
        private bool CalculateTags(PtProductInfo ptProduct)
        {
            bool combination = false;
            if (ptProduct.IsWaitSyncBaseProduct) // 1. 更新状态：待更新 -> 草稿的完善状态是 待完善 
            {
                ptProduct.NeedUpdate = true;
                combination = true;
            }
            else if (!ptProduct.IsWaitSyncBaseProduct && !ptProduct.NeedUpdate) // 2. 更新状态：已更新 完善状态：已完善 ->草稿的完善状态是 已完善
            {
                ptProduct.NeedUpdate = false;
            }
            else if (!ptProduct.IsWaitSyncBaseProduct && ptProduct.NeedUpdate) //  3. 更新状态：已更新 完善状态：待完善 ->草稿的完善状态是 待完善 
            {
                ptProduct.NeedUpdate = true;
            }

            // 采购价或分销价未填写，导致不能计算售价的，但其他数据全部正常（包括商品数据、sku数据）的商品，需归类为【已完善】
            // if (ptProduct.CategoryId)
            // {
            // }

            return combination;
        }

        public List<CategoryCheckAuth> CheckShopCateListAuth(List<CategoryCheckAuth> authReqModels)
        {
            try
            {
                var authResModels = new ConcurrentBag<CategoryCheckAuth>();
                var platformCategorySupplierService = new PlatformCategorySupplierService();
                Parallel.ForEach(authReqModels, new ParallelOptions { MaxDegreeOfParallelism = 3 }, authReq =>
                {
                    // 异步检测店铺类目发布权限
                    try
                    {
                        var data = platformCategorySupplierService.CheckShopCateAuth(authReq);
                        authResModels.Add(data);
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"检测发布规则异常：{ex.Message}，参数：{authReq.ToJson()}");
                    }
                });

                return authResModels.ToList();
            }
            catch (Exception ex)
            {
                Log.WriteError($"检测发布规则异常：{ex.Message}，参数：{ex.ToJson()}");
                return new List<CategoryCheckAuth>();
            }
        }
    }
}
