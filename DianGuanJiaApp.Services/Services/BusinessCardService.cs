using System;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Repository;
using System.Collections.Generic;
using System.Linq;
using DianGuanJiaApp.Data.Entity.SupplierProduct;
using DianGuanJiaApp.Data.FxModel;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.FxModel.SupplierProduct;
using DianGuanJiaApp.Data.Repository.SupplierProduct;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Services.Services.SupplierProduct;

namespace DianGuanJiaApp.Services.Services
{
    public class BusinessCardService : BaseService<BusinessCard>
    {
        private readonly BusinessCardRepository _businessCardRepository;
        private readonly FxUserShopRepository _fxUserShopRepository;
        private readonly UserFxService _userFxService;
        private readonly UserSupplierStatusRepository _userSupplierStatusRepository;
        private readonly BusinessCardRemarkRepository _businessCardRemarkService;
        private readonly ShippingFeeTemplateService _shippingFeeTemplateService = new ShippingFeeTemplateService();

        public BusinessCardService()
        {
            _businessCardRepository = new BusinessCardRepository();
            _fxUserShopRepository = new FxUserShopRepository();
            _businessCardRemarkService = new BusinessCardRemarkRepository();
            _userSupplierStatusRepository = new UserSupplierStatusRepository();
            _userFxService = new UserFxService();
            _baseRepository = _businessCardRepository;
        }

        /// <summary>
        /// 获取名片信息
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public List<BusinessCard> GetListByFxUserId(int fxUserId)
        {
            return _businessCardRepository.GetByFxUserId(fxUserId);
        }

        /// <summary>
        /// 获取名片信息
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public BusinessCardModel GetViewModel(int fxUserId)
        { 
            var businessCard = GetListByFxUserId(fxUserId);
            var resultModel = new BusinessCardModel();
            
            // 先获取默认模板，若不存在则返回空
            var temp = _shippingFeeTemplateService.LoadList(fxUserId, 1).FirstOrDefault(x => x.Status != 3);
            var templateType = string.Empty;
            if (temp != null)
            {
                var dbModel = _shippingFeeTemplateService.GetFirst(temp.Id, fxUserId);
                templateType = dbModel.TemplateType;
            }
            
            // 名片备注
            var remark = _businessCardRemarkService.GetRemarkByUserId(fxUserId, SiteContext.Current.CurrentFxUserId);
            if (string.IsNullOrEmpty(remark?.Remark) == false)
            {
                resultModel.Remark = remark.Remark;
            }

            foreach (var card in businessCard.Where(card => !string.IsNullOrEmpty(card.ContentJson)))
            {
                switch (card.ExtFlag)
                {
                    case BusinessCardExtFlag.CompanyInfo:
                        resultModel.CompanyInfoList = card.ContentJson.ToObject<List<BusinessCardCompanyInfo>>();
                        break;
                    case BusinessCardExtFlag.BusinessContact:
                        resultModel.BusinesscontactList = card.ContentJson.ToObject<List<BusinessCardContact>>();
                        break;
                    case BusinessCardExtFlag.AfterSaleAddress:
                        resultModel.AftersaleaddressList = card.ContentJson.ToObject<List<BusinessCardAfterSaleAddress>>();
                        break;
                    case BusinessCardExtFlag.PaymentInfo:
                        resultModel.PaymentinfoList = card.ContentJson.ToObject<List<BusinessCardPaymentInfo>>();
                        break;
                    case BusinessCardExtFlag.PlatformType:
                        resultModel.PlatformtypeList = card.ContentJson.ToObject<List<BusinessCardPlatformType>>();
                        break;
                    case BusinessCardExtFlag.SendAddress:
                        // 兼容新旧数据格式的地址获取
                        var historicalAddressList = card.ContentJson.ToObject<List<BusinessCardSendaddress>>();
                        resultModel.SendaddressList = GetBusinessCardSendAddressesWithCompatibility(card.FxUserId, historicalAddressList);
                        break;
                    case BusinessCardExtFlag.AfteSales:
                        resultModel.Aftesales = card.ContentJson.ToObject<List<BusinessCardAftesales>>().FirstOrDefault();
                        break;
                    case BusinessCardExtFlag.Shop:
                        resultModel.ShopList = card.ContentJson.ToObject<List<BusinessCardShop>>();
                        break;

                    // 以下是小站资料新增
                    case BusinessCardExtFlag.SendCourier:
                        resultModel.SendCourier = card.ContentJson.ToObject<string>();
                        break;
                    case BusinessCardExtFlag.TemplateType:
                        resultModel.TemplateType = card.ContentJson.ToObject<string>();
                        break;
                    case BusinessCardExtFlag.DeliveryTime:
                        resultModel.DeliveryTime = card.ContentJson.ToObject<string>();
                        break;
                    case BusinessCardExtFlag.ExpressBill:
                        resultModel.ExpressBill = card.ContentJson.ToObject<List<ExpressBill>>();
                        break;
                    case BusinessCardExtFlag.OrderCutoffTime:
                        resultModel.OrderCutoffTime = card.ContentJson.ToObject<OrderCutoffTime>();
                        break;
                    case BusinessCardExtFlag.AfterSalePromise:
                        resultModel.AfterSalePromise = card.ContentJson.ToObject<int?>();
                        break;
                    case BusinessCardExtFlag.AfterSaleTime:
                        resultModel.AfterSaleTime = card.ContentJson.ToObject<string>();
                        break;
                    case BusinessCardExtFlag.StationName:
                        resultModel.StationName = card.ContentJson.ToObject<string>();
                        break;
                    case BusinessCardExtFlag.Period:
                        resultModel.Period = card.ContentJson.ToObject<string>();
                        break;
                }
            }

            var user = _userFxService.GetUserFxAddressInfo(fxUserId);
            if (resultModel.AvatarUrl.IsNullOrEmpty())
            {
                resultModel.AvatarUrl = user.AvatarUrl;
            }

            if (resultModel.StationName.IsNullOrEmpty())
            {
                resultModel.StationName = user.Mobile;
            }
            resultModel.TemplateType = templateType;

            return resultModel; 
        }

        /// <summary>
        /// 更新小站资料
        /// </summary>
        /// <param name="model"></param>
        /// <param name="fxUserId"></param>
        public void UpdateOrAddStationInfo(BusinessCardModel model, int fxUserId)
        {
            var existingExt = GetListByFxUserId(fxUserId);

            // 公司信息
            UpdateBusinessCardInfo(model.CompanyInfoList, BusinessCardExtFlag.CompanyInfo);
            // 业务联系人
            UpdateBusinessCardInfo(model.BusinesscontactList, BusinessCardExtFlag.BusinessContact);
            // 售后地址
            UpdateBusinessCardInfo(model.AftersaleaddressList, BusinessCardExtFlag.AfterSaleAddress);
            // 收款信息
            UpdateBusinessCardInfo(model.PaymentinfoList, BusinessCardExtFlag.PaymentInfo);
            // 平台类项
            UpdateBusinessCardInfo(model.PlatformtypeList, BusinessCardExtFlag.PlatformType);
            // 发货地址
            UpdateBusinessCardInfo(model.SendaddressList, BusinessCardExtFlag.SendAddress);
            // 售后服务
            if (model.Aftesales.IsNotNullOrEmpty()) UpdateBusinessCardInfo(new List<BusinessCardAftesales> { model.Aftesales }, BusinessCardExtFlag.AfteSales);
            // 小站名称
            if (model.StationName.IsNotNullOrEmpty()) UpdateOrAddBusinessCard(model.StationName, BusinessCardExtFlag.StationName, fxUserId, existingExt);
            // 发货快递
            if (model.SendCourier.IsNotNullOrEmpty()) UpdateOrAddBusinessCard(model.SendCourier, BusinessCardExtFlag.SendCourier, fxUserId, existingExt);
            // 代发运费
            if (model.TemplateType.IsNotNullOrEmpty()) UpdateOrAddBusinessCard(model.TemplateType, BusinessCardExtFlag.TemplateType, fxUserId, existingExt);
            // 发货时效
            if (model.DeliveryTime.IsNotNullOrEmpty()) UpdateOrAddBusinessCard(model.DeliveryTime, BusinessCardExtFlag.DeliveryTime, fxUserId, existingExt);
            // 截单时间
            if (model.OrderCutoffTime != null) UpdateOrAddBusinessCard(model.OrderCutoffTime, BusinessCardExtFlag.OrderCutoffTime, fxUserId, existingExt);
            // 支持面单
            if (model.ExpressBill.IsNotNullOrEmpty()) UpdateOrAddBusinessCard(model.ExpressBill, BusinessCardExtFlag.ExpressBill, fxUserId, existingExt);
            // 售后处理时效
            if (model.AfterSaleTime.IsNotNullOrEmpty()) UpdateOrAddBusinessCard(model.AfterSaleTime, BusinessCardExtFlag.AfterSaleTime, fxUserId, existingExt);
            // 售后承诺
            if (model.AfterSalePromise.IsNotNullOrEmpty()) UpdateOrAddBusinessCard(model.AfterSalePromise, BusinessCardExtFlag.AfterSalePromise, fxUserId, existingExt);
            // 结算周期
            if (model.Period.IsNotNullOrEmpty()) UpdateOrAddBusinessCard(model.Period, BusinessCardExtFlag.Period, fxUserId, existingExt);

            return;

            void UpdateBusinessCardInfo<T>(List<T> list, string flag)
            {
                if (list != null)
                {
                    UpdateOrAddBusinessCard(list, flag, fxUserId, existingExt);
                }
            }
        }


        /// <summary>
        /// 更新对应扩展信息
        /// </summary>
        /// <param name="modelList"></param>
        /// <param name="extFlag"></param>
        /// <param name="fxUserId"></param>
        /// <param name="entList"></param>
        /// <returns></returns>
        public void UpdateOrAddBusinessCard<T>(List<T> modelList,string extFlag, int fxUserId, List<BusinessCard> entList = null)
        {
            var existingExt = entList == null
                ? GetListByFxUserId(fxUserId).FirstOrDefault(x => x.ExtFlag == extFlag)
                : entList.FirstOrDefault(x => x.ExtFlag == extFlag);

            // 将modelList里的每个Id属性按List长度重新赋值
            var idProp = typeof(T).GetProperty("Id");
            var i = 1;
            foreach (var model in modelList.Where(model => idProp != null))
            {
                if (idProp != null) idProp.SetValue(model, i++);
            }

            var cardAfterSaleAddress = (modelList as List<BusinessCardAfterSaleAddress>)?.FirstOrDefault();
            var cardSendAddressList = modelList as List<BusinessCardSendaddress>;
            var jsonObj = Newtonsoft.Json.JsonConvert.SerializeObject(modelList);
            var afterAddressSvc = new AfterSaleAddressService();

            if (existingExt != null)
            {
                existingExt.ContentJson = jsonObj;
                existingExt.UpdateTime = DateTime.Now;
                bool flag = Update(existingExt);

                // 处理发货地址同步到SupplierAddress表
                if (flag && extFlag == BusinessCardExtFlag.SendAddress && cardSendAddressList != null && cardSendAddressList.Any())
                {
                    try
                    {
                        // 获取现有的AddressCode（如果存在）
                        var existingAddressCode = GetBusinessCardAddressCode(fxUserId);

                        // 使用重构后的通用方法
                        SyncBusinessCardAddresses(fxUserId, cardSendAddressList, existingAddressCode, "更新");
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"同步名片发货地址到SupplierAddress表失败: {ex.Message}", LogModuleTypeEnum.MyStationCard);
                    }
                }

                if (flag && extFlag == BusinessCardExtFlag.AfterSaleAddress)
                {
                    #region 同步完善信息到厂家售后地址
                    if (cardAfterSaleAddress != null && !string.IsNullOrWhiteSpace(cardAfterSaleAddress.Street))
                    {
                        var req = new SyncSupperCompletionStatusReq() { TargetFxuserId = fxUserId, ContentJson = cardAfterSaleAddress.ToJson() };
                        var result = afterAddressSvc.CrossCloudAccess(req, () =>
                        {
                            return afterAddressSvc.SyncCompletionStatus(req);
                        }, "SyncSupperCompletionStatus", "跨云厂家售后地址-同步完善信息");
                    }
                    #endregion
                }
            }
            else
            {
                var newExt = new BusinessCard
                {
                    FxUserId = fxUserId,
                    ExtFlag = extFlag,
                    ContentJson = jsonObj,
                    CreateTime = DateTime.Now
                };

                int flag = Add(newExt);

                // 处理发货地址同步到SupplierAddress表
                if (flag > 0 && extFlag == BusinessCardExtFlag.SendAddress && cardSendAddressList != null && cardSendAddressList.Any())
                {
                    try
                    {
                        // 使用重构后的通用方法（新增记录时不传入现有地址编码）
                        SyncBusinessCardAddresses(fxUserId, cardSendAddressList, null, "新增");
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"同步新增名片发货地址到SupplierAddress表失败: {ex.Message}", LogModuleTypeEnum.MyStationCard);
                        // 地址同步失败不影响名片信息的正常创建
                    }
                }

                if (flag > 0 && extFlag == BusinessCardExtFlag.AfterSaleAddress)
                {
                    #region 同步完善信息到厂家售后地址
                    if (cardAfterSaleAddress != null && !string.IsNullOrWhiteSpace(cardAfterSaleAddress.Street))
                    {
                        var req = new SyncSupperCompletionStatusReq() { TargetFxuserId = fxUserId, ContentJson = cardAfterSaleAddress.ToJson() };
                        var result = afterAddressSvc.CrossCloudAccess(req, () =>
                        {
                            return afterAddressSvc.SyncCompletionStatus(req);
                        }, "SyncSupperCompletionStatus", "跨云厂家售后地址-同步完善信息");
                    }
                    #endregion
                }
            }
        }

        /// <summary>
        /// 更新对应扩展信息
        /// </summary>
        /// <param name="model"></param>
        /// <param name="extFlag"></param>
        /// <param name="fxUserId"></param>
        /// <param name="entList"></param>
        /// <returns></returns>
        public void UpdateOrAddBusinessCard<T>(T model, string extFlag, int fxUserId, List<BusinessCard> entList = null)
        {
            var existingExt = entList == null
                ? GetListByFxUserId(fxUserId).FirstOrDefault(x => x.ExtFlag == extFlag)
                : entList.FirstOrDefault(x => x.ExtFlag == extFlag);

            var jsonObj = Newtonsoft.Json.JsonConvert.SerializeObject(model);

            if (existingExt != null)
            {
                existingExt.ContentJson = jsonObj;
                existingExt.UpdateTime = DateTime.Now;
                Update(existingExt);
            }
            else
            {
                var newExt = new BusinessCard
                {
                    FxUserId = fxUserId,
                    ExtFlag = extFlag,
                    ContentJson = jsonObj,
                    CreateTime = DateTime.Now
                };
                Add(newExt);
            }
        }

        /// <summary>
        /// 获取小站的发货信息
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public ShipmentsInfo GetShipmentsInfo(int fxUserId)
        {
            var businessCard = GetListByFxUserId(fxUserId);
            var resultModel = new ShipmentsInfo();
            
            // 先获取默认模板，若不存在则返回空
            var temp = _shippingFeeTemplateService.LoadList(fxUserId, 1).FirstOrDefault(x => x.Status != 3);
            var templateType = string.Empty;
            if (temp != null)
            {
                var dbModel = _shippingFeeTemplateService.GetFirst(temp.Id, fxUserId);
                templateType = dbModel.TemplateType;
            }

            foreach (var card in businessCard.Where(card => !string.IsNullOrEmpty(card.ContentJson)))
            {
                switch (card.ExtFlag)
                {
                    case BusinessCardExtFlag.SendAddress:
                        // 兼容新旧数据格式的地址获取
                        var historicalAddressList2 = card.ContentJson.ToObject<List<BusinessCardSendaddress>>();
                        resultModel.SendaddressList = GetBusinessCardSendAddressesWithCompatibility(card.FxUserId, historicalAddressList2);
                        break;
                    case BusinessCardExtFlag.SendCourier:
                        resultModel.SendCourier = card.ContentJson.ToObject<string>();
                        break;
                    case BusinessCardExtFlag.TemplateType:
                        resultModel.TemplateType = card.ContentJson.ToObject<string>();
                        break;
                    case BusinessCardExtFlag.DeliveryTime:
                        resultModel.DeliveryTime = card.ContentJson.ToObject<string>();
                        break;
                    case BusinessCardExtFlag.ExpressBill:
                        resultModel.ExpressBill = card.ContentJson.ToObject<List<ExpressBill>>();
                        break;
                }
            }
            
            resultModel.TemplateType = templateType;

            return resultModel;
        }

        /// <summary>
        /// 获取业务联系人
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public List<BusinessCardContact> GetCardContacts(int fxUserId,bool needEncryptAccount = false)
        {
            var businessCard = GetListByFxUserId(fxUserId);

            foreach (var card in businessCard.Where(card => !string.IsNullOrEmpty(card.ContentJson)))
            {
                switch (card.ExtFlag)
                {
                    case BusinessCardExtFlag.BusinessContact:
                        var con = card.ContentJson.ToObject<List<BusinessCardContact>>();
                        if(needEncryptAccount && SiteContext.IsSubAccount() && !SiteContext.HasPermission(FxPermission.SupplierAccount))
                        {
                            con?.ForEach(c =>
                            {
                                c.Name = Utility.SubAccount.EncryptUtil.EncryptAccount(c.Name);
                                c.Tel = Utility.SubAccount.EncryptUtil.EncryptAccount(c.Tel);
                                c.ImageUrl = "";
                            });
                        }
                        return con;
                }
            }

            return new List<BusinessCardContact>();
        }


        /// <summary>
        /// 获取用户头像
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public string GetAvatarUrl(int fxUserId)
        {
            var user = _userFxService.GetUserFxAddressInfo(fxUserId);
            return user.AvatarUrl;
        }

        /// <summary>
        /// 获取公司信息
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public List<BusinessCardCompanyInfo> GetCompanyInfo(int fxUserId)
        {
            var businessCard = GetListByFxUserId(fxUserId);

            foreach (var card in businessCard.Where(card => !string.IsNullOrEmpty(card.ContentJson)))
            {
                switch (card.ExtFlag)
                {
                    case BusinessCardExtFlag.CompanyInfo:
                        return card.ContentJson.ToObject<List<BusinessCardCompanyInfo>>();
                }
            }

            return new List<BusinessCardCompanyInfo>();
        }

        /// <summary>
        /// 获取小站的售后信息
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public AfterSalesInfo GetAfterSalesInfo(int fxUserId)
        {
            var businessCard = GetListByFxUserId(fxUserId);
            var resultModel = new AfterSalesInfo();

            foreach (var card in businessCard.Where(card => !string.IsNullOrEmpty(card.ContentJson)))
            {
                switch (card.ExtFlag)
                {
                    case BusinessCardExtFlag.AfterSaleAddress:
                        resultModel.AftersaleaddressList = card.ContentJson.ToObject<List<BusinessCardAfterSaleAddress>>();
                        break;
                    case BusinessCardExtFlag.AfterSaleTime:
                        resultModel.AfterSaleTime = card.ContentJson.ToObject<string>();
                        break;
                    case BusinessCardExtFlag.AfterSalePromise:
                        resultModel.AfterSalePromise = card.ContentJson.ToObject<int?>();
                        break;
                    case BusinessCardExtFlag.AfteSales:
                        resultModel.Aftersales = card.ContentJson.ToObject<List<BusinessCardAftesales>>();
                        break;
                }
            }

            return resultModel;
        }
        
        /// <summary>
        /// 同步我的店铺
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public List<BusinessCardShop> SyncMyShop(int fxUserId)
        {
            var shopsList = _fxUserShopRepository.GetShopsByFxUserId(fxUserId, true);

            var resultList = new List<BusinessCardShop>();

            shopsList.ForEach(x =>
            {
                resultList.Add(new BusinessCardShop()
                {
                    ShopId = x.ShopId.ToString(),
                    ShopName = x.NickName,
                    Platform = x.PlatformTypeName,
                });

            });

            return resultList;
        }

        /// <summary>
        /// 获取厂家小站信息
        /// </summary>
        /// <param name="supplierFxUserId"></param>
        /// <param name="curFxUserId"></param>
        /// <returns></returns>
        public StationInfoBySupplierModel GetSupplierStation(int supplierFxUserId, int curFxUserId)
        {
            // 先确认是否有合作关系
            var supplierIds = _userSupplierStatusRepository
                .GetByFxUserIdAndStatus(curFxUserId, new List<int> { 1, 5, 6 }).Select(x => x.SupplierFxUserId)
                .ToList();
            if (!supplierIds.Contains(supplierFxUserId)) throw new LogicException("您未与该厂家建立合作关系，请检查！");

            var supplier = _businessCardRepository.GetByFxUserId(supplierFxUserId);
            var resultModel = new StationInfoBySupplierModel();

            // 获取合作反馈
            var evaluate = new CooperateEvaluateService().GetCooperateEvaluation(supplierFxUserId, curFxUserId);
            resultModel.CooperateEvaluateList = evaluate;

            // 获取合作备注
            var remark = _businessCardRemarkService.GetRemarkByUserId(supplierFxUserId, curFxUserId);
            resultModel.Remark = remark?.Remark;
            
            // 先获取默认模板，若不存在则返回空
            var temp = _shippingFeeTemplateService.LoadList(supplierFxUserId, 1).FirstOrDefault(x => x.Status != 3);
            var templateType = string.Empty;
            if (temp != null)
            {
                var dbModel = _shippingFeeTemplateService.GetFirst(temp.Id, supplierFxUserId);
                templateType = dbModel.TemplateType;
            }

            foreach (var card in supplier.Where(card => !string.IsNullOrEmpty(card.ContentJson)))
            {
                switch (card.ExtFlag)
                {
                    case BusinessCardExtFlag.CompanyInfo:
                        resultModel.CompanyInfoList = card.ContentJson.ToObject<List<BusinessCardCompanyInfo>>();
                        break;
                    case BusinessCardExtFlag.BusinessContact:
                        resultModel.BusinesscontactList = card.ContentJson.ToObject<List<BusinessCardContact>>();
                        break;
                    case BusinessCardExtFlag.AfterSaleAddress:
                        resultModel.AftersaleaddressList = card.ContentJson.ToObject<List<BusinessCardAfterSaleAddress>>();
                        break;
                    case BusinessCardExtFlag.PaymentInfo:
                        resultModel.PaymentinfoList = card.ContentJson.ToObject<List<BusinessCardPaymentInfo>>();
                        break;
                    case BusinessCardExtFlag.SendAddress:
                        // 兼容新旧数据格式的地址获取
                        var historicalAddressList3 = card.ContentJson.ToObject<List<BusinessCardSendaddress>>();
                        resultModel.SendaddressList = GetBusinessCardSendAddressesWithCompatibility(card.FxUserId, historicalAddressList3);
                        break;
                    case BusinessCardExtFlag.AfteSales:
                        resultModel.Aftesales = card.ContentJson.ToObject<List<BusinessCardAftesales>>().FirstOrDefault();
                        break;

                    // 以下是小站资料新增
                    case BusinessCardExtFlag.SendCourier:
                        resultModel.SendCourier = card.ContentJson.ToObject<string>();
                        break;
                    case BusinessCardExtFlag.TemplateType:
                        resultModel.TemplateType = card.ContentJson.ToObject<string>();
                        break;
                    case BusinessCardExtFlag.DeliveryTime:
                        resultModel.DeliveryTime = card.ContentJson.ToObject<string>();
                        break;
                    case BusinessCardExtFlag.ExpressBill:
                        resultModel.ExpressBill = card.ContentJson.ToObject<List<ExpressBill>>();
                        break;
                    case BusinessCardExtFlag.OrderCutoffTime:
                        resultModel.OrderCutoffTime = card.ContentJson.ToObject<OrderCutoffTime>();
                        break;
                    case BusinessCardExtFlag.AfterSalePromise:
                        resultModel.AfterSalePromise = card.ContentJson.ToObject<int?>();
                        break;
                    case BusinessCardExtFlag.AfterSaleTime:
                        resultModel.AfterSaleTime = card.ContentJson.ToObject<string>();
                        break;
                    case BusinessCardExtFlag.StationName:
                        resultModel.StationName = card.ContentJson.ToObject<string>();
                        break;
                    case BusinessCardExtFlag.Period:
                        resultModel.Period = card.ContentJson.ToObject<string>();
                        break;
                }
            }

            if(SiteContext.IsSubAccount() && !SiteContext.HasPermission(FxPermission.SupplierAccount))
            {
                resultModel.BusinesscontactList?.ForEach(b =>
                {
                    b.Name = Utility.SubAccount.EncryptUtil.EncryptAccount(b.Name);
                    b.Tel = Utility.SubAccount.EncryptUtil.EncryptAccount(b.Tel);
                    b.ImageUrl = "";
                });
            }

            var user = _userFxService.GetUserFxAddressInfo(supplierFxUserId, needEncryptAccount: true);
            if (resultModel.AvatarUrl.IsNullOrEmpty())
            {
                resultModel.AvatarUrl = user.AvatarUrl;
            }

            if (resultModel.StationName.IsNullOrEmpty())
            {
                resultModel.StationName = user.Mobile;
            }
            resultModel.TemplateType = templateType;

            return resultModel;
        }

        /// <summary>
        /// 获取商家小站信息
        /// </summary>
        /// <param name="agentFxUserId"></param>
        /// <param name="curFxUserId"></param>
        /// <returns></returns>
        public StationInfoByAgentModel GetAgentStation(int agentFxUserId, int curFxUserId)
        {
            // 先确认是否有合作关系
            var supplierIds = _userSupplierStatusRepository
                .GetByFxUserIdAndStatus(agentFxUserId, new List<int> { 1, 5, 6 }).Select(x => x.SupplierFxUserId)
                .ToList();
            if (!supplierIds.Contains(curFxUserId)) throw new LogicException("您未与该商家建立合作关系，请检查！");

            var supplier = _businessCardRepository.GetByFxUserId(agentFxUserId);
            var resultModel = new StationInfoByAgentModel();

            // 获取合作反馈
            var evaluate = new CooperateEvaluateService().GetCooperateEvaluation(agentFxUserId, curFxUserId);
            resultModel.CooperateEvaluateList = evaluate;

            // 获取合作备注
            var remark = _businessCardRemarkService.GetRemarkByUserId(agentFxUserId, curFxUserId);
            resultModel.Remark = remark?.Remark;

            foreach (var card in supplier.Where(card => !string.IsNullOrEmpty(card.ContentJson)))
            {
                switch (card.ExtFlag)
                {
                    case BusinessCardExtFlag.CompanyInfo:
                        resultModel.CompanyInfoList = card.ContentJson.ToObject<List<BusinessCardCompanyInfo>>();
                        break;
                    case BusinessCardExtFlag.BusinessContact:
                        resultModel.BusinesscontactList = card.ContentJson.ToObject<List<BusinessCardContact>>();
                        break;
                    case BusinessCardExtFlag.PaymentInfo:
                        resultModel.PaymentinfoList = card.ContentJson.ToObject<List<BusinessCardPaymentInfo>>();
                        break;
                    case BusinessCardExtFlag.SendAddress:
                        // 兼容新旧数据格式的地址获取
                        var historicalAddressList4 = card.ContentJson.ToObject<List<BusinessCardSendaddress>>();
                        resultModel.SendaddressList = GetBusinessCardSendAddressesWithCompatibility(card.FxUserId, historicalAddressList4);
                        break;
                    case BusinessCardExtFlag.PlatformType:
                        resultModel.PlatformtypeList = card.ContentJson.ToObject<List<BusinessCardPlatformType>>();
                        break;
                    case BusinessCardExtFlag.StationName:
                        resultModel.StationName = card.ContentJson.ToObject<string>();
                        break;
                    case BusinessCardExtFlag.Period:
                        resultModel.Period = card.ContentJson.ToObject<string>();
                        break;
                }
            }

            var user = _userFxService.GetUserFxAddressInfo(agentFxUserId);
            if (resultModel.AvatarUrl.IsNullOrEmpty())
            {
                resultModel.AvatarUrl = user.AvatarUrl;
            }

            if (resultModel.StationName.IsNullOrEmpty())
            {
                resultModel.StationName = user.Mobile;
            }

            return resultModel;
        }

        /// <summary>
        /// 根据用户ID和扩展标识获取BusinessCard记录
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="extFlag"></param>
        /// <returns></returns>
        private BusinessCard GetByFxUserIdAndExtFlag(int fxUserId, string extFlag)
        {
            return GetListByFxUserId(fxUserId).FirstOrDefault(x => x.ExtFlag == extFlag);
        }

        /// <summary>
        /// 获取名片的AddressCode
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        private string GetBusinessCardAddressCode(int fxUserId)
        {
            try
            {
                var addressCodeRecord = GetByFxUserIdAndExtFlag(fxUserId, BusinessCardExtFlag.SendAddressCode);
                if (addressCodeRecord != null && !string.IsNullOrEmpty(addressCodeRecord.ContentJson))
                {
                    return addressCodeRecord.ContentJson.Trim('"'); // 移除JSON字符串的引号
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"获取名片AddressCode失败: FxUserId={fxUserId}, Error={ex.Message}", LogModuleTypeEnum.MyStationCard);
            }
            return null;
        }

        /// <summary>
        /// 保存或更新名片的AddressCode
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="addressCode"></param>
        private void SaveBusinessCardAddressCode(int fxUserId, string addressCode)
        {
            try
            {
                if (string.IsNullOrEmpty(addressCode))
                    return;

                var existingRecord = GetByFxUserIdAndExtFlag(fxUserId, BusinessCardExtFlag.SendAddressCode);
                var jsonContent = Newtonsoft.Json.JsonConvert.SerializeObject(addressCode);

                if (existingRecord != null)
                {
                    // 更新现有记录
                    existingRecord.ContentJson = jsonContent;
                    existingRecord.UpdateTime = DateTime.Now;
                    Update(existingRecord);
                    Log.Debug(() => $"更新名片AddressCode: FxUserId={fxUserId}, AddressCode={addressCode}", LogModuleTypeEnum.MyStationCard);
                }
                else
                {
                    // 创建新记录
                    var newRecord = new BusinessCard
                    {
                        FxUserId = fxUserId,
                        ExtFlag = BusinessCardExtFlag.SendAddressCode,
                        ContentJson = jsonContent,
                        CreateTime = DateTime.Now
                    };
                    Add(newRecord);
                    Log.Debug(() => $"新增名片AddressCode: FxUserId={fxUserId}, AddressCode={addressCode}", LogModuleTypeEnum.MyStationCard);
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"保存名片AddressCode失败: FxUserId={fxUserId}, AddressCode={addressCode}, Error={ex.Message}", LogModuleTypeEnum.MyStationCard);
            }
        }

        /// <summary>
        /// 处理名片发货地址同步的通用方法
        /// </summary>
        /// <param name="fxUserId">用户ID</param>
        /// <param name="cardSendAddressList">发货地址列表</param>
        /// <param name="existingAddressCode">现有地址编码（可为null）</param>
        /// <returns>返回地址编码</returns>
        private string ProcessBusinessCardAddresses(int fxUserId, List<BusinessCardSendaddress> cardSendAddressList, string existingAddressCode = null)
        {
            if (cardSendAddressList == null || !cardSendAddressList.Any())
                return null;

            try
            {
                // 统一使用 SupplierProduct.SupplierAddressService
                var addressService = new SupplierAddressService();
                string addressCode = null;

                foreach (var sendAddress in cardSendAddressList.Where(addr => addr != null))
                {
                    var addressResult = addressService.SaveBusinessCardAddress(fxUserId, existingAddressCode, sendAddress);
                    if (addressResult.Success && !string.IsNullOrEmpty(addressResult.Data))
                    {
                        if (string.IsNullOrEmpty(addressCode))
                        {
                            addressCode = addressResult.Data; // 记录第一个地址的Code
                        }
                    }
                }

                return addressCode;
            }
            catch (Exception ex)
            {
                Log.WriteError($"处理名片发货地址失败: FxUserId={fxUserId}, Error={ex.Message}", LogModuleTypeEnum.MyStationCard);
                return null;
            }
        }

        /// <summary>
        /// 同步名片发货地址到SupplierAddress表的通用方法
        /// </summary>
        /// <param name="fxUserId">用户ID</param>
        /// <param name="cardSendAddressList">发货地址列表</param>
        /// <param name="existingAddressCode">现有地址编码（可为null）</param>
        /// <param name="operationType">操作类型（用于日志记录）</param>
        private void SyncBusinessCardAddresses(int fxUserId, List<BusinessCardSendaddress> cardSendAddressList, string existingAddressCode = null, string operationType = "")
        {
            var addressCode = ProcessBusinessCardAddresses(fxUserId, cardSendAddressList, existingAddressCode);

            if (!string.IsNullOrEmpty(addressCode))
            {
                SaveBusinessCardAddressCode(fxUserId, addressCode);
                Log.Debug(() => $"{operationType}名片发货地址同步成功: FxUserId={fxUserId}, AddressCode={addressCode}", LogModuleTypeEnum.MyStationCard);
            }
        }

        /// <summary>
        /// 获取名片发货地址信息（兼容新旧数据格式）
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="historicalAddressList">历史数据的发货地址列表</param>
        /// <returns></returns>
        private List<BusinessCardSendaddress> GetBusinessCardSendAddressesWithCompatibility(int fxUserId, List<BusinessCardSendaddress> historicalAddressList)
        {
            try
            {
                // 获取AddressCode
                var addressCode = GetBusinessCardAddressCode(fxUserId);

                if (!string.IsNullOrEmpty(addressCode))
                {
                    // 从SupplierAddress表获取地址信息
                    var addressService = new SupplierProduct.SupplierAddressService();
                    var addressList = addressService.GetBusinessCardAddresses(fxUserId, addressCode, historicalAddressList);
                    if (addressList != null && addressList.Any())
                    {
                        return addressList;
                    }
                }

                // 使用历史数据
                return historicalAddressList ?? new List<BusinessCardSendaddress>();
            }
            catch (Exception ex)
            {
                Log.WriteError($"获取名片地址信息失败，使用历史数据: FxUserId={fxUserId}, Error={ex.Message}", LogModuleTypeEnum.MyStationCard);
                return historicalAddressList ?? new List<BusinessCardSendaddress>();
            }
        }
    }
}
