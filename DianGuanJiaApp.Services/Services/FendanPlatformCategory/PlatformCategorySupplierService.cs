using DianGuanJiaApp.Data;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.FxModel.CategoryProduct;
using DianGuanJiaApp.Data.FxModel.Listing;
using DianGuanJiaApp.Data.MessageModel;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Model.BaseProduct;
using DianGuanJiaApp.Data.Model.LogModel;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Services.BaseProduct;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Services.Services.DataEventTracking;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.Helpers;
using MySql.Data.MySqlClient;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Services
{
    public partial class PlatformCategorySupplierService : BaseService<PlatformCategory>
    {
        private PlatformCategoryRepository _platformSelectCateRep;
        private PlatformCategorySupplierRepository _platformCateRep;

        public const string LOGFILENAME = "PlatformCategorySupplierCate.txt";

        public PlatformCategorySupplierService()
        {
            _platformSelectCateRep = new PlatformCategoryRepository();
            _platformCateRep = new PlatformCategorySupplierRepository();
        }

        /// <summary>
        /// 分单系统货盘-同步类目-类目属性-类目规则
        /// </summary>
        /// <param name="shopId">店铺Id</param>
        public void SyncFenDanCateGory(int shopId)
        {
            try
            {
                WriteInfoLog($"货盘类目数据同步开始 {DateTime.Now}");
                List<PlatformCategory> lastLevelCate = new List<PlatformCategory>();
                var shopSvc = new ShopService(PlatformAppScene.listing);
                var targetShops = shopSvc.GetShopByIds(new List<int>() { shopId });
                var shop = targetShops.FirstOrDefault();

                #region 业务日志1，处理前的数据
                var businessLogs = new List<BusinessLogModel>()
                {
                    new BusinessLogModel
                    {
                        MethodName ="SyncFnDdCateGory",
                        BatchId = "",
                        SourceType = "",
                        PlatformType = shop.PlatformType,
                        CreatorId = 0,
                        FxUserId = 0,
                        ShopId = shopId,
                        BusinessType = BusinessTypes.ListingTask.ToString(),
                        SubBusinessType = SubBusinessTypes.SyncCatesGory.ToString(),
                        BusinessId = shopId.ToString(),
                        Content = $"货盘类目数据同步开始 {DateTime.Now}",
                    }
                };

                //上传日志
                BusinessLogDataEventTrackingService.Instance.WriteLog(businessLogs);
                #endregion

                if (shop.PlatformType == PlatformType.TouTiao.ToString())
                {
                    // 还需要再查询1次，抖店跨云查询，保证token最准
                    shop = shopSvc.GetTouTiaoShopByIds(shopId).FirstOrDefault();

                    // 1.同步店铺的类目
                    var platformService = PlatformFactory.GetPlatformService(shop, scene: PlatformAppScene.listing);
                    lastLevelCate = SyncFdCates(shop, platformService);
                    WriteInfoLog($"抖店类目拉取完成 {DateTime.Now}");

                    Task propTask = Task.Run(() =>
                    {
                        // 2.抖店类目属性和属性值
                        SyncFnDdCategoryProp(shop, lastLevelCate);
                        WriteInfoLog($"抖店属性拉取完成 {DateTime.Now}");
                    });

                    Task ruleTask = Task.Run(() =>
                    {
                        // 3.抖店类目-发布规则
                        SyncFnDdCategoryPublishRule(shop, lastLevelCate);
                        WriteInfoLog($"抖店发布规则拉取完成 {DateTime.Now}");
                    });
                    Task.WaitAll(propTask, ruleTask);
                    Task.Run(() =>
                    {
                        // 【抖店】sku导航属性 这个就不用等待了
                        SyncNavigationCateProp(shop);
                        WriteInfoLog($"抖店sku导航属性完成 {DateTime.Now}");
                    });
                }
                else if (shop.PlatformType == PlatformType.Pinduoduo.ToString())
                {
                    var platformService = PlatformFactory.GetPlatformService(shop, scene: PlatformAppScene.listing);
                    lastLevelCate = SyncFdCatesByPdd(shop);
                    List<string> lastCateIds = lastLevelCate.Select(c => c.Name).ToList();

                    // List<string> lastCateIds = GetLastLevelCate(PlatformType.Pinduoduo); // 从数据库中获取末级类目.单独跑发布规则和属性，可以启用。速度更快

                    Task propTask = Task.Run(() =>
                    {
                        SyncCategoryPropByPdd(shop, lastCateIds, platformService);
                    });
                    Task ruleTask = Task.Run(() =>
                    {
                        SyncPublishRuleByPdd(shop, lastCateIds, platformService);
                    });
                    Task.WaitAll(propTask, ruleTask);
                }
                else if (shop.PlatformType == PlatformType.KuaiShou.ToString())
                {
                    var platformService = PlatformFactory.GetPlatformService(shop, scene: PlatformAppScene.listing);
                    lastLevelCate = SyncFdCatesByKs(shop);

                    List<string> lastCateIds = lastLevelCate.Select(c => c.Name).ToList();
                    // List<string> lastCateIds = GetLastLevelCate(PlatformType.KuaiShou);
                    
                    SyncCategoryPropByKs(shop, lastCateIds, platformService);

                    SyncPublishRuleByKs(shop, lastCateIds, platformService);
                }

                WriteInfoLog($"货盘类目数据同步完成 {DateTime.Now}");

                #region 业务日志2，处理后的数据
                var businessLogs2 = new List<BusinessLogModel>()
                {
                    new BusinessLogModel
                    {
                        MethodName ="SyncFnDdCateGory",
                        BatchId = "",
                        SourceType = "",
                        PlatformType = shop.PlatformType,
                        CreatorId = 0,
                        FxUserId = 0,
                        ShopId = shopId,
                        BusinessType = BusinessTypes.ListingTask.ToString(),
                        SubBusinessType = SubBusinessTypes.SyncCatesGory.ToString(),
                        BusinessId = shopId.ToString(),
                        Content = $"货盘类目数据同步完成 {DateTime.Now}",
                    }
                };

                //上传日志
                BusinessLogDataEventTrackingService.Instance.WriteLog(businessLogs2);
                #endregion
            }
            catch (Exception e)
            {
                var ex = e;
                if (e.InnerException != null)
                    ex = e.InnerException;

                ExceptionLogDataEventTrackingService.Instance.WriteLog(e, $"{shopId} 同步类目-属性-规则错误：{ex.Message}");
                WriteErrorLog($"{shopId} 同步类目-类目属性-属性规则错误：{ex.Message} 【堆栈】：{ex.StackTrace}");
                // HttpUtility.PostToRemind($"同步类目-类目属性-属性规则失败:{shopId.ToJson()}", ex);
            }
        }

        /// <summary>
        ///  【抖店】 类目
        /// </summary>
        /// <param name="shop">店铺</param>
        /// <param name="platformService">平台类</param>
        /// <returns></returns>
        private List<PlatformCategory> SyncFdCates(Shop shop, IPlatformService platformService)
        {
            if (shop.PlatformType != PlatformType.TouTiao.ToString())
                return new List<PlatformCategory>();

            Stopwatch sw = Stopwatch.StartNew();
            // 1. 拉取Api数据
            var apiCates = new DouDianPlatformCateService().GetNewCateByDouDianByShop(platformService);
            // var apiCates = new PlatformCategoryRepository().GetPlatformCategoriesByShopId(shop.Id, "TouTiao");//  本机测试，免的耗时太久
            sw.Stop();
            WriteInfoLog($"{DateTime.Now.ToString("HH:mm:ss")} 获取抖店类目耗时：{sw.ElapsedMilliseconds} 毫秒");
            sw.Restart();

            // 2.区分出增删改
            var dbCates = _platformCateRep.GetShopCates(shop);

            // 去重
            var newApicates = apiCates.GroupBy(x => x.Code).Select(x => x.FirstOrDefault()).ToList();

            WriteErrorLog($"获取抖店类目，apiCates:{apiCates.Count}个 去重后:{newApicates.Count}个 ");

            var tuple = CompareOldCatesV2(PlatformType.TouTiao.ToString(), newApicates, dbCates, true);

            Task.Run(() => SyncPtInfoByDd(shop, tuple));

            // 3.处理增删改--(系统的类目)
            if (tuple.Item1.Count > 0 || tuple.Item2.Count > 0 || tuple.Item3.Count > 0)//如果类目没变化，则不用处理，下面的判断也是
                BulkMulti(tuple.Item1, tuple.Item2, tuple.Item3);

            // 4.更新类目和店铺的关系表
            var cateShopRelations = newApicates.Select(f => new PlatformCateShopRelation()
            {
                CateId = f.CateId,
                RelationCode = f.Code,
                ShopId = shop.Id,
                CreateTime = DateTime.Now,
            }).ToList();

            if (cateShopRelations.Any())
            {
                // 获取当前店铺的头条类目信息有多少个，和现在同步下来的是一样的数量就不添加
                var shopCateNumber = _platformCateRep.GetCateShopRelationsNumber(shop.Id);
                if (shopCateNumber != cateShopRelations.Count)
                    _platformCateRep.SaveCateShopRelation(shop.Id, cateShopRelations);
            }

            WriteInfoLog($"{DateTime.Now.ToString("HH:mm:ss")} 【成功同步】抖店类目耗时：{sw.ElapsedMilliseconds} 毫秒");
            var lastLevelCate = newApicates.Where(a => !a.IsParent).ToList();
            WriteInfoLog($"{DateTime.Now.ToString("HH:mm:ss")} 找到本次抖店类目:{apiCates.Count}个 末级类目：{lastLevelCate.Count}个");
            return lastLevelCate;
        }

        /// <summary>
        /// 【抖店】类目变更同步到平台资料
        /// </summary>
        private void SyncPtInfoByDd(Shop shop, Tuple<List<PlatformCategory>, List<PlatformCategory>, List<PlatformCategory>> tuple)
        {
            try
            {
                // 检测上次是否执行完毕
                var redisKey = CacheKeys.SyncPtInfoTask.Replace("{Sid}", shop.Id.ToString())
                                                       .Replace("{Pt}", PlatformType.TouTiao.ToString());
                var value = RedisHelper.Get<string>(redisKey);
                if (!string.IsNullOrWhiteSpace(value))
                {
                    WriteInfoLog($"[抖店-类目变更同步到平台资料]:上次执行的任务未完成，本次取消. 缓存值：{value}");
                    return;
                }

                RedisHelper.Set(redisKey, DateTime.Now.ToString("yyyy-MM-dd~HH:mm:ss"), TimeSpan.FromDays(1));
                var fxUserId = SiteContext.Current.CurrentFxUserId;
                var ptSvc = new PtProductInfoService(string.Empty, fxUserId);

                // 更新的类目
                if (tuple.Item2.Any())
                {
                    List<string> update_cateIds = tuple.Item2.Select(f => f.CateId).ToList();
                    #region 【类目属性】

                    var dbCateList = new PlatformCategoryService().GetCategoryPropByList(PlatformType.TouTiao.ToString(), update_cateIds);
                    var apiCateList = SyncFnDdCategoryProp(shop, update_cateIds.Select(a => new PlatformCategory() { CateId = a }).ToList());

                    // 找出必填的类目属性
                    var db_required_prop = new Dictionary<string, List<DyCategoryPropInfoV2Model>>();
                    foreach (var item in dbCateList)
                    {
                        var attr = item.Content.ToObject<DyCategoryPropInfoV2Model>();
                        if (attr.required == 1 && db_required_prop.TryGetValue(attr.category_id.ToString(), out List<DyCategoryPropInfoV2Model> data))
                        {
                            data.Add(attr);
                        }
                        else
                        {
                            var li = new List<DyCategoryPropInfoV2Model>() { attr };
                            db_required_prop.Add(attr.category_id.ToString(), li);
                        }
                    }

                    var api_required_prop = apiCateList.ToDictionary(a => a.Key, b => b.Value.Where(a => a.required == 1).ToList());

                    // 计算出，删除的必填类目属性和新增的必填类目属性
                    (var del, var add) = GetDifferenceByDd(db_required_prop, api_required_prop);
                    HashSet<string> cateIdList = new HashSet<string>();
                    foreach (var item in del) cateIdList.Add(item.Key);
                    foreach (var item in add) cateIdList.Add(item.Key);

                    new PtProductInfoService().SyncPrductProperty(cateIdList, PlatformType.TouTiao);
                    #endregion

                    // 销售属性
                    // var dbRuleList = new PlatformCategoryService().GetPlatformCategoryPublishRuleList(PlatformType.TouTiao.ToString(), update_cateIds);
                    var apiRuleList = SyncFnDdCategoryPublishRule(shop, update_cateIds.Select(a => new PlatformCategory() { CateId = a }).ToList());
                    ptSvc.SyncSalePrductRuleByDd(apiRuleList);
                }
                // 删除的类目
                if (tuple.Item3.Any())
                {
                    var del_cateIds = tuple.Item3.Select(f => f.CateId).ToList();
                    ptSvc.DelPtDataCates(del_cateIds, PlatformType.TouTiao.ToString());
                }

                RedisHelper.Del(redisKey);
            }
            catch (Exception ex)
            {
                WriteErrorLog($"类目变更同步抖店失败：{ex.Message} 店铺：{shop.NickName} 堆栈：{ex.StackTrace}");
            }
        }

        /// <summary>
        /// 【抖店】计算删除和新增的类目属性
        /// </summary>
        private (Dictionary<string, List<DyCategoryPropInfoV2Model>>, Dictionary<string, List<DyCategoryPropInfoV2Model>>) GetDifferenceByDd(
            Dictionary<string, List<DyCategoryPropInfoV2Model>> sourceList,
            Dictionary<string, List<DyCategoryPropInfoV2Model>> targetList)
        {
            // 计算删除了的属性
            var del = new Dictionary<string, List<DyCategoryPropInfoV2Model>>();
            var add = new Dictionary<string, List<DyCategoryPropInfoV2Model>>();
            foreach (var source in sourceList)
            {
                if (targetList.TryGetValue(source.Key, out List<DyCategoryPropInfoV2Model> targetProps))
                {
                    if (!targetProps.Any(p => source.Value.Any(targetProp => targetProp.property_name == p.property_name)))
                    {
                        del.Add(source.Key, source.Value);
                    }
                }
            }

            // 计算新增的属性
            foreach (var target in targetList)
            {
                if (sourceList.TryGetValue(target.Key, out List<DyCategoryPropInfoV2Model> targetProps))
                {
                    if (!targetProps.Any(p => target.Value.Any(targetProp => targetProp.property_name == p.property_name)))
                    {
                        add.Add(target.Key, target.Value);
                    }
                }
            }

            return (del, add);
        }

        /// <summary>
        /// 寻找子级类目
        /// </summary>
        /// <param name="AllCates">所有类目</param>
        /// <param name="currCate">当前类吗</param>
        /// <param name="isDeletd">是否删除</param>
        /// <returns></returns>
        private Dictionary<string, PlatformCategory> ForCates(Dictionary<string, PlatformCategory> AllCates, PlatformCategory currCate, bool isDeletd = false)
        {
            if (!currCate.IsParent)
                return new Dictionary<string, PlatformCategory>();
            var cates = GetSubCates(AllCates, currCate, new Dictionary<string, PlatformCategory>(), isDeletd);
            return cates;
        }

        private Dictionary<string, PlatformCategory> GetSubCates(Dictionary<string, PlatformCategory> AllCates, PlatformCategory currCate, Dictionary<string, PlatformCategory> currCates, bool isDeletd = false)
        {
            var cates = AllCates.Where(db => db.Value.ParentId == currCate.CateId).ToDictionary(key => key.Key, value => value.Value);
            foreach (var item in cates)
            {
                currCates.Add(item.Key, item.Value);
            }
            foreach (var item in cates)
            {
                if (isDeletd)
                {
                    item.Value.Status = "deleted";
                    item.Value.UpdateTime = DateTime.Now;
                }
                var cCates = GetSubCates(AllCates, item.Value, currCates, isDeletd);
            }
            return currCates;
        }

        /// <summary>
        /// 【抖店】 属性
        /// </summary>
        /// <param name="shop">店铺</param>
        /// <param name="cates">类目</param>
        public Dictionary<string, List<DyCategoryPropInfoV2Model>> SyncFnDdCategoryProp(Shop shop, List<PlatformCategory> cates)
        {
            var hoit_props = new Dictionary<string, List<DyCategoryPropInfoV2Model>>();

            var zdPlatformSvc = new ZhiDianNewPlatformService(shop);
            Stopwatch sw = Stopwatch.StartNew();
            int current = 0;
            Parallel.ForEach(cates, new ParallelOptions() { MaxDegreeOfParallelism = 4 }, pitem =>
            {
                Interlocked.Increment(ref current);
                try
                {
                    var catePropList = zdPlatformSvc.GetCatePropInfoNew(pitem.CateId);
                    var conlist = new List<PlatformCategoryProp>();
                    foreach (var p in catePropList)
                    {
                        var de = new PlatformCategoryProp();
                        de.PlatformType = shop.PlatformType;
                        de.Name = p.property_name;
                        de.CateId = p.category_id.ToString();
                        de.Content = p.ToJson();
                        de.PropertyId = p.property_id.ToString();
                        de.Name = p.property_name;
                        de.CreateTime = DateTime.Now;
                        de.UpdateTime = DateTime.Now;
                        de.SetUnionCode();
                        conlist.Add(de);
                    }

                    if (!hoit_props.ContainsKey(pitem.CateId))
                        hoit_props.Add(pitem.CateId, catePropList);

                    _platformCateRep.SaveCategoryProp2(pitem.CateId, conlist, shop.PlatformType); //保存
                }
                catch (LogicException ex) // 如果是 Exception 异常，还是得抛出，由上层去处理
                {
                    WriteErrorLog($"{pitem.CateId}-{shop.ShopName} 请求抖店属性接口报错:{ex.Message}");

                    // 如果是授权到期，抛出异常，终止循环
                    if (ex.Message.Contains("授权过期"))
                        throw;
                }

                Thread.Sleep(500);
            });
            sw.Stop();
            WriteInfoLog($"{DateTime.Now.ToString("HH:mm:ss")} 抖店类目属性成功，耗时：{sw.ElapsedMilliseconds} 毫秒");
            return hoit_props;
        }

        /// <summary>
        /// 【抖店】类目发布规则
        /// </summary>
        /// <param name="shop">店铺</param>
        /// <param name="cates">类目</param>
        public Dictionary<string, ProductUpdateRuleInfoModel> SyncFnDdCategoryPublishRule(Shop shop, List<PlatformCategory> cates)
        {
            var hoit_roles = new Dictionary<string, ProductUpdateRuleInfoModel>();

            // var conlist = new ConcurrentBag<PlatformCategoryPublishRule>();
            var zdPlatformSvc = new ZhiDianNewPlatformService(shop);
            Stopwatch sw = Stopwatch.StartNew();
            int current = 0;
            Parallel.ForEach(cates, new ParallelOptions() { MaxDegreeOfParallelism = 4 }, pitem =>
            {
                Interlocked.Increment(ref current);

                try
                {
                    long l_cateId = long.Parse(pitem.CateId.ToString());
                    var puRulemodel = zdPlatformSvc.GetAddProductRuleByCateId(l_cateId);
                    // 规则删除了，会导致 末级类目没有对应规则，前端规格转换报错
                    // if (puRulemodel == null)
                    // {
                    //     _platformCateRep.DelCategoryPublishRule(pitem.CateId); // 平台方删除，DB同步删除
                    // }else
                    if (puRulemodel != null)
                    {
                        if (!hoit_roles.ContainsKey(pitem.CateId))
                            hoit_roles.Add(pitem.CateId, puRulemodel);

                        var de = new PlatformCategoryPublishRule();
                        de.PlatformType = shop.PlatformType;
                        de.CateId = pitem.CateId;
                        de.Content = puRulemodel.ToJson();
                        de.CreateTime = DateTime.Now;
                        de.UpdateTime = DateTime.Now;
                        de.SetUnionCode();

                        _platformCateRep.SaveCategoryPublishRule3(de); //保存
                        // conlist.Add(de);
                    }
                    Thread.Sleep(500);
                }
                catch (LogicException ex) // 如果是 Exception 异常，还是得抛出，由上层去处理
                {
                    WriteErrorLog($"{pitem.CateId}-{shop.ShopName} 请求抖店规则接口报错:{ex.Message}");

                    // 如果是授权到期，抛出异常，终止循环
                    if (ex.Message.Contains("授权过期"))
                        throw;
                }
            });
            sw.Stop();
            WriteInfoLog($"{DateTime.Now.ToString("HH:mm:ss")} 获取抖店类目类目发布规则耗时：{sw.ElapsedMilliseconds} 毫秒");
            return hoit_roles;
            // _platformCateRep.SaveCategoryPublishRule2(conlist.ToList(), shop.PlatformType); //保存
        }

        /// <summary>
        /// 【抖店】获取商品的审核状态
        /// </summary>
        public ListingProductStatusEnum? GetProductAuditList(int shopId, string product_id, int productStatus)
        {
            if (productStatus == 0 || string.IsNullOrWhiteSpace(product_id)) return null;

            if (!Enum.TryParse(productStatus.ToString(), out ListingProductStatusEnum statusEnum)) return null;

            if (statusEnum != ListingProductStatusEnum.PutStashAudit && statusEnum != ListingProductStatusEnum.ReadyForSaleAudit)
            {
                return null;
            }

            var targetShops = new ShopService(PlatformAppScene.listing).GetShopByIds(new List<int>() { shopId });
            var shop = targetShops.FirstOrDefault();
            PlatformFactory.GetPlatformService(shop, scene: PlatformAppScene.listing);

            // 指定审核状态返回商品列表：0-审核中 1-审核通过 2-审核拒绝
            var zdPlatformSvc = new ZhiDianNewPlatformService(shop);

            Stopwatch stopwatch = Stopwatch.StartNew();
            var check_Pass = zdPlatformSvc.GetProductAuditList(product_id, "1"); // 查询审核通过状态
            stopwatch.Stop();

            if (check_Pass.Any())
            {
                switch (statusEnum)
                {
                    case ListingProductStatusEnum.PutStashAudit: return ListingProductStatusEnum.PutStashPass;
                    case ListingProductStatusEnum.ReadyForSaleAudit: return ListingProductStatusEnum.ReadyForSalePass;
                    default: break;
                }
            }
            else
            {
                stopwatch.Restart();
                var check_SalePass = zdPlatformSvc.GetProductAuditList(product_id, "2"); // 查询审核驳回的状态
                if (check_SalePass.Any())
                {
                    switch (statusEnum)
                    {
                        case ListingProductStatusEnum.PutStashAudit: return ListingProductStatusEnum.PutStashReject;
                        case ListingProductStatusEnum.ReadyForSaleAudit: return ListingProductStatusEnum.ReadyForSaleReject;
                        default: break;
                    }
                }
            }

            return null;
        }

        /// <summary>
        /// 【抖店】店铺类目发布权限校验
        /// </summary>
        public CategoryCheckAuth CheckShopCateAuth(CategoryCheckAuth model)
        {
            var redisKey = CacheKeys.ShopCateAuth.Replace("{Sid}", model.Shop.ShopId).Replace("{Cid}", model.CateId);
            if (RedisHelper.Exists(redisKey))
            {
                model.IsAuth = RedisHelper.Get<bool>(redisKey);
            }
            else
            {
                var res = new ZhiDianNewPlatformService(model.Shop).PublishPreCheck(model.CateId);
                if (res.HasValue)
                {
                    model.IsAuth = res.Value;
                    RedisHelper.Set(redisKey, model.IsAuth, TimeSpan.FromHours(new Random().Next(2, 5)));
                }
                else
                {
                    model.IsAuth = false;
                }
            }
            return model;
        }

        /// <summary>
        /// 【抖店】店铺类目发布权限校验-删除缓存
        /// </summary>
        public bool ClearShopCateAuth(CategoryCheckAuth model)
        {
            var redisKey = CacheKeys.ShopCateAuth.Replace("{Sid}", model.Shop.ShopId).Replace("{Cid}", model.CateId);
            RedisHelper.Del(new List<string> { redisKey }.ToArray());
            return true;
        }

        /// <summary>
        /// 【抖店】商品类目预测-店铺调用记录
        /// </summary>
        /// <param name="subject">商品标题</param>
        /// <param name="shopIds">目标店铺</param>
        /// <returns></returns>
        public CategoryPredictionResult ProductCateForecastByShop(string subject, List<int> shopIds)
        {
            if (string.IsNullOrWhiteSpace(subject) || shopIds == null || shopIds.Count == 0) return null;

            CategoryPredictionResult result = null;
            var shopList = new ShopService(PlatformAppScene.listing).GetShopByIds(shopIds);
            foreach (var itemShop in shopList)
            {
                PlatformFactory.GetPlatformService(itemShop, scene: PlatformAppScene.listing);
                try
                {
                    result = new ZhiDianNewPlatformService(itemShop).GetRecommendCategory("category_infer", subject, string.Empty);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"类目预测调用记录失败：{ex.Message}. 堆栈：{ex.StackTrace}");
                }
            }
            return result;
        }

        /// <summary>
        /// 【抖店】商品类目预测
        /// </summary>
        /// <param name="subject">商品标题</param>
        /// <param name="picurl">商品主图</param>
        /// <returns></returns>
        public CategoryPredictionResult ProductCateForecast(string subject,int shopId, string picurl)
        {
            Shop shop = null;
            int targetShopId = shopId;
            bool islocal = CustomerConfig.IsDebug && CustomerConfig.IsLocalDbDebug;
            if (shopId == 0 && !islocal) // 使用系统级店铺
            {
                shop = new ShopService(PlatformAppScene.listing).GetShopNotExpTokenTouTiaoByFxUser(SiteContext.Current.CurrentFxUserId).FirstOrDefault();
            }
            else
            {
                if (islocal) targetShopId = 1687; // 本地调试，使用店铺

                shop = new ShopService(PlatformAppScene.listing).GetShopByIds(new List<int>() { targetShopId }).FirstOrDefault();
                if (shop == null) throw new LogicException("没有找到该店铺");
                PlatformFactory.GetPlatformService(shop, scene: PlatformAppScene.listing);
            }
            
            if (shop == null)
            {
                WriteInfoLog($"【类目预测】当前用户:{SiteContext.Current.CurrentFxUserId} 没有找到有权限的抖音店铺");
                return new CategoryPredictionResult();
            }

            var zdPlatformSvc = new ZhiDianNewPlatformService(shop);

            try
            {
                /*
                // 当前云一定是在 精选云
                if (!picurl.StartsWith("http"))
                {
                    picurl = CustomerConfig.AlibabaFenFaSystemUrl + picurl.Replace("Common/GetImageFile", "ImageFile/Get");
                }
                var material_name = picurl.ToShortMd5();

                // 1.上传到素材库
                var folder_id = zdPlatformSvc.SearchFolder();
                var materialModel = zdPlatformSvc.UploadImageSync(folder_id, picurl, $"{material_name}.jpg", 2, true);

                // 2.查询图片 materil_name
                var imgModel = zdPlatformSvc.QueryMaterialDetail(materialModel.material_id);
                var resultList = zdPlatformSvc.GetRecommendCategory("category_infer", subject, imgModel.audit_status == 3 ? imgModel.byte_url : string.Empty);
                */

                // 3. 类目预测
                var categoryPredictionResult = zdPlatformSvc.GetRecommendCategory("category_infer", subject, string.Empty);
                return categoryPredictionResult;
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("授权过期")) throw;
                Log.WriteError($"类目预测失败：{ex.Message}. 堆栈：{ex.StackTrace}");
                return new CategoryPredictionResult();
            }
        }

        /// <summary>
        /// 【抖店】同步计算sku导航属性
        /// </summary>
        /// <param name="shop">店铺</param>
        public void SyncNavigationCateProp(Shop shop)
        {
            try
            {
                var list = _platformCateRep.GetList();

                var zdPlatformSvc = new ZhiDianNewPlatformService(shop);
                foreach (var item in list)
                {
                    if (!string.IsNullOrWhiteSpace(item.Content))
                    {
                        var model = item.Content.ToObject<ProductUpdateRuleInfoModel>();
                        var navigationCateProp = GetNavigationCateProp(model, item.CateId, zdPlatformSvc);
                        if (navigationCateProp != null && navigationCateProp.Any())
                        {
                            // var pp3 = navigationCateProp.Select(p => p.Code).ToList();
                            // var pp4 = navigationCateProp.GroupBy(x => x.Code).Select(a => a.FirstOrDefault()).Count();

                            foreach (PlatformCategoryNavigation nav in navigationCateProp)
                            {
                                _platformCateRep.SaveNavigationCateProp(nav);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                WriteErrorLog($"{shop.ShopName} 同步计算sku导航属性异常:{ex.Message} {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 【抖店】计算sku导航属性的逻辑
        /// </summary>  
        public List<PlatformCategoryNavigation> GetNavigationCateProp(ProductUpdateRuleInfoModel productUpdateRuleInfo, string cateId, ZhiDianNewPlatformService service)
        {
            List<PlatformCategoryNavigation> navCateValues = new List<PlatformCategoryNavigation>();
            var displayStyle = new string[] { "cascader", "cascader_multi_select" };

            foreach (var item in productUpdateRuleInfo.product_spec_rule.required_spec_details)
            {
                if (displayStyle.Contains(item.value_display_style) && item.navigation_properties != null && item.navigation_properties.Any())
                {
                    var property_id = item.navigation_properties.FirstOrDefault()?.property_id;
                    if (property_id.HasValue)
                    {
                        // 获取导航样式属性
                        DyCascadeValueProperty property_values = service.GetCategoryPropertyValue(cateId, property_id.Value.ToString());
                        foreach (DyCascadeValuePropertyValue propval in property_values.property_values)
                        {
                            var listingnav = new PlatformCategoryNavigation(property_values.property_id, propval.property_value_id, propval.property_value_name, PlatformType.TouTiao.ToString());
                            listingnav.CateId = cateId;
                            listingnav.LowerLevelAttr = GetLowerLevelAttrData(cateId, property_values.property_id.ToString(), propval, service, 0);
                            navCateValues.Add(listingnav);
                        }
                    }
                }
            }

            return navCateValues;
        }

        /// <summary>
        /// 【抖店】计算sku导航属性的逻辑->获取下一级属性值
        /// </summary>
        /// <param name="cateId">类目id</param>
        /// <param name="property_id">属性Id</param>
        /// <param name="propval">属性值模型</param>
        /// <param name="service"></param>
        /// <param name="cascade_id">级联id</param>
        /// <returns></returns>
        private List<LowerLevelAttr> GetLowerLevelAttrData(string cateId, string property_id, DyCascadeValuePropertyValue propval, ZhiDianNewPlatformService service, long cascade_id)
        {
            // 获取导航样式属性值
            DyCascadeValue dyCascade = service.GetCascadeValue(cateId, property_id, new List<Tuple<string, string, string>>
                {
                    Tuple.Create(propval.property_value_id.ToString(), propval.property_value_name.ToString(), cascade_id.ToString())
                });

            List<LowerLevelAttr> resultList = new List<LowerLevelAttr>();
            foreach (var response in dyCascade.properties)
            {
                // 是否有下级
                if (response.has_sub_property)
                {
                    foreach (var property in response.property_values)
                    {
                        LowerLevelAttr lowerLevelAttr = new LowerLevelAttr(property.property_value_id, property.property_value_name);
                        resultList.Add(lowerLevelAttr);
                        if (property.cascade_id > 0)
                        {
                            var list = GetLowerLevelAttrData(cateId, property_id, propval, service, property.cascade_id);
                            resultList.AddRange(list);
                        }
                    }
                }
                else
                {
                    foreach (var property in response.property_values)
                    {
                        LowerLevelAttr lowerLevelAttr = new LowerLevelAttr(property.property_value_id, property.property_value_name);
                        resultList.Add(lowerLevelAttr);
                    }
                    return resultList;
                }
            }

            return resultList;
        }

        /// <summary>
        /// 【平台】品牌
        /// </summary>
        public List<BrandModel> GetPtBrand(GetBrandReqModel req)
        {
            var redisKey = CacheKeys.PtShopBrandKey.Replace("{Sid}", req.ShopId.ToString())
                                                   .Replace("{Cid}", req.CateId)
                                                   .Replace("{Pt}", req.PlatformType);
            var value = RedisHelper.Get<List<BrandModel>>(redisKey);
            if (value != null && req.IsCache == 0 && string.IsNullOrWhiteSpace(req.Name))
                return value;

            List<BrandModel> resultList = new List<BrandModel>();
            var skey = PlatformAppScene.listing;
            var shopSvc = new ShopService(skey);
            if (req.PlatformType == PlatformType.Pinduoduo.ToString())
            {
                resultList = GetBrandByPdd(req);
            }
            else if (req.PlatformType == PlatformType.KuaiShou.ToString())
            {
                resultList = GetBrandByKs(req);
            }
            else if (req.PlatformType == PlatformType.TouTiao.ToString())
            {
                if (req.ShopId == 0)
                {
                    // 平台资料场景。通过末级类目Id找到所属店铺
                    int shopid = _platformSelectCateRep.GetCurrentShop(req.CateId, req.PlatformType);
                    Shop shop = shopSvc.GetTouTiaoShopByIds(shopid)?.FirstOrDefault();

                    if (shop == null)
                    {
                        var shopList = shopSvc.GetShopNotExpTokenTouTiao(); // 随机找个没过期的店铺去拉
                        resultList = GetShopBrand(req, skey, shopList);
                    }
                    else
                    {
                        // 使用类目归属的店铺去拉取品牌
                        resultList = GetShopBrand(req, skey, new List<Shop>() { shop });
                        if (resultList.Count == 0)
                        {
                            Log.WriteLine($"使用类目归属的店铺({shop.Id})去拉取品牌失败，改成其他店铺去拉取:");
                            var shopList = shopSvc.GetShopNotExpTokenTouTiao(); // 随机找个没过期的店铺去拉
                            Log.WriteLine($"使用其他店铺去拉取品牌成功，找到店铺：{shopList.Count}个，接口入参:{req.ToJson()}");
                            resultList = GetShopBrand(req, skey, shopList);
                        }
                    }
                }
                else
                {
                    Shop shop = shopSvc.GetTouTiaoShopByIds(req.ShopId).FirstOrDefault();
                    resultList = GetShopBrand(req, skey, new List<Shop>() { shop });
                }
            }

            if (string.IsNullOrWhiteSpace(req.Name))
                RedisHelper.Set(redisKey, resultList, TimeSpan.FromHours(new Random().Next(2, 5)));
            return resultList;
        }

        /// <summary>
        /// 【抖店】品牌
        /// </summary>
        private List<BrandModel> GetShopBrand(GetBrandReqModel req, string skey, List<Shop> shopList)
        {
            for (int i = 0; i < shopList.Count; i++)
            {
                var mshop = shopList[i];
                var zhidSvc = PlatformFactory.GetPlatformService(mshop, scene: skey);
                try
                {
                    var toutiaoPtService = zhidSvc as ZhiDianNewPlatformService;
                    var result = toutiaoPtService.GetBrandListNewV2(req.CateId, out bool auth_req);
                    var rlist = result.Item1.Where(a => !string.IsNullOrWhiteSpace(a.BrandNameCN))
                        .Select<BrandInfo, BrandModel>(a => new BrandModel(a.BrandNameCN)).ToList();
                    return rlist;
                }
                catch (LogicException ex)
                {
                }
            }
            return new List<BrandModel>();
        }

        /// <summary>
        /// 【平台】获取类目属性值
        /// </summary>
        /// <param name="reqModel"></param>
        public List<GetCategoryPropValResModel> GetCategoryPropVal(GetCategoryPropValReqModel reqModel)
        {
            // 先查库
            string redisKey = $"CategoryPropVal_{reqModel.PlatformType}_{reqModel.CateId}_{reqModel.PropertyId}";
            List<GetCategoryPropValResModel> resList = new List<GetCategoryPropValResModel>();
            if (reqModel.PropertyVal.IsNullOrEmpty())
            {
                resList = RedisHelper.Get<List<GetCategoryPropValResModel>>(redisKey);
                if (resList != null && resList.Any()) return resList;
            }

            resList = _platformSelectCateRep.GetCategoryPropVal(reqModel);
            if (resList.Any())
            {
                RedisHelper.Set(redisKey, resList, TimeSpan.FromHours(new Random().Next(5, 7)));
                return resList;
            }

            var shopSvc = new ShopService(PlatformAppScene.listing);
            if (reqModel.PlatformType == PlatformType.KuaiShou.ToString())
            {
                var shop = shopSvc.GetLatestPlatformShop(PlatformType.KuaiShou);
                var platformService = PlatformFactory.GetPlatformService(shop, scene: PlatformAppScene.listing);

                resList = SyncCategoryPropValByKs(reqModel.CateId, reqModel.PropertyId, reqModel.PropertyVal, platformService);
            }

            // 入库
            if (resList.Any())
            {
                _platformCateRep.SaveCategoryPropVal(resList);
            }

            return resList;
        }

        /// <summary>
        /// 【平台】商品资质
        /// 返回 string 不同的平台返回不同的json格式
        /// </summary>
        public string GetProductQualification(GetProductQualificationReqModel reqModel)
        {
            // 先查库
            ProductQualification dbmodel = _platformSelectCateRep.GetProductQualification(reqModel).FirstOrDefault();

            if (reqModel.PlatformType == PlatformType.KuaiShou.ToString())
            {
                if (dbmodel != null) return dbmodel.Content;

                var result = ProductQualification(reqModel.CateId);
                if (result.Any())
                {
                    dbmodel = new ProductQualification()
                    {
                        CateId = reqModel.CateId,
                        Content = result.ToJson(),
                        PlatformType = reqModel.PlatformType,
                        CreateTime = DateTime.Now,
                    };
                    dbmodel.SetUnionCode();
                    _platformCateRep.SaveProductQualification(dbmodel);
                }

                return result.ToJson();
            }
            else if (reqModel.PlatformType == PlatformType.XiaoHongShu.ToString()) { }

            return string.Empty;
        }

        #region 平台公共方法
        /// <summary>
        /// 区分出增删改的类目落库
        /// </summary>
        /// <returns>1.插入 2.更新 3.删除</returns>
        public Tuple<List<PlatformCategory>, List<PlatformCategory>, List<PlatformCategory>> SaveCateReturnLastCate(Shop shop, List<PlatformCategory> apiCates,
            List<PlatformCategory> lastLevelCate, PlatformType platformType)
        {
            // 1.获取已有的类目
            var dbCates = _platformCateRep.GetShopCates(shop);

            // 2.去重
            var newApicates = apiCates.GroupBy(x => x.Code).Select(x => x.FirstOrDefault()).ToList();

            WriteInfoLog($"获取{platformType}类目，apiCates:{apiCates.Count()}个 去重后:{newApicates.Count}个 ");

            // 3.区分出增删改
            var tuple = CompareOldCatesV2(platformType.ToString(), newApicates, dbCates, true);

            // 4.处理增删改--(系统的类目)
            if (tuple.Item1.Count > 0 || tuple.Item2.Count > 0 || tuple.Item3.Count > 0)//如果类目没变化，则不用处理，下面的判断也是
                BulkMulti(tuple.Item1, tuple.Item2, tuple.Item3);

            // 5.更新类目和店铺的关系表
            var cateShopRelations = newApicates.Select(f => new PlatformCateShopRelation()
            {
                CateId = f.CateId,
                RelationCode = f.Code,
                ShopId = shop.Id,
                CreateTime = DateTime.Now,
            }).ToList();

            if (cateShopRelations.Any())
            {
                // 获取当前店铺的平台类目信息有多少个，和现在同步下来的是一样的数量就不添加
                var shopCateNumber = _platformCateRep.GetCateShopRelationsNumber(shop.Id);
                if (shopCateNumber != cateShopRelations.Count)
                    _platformCateRep.SaveCateShopRelation(shop.Id, cateShopRelations);
            }

            lastLevelCate.AddRange(newApicates.Where(a => !a.IsParent));
            return tuple;
        }

        /// <summary>
        /// 区分出插入，更新，删除
        /// 对比的是系统类目
        /// </summary>
        /// <param name="apiCates">api新拉的数据</param>
        /// <param name="dbCates">Db旧数据</param>
        /// <param name="isDelNotExists">api新拉数据和db旧数据求差集并删除</param>
        /// <returns>1.插入 2.更新 3.删除</returns>
        public Tuple<List<PlatformCategory>, List<PlatformCategory>, List<PlatformCategory>> CompareOldCatesV2(string platformType,
            List<PlatformCategory> apiCates, List<PlatformCategory> dbCates, bool isDelNotExists = false)
        {
            if (apiCates == null && apiCates.Any() == false)
                throw new LogicException("未传入新类目数据比较");

            var addList = new List<PlatformCategory>();
            var delList = new Dictionary<string, PlatformCategory>();
            var upList = new List<PlatformCategory>();
            //当前时间
            var cuurTime = DateTime.Now;
            //查询数据库的旧类目数据

            var oldCates = dbCates.ToDictionary(key => key.CateId, value => value);
            //接口和旧数据比较,数据库存在的话判断名字和上一级类目id是否一致,不一致则删除,一致就更新, 数据库不存在就插入
            apiCates.OrderBy(d => d.Level).ToList().ForEach(c =>
            {
                if (delList.Any(a => a.Key == c.CateId))
                    return;

                var dbcate = oldCates.FirstOrDefault(o => o.Key == c.CateId).Value;
                if (dbcate != null)
                {
                    //存在数据,判断父级是否一致
                    if (dbcate.ParentId == c.ParentId)
                    {
                        //一致在判断名字是否一致,不一致则更新
                        if (dbcate.Name != c.Name || dbcate.Level != c.Level)
                        {
                            dbcate.Name = c.Name;
                            dbcate.Level = c.Level;
                            dbcate.UpdateTime = cuurTime;
                            upList.Add(dbcate);
                        }
                    }
                    else
                    {
                        //不一致删除
                        //delList.Add(dbcate.CateId, dbcate);
                        if (!delList.ContainsKey(dbcate.CateId))
                            delList.Add(dbcate.CateId, dbcate);
                        //找出所有子级
                        var chiCates = ForCates(oldCates, dbcate);
                        foreach (var item in chiCates)
                        {
                            if (!delList.ContainsKey(item.Key))
                                delList.Add(item.Key, item.Value);
                        }
                    }
                }
                else
                {
                    c.CreateTime = cuurTime;
                    addList.Add(c);
                }
            });

            if (isDelNotExists == true)
            {
                var apiCateLookup = apiCates.ToLookup(s => s.CateId);
                foreach (var item in oldCates)
                {
                    if (delList.ContainsKey(item.Key))
                        continue;
                    if (apiCateLookup[item.Key].FirstOrDefault() == null)
                    {
                        delList.Add(item.Key, item.Value);
                    }
                }
            }

            WriteInfoLog($"平台：{platformType},新增类目:{addList.Count}个,更新类目:{upList.Count}个,删除类目:{delList.Count}个，接口类目数据;{apiCates.Count}个,db:{oldCates.Count}个");
            return Tuple.Create(addList, upList, delList.Values.ToList());
        }

        private void BulkMulti(List<PlatformCategory> addlist, List<PlatformCategory> uplist, List<PlatformCategory> dellist)
        {
            try
            {
                if (dellist != null && dellist.Any())
                    _platformCateRep.BulkDelete(dellist);

                if (uplist != null && uplist.Any())
                    _platformCateRep.BulkUpdate(uplist);

                if (addlist != null && addlist.Any())
                    _platformCateRep.BulkAdd(addlist);
            }
            catch (MySqlException ex) when (ex.Number == 1062) // 错误码1062代表Duplicate entry
            {
                WriteInfoLog($"捕获到唯一性约束冲突异常:{ex.Message}");
            }
            catch (Exception ex)
            {
                LogHelper.Instance.WriteLog(ex, $"BulkMulti类目执行插入，更新，删除失败,错误信息：{ex.Message}");

                // HttpUtility.PostToRemind($"BulkMulti类目执行插入，更新，删除失败,错误信息：\r\n{ex.Message}", ex);
                throw;
            }
        }

        private PlatformCategory ToPlatformCate(ListingCategory f, string shopPt)
        {
            var result = new PlatformCategory()
            {
                PlatformType = shopPt,
                CateId = f.Id,
                Name = f.Name,
                ParentId = f.Pid,
                Status = f.Status ?? "",
                SortOrder = Convert.ToInt32(f.SortOrder),
                IsParent = f.IsParent,
                Level = Convert.ToInt32(f.Level),
                CreateTime = DateTime.Now,
                UpdateTime = DateTime.Now
            };
            return result;
        }

        public List<string> GetLastLevelCate(PlatformType platformType)
        {
            var lastLevelCates = _platformSelectCateRep.GetLastLevelCate(platformType);  // 找出末级类目
            return lastLevelCates;
        }

        public void WriteInfoLog(string msg)
        {
            Log.Debug(() => msg, LOGFILENAME);
        }

        public void WriteErrorLog(string errorMsg)
        {
            Log.WriteError(errorMsg, LOGFILENAME);
        }

        public void ProcessFxListingCateChangeMessage(ListingCateChangeMessageModel listingCompleteMsgs)
        {
            if (listingCompleteMsgs == null) return;

            var shopjson = DES.DecryptDES(listingCompleteMsgs.Shop, CustomerConfig.LoginCookieEncryptKey);
            Shop shop = shopjson.ToObject<Shop>();
            List<PlatformCategory> updateCateList = listingCompleteMsgs.UpdateCateList.Select(a => new PlatformCategory() { CateId = a }).ToList();
            List<PlatformCategory> delCateList = listingCompleteMsgs.DelCateList.Select(a => new PlatformCategory() { CateId = a }).ToList();

            var tuple = new Tuple<List<PlatformCategory>, List<PlatformCategory>, List<PlatformCategory>>(new List<PlatformCategory>(), updateCateList, delCateList);
            if (listingCompleteMsgs.PlatformType == PlatformType.Pinduoduo)
                Task.Run(() => SyncPtInfoByPddV2(shop, tuple));
            else if (listingCompleteMsgs.PlatformType == PlatformType.TouTiao)
                Task.Run(() => SyncPtInfoByDd(shop, tuple));
            else if (listingCompleteMsgs.PlatformType == PlatformType.KuaiShou)
                Task.Run(() => SyncPtInfoByKs(shop, tuple));
        }
        #endregion
    }
}
