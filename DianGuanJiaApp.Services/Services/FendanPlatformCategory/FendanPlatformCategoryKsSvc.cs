using DianGuanJiaApp.Data;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.FxModel.CategoryProduct;
using DianGuanJiaApp.Data.FxModel.Listing;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Services.BaseProduct;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Services.Services.SupplierProduct;
using DianGuanJiaApp.Utility.Extension;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Services
{
    /// <summary>
    /// 快手，分单类目相关的实现
    /// </summary>
    public partial class PlatformCategorySupplierService
    {
        /// <summary>
        /// 【快手】类目
        /// </summary>
        public List<PlatformCategory> SyncFdCatesByKs(Shop shop)
        {
            KuaiShouPlatformService svc = new KuaiShouPlatformService(shop);

            var catesResult = svc.GetAllListingCategory();
            List<PlatformCategory> apiCates = catesResult.Select(f => ToPlatformCate(f, PlatformType.KuaiShou.ToString())).ToList();

            List<PlatformCategory> lastCates = new List<PlatformCategory>();
            var tuple = SaveCateReturnLastCate(shop, apiCates, lastCates, PlatformType.KuaiShou);

            // Task.Run(() => SyncPtInfoByPddV2(shop, tuple));
            return lastCates;
        }

        /// <summary>
        ///【快手】类目属性
        /// </summary>
        public Dictionary<string, ListingCategoryConfig> SyncCategoryPropByKs(Shop shop, List<string> lastLevelCates, IPlatformService platformService)
        {
            var platformSvc = platformService as KuaiShouPlatformService;
            Stopwatch sw = Stopwatch.StartNew();
            var hoit_roles = new Dictionary<string, ListingCategoryConfig>();
            Parallel.ForEach(lastLevelCates, new ParallelOptions() { MaxDegreeOfParallelism = 5 }, (cateId, loopState) =>
            {
                try
                {
                    ListingCategoryConfig listingCategoryConfig = platformSvc.GetCategoryConfig(cateId);
                    var conlist = new List<PlatformCategoryProp>();
                    var de = new PlatformCategoryProp();
                    de.PlatformType = shop.PlatformType;
                    de.Name = "";
                    de.CateId = cateId.ToString();
                    de.Content = listingCategoryConfig.ToJson();
                    de.PropertyId = "";
                    de.CreateTime = DateTime.Now;
                    de.UpdateTime = DateTime.Now;
                    de.SetUnionCode();
                    conlist.Add(de);

                    if (!hoit_roles.ContainsKey(cateId))
                        hoit_roles.Add(cateId, listingCategoryConfig);

                    _platformCateRep.SaveCategoryProp2(cateId.ToString(), conlist, PlatformType.KuaiShou.ToString());
                }
                catch (Exception ex)
                {
                    WriteErrorLog($"{cateId}-{shop.ShopName} 请求快手属性接口报错:{ex.Message}");

                    // 如果是授权到期，终止循环
                    if (ex.Message.Contains("授权过期"))
                    {
                        loopState.Stop(); // 授权过期，停止对这个店铺的api操作
                    }
                }

                Thread.Sleep(500);
            });

            sw.Stop();
            WriteInfoLog($"{DateTime.Now.ToString("HH:mm:ss")} 快手({shop.ShopName})-同步类目属性耗时：{sw.ElapsedMilliseconds} 毫秒 末级类目有{lastLevelCates.Count}个");
            return hoit_roles;
        }

        /// <summary>
        ///【快手】属性值
        /// </summary>
        public List<GetCategoryPropValResModel> SyncCategoryPropValByKs(string cateId, string propId, string propValue, IPlatformService platformService)
        {
            var platformSvc = platformService as KuaiShouPlatformService;
            var result = platformSvc.GetCategoryPropValuesLimitRetry(cateId, propId, propValue, 0, 500);
            return result.Select(item =>
            {
                var mo = new GetCategoryPropValResModel();
                mo.PlatformType = PlatformType.KuaiShou.ToString();
                mo.CateId = item.CateId;
                mo.PropertyId = item.PropertyId;
                mo.PropertyVal = item.PropertyVal;
                mo.PropertyValId = item.PropertyValId;
                return mo;
            }).ToList();
        }

        /// <summary>
        /// 商品资质
        /// </summary>
        public List<KuaishouQualificationCollectionConfigModel> ProductQualification(string cateId)
        {
            var shop = new ShopService(PlatformAppScene.listing).GetLatestPlatformShop(PlatformType.KuaiShou);
            var platformSvc = PlatformFactory.GetPlatformService(shop, scene: PlatformAppScene.listing) as KuaiShouPlatformService;
            List<KuaishouQualificationCollectionConfigModel> resList = platformSvc.QualificationCollectionConfig(cateId);
            return resList;
        }

        /// <summary>
        /// 品牌
        /// </summary>
        public List<BrandModel> GetBrandByKs(GetBrandReqModel req)
        {
            var shop = new ShopService(PlatformAppScene.listing).GetLatestPlatformShop(PlatformType.KuaiShou);
            var platformSvc = PlatformFactory.GetPlatformService(shop, scene: PlatformAppScene.listing) as KuaiShouPlatformService;
            Dictionary<string, string> check_Pass = platformSvc.GetBrandList(req.CateId, req.PropId, req.Name);
            return check_Pass.Select(x => new BrandModel(x.Key)).ToList();
        }

        /// <summary>
        /// 【快手】销售属性
        /// </summary>
        public Dictionary<string, KuaishouSalePropRuleModel> SyncPublishRuleByKs(Shop shop, List<string> lastLevelCates, IPlatformService platformService)
        {
            var platformSvc = platformService as KuaiShouPlatformService;
            var hoit_roles = new Dictionary<string, KuaishouSalePropRuleModel>();
            Stopwatch sw = Stopwatch.StartNew();
            // int _count = 0;
            // foreach (var cateId in lastLevelCates)
            Parallel.ForEach(lastLevelCates, new ParallelOptions() { MaxDegreeOfParallelism = 5 }, (cateId, loopState) =>
            {
                try
                {
                    // System.Threading.Interlocked.Increment(ref _count); if (_count > 30) loopState.Stop();

                    var puRulemodel = platformSvc.GetSalePropRule(cateId);
                    if (puRulemodel != null)
                    {
                        if (!hoit_roles.ContainsKey(cateId))
                            hoit_roles.Add(cateId, puRulemodel);

                        var de = new PlatformCategoryPublishRule();
                        de.PlatformType = shop.PlatformType;
                        de.CateId = cateId.ToString();
                        de.Content = puRulemodel.ToJson();
                        de.Code = DateTime.Now.Ticks.ToString().ToShortMd5();
                        de.CreateTime = DateTime.Now;
                        de.UpdateTime = DateTime.Now;

                        de.SetUnionCode();
                        _platformCateRep.SaveCategoryPublishRule3(de);
                    }
                }
                catch (Exception ex)
                {
                    WriteErrorLog($"{cateId}-{shop.ShopName} 请求快手销售属性规则接口报错:{ex.Message}");

                    // 如果是授权到期，终止循环
                    if (ex.Message.Contains("授权过期"))
                    {
                        loopState.Stop(); // 授权过期，停止对这个店铺的api操作
                    }
                }

                Thread.Sleep(500);
            });
            sw.Stop();
            WriteInfoLog($"{DateTime.Now.ToString("HH:mm:ss")} 快手({shop.ShopName})-同步销售属性耗时：{sw.ElapsedMilliseconds} 毫秒");
            return hoit_roles;
        }

        /// <summary>
        /// 获取商品审核状态
        /// </summary>
        public ListingProductStatusEnum? GetProductAuditListByKs(int shopId, string product_id, int productStatus)
        {
            if (productStatus == 0 || string.IsNullOrWhiteSpace(product_id)) return null;

            if (!Enum.TryParse(productStatus.ToString(), out ListingProductStatusEnum statusEnum)) return null;

            // 只有是立即上架审核中的才需要去获取审核状态
            if (statusEnum != ListingProductStatusEnum.ReadyForSaleAudit)
                return null;

            var shop = new ShopService(PlatformAppScene.listing).GetLatestPlatformShop(PlatformType.KuaiShou);
            var platformSvc = PlatformFactory.GetPlatformService(shop, scene: PlatformAppScene.listing) as KuaiShouPlatformService;
            var model = platformSvc.GetProductDetail(product_id);
            /*
                0待审核 ：就是审核中
                1审核待修改：就是审核未通过
                2审核通过 
                3审核拒绝：也是审核未通过
             */
            ListingProductStatusEnum? result = null;
            switch (model.AuditStatus)
            {
                case 0: result = ListingProductStatusEnum.ReadyForSaleAudit; break;
                case 1: result = ListingProductStatusEnum.ReadyForSaleReject; break;
                case 2: result = ListingProductStatusEnum.ReadyForSalePass; break;
                case 3: result = ListingProductStatusEnum.ReadyForSaleReject; break;
            }

            return result;
        }

        /// <summary>
        /// 【快手】类目变更同步到平台资料
        /// </summary>
        public void SyncPtInfoByKs(Shop shop, Tuple<List<PlatformCategory>, List<PlatformCategory>, List<PlatformCategory>> tuple)
        {
            try
            {
                // 检测上次是否执行完毕
                var redisKey = CacheKeys.SyncPtInfoTask.Replace("{Sid}", shop.Id.ToString())
                                                       .Replace("{Pt}", PlatformType.KuaiShou.ToString());
                var value = RedisHelper.Get<string>(redisKey);
                if (!string.IsNullOrWhiteSpace(value))
                {
                    WriteInfoLog($"[快手-类目变更同步到平台资料]:上次执行的任务未完成，本次取消. 缓存值：{value}");
                    return;
                }
                RedisHelper.Set(redisKey, DateTime.Now.ToString("yyyy-MM-dd~HH:mm:ss"), TimeSpan.FromDays(1));
                var fxUserId = SiteContext.Current.CurrentFxUserId;
                var ptSvc = new PtProductInfoService(string.Empty, fxUserId);

                // 更新的类目
                if (tuple.Item2.Any())
                {
                    var platformCategorySvc = new PlatformCategoryService();
                    List<string> update_cateIds = tuple.Item2.Select(f => f.CateId).ToList();
                    var platformService = PlatformFactory.GetPlatformService(shop, scene: PlatformAppScene.listing);

                    #region 类目属性
                    // 快手类目属性与类目是一对一
                    var dbCateProp = platformCategorySvc.GetCategoryPropList(PlatformType.KuaiShou.ToString(), update_cateIds).FirstOrDefault();
                    var db_required_Cate = new Dictionary<string, List<ListingCategoryConfig>>();

                    var attr = dbCateProp.Content.ToObject<ListingCategoryConfig>();
                    if (attr.categoryPropSwitch && attr.propConfigs.Any(x => x.required)
                        && db_required_Cate.TryGetValue(dbCateProp.CateId.ToString(), out List<ListingCategoryConfig> lconfig))
                    {
                        lconfig.Add(attr);
                    }
                    else
                    {
                        var li = new List<ListingCategoryConfig>() { attr };
                        db_required_Cate.Add(dbCateProp.CateId.ToString(), li);
                    }
                    
                    var apiCatePropList = SyncCategoryPropByKs(shop, update_cateIds, platformService);
                    var api_required_Cate = new Dictionary<string, List<ListingCategoryConfig>>();
                    foreach (var item in apiCatePropList)
                    {
                        if (api_required_Cate.TryGetValue(item.Key, out List<ListingCategoryConfig> data) &&
                            item.Value.categoryPropSwitch && item.Value.propConfigs.Any(x => x.required))
                        {
                            data.Add(item.Value);
                        }
                        else
                        {
                            var li = new List<ListingCategoryConfig>() { item.Value };
                            api_required_Cate.Add(item.Key, li);
                        }
                    }
                    // 计算出，删除的必填属性和新增的必填属性
                    (var del_cate_prop, var add_cate_prop) = GetDifferencePropKS(db_required_Cate, api_required_Cate);

                    #endregion

                    #region 销售属性

                    /*
                    var dbRuleList = platformCategorySvc.GetPlatformCategoryPublishRuleList(PlatformType.KuaiShou.ToString(), update_cateIds);
                    var db_required_prop = new Dictionary<string, List<KuaishouSalePropRuleModel>>();
                    foreach (var item in dbRuleList)
                    {
                        var attr = item.Content.ToObject<KuaishouSalePropRuleModel>();
                        if (attr.presetSaleProp.Any(x => x.salePropConfigRule.required)
                            && db_required_prop.TryGetValue(item.CateId.ToString(), out List<KuaishouSalePropRuleModel> data))
                        {
                            data.Add(attr);
                        }
                        else
                        {
                            var li = new List<KuaishouSalePropRuleModel>() { attr };
                            db_required_prop.Add(item.CateId.ToString(), li);
                        }
                    }

                    var apiRuleList = SyncPublishRuleByKs(shop, update_cateIds, platformService);
                    var api_required_prop = new Dictionary<string, List<KuaishouSalePropRuleModel>>();
                    foreach (var item in apiRuleList)
                    {
                        if (api_required_prop.TryGetValue(item.Key, out List<KuaishouSalePropRuleModel> data)
                            && item.Value.presetSaleProp.Any(x => x.salePropConfigRule.required))
                        {
                            data.Add(item.Value);
                        }
                        else
                        {
                            var li = new List<KuaishouSalePropRuleModel>() { item.Value };
                            api_required_prop.Add(item.Key, li);
                        }
                    }

                    // 计算出，删除的必填属性和新增的必填属性
                    (var del, var add) = GetDifferenceKS(db_required_prop, api_required_prop);
                    */

                    var apiRuleList = SyncPublishRuleByKs(shop, update_cateIds, platformService);
                    ptSvc.SyncSalePrductRuleByKs(apiRuleList);
                    #endregion

                    HashSet<string> cateIdList = new HashSet<string>();
                    foreach (var item in del_cate_prop) cateIdList.Add(item.Key);
                    foreach (var item in add_cate_prop) cateIdList.Add(item.Key);
                    //foreach (var item in del) cateIdList.Add(item.Key);
                    //foreach (var item in add) cateIdList.Add(item.Key);

                    ptSvc.SyncPrductProperty(cateIdList, PlatformType.KuaiShou);
                }
                // 删除的类目
                if (tuple.Item3.Any())
                {
                    var del_cateIds = tuple.Item3.Select(f => f.CateId).ToList();
                    ptSvc.DelPtDataCates(del_cateIds, PlatformType.KuaiShou.ToString());
                }
                RedisHelper.Del(redisKey);
            }
            catch (Exception ex)
            {
                WriteErrorLog($"类目变更同步快手失败：{ex.Message} 店铺：{shop.NickName} 堆栈：{ex.StackTrace}");
            }
        }

        /// <summary>
        /// 计算删除和新增的类目属性
        /// </summary>
        private (Dictionary<string, List<ListingCategoryConfig>>, Dictionary<string, List<ListingCategoryConfig>>) GetDifferencePropKS(
            Dictionary<string, List<ListingCategoryConfig>> sourceList,
            Dictionary<string, List<ListingCategoryConfig>> targetList)
        {
            var del = new Dictionary<string, List<ListingCategoryConfig>>();
            var add = new Dictionary<string, List<ListingCategoryConfig>>();
            // 计算删除了的属性
            foreach (var source in sourceList)
            {
                if (targetList.TryGetValue(source.Key, out List<ListingCategoryConfig> targetProps))
                {
                    var targetProps_c = targetProps.SelectMany(b => b.propConfigs);
                    var sourceList_c = source.Value.SelectMany(b => b.propConfigs);

                    if (!targetProps_c.Any(p => sourceList_c.Any(targetProp => targetProp.propName == p.propName)))
                    {
                        del.Add(source.Key, source.Value);
                    }
                }
            }

            // 计算新增的属性
            foreach (var target in targetList)
            {
                if (sourceList.TryGetValue(target.Key, out List<ListingCategoryConfig> sourceProps))
                {
                    var sourceProps_c = sourceProps.SelectMany(b => b.propConfigs);
                    var targetList_c = target.Value.SelectMany(b => b.propConfigs);

                    if (!sourceProps_c.Any(p => targetList_c.Any(targetProp => targetProp.propName == p.propName)))
                    {
                        add.Add(target.Key, target.Value);
                    }
                }
            }

            return (del, add);
        }

        /// <summary>
        /// 计算删除和新增的销售属性
        /// </summary>
        private (Dictionary<string, List<KuaishouSalePropRuleModel>>, Dictionary<string, List<KuaishouSalePropRuleModel>>) GetDifferenceKS(
            Dictionary<string, List<KuaishouSalePropRuleModel>> sourceList,
            Dictionary<string, List<KuaishouSalePropRuleModel>> targetList)
        {
            // 计算删除了的属性
            var del = new Dictionary<string, List<KuaishouSalePropRuleModel>>();
            var add = new Dictionary<string, List<KuaishouSalePropRuleModel>>();
            foreach (var source in sourceList)
            {
                if (targetList.TryGetValue(source.Key, out List<KuaishouSalePropRuleModel> targetProps))
                {
                    var targetProps_c = targetProps.SelectMany(b => b.presetSaleProp);
                    var sourceList_c = source.Value.SelectMany(b => b.presetSaleProp);

                    if (!targetProps_c.Any(p => sourceList_c.Any(targetProp => targetProp.propName == p.propName)))
                    {
                        del.Add(source.Key, source.Value);
                    }
                }
            }

            // 计算新增的属性
            foreach (var target in targetList)
            {
                if (sourceList.TryGetValue(target.Key, out List<KuaishouSalePropRuleModel> sourceProps))
                {
                    var sourceProps_c = sourceProps.SelectMany(b => b.presetSaleProp);
                    var targetList_c = target.Value.SelectMany(b => b.presetSaleProp);

                    if (!sourceProps_c.Any(p => targetList_c.Any(targetProp => targetProp.propName == p.propName)))
                    {
                        add.Add(target.Key, target.Value);
                    }
                }
            }

            return (del, add);
        }

        /// <summary>
        /// 处理快手的回调消息
        /// </summary>
        public void ProcesskuaiShouFxListingMessage(KuaiShouMessageModel model)
        {
            if (model.eventType == "kwaishop_item_auditStatusChange" && !string.IsNullOrWhiteSpace(model.info))
            {
                var ksauditStatusChange = model.info.ToObject<KuaiShouAuditStatusChange>();
                if (ksauditStatusChange != null)
                {
                    new ListingTaskRecordsService().UpdateProductStatusByKs(ksauditStatusChange.itemId, ksauditStatusChange.status);
                }
            }
        }
    }
}
