using DianGuanJiaApp.Data;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.FxModel.CategoryProduct;
using DianGuanJiaApp.Data.FxModel.Listing;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Services.BaseProduct;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Utility.Extension;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Services
{
    /// <summary>
    /// 拼多多，分单类目相关的实现
    /// </summary>
    public partial class PlatformCategorySupplierService
    {
        /// <summary>
        /// 【拼多多】类目
        /// </summary>
        public List<PlatformCategory> SyncFdCatesByPdd(Shop shop)
        {
            var svc = new PinduoduoPlatformService(shop);
            var catesResult = new List<ListingCategory>();

            GetAllCateByPinduoduo(svc, shop.Id, "0", catesResult);
            List<PlatformCategory> apiCates = catesResult.Select(f => ToPlatformCate(f, PlatformType.Pinduoduo.ToString())).ToList();

            List<PlatformCategory> lastCates = new List<PlatformCategory>();
            var tuple = SaveCateReturnLastCate(shop, apiCates, lastCates, PlatformType.Pinduoduo);

            Task.Run(() => SyncPtInfoByPddV2(shop, tuple));
            return lastCates;
        }

        private void SyncPtInfoByPdd(Shop shop, Tuple<List<PlatformCategory>, List<PlatformCategory>, List<PlatformCategory>> tuple)
        {
            try
            {
                // 更新的类目
                if (tuple.Item2.Any())
                {
                    List<string> update_cateIds = tuple.Item2.Select(f => f.CateId).ToList();
                    var dbRuleList = new PlatformCategoryService().GetPlatformCategoryPublishRuleList(PlatformType.Pinduoduo.ToString(), update_cateIds);

                    var platformService = PlatformFactory.GetPlatformService(shop, scene: PlatformAppScene.listing);
                    var apiRuleList = SyncPublishRuleByPdd(shop, update_cateIds, platformService);

                    #region 检测类目属性表更——>平台资料
                    // <类目Id，类目属性和销售属性>
                    var update_prop = dbRuleList.ToDictionary(a => a.CateId, b =>
                    {
                        PddAddProductRuleInfo rules = b.Content.ToObject<PddAddProductRuleInfo>();
                        return rules.goods_properties_rule.properties.Where(a => !a.is_sale).ToList();
                    });

                    var hoit_prop = apiRuleList.ToDictionary(a => a.Key, b =>
                    {
                        return b.Value.goods_properties_rule.properties.Where(a => !a.is_sale).ToList();
                    });

                    (var del, var add) = GetDifference(update_prop, hoit_prop);
                    new PtProductInfoService().SyncCatePropPtDataListByPdd(del, add);
                    #endregion

                    #region 检测销售属性表更——>平台资料
                    var update_prop_sale = dbRuleList.ToDictionary(a => a.CateId, b =>
                    {
                        PddAddProductRuleInfo rules = b.Content.ToObject<PddAddProductRuleInfo>();
                        return rules.goods_properties_rule.properties.Where(a => a.is_sale && a.required).ToList();
                    });
                    var hoit_prop_sale = apiRuleList.ToDictionary(a => a.Key, b =>
                    {
                        return b.Value.goods_properties_rule.properties.Where(a => a.is_sale && a.required).ToList();
                    });

                    (var del1, var add1) = GetDifference(update_prop_sale, hoit_prop_sale);
                    new PtProductInfoService().SyncSalePropPtDataListByPdd(del1, add1);
                    #endregion
                }
                // 删除的类目
                if (tuple.Item3.Any())
                {
                    var del_cateIds = tuple.Item3.Select(f => f.CateId).ToList();
                    new PtProductInfoService().DelPtDataCates(del_cateIds, PlatformType.Pinduoduo.ToString());
                }
            }
            catch (Exception ex)
            {
                WriteErrorLog($"错误提示：{ex.Message} 堆栈：{ex.StackTrace}");
            }
        }

        /// <summary>
        /// 类目变更同步到平台资料V2
        /// </summary>
        private void SyncPtInfoByPddV2(Shop shop, Tuple<List<PlatformCategory>, List<PlatformCategory>, List<PlatformCategory>> tuple)
        {
            try
            {
                // 检测上次是否执行完毕
                var redisKey = CacheKeys.SyncPtInfoTask.Replace("{Sid}", shop.Id.ToString())
                                                       .Replace("{Pt}", PlatformType.Pinduoduo.ToString());
                var value = RedisHelper.Get<string>(redisKey);
                if (!string.IsNullOrWhiteSpace(value))
                {
                    WriteInfoLog($"[拼多多-类目变更同步到平台资料]:上次执行的任务未完成，本次取消. 缓存值：{value}");
                    return;
                }
                RedisHelper.Set(redisKey, DateTime.Now.ToString("yyyy-MM-dd~HH:mm:ss"), TimeSpan.FromDays(1));
                var fxUserId = SiteContext.Current.CurrentFxUserId;
                var ptSvc = new PtProductInfoService(string.Empty, fxUserId);

                // 更新的类目
                if (tuple.Item2.Any())
                {
                    List<string> update_cateIds = tuple.Item2.Select(f => f.CateId).ToList();
                    var dbRuleList = new PlatformCategoryService().GetPlatformCategoryPublishRuleList(PlatformType.Pinduoduo.ToString(), update_cateIds);

                    var platformService = PlatformFactory.GetPlatformService(shop, scene: PlatformAppScene.listing);
                    var apiRuleList = SyncPublishRuleByPdd(shop, update_cateIds, platformService);

                    // 找出必填属性
                    var db_required_prop = dbRuleList.ToDictionary(a => a.CateId, b =>
                    {
                        PddAddProductRuleInfo rules = b.Content.ToObject<PddAddProductRuleInfo>();
                        return rules.goods_properties_rule.properties.Where(a => a.required).ToList();
                    });

                    var api_required_prop = apiRuleList.ToDictionary(a => a.Key, b => b.Value.goods_properties_rule.properties.Where(a => a.required).ToList());

                    // 计算出，删除的必填属性和新增的必填属性
                    (var del, var add) = GetDifference(db_required_prop, api_required_prop);
                    HashSet<string> cateIdList = new HashSet<string>();
                    foreach (var item in del) cateIdList.Add(item.Key);
                    foreach (var item in add) cateIdList.Add(item.Key);

                    ptSvc.SyncPrductProperty(cateIdList, PlatformType.Pinduoduo);
                }
                // 删除的类目
                if (tuple.Item3.Any())
                {
                    var del_cateIds = tuple.Item3.Select(f => f.CateId).ToList();
                    ptSvc.DelPtDataCates(del_cateIds, PlatformType.Pinduoduo.ToString());
                }
                RedisHelper.Del(redisKey);
            }
            catch (Exception ex)
            {
                WriteErrorLog($"类目变更同步拼多多失败：{ex.Message} 店铺：{shop.NickName} 堆栈：{ex.StackTrace}");
            }
        }

        /// <summary>
        /// 计算删除和新增的类目属性
        /// </summary>
        private (Dictionary<string, List<PrductProperty>>, Dictionary<string, List<PrductProperty>>) GetDifference(
            Dictionary<string, List<PrductProperty>> sourceList,
            Dictionary<string, List<PrductProperty>> targetList)
        {
            // 计算删除了的属性
            var del = new Dictionary<string, List<PrductProperty>>();
            var add = new Dictionary<string, List<PrductProperty>>();
            foreach (var source in sourceList)
            {
                if (targetList.TryGetValue(source.Key, out List<PrductProperty> targetProps))
                {
                    if (!targetProps.Any(p => source.Value.Any(targetProp => targetProp.name == p.name)))
                    {
                        del.Add(source.Key, source.Value);
                    }
                }
            }

            // 计算新增的属性
            foreach (var target in targetList)
            {
                if (sourceList.TryGetValue(target.Key, out List<PrductProperty> targetProps))
                {
                    if (!targetProps.Any(p => target.Value.Any(targetProp => targetProp.name == p.name)))
                    {
                        add.Add(target.Key, target.Value);
                    }
                }
            }

            return (del, add);
        }

        /// <summary>
        /// 递归获取拼多多类目
        /// </summary>
        public void GetAllCateByPinduoduo(PinduoduoPlatformService pddPtService, int shopId, string pid, List<ListingCategory> cateList)
        {
            var result = new List<ListingCategory>();
            for (int i = 0; i < 6; i++)
            {
                try
                {
                    result = pddPtService.GetPddAllListingCategory(pid);
                    break;
                }
                catch (Exception)
                {
                    if (i > 4) throw;
                }
            }

            if (result.Any())
            {
                result.ForEach(f =>
                {
                    f.IsParent = true;
                    cateList.Add(f);
                });
            }
            else
            {
                //没有子集的时候，将类目设为叶子类目
                var parentCate = cateList.FirstOrDefault(f => f.Id == pid);
                if (parentCate != null)
                    parentCate.IsParent = false;
                return;
            }

            var parentCateList = result.Where(f => f.IsParent);
            if (parentCateList.Any() == false)
                return;

            //循环递归加载子集
            foreach (var cate in parentCateList)
            {
                Thread.Sleep(300);
                GetAllCateByPinduoduo(pddPtService, shopId, cate.Id, cateList);
            }
        }

        /// <summary>
        /// 【拼多多】类目属性和类目属性值
        /// </summary>
        public void SyncCategoryPropByPdd(Shop shop, List<string> lastLevelCates, IPlatformService platformService)
        {
            var platformSvc = platformService as PinduoduoPlatformService;
            Stopwatch sw = Stopwatch.StartNew();
            Parallel.ForEach(lastLevelCates, new ParallelOptions() { MaxDegreeOfParallelism = 5 }, (cateId, loopState) =>
            {
                try
                {
                    List<PddGoodsSpecGet> catePropList = platformSvc.GetSpecInfoByCateId(long.Parse(cateId));
                    var conlist = new List<PlatformCategoryProp>();
                    foreach (var p in catePropList)
                    {
                        var de = new PlatformCategoryProp();
                        de.PlatformType = shop.PlatformType;
                        de.Name = p.parent_spec_name;
                        de.CateId = cateId.ToString();
                        de.Content = string.Empty;
                        de.PropertyId = p.parent_spec_id.ToString();
                        de.CreateTime = DateTime.Now;
                        de.UpdateTime = DateTime.Now;
                        de.SetUnionCode();
                        conlist.Add(de);
                    }

                    _platformCateRep.SaveCategoryProp2(cateId.ToString(), conlist, PlatformType.Pinduoduo.ToString());
                }
                catch (Exception ex)
                {
                    WriteErrorLog($"{cateId}-{shop.ShopName} 请求拼多多属性接口报错:{ex.Message}");

                    // 如果是授权到期，终止循环
                    if (ex.Message.Contains("授权过期"))
                    {
                        loopState.Stop(); // 授权过期，停止对这个店铺的api操作
                    }
                }

                Thread.Sleep(500);
            });

            sw.Stop();
            WriteInfoLog($"{DateTime.Now.ToString("HH:mm:ss")} 拼多多({shop.ShopName})-同步类目属性耗时：{sw.ElapsedMilliseconds} 毫秒 末级类目有{lastLevelCates.Count}个");
        }

        /// <summary>
        /// 【拼多多】类目发布规则
        /// </summary>
        public Dictionary<string, PddAddProductRuleInfo> SyncPublishRuleByPdd(Shop shop, List<string> lastLevelCates, IPlatformService platformService)
        {
            var platformSvc = platformService as PinduoduoPlatformService;
            var hoit_roles = new Dictionary<string, PddAddProductRuleInfo>();
            Stopwatch sw = Stopwatch.StartNew();
            Parallel.ForEach(lastLevelCates, new ParallelOptions() { MaxDegreeOfParallelism = 5 }, (cateId, loopState) =>
            {
                long l_cateId = long.Parse(cateId.ToString());
                try
                {
                    var puRulemodel = platformSvc.GetAddProductRuleByCateId(l_cateId);
                    if (puRulemodel != null)
                    {
                        if (!hoit_roles.ContainsKey(cateId))
                            hoit_roles.Add(cateId, puRulemodel);

                        var de = new PlatformCategoryPublishRule();
                        de.PlatformType = shop.PlatformType;
                        de.CateId = cateId.ToString();
                        de.Content = puRulemodel.ToJson();
                        de.Code = DateTime.Now.Ticks.ToString().ToShortMd5();
                        de.CreateTime = DateTime.Now;
                        de.UpdateTime = DateTime.Now;

                        de.SetUnionCode();
                        _platformCateRep.SaveCategoryPublishRule3(de);
                    }
                }
                catch (Exception ex)
                {
                    WriteErrorLog($"{cateId}-{shop.ShopName} 请求拼多多规则接口报错:{ex.Message}");

                    // 如果是授权到期，终止循环
                    if (ex.Message.Contains("授权过期"))
                    {
                        loopState.Stop(); // 授权过期，停止对这个店铺的api操作
                    }
                }

                Thread.Sleep(500);
            });
            sw.Stop();
            WriteInfoLog($"{DateTime.Now.ToString("HH:mm:ss")} 拼多多({shop.ShopName})-同步类目类目发布规则耗时：{sw.ElapsedMilliseconds} 毫秒");
            return hoit_roles;
        }

        /// <summary>
        /// 获取商品审核状态
        /// </summary>
        public ListingProductStatusEnum? GetProductAuditListByPdd(int shopId, string product_id, int productStatus)
        {
            if (productStatus == 0 || string.IsNullOrWhiteSpace(product_id)) return null;

            if (!Enum.TryParse(productStatus.ToString(), out ListingProductStatusEnum statusEnum)) return null;

            // 只有是立即上架审核中的才需要去获取审核状态
            if (statusEnum != ListingProductStatusEnum.ReadyForSaleAudit)
                return null;

            var targetShops = new ShopService(PlatformAppScene.listing).GetShopByIds(new List<int>() { shopId });
            var shop = targetShops.FirstOrDefault();
            var pddListsvc = PlatformFactory.GetPlatformService(shop, scene: PlatformAppScene.listing) as PinduoduoPlatformService;

            Stopwatch stopwatch = Stopwatch.StartNew();
            Dictionary<long, int> check_Pass = pddListsvc.GetProductStatus(new List<string>() { product_id });
            stopwatch.Stop();

            // 草稿状态（0:编辑中,1:审核中,2:审核通过,3:审核驳回）
            if (check_Pass.TryGetValue(long.Parse(product_id), out int auditStatus))
            {
                switch (auditStatus)
                {
                    case 0: // 返回0是很有问题的，目前不明确平台在什么情况才会返回 0
                    case 1: return ListingProductStatusEnum.ReadyForSaleAudit;
                    case 2: return ListingProductStatusEnum.ReadyForSalePass;
                    case 3: return ListingProductStatusEnum.ReadyForSaleReject;
                    default: break;
                }
            }

            WriteInfoLog($"{DateTime.Now.ToString("HH:mm:ss")} 查询({product_id})审核通过状态耗时：{stopwatch.ElapsedMilliseconds} 毫秒 结果是{check_Pass.Count}");

            return null;
        }

        /// <summary>
        /// 获取拼多多品牌
        /// </summary>
        public List<BrandModel> GetBrandByPdd(GetBrandReqModel req)
        {
            // var targetShops = new ShopService(PlatformAppScene.listing).GetShopByIds(new List<int>() { req.ShopId });
            // var shop = targetShops.FirstOrDefault();
            var shop = new ShopService(PlatformAppScene.listing).GetLatestPlatformShop(PlatformType.Pinduoduo);
            var pddListsvc = PlatformFactory.GetPlatformService(shop, scene: PlatformAppScene.listing) as PinduoduoPlatformService;
            Dictionary<string, long> check_Pass = pddListsvc.GetTemplatePropertyValueSearch(req.CateId, req.PropId, req.Name);
            return check_Pass.Select(x => new BrandModel(x.Key)).ToList();
        }
    }
}
