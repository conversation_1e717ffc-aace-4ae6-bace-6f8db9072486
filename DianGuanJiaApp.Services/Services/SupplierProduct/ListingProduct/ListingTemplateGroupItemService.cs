using DianGuanJiaApp.Data.Entity.SupplierProduct.ListingProduct;
using DianGuanJiaApp.Data.FxModel.Listing;
using DianGuanJiaApp.Data.Repository.SupplierProduct.ListingProduct;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Utility;
using SecurityLog;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Services.Services.SupplierProduct.ListingProduct
{
    public class ListingTemplateGroupItemService : SupplierProductBaseService<ListingTemplateGroupItem>
    {
        private readonly ListingTemplateGroupItemRepository _repository;
        public ListingTemplateGroupItemService()
        {
            _repository = new ListingTemplateGroupItemRepository();
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="uniqueCode"></param>
        /// <returns></returns>
        public List<ShopDeliveryTemplateModel> GetDeliveryTemplate(string uniqueCode, string platformType)
        {
            var shopExpressTemplate = _repository.GetTemplateGroupItemByCode(uniqueCode, platformType) ?? new List<ListingTemplateGroupItem>();
            var models = shopExpressTemplate.Select(x => new ShopDeliveryTemplateModel { Id = x.Id, ShopId = x.ShopId, PlatformTemplateId = x.PlatformTemplateId,PlatformTemplateName = x.PlatformTemplateName }).ToList();
            #region 需要通过shopId获取店铺所有的运费模板，该步骤已经在GetListingShopData中已经获取了，这段不用了，注释
            //var platformShop = SiteContext.CurrentNoThrow.AllShops.Where(s => s.PlatformType.Equals(platformType, StringComparison.CurrentCultureIgnoreCase) && templateShopIds.Contains(s.Id)).ToList();
            //platformShop.ForEach(s =>
            //{
            //    List<PlatformDeliveryTemplate> platformDeliveryTemplate = new List<PlatformDeliveryTemplate>();
            //    try
            //    {

            //        ZhiDianNewPlatformService service = new ZhiDianNewPlatformService(s);
            //        var deliveryTemplates = service.GetListingDeliveryTemplate();

            //        if (deliveryTemplates != null && deliveryTemplates.Count > 0)
            //        {
            //            platformDeliveryTemplate = deliveryTemplates.Select(x => new PlatformDeliveryTemplate
            //            {
            //                Id = x.Id,
            //                DeliveryTemplateName = x.Name
            //            }).ToList();
            //        }
            //    }
            //    catch (Exception ex)
            //    {
            //        Utility.Log.WriteError($"获取店铺:{s.ShopName}的运费模板异常，信息：{ex.Message}", "GetListingDeliveryTemplate.txt");
            //    }
            //    var model = models.SingleOrDefault(m => m.ShopId == s.Id);
            //    if (model != null)
            //    {
            //        model.ShopName = s.ShopName;
            //        model.AllExpressTemplates = platformDeliveryTemplate;
            //    }
            //    //else
            //    //{
            //    //    model = new ShopDeliveryTemplateModel
            //    //    {
            //    //        ShopId = s.Id,
            //    //        ShopName = s.ShopName,
            //    //        AllExpressTemplates = platformDeliveryTemplate
            //    //    };
            //    //    models.Add(model);
            //    //}
            //});
            #endregion
            return models;
        }
        /// <summary>
        /// 刷新店铺运费模板
        /// </summary>
        /// <param name="shopId"></param>
        /// <param name="platformType"></param>
        /// <returns></returns>
        public Dictionary<int,List<PlatformDeliveryTemplate>> RefreshDeliveryTemplate(List<int> shopIds)
        {
            // 假如店铺订购了分单和铺货两个应用，这样子写可能会取到分单的应用
            // var platformShop = SiteContext.CurrentNoThrow.AllShops.Where(s => s.Id == shopId && s.PlatformType.Equals(platformType, StringComparison.CurrentCultureIgnoreCase)).FirstOrDefault();
            var platformShops = new ShopService("listing").GetShopsAndShopExtensionFunc(shopIds, isCheckAuth:true, isProcessPddShopExtension: false);
            if (platformShops == null) throw new LogicException("当前店铺不存在铺货的应用，请先订购铺货应用或添加相关的店铺");
            Dictionary<int, List<PlatformDeliveryTemplate>> result = new Dictionary<int, List<PlatformDeliveryTemplate>>();
            platformShops.ForEach(platformShop => {
                try
                {
                    if (platformShop.PlatformType.Equals("toutiao", StringComparison.CurrentCultureIgnoreCase))
                    {
                        ZhiDianNewPlatformService service = new ZhiDianNewPlatformService(platformShop);
                        var deliveryTemplates = service.GetListingDeliveryTemplate();
                        if (deliveryTemplates != null && deliveryTemplates.Count > 0)
                        {
                            if (!result.ContainsKey(platformShop.Id))
                            {
                                result[platformShop.Id] = new List<PlatformDeliveryTemplate>();
                            }
                            result[platformShop.Id].AddRange(deliveryTemplates.Select(x => new PlatformDeliveryTemplate
                            {
                                Id = x.Id,
                                DeliveryTemplateName = x.Name
                            }).ToList());
                        }
                    }
                }
                catch (Exception ex)
                {
                    Utility.Log.WriteError($"获取店铺:{platformShop.ShopName}的运费模板异常，信息：{ex.Message}", "GetListingDeliveryTemplate.txt");
                }
            });
            
            return result;
        }

        public void Save(List<SaveLisTempGroupItemModel> saveLisTempGroupItemModels)
        {
            if (saveLisTempGroupItemModels == null) { throw new LogicException("运费模板保存失败"); }
            var uniqueCode = saveLisTempGroupItemModels.FirstOrDefault()?.GroupUniqueCode;
            var platformType = saveLisTempGroupItemModels.FirstOrDefault()?.PlatformType;
            var shopExpressTemplate = _repository.GetTemplateGroupItemByCode(uniqueCode, platformType) ?? new List<ListingTemplateGroupItem>();
            var shopIds = shopExpressTemplate.Select(x => x.ShopId).ToList();
            // 防止重复插入
            var temp = saveLisTempGroupItemModels.Where(x => (x.Id.HasValue && x.Id > 0) || !shopIds.Contains(x.ShopId)).ToList();

            var saveModel = temp.Select(s => new ListingTemplateGroupItem
            {
                Id = s.Id ?? 0,
                GroupUniqueCode = s.GroupUniqueCode,
                PlatformType = s.PlatformType,
                ShopId = s.ShopId,
                PlatformTemplateId = s.PlatformTemplateId,
                PlatformTemplateName = s.PlatformTemplateName,
                CreateTime = DateTime.Now,
                UpdateTime = DateTime.Now,
            }).ToList();
            saveLisTempGroupItemModels.Where(x => shopIds.Contains(x.ShopId)).ToList().ForEach(x => {
                var updateTemplate = shopExpressTemplate.FirstOrDefault(e => x.ShopId == e.ShopId);
                if (updateTemplate != null && updateTemplate.PlatformTemplateId != x.PlatformTemplateId)
                {
                    updateTemplate.UpdateTime = DateTime.Now;
                    updateTemplate.PlatformTemplateId = x.PlatformTemplateId;
                    updateTemplate.PlatformTemplateName = x.PlatformTemplateName;
                    saveModel.Add(updateTemplate);
                }
            });
            _repository.Save(saveModel);
        }
        /// <summary>
        /// 判断是否有未配置的模板
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        public bool IsSetShopTemplate(JudgeTemplateReq req)
        {
            var shopExpressTemplate = _repository.GetTemplateGroupItemByCode(req.GroupUniqueCode, req.PlatformType) ?? new List<ListingTemplateGroupItem>();
            foreach (var shopId in req.ShopIds)
            {
                var template = shopExpressTemplate.FirstOrDefault(t => t.ShopId == shopId);
                if (template == null || string.IsNullOrWhiteSpace(template.PlatformTemplateId))
                {
                    return false;
                }
            }
            return true;
        }
    }
}
