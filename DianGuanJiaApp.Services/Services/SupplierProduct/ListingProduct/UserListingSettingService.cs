using DianGuanJiaApp.Data.Entity.SupplierProduct.ListingProduct;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.FxModel.Listing;
using DianGuanJiaApp.Data.Repository.SupplierProduct.ListingProduct;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using Nest;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Services.Services.SupplierProduct.ListingProduct
{
    /// <summary>
    /// 铺货设置服务
    /// </summary>
    public class UserListingSettingService : SupplierProductBaseService<UserListingSetting>
    {
        private readonly UserListingSettingRepository _userListingSettingRepository;
        public UserListingSettingService()
        {
            _userListingSettingRepository = new UserListingSettingRepository();
        }
        /// <summary>
        /// 查询
        /// </summary>
        /// <param name="platformType"></param>
        /// <returns></returns>
        /// <exception cref="LogicException"></exception>
        public UserListingSettingModel Get(string platformType)
        {
            try
            {
                var fxUserId = SiteContext.GetCurrentFxUserId();
                var usersetting = _userListingSettingRepository.Get(fxUserId, platformType);
                if (usersetting == null)
                {
                    return null;
                    //throw new LogicException("此账号无铺货设置");
                }
                UserListingSettingModel userListingSettingModel = new UserListingSettingModel();

                ListingSetting listingSetting = new ListingSetting();
                listingSetting.Id = usersetting.Id;
                listingSetting.IsDefault = usersetting.IsDefault;
                listingSetting.PlatformType = usersetting.PlatformType;
                listingSetting.ListingSettingValue = JsonConvert.DeserializeObject<ListingSettingValue>(usersetting.SettingJson);
                if (string.IsNullOrWhiteSpace(usersetting.BatchSettingJson))
                {
                    // 无数据时，给出默认值输出前端
                    listingSetting.BatchSettingValue = new BatchListingSettingValue();
                    listingSetting.BatchSettingValue.SetDefault();
                   
                }
                else
                {
                    listingSetting.BatchSettingValue = JsonConvert.DeserializeObject<BatchListingSettingValue>(usersetting.BatchSettingJson);
                }
                userListingSettingModel.ListingSettingData = listingSetting;
                return userListingSettingModel;
            }
            catch (Exception ex)
            {
                Log.WriteError($"获取铺货设置异常：{ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取批量铺货设置
        /// </summary>
        public BatchListingSettingValue GetSettingModel(string platformType)
        {
            var fxUserId = SiteContext.GetCurrentFxUserId();
            var usersetting = _userListingSettingRepository.Get(fxUserId, platformType);
            if (usersetting == null || string.IsNullOrWhiteSpace(usersetting.BatchSettingJson))
            {
                // 无数据时，给出默认值输出前端
                var batchSettingValue = new BatchListingSettingValue();
                batchSettingValue.SetDefault();
                return batchSettingValue;
            }
            else
            {
                var batchSettingValue = JsonConvert.DeserializeObject<BatchListingSettingValue>(usersetting.BatchSettingJson);
                return batchSettingValue;
            }
        }

        /// <summary>
        /// 保存或更新
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="setting"></param>
        public int SaveOrUpdate(ListingSetting setting, bool isbatch = false)
        {
            if (setting == null)
            {
                throw new LogicException("铺货设置参数异常");
            }
            try
            {
                var fxUserId = SiteContext.GetCurrentFxUserId();
                UserListingSetting saveSetting = _userListingSettingRepository.Get(fxUserId, setting.PlatformType);
                if (saveSetting == null)
                {
                    saveSetting = new UserListingSetting();
                    saveSetting.FxUserId = SiteContext.GetCurrentFxUserId();
                    saveSetting.PlatformType = setting.PlatformType;
                    saveSetting.CreateTime = DateTime.Now;
                    saveSetting.IsDefault = true;
                }
                else
                {
                    saveSetting.UpdateTime = DateTime.Now;
                }
                saveSetting.IsDefault = setting.IsDefault;

                // 单个设置时，BatchSettingValue 是null，批量设置时，ListingSettingValue 是null
                saveSetting.SettingJson = setting.ListingSettingValue.ToJson();
                saveSetting.BatchSettingJson = setting.BatchSettingValue.ToJson();

                //saveSetting.Id = setting.Id;

                return _userListingSettingRepository.SaveOrUpdate(saveSetting, isbatch);
            }
            catch (Exception ex)
            {
                Log.WriteError($"保存铺货设置参数异常:{ex.Message}");
            }
            return 0;
        }
    }
}
