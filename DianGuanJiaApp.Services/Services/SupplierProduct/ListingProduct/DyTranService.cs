using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Model.BaseProduct;
using DianGuanJiaApp.Services.ListingProduct;
using DianGuanJiaApp.Services.ListingProduct.Models.TouTiao;
using DianGuanJiaApp.Services.Model;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace DianGuanJiaApp.Services.Services.SupplierProduct.ListingProduct
{
    /// <summary>
    /// 抖店规格+属性转换的服务
    /// </summary>
    public class DyTranService
    {
        private readonly string _defalutSkuValue = "无规格";
        private readonly char _splitCh = '-';
        /// <summary>
        /// 转化基础资料规格（新）
        /// </summary>
        /// <param name="rules"></param>
        /// <param name="sourceSkuModels"></param>
        /// <returns></returns>
        public List<PtProductInfoSkuModel> TranBaseProductSpec(ProductUpdateRuleInfoModel rules, List<PtProductInfoSkuModel> sourceSkuModels)//List<SupplierProductUpdateSkuModel> sourceSkuModels)
        {
            // 粗暴的转化
            List<PtProductInfoSkuModel> result = new List<PtProductInfoSkuModel>();

            var specDetails = rules?.product_spec_rule?.required_spec_details ?? new List<RequiredSpecDetail>();

            // 无规格的发布规则，返回原值
            if (specDetails == null || specDetails.Count == 0)
            {
                return sourceSkuModels;
            }

            // 匹配的规格仅限两个，除非必填规格超过两个
            var requiredSpecs = specDetails.Where(x => x.is_required).ToList() ?? new List<RequiredSpecDetail>();
            switch (requiredSpecs.Count)
            {
                case 0:
                    result = NoReqSku(sourceSkuModels, specDetails);
                    break;
                case 1:
                    result = OneReqSku(sourceSkuModels, specDetails);
                    break;
                case 2:
                    result = TwoReqSku(sourceSkuModels, specDetails);
                    break;
                case 3:
                    result = ThreeReqSku(sourceSkuModels, specDetails);
                    break;
                default:
                    throw new LogicException("平台必填规格超过了3个，暂未支持处理这么多的");
            }
            return result;
        }

        public List<PtProductInfoSkuModel> TranBaseProductSpec(ProductUpdateRuleInfoModel rules, List<PtProductInfoSkuModel> sourceSkuModels, int skuModeType = 0)
        {
            if (skuModeType == 0)
            {
            }
            return TranBaseProductSpec(rules, sourceSkuModels);
            // 粗暴的转化
            List<PtProductInfoSkuModel> result = new List<PtProductInfoSkuModel>();

            var specDetails = rules?.product_spec_rule?.required_spec_details ?? new List<RequiredSpecDetail>();

            // 无规格的发布规则，返回原值
            if (specDetails == null || specDetails.Count == 0)
            {
                return sourceSkuModels;
            }

            // skuModeType=1表示是自定义规格，按';'或者'；'，默认按2个规格处理，除非规格值分割之后都为1才按1个规格值处理
            // 匹配的规格仅限两个，除非必填规格超过两个
            var requiredSpecs = specDetails.Where(x => x.is_required).ToList() ?? new List<RequiredSpecDetail>();

            var skuName1 = string.Empty;
            var skuName2 = string.Empty;
            var skuName3 = string.Empty;
            if (requiredSpecs.Count == 0)
            {
                skuName1 = specDetails.FirstOrDefault()?.sell_property_name ?? string.Empty;
                skuName2 = specDetails.ElementAtOrDefault(1)?.sell_property_name ?? string.Empty;
            }
            else if (requiredSpecs.Count == 1)
            {
                skuName1 = requiredSpecs.FirstOrDefault().sell_property_name;
                skuName2 = specDetails.FirstOrDefault().sell_property_name ?? string.Empty;
                if (skuName2.IsNotNullOrEmpty())
                {
                    // 如果相等，证明必填规格处于第一位
                    if (skuName1 == skuName2)
                    {
                        skuName2 = specDetails.ElementAtOrDefault(1)?.sell_property_name ?? string.Empty;
                    }
                    else
                    {
                        var temp = skuName1;
                        skuName1 = skuName2;
                        skuName2 = temp;
                    }

                }
            }
            else if(requiredSpecs.Count == 2)
            {
                skuName1 = requiredSpecs.ElementAtOrDefault(0)?.sell_property_name;
                skuName2 = requiredSpecs.ElementAtOrDefault(1)?.sell_property_name;
            }
            else
            {
                skuName1 = requiredSpecs.ElementAtOrDefault(0)?.sell_property_name;
                skuName2 = requiredSpecs.ElementAtOrDefault(1)?.sell_property_name;
                skuName3 = requiredSpecs.ElementAtOrDefault(2)?.sell_property_name;
            }

            if (skuName3.IsNotNullOrEmpty())
            {
                sourceSkuModels.ForEach(x => {
                    x.Attribute.AttributeName1 = skuName1;
                    x.Attribute.AttributeName2 = skuName2;
                    x.Attribute.AttributeName3 = skuName3;
                    if (x.Attribute.AttributeValue2.IsNullOrEmpty())
                    {
                        x.Attribute.AttributeValue2 = _defalutSkuValue;
                    }
                    if (x.Attribute.AttributeValue3.IsNullOrEmpty())
                    {
                        x.Attribute.AttributeValue3 = _defalutSkuValue;
                    }

                    x.Attributes = PtProductSkuAttributeModel.FromClass(x.Attribute);
                });
            }
            else
            {
                if (skuName2.IsNotNullOrEmpty())
                {
                    sourceSkuModels.ForEach(x => {
                        x.Attribute.AttributeName1 = skuName1;
                        x.Attribute.AttributeName2 = skuName2;
                        x.Attribute.AttributeName3 = string.Empty;
                        x.Attribute.AttributeValue2 = $"{x.Attribute.AttributeValue2}{_splitCh}{x.Attribute.AttributeValue3}".Trim(_splitCh).Trim();
                        if (x.Attribute.AttributeValue2.IsNullOrEmpty())
                        {
                            x.Attribute.AttributeValue2 = _defalutSkuValue;
                        }
                        x.Attribute.AttributeValue3 = string.Empty;
                        x.Attributes = PtProductSkuAttributeModel.FromClass(x.Attribute);
                    });
                }
                else
                {
                    sourceSkuModels.ForEach(x => {
                        x.Attribute.AttributeName1 = skuName1;
                        x.Attribute.AttributeName2 = string.Empty;
                        x.Attribute.AttributeName3 = string.Empty;

                        x.Attribute.AttributeValue1 = $"{x.Attribute.AttributeValue1}{_splitCh}{x.Attribute.AttributeValue2}{_splitCh}{x.Attribute.AttributeValue3}".Trim(_splitCh).Trim();
                        x.Attribute.AttributeValue2 = string.Empty;
                        x.Attribute.AttributeValue3 = string.Empty;

                        x.Attributes = PtProductSkuAttributeModel.FromClass(x.Attribute);
                    });
                }
            }
            
            return sourceSkuModels;
        }
        #region 返回匹配好的数据
        private List<string> GetSourceTotalSku(PtProductInfoSkuModel model)
        {
            var skuName1 = model?.Attribute.AttributeName1;
            var skuName2 = model?.Attribute.AttributeName2;
            var skuName3 = model?.Attribute.AttributeName3;
            List<string> sourceTotalSku = new List<string> { skuName1, skuName2, skuName3 };
            return sourceTotalSku.Where(x => !string.IsNullOrWhiteSpace(x)).Distinct().ToList();
        }
        /// <summary>
        /// 三个必填规格
        /// </summary>
        /// <param name="sourceSkuModels"></param>
        /// <param name="specDetails"></param>
        /// <returns></returns>
        private List<PtProductInfoSkuModel> ThreeReqSku(List<PtProductInfoSkuModel> sourceSkuModels, List<RequiredSpecDetail> specDetails)
        {
            List<PtProductInfoSkuModel> result = new List<PtProductInfoSkuModel>();
            // 匹配的规格仅限两个，除非必填规格超过两个
            var requiredSpecs = specDetails.Where(x => x.is_required).ToList() ?? new List<RequiredSpecDetail>();
            // 所有必填规格的名称
            var reqSkuNames = requiredSpecs.Select(x => x.sell_property_name).ToList();
            // 原规格名称
            List<string> sourceTotalSku = GetSourceTotalSku(sourceSkuModels.FirstOrDefault());
            // 所有的平台名称
            List<string> allPlatSkuName = specDetails.Select(x => x.sell_property_name).ToList();
            // 原规格与平台规格匹配的结果
            var matchSkuNameResult = MatchSkuName(sourceTotalSku, allPlatSkuName);

            #region 三个必填项
            var firstPlatSkuName = reqSkuNames[0];
            var secondPlatSkuName = reqSkuNames[1];
            var thirdPlatSkuName = reqSkuNames[2];
            var matchFirstSourceSkuName = string.Empty;// MatchSkuName(firstPlatSkuName, sourceTotalSku);
            var matchSecondSourceSkuName = string.Empty;// MatchSkuName(secondPlatSkuName, sourceTotalSku);
            var matchThirdSourceSkuName = string.Empty;// MatchSkuName(thirdPlatSkuName, sourceTotalSku);
            foreach (var item in matchSkuNameResult)
            {
                if (item.Value == firstPlatSkuName)
                {
                    matchFirstSourceSkuName = item.Key;
                    continue;
                }
                if (item.Value == secondPlatSkuName)
                {
                    matchSecondSourceSkuName = item.Key;
                    continue;
                }
                if (item.Value == thirdPlatSkuName)
                {
                    matchThirdSourceSkuName = item.Key;
                    continue;
                }
            }
            foreach (var item in sourceSkuModels)
            {
                PtProductInfoSkuModel skuModel = GetNewPtProductInfoSkuModel(item);
                //skuModel.Attribute = new PtProductSkuAttributeModel();
                skuModel.Attribute.AttributeName1 = firstPlatSkuName;
                skuModel.Attribute.AttributeName2 = secondPlatSkuName;
                skuModel.Attribute.AttributeName3 = thirdPlatSkuName;
                #region 赋值
                skuModel.Attribute.AttributeValue1 = item.Attribute.AttributeValue1;
                skuModel.Attribute.AttributeValue2 = item.Attribute.AttributeValue2;
                skuModel.Attribute.AttributeValue3 = item.Attribute.AttributeValue3;
                // 是否匹配上第一个
                if (matchFirstSourceSkuName.IsEmpty())
                {
                    // 是否匹配上第二个
                    if (matchSecondSourceSkuName.IsEmpty())
                    {
                        // 是否匹配上第三个
                        if (!matchThirdSourceSkuName.IsEmpty())
                        {
                            if (matchThirdSourceSkuName == item.Attribute.AttributeName1)
                            {
                                skuModel.Attribute.AttributeValue1 = item.Attribute.AttributeValue2;
                                skuModel.Attribute.AttributeValue2 = item.Attribute.AttributeValue3;
                                skuModel.Attribute.AttributeValue3 = item.Attribute.AttributeValue1;
                            }
                            else if (matchThirdSourceSkuName == item.Attribute.AttributeName2)
                            {
                                skuModel.Attribute.AttributeValue1 = item.Attribute.AttributeValue1;
                                skuModel.Attribute.AttributeValue2 = item.Attribute.AttributeValue3;
                                skuModel.Attribute.AttributeValue3 = item.Attribute.AttributeValue2;
                            }
                        }
                    }
                    else
                    {
                        if (matchThirdSourceSkuName.IsEmpty())
                        {
                            if (matchSecondSourceSkuName == item.Attribute.AttributeName1)
                            {
                                skuModel.Attribute.AttributeValue1 = item.Attribute.AttributeValue2;
                                skuModel.Attribute.AttributeValue2 = item.Attribute.AttributeValue1;
                                skuModel.Attribute.AttributeValue3 = item.Attribute.AttributeValue3;
                            }
                            else if (matchSecondSourceSkuName == item.Attribute.AttributeName3)
                            {
                                skuModel.Attribute.AttributeValue1 = item.Attribute.AttributeValue1;
                                skuModel.Attribute.AttributeValue2 = item.Attribute.AttributeValue3;
                                skuModel.Attribute.AttributeValue3 = item.Attribute.AttributeValue2;
                            }
                        }
                        else
                        {
                            if (matchSecondSourceSkuName == item.Attribute.AttributeName1)
                            {
                                skuModel.Attribute.AttributeValue2 = item.Attribute.AttributeValue1;
                                if (matchThirdSourceSkuName == item.Attribute.AttributeName2)
                                {
                                    skuModel.Attribute.AttributeValue1 = item.Attribute.AttributeValue3;
                                    skuModel.Attribute.AttributeValue3 = item.Attribute.AttributeValue2;
                                }
                                else
                                {
                                    skuModel.Attribute.AttributeValue1 = item.Attribute.AttributeValue2;
                                    skuModel.Attribute.AttributeValue3 = item.Attribute.AttributeValue3;
                                }

                            }
                            else if (matchSecondSourceSkuName == item.Attribute.AttributeName2)
                            {
                                skuModel.Attribute.AttributeValue2 = item.Attribute.AttributeValue2;
                                if (matchThirdSourceSkuName == item.Attribute.AttributeName1)
                                {
                                    skuModel.Attribute.AttributeValue1 = item.Attribute.AttributeValue3;
                                    skuModel.Attribute.AttributeValue3 = item.Attribute.AttributeValue1;
                                }
                            }
                            else if (matchSecondSourceSkuName == item.Attribute.AttributeName3)
                            {
                                skuModel.Attribute.AttributeValue2 = item.Attribute.AttributeValue3;
                                if (matchThirdSourceSkuName == item.Attribute.AttributeName1)
                                {
                                    skuModel.Attribute.AttributeValue1 = item.Attribute.AttributeValue2;
                                    skuModel.Attribute.AttributeValue3 = item.Attribute.AttributeValue1;
                                }
                                else
                                {
                                    skuModel.Attribute.AttributeValue1 = item.Attribute.AttributeValue1;
                                    skuModel.Attribute.AttributeValue3 = item.Attribute.AttributeValue2;
                                }
                            }
                        }
                    }
                }
                else
                {
                    if (matchSecondSourceSkuName.IsEmpty())
                    {
                        // 是否匹配上第三个
                        if (!matchThirdSourceSkuName.IsEmpty())
                        {
                            if (matchFirstSourceSkuName == item.Attribute.AttributeName1)
                            {
                                skuModel.Attribute.AttributeValue1 = item.Attribute.AttributeValue1;
                                if (matchThirdSourceSkuName == item.Attribute.AttributeName2)
                                {
                                    skuModel.Attribute.AttributeValue2 = item.Attribute.AttributeValue3;
                                    skuModel.Attribute.AttributeValue3 = item.Attribute.AttributeValue2;
                                }
                            }
                            else if (matchFirstSourceSkuName == item.Attribute.AttributeName2)
                            {
                                skuModel.Attribute.AttributeValue1 = item.Attribute.AttributeValue2;
                                if (matchThirdSourceSkuName == item.Attribute.AttributeName1)
                                {
                                    skuModel.Attribute.AttributeValue2 = item.Attribute.AttributeValue3;
                                    skuModel.Attribute.AttributeValue3 = item.Attribute.AttributeValue1;
                                }
                                else
                                {
                                    skuModel.Attribute.AttributeValue2 = item.Attribute.AttributeValue1;
                                    skuModel.Attribute.AttributeValue3 = item.Attribute.AttributeValue3;
                                }
                            }
                            else if (matchFirstSourceSkuName == item.Attribute.AttributeName3)
                            {
                                skuModel.Attribute.AttributeValue1 = item.Attribute.AttributeValue3;
                                if (matchThirdSourceSkuName == item.Attribute.AttributeName1)
                                {
                                    skuModel.Attribute.AttributeValue2 = item.Attribute.AttributeValue2;
                                    skuModel.Attribute.AttributeValue3 = item.Attribute.AttributeValue1;
                                }
                                else
                                {
                                    skuModel.Attribute.AttributeValue2 = item.Attribute.AttributeValue1;
                                    skuModel.Attribute.AttributeValue3 = item.Attribute.AttributeValue2;
                                }
                            }
                        }
                    }
                    else
                    {
                        if (matchThirdSourceSkuName.IsEmpty())
                        {
                            if (matchFirstSourceSkuName == item.Attribute.AttributeName1)
                            {
                                if (matchSecondSourceSkuName == item.Attribute.AttributeName3)
                                {
                                    skuModel.Attribute.AttributeValue2 = item.Attribute.AttributeValue3;
                                    skuModel.Attribute.AttributeValue3 = item.Attribute.AttributeValue2;
                                }

                            }
                            else if (matchFirstSourceSkuName == item.Attribute.AttributeName2)
                            {
                                skuModel.Attribute.AttributeValue1 = item.Attribute.AttributeValue2;
                                if (matchSecondSourceSkuName == item.Attribute.AttributeName1)
                                {
                                    skuModel.Attribute.AttributeValue2 = item.Attribute.AttributeValue1;
                                }
                                else
                                {
                                    skuModel.Attribute.AttributeValue2 = item.Attribute.AttributeValue3;
                                    skuModel.Attribute.AttributeValue3 = item.Attribute.AttributeValue1;
                                }
                            }
                            else if (matchFirstSourceSkuName == item.Attribute.AttributeName3)
                            {
                                skuModel.Attribute.AttributeValue1 = item.Attribute.AttributeValue3;
                                if (matchSecondSourceSkuName == item.Attribute.AttributeName1)
                                {
                                    skuModel.Attribute.AttributeValue2 = item.Attribute.AttributeValue1;
                                    skuModel.Attribute.AttributeValue3 = item.Attribute.AttributeValue2;
                                }
                                else
                                {
                                    skuModel.Attribute.AttributeValue2 = item.Attribute.AttributeValue2;
                                    skuModel.Attribute.AttributeValue3 = item.Attribute.AttributeValue1;
                                }
                            }
                        }
                        else
                        {
                            if (matchFirstSourceSkuName == item.Attribute.AttributeName1)
                            {
                                if (matchSecondSourceSkuName == item.Attribute.AttributeName3)
                                {
                                    skuModel.Attribute.AttributeValue2 = item.Attribute.AttributeValue3;
                                    skuModel.Attribute.AttributeValue3 = item.Attribute.AttributeValue2;
                                }
                            }
                            else if (matchFirstSourceSkuName == item.Attribute.AttributeName2)
                            {
                                skuModel.Attribute.AttributeValue1 = item.Attribute.AttributeValue2;
                                if (matchSecondSourceSkuName == item.Attribute.AttributeName1)
                                {
                                    skuModel.Attribute.AttributeValue2 = item.Attribute.AttributeValue1;
                                    skuModel.Attribute.AttributeValue3 = item.Attribute.AttributeValue3;
                                }
                                else
                                {
                                    skuModel.Attribute.AttributeValue2 = item.Attribute.AttributeValue3;
                                    skuModel.Attribute.AttributeValue3 = item.Attribute.AttributeValue1;
                                }
                            }
                            else if (matchFirstSourceSkuName == item.Attribute.AttributeName3)
                            {
                                skuModel.Attribute.AttributeValue1 = item.Attribute.AttributeValue3;
                                if (matchSecondSourceSkuName == item.Attribute.AttributeName1)
                                {
                                    skuModel.Attribute.AttributeValue2 = item.Attribute.AttributeValue1;
                                    skuModel.Attribute.AttributeValue3 = item.Attribute.AttributeValue2;
                                }
                                else
                                {
                                    skuModel.Attribute.AttributeValue2 = item.Attribute.AttributeValue2;
                                    skuModel.Attribute.AttributeValue3 = item.Attribute.AttributeValue1;
                                }
                            }
                        }
                    }
                }
                #endregion
                // 为空证明sourceSkuModels只有一种
                if (string.IsNullOrWhiteSpace(skuModel.Attribute.AttributeValue2))
                {
                    skuModel.Attribute.AttributeValue2 = _defalutSkuValue;
                }
                // 为空证明sourceSkuModels只有2种
                if (string.IsNullOrWhiteSpace(skuModel.Attribute.AttributeValue3))
                {
                    skuModel.Attribute.AttributeValue3 = _defalutSkuValue;
                }
                // 为空证明sourceSkuModels只有一种
                if (string.IsNullOrWhiteSpace(skuModel.Attribute.AttributeValue1))
                {
                    skuModel.Attribute.AttributeValue1 = _defalutSkuValue;
                }

                var matchSkuValue1 = specDetails.Where(x => x.sell_property_name == firstPlatSkuName).FirstOrDefault()?.property_values.Where(x => x.sell_property_value_name == skuModel.Attribute.AttributeValue1).FirstOrDefault()?.sell_property_value_name ?? string.Empty;
                if (!string.IsNullOrWhiteSpace(matchSkuValue1))
                {
                    skuModel.Attribute.AttributeValue1 = matchSkuValue1;
                }
                var matchSkuValue2 = specDetails.Where(x => x.sell_property_name == secondPlatSkuName).FirstOrDefault()?.property_values.Where(x => x.sell_property_value_name == skuModel.Attribute.AttributeValue2).FirstOrDefault()?.sell_property_value_name ?? string.Empty;
                if (!string.IsNullOrWhiteSpace(matchSkuValue2))
                {
                    skuModel.Attribute.AttributeValue2 = matchSkuValue2;
                }
                var matchSkuValue3 = specDetails.Where(x => x.sell_property_name == thirdPlatSkuName).FirstOrDefault()?.property_values.Where(x => x.sell_property_value_name == skuModel.Attribute.AttributeValue3).FirstOrDefault()?.sell_property_value_name ?? string.Empty;
                if (!string.IsNullOrWhiteSpace(matchSkuValue3))
                {
                    skuModel.Attribute.AttributeValue3 = matchSkuValue3;
                }
                //skuModel.Subject = $"{skuModel.Attribute.AttributeValue1}{_splitCh}{skuModel.Attribute.AttributeValue2}{_splitCh}{skuModel.Attribute.AttributeValue3}";
                skuModel.SettlePrice = item.SettlePrice;
                skuModel.SalePrice = item.SalePrice;
                result.Add(skuModel);
            }
            #endregion

            return result;
        }
        /// <summary>
        /// 两个必填规格
        /// </summary>
        /// <param name="sourceSkuModels"></param>
        /// <param name="specDetails"></param>
        /// <returns></returns>
        private List<PtProductInfoSkuModel> TwoReqSku(List<PtProductInfoSkuModel> sourceSkuModels, List<RequiredSpecDetail> specDetails)
        {
            List<PtProductInfoSkuModel> result = new List<PtProductInfoSkuModel>();
            // 匹配的规格仅限两个，除非必填规格超过两个
            var requiredSpecs = specDetails.Where(x => x.is_required).ToList() ?? new List<RequiredSpecDetail>();
            // 所有必填规格的名称
            var reqSkuNames = requiredSpecs.Select(x => x.sell_property_name).ToList();
            // 原规格名称
            List<string> sourceTotalSku = GetSourceTotalSku(sourceSkuModels.FirstOrDefault());
            // 所有的平台名称
            List<string> allPlatSkuName = specDetails.Select(x => x.sell_property_name).ToList();
            // 原规格与平台规格匹配的结果
            var matchSkuNameResult = MatchSkuName(sourceTotalSku, allPlatSkuName);
            #region 仅返回两个必填规格
            var reqFirstIndex = allPlatSkuName.FindIndex(x => x == reqSkuNames[0]);
            var reqSecondIndex = allPlatSkuName.FindIndex(x => x == reqSkuNames[1]);
            var firstPlatSkuName = string.Empty;
            var secondPlatSkuName = string.Empty;
            #region 确定平台规格
            if (reqFirstIndex < reqSecondIndex)
            {
                firstPlatSkuName = allPlatSkuName[reqFirstIndex];
                secondPlatSkuName = allPlatSkuName[reqSecondIndex];
            }
            else
            {
                firstPlatSkuName = allPlatSkuName[reqSecondIndex];
                secondPlatSkuName = allPlatSkuName[reqFirstIndex];
            }
            var matchFirstSourceSkuName = string.Empty;//MatchSkuName(firstPlatSkuName, sourceTotalSku);
            var matchSecondSourceSkuName = string.Empty;//MatchSkuName(secondPlatSkuName, sourceTotalSku);
            foreach (var item in matchSkuNameResult)
            {
                if (item.Value == firstPlatSkuName)
                {
                    matchFirstSourceSkuName = item.Key;
                    continue;
                }
                if (item.Value == secondPlatSkuName)
                {
                    matchSecondSourceSkuName = item.Key;
                    continue;
                }
            }
            #endregion
            foreach (var item in sourceSkuModels)
            {
                PtProductInfoSkuModel skuModel = GetNewPtProductInfoSkuModel(item);
                //skuModel.Attribute = new PtProductSkuAttributeModel();
                skuModel.Attribute.AttributeName1 = firstPlatSkuName;
                skuModel.Attribute.AttributeName2 = secondPlatSkuName;

                #region 赋值
                skuModel.Attribute.AttributeValue1 = item.Attribute.AttributeValue1;
                skuModel.Attribute.AttributeValue2 = item.Attribute.AttributeValue2;
                // 原规格项只有一个
                if (sourceTotalSku.Count == 1)
                {
                    if (matchFirstSourceSkuName.IsEmpty())
                    {
                        if (matchSecondSourceSkuName.IsEmpty())
                        {
                            skuModel.Attribute.AttributeValue1 = item.Attribute.AttributeValue1;
                            skuModel.Attribute.AttributeValue2 = _defalutSkuValue;
                        }
                        else
                        {
                            if (matchSecondSourceSkuName == item.Attribute.AttributeName1)
                            {
                                skuModel.Attribute.AttributeValue1 = _defalutSkuValue;
                                skuModel.Attribute.AttributeValue2 = item.Attribute.AttributeValue1;
                            }
                        }
                    }
                    else
                    {
                        if (matchFirstSourceSkuName == item.Attribute.AttributeName2)
                        {
                            skuModel.Attribute.AttributeValue1 = item.Attribute.AttributeValue2;
                            skuModel.Attribute.AttributeValue2 = _defalutSkuValue;
                        }
                    }
                }
                else
                {
                    skuModel.Attribute.AttributeValue1 = item.Attribute.AttributeValue1;
                    skuModel.Attribute.AttributeValue2 = $"{item.Attribute.AttributeValue2}{_splitCh}{item.Attribute.AttributeValue3}".Trim(_splitCh);
                    if (matchFirstSourceSkuName.IsEmpty())
                    {
                        if (!matchSecondSourceSkuName.IsEmpty())
                        {
                            if (matchSecondSourceSkuName == item.Attribute.AttributeName1)
                            {
                                skuModel.Attribute.AttributeValue1 = $"{item.Attribute.AttributeValue2}{_splitCh}{item.Attribute.AttributeValue3}".Trim(_splitCh);
                                skuModel.Attribute.AttributeValue2 = $"{item.Attribute.AttributeValue1}";
                            }
                            else if (matchSecondSourceSkuName == item.Attribute.AttributeName2)
                            {
                                skuModel.Attribute.AttributeValue1 = $"{item.Attribute.AttributeValue1}{_splitCh}{item.Attribute.AttributeValue3}".Trim(_splitCh);
                                skuModel.Attribute.AttributeValue2 = $"{item.Attribute.AttributeValue2}";
                            }
                            else if (matchSecondSourceSkuName != item.Attribute.AttributeName3)
                            {
                                skuModel.Attribute.AttributeValue1 = $"{item.Attribute.AttributeValue1}{_splitCh}{item.Attribute.AttributeValue2}".Trim(_splitCh);
                                skuModel.Attribute.AttributeValue2 = $"{item.Attribute.AttributeValue3}";
                            }
                        }
                        //skuModel.Attribute.AttributeValue1 = item.Attribute.AttributeValue1;
                        //skuModel.Attribute.AttributeValue2 = $"{item.Attribute.AttributeValue2}{splitCh}{item.Attribute.AttributeValue3}".Trim(splitCh);
                    }
                    else
                    {
                        if (matchSecondSourceSkuName.IsEmpty())
                        {
                            if (matchFirstSourceSkuName == item.Attribute.AttributeName2)
                            {
                                skuModel.Attribute.AttributeValue1 = $"{item.Attribute.AttributeValue2}".Trim(_splitCh);
                                skuModel.Attribute.AttributeValue2 = $"{item.Attribute.AttributeValue1}{_splitCh}{item.Attribute.AttributeValue3}".Trim(_splitCh);
                            }
                            else if (matchFirstSourceSkuName == item.Attribute.AttributeName3)
                            {
                                skuModel.Attribute.AttributeValue1 = $"{item.Attribute.AttributeValue3}".Trim(_splitCh);
                                skuModel.Attribute.AttributeValue2 = $"{item.Attribute.AttributeValue1}{_splitCh}{item.Attribute.AttributeValue2}".Trim(_splitCh);
                            }
                        }
                        else
                        {
                            if (matchFirstSourceSkuName == item.Attribute.AttributeName2)
                            {
                                skuModel.Attribute.AttributeValue1 = $"{item.Attribute.AttributeValue2}".Trim(_splitCh);
                                skuModel.Attribute.AttributeValue2 = $"{item.Attribute.AttributeValue1}{_splitCh}{item.Attribute.AttributeValue3}".Trim(_splitCh);
                            }
                            else if (matchFirstSourceSkuName == item.Attribute.AttributeName3)
                            {
                                skuModel.Attribute.AttributeValue1 = $"{item.Attribute.AttributeValue3}".Trim(_splitCh);
                                skuModel.Attribute.AttributeValue2 = $"{item.Attribute.AttributeValue1}{_splitCh}{item.Attribute.AttributeValue2}".Trim(_splitCh);
                            }
                        }
                    }
                }
                #endregion
                // 为空证明sourceSkuModels只有一种
                if (string.IsNullOrWhiteSpace(skuModel.Attribute.AttributeValue2))
                {
                    skuModel.Attribute.AttributeValue2 = _defalutSkuValue;
                }
                var matchSkuValue1 = specDetails.Where(x => x.sell_property_name == firstPlatSkuName).FirstOrDefault()?.property_values.Where(x => x.sell_property_value_name == skuModel.Attribute.AttributeValue1).FirstOrDefault()?.sell_property_value_name ?? string.Empty;
                if (!string.IsNullOrWhiteSpace(matchSkuValue1))
                {
                    skuModel.Attribute.AttributeValue1 = matchSkuValue1;
                }
                var matchSkuValue2 = specDetails.Where(x => x.sell_property_name == secondPlatSkuName).FirstOrDefault()?.property_values.Where(x => x.sell_property_value_name == skuModel.Attribute.AttributeValue2).FirstOrDefault()?.sell_property_value_name ?? string.Empty;
                if (!string.IsNullOrWhiteSpace(matchSkuValue2))
                {
                    skuModel.Attribute.AttributeValue2 = matchSkuValue2;
                }
                //skuModel.Subject = $"{skuModel.Attribute.AttributeValue1}{_splitCh}{skuModel.Attribute.AttributeValue2}";
                skuModel.SettlePrice = item.SettlePrice;
                skuModel.SalePrice = item.SalePrice;
                result.Add(skuModel);
            }
            #endregion
            return result;
        }
        /// <summary>
        /// 有一个必填规格，剩下一个从非必填规格拿（如果有），凑够两个
        /// </summary>
        /// <param name="sourceSkuModels"></param>
        /// <param name="specDetails"></param>
        /// <returns></returns>
        private List<PtProductInfoSkuModel> OneReqSku(List<PtProductInfoSkuModel> sourceSkuModels, List<RequiredSpecDetail> specDetails)
        {
            List<PtProductInfoSkuModel> result = new List<PtProductInfoSkuModel>();
            // 匹配的规格仅限两个，除非必填规格超过两个
            var requiredSpecs = specDetails.Where(x => x.is_required).ToList() ?? new List<RequiredSpecDetail>();
            var norequiredSpecs = specDetails.Where(x => !x.is_required).ToList() ?? new List<RequiredSpecDetail>();

            // 所有必填规格的名称
            var reqSkuNames = requiredSpecs.Select(x => x.sell_property_name).ToList();
            // 所有非必填规格的名称
            var noreqSkuNames = norequiredSpecs.Select(x => x.sell_property_name).ToList();
            // 原规格名称
            List<string> sourceTotalSku = GetSourceTotalSku(sourceSkuModels.FirstOrDefault());
            // 所有的平台名称
            List<string> allPlatSkuName = specDetails.Select(x => x.sell_property_name).ToList();
            // 原规格与平台规格匹配的结果
            var matchSkuNameResult = MatchSkuName(sourceTotalSku, allPlatSkuName);
            #region 仅有一个必填
            // 无非必填规格
            if (noreqSkuNames.Count == 0)
            {
                var firstPlatSkuName = reqSkuNames.FirstOrDefault();
                foreach (var item in sourceSkuModels)
                {
                    PtProductInfoSkuModel skuModel = GetNewPtProductInfoSkuModel(item);
                    //skuModel.Attribute = new PtProductSkuAttributeModel();
                    skuModel.Attribute.AttributeName1 = firstPlatSkuName;
                    skuModel.Attribute.AttributeValue1 = $"{item.Attribute.AttributeValue1}{_splitCh}{item.Attribute.AttributeValue2}{_splitCh}{item.Attribute.AttributeValue3}".Trim(_splitCh);
                    skuModel.Attribute.AttributeValue1 = skuModel.Attribute.AttributeValue1.Trim(_splitCh);
                    //skuModel.Subject = skuModel.Attribute.AttributeValue1;

                    var matchSkuValue = specDetails.Where(x => x.sell_property_name == firstPlatSkuName).FirstOrDefault()?.property_values.Where(x => x.sell_property_value_name == skuModel.Attribute.AttributeValue1).FirstOrDefault()?.sell_property_value_name ?? string.Empty;

                    if (!string.IsNullOrWhiteSpace(matchSkuValue))
                    {
                        skuModel.Attribute.AttributeValue1 = matchSkuValue;
                    }
                    result.Add(skuModel);
                }
            }
            else
            {
                var firstPlatSkuName = allPlatSkuName.FirstOrDefault();
                var secondPlatSkuName = firstPlatSkuName == reqSkuNames[0] ? allPlatSkuName[1] : reqSkuNames[0];
                var matchFirstSourceSkuName = string.Empty;//MatchSkuName(firstPlatSkuName, sourceTotalSku);
                var matchSecondSourceSkuName = string.Empty;//MatchSkuName(secondPlatSkuName, sourceTotalSku);
                foreach (var item in matchSkuNameResult)
                {
                    if (item.Value == firstPlatSkuName)
                    {
                        matchFirstSourceSkuName = item.Key;
                    }
                    if (item.Value == secondPlatSkuName)
                    {
                        matchSecondSourceSkuName = item.Key;
                    }
                }
                //var matchThirdSourceSkuName = MatchSkuName(secondPlatSkuName, sourceTotalSku);
                if (sourceTotalSku.Count == 1)
                {
                    foreach (var item in sourceSkuModels)
                    {
                        PtProductInfoSkuModel skuModel = GetNewPtProductInfoSkuModel(item);
                        //skuModel.Attribute = new PtProductSkuAttributeModel();
                        skuModel.Attribute.AttributeName1 = firstPlatSkuName;
                        skuModel.Attribute.AttributeName2 = secondPlatSkuName;

                        if (matchFirstSourceSkuName.IsEmpty())
                        {
                            if (matchSecondSourceSkuName.IsEmpty())
                            {
                                skuModel.Attribute.AttributeValue1 = item.Attribute.AttributeValue1;
                                skuModel.Attribute.AttributeValue2 = _defalutSkuValue;
                            }
                            else
                            {
                                skuModel.Attribute.AttributeValue2 = item.Attribute.AttributeValue1;
                                skuModel.Attribute.AttributeValue1 = _defalutSkuValue;
                            }
                        }
                        else
                        {
                            skuModel.Attribute.AttributeValue1 = item.Attribute.AttributeValue1;
                            skuModel.Attribute.AttributeValue2 = _defalutSkuValue;
                        }
                        var matchSkuValue1 = specDetails.Where(x => x.sell_property_name == firstPlatSkuName).FirstOrDefault()?.property_values.Where(x => x.sell_property_value_name == skuModel.Attribute.AttributeValue1).FirstOrDefault()?.sell_property_value_name ?? string.Empty;
                        if (!string.IsNullOrWhiteSpace(matchSkuValue1))
                        {
                            skuModel.Attribute.AttributeValue1 = matchSkuValue1;
                        }
                        var matchSkuValue2 = specDetails.Where(x => x.sell_property_name == secondPlatSkuName).FirstOrDefault()?.property_values.Where(x => x.sell_property_value_name == skuModel.Attribute.AttributeValue2).FirstOrDefault()?.sell_property_value_name ?? string.Empty;
                        if (!string.IsNullOrWhiteSpace(matchSkuValue2))
                        {
                            skuModel.Attribute.AttributeValue2 = matchSkuValue2;
                        }
                        //skuModel.Subject = $"{skuModel.Attribute.AttributeValue1}{_splitCh}{skuModel.Attribute.AttributeValue2}";
                        result.Add(skuModel);
                    }
                }
                else// if(sourceTotalSku.Count == 2)
                {
                    foreach (var item in sourceSkuModels)
                    {
                        PtProductInfoSkuModel skuModel = GetNewPtProductInfoSkuModel(item);
                        //skuModel.Attribute = new PtProductSkuAttributeModel();
                        skuModel.Attribute.AttributeName1 = firstPlatSkuName;
                        skuModel.Attribute.AttributeName2 = secondPlatSkuName;

                        skuModel.Attribute.AttributeValue1 = item.Attribute.AttributeValue1;
                        skuModel.Attribute.AttributeValue2 = $"{item.Attribute.AttributeValue2}{_splitCh}{item.Attribute.AttributeValue3}".Trim(_splitCh);
                        if (matchFirstSourceSkuName.IsEmpty())
                        {
                            if (!matchSecondSourceSkuName.IsEmpty())
                            {
                                if (matchSecondSourceSkuName == item.Attribute.AttributeName1)
                                {
                                    skuModel.Attribute.AttributeValue1 = $"{item.Attribute.AttributeValue2}{_splitCh}{item.Attribute.AttributeValue3}".Trim(_splitCh);
                                    skuModel.Attribute.AttributeValue2 = item.Attribute.AttributeValue1;
                                }
                                else if (matchSecondSourceSkuName == item.Attribute.AttributeName2)
                                {
                                    skuModel.Attribute.AttributeValue1 = $"{item.Attribute.AttributeValue1}{_splitCh}{item.Attribute.AttributeValue3}".Trim(_splitCh);
                                    skuModel.Attribute.AttributeValue2 = item.Attribute.AttributeValue2;
                                }
                                else if (matchSecondSourceSkuName == item.Attribute.AttributeName3)
                                {
                                    skuModel.Attribute.AttributeValue1 = $"{item.Attribute.AttributeValue1}{_splitCh}{item.Attribute.AttributeValue2}".Trim(_splitCh);
                                    skuModel.Attribute.AttributeValue2 = item.Attribute.AttributeValue3;
                                }
                            }
                        }
                        else
                        {
                            if (matchFirstSourceSkuName == item.Attribute.AttributeName2)
                            {
                                skuModel.Attribute.AttributeValue1 = item.Attribute.AttributeValue2;
                                skuModel.Attribute.AttributeValue2 = $"{item.Attribute.AttributeValue1}{_splitCh}{item.Attribute.AttributeValue3}".Trim(_splitCh);
                            }
                            else if (matchFirstSourceSkuName == item.Attribute.AttributeName3)
                            {
                                skuModel.Attribute.AttributeValue1 = item.Attribute.AttributeValue3;
                                skuModel.Attribute.AttributeValue2 = $"{item.Attribute.AttributeValue1}{_splitCh}{item.Attribute.AttributeValue2}".Trim(_splitCh);
                            }
                        }
                        var matchSkuValue1 = specDetails.Where(x => x.sell_property_name == firstPlatSkuName).FirstOrDefault()?.property_values.Where(x => x.sell_property_value_name == skuModel.Attribute.AttributeValue1).FirstOrDefault()?.sell_property_value_name ?? string.Empty;
                        if (!string.IsNullOrWhiteSpace(matchSkuValue1))
                        {
                            skuModel.Attribute.AttributeValue1 = matchSkuValue1;
                        }
                        var matchSkuValue2 = specDetails.Where(x => x.sell_property_name == secondPlatSkuName).FirstOrDefault()?.property_values.Where(x => x.sell_property_value_name == skuModel.Attribute.AttributeValue2).FirstOrDefault()?.sell_property_value_name ?? string.Empty;
                        if (!string.IsNullOrWhiteSpace(matchSkuValue2))
                        {
                            skuModel.Attribute.AttributeValue2 = matchSkuValue2;
                        }
                        //skuModel.Subject = $"{skuModel.Attribute.AttributeValue1}{_splitCh}{skuModel.Attribute.AttributeValue2}";
                        skuModel.SettlePrice = item.SettlePrice;
                        skuModel.SalePrice = item.SalePrice;
                        result.Add(skuModel);
                    }
                }
            }
            #endregion
            return result;
        }
        /// <summary>
        /// 没有非必填规格，取前面两个规格再进行匹配
        /// </summary>
        /// <param name="sourceSkuModels"></param>
        /// <param name="spec"></param>
        /// <returns></returns>
        /// <exception cref="LogicException"></exception>
        private List<PtProductInfoSkuModel> NoReqSku(List<PtProductInfoSkuModel> sourceSkuModels, List<RequiredSpecDetail> specDetails)
        {
            List<PtProductInfoSkuModel> result = new List<PtProductInfoSkuModel>();
            var norequiredSpecs = specDetails.Where(x => !x.is_required).ToList() ?? new List<RequiredSpecDetail>();
            // 所有非必填规格的名称
            var noreqSkuNames = norequiredSpecs.Select(x => x.sell_property_name).ToList();

            List<string> sourceTotalSku = GetSourceTotalSku(sourceSkuModels.FirstOrDefault());
            #region 当仅有非必填规格时，按顺序最多只取前面两种规格
            if (norequiredSpecs.Count == 0) throw new LogicException("当必填规格数为0时，非必填规格数不能为0");
            if (norequiredSpecs.Count == 1)
            {
                var firstPlatSkuName = noreqSkuNames[0];
                foreach (var item in sourceSkuModels)
                {
                    PtProductInfoSkuModel skuModel = GetNewPtProductInfoSkuModel(item);
                    skuModel.Attribute.AttributeName1 = firstPlatSkuName;
                    skuModel.Attribute.AttributeValue1 = $"{item.Attribute.AttributeValue1}{_splitCh}{item.Attribute.AttributeValue2}{_splitCh}{item.Attribute.AttributeValue3}".Trim(_splitCh);

                    var matchSkuValue1 = specDetails.Where(x => x.sell_property_name == firstPlatSkuName).FirstOrDefault()?.property_values.Where(x => x.sell_property_value_name == skuModel.Attribute.AttributeValue1).FirstOrDefault()?.sell_property_value_name ?? string.Empty;
                    if (!string.IsNullOrWhiteSpace(matchSkuValue1))
                    {
                        skuModel.Attribute.AttributeValue1 = matchSkuValue1;
                    }
                    //skuModel.Subject = skuModel.Attribute.AttributeValue1;
                    result.Add(skuModel);
                }
            }
            else
            {
                var firstPlatSkuName = noreqSkuNames[0];
                var secondPlatSkuName = noreqSkuNames[1];

                //var matchFirstSourceSkuName = sourceTotalSku.FirstOrDefault(x => x == firstPlatSkuName || x.Contains(firstPlatSkuName) || firstPlatSkuName.Contains(x));

                var matchFirstSourceSkuName = MatchSkuName(firstPlatSkuName, sourceTotalSku);
                var matchSecondSourceSkuName = MatchSkuName(secondPlatSkuName, sourceTotalSku);
                if (!matchFirstSourceSkuName.IsEmpty() && !matchSecondSourceSkuName.IsEmpty() && matchFirstSourceSkuName == matchSecondSourceSkuName)
                {
                    matchSecondSourceSkuName = string.Empty;
                }
                foreach (var item in sourceSkuModels)
                {
                    PtProductInfoSkuModel skuModel = GetNewPtProductInfoSkuModel(item);
                    skuModel.Attribute.AttributeName1 = firstPlatSkuName;
                    skuModel.Attribute.AttributeName2 = secondPlatSkuName;

                    if (matchFirstSourceSkuName.IsEmpty())
                    {
                        if (matchSecondSourceSkuName.IsEmpty())
                        {
                            skuModel.Attribute.AttributeValue1 = $"{item.Attribute.AttributeValue1}";
                            skuModel.Attribute.AttributeValue2 = sourceTotalSku.Count == 1 ? _defalutSkuValue : $"{item.Attribute.AttributeValue2}{_splitCh}{item.Attribute.AttributeValue3}".Trim(_splitCh);
                        }
                        else
                        {
                            if (matchSecondSourceSkuName == item.Attribute.AttributeName1)
                            {
                                skuModel.Attribute.AttributeValue2 = $"{item.Attribute.AttributeValue1}";
                                skuModel.Attribute.AttributeValue1 = $"{item.Attribute.AttributeValue2}{_splitCh}{item.Attribute.AttributeValue3}".Trim(_splitCh);
                            }
                            else if (matchSecondSourceSkuName == item.Attribute.AttributeName2)
                            {
                                skuModel.Attribute.AttributeValue2 = $"{item.Attribute.AttributeValue2}";
                                skuModel.Attribute.AttributeValue1 = $"{item.Attribute.AttributeValue1}{_splitCh}{item.Attribute.AttributeValue3}".Trim(_splitCh);
                            }
                            else if (matchSecondSourceSkuName == item.Attribute.AttributeName3)
                            {
                                skuModel.Attribute.AttributeValue2 = $"{item.Attribute.AttributeValue3}";
                                skuModel.Attribute.AttributeValue1 = $"{item.Attribute.AttributeValue1}{_splitCh}{item.Attribute.AttributeValue2}".Trim(_splitCh);
                            }
                        }

                    }
                    else
                    {
                        if (matchFirstSourceSkuName == item.Attribute.AttributeName1)
                        {
                            skuModel.Attribute.AttributeValue1 = $"{item.Attribute.AttributeValue1}";
                            skuModel.Attribute.AttributeValue2 = sourceTotalSku.Count == 1 ? _defalutSkuValue : $"{item.Attribute.AttributeValue2}{_splitCh}{item.Attribute.AttributeValue3}".Trim(_splitCh);
                        }
                        else if (matchFirstSourceSkuName == item.Attribute.AttributeName2)
                        {
                            skuModel.Attribute.AttributeValue1 = $"{item.Attribute.AttributeValue2}";
                            skuModel.Attribute.AttributeValue2 = sourceTotalSku.Count == 1 ? _defalutSkuValue : $"{item.Attribute.AttributeValue1}{_splitCh}{item.Attribute.AttributeValue3}".Trim(_splitCh);
                        }
                        else
                        {
                            skuModel.Attribute.AttributeValue1 = $"{item.Attribute.AttributeValue3}";
                            skuModel.Attribute.AttributeValue2 = sourceTotalSku.Count == 1 ? _defalutSkuValue : $"{item.Attribute.AttributeValue1}{_splitCh}{item.Attribute.AttributeValue2}".Trim(_splitCh);
                        }

                    }
                    //skuModel.Attribute.AttributeValue1 = $"{item.Attribute.AttributeValue1}-{item.Attribute.AttributeValue2}-{item.Attribute.AttributeValue3}".Trim('-');

                    if (string.IsNullOrWhiteSpace(skuModel.Attribute.AttributeValue1))
                    {
                        skuModel.Attribute.AttributeValue1 = _defalutSkuValue;
                    }
                    if (string.IsNullOrWhiteSpace(skuModel.Attribute.AttributeValue2))
                    {
                        skuModel.Attribute.AttributeValue2 = _defalutSkuValue;
                    }

                    var matchSkuValue1 = specDetails.Where(x => x.sell_property_name == firstPlatSkuName).FirstOrDefault()?.property_values.Where(x => x.sell_property_value_name == skuModel.Attribute.AttributeValue1).FirstOrDefault()?.sell_property_value_name ?? string.Empty;
                    if (!string.IsNullOrWhiteSpace(matchSkuValue1))
                    {
                        skuModel.Attribute.AttributeValue1 = matchSkuValue1;
                    }
                    var matchSkuValue2 = specDetails.Where(x => x.sell_property_name == secondPlatSkuName).FirstOrDefault()?.property_values.Where(x => x.sell_property_value_name == skuModel.Attribute.AttributeValue2).FirstOrDefault()?.sell_property_value_name ?? string.Empty;
                    if (!string.IsNullOrWhiteSpace(matchSkuValue2))
                    {
                        skuModel.Attribute.AttributeValue2 = matchSkuValue1;
                    }
                    //skuModel.Subject = $"{skuModel.Attribute.AttributeValue1}{_splitCh}{skuModel.Attribute.AttributeValue2}";
                    skuModel.SettlePrice = item.SettlePrice;
                    skuModel.SalePrice = item.SalePrice;
                    result.Add(skuModel);
                }
            }
            #endregion
            return result;
        }
        #endregion
        private PtProductInfoSkuModel GetNewPtProductInfoSkuModel(PtProductInfoSkuModel tranSourceSkuModel)
        {
            var resultModel = new PtProductInfoSkuModel
            {
                SkuCode = tranSourceSkuModel.SkuCode,
                Attribute = new PtProductSkuAttributeModel(),
                FromBaseProductSkuUid = tranSourceSkuModel.FromBaseProductSkuUid,
                FromSupplierProductSkuUid = tranSourceSkuModel.FromSupplierProductSkuUid,
                Subject = tranSourceSkuModel.Subject,
                DistributePrice = tranSourceSkuModel.DistributePrice,
                //SalePrice = tranSourceSkuModel.SettlePrice ?? 0,
                StockCount = tranSourceSkuModel.StockCount,
                ImageObjectId = tranSourceSkuModel.ImageObjectId,
                ImageUrl = tranSourceSkuModel.ImageUrl,
                OtherJson = tranSourceSkuModel.OtherJson,
                RootNodeFxUserId = tranSourceSkuModel.RootNodeFxUserId,
                SharePathCode = tranSourceSkuModel.SharePathCode,
                PathNodeDeep = tranSourceSkuModel.PathNodeDeep,
                IsDefaultPadding = tranSourceSkuModel.IsDefaultPadding
            };

            resultModel.Attribute.AttributeCode1 = tranSourceSkuModel.Attribute?.AttributeCode1;
            resultModel.Attribute.AttributeCode2 = tranSourceSkuModel.Attribute?.AttributeCode2;
            resultModel.Attribute.AttributeCode3 = tranSourceSkuModel.Attribute?.AttributeCode3;
            return resultModel;
        }

        /// <summary>
        /// 匹配SkuName
        /// </summary>
        /// <param name="platSkuName">平台的SkuName</param>
        /// <param name="allSourceSkuName">源SkuName集合</param>
        /// <returns></returns>
        private string MatchSkuName(string platSkuName, List<string> noMatchSourceSkuName)
        {
            string matchResult = string.Empty;
            foreach (var item in noMatchSourceSkuName)
            {
                if (!platSkuName.IsEmpty() && (platSkuName == item || platSkuName.Contains(item) || item.Contains(platSkuName)))
                {
                    matchResult = item;
                    break;
                    //return item;
                }
            }
            return matchResult;
        }
        /// <summary>
        /// 规格名匹配
        /// </summary>
        /// <param name="sourceSkuName"></param>
        /// <param name="allPlatSkuName"></param>
        /// <returns></returns>
        private Dictionary<string, string> MatchSkuName(List<string> sourceSkuName, List<string> allPlatSkuName)
        {
            // key 源skuNanm，value 匹配的skuName(非必填和必填)
            Dictionary<string, string> result = new Dictionary<string, string>();
            // 精确匹配
            sourceSkuName.Where(x => allPlatSkuName.Contains(x)).ToList().ForEach(x =>
            {
                var matchSkuName = allPlatSkuName.FirstOrDefault(y => y == x);
                if (!result.ContainsKey(x))
                {
                    result.Add(x, matchSkuName);
                }

            });
            // 模糊匹配
            sourceSkuName.ForEach(x =>
            {
                allPlatSkuName.ForEach(a =>
                {
                    if (!result.ContainsKey(x) && (x.Contains(a) || a.Contains(x)))
                    {
                        result.Add(x, a);
                        return;
                    }
                });
            });
            return result;
        }

        /// <summary>
        /// 切换类目时进行匹配新类目属性
        /// </summary>
        /// <param name="ptProductInfoAttrs"></param>
        /// <param name="platformAttrJsons"></param>
        /// <returns></returns>
        public List<ListingProductEditResultModelByCateProps> MatchToutiaoNewCategoryAttr(string ptProductInfoAttrs, List<string> platformAttrJsons)
        {
            //var attributes = JsonConvert.DeserializeObject<List<ListingProductEditResultModelByCateProps>>(ptProductInfoAttrs)?.Select(x => new SupplierProductAttribute
            //{
            //    AttributeName = x.Name,
            //    AttributeValue = x.Value
            //}).ToList() ?? new List<SupplierProductAttribute>();
            var attributes = ptProductInfoAttrs.ToObject<List<ListingProductEditResultModelByCateProps>>()?.Select(x => new SupplierProductAttribute
            {
                AttributeName = x.Name,
                AttributeValue = x.Value
            }).ToList() ?? new List<SupplierProductAttribute>();
            List<DyCategoryPropInfoV2Model> dyProps = new List<DyCategoryPropInfoV2Model>();
            foreach (var json in platformAttrJsons)
            {
                var attr = json.ToObject<DyCategoryPropInfoV2Model>();//JsonConvert.DeserializeObject<DyCategoryPropInfoV2Model>(json);
                if (attr != null)
                {
                    dyProps.Add(attr);
                }
            }
            var cateProps = new List<ListingProductEditResultModelByCateProps>();
            dyProps.ForEach(item =>
            {
                var catePropRule = new ListingProductEditResultModelByCateProps();
                catePropRule.Name = item.property_name;
                if (attributes.FirstOrDefault(x => x.AttributeName == item.property_name) != null)
                    catePropRule.Value = attributes.FirstOrDefault(x => x.AttributeName == item.property_name).AttributeValue;
                catePropRule.FieldType = FieldTypeEnum.input.ToString();
                catePropRule.Options = new List<string>();
                catePropRule.Rule = new ListingProductEditResultModelByRule();
                catePropRule.Rule.IsRequired = item.required == 1;
                if (item.options?.Any() == true)
                {
                    catePropRule.Options = item.options.Select(x => x.name).ToList();
                }
                if (item.type == "select" && catePropRule.Options.Any())
                {
                    catePropRule.FieldType = FieldTypeEnum.singleCheck.ToString();
                }
                if (item.type == "multi_select" && catePropRule.Options.Any())
                {
                    catePropRule.FieldType = FieldTypeEnum.checkbox.ToString();
                    catePropRule.Rule.MaxLength = item.multi_select_max.ToString();
                }
                if (item.type == "date_time_range")
                {
                    catePropRule.FieldType = FieldTypeEnum.dateTimeRange.ToString();
                    catePropRule.MeasureTemplates = GetTouTiaoMeasureTemplates(item, attributes);
                }
                if (item.type == "date_time")
                {
                    catePropRule.Value = GetTouTiaoMeasureTemplatesByShowTime(catePropRule.Value, false);

                    catePropRule.FieldType = FieldTypeEnum.dateTime.ToString();
                    catePropRule.MeasureTemplates = GetTouTiaoMeasureTemplates(item, attributes);
                }
                if (item.type == "measure")
                {
                    catePropRule.FieldType = FieldTypeEnum.measure.ToString();
                    catePropRule.MeasureTemplates = GetTouTiaoMeasureTemplates(item, attributes);
                }
                if (item.type == "multi_value_measure")
                {
                    catePropRule.FieldType = FieldTypeEnum.multiValueMeasure.ToString();
                    catePropRule.MeasureTemplates = GetTouTiaoMeasureTemplates(item, attributes);
                }
                if (catePropRule.Name == "品牌")
                {
                    var brandValue = attributes?.FirstOrDefault(x => x.AttributeName == "品牌")?.AttributeValue;
                    catePropRule.FieldType = "brandSearch";
                    catePropRule.Value = brandValue ?? string.Empty;
                }

                cateProps.Add(catePropRule);
            });
            return cateProps;
        }
        /// <summary>
        /// 小站商品属性匹配抖音属性
        /// </summary>
        /// <param name="supplierProductAttributeJson"></param>
        /// <param name="platformAttrJson"></param>
        /// <returns></returns>
        public List<ListingProductEditResultModelByCateProps> MatchToutiaoAttr(string supplierProductAttributeJson, List<string> platformAttrJsons)
        {
            //var attributes = JsonConvert.DeserializeObject<List<ListingProductEditResultModelByCateProps>>(supplierProductAttributeJson)?.Select(x => new SupplierProductAttribute
            //{
            //    AttributeName = x.Name,
            //    AttributeValue = x.Value
            //}).ToList() ?? new List<SupplierProductAttribute>();
            var attributes = supplierProductAttributeJson.ToObject<List<ListingProductEditResultModelByCateProps>>()?.Select(x => new SupplierProductAttribute
            {
                AttributeName = x.Name,
                AttributeValue = x.Value
            }).ToList() ?? new List<SupplierProductAttribute>();
            if (!string.IsNullOrWhiteSpace(supplierProductAttributeJson) && attributes?.Count == 0)
            {
                Log.WriteError($"{nameof(supplierProductAttributeJson)}反序列化失败，源字符串：{supplierProductAttributeJson}");
            }

            Log.Debug(() => $"1. 进入 MatchToutiaoAttr", "TranAttributeAndSku.txt");

            List<DyCategoryPropInfoV2Model> dyProps = new List<DyCategoryPropInfoV2Model>();
            foreach (var json in platformAttrJsons)
            {
                try
                {
                    //var attr = JsonConvert.DeserializeObject<DyCategoryPropInfoV2Model>(json);
                    var attr = json.ToObject<DyCategoryPropInfoV2Model>();
                    if (attr != null)
                    {
                        dyProps.Add(attr);
                    }
                }
                catch (Exception ex)
                {
                    Log.WriteError($"平台属性字符串序列化失败：{json},异常原因：{ex.Message}", "TranAttributeAndSku.txt");
                }
            }

            ////类目属性
            var cateErrorList = new List<CateErrorInfo>();
            // 因现存在数据库的类目属性值（在platformAttrJsons中）未包含其级联属性，故先放着
            Dictionary<string, List<ProductFormatNew>> cascadeProps = new Dictionary<string, List<ProductFormatNew>>();

            Dictionary<string, List<ProductFormatNew>> propduct_format_new_dict = new Dictionary<string, List<ProductFormatNew>>();
            try
            {
                propduct_format_new_dict = GetProductformatV2(dyProps, attributes, cateErrorList, cascadeProps);
            }
            catch (Exception ex)
            {
                Log.WriteError($"2. GetProductformatV2 报错: {ex.Message} 堆栈：{ex.StackTrace}", "TranAttributeAndSku.txt");

                throw;
            }

            var cateProps = new List<ListingProductEditResultModelByCateProps>();
            try
            {
                dyProps.ForEach(item =>
                {
                    var catePropRule = new ListingProductEditResultModelByCateProps();
                    catePropRule.Name = item.property_name;
                    catePropRule.Id = item.property_id.ToString2();
                    if (propduct_format_new_dict.ContainsKey(item.property_id.ToString()))
                        catePropRule.Value = propduct_format_new_dict[item.property_id.ToString()].FirstOrDefault()?.name;
                    catePropRule.FieldType = FieldTypeEnum.input.ToString();
                    catePropRule.Options = new List<string>();
                    catePropRule.Rule = new ListingProductEditResultModelByRule();
                    catePropRule.Rule.IsRequired = item.required == 1;
                    if (item.options?.Any() == true)
                    {
                        catePropRule.Options = item.options.Select(x => x.name).ToList();
                    }
                    if (item.type == "select" && catePropRule.Options.Any())
                    {
                        catePropRule.FieldType = FieldTypeEnum.singleCheck.ToString();
                    }
                    if (item.type == "multi_select" && catePropRule.Options.Any())
                    {
                        catePropRule.FieldType = FieldTypeEnum.checkbox.ToString();
                        catePropRule.Rule.MaxLength = item.multi_select_max.ToString();
                    }
                    if (item.type == "date_time_range")
                    {
                        catePropRule.FieldType = FieldTypeEnum.dateTimeRange.ToString();
                        catePropRule.MeasureTemplates = GetTouTiaoMeasureTemplates(item, attributes);
                    }
                    if (item.type == "date_time")
                    {
                        catePropRule.Value = GetTouTiaoMeasureTemplatesByShowTime(catePropRule.Value, false);

                        catePropRule.FieldType = FieldTypeEnum.dateTime.ToString();
                        catePropRule.MeasureTemplates = GetTouTiaoMeasureTemplates(item, attributes);
                    }
                    if (item.type == "measure")
                    {
                        catePropRule.FieldType = FieldTypeEnum.measure.ToString();
                        catePropRule.MeasureTemplates = GetTouTiaoMeasureTemplates(item, attributes);
                    }
                    if (item.type == "multi_value_measure")
                    {
                        catePropRule.FieldType = FieldTypeEnum.multiValueMeasure.ToString();
                        catePropRule.MeasureTemplates = GetTouTiaoMeasureTemplates(item, attributes);
                    }
                    if (catePropRule.Name == "品牌")
                    {
                        var brandValue = attributes?.FirstOrDefault(x => x.AttributeName == "品牌")?.AttributeValue;
                        catePropRule.FieldType = "brandSearch";
                        catePropRule.Value = brandValue ?? string.Empty;
                    }

                    catePropRule.Rule.DiyType = item.diy_type;
                    cateProps.Add(catePropRule);
                });
            }
            catch (Exception ex)
            {
                Log.WriteError($"3. dyProps 循环 报错: {ex.Message} 堆栈：{ex.StackTrace}", "TranAttributeAndSku.txt");

                throw;
            }
    
            return cateProps;
        }

        /// <summary>
        /// 获取类目属性V2，增加支持类目属性级联与销售属性级联功能
        /// </summary>
        /// <param name="categoryPropList"></param>
        /// <param name="product"></param>
        /// <param name="cateErrorList"></param>
        /// <returns></returns>
        private Dictionary<string, List<ProductFormatNew>> GetProductformatV2(List<DyCategoryPropInfoV2Model> categoryPropList, List<SupplierProductAttribute> supplierProductAttributes, List<CateErrorInfo> cateErrorList, Dictionary<string, List<ProductFormatNew>> cascadeProps, bool useDefault = false)
        {
            if (categoryPropList == null || categoryPropList.Any() == false)
                return null;

            if (supplierProductAttributes == null)
                supplierProductAttributes = new List<SupplierProductAttribute>();

            //过滤属性值为null的
            supplierProductAttributes = supplierProductAttributes.Where(a => !string.IsNullOrEmpty(a.AttributeValue)).ToList();
            var mappings = ProductPropertiesMapping.GetTouTiaoProperties();

            var result = new Dictionary<string, List<ProductFormatNew>>();

            var specials = new List<string>() { "食品生产许可证编号", "生产企业名称", "CCC证书编号", "备案/批准文号", "生产企业统一社会信用代码", "无公害农产品认证编号", "消毒品卫生许可证编号", "ISBN编号" };
            foreach (var item in categoryPropList)
            {
                //1：创建商品时该属性字段必填 0：创建商品时该属性字段选填
                //if (item.required == 0 && specials.Contains(item.property_name) == false)
                //    continue;

                var cateErrorInfos = new List<CateErrorInfo>();

                var defaultVal = string.Empty;
                //var defaultVal = "默认";
                //if (item.property_name == "食品生产许可证编号") defaultVal = "SC11734040305154";
                //else if (item.property_name == "生产企业名称") defaultVal = "见包装";
                //else if (item.property_name == "CCC证书编号") defaultVal = $"{DateTime.Now.AddYears(-1):yyyy}01090{CommUtls.GenerateRandomNumber(7)}";
                //else if (item.property_name == "备案/批准文号") defaultVal = $"国妆特字G{DateTime.Now.AddYears(-1).ToString("yyyyMMdd")}{Utility.CommUtls.GenerateRandomNumber(2)}";
                //else if (item.property_name == "生产企业统一社会信用代码") defaultVal = string.Format($"{DateTime.Now.ToString("yyyyMMdd{0}HHmm")}", "DGJAPP");
                //else if (item.property_name == "无公害农产品认证编号") defaultVal = $"WGH-17-{DateTime.Now.ToString("ddHHmm")}";
                //else if (item.property_name == "消毒品卫生许可证编号") defaultVal = $"浙卫消证字（{DateTime.Now.ToString("yyyy")}）第{DateTime.Now.ToString("MMdd")}号";
                //else if (item.property_name == "ISBN编号") defaultVal = $"{DateTime.Now.ToString("MMddHHmmss")}";   //应仅为10位或13位阿拉伯数字
                //else if (item.property_name.Contains("编号")) defaultVal = $"{DateTime.Now.ToString("MMddHHmmss")}";

                var propVal = GetDyCatePropetyVal(item, defaultVal, supplierProductAttributes, specials, mappings, out cateErrorInfos, useDefault);
                if (cateErrorInfos.Any())
                    cateErrorList.AddRange(cateErrorInfos);
                if (propVal != null && propVal.Any() && !result.ContainsKey(item.property_id.ToString()))
                    result.Add(item.property_id.ToString(), propVal);

                //级联属性,需要重新获取并重新填写（propVal.FirstOrDefault().value > 0 说明有填过值，就需要重新获取）
                if (item.has_sub_property && propVal.Any() && propVal.FirstOrDefault().value > 0 && !cascadeProps.ContainsKey(item.property_id.ToString()))
                {
                    cascadeProps.Add(item.property_id.ToString(), propVal);
                }
            }


            //不能全部字段都不提交，如果没有选择一个
            //若option为空数组， value可填 0
            if (result.Any() == false && categoryPropList.Any() == true)
            {
                var propModel = categoryPropList.FirstOrDefault();

                if (propModel.type == "text")
                {
                    result.Add(propModel.property_id.ToString(), new List<ProductFormatNew>() { new ProductFormatNew() { diy_type = propModel.diy_type, name = "" } });
                }
                else
                {
                    if (propModel.options.Any() == false)
                    {
                        result.Add(propModel.property_id.ToString(), new List<ProductFormatNew>() { new ProductFormatNew() { diy_type = propModel.diy_type, value = 0, name = "" } });
                    }
                }
            }

            return result;
        }
        private List<ProductFormatNew> GetDyCatePropetyVal(DyCategoryPropInfoV2Model propModel, string defaultVal, List<SupplierProductAttribute> supplierProductAttributes, List<string> specials, List<ProductPropertiesMapping> mappings, out List<CateErrorInfo> cateErrorInfos, bool useDefault = false)
        {
            List<ProductFormatNew> result = new List<ProductFormatNew>();
            var propVal = string.Empty;
            cateErrorInfos = new List<CateErrorInfo>();
            if (propModel.type == "text")
            {
                if (supplierProductAttributes?.Any() == true)
                {
                    var mappingProp = supplierProductAttributes.FirstOrDefault(f => f.AttributeName == propModel.property_name);
                    if (mappingProp == null && (propModel.property_name.Contains("净含量") || propModel.property_name.Contains("容量")))
                    {
                        mappingProp = supplierProductAttributes.FirstOrDefault(f => f.AttributeName.Contains("净含量") || f.AttributeName.Contains("容量"));
                    }
                    if (mappingProp == null && propModel.property_name.Contains("备案"))
                    {
                        mappingProp = supplierProductAttributes.FirstOrDefault(f => f.AttributeName.Contains("备案"));
                    }
                    if (mappingProp == null && propModel.property_name.Contains("批准文号"))
                    {
                        mappingProp = supplierProductAttributes.FirstOrDefault(f => f.AttributeName.Contains("特殊用途化妆品证号"));
                    }
                    if (mappingProp == null && (propModel.property_name.Contains("生产许可证") || propModel.property_name.Contains("生产许可编码")))
                    {
                        mappingProp = supplierProductAttributes.FirstOrDefault(f => f.AttributeName.Contains("生产许可证"));
                    }
                    if (mappingProp != null)
                    {
                        if (mappingProp != null && (propModel.property_name.Contains("净含量") || propModel.property_name.Contains("容量")))
                        {
                            //从阿里的净含量中提取净含量
                            var patternNumber = @"\d+";
                            var size = "";
                            foreach (Match match in Regex.Matches(mappingProp.AttributeValue, patternNumber))
                            {
                                var val = match.Value;
                                size = val?.TrimStart('0');
                                if (size.ToLong() > 0)
                                    break;
                            }
                            var patternUnit = @"[a-zA-Z]+";
                            var unit = "ml";
                            foreach (Match match in Regex.Matches(mappingProp.AttributeValue, patternUnit))
                            {
                                var val = match.Value;
                                unit = val;
                                if (string.IsNullOrEmpty(unit) == false)
                                    break;
                            }
                            propVal = size + unit;
                        }
                        else if (propModel.property_name == "货号")
                        {
                            //if (mappingProp.AttributeValue == "其他")
                            //    propVal = $"M{DateTime.Now.ToString("HHmmssff")}";
                            //else
                            //    propVal = $"M{DateTime.Now.ToString("ssff")}-{mappingProp.AttributeValue}";
                            propVal = mappingProp.AttributeValue;
                        }
                        else if (propModel.property_name == "型号")
                        {
                            //if (Regex.IsMatch(mappingProp.AttributeValue, @"^\d+$|^其他$|^[^a-zA-Z]$+"))
                            //    propVal = $"S{DateTime.Now.ToString("HHmmssff")}";
                            //else
                            propVal = mappingProp.AttributeValue;
                        }
                        else if (propModel.property_name == "材质")
                        {
                            //if (Regex.IsMatch(mappingProp.AttributeValue, @"^\d+$|^其他$|^[^a-zA-Z]$+"))
                            //    propVal = $"P{DateTime.Now.ToString("HHmmssff")}";
                            //else
                            propVal = mappingProp.AttributeValue;

                            cateErrorInfos.Add(new CateErrorInfo()
                            {
                                CateName = propModel.property_name,
                                ExceptionType = CateExceptionType.UseDefault
                            });
                        }
                        else if (propModel.property_name == "毛重" || propModel.property_name == "包装体积")
                        {
                            if (!Regex.IsMatch(mappingProp.AttributeValue, @"\d+")) propVal = string.Empty;
                        }
                        else
                        {
                            propVal = mappingProp.AttributeValue;
                        }
                    }
                    else if (propModel.required == 1 || specials.Contains(propModel.property_name))
                    {
                        propVal = defaultVal;
                        cateErrorInfos.Add(new CateErrorInfo()
                        {
                            CateName = propModel.property_name,
                            ExceptionType = CateExceptionType.UseDefault
                        });
                    }
                }

                bool isUseDefault = false;
                propVal = ProductPropertiesMapping.GetPropertiesValue(mappings, propModel.property_name, propVal, propModel.required == 1, out isUseDefault);
                if (!string.IsNullOrEmpty(propVal))
                {
                    result.Add(new ProductFormatNew()
                    {
                        name = propVal
                    });
                }
            }
            else if (propModel.type == "timestamp")
            {
                //1.查找供应商商品是否有这个属性
                var supplierProps = supplierProductAttributes.Where(f => f.AttributeName == propModel.property_name).ToList();
                string value = "";
                foreach (var supplierProp in supplierProps)
                {
                    try
                    {
                        value = supplierProp.AttributeValue?.ToDateTime().Value.ToStamp().ToString();
                    }
                    catch (Exception ex) { }
                    if (string.IsNullOrEmpty(value) != false && value != "0")
                    {
                        break;
                    }
                }

                if (string.IsNullOrEmpty(value) && propModel.required == 1)
                {
                    //cateErrorInfos.Add(new CateErrorInfo()
                    //{
                    //    CateName = propModel.property_name,
                    //    ExceptionType = Enum.CateExceptionType.UseDefault
                    //});
                    value = DateTime.Now.ToStamp().ToString();
                }

                if (string.IsNullOrEmpty(value) == false)
                {
                    result.Add(new ProductFormatNew()
                    {
                        name = value,
                    });
                }

            }
            else if (propModel.type == "timeframe")//时间区间类型，默认设置俩个区间
            {
                if (propModel.required == 1)
                {
                    //cateErrorInfos.Add(new CateErrorInfo()
                    //{
                    //    CateName = propModel.property_name,
                    //    ExceptionType = Enum.CateExceptionType.UseDefault
                    //});

                    string part1 = DateTime.Now.AddMonths(-1).ToStamp().ToString();
                    string part2 = DateTime.Now.ToStamp().ToString();
                    result.Add(new ProductFormatNew()
                    {
                        name = part1,
                    });

                    result.Add(new ProductFormatNew()
                    {
                        name = part2,
                    });
                }
            }
            else if (propModel.type == "date_time_range")//时间段
            {
                //匹配货源属性
                var supplierProps = supplierProductAttributes.Where(f => f.AttributeName == propModel.property_name).ToList();
                if (supplierProps.Count == 0)
                    supplierProps = supplierProductAttributes.Where(f => propModel.property_name.IndexOf(f.AttributeName) != -1).ToList();


                //度量衡值measure_info
                var measure_info = new CatePropetyValueMeasureInfo();
                var measureTemplates = propModel.measure_templates.FirstOrDefault();
                measure_info.template_id = measureTemplates.template_id;//度量衡模板id
                measure_info.values = new List<SpecInfoValueMeasureInfoValue>();

                var isFist = true;
                var supplierProp = supplierProps.FirstOrDefault();

                var timeValue = string.IsNullOrEmpty(supplierProp?.AttributeValue) ? "" : supplierProp?.AttributeValue;
                var timeValueList = new List<string>();
                if (timeValue.IndexOf(",") != -1) //编辑的时候，会有这种时间,两种时间
                {
                    timeValueList = timeValue.Split(',').ToList();
                    timeValueList.Remove("");//删除空值的
                }

                if (timeValueList.Count > 1)
                {
                    var leftTime = GetTouTiaoMeasureTemplatesByShowTime(timeValueList[0], false);
                    var rightTime = GetTouTiaoMeasureTemplatesByShowTime(timeValueList[1], false);

                    measureTemplates.value_modules.ForEach(m =>
                    {
                        var specInfoValueMeasureInfoValue = new SpecInfoValueMeasureInfoValue()
                        {
                            module_id = m.module_id,
                            prefix = m.prefix,
                            suffix = isFist ? m.suffix : "",
                            value = isFist ? leftTime : rightTime,
                            unit_id = 0,
                            unit_name = string.Empty,
                        };
                        measure_info.values.Add(specInfoValueMeasureInfoValue);
                        isFist = false;
                    });

                }
                else
                {
                    timeValue = timeValue.Replace(",", "");
                    measureTemplates.value_modules.ForEach(m =>
                    {
                        timeValue = GetTouTiaoMeasureTemplatesByShowTime(timeValue, false);
                        var specInfoValueMeasureInfoValue = new SpecInfoValueMeasureInfoValue()
                        {
                            module_id = m.module_id,
                            prefix = m.prefix,
                            suffix = isFist ? m.suffix : "",
                            value = timeValue,
                            unit_id = 0,
                            unit_name = string.Empty,
                        };
                        measure_info.values.Add(specInfoValueMeasureInfoValue);
                        isFist = false;
                    });
                }




                result.Add(new ProductFormatNew()
                {
                    diy_type = propModel.diy_type,
                    value = 0,
                    name = string.Join(",", measure_info.values.Select(s => s.value)),
                    measure_info = measure_info
                });
            }
            else if (propModel.type == "measure" || propModel.type == "date_time")//单值度量衡
            {
                if (propModel.measure_templates?.Count > 0)
                {
                    //匹配货源属性
                    var supplierProps = supplierProductAttributes.Where(f => f.AttributeName == propModel.property_name).ToList();
                    if (supplierProps.Count == 0)
                        supplierProps = supplierProductAttributes.Where(f => propModel.property_name.IndexOf(f.AttributeName) != -1).ToList();

                    //度量衡值measure_info
                    var measure_info = new CatePropetyValueMeasureInfo();
                    var measureTemplates = propModel.measure_templates.FirstOrDefault();
                    measure_info.template_id = measureTemplates.template_id;//度量衡模板id
                    measure_info.values = new List<SpecInfoValueMeasureInfoValue>();


                    var valueModules = measureTemplates.value_modules.FirstOrDefault();//非多值度量衡时，这个就是单个
                    var unitRes = valueModules?.units?.FirstOrDefault(u => supplierProps.FirstOrDefault(s => s.AttributeValue.IndexOf(u.unit_name) != -1) != null);//匹配单位，货源和度量衡规则

                    //提取单位和货源值
                    var supplierProp = supplierProps.FirstOrDefault(s => s.AttributeValue.IndexOf(unitRes?.unit_name ?? "") != -1) ?? supplierProps.FirstOrDefault();
                    var value = "";
                    if (propModel.type == "date_time")//时间戳，转换下
                        value = GetTouTiaoMeasureTemplatesByShowTime(supplierProp?.AttributeValue, true);
                    else
                    {
                        string valueMumbers = Regex.Replace(supplierProp?.AttributeValue ?? "", "[^0-9]", "") ?? supplierProp?.AttributeValue;
                        value = !string.IsNullOrEmpty(valueMumbers) ? valueMumbers : supplierProp?.AttributeValue;
                    }

                    var specInfoValueMeasureInfoValue = new SpecInfoValueMeasureInfoValue()
                    {
                        module_id = valueModules.module_id,
                        prefix = valueModules.prefix,
                        suffix = valueModules.suffix,
                        value = value,
                        unit_id = unitRes?.unit_id ?? 0,
                        unit_name = unitRes?.unit_name ?? string.Empty,
                    };
                    measure_info.values.Add(specInfoValueMeasureInfoValue);

                    result.Add(new ProductFormatNew()
                    {
                        diy_type = propModel.diy_type,
                        value = 0,
                        name = specInfoValueMeasureInfoValue.value + specInfoValueMeasureInfoValue.unit_name + specInfoValueMeasureInfoValue.suffix,
                        measure_info = measure_info
                    });

                }
            }
            else if (propModel.type == "multi_value_measure")//多值度量衡
            {
                //说明：这个类型下，是抖音定制的，只需要达到100%即可

                //匹配货源属性
                var supplierProps = supplierProductAttributes.Where(f => f.AttributeName == propModel.property_name).ToList();

                if (supplierProps.Count == 0)
                {
                    supplierProps = supplierProductAttributes.Where(f => propModel.property_name.IndexOf(f.AttributeName) != -1).ToList();
                    if (supplierProps.Count == 0) //依旧匹配不到，名称类似的情况下，转换
                    {
                        if (propModel.property_name == "面料材质")
                            supplierProps = supplierProductAttributes.Where(f => f.AttributeName == "主面料成分" || f.AttributeName == "主面料成分的含量").ToList();
                    }
                }

                var suProps = supplierProps.FirstOrDefault(s => s.AttributeName == "面料材质");
                if (suProps != null && suProps.AttributeValue.IndexOf("||") != -1)//铺货编辑的场景
                {
                    suProps.AttributeValue = suProps.AttributeValue.Replace("||", "|");
                    var attributeValueList = suProps.AttributeValue.Split('|').ToList();
                    attributeValueList.Remove("");//删除空的数组值

                    var measureTemplates = propModel.measure_templates.FirstOrDefault();
                    var materialList = measureTemplates.value_modules.FirstOrDefault(f => f.values.Count > 0).values;//面料列表

                    materialList = materialList.Where(w => supplierProps.FirstOrDefault(s => s.AttributeValue == w.value_name) != null).ToList();

                    //棉,80||其他材质,20    按|| 分组就是两个，然后拼装两个productFormatNew
                    attributeValueList.ForEach(a =>
                    {
                        var productFormatNew = GetMultiValueMeasureBymeasureInfoByEdit(propModel, a);
                        result.Add(productFormatNew);

                    });

                }
                else if (propModel.property_name == "面料材质")//对接的时候，只有这种类型的
                {
                    var measureTemplates = propModel.measure_templates.FirstOrDefault();
                    var materialList = measureTemplates.value_modules.FirstOrDefault(f => f.values.Count > 0).values;//面料列表

                    materialList = materialList.Where(w => supplierProps.FirstOrDefault(s => s.AttributeValue == w.value_name) != null).ToList();
                    double percentNumber = 100;
                    materialList.ForEach(f =>
                    {

                        var percentSupplierProps = supplierProps.FirstOrDefault(sup => sup.AttributeName == "主面料成分的含量");
                        if (percentSupplierProps != null)
                        {
                            double percentSupplierPropsNumber = 0;
                            try
                            {
                                var attributeValue = percentSupplierProps.AttributeValue.Replace("%", "");
                                percentSupplierPropsNumber = Math.Round(Convert.ToDouble(attributeValue), 2);


                                percentNumber = Math.Round(percentNumber - percentSupplierPropsNumber, 2);
                            }
                            catch (Exception ee) { }

                            //百分比是有效的
                            if (percentSupplierPropsNumber > 0)
                            {
                                var productFormatNew = GetMultiValueMeasureBymeasureInfo(propModel, percentSupplierPropsNumber.ToString(), f.value_name);
                                result.Add(productFormatNew);
                            }
                            else
                            {
                                var productFormatNew = GetMultiValueMeasureBymeasureInfo(propModel, "", f.value_name);
                                result.Add(productFormatNew);
                                percentNumber = 0;
                            }

                        }
                        else
                        {
                            var productFormatNew = GetMultiValueMeasureBymeasureInfo(propModel, "", f.value_name);
                            result.Add(productFormatNew);
                            percentNumber = 0;
                        }

                    });

                    //需要达到100
                    if (percentNumber < 100 && percentNumber > 0)
                    {
                        var productFormatNew = GetMultiValueMeasureBymeasureInfo(propModel, Convert.ToString(percentNumber), "其他");
                        result.Add(productFormatNew);
                    }


                }

                //其他的暂时使用默认值
                if (result.Count == 0)
                    result.Add(GetMultiValueMeasureBymeasureInfo(propModel, "", ""));
            }
            else
            {
                //1.属性值
                var propValues = propModel.options;

                //2.查找供应商商品是否有这个属性
                var supplierProps = supplierProductAttributes.Where(f => f.AttributeName == propModel.property_name).ToList();

                //多选最多选择5项
                if (propModel.type == "multi_select")
                    supplierProps = supplierProps.Take(5).ToList();

                //3.是否支持自定义
                ProductFormatNew hasVal = null;
                if (propModel.diy_type == 1)
                {
                    foreach (var supplierProp in supplierProps.Where(s => !s.AttributeValue.Contains("其他") && !s.AttributeValue.Contains("other")))
                    {
                        hasVal = new ProductFormatNew()
                        {
                            diy_type = 1,
                            name = supplierProp.AttributeValue,
                            value = 0
                        };
                        result.Add(hasVal);
                    }
                }
                //3.不支持自定义属性判断供应商属性值是否在目标平台的属性值中
                else
                {
                    foreach (var supplierProp in supplierProps)
                    {
                        if (propModel.type == "multi_select")
                        {
                            var suppValues = supplierProp?.AttributeValue.Split(',');
                            if (suppValues != null)
                            {
                                var values = suppValues.Intersect(propValues.Select(x => x.name))?.ToList();
                                if (values != null)
                                {
                                    string valuename = string.Join(",", values);
                                    hasVal = new ProductFormatNew()
                                    {
                                        diy_type = propModel.diy_type,
                                        name = valuename,
                                        //value = val.value_id
                                    };
                                    result.Add(hasVal);
                                    continue;
                                }
                            }
                        }
                        else
                        {
                            foreach (var val in propValues)
                            {
                                if (val.name == supplierProp.AttributeValue)
                                {
                                    hasVal = new ProductFormatNew()
                                    {
                                        diy_type = propModel.diy_type,
                                        name = val.name,
                                        value = val.value_id
                                    };
                                    break;
                                }
                            }
                        }

                        if (hasVal == null || supplierProp.AttributeValue == "中国")
                        {
                            //模糊查找
                            foreach (var val in propValues)
                            {
                                if (val.value.StartsWith(supplierProp.AttributeValue))
                                {
                                    hasVal = new ProductFormatNew()
                                    {
                                        diy_type = propModel.diy_type,
                                        name = val.name,
                                        value = val.value_id
                                    };
                                    break;
                                }
                            }
                        }
                        if (hasVal != null && (result.Any(r => r.value == hasVal.value && r.diy_type == hasVal.diy_type) == false))
                        {
                            result.Add(hasVal);
                        }
                    }
                }

                //4.找到了属性值，返回相应的值Id,没找到取有 “其他”,“other”,“默认”等字样的值，没有，则取第一个值
                if (result.Any() == false && propModel.required == 1)
                {
                    //cateErrorInfos.Add(new CateErrorInfo()
                    //{
                    //    CateName = propModel.property_name,
                    //    ExceptionType = Enum.CateExceptionType.UseDefault
                    //});

                    CatePropOption defaultItem = null;
                    if (propModel.property_name.Contains("产地"))
                        defaultItem = propValues.FirstOrDefault(f => f.name.StartsWith("中国"));
                    else
                        defaultItem = propValues.FirstOrDefault(f => f.name != null && (f.name.StartsWith("其他") || f.name.StartsWith("other") || f.name.StartsWith("默认")));
                    if (defaultItem == null)
                        defaultItem = propValues.FirstOrDefault(f => f.name == "否");
                    //if (defaultItem == null)
                    //    defaultItem = propValues.FirstOrDefault();

                    if (defaultItem != null && (result.Any(r => r.value == defaultItem.value_id && r.diy_type == propModel.diy_type) == false))
                    {
                        result.Add(new ProductFormatNew()
                        {
                            diy_type = propModel.diy_type,
                            name = defaultItem.name,
                            value = defaultItem.value_id
                        });
                    }
                }
            }
            return result;
        }
        /// <summary>
        /// 是否需要转时间戳
        /// </summary>
        /// <param name="value"></param>
        /// <param name=""></param>
        /// <returns></returns>
        private string GetTouTiaoMeasureTemplatesByShowTime(string value, bool isShowToStamp)
        {
            var time = string.Empty;
            //转换用于编辑铺货的时间格式
            try
            {
                if (string.IsNullOrEmpty(value))
                    value = "";

                value = value.Replace("年", "-").Replace("月", "-").Replace("日", "");

                if (value.IndexOf("-") != -1)
                {
                    time = Convert.ToDateTime(value).ToString("yyyy-MM-dd");
                }
                else
                {
                    if (value.Length == 8)
                        time = DateTime.ParseExact(value, "yyyyMMdd", CultureInfo.InvariantCulture).ToString("yyyy-MM-dd");
                    else if (value.Length == 7)
                        time = DateTime.ParseExact(value, "yyyyMMd", CultureInfo.InvariantCulture).ToString("yyyy-MM-dd");
                }

                if (!string.IsNullOrEmpty(time) && isShowToStamp)
                    time = Convert.ToDateTime(time).ToStamp().ToString();//如不是时间格式，也一起转，报错不抛出
            }
            catch (Exception e)
            {
            }

            return time;
        }
        //抖音铺货，属性度量衡（编辑的）
        private ProductFormatNew GetMultiValueMeasureBymeasureInfoByEdit(DyCategoryPropInfoV2Model propModel, string measureInfoNameValue)
        {
            var multiValue = measureInfoNameValue.Split(',').ToList();
            multiValue.Remove("");//删除空的数组值

            var measureInfoName = string.Empty;
            var measureInfoValue = string.Empty;
            if (multiValue.Count == 2)
            {
                measureInfoName = multiValue[0];
                measureInfoValue = multiValue[1];
            }
            else
            {
                measureInfoName = multiValue[0];//不是两个属性，是属于错误的，默认赋值一个
                measureInfoValue = multiValue[0];//不是两个属性，是属于错误的，默认赋值一个
            }



            var model = new ProductFormatNew();
            var measure_info = new CatePropetyValueMeasureInfo();
            var measureTemplates = propModel.measure_templates.FirstOrDefault();
            measure_info.template_id = measureTemplates.template_id;//度量衡模板id
            measure_info.values = new List<SpecInfoValueMeasureInfoValue>();

            measureTemplates.value_modules.ForEach(m =>
            {
                var specInfoValueMeasureInfoValue = new SpecInfoValueMeasureInfoValue()
                {
                    module_id = m.module_id,
                    unit_id = 0
                };
                var valueName = string.Empty;
                if (m.units.Count > 0)
                {
                    specInfoValueMeasureInfoValue.unit_id = m.units.FirstOrDefault().unit_id;
                    specInfoValueMeasureInfoValue.unit_name = m.units.FirstOrDefault().unit_name;
                    specInfoValueMeasureInfoValue.value = measureInfoValue;

                    measure_info.value_name += specInfoValueMeasureInfoValue.value + specInfoValueMeasureInfoValue.unit_name;
                }
                else if (m.values.Count > 0)
                {
                    specInfoValueMeasureInfoValue.value = measureInfoName;

                    measure_info.value_name += specInfoValueMeasureInfoValue.value;
                }
                measure_info.values.Add(specInfoValueMeasureInfoValue);
            });


            model.diy_type = propModel.diy_type;
            model.value = 0;
            model.name = measure_info.value_name;
            model.measure_info = measure_info;

            return model;
        }
        //抖音铺货，属性度量衡。
        private ProductFormatNew GetMultiValueMeasureBymeasureInfo(DyCategoryPropInfoV2Model propModel, string infoUnitByValue, string infoValuesByValue)
        {
            var model = new ProductFormatNew();
            var measure_info = new CatePropetyValueMeasureInfo();
            var measureTemplates = propModel.measure_templates.FirstOrDefault();
            measure_info.template_id = measureTemplates.template_id;//度量衡模板id
            measure_info.values = new List<SpecInfoValueMeasureInfoValue>();

            measureTemplates.value_modules.ForEach(m =>
            {
                var specInfoValueMeasureInfoValue = new SpecInfoValueMeasureInfoValue()
                {
                    module_id = m.module_id,
                    unit_id = 0
                };
                var valueName = string.Empty;
                if (m.units.Count > 0)
                {
                    specInfoValueMeasureInfoValue.unit_id = m.units.FirstOrDefault().unit_id;
                    specInfoValueMeasureInfoValue.unit_name = m.units.FirstOrDefault().unit_name;
                    //specInfoValueMeasureInfoValue.value = string.IsNullOrEmpty(infoUnitByValue) ? "100" : infoUnitByValue;
                    specInfoValueMeasureInfoValue.value = infoUnitByValue;

                    measure_info.value_name += specInfoValueMeasureInfoValue.value + specInfoValueMeasureInfoValue.unit_name;
                }
                else if (m.values.Count > 0)
                {
                    specInfoValueMeasureInfoValue.value = string.IsNullOrEmpty(infoValuesByValue) ? m.values.FirstOrDefault()?.value_name : infoValuesByValue;

                    measure_info.value_name += specInfoValueMeasureInfoValue.value;
                }
                measure_info.values.Add(specInfoValueMeasureInfoValue);
            });


            model.diy_type = propModel.diy_type;
            model.value = 0;
            model.name = measure_info.value_name;
            model.measure_info = measure_info;

            return model;
        }
        private ListingProductEditResultModelByMeasureTemplates GetTouTiaoMeasureTemplates(DyCategoryPropInfoV2Model item, List<SupplierProductAttribute> supplierProductAttributes)
        {
            var model = new ListingProductEditResultModelByMeasureTemplates();

            if (item.type == "date_time_range") //时间区间
            {
                model.LeftValue = "";
                model.RightValue = "";

                var supplierProps = supplierProductAttributes.Where(f => f.AttributeName == item.property_name).ToList();
                if (supplierProps.Count == 0)
                    supplierProps = supplierProductAttributes.Where(f => item.property_name.IndexOf(f.AttributeName) != -1).ToList();


                var supplierProp = supplierProps.FirstOrDefault();
                if (supplierProp != null)
                {

                    var timeValueList = new List<string>();
                    var timeValue = supplierProp.AttributeValue ?? "";
                    if (timeValue.IndexOf(",") != -1) //编辑的时候，会有这种时间,两种时间
                    {
                        timeValueList = timeValue.Split(',').ToList();
                        timeValueList.Remove("");//删除空值的
                    }
                    if (timeValueList.Count > 1)
                    {
                        model.LeftValue = GetTouTiaoMeasureTemplatesByShowTime(timeValueList[0], false);
                        model.RightValue = GetTouTiaoMeasureTemplatesByShowTime(timeValueList[1], false);
                    }
                    else if (timeValueList.Count == 1)
                    {
                        model.LeftValue = GetTouTiaoMeasureTemplatesByShowTime(timeValueList[0], false);
                        model.RightValue = GetTouTiaoMeasureTemplatesByShowTime(timeValueList[0], false);
                    }


                }
            }
            if (item.type == "date_time")
            {
                //暂时不用measure_templates
            }
            if (item.type == "measure")//保质期，格式：1+天；"1"是通过货源提取（货源找不到，赋空）；"天"是规则上的下拉框
            {
                model.LeftValue = "";
                if (item.measure_templates?.Count > 0)
                {
                    var measureTemplate = item.measure_templates.FirstOrDefault();
                    var valueModules = measureTemplate.value_modules.FirstOrDefault();//非多值度量衡时，这个就是单个


                    var supplierProps = supplierProductAttributes.Where(f => f.AttributeName == item.property_name).ToList();
                    if (supplierProps.Count == 0)
                        supplierProps = supplierProductAttributes.Where(f => item.property_name.IndexOf(f.AttributeName) != -1).ToList();

                    //度量衡值measure_info
                    var measure_info = new CatePropetyValueMeasureInfo();
                    var measureTemplates = item.measure_templates.FirstOrDefault();
                    measure_info.template_id = measureTemplates.template_id;//度量衡模板id
                    measure_info.values = new List<SpecInfoValueMeasureInfoValue>();

                    var unitRes = valueModules.units.FirstOrDefault(u => supplierProps.FirstOrDefault(s => s.AttributeValue.IndexOf(u.unit_name) != -1) != null);//匹配单位，货源和度量衡规则
                    if (unitRes != null)
                    {
                        //提取单位和货源值
                        var supplierProp = supplierProps.FirstOrDefault(s => s.AttributeValue.IndexOf(unitRes?.unit_name ?? "") != -1) ?? supplierProps.FirstOrDefault();
                        string valueMumbers = Regex.Replace(supplierProp?.AttributeValue ?? "", "[^0-9]", "") ?? supplierProp?.AttributeValue;

                        var rightOptions = new List<ListingProductEditByMeasureTemplateOptions>();
                        valueModules.units.ForEach(v =>
                        {
                            rightOptions.Add(new ListingProductEditByMeasureTemplateOptions()
                            {
                                Value = v.unit_name,
                                IsSelect = unitRes.unit_name == v.unit_name
                            });
                        });

                        //值
                        model.LeftValue = !string.IsNullOrEmpty(valueMumbers) ? valueMumbers : supplierProp?.AttributeValue;
                        //单位
                        model.RightOptions = rightOptions;
                    }
                    else
                    {
                        var rightOptions = new List<ListingProductEditByMeasureTemplateOptions>();
                        valueModules.units.ForEach(v =>
                        {
                            rightOptions.Add(new ListingProductEditByMeasureTemplateOptions()
                            {
                                Value = v.unit_name,
                                IsSelect = false
                            });
                        });

                        //值
                        model.LeftValue = "";
                        //单位
                        model.RightOptions = rightOptions;
                    }

                }
            }
            if (item.type == "multi_value_measure")
            {
                model.MultiValueMeasureList = new List<ListingProductEditByMultiValueMeasure>();

                //匹配货源属性
                var supplierProps = supplierProductAttributes.Where(f => f.AttributeName == item.property_name).ToList();

                if (supplierProps.Count == 0)
                {
                    supplierProps = supplierProductAttributes.Where(f => item.property_name.IndexOf(f.AttributeName) != -1).ToList();
                    if (supplierProps.Count == 0) //依旧匹配不到，名称类似的情况下，转换
                    {
                        if (item.property_name == "面料材质")
                            supplierProps = supplierProductAttributes.Where(f => f.AttributeName == "主面料成分" || f.AttributeName == "主面料成分的含量").ToList();
                    }
                }

                var suProps = supplierProps.FirstOrDefault(s => s.AttributeName == "面料材质");
                if (suProps != null && suProps.AttributeValue.IndexOf("||") != -1)//铺货编辑的场景
                {
                    suProps.AttributeValue = suProps.AttributeValue.Replace("||", "|");
                    var attributeValueList = suProps.AttributeValue.Split('|').ToList();
                    attributeValueList.Remove("");//删除空的数组值


                    var index = 1;
                    attributeValueList.ForEach(a =>
                    {
                        var multiValue = a.Split(',').ToList();
                        multiValue.Remove("");//删除空的数组值
                        if (multiValue.Count == 2)
                        {
                            model.MultiValueMeasureList.Add(new ListingProductEditByMultiValueMeasure()
                            {
                                Index = index,
                                Name = multiValue[0],
                                Value = multiValue[1]
                            });

                        }
                        else if (multiValue.Count == 1)
                        {
                            model.MultiValueMeasureList.Add(new ListingProductEditByMultiValueMeasure()
                            {
                                Index = index,
                                Name = multiValue[0],
                                Value = multiValue[0]
                            });
                        }
                        index++;
                    });

                }
                else if (item.property_name == "面料材质")
                {
                    var measureTemplates = item.measure_templates.FirstOrDefault();
                    var materialList = measureTemplates.value_modules.FirstOrDefault(f => f.values.Count > 0).values;//面料列表

                    materialList = materialList.Where(w => supplierProps.FirstOrDefault(s => s.AttributeValue == w.value_name) != null).ToList();
                    double percentNumber = 100;
                    var index = 1;
                    materialList.ForEach(f =>
                    {

                        var percentSupplierProps = supplierProps.FirstOrDefault(sup => sup.AttributeName == "主面料成分的含量");
                        if (percentSupplierProps != null)
                        {
                            double percentSupplierPropsNumber = 0;
                            try
                            {
                                var attributeValue = percentSupplierProps.AttributeValue.Replace("%", "");
                                percentSupplierPropsNumber = Math.Round(Convert.ToDouble(attributeValue), 2);


                                percentNumber = Math.Round(percentNumber - percentSupplierPropsNumber, 2);
                            }
                            catch (Exception ee) { }

                            //百分比是有效的
                            if (percentSupplierPropsNumber > 0)
                            {
                                var measureModel = GetMultiValueMeasureModelList(item, percentSupplierPropsNumber.ToString(), f.value_name);
                                measureModel.Index = index;
                                index++;
                                model.MultiValueMeasureList.Add(measureModel);
                            }
                            else
                            {
                                var measureModel = GetMultiValueMeasureModelList(item, "", f.value_name);
                                measureModel.Index = index;
                                index++;
                                model.MultiValueMeasureList.Add(measureModel);
                                percentNumber = 0;
                            }

                        }
                        else
                        {
                            var measureModel = GetMultiValueMeasureModelList(item, "", f.value_name);
                            measureModel.Index = index;
                            index++;
                            model.MultiValueMeasureList.Add(measureModel);
                            percentNumber = 0;
                        }

                    });

                    //需要达到100
                    if (percentNumber < 100 && percentNumber > 0)
                    {
                        var measureModel = GetMultiValueMeasureModelList(item, Convert.ToString(percentNumber), "其他");
                        measureModel.Index = index;
                        index++;
                        model.MultiValueMeasureList.Add(measureModel);
                    }
                }



                //没有匹配时，默认值，用于前端填写
                if (model.MultiValueMeasureList.Count == 0)
                {
                    model.MultiValueMeasureList.Add(new ListingProductEditByMultiValueMeasure()
                    {
                        Index = 1,
                        Name = "",
                        //Value = "100"
                        Value = ""
                    });
                }

            }
            return model;
        }
        private ListingProductEditByMultiValueMeasure GetMultiValueMeasureModelList(DyCategoryPropInfoV2Model propModel, string infoUnitByValue, string infoValuesByValue)
        {
            var model = new ListingProductEditByMultiValueMeasure();
            var measureTemplates = propModel.measure_templates.FirstOrDefault();

            measureTemplates.value_modules.ForEach(m =>
            {
                if (m.units.Count > 0)
                {
                    //model.Value = string.IsNullOrEmpty(infoUnitByValue) ? "100" : infoUnitByValue;
                    model.Value = string.IsNullOrEmpty(infoUnitByValue) ? "" : infoUnitByValue;
                }
                else if (m.values.Count > 0)
                {
                    model.Name = string.IsNullOrEmpty(infoValuesByValue) ? m.values.FirstOrDefault()?.value_name : infoValuesByValue;
                }
            });

            return model;
        }
    }
}
