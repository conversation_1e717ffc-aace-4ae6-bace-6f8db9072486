using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Services.PlatformService.OtherPlatforms;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace DianGuanJiaApp.Services.Services
{
    public interface IAppService
    {
        AppPlatformModel getAppModel(string appkey);
    }

    public static class AppServiceFactory
    {
        public static IAppService GetAppService(string platform)
        {
            IAppService result = null;
            switch (platform.ToLower())
            {
                case "1688":
                case "alibaba":
                    result = new AlibabaAppService();
                    break;
                case "pinduoduo":
                case "pdd":
                    result = new PinduoduoAppService();
                    break;
                case "taobao":
                case "tb":
                    result = new TaobaoAppService();
                    break;
                case "xiaodian":
                case "xd":
                    result = new XiaoDianAppService();
                    break;
                case "mogujie":
                case "mg":
                case "meilishuo":
                case "mls":
                    result = new MoGuJieAppService();
                    break;
                case "youzan":
                case "yz":
                    result = new YouZanAppService();
                    break;
                case "weidian":
                case "wd":
                    result = new WeiDianAppService();
                    break;
                case "weimeng":
                case "wm":
                    result = new WeiMengAppService();
                    break;
                case "vipshop":
                case "vshop":
                    result = new VipShopAppService();
                    break;
                case "toutiao":
                case "tt":
                case "zhidian":
                case "zd":
                case "douyinxiaodian":
                case "dyxd":
                case "toutiaoxiaodian":
                case "ttxd":
                case "luban":
                case "lb":
                case "douyinmicroapp":
                case "toutiaosaleshop":
                    result = new DouYinAppService();
                    break;
                case "suning":
                case "sn":
                    result = new SuningAppService();
                    break;
                case "jingdong":
                case "jd":
                    result = new JingdongAppService();
                    break;
                case "jingdongpurchase":
                    result = new JingdongPurchaseAppService();
                    break;
                case "mengtui":
                case "mt":
                    result = new MengTuiAppService();
                    break;
                case "yunji":
                case "yj":
                    result = new YunJiAppService();
                    break;
                case "kuaishou":
                case "ks":
                    result = new KuaiShouAppService();
                    break;
                case "beibei":
                case "bb":
                    result = new BeiBeiAppService();
                    break;
                case "open":
                case "openv1":
                case "openv2":
                    result = new OpenAppService();
                    break;
                case "Offline":
                case "offline":
                case "ol":
                    result = new OfflineAppService();
                    break;
                case "alibabac2m":
                    result = new AlibabaC2MAppService();
                    break;
                case "xiaohongshu":
                case "xhs":
                    result = new XiaoHongShuAppService();
                    break;
                case "wxxiaoshangdian":
                case "wxxsd":
                    result = new WxXiaoShangDianAppService();
                    break;
                case "wxvideo":
                    result = new WxVideoAppService();
                    break;
                case "mokuai":
                case "mk":
                    result = new MoKuaiAppService();
                    break;
                case "duxiaodian":
                case "dxd":
                    result = new DuXiaoDianAppService();
                    break;
                case "tuanhaohuo":
                case "thh":
                    result = new TuanHaoHuoAppService();
                    break;
                case "kuaituantuan":
                case "pddktt":
                    result = new KuaiTuanTuanAppService();
                    break;
                case "virtual":
                    result = new OfflineVirtualAppService();
                    break;                    
                case "tiktok":
                    result = new TikTokAppService();
                    break;
                case "other_heliang":
                    result = new OtherHeliangAppService();
                    break;
                case "taobaomaicai":
                    result = new TaobaoMaiCaiAppService();
                    break;
                case "ownshop":
                    result = new OfflineVirtualAppService();
                    break;
                case "other_juhaomai":
                    result = new OtherJuHaoMaiAppService();
                    break;
                case "bilibili":
                    result = new BiliBiliAppService();
                    break;
                case "other_haoyouduo":
                    result = new OtherHaoYouDuoAppService();
                    break;
                default:
                    result = null; break;
            }
            return result;
        }
    }

    public class DouYinAppService : IAppService
    {
        public AppPlatformModel getAppModel(string appkey)
        {
            AppPlatformModel result = null;
            if (appkey == CustomerConfig.TouTiaoFxNewAppKey)
            {
                result = new AppPlatformModel
                {
                    Name = "店管家_分销代发",
                    Appkey = appkey,
                    Platform = "TouTiao",
                    Img = "fxapp-platform.png",
                    AuthUrl = "http://auth.dgjapp.com/auth/douyinfxnew?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    MarketUrl = "https://fuwu.jinritemai.com/detail?service_id=24069&from=isv.detail",
                };
            }
            else if (appkey == CustomerConfig.TouTiaoFxListingAppKey)
            {
                result = new AppPlatformModel
                {
                    Name = "店管家分销铺货",
                    Appkey = appkey,
                    Platform = "TouTiao",
                    Img = "fxgoodapp-platform.png",
                    AuthUrl = "http://auth.dgjapp.com/auth/douyinfxlisting?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    MarketUrl = $"https://fuwu.jinritemai.com/detail?service_id={CustomerConfig.TouTiaoFxListingServiceId}&from=dgj",
                };
            }
            else if (appkey == CustomerConfig.TouTiaoFxAppKey)
            {
                result = new AppPlatformModel
                {
                    Name = "店管家铺货助手",
                    Appkey = appkey,
                    Platform = "TouTiao",
                    Img = "goodapp-platform.png",
                    AuthUrl = "http://auth.dgjapp.com/auth/douyinfx?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    MarketUrl = "https://fuwu.jinritemai.com/detail?service_id=19552&from=dgj",
                };
            }
            else if (appkey == CustomerConfig.FxTouTiaoSaleShopAppKey)
            {
                result = new AppPlatformModel
                {
                    Name = "店管家_分销代发",
                    Appkey = appkey,
                    Platform = "TouTiaoSaleShop",
                    Img = "printapp-platform.png",
                    AuthUrl = "http://auth.dgjapp.com/auth/fxdouyinsaleshop?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    // TODO 待应用上线后补充
                    MarketUrl = $"https://fuwu.jinritemai.com/detail?service_id={CustomerConfig.FxTouTiaoSaleShopAppServiceId}&from=isv.detail",
                };
            }
            else
            {
                result = new AppPlatformModel
                {
                    Name = "店管家打单",
                    Appkey = CustomerConfig.TouTiaoAppKey,
                    Platform = "TouTiao",
                    Img = "printapp-platform.png",
                    AuthUrl = "http://auth.dgjapp.com/auth/douyin?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    MarketUrl = "https://fuwu.jinritemai.com/detail?service_id=7&from=dgj",
                };
            }
            return result;
        }

    }

    public class YouZanAppService : IAppService
    {
        public AppPlatformModel getAppModel(string appkey)
        {
            string token = HttpContext.Current?.Request?.QueryString["token"];
            AppPlatformModel result = null;
            if (appkey == CustomerConfig.YouZanFxAppKey)
            {
                result = new AppPlatformModel
                {
                    Name = "店管家_分销代发",
                    Appkey = CustomerConfig.YouZanFxAppKey,
                    Platform = "YouZan",
                    Img = "fxapp-platform.png",
                    AuthUrl = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/fxauth/youzan?state=" + WebHelper.UrlEncode(new { SuccToUrl = "http://auth.dgjapp.com/auth/authsuccess", AuthType = "3", Rp = token }.ToJson()),
                    MarketUrl = $"https://yingyong.youzan.com/cloud-app-detail/10001603",
                };
            }
            else
            {
                result = new AppPlatformModel
                {
                    Name = "店管家打单",
                    Appkey = CustomerConfig.YouZanAppKey,
                    Platform = "YouZan",
                    Img = "printapp-platform.png",
                    AuthUrl = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/youzan?state=" + WebHelper.UrlEncode(new { SuccToUrl = "http://auth.dgjapp.com/auth/authsuccess", AuthType = "3", Rp = token }.ToJson()),
                    MarketUrl = $"https://yingyong.youzan.com/cloud-app-detail/43116",
                };
            }
            return result;

        }
    }

    public class MoGuJieAppService : IAppService
    {
        public AppPlatformModel getAppModel(string appkey)
        {
            AppPlatformModel result = null;
            if (appkey == CustomerConfig.MoGuFxAppKey)
            {
                result = new AppPlatformModel
                {
                    Name = "店管家_分销代发",
                    Appkey = CustomerConfig.MoGuFxAppKey,
                    Platform = "MoGuJie",
                    Img = "fxapp-platform.png",
                    AuthUrl = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/fxauth/mogujie" + "?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                };
            }
            else
            {
                result = new AppPlatformModel
                {
                    Name = "店管家打单",
                    Appkey = CustomerConfig.MoGuAppKey,
                    Platform = "MoGuJie",
                    Img = "printapp-platform.png",
                    AuthUrl = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/mogujie" + "?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                };
            }
            return result;
        }
    }

    public class MeiLiShuoAppService : IAppService
    {
        public AppPlatformModel getAppModel(string appkey)
        {
            AppPlatformModel result = null;
            if (appkey == CustomerConfig.MoGuFxAppKey)
            {
                result = new AppPlatformModel
                {
                    Name = "店管家分销",
                    Appkey = CustomerConfig.MoGuFxAppKey,
                    Platform = "MeiLiShuo",
                    Img = "fxapp-platform.png",
                    AuthUrl = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/fxauth/meilishuo" + "?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                };
            }
            else
            {
                result = new AppPlatformModel
                {
                    Name = "店管家打单",
                    Appkey = CustomerConfig.MoGuAppKey,
                    Platform = "MeiLiShuo",
                    Img = "printapp-platform.png",
                    AuthUrl = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/meilishuo" + "?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                };
            }
            return result;
        }
    }

    public class SuningAppService : IAppService
    {
        public AppPlatformModel getAppModel(string appkey)
        {
            AppPlatformModel result = null;
            if (appkey == CustomerConfig.SuningFxAppKey)
            {
                result = new AppPlatformModel()
                {
                    Name = "店管家_分销代发",
                    Appkey = CustomerConfig.SuningFxAppKey,
                    Platform = "Suning",
                    Img = "fxapp-platform.png",
                    AuthUrl = "http://auth.dgjapp.com/fxauth/suning?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    MarketUrl = System.Configuration.ConfigurationManager.AppSettings.Get("AppPayUrl:Suning") ?? "http://fuwu.suning.com/detail/10003725.html"
                };
            }
            else
            {
                result = new AppPlatformModel()
                {
                    Name = "店管家打单",
                    Appkey = CustomerConfig.SuningAppKey,
                    Platform = "Suning",
                    Img = "printapp-platform.png",
                    AuthUrl = "http://auth.dgjapp.com/auth/suning?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    MarketUrl = System.Configuration.ConfigurationManager.AppSettings.Get("AppPayUrl:Suning") ?? "http://fuwu.suning.com/detail/10003725.html"
                };
            }
            return result;
        }
    }

    public class VipShopAppService : IAppService
    {
        public AppPlatformModel getAppModel(string appkey)
        {
            AppPlatformModel result = null;
            if (appkey == CustomerConfig.VipShopFxAppKey)
            {
                result = new AppPlatformModel()
                {
                    Name = "店管家_分销代发",
                    Appkey = CustomerConfig.VipShopFxAppKey,
                    Platform = "VipShop",
                    Img = "fxapp-platform.png",
                    AuthUrl = "http://auth.dgjapp.com/fxauth/vipshop?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    MarketUrl = ""
                };
            }
            else
            {
                result = new AppPlatformModel()
                {
                    Name = "店管家打单",
                    Appkey = CustomerConfig.VipShopAppKey,
                    Platform = "VipShop",
                    Img = "printapp-platform.png",
                    AuthUrl = "http://auth.dgjapp.com/auth/vipshop?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    MarketUrl = ""
                };
            }
            return result;
        }
    }

    public class JingdongAppService : IAppService
    {
        public AppPlatformModel getAppModel(string appkey)
        {
            AppPlatformModel result = null;
            if (appkey == CustomerConfig.JingDongFxAppKey)
            {
                string token = HttpContext.Current?.Request?.QueryString["token"];
                result = new AppPlatformModel()
                {
                    Name = "店管家_分销代发",
                    Appkey = CustomerConfig.JingDongFxAppKey,
                    Platform = "JingDong",
                    Img = "fxapp-platform.png",
                    AuthUrl = "http://auth.dgjapp.com/fxauth/jingdong?state=" + WebHelper.UrlEncode(new { AType = "3", SuccToUrl = "http://auth.dgjapp.com/auth/authsuccess", CallUrl = "http://jd3.dgjapp.com"}.ToJson()),
                    MarketUrl = $"https://fw.jd.com/main/detail/FW_GOODS-1825002?p=fw&t=pcsearch"
                };
            }
            else
            {
                result = new AppPlatformModel()
                {
                    Name = "店管家打单",
                    Appkey = CustomerConfig.JingDongAppKey,
                    Platform = "JingDong",
                    Img = "printapp-platform.png",
                    AuthUrl = "http://auth.dgjapp.com/auth/jingdong?state=" + WebHelper.UrlEncode(new { AType = "3", SuccToUrl = "http://auth.dgjapp.com/auth/authsuccess", CallUrl = "http://jd3.dgjapp.com" }.ToJson()),
                    MarketUrl = $"https://fw.jd.com/975802.html?itemCode=FW_GOODS-975802-1"
                };
            }
            return result;
        }
    }

    public class JingdongPurchaseAppService : IAppService
    {
        public AppPlatformModel getAppModel(string appkey)
        {
            string token = HttpContext.Current?.Request?.QueryString["token"];
            AppPlatformModel result = new AppPlatformModel()
            {
                Name = "店管家_分销代发",
                Appkey = CustomerConfig.JingDongFxAppKey,
                Platform = "JingdongPurchase",
                Img = "fxapp-platform.png",
                AuthUrl = "http://auth.dgjapp.com/fxauth/jingdong?state=" + WebHelper.UrlEncode(new { AType = "4", SuccToUrl = "http://auth.dgjapp.com/auth/authsuccess", CallUrl = "http://jd3.dgjapp.com" }.ToJson()),
                MarketUrl = $"https://fw.jd.com/main/detail/FW_GOODS-1825002?p=fw&t=pcsearch"
            };
            return result;
        }
    }
    public class WeiDianAppService : IAppService
    {
        public AppPlatformModel getAppModel(string appkey)
        {
            AppPlatformModel result = null;
            if (appkey == CustomerConfig.WeiDianFxAppKey)
            {
                result = new AppPlatformModel
                {
                    Name = "店管家_分销代发",
                    Appkey = CustomerConfig.WeiDianFxAppKey,
                    Platform = "WeiDian",
                    Img = "fxapp-platform.png",
                    AuthUrl = "http://auth.dgjapp.com/fxauth/weidian?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    MarketUrl = "",
                };
            }
            else
            {
                result = new AppPlatformModel()
                {
                    Name = "店管家打单",
                    Appkey = CustomerConfig.WeiDianAppKey,
                    Platform = "WeiDian",
                    Img = "printapp-platform.png",
                    AuthUrl = "http://auth.dgjapp.com/auth/weidian?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    MarketUrl = "",
                };
            }
            return result;
        }
    }

    public class AlibabaAppService : IAppService
    {
        public AppPlatformModel getAppModel(string appkey)
        {
            AppPlatformModel result = null;
            if (appkey == CustomerConfig.AlibabaQingAppKey)
            {
                result = new AppPlatformModel
                {
                    Name = "店管家_分销代发",
                    Img = "fxapp-platform.png",
                    Appkey = CustomerConfig.AlibabaQingAppKey,
                    Platform = "Alibaba",
                    AuthUrl = "https://auth.dgjapp.com/fxauth/alibabaqyy?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    MarketUrl = $"https://pc.1688.com/product/detail.htm?productCode=N5BTogeN0ZNR8LRWgJ0BE1x6qiUhwmzHLIG2l80SXTQ%3D&productType=GROUP",
                };
            }
            else
            {
                result = new AppPlatformModel
                {
                    Name = "店管家打单",
                    Img = "printapp-platform.png",
                    Appkey = CustomerConfig.AlibabaAppKey,
                    Platform = "Alibaba",
                    AuthUrl = "https://pr1688.dgjapp.com?CallUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/entrance?authtype=3") + "&SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess"),
                    MarketUrl = $"http://pc.1688.com/product/detail.htm?productCode=Tz%2BIZt9qCGKsMpNFCCCY9%2BmqRnw6h1ZBD3N%2Fli2CCyg%3D&productType=GROUP&tracelog=app_map_dgj",
                };
            }
            return result;
        }
    }

    public class PinduoduoAppService : IAppService
    {
        public AppPlatformModel getAppModel(string appkey)
        {
            AppPlatformModel result = null;
            if (appkey == CustomerConfig.PddFxAppKey)
            {
                result = new AppPlatformModel()
                {
                    Name = "店管家_分销代发",
                    Appkey = CustomerConfig.PddFxAppKey,
                    Platform = "Pinduoduo",
                    Img = "fxapp-platform.png",
                    AuthUrl = "http://testauth.dgjapp.com/fxauth/pdd?AuthType=3&SuccToUrl=" + WebHelper.UrlEncode("http://pdd10.dgjapp.com/auth/authsuccess"),
                    MarketUrl = "https://mms.pinduoduo.com/service-market/service-detail?detailId=52528"
                };
            }
            else
            {
                result = new AppPlatformModel()
                {
                    Name = "店管家打单",
                    Appkey = appkey,
                    Platform = "Pinduoduo",
                    Img = "fxapp-platform.png",
                    AuthUrl = $"{CustomerConfig.PinduoduoOldSystemLink}/default.aspx?AuthType=3&SuccToUrl=" + WebHelper.UrlEncode("http://pdd10.dgjapp.com/auth/authsuccess"),
                    MarketUrl = "https://mms.pinduoduo.com/service-market/service-detail?detailId=172"
                };
            }
            return result;
        }
    }

    public class TaobaoAppService : IAppService
    {
        public AppPlatformModel getAppModel(string appkey)
        {
            return new AppPlatformModel()
            {
                Name = "店管家打单",
                Appkey = CustomerConfig.TaobaAppKey,
                Platform = "Taobao",
                Img = "printapp-platform.png",
                AuthUrl = "https://taobao2.dgjapp.com?CallUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/entrance?authtype=3") + "&SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess"),
                MarketUrl = System.Configuration.ConfigurationManager.AppSettings.Get("AppPayUrl:Taobao") ?? "https://fuwu.taobao.com/ser/detail.htm?spm=a1z13.8286890.0.0.10715acaQKIHIe&service_code=FW_GOODS-1000059019&tracelog=itcategory"
            };
        }
    }

    public class XiaoDianAppService : IAppService
    {
        public AppPlatformModel getAppModel(string appkey)
        {
            return new AppPlatformModel
            {
                Name = "店管家打单",
                Platform = "XiaoDian",
                Img = "printapp-platform.png",
                Appkey = CustomerConfig.XiaoDianAppKey,
                AuthUrl = "http://auth.dgjapp.com/auth/XiaoDian?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                MarketUrl = "",
            };
        }
    }
    public class WeiMengAppService : IAppService
    {
        public AppPlatformModel getAppModel(string appkey)
        {
            if (appkey == CustomerConfig.WeiMengFxAppKey)
            {
                return new AppPlatformModel()
                {
                    Platform = "WeiMeng",
                    Appkey = CustomerConfig.WeiMengFxAppKey,
                    Name = "店管家分销",
                    Img = "fxapp-platform.png",
                    AuthUrl = "http://auth.dgjapp.com/auth/weimengfx?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    MarketUrl = System.Configuration.ConfigurationManager.AppSettings.Get("AppPayUrl:Weimeng") ?? "https://fuwu.weimob.com/product_906.html"
                };
            }
            else
            {
                return new AppPlatformModel()
                {
                    Platform = "WeiMeng",
                    Appkey = CustomerConfig.WeiMengAppKey,
                    Name = "店管家打单",
                    Img = "printapp-platform.png",
                    AuthUrl = "http://auth.dgjapp.com/auth/weimeng?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    MarketUrl = System.Configuration.ConfigurationManager.AppSettings.Get("AppPayUrl:Weimeng") ?? "https://fuwu.weimob.com/product_906.html"
                };
            }

        }
    }

    public class MengTuiAppService : IAppService
    {
        public AppPlatformModel getAppModel(string appkey)
        {
            return new AppPlatformModel
            {
                Name = "店管家分销",
                Platform = "MengTui",
                Img = "fxapp-platform.png",
                Appkey = CustomerConfig.MengTuiAppKey,
                AuthUrl = "http://auth.dgjapp.com/auth/mengtui?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                MarketUrl = "",
            };
        }
    }

    public class YunJiAppService : IAppService
    {
        public AppPlatformModel getAppModel(string appkey)
        {
            return new AppPlatformModel
            {
                Platform = "YunJi",
                Name = "店管家打单",
                Img = "printapp-platform.png",
                Appkey = CustomerConfig.YunJiAppISVID,
                AuthUrl = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/yunji",
                MarketUrl = "",
            };
        }
    }

    public class KuaiShouAppService : IAppService
    {
        public AppPlatformModel getAppModel(string appkey)
        {
            if (appkey == CustomerConfig.KuaiShouNewFxAppKey)
            {
                return new AppPlatformModel()
                {
                    Platform = "KuaiShou",
                    Name = "店管家_分销代发(新)",
                    Img = "toutiao-platform.png",
                    ImgXy = "-140px 0",
                    Appkey = CustomerConfig.KuaiShouNewFxAppKey,
                    AuthUrl = "https://auth.dgjapp.com/fxauth/kuaishouv2?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    MarketUrl = $"https://fuwu.kwaixiaodian.com/detail?id=21319903733583"
                };
            }
            else if (appkey == CustomerConfig.KuaiShouFxFastAppKey)
            {
                return new AppPlatformModel()
                {
                    Platform = "KuaiShou",
                    Name = "店管家_智速打单",
                    Img = "toutiao-platform.png",
                    ImgXy = "-140px 0",
                    Appkey = CustomerConfig.KuaiShouFxFastAppKey,
                    AuthUrl = "https://auth.dgjapp.com/fxauth/kuaishouv2?SuccToUrl=" + WebHelper.UrlEncode("http://test7.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    MarketUrl = $"https://fuwu.kwaixiaodian.com/detail?id=21319903733583"
                };
            }
            else
            {
                return new AppPlatformModel()
                {
                    Platform = "KuaiShou",
                    Name = "店管家_分销管理(旧)",
                    Img = "toutiao-platform.png",
                    ImgXy = "-420px 0",
                    Appkey = CustomerConfig.KuaiShouFxAppKey,
                    AuthUrl = "https://auth.dgjapp.com/fxauth/kuaishou?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                    MarketUrl = $"https://fuwu.kwaixiaodian.com/detail?id=1640236816583"
                };
            }
        }
    }

    public class BeiBeiAppService : IAppService
    {
        public AppPlatformModel getAppModel(string appkey)
        {
            return new AppPlatformModel
            {
                Platform = "BeiBei",
                Name = "店管家打单",
                Appkey = CustomerConfig.BeiBeiAppKey
            };
        }
    }

    public class OpenAppService : IAppService
    {
        public AppPlatformModel getAppModel(string appkey)
        {
            return new AppPlatformModel
            {
                Platform = "Open",
                Name = "店管家打单",
                Appkey = appkey
            };
        }
    }

    public class OfflineAppService : IAppService
    {
        public AppPlatformModel getAppModel(string appkey)
        {
            return new AppPlatformModel
            {
                Platform = "Open",
                Name = "店管家打单",
                Appkey = appkey
            };
        }
    }

    public class AlibabaC2MAppService : IAppService
    {
        public AppPlatformModel getAppModel(string appkey)
        {
            return new AppPlatformModel()
            {
                Platform = "AlibabaC2M",
                Name = "店管家打单",
                Img = "printapp-platform.png",
                Appkey = CustomerConfig.AlibabaC2MAppKey,
                AuthUrl = "http://auth.dgjapp.com/auth/alibabaC2M?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
            };
        }
    }


    public class XiaoHongShuAppService : IAppService
    {
        public AppPlatformModel getAppModel(string appkey)
        {
            if (appkey == CustomerConfig.XiaoHongShuFXAppKey)
            {
                return new AppPlatformModel()
                {
                    Platform = "XiaoHongShu",
                    Name = "店管家_分销代发",
                    Img = "fxapp-platform.png",
                    Appkey = CustomerConfig.XiaoHongShuFXAppKey,
                    AuthUrl = "http://auth.dgjapp.com/auth/xiaohongshuv2fx?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                };
            }
            else
            {
                return new AppPlatformModel()
                {
                    Platform = "XiaoHongShu",
                    Name = "店管家打单",
                    Img = "printapp-platform.png",
                    Appkey = CustomerConfig.XiaoHongShuAppKey,
                    AuthUrl = "http://auth.dgjapp.com/auth/xiaohongshuv2?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                };
            }

        }
    }

    public class WxXiaoShangDianAppService : IAppService
    {
        public AppPlatformModel getAppModel(string appkey)
        {
            return new AppPlatformModel()
            {
                Name = "店管家打单",
                Platform = "WxXiaoShangDian",
                Img = "fxapp-platform.png",
                Appkey = CustomerConfig.WxXiaoShangDianAppId,
                //AuthUrl = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/fxauth/wxxiaoshangdian",
                MarketUrl = "https://shop.weixin.qq.com/"
            };
        }
    }

    public class WxVideoAppService : IAppService
    {
        public AppPlatformModel getAppModel(string appkey)
        {
            if (appkey == CustomerConfig.Fx_WxComponentNewAppId)
            {
                return new AppPlatformModel()
                {
                    Name = "中恒_分销代发",
                    Platform = "WxVideo",
                    Img = "fxapp-platform.png",
                    Appkey = CustomerConfig.Fx_WxComponentNewAppId,
                    //AuthUrl = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/fxauth/wxvideo",
                    MarketUrl = "https://channels.weixin.qq.com/shop"
                };
            }

            if (appkey == CustomerConfig.Fx_WxShopNewAppId)
            {
                return new AppPlatformModel
                {
                    Name = "店管家_分销代发",
                    Platform = "WxVideo",
                    Img = "fxapp-platform.png",
                    Appkey = CustomerConfig.Fx_WxShopNewAppId,
                    // MarketUrl = "https://channels.weixin.qq.com/shop"
                    // TODO 临时跳转到百度，待正式应用上线有续费链接修改
                    MarketUrl = "https://www.baidu.com"
                }; 
            }

            return new AppPlatformModel()
            {
                Name = "店管家_分销代发",
                Platform = "WxVideo",
                Img = "fxapp-platform.png",
                Appkey = CustomerConfig.Fx_WxXiaoShangDianAppId,
                //AuthUrl = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/fxauth/wxvideo",
                MarketUrl = "https://channels.weixin.qq.com/shop"
            };
        }
    }

    public class MoKuaiAppService : IAppService
    {
        public AppPlatformModel getAppModel(string appkey)
        {
            if (appkey == CustomerConfig.MoKuaiFxAppKey)
            {
                return new AppPlatformModel()
                {
                    Appkey = CustomerConfig.MoKuaiFxAppKey,
                    Name = "店管家分销",
                    Platform = "MoKuai",
                    Img = "printapp-platform.png",
                    AuthUrl = "http://auth.dgjapp.com/fxauth/mokuai?SuccToUrl=" + WebHelper.UrlEncode("http://test7.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                };
            }
            else
            {
                return new AppPlatformModel()
                {
                    Appkey = CustomerConfig.MoKuaiAppKey,
                    Name = "店管家打单",
                    Platform = "MoKuai",
                    Img = "printapp-platform.png",
                    AuthUrl = "http://auth.dgjapp.com/auth/mokuai?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                };
            }
        }
    }

    public class TuanHaoHuoAppService : IAppService
    {
        public AppPlatformModel getAppModel(string appkey)
        {
            return new AppPlatformModel()
            {
                Platform = "TuanHaoHuo",
                Name = "店管家打单",
                Appkey = CustomerConfig.TuanHaoHuoAppKey,
                Img = "printapp-platform.png",
                AuthUrl = "http://auth.dgjapp.com/auth/tuanhaohuo?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
            };
        }
    }

    public class KuaiTuanTuanAppService : IAppService
    {
        public AppPlatformModel getAppModel(string appkey)
        {
            return new AppPlatformModel()
            {
                Platform = "KuaiTuanTuan",
                Name = "店管家打单",
                Appkey = CustomerConfig.KuaiTuanTuanAppKey,
                Img = "printapp-platform.png",
                AuthUrl = "http://auth.dgjapp.com/auth/pddktt?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
            };
        }
    }

    public class OtherHeliangAppService: IAppService
    {
        public AppPlatformModel getAppModel(string appkey)
        {
            return new AppPlatformModel()
            {
                Platform = "Other_Heliang",
                Name ="-",
            };
        }
    }

    public class OtherJuHaoMaiAppService : IAppService
    {
        public AppPlatformModel getAppModel(string appkey)
        {
            return new AppPlatformModel()
            {
                Platform = "Other_JuHaoMai",
                Name = "-",
            };
        }
    }

    public class OtherHaoYouDuoAppService : IAppService
    {
        public AppPlatformModel getAppModel(string appkey)
        {
            return new AppPlatformModel()
            {
                Platform = "Other_HaoYouDuo",
                Name = "-",
            };
        }
    }

    public class BiliBiliAppService : IAppService
    {
        public AppPlatformModel getAppModel(string appkey)
        {
            return new AppPlatformModel()
            {
                Appkey = CustomerConfig.BiliBiliFxAppKey,
                Name = "店管家_分销代发",
                Platform = "BiliBili",
                Img = "printapp-platform.png",
                AuthUrl = "http://auth.dgjapp.com/fxauth/bilibili?state=" + WebHelper.UrlEncode(new { AType = "3", SuccToUrl = "http://auth.dgjapp.com/auth/authsuccess" }.ToJson()),
            };
        }
    }
    public class OfflineVirtualAppService : IAppService
    {
        public AppPlatformModel getAppModel(string appkey)
        {
            return null;
        }
    }

    public class DuXiaoDianAppService : IAppService
    {
        public AppPlatformModel getAppModel(string appkey)
        {
            AppPlatformModel result = null;
            if (appkey == CustomerConfig.DuXiaoDianV2FxAppKey)
            {
                result = new AppPlatformModel
                {
                    Name = "店管家_分销代发",
                    Appkey = CustomerConfig.DuXiaoDianV2FxAppKey,
                    Platform = "DuXiaoDian",
                    Img = "fxapp-platform.png",
                    AuthUrl = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/fxauth/duxiaodian" + "?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                };
            }
            else
            {
                result = new AppPlatformModel
                {
                    Name = "店管家打单",
                    Appkey = CustomerConfig.DuXiaoDianAppKey,
                    Platform = "DuXiaoDian",
                    Img = "printapp-platform.png",
                    AuthUrl = $"{CustomerConfig.AuthCallbackUrl?.TrimEnd('/')}/auth/duxiaodian" + "?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess") + "&AuthType=3",
                };
            }
            return result;
        }
    }

    /// <summary>
    /// TikTok应用信息
    /// </summary>
    public class TikTokAppService : IAppService
    {
        public AppPlatformModel getAppModel(string appkey)
        {
            return new AppPlatformModel()
            {
                Name = "店管家TikTok",
                Appkey = CustomerConfig.TikTokAppKey,
                Platform = PlatformType.TikTok.ToString(),
                Img = "printapp-platform.png",
                AuthUrl = ((CustomerConfig.IsDebug && CustomerConfig.IsLocalDbDebug) ? "http://396ec02725.zicp.vip" : "http://auth.dgjapp.com") + "/fxauth/crossborder?SuccToUrl=" + WebHelper.UrlEncode("http://auth.dgjapp.com/auth/authsuccess"),
            };
        }
    }
    public class TaobaoMaiCaiAppService : IAppService
    {
        public AppPlatformModel getAppModel(string appkey)
        {
            string token = HttpContext.Current?.Request?.QueryString["token"];
            return new AppPlatformModel
            {
                Name = "店管家_淘菜菜",
                Appkey = CustomerConfig.TaobaoMaiCaiAppKey,
                Platform = "TaobaoMaiCai",
                Img = "fxapp-platform.png",
                AuthUrl = "http://auth.dgjapp.com/auth/taobaomaicai?state=" + Convert.ToBase64String(Encoding.Default.GetBytes(new { SuccToUrl = "http://auth.dgjapp.com/auth/authsuccess", AuthType = "3", Rp = token }.ToJson()))
            };
        }
    }
    
}
