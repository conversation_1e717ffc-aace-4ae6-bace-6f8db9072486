using DianGuanJiaApp.Data;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.FxModel.CategoryProduct;
using DianGuanJiaApp.Data.FxModel.Listing;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Services.ListingProduct;
using DianGuanJiaApp.Services.Model;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Services.Services.SupplierProduct.ListingProduct;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using Org.BouncyCastle.Ocsp;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Services.Services
{
    public class PlatformCategoryService : BaseService<PlatformCategory>
    {
        private PlatformCategoryRepository _platformCategoryRep;

        public PlatformCategoryService()
        {
            _platformCategoryRep = new PlatformCategoryRepository();
        }

        public List<BrandModel> GetBrand2(GetBrandReqModel req)
        {
            var redisKey = CacheKeys.DdShopBrandKey.Replace("{Sid}", req.ShopId.ToString())
                                                   .Replace("{Cid}", req.CateId);
            var value = RedisHelper.Get<List<BrandModel>>(redisKey);
            if (value != null && req.IsCache == 0)
                return value;

            List<BrandModel> resultList = new List<BrandModel>();
            var skey = PlatformAppScene.listing;
            var shopSvc = new ShopService(skey);
            if (req.ShopId == 0)
            {
                // 平台资料场景。通过末级类目Id找到所属店铺
                int shopid = _platformCategoryRep.GetCurrentShop(req.CateId, req.PlatformType);
                Shop shop = shopSvc.GetTouTiaoShopByIds(shopid)?.FirstOrDefault();

                if (shop == null)
                {
                    var shopList = shopSvc.GetShopNotExpTokenTouTiao(); // 随机找个没过期的店铺去拉
                    resultList = GetShopBrand(req, skey, shopList);
                }
                else
                {
                    // 使用类目归属的店铺去拉取品牌
                    resultList = GetShopBrand(req, skey, new List<Shop>() { shop });
                    if (resultList.Count == 0)
                    {
                        Log.WriteLine($"使用类目归属的店铺({shop.Id})去拉取品牌失败，改成其他店铺去拉取:");
                        var shopList = shopSvc.GetShopNotExpTokenTouTiao(); // 随机找个没过期的店铺去拉
                        Log.WriteLine($"使用其他店铺去拉取品牌成功，找到店铺：{shopList.Count}个，接口入参:{req.ToJson()}");
                        resultList = GetShopBrand(req, skey, shopList);
                    }
                }
            }
            else
            {
                Shop shop = shopSvc.GetTouTiaoShopByIds(req.ShopId).FirstOrDefault();
                resultList = GetShopBrand(req, skey, new List<Shop>() { shop });
            }

            RedisHelper.Set(redisKey, resultList, TimeSpan.FromHours(new Random().Next(2, 5)));
            return resultList;
        }

        private List<BrandModel> GetShopBrand(GetBrandReqModel req, string skey, List<Shop> shopList)
        {
            for (int i = 0; i < shopList.Count; i++)
            {
                var mshop = shopList[i];
                var zhidSvc = PlatformFactory.GetPlatformService(mshop, scene: skey);
                try
                {
                    var toutiaoPtService = zhidSvc as ZhiDianNewPlatformService;
                    var result = toutiaoPtService.GetBrandListNewV2(req.CateId, out bool auth_req);
                    var rlist = result.Item1.Where(a => !string.IsNullOrWhiteSpace(a.BrandNameCN))
                        .Select<BrandInfo, BrandModel>(a => new BrandModel(a.BrandNameCN)).ToList();
                    return rlist;
                }
                catch (LogicException ex)
                {
                }
            }

            return new List<BrandModel>();
        }

        public List<ListingCategory> GetCategoryByName(GetCategoryByNameResModel req)
        {
            int returnCount = req.ReturnCount;
            var result = new List<ListingCategory>();

            //小红书类目按用户维度可见且接口限流，不适合同步，这里默认不返回搜索结果
            if (req.ShopPt == PlatformType.XiaoHongShu.ToString())
            {
                return result;
            }

            var platformCateService = new PlatformCategoryService();
            //1.把用户的类目全部加载出来
            var categories = _platformCategoryRep.GetPlatformCategoriesByShopId(0, req.ShopPt);

            //2.查询搜索的类目数据
            var searchResult = categories.Where(f => f.PlatformType == req.ShopPt &&
                                                f.Name.IndexOf(req.CateName, StringComparison.OrdinalIgnoreCase) >= 0)
                .OrderByDescending(f => f.Level).ToList(); //按层级倒序
            if (searchResult == null || searchResult.Any() == false)
                return result;

            //用于匹配父级
            var parentCateDict = categories.ToDictionary(f => f.CateId, f => f);
            //用于匹配子集
            var childCateLookup = categories.ToLookup(f => f.ParentId, f => f);

            //3.取指定数量的类目
            var tempList = new List<PlatformCategory>();
            if (searchResult.Count > returnCount)
                tempList = searchResult.Take(returnCount).ToList();
            else
                tempList = searchResult;

            //找出规定数量的最后一级
            var lastLevelCates = new List<ListingCategory>();
            foreach (var item in tempList)
            {
                GetChildCategories(childCateLookup, item, returnCount, lastLevelCates);
                if (lastLevelCates.Count >= returnCount)
                    break;
            }

            //找父级
            foreach (var item in lastLevelCates)
            {
                var cate = GetParentCategories(parentCateDict, item);
                result.Add(cate);
            }

            result.Sort((x, y) => x.Name.CompareTo(y.Name));
            return result;
        }

        public List<ListingCategory> GetCategoryList(GetCategoryListReqModel req)
        {
            List<PlatformCategory> list = new List<PlatformCategory>();
            if (req.PlatformType == PlatformType.TikTok.ToString())
                list = _platformCategoryRep.GetCategoryAndRelationList(req).OrderBy(x => x.SortOrder).ToList();
            else
                list = _platformCategoryRep.GetCategoryList(req);
            return list.Select(a => new ListingCategory()
            {
                Id = a.CateId,
                Level = a.Level,
                IsParent = a.IsParent,
                Name = a.Name,
                Pid = a.ParentId,
                SortOrder = a.SortOrder,
                Status = a.Status,
                LicenseStatus = a.LicenseStatus,
            }).ToList();
        }

        public List<string> GetCategoryProp(string cateid)
        {
            List<string> propContent = _platformCategoryRep.GetCategoryProp(PlatformType.TouTiao.ToString(), cateid);
            return propContent;
        }

        public List<PlatformCategoryProp> GetCategoryPropByCateId(string platformType, string cateId)
        {
            return _platformCategoryRep.GetCategoryPropByCateId(platformType, cateId);
        }
        public List<int> GetShopIdByCateId(string cateId)
        {
            return _platformCategoryRep.GetShopIdByCateId(cateId);
        }
        public List<ListingProductEditResultModelByCateProps> GetCategoryProp(GetCategoryPropReqModel req)
        {
            var rrlist = new ConcurrentBag<ListingProductEditResultModelByCateProps>();
            if (req.PlatformType == PlatformType.Alibaba.ToString())
            {
                List<string> propContent = _platformCategoryRep.GetCategoryProp(req.PlatformType, req.EndCateId);
                foreach (var item in propContent)
                {
                    var propali = item.ToObject<CategoryPropAli>();
                    var putprop = new ListingProductEditResultModelByCateProps();

                    bool flag = propali.aspect != "3;" && propali.firstLevel && !propali.isSKUAttribute && propali.required;

                    // 可以当SKU的属性需要排除掉。1688 的属性只展示必填的
                    if (flag)
                    {
                        switch (propali.inputType)
                        {
                            // 数字输入框:
                            case "-1": putprop.FieldType = FieldTypeEnum.input.ToString(); break;
                            // 文本输入框
                            case "0": putprop.FieldType = FieldTypeEnum.input.ToString(); break;
                            // 下拉框
                            case "1": putprop.FieldType = FieldTypeEnum.singleCheck.ToString(); break;
                            // 多选框
                            case "2": putprop.FieldType = FieldTypeEnum.checkbox.ToString(); break;
                            // 单选框
                            case "3": putprop.FieldType = FieldTypeEnum.singleCheck.ToString(); break;
                            // 下拉框列表
                            case "4": putprop.FieldType = FieldTypeEnum.checkboxlist.ToString(); break;
                            // 日期
                            case "5": putprop.FieldType = FieldTypeEnum.dateTime.ToString(); break;
                            default: break;
                        }

                        putprop.Value = string.Empty;
                        putprop.Name = propali.name;
                        putprop.Options = new List<string>();
                        if (propali.attrValues != null && propali.attrValues.Count() > 0)
                        {
                            putprop.Options = propali.attrValues.Select<CategoryPropAli.Attrvalue, string>(a => a.name).ToList();
                        }
                        putprop.MeasureTemplates = new ListingProductEditResultModelByMeasureTemplates(); // 1688的没有度量衡
                        putprop.Rule = new ListingProductEditResultModelByRule()
                        {
                            IsRequired = propali.required
                        };
                        rrlist.Add(putprop);
                    }
                }
            }
            else if (req.PlatformType == PlatformType.TouTiao.ToString())
            {
                DyTranService dyTranService = new DyTranService();
                List<string> propContent = _platformCategoryRep.GetCategoryProp(req.PlatformType, req.EndCateId);
                dyTranService.MatchToutiaoAttr("", propContent)?.ForEach(x =>
                {
                    rrlist.Add(x);
                });
            }
            return rrlist.ToList();
        }
        /// <summary>
        /// 获取原始的平台类目属性
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        public List<string> GetPrimevalCategoryProps(GetCategoryPropReqModel req)
        {
            return _platformCategoryRep.GetCategoryProp(req.PlatformType, req.EndCateId);
        }
        /// <summary>
        /// 获取指定平台类目层级关系
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        public List<PlatformCategory> GetPlatformCategory(GetCategoryPropReqModel req)
        {
            List<PlatformCategory> platformCategories = new List<PlatformCategory>();
            GetAllPlatformCategoryLevel(req.PlatformType, req.EndCateId, platformCategories);
            return platformCategories;
        }
        private void GetAllPlatformCategoryLevel(string platformType, string endCateId, List<PlatformCategory> platformCategories)
        {
            var platformCategory = _platformCategoryRep.GetPlatformCategory(platformType, endCateId);
            if (platformCategory != null)
            {
                platformCategories.Add(platformCategory);
                if (!string.IsNullOrWhiteSpace(platformCategory.ParentId) && int.TryParse(platformCategory.ParentId, out int num) && num > 0)
                {
                    GetAllPlatformCategoryLevel(platformType, platformCategory.ParentId, platformCategories);
                }
            }
        }
        /// <summary>
        /// 找出当前类目的子类目
        /// </summary>
        /// <param name="allCategories"></param>
        /// <param name="cate"></param>
        private void GetChildCategories(ILookup<string, PlatformCategory> childCateLookup, PlatformCategory cate, int returnCount, List<ListingCategory> lastLevelCates)
        {
            //达到数量，则不再加载
            if (lastLevelCates.Count >= returnCount)
                return;

            var childCateList = childCateLookup[cate.CateId];
            //没有子集，说明已经是最后一级
            if (childCateList == null || childCateList.Any() == false)
            {
                //判断是否存在该类目，不存在才加入。并且加入的必须是叶子类目
                if (lastLevelCates.Any(f => f.Id == cate.CateId) == false && cate.IsParent == false)
                {
                    lastLevelCates.Add(new ListingCategory()
                    {
                        Id = cate.CateId,
                        Name = cate.Name,
                        Pid = cate.ParentId,
                        Status = cate.Status,
                        SortOrder = cate.SortOrder,
                        IsParent = cate.IsParent,
                        Level = cate.Level
                    });
                }
                return;
            }

            foreach (var item in childCateList)
                GetChildCategories(childCateLookup, item, returnCount, lastLevelCates);
        }

        /// <summary>
        /// 找出当前类目的父类目
        /// </summary>
        /// <param name="allCategories"></param>
        /// <param name="cate"></param>
        private ListingCategory GetParentCategories(Dictionary<string, PlatformCategory> parentCateDict, ListingCategory cate)
        {
            if (cate.Pid == "0")
                return cate;
            ListingCategory parentCate = null;
            if (parentCateDict.ContainsKey(cate.Pid))
            {
                var item = parentCateDict[cate.Pid];
                parentCate = new ListingCategory()
                {
                    Id = item.CateId,
                    Name = item.Name,
                    Pid = item.ParentId,
                    Status = item.Status,
                    SortOrder = item.SortOrder,
                    IsParent = item.IsParent,
                    Level = item.Level,
                    Child = new List<ListingCategory>() { cate }
                };
            }
            if (parentCate == null)
                return cate;

            return GetParentCategories(parentCateDict, parentCate);
        }

        public string GetPlatformCategoryPublishRule(string platformType, string categoryId)
        {
            return _platformCategoryRep.GetPlatformCategoryPublishRule(platformType, categoryId);
        }

        public StringBuilder GetCategoryDataComparison()
        {
            // 数据比对
            return _platformCategoryRep.GetCategoryDataComparison();
        }

        public List<PlatformCategoryNavigation> GetNavigationCateProp(string platformType, string propertyId)
        {
            return _platformCategoryRep.GetNavigationCateProp(platformType, propertyId);
        }
    }
}
