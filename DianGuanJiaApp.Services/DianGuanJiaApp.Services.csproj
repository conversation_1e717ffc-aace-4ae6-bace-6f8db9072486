<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{ABEE0A3A-3B86-4631-ADD0-3BDD17A0B1FC}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>DianGuanJiaApp.Services</RootNamespace>
    <AssemblyName>DianGuanJiaApp.Services</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Aliyun.OSS, Version=2.13.0.0, Culture=neutral, PublicKeyToken=0ad4175f0dac0b9b, processorArchitecture=MSIL">
      <HintPath>..\packages\Aliyun.OSS.SDK.2.13.0\lib\net45\Aliyun.OSS.dll</HintPath>
    </Reference>
    <Reference Include="AopSdk, Version=2.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Build\AopSdk.dll</HintPath>
    </Reference>
    <Reference Include="api-sdk">
      <HintPath>..\packages\Build\api-sdk.dll</HintPath>
      <Aliases>snsdk</Aliases>
    </Reference>
    <Reference Include="BouncyCastle.Crypto, Version=1.9.0.0, Culture=neutral, PublicKeyToken=0e99375e54769942, processorArchitecture=MSIL">
      <HintPath>..\packages\Portable.BouncyCastle.1.9.0\lib\net40\BouncyCastle.Crypto.dll</HintPath>
    </Reference>
    <Reference Include="BouncyCastle.Cryptography, Version=2.0.0.0, Culture=neutral, PublicKeyToken=072edcf4a5328938, processorArchitecture=MSIL">
      <HintPath>..\packages\BouncyCastle.Cryptography.2.4.0\lib\net461\BouncyCastle.Cryptography.dll</HintPath>
    </Reference>
    <Reference Include="CodeProject.ObjectPool, Version=3.0.0.0, Culture=neutral, PublicKeyToken=2f204b7110a52060, processorArchitecture=MSIL">
      <HintPath>..\packages\CodeProject.ObjectPool.3.2.4\lib\net45\CodeProject.ObjectPool.dll</HintPath>
    </Reference>
    <Reference Include="CSRedisCore, Version=3.8.670.0, Culture=neutral, PublicKeyToken=9aa6a3079358d437, processorArchitecture=MSIL">
      <HintPath>..\packages\CSRedisCore.3.8.670\lib\net45\CSRedisCore.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Data.v24.2, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\packages\DevExpress.Data.24.2.6\lib\net462\DevExpress.Data.v24.2.dll</HintPath>
    </Reference>
    <Reference Include="DinkToPdf, Version=1.0.8.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\DinkToPdf.1.0.8\lib\netstandard1.6\DinkToPdf.dll</HintPath>
    </Reference>
    <Reference Include="DnsClient, Version=1.0.7.0, Culture=neutral, PublicKeyToken=4574bb5573c51424, processorArchitecture=MSIL">
      <HintPath>..\packages\DnsClient.1.0.7\lib\net45\DnsClient.dll</HintPath>
    </Reference>
    <Reference Include="EbillSdk">
      <HintPath>..\packages\Build\EbillSdk.dll</HintPath>
    </Reference>
    <Reference Include="Elasticsearch.Net, Version=*******, Culture=neutral, PublicKeyToken=96c599bbe3e70f5d, processorArchitecture=MSIL">
      <HintPath>..\packages\Elasticsearch.Net.7.17.5\lib\net461\Elasticsearch.Net.dll</HintPath>
    </Reference>
    <Reference Include="Google.Protobuf, Version=3.25.1.0, Culture=neutral, PublicKeyToken=a7d26565bac4d604, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Protobuf.3.19.4\lib\net45\Google.Protobuf.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Google.ProtocolBuffers">
      <HintPath>..\packages\Build\Google.ProtocolBuffers.dll</HintPath>
    </Reference>
    <Reference Include="Google.ProtocolBuffers.Serialization">
      <HintPath>..\packages\Build\Google.ProtocolBuffers.Serialization.dll</HintPath>
    </Reference>
    <Reference Include="HtmlAgilityPack, Version=1.11.46.0, Culture=neutral, PublicKeyToken=bd319b19eaf3b43a, processorArchitecture=MSIL">
      <HintPath>..\packages\HtmlAgilityPack.1.11.46\lib\Net45\HtmlAgilityPack.dll</HintPath>
    </Reference>
    <Reference Include="HtmlRenderer, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\HtmlRenderer.Core.*******\lib\net45\HtmlRenderer.dll</HintPath>
    </Reference>
    <Reference Include="HtmlRenderer.PdfSharp, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\HtmlRenderer.PdfSharp.*******\lib\net45\HtmlRenderer.PdfSharp.dll</HintPath>
    </Reference>
    <Reference Include="ICSharpCode.SharpZipLib, Version=0.86.0.518, Culture=neutral, PublicKeyToken=1b03e6acf1164f73, processorArchitecture=MSIL">
      <HintPath>..\packages\SharpZipLib.0.86.0\lib\20\ICSharpCode.SharpZipLib.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="itextsharp, Version=********, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <HintPath>..\packages\iTextSharp.********\lib\net461\itextsharp.dll</HintPath>
    </Reference>
    <Reference Include="itextsharp.xmlworker, Version=********, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <HintPath>..\packages\itextsharp.xmlworker.********\lib\net461\itextsharp.xmlworker.dll</HintPath>
    </Reference>
    <Reference Include="K4os.Compression.LZ4, Version=*******, Culture=neutral, PublicKeyToken=2186fa9121ef231d, processorArchitecture=MSIL">
      <HintPath>..\packages\K4os.Compression.LZ4.1.2.6\lib\net46\K4os.Compression.LZ4.dll</HintPath>
    </Reference>
    <Reference Include="K4os.Compression.LZ4.Streams, Version=*******, Culture=neutral, PublicKeyToken=2186fa9121ef231d, processorArchitecture=MSIL">
      <HintPath>..\packages\K4os.Compression.LZ4.Streams.1.2.6\lib\net46\K4os.Compression.LZ4.Streams.dll</HintPath>
    </Reference>
    <Reference Include="K4os.Hash.xxHash, Version=1.0.6.0, Culture=neutral, PublicKeyToken=32cd54395057cec3, processorArchitecture=MSIL">
      <HintPath>..\packages\K4os.Hash.xxHash.1.0.6\lib\net46\K4os.Hash.xxHash.dll</HintPath>
    </Reference>
    <Reference Include="LOGSDK">
      <HintPath>..\packages\Build\LOGSDK.dll</HintPath>
    </Reference>
    <Reference Include="LZ4Sharp">
      <HintPath>..\packages\Build\LZ4Sharp.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Http.Features, Version=5.0.17.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Http.Features.5.0.17\lib\net461\Microsoft.AspNetCore.Http.Features.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=9.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.9.0.0\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Diagnostics.Tracing.EventSource, Version=1.1.28.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Diagnostics.Tracing.EventSource.Redist.1.1.28\lib\net46\Microsoft.Diagnostics.Tracing.EventSource.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection.Abstractions, Version=9.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.Abstractions.9.0.0\lib\net462\Microsoft.Extensions.DependencyInjection.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging.Abstractions, Version=9.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.Abstractions.9.0.0\lib\net462\Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Web.Infrastructure, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Web.Infrastructure.*******\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Primitives, Version=5.0.0.1, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Primitives.5.0.1\lib\net461\Microsoft.Extensions.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Win32.Primitives.4.0.1\lib\net46\Microsoft.Win32.Primitives.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="MongoDB.Bson, Version=2.7.2.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\MongoDB.Bson.2.7.2\lib\net45\MongoDB.Bson.dll</HintPath>
    </Reference>
    <Reference Include="MongoDB.Driver, Version=2.7.2.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\MongoDB.Driver.2.7.2\lib\net45\MongoDB.Driver.dll</HintPath>
    </Reference>
    <Reference Include="MongoDB.Driver.Core, Version=2.7.2.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\MongoDB.Driver.Core.2.7.2\lib\net45\MongoDB.Driver.Core.dll</HintPath>
    </Reference>
    <Reference Include="MySql.Data, Version=8.0.30.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d, processorArchitecture=MSIL">
      <HintPath>..\packages\MySql.Data.8.0.30\lib\net452\MySql.Data.dll</HintPath>
    </Reference>
    <Reference Include="Nest, Version=*******, Culture=neutral, PublicKeyToken=96c599bbe3e70f5d, processorArchitecture=MSIL">
      <HintPath>..\packages\NEST.7.17.5\lib\net461\Nest.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NPOI, Version=2.4.0.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.4.0\lib\net40\NPOI.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="NPOI.OOXML, Version=2.4.0.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.4.0\lib\net40\NPOI.OOXML.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="NPOI.OpenXml4Net, Version=2.4.0.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.4.0\lib\net40\NPOI.OpenXml4Net.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="NPOI.OpenXmlFormats, Version=2.4.0.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.4.0\lib\net40\NPOI.OpenXmlFormats.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="OpenTelemetry.Api.ProviderBuilderExtensions">
      <HintPath>..\packages\Build\OpenTelemetryPackage\OpenTelemetry.Api.ProviderBuilderExtensions.dll</HintPath>
    </Reference>
    <Reference Include="Osp sdk">
      <HintPath>..\packages\Build\Osp sdk.dll</HintPath>
    </Reference>
    <Reference Include="PdfSharp, Version=1.50.5147.0, Culture=neutral, PublicKeyToken=f94615aa0424f9eb, processorArchitecture=MSIL">
      <HintPath>..\packages\PDFsharp.1.50.5147\lib\net20\PdfSharp.dll</HintPath>
    </Reference>
    <Reference Include="PdfSharp.Charting, Version=1.50.5147.0, Culture=neutral, PublicKeyToken=f94615aa0424f9eb, processorArchitecture=MSIL">
      <HintPath>..\packages\PDFsharp.1.50.5147\lib\net20\PdfSharp.Charting.dll</HintPath>
    </Reference>
    <Reference Include="Pipelines.Sockets.Unofficial, Version=*******, Culture=neutral, PublicKeyToken=42ea0a778e13fbe2, processorArchitecture=MSIL">
      <HintPath>..\packages\Pipelines.Sockets.Unofficial.2.2.2\lib\net461\Pipelines.Sockets.Unofficial.dll</HintPath>
    </Reference>
    <Reference Include="Polly, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Polly.5.9.0\lib\net45\Polly.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=c4ed5b9ae8358a28, processorArchitecture=MSIL">
      <HintPath>..\packages\QRCoder.1.6.0\lib\net40\QRCoder.dll</HintPath>
    </Reference>
    <Reference Include="RabbitMQ.Client, Version=*******, Culture=neutral, PublicKeyToken=89e7d7c5feba84ce, processorArchitecture=MSIL">
      <HintPath>..\packages\RabbitMQ.Client.5.1.2\lib\net451\RabbitMQ.Client.dll</HintPath>
    </Reference>
    <Reference Include="SecurityLog, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Build\SecurityLog.dll</HintPath>
    </Reference>
    <Reference Include="StackExchange.Redis, Version=2.0.0.0, Culture=neutral, PublicKeyToken=c219ff1ca8c2ce46, processorArchitecture=MSIL">
      <HintPath>..\packages\StackExchange.Redis.2.5.61\lib\net461\StackExchange.Redis.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.AppContext, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.AppContext.4.1.0\lib\net46\System.AppContext.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Buffers, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" />
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Configuration.ConfigurationManager, Version=4.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Configuration.ConfigurationManager.4.4.1\lib\net461\System.Configuration.ConfigurationManager.dll</HintPath>
    </Reference>
    <Reference Include="System.Configuration.Install" />
    <Reference Include="System.Console, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Console.4.0.0\lib\net46\System.Console.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Core" />
    <Reference Include="System.Data.OracleClient" />
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=9.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.DiagnosticSource.9.0.0\lib\net462\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.PerformanceCounter, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.PerformanceCounter.5.0.0\lib\net461\System.Diagnostics.PerformanceCounter.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Tracing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.Tracing.4.1.0\lib\net462\System.Diagnostics.Tracing.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Drawing" />
    <Reference Include="System.Globalization.Calendars, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Globalization.Calendars.4.0.1\lib\net46\System.Globalization.Calendars.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.4.1.0\lib\net462\System.IO.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Compression, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Compression.4.3.0\lib\net46\System.IO.Compression.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem" />
    <Reference Include="System.IO.Compression.ZipFile, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Compression.ZipFile.4.0.1\lib\net46\System.IO.Compression.ZipFile.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.FileSystem.4.0.1\lib\net46\System.IO.FileSystem.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.FileSystem.Primitives.4.0.1\lib\net46\System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Pipelines, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Pipelines.5.0.2\lib\net461\System.IO.Pipelines.dll</HintPath>
    </Reference>
    <Reference Include="System.Management" />
    <Reference Include="System.Memory, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net" />
    <Reference Include="System.Net.Http, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Http.4.1.0\lib\net46\System.Net.Http.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Net.Sockets, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Sockets.4.1.0\lib\net46\System.Net.Sockets.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Reflection.4.3.0\lib\net462\System.Reflection.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Reflection.TypeExtensions, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Reflection.TypeExtensions.4.3.0\lib\net462\System.Reflection.TypeExtensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.4.3.0\lib\net462\System.Runtime.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.Caching" />
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Extensions, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.Extensions.4.1.0\lib\net462\System.Runtime.Extensions.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.InteropServices.4.3.0\lib\net462\System.Runtime.InteropServices.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.InteropServices.RuntimeInformation.4.3.0\lib\net45\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Loader, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.Loader.4.3.0\lib\netstandard1.5\System.Runtime.Loader.dll</HintPath>
    </Reference>
    <Reference Include="System.Security" />
    <Reference Include="System.Security.AccessControl, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.AccessControl.4.7.0\lib\net461\System.Security.AccessControl.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Algorithms.4.2.0\lib\net461\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Encoding.4.0.0\lib\net46\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Primitives.4.0.0\lib\net46\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.X509Certificates.4.1.0\lib\net461\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Permissions, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Permissions.4.7.0\lib\net461\System.Security.Permissions.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Principal.Windows, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Principal.Windows.4.7.0\lib\net461\System.Security.Principal.Windows.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Text.Encoding.CodePages, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encoding.CodePages.4.4.0\lib\net461\System.Text.Encoding.CodePages.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encodings.Web, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.7.0.0\lib\netstandard2.0\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Json.7.0.1\lib\netstandard2.0\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Channels, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Channels.5.0.0\lib\net461\System.Threading.Channels.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Thread, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Thread.4.0.0\lib\net46\System.Threading.Thread.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Transactions" />
    <Reference Include="System.ValueTuple, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net461\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Helpers, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.9\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=5.2.9.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.9\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.9\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.9\lib\net45\System.Web.WebPages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.9\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.9\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="Ubiety.Dns.Core, Version=2.2.1.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d, processorArchitecture=MSIL">
      <HintPath>..\packages\MySql.Data.8.0.30\lib\net452\Ubiety.Dns.Core.dll</HintPath>
    </Reference>
    <Reference Include="Volcengine.TOS, Version=2.1.2.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Volcengine.TOS.SDK.2.1.2\lib\net45\Volcengine.TOS.dll</HintPath>
    </Reference>
    <Reference Include="WindowsBase" />
    <Reference Include="ZstdNet, Version=1.4.5.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d, processorArchitecture=MSIL">
      <HintPath>..\packages\MySql.Data.8.0.30\lib\net452\ZstdNet.dll</HintPath>
    </Reference>
    <Reference Include="ZstdSharp, Version=0.7.1.0, Culture=neutral, PublicKeyToken=8d151af33a4ad5cf, processorArchitecture=MSIL">
      <HintPath>..\packages\ZstdSharp.Port.0.7.1\lib\net461\ZstdSharp.dll</HintPath>
    </Reference>
    <Reference Include="zxing, Version=0.16.10.0, Culture=neutral, PublicKeyToken=4e88037ac681fe60, processorArchitecture=MSIL">
      <HintPath>..\packages\ZXing.Net.0.16.10\lib\net461\zxing.dll</HintPath>
    </Reference>
    <Reference Include="zxing.presentation, Version=0.16.10.0, Culture=neutral, PublicKeyToken=4e88037ac681fe60, processorArchitecture=MSIL">
      <HintPath>..\packages\ZXing.Net.0.16.10\lib\net461\zxing.presentation.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="IService\ICloudStorageService.cs" />
    <Compile Include="IService\IColdLogicOrderService.cs" />
    <Compile Include="IService\IDataArchiveBaseService.cs" />
    <Compile Include="IService\ILogicOrderService.cs" />
    <Compile Include="IService\IColdOrderService.cs" />
    <Compile Include="Model\ApiLogEntity\SyncAfterSaleOrderLog.cs" />
    <Compile Include="Model\ApiLogEntity\SyncOrderLog.cs" />
    <Compile Include="Model\ApiLogEntity\SyncLogEntityBase.cs" />
    <Compile Include="Model\ApiLogEntity\SyncProducrLog.cs" />
    <Compile Include="Model\ExceptionModel\OrderBalanceException.cs" />
    <Compile Include="Model\FieldTypeEnum.cs" />
    <Compile Include="Model\DyTranModel\CateErrorInfo.cs" />
    <Compile Include="Model\DyTranModel\ProductPropertiesMapping.cs" />
    <Compile Include="LogisticService\KuaiShouLogisticService.cs" />
    <Compile Include="IService\IMergerFxOrderService.cs" />
    <Compile Include="IService\IProductFxService.cs" />
    <Compile Include="IService\IService.cs" />
    <Compile Include="LogisticService\TouTiaoLogisticService.cs" />
    <Compile Include="LogisticService\NewYunDaLogisticService.cs" />
    <Compile Include="LogisticService\NewZTOLogisticService.cs" />
    <Compile Include="LogisticService\DBKDLogisticService.cs" />
    <Compile Include="LogisticService\NewSTOLogisticService.cs" />
    <Compile Include="LogisticService\WaybillCommonService.cs" />
    <Compile Include="LogisticService\JTLogisticService.cs" />
    <Compile Include="LogisticService\NewYTOLogisticService.cs" />
    <Compile Include="LogisticService\WxVideoLogisticService.cs" />
    <Compile Include="LogisticService\XiaoHongShuLogisticService.cs" />
    <Compile Include="Model\AlibabaEncryptOutOrderInfoModel.cs" />
    <Compile Include="Model\BindSupplieTempModel.cs" />
    <Compile Include="Model\Comensate\InvokeApiDataMetaDataModel.cs" />
    <Compile Include="Model\ListingTaskRecordsModel.cs" />
    <Compile Include="Model\ExecuteCheckModel.cs" />
    <Compile Include="Model\FxOrderOnlineSendModel.cs" />
    <Compile Include="Model\KuaiShouMarketOrderDetailModel.cs" />
    <Compile Include="Model\ListingProductEditResultModel.cs" />
    <Compile Include="Model\OrderRecommendedExpressQueryModel.cs" />
    <Compile Include="Model\PushDbInfo.cs" />
    <Compile Include="Model\SaveShippingFeeTemplateModel.cs" />
    <Compile Include="Model\SellerFlagInfoModel.cs" />
    <Compile Include="Model\GetPathflowCodeModel.cs" />
    <Compile Include="Model\TemplateAddModel.cs" />
    <Compile Include="Model\TemplateEditModel.cs" />
    <Compile Include="Model\TouTiaoOrderDeliveryDecisionQueryModel.cs" />
    <Compile Include="Model\DyTranModel\SupplierProductAttribute.cs" />
    <Compile Include="Model\UpdateSellerRemarkModel.cs" />
    <Compile Include="Model\WebSiteWriteLogModel.cs" />
    <Compile Include="Model\WeiMengInterfaceModel.cs" />
    <Compile Include="MongoDBService\LogOperatorStatisticService.cs" />
    <Compile Include="LogisticService\ZYKDLogisticService.cs" />
    <Compile Include="MongoDBService\LogForPlatformTypeExceptionDetailService.cs" />
    <Compile Include="MongoDBService\MongoDBService.cs" />
    <Compile Include="Model\TopApiModelExtension.cs" />
    <Compile Include="PlatformLogHelper.cs" />
    <Compile Include="PlatformService\AlibabaC2MService.cs" />
    <Compile Include="PlatformService\AlibabaCrossPlatformService.cs" />
    <Compile Include="PlatformService\AlibabaQingPlatformService.cs" />
    <Compile Include="PlatformService\AlibabaTaoBigShopPlatformService.cs" />
    <Compile Include="PlatformService\AlibabaZhuKePlatformService.cs" />
    <Compile Include="PlatformService\ApiLogService\AlibabaC2MServiceExtension.cs" />
    <Compile Include="PlatformService\ApiLogService\AlibabaPlatformServiceExtension.cs" />
    <Compile Include="PlatformService\ApiLogService\DuXiaoDianV2PlatformServiceExtension.cs" />
    <Compile Include="PlatformService\ApiLogService\KuaiShouPlatformServiceExtension.cs" />
    <Compile Include="PlatformService\ApiLogService\MoGuJiePlatformServiceExtension.cs" />
    <Compile Include="PlatformService\ApiLogService\WeiMengPlatformServiceV2Extension.cs" />
    <Compile Include="PlatformService\ApiLogService\WxVideoPlatformServiceExtension.cs" />
    <Compile Include="PlatformService\ApiLogService\XiaoHongShuPlatformServiceExtension.cs" />
    <Compile Include="PlatformService\ApiLogService\XiaoHongShuV2PlatformServiceExtension.cs" />
    <Compile Include="PlatformService\ApiLogService\ZhiDianNewPlatformServiceExtension.cs" />
    <Compile Include="PlatformService\BeiBeiPlatformService.cs" />
    <Compile Include="PlatformService\BeiBeiApiClient.cs" />
    <Compile Include="PlatformService\BasePlatformService.cs" />
    <Compile Include="PlatformService\BiliBiliApiClient.cs" />
    <Compile Include="PlatformService\BiliBiliPlatformService.cs" />
    <Compile Include="PlatformService\DouDian\DouDianPlatformCateService.cs" />
    <Compile Include="PlatformService\DuXiaoDianApiClient.cs" />
    <Compile Include="PlatformService\DuXiaoDianPlatformService.cs" />
    <Compile Include="PlatformService\DuXiaoDianV2ApiClient.cs" />
    <Compile Include="PlatformService\DuXiaoDianV2PlatformService.cs" />
    <Compile Include="PlatformService\ISyncOrderJsonService.cs" />
    <Compile Include="PlatformService\IUpdateReceiverInfo.cs" />
    <Compile Include="PlatformService\JingDongPurchasePlatformService.cs" />
    <Compile Include="PlatformService\KuaiShouSupplierPlatformService.cs" />
    <Compile Include="PlatformService\KuaiTuanTuanApiClient.cs" />
    <Compile Include="PlatformService\KuaiShouLogisticsService.cs" />
    <Compile Include="PlatformService\KuaiTuanTuanPlatformService.cs" />
    <Compile Include="PlatformService\MoKuaiApiClient.cs" />
    <Compile Include="PlatformService\MoKuaiPlatformService.cs" />
    <Compile Include="PlatformService\OfflineVirtualPlatformService.cs" />
    <Compile Include="PlatformService\OpenPlatformApiClient.cs" />
    <Compile Include="PlatformService\OpenPlatformService.cs" />
    <Compile Include="PlatformService\KuaiShouApiClient.cs" />
    <Compile Include="PlatformService\KuaiShouPlatformService.cs" />
    <Compile Include="PlatformService\OtherPlatforms\HaoYouDuoApiClient.cs" />
    <Compile Include="PlatformService\OtherPlatforms\HaoYouDuoPlatformService.cs" />
    <Compile Include="PlatformService\OtherPlatforms\JuHaoMaiApiClient.cs" />
    <Compile Include="PlatformService\OtherPlatforms\JuHaoMaiPlatformService.cs" />
    <Compile Include="PlatformService\OtherPlatforms\HeliangApiClient.cs" />
    <Compile Include="PlatformService\OwnShopPlatformService.cs" />
    <Compile Include="PlatformService\OtherPlatforms\HeliangPlatformService.cs" />
    <Compile Include="PlatformService\PinduoduoPlatformFFMService.cs" />
    <Compile Include="PlatformService\PinduoduoPlatformFDSService.cs" />
    <Compile Include="PlatformService\SyncFxOrderService.cs" />
    <Compile Include="PlatformService\SyncFxProductService.cs" />
    <Compile Include="PlatformService\TaobaoMaiCaiPlatformService.cs" />
    <Compile Include="PlatformService\TikTokApiClient.cs" />
    <Compile Include="PlatformService\TikTokPlatformService.cs" />
    <Compile Include="PlatformService\Track\IPlatformTrackPushService.cs" />
    <Compile Include="PlatformService\Track\PlatformTrackSendUtils.cs" />
    <Compile Include="PlatformService\WeiMengApiClientV2.cs" />
    <Compile Include="PlatformService\WeiMengPlatformServiceV2.cs" />
    <Compile Include="PlatformService\WeiXin\WxVideoLogisticService.cs" />
    <Compile Include="PlatformService\WeiXin\WxXiaoShangDianApiClient.cs" />
    <Compile Include="PlatformService\WeiXin\WxXiaoShangDianPlatformService.cs" />
    <Compile Include="PlatformService\TuanHaoHuoApiClient.cs" />
    <Compile Include="PlatformService\XiaoHongShu\XiaoHongShuLogisticService.cs" />
    <Compile Include="PlatformService\XiaoHongShu\XiaoHongShuV2PlatformService.cs" />
    <Compile Include="PlatformService\XiaoHongShu\XiaoHongShuV2ApiClient.cs" />
    <Compile Include="PlatformService\XiaoHongShu\XiaoHongShuApiClient.cs" />
    <Compile Include="PlatformService\TuanHaoHuoPlatformService.cs" />
    <Compile Include="PlatformService\XiaoHongShu\XiaoHongShuPlatformService.cs" />
    <Compile Include="PlatformService\VipShopJITPlatformService.cs" />
    <Compile Include="PlatformService\ZhiDianNewApiClient.cs" />
    <Compile Include="PlatformService\ZhiDianNewPlatformService.cs" />
    <Compile Include="PlatformService\ZhiDianNewPlatformService2.cs" />
    <Compile Include="PlatformService\ZhiDianNewPlatformServiceAfterSaleOrder.cs" />
    <Compile Include="PlatformService\ZhiDianNewSaleShopPlatformService.cs" />
    <Compile Include="PlatformService\ZYKDApiClient.cs" />
    <Compile Include="ServicesExtension\OpenEbillSendTaskService.cs" />
    <Compile Include="ServicesExtension\AlipayService.cs" />
    <Compile Include="ServicesExtension\LoggerConfigService.cs" />
    <Compile Include="ServicesExtension\RedisConfigService.cs" />
    <Compile Include="ServicesExtension\MergerFxOrderService.cs" />
    <Compile Include="ServicesExtension\OuterDbAcessService.cs" />
    <Compile Include="ServicesExtension\PayService.cs" />
    <Compile Include="PlatformService\YunJiApiClient.cs" />
    <Compile Include="PlatformService\MengTuiApiClient.cs" />
    <Compile Include="PlatformService\YunJiPlatformService.cs" />
    <Compile Include="PlatformService\MengTuiPlatformService.cs" />
    <Compile Include="ServicesExtension\RedisService.cs" />
    <Compile Include="ServicesExtension\SystemApiFxAfterSaleQueueService.cs" />
    <Compile Include="ServicesExtension\SystemApiFxExportTaskQueueService.cs" />
    <Compile Include="ServicesExtension\SystemApiFxQueueService.cs" />
    <Compile Include="ServicesExtension\SystemApiQueueService.cs" />
    <Compile Include="Services\AfterSaleAddressService.cs" />
    <Compile Include="Services\AfterSaleOrderItemStockRecordService.cs" />
    <Compile Include="Services\AntiCrawlerConfigService.cs" />
    <Compile Include="Services\BaseProduct\BaseProductptRelationsCompensateService.cs" />
    <Compile Include="Services\BaseProduct\BaseProductSkuAttributeService.cs" />
    <Compile Include="Services\BaseProduct\GenerateBaseProductRecordService.cs" />
    <Compile Include="Services\BaseProduct\PtImgService.cs" />
    <Compile Include="Services\BaseProduct\PtProductInfoService.cs" />
    <Compile Include="Services\DataArchive\DataArchiveBySettingIllegalCharacterService.cs" />
    <Compile Include="Services\DataEventTrackingLog\OrderWaybillCodeInvokeApiLogService.cs" />
    <Compile Include="Services\DataEventTrackingLog\SplitLogicOrderHistoryLogService.cs" />
    <Compile Include="Services\DataEventTrackingLog\SplitLogicOrderPathFlowExLogService.cs" />
    <Compile Include="Services\DataEventTracking\OrderWaybillCodeInvokeApiDataTrackingService.cs" />
    <Compile Include="Services\DataArchive\DataArchiveByCommonSettingClearService.cs" />
    <Compile Include="Services\DataEventTrackingLog\DataDuplicationLogService.cs" />
    <Compile Include="Services\DataEventTrackingLog\OptimisticLockLogService.cs" />
    <Compile Include="Services\DataEventTrackingLog\SendHistoryReturnRecordLogService.cs" />
    <Compile Include="Services\DataEventTrackingLog\ServiceQueueMonitorLogService.cs" />
    <Compile Include="Services\DataEventTrackingLog\SyncServiceTaskLogService.cs" />
    <Compile Include="Services\DataEventTracking\DataDuplicationLogDataEventTrackingService.cs" />
    <Compile Include="Services\DataEventTracking\DataDuplicationStatusHistoryLogTrackingService.cs" />
    <Compile Include="Services\DataEventTracking\DataSyncDuplicationLogDataEventTrackingService.cs" />
    <Compile Include="Services\DataEventTracking\ExceptionLogDataEventTrackingService.cs" />
    <Compile Include="Services\DataEventTracking\MessageDataSyncStorageLogDataTrackingService.cs" />
    <Compile Include="Services\DataEventTracking\OptimisticLockDataEventTrackingService.cs" />
    <Compile Include="Services\DataEventTracking\SendHistoryReturnRecordLogDataTrackingService.cs" />
    <Compile Include="Services\DataEventTracking\ServiceQueueMonitorLogDataEventTrackingService.cs" />
    <Compile Include="Services\DataEventTracking\SplitLogicOrderHistoryLogDataTrackingService.cs" />
    <Compile Include="Services\DataEventTracking\SplitLogicOrderPathFlowExLogDataTrackingService.cs" />
    <Compile Include="Services\DataEventTracking\SyncServiceTaskDataEventTrackingService.cs" />
    <Compile Include="Services\InstantRetailService.cs" />
    <Compile Include="Services\OpenTelemetry\OtlpSignozService.cs" />
    <Compile Include="Services\CrossBorder\CrossBorderPrintService.cs" />
    <Compile Include="Services\OpenPlatform\OpenPlatformAfterSaleService.cs" />
    <Compile Include="Services\OpenPlatform\OpenPlatformBaseProductService.cs" />
    <Compile Include="Services\DeliveryModeChangeLogService.cs" />
    <Compile Include="Services\MemberLevelService.cs" />
    <Compile Include="Services\OpenPlatform\OpenPlatformWaybillService.cs" />
    <Compile Include="Services\OpenTelemetry\OpenTelemetryConfigService.cs" />
    <Compile Include="Services\OpenTelemetry\OtlpClickHouseService.cs" />
    <Compile Include="Services\OpenTelemetry\OtlpEsService.cs" />
    <Compile Include="Services\OpenTelemetry\OtlpSignozService.cs" />
    <Compile Include="Services\ProductSkuHistoryService.cs" />
    <Compile Include="Services\FxUserShopDefaultSupplierService.cs" />
    <Compile Include="Services\FreightTemplate\FreightTemplateService.cs" />
    <Compile Include="Services\FreightTemplate\Sdk\FreightTemplateClient.cs" />
    <Compile Include="Services\FreightTemplate\Sdk\Helpers\TimeStampHelper.cs" />
    <Compile Include="Services\FreightTemplate\Sdk\IFreightTemplateClient.cs" />
    <Compile Include="Services\RepairSubProductSettlementPrice.cs" />
    <Compile Include="Services\SplitExpressService.cs" />
    <Compile Include="Services\ReceiverOaidService.cs" />
    <Compile Include="Services\Stat\StatActiveUserCountService.cs" />
    <Compile Include="Services\Stat\StatSendHistoryOrderCountNewService.cs" />
    <Compile Include="Services\ShopVideo\ProductService.cs" />
    <Compile Include="Services\ShopVideo\PublishVideoTaskService.cs" />
    <Compile Include="Services\ShopVideo\ShopVideoService.cs" />
    <Compile Include="Services\ShopVideo\VideoUploadRecordService.cs" />
    <Compile Include="Services\Stat\StatActiveService.cs" />
    <Compile Include="Services\Stat\StatBaseproduceService.cs" />
    <Compile Include="Services\Stat\StatPlatformOrderService.cs" />
    <Compile Include="Services\Stat\StatSendHistoryCountService.cs" />
    <Compile Include="Services\SubAccount\PostPermissionFxService.cs" />
    <Compile Include="Services\DataEventTrackingLog\OrderSendInvokeApiLogService.cs" />
    <Compile Include="Services\DataEventTrackingLog\OrderSyncAnalysisAutoReasonLogService.cs" />
    <Compile Include="Services\DataEventTrackingLog\OrderSyncStatisticsLogService.cs" />
    <Compile Include="Services\DataEventTracking\OrderSendInvokeApiDataTrackingService .cs" />
    <Compile Include="Services\MessageQueue\OrderSyncAnalysisSecondMessageService.cs" />
    <Compile Include="Services\MessageQueue\TouTiaoOrderSyncAnalysisMessageHandleService.cs" />
    <Compile Include="Services\MessageQueue\TouTiaoOrderSyncAnalysisMessageService.cs" />
    <Compile Include="Services\CrossBorder\CurrencyConvertRateServer.cs" />
    <Compile Include="Services\CrossBorder\GlobalProductService.cs" />
    <Compile Include="Services\CrossBorder\MatchCateService.cs" />
    <Compile Include="Services\CrossBorder\PlatformCategoryCrossBorderService.cs" />
    <Compile Include="Services\CrossBorder\CollectClaimRelationService.cs" />
    <Compile Include="Services\CrossBorder\CollectProductBaseService.cs" />
    <Compile Include="Services\CrossBorder\CommCollecBoxService.cs" />
    <Compile Include="Services\SupplierProduct\ListingProduct\DyTranService.cs" />
    <Compile Include="Services\SupplierProduct\ListingProduct\ListingTemplateGroupItemService.cs" />
    <Compile Include="Services\SupplierProduct\ListingProduct\ListingTemplateGroupService.cs" />
    <Compile Include="Services\BaseProduct\BaseOfSupplierSkuRelationService.cs" />
    <Compile Include="Services\BaseProduct\BaseProductSkuCommonService.cs" />
    <Compile Include="Services\ApiCallLogService.cs" />
    <Compile Include="Services\BaseProduct\BaseProductSkuSupplierConfigService.cs" />
    <Compile Include="Services\BaseProduct\BaseProductAbnormalService.cs" />
    <Compile Include="Services\BusinessCardRemarkService.cs" />
    <Compile Include="Services\CloudMessageBaseService.cs" />
    <Compile Include="Services\ConfigDbMigrate\ConfigDbMigrateService.cs" />
    <Compile Include="Services\ConfigDbMigrate\PrintHistoryDbMigrateService.cs" />
    <Compile Include="Services\ConfigDbMigrate\ReceDbMigrateService.cs" />
    <Compile Include="Services\CooperateStatusRecordService.cs" />
    <Compile Include="Services\CooperateEvaluateService.cs" />
    <Compile Include="Services\BusinessCardService.cs" />
    <Compile Include="Services\CustomEncryptionService.cs" />
    <Compile Include="Services\AfterSaleActionItemRecordService.cs" />
    <Compile Include="Services\AfterSaleOrderRelationService.cs" />
    <Compile Include="Services\AliOpenEbillSendRecordService.cs" />
    <Compile Include="Services\AfterSaleActionRecordService.cs" />
    <Compile Include="Services\BaseProduct\BaseProductService.cs" />
    <Compile Include="Services\ApiCallLogService.cs" />
    <Compile Include="Services\BaseProduct\BaseProductSkuSupplierConfigService.cs" />
    <Compile Include="Services\BaseProduct\MessageRecordService.cs" />
    <Compile Include="Services\BaseProduct\OssObjectService.cs" />
    <Compile Include="Services\BaseProduct\BaseProductBaseService.cs" />
    <Compile Include="Services\BaseProduct\BaseOfPtSkuRelationService.cs" />
    <Compile Include="Services\BaseProduct\ProductDbConfigService.cs" />
    <Compile Include="Services\BaseProduct\BaseProductSkuService.cs" />
    <Compile Include="Services\Base\BaseColdHotStorageService.cs" />
    <Compile Include="Services\CloudStorageUploaderFactory.cs" />
    <Compile Include="Services\ColdHotStorage\ColdLogicOrderStateService.cs" />
    <Compile Include="Services\ColdHotStorage\LogicOrderColdHotStorageService.cs" />
    <Compile Include="Services\ColdHotStorage\OrderColdHotStorageService.cs" />
    <Compile Include="Services\DataDuplication\PrintHistoryDuplicationService.cs" />
    <Compile Include="Services\SupplierProduct\ListingProduct\UserListingSettingService.cs" />
    <Compile Include="Services\DataArchive\ArchiveUserService.cs" />
    <Compile Include="Services\DataArchive\DataArchiveBaseService.cs" />
    <Compile Include="Services\DataArchive\DataArchiveByCleanService.cs" />
    <Compile Include="Services\DataArchive\DataArchiveByExpiredUserService.cs" />
    <Compile Include="Services\DataArchive\DataArchiveBySettlementBillClearService.cs" />
    <Compile Include="Services\DataArchive\DataArchiveBySettlementBillService.cs" />
    <Compile Include="Services\DataArchive\DataArchiveByTimeBackupDbService.cs" />
    <Compile Include="Services\DataArchive\DataArchiveHistoryService.cs" />
    <Compile Include="Services\DataArchive\DataArchiveRestoreService.cs" />
    <Compile Include="Services\DataArchive\DataArchiveService.cs" />
    <Compile Include="Services\DataArchive\DataArchiveWinService.cs" />
    <Compile Include="Services\DataEventTrackingLog\SendHistoryAnalysisResultLogService.cs" />
    <Compile Include="Services\FxUserForeignShopService.cs" />
    <Compile Include="Services\GreyCompatible\ProfitStatisticsMsgService.cs" />
    <Compile Include="Services\HeartbeatService.cs" />
    <Compile Include="Services\MessageHistoryRecordService.cs" />
    <Compile Include="Services\IPAreaMappingService.cs" />
    <Compile Include="Services\MessageQueue\ImportSettlementPriceHandleMessageService.cs" />
    <Compile Include="Services\HotProduct\HotProductCloudMessageService.cs" />
    <Compile Include="Services\HotProduct\HotProductDbConfigService.cs" />
    <Compile Include="Services\HotProduct\HotProductService.cs" />
    <Compile Include="Services\HotProduct\ProductSaleStatisticsService.cs" />
    <Compile Include="Services\HotProduct\ProductService.cs" />
    <Compile Include="Services\HotProduct\ProductSkuSendDetailService.cs" />
    <Compile Include="Services\HotProduct\ProductSkuService.cs" />
    <Compile Include="Services\MessageQueue\OnlineSendMessageHandleService.cs" />
    <Compile Include="Services\MessageQueue\OnlineSendMessageService.cs" />
    <Compile Include="Services\DataEventTrackingLog\TraceBatchLogService.cs" />
    <Compile Include="Services\DataEventTrackingLog\MessageDataSyncStorageLogService.cs" />
    <Compile Include="Services\DataEventTrackingLog\TraceDataLogService.cs" />
    <Compile Include="Services\DataEventTracking\TraceBatchTrackingService.cs" />
    <Compile Include="Services\DataEventTracking\TraceDataTrackingService.cs" />
    <Compile Include="Services\DataMigrate\ColdDataMigrateService.cs" />
    <Compile Include="Services\DataMigrate\StatRelationSupplierService.cs" />
    <Compile Include="Services\CommonSettingRecordService.cs" />
    <Compile Include="Services\DistributorProductSkuMappingRecordService.cs" />
    <Compile Include="Services\DuplicationCompensate\LogicOrderDuplicationCompensateService.cs" />
    <Compile Include="Services\DataEventTrackingLog\BusinessLogService.cs" />
    <Compile Include="Services\DataEventTracking\BusinessLogDataEventTrackingService.cs" />
    <Compile Include="Services\DuplicationCompensate\WaybillCodeDuplicationCompensateService.cs" />
    <Compile Include="Services\DuplicationCompensate\SendHistoryDuplicationCompensateService.cs" />
    <Compile Include="Services\DuplicationCompensate\ProductDuplicationCompensateService.cs" />
    <Compile Include="Services\AlibabaFxOrderService.cs" />
    <Compile Include="Services\CrossCloud\SyncBusinessDataService.cs" />
    <Compile Include="Services\DataDuplication\PurchaseOrderRelationDuplicationService.cs" />
    <Compile Include="Services\DataEventTrackingLog\DefaultSettlementPriceHistoryLogService.cs" />
    <Compile Include="Services\DistributorProductService.cs" />
    <Compile Include="Services\ImagesRelationService.cs" />
    <Compile Include="Services\MessageQueue\SiteMessageService.cs" />
    <Compile Include="Services\MessageQueue\OnlineSendRequestIdService.cs" />
    <Compile Include="Services\OrderSelfDeliveryService.cs" />
    <Compile Include="Services\PlatformCategorySupplierService.cs" />
    <Compile Include="Services\PlatformCategoryService.cs" />
    <Compile Include="Services\OpenPlatform\OpenApiTokenService.cs" />
    <Compile Include="Services\OpenPlatform\OpenPlatformAppPermissionService.cs" />
    <Compile Include="Services\OpenPlatform\OpenPlatformAppService.cs" />
    <Compile Include="Services\OpenPlatform\OpenPlatformOrderService.cs" />
    <Compile Include="Services\OpenPlatform\OpenPlatformProductService.cs" />
    <Compile Include="Services\OpenPlatform\OpenPlatformStockService.cs" />
    <Compile Include="Services\OpenPlatform\OpenPlatformUserService.cs" />
    <Compile Include="Services\OpenPlatform\OutOrderService.cs" />
    <Compile Include="Services\ProfitStatistics\ProfitLogicOrderService.cs" />
    <Compile Include="Services\ProfitStatistics\ProfitLogicOrderItemService.cs" />
    <Compile Include="Services\ProfitStatistics\ProfitOrderItemService.cs" />
    <Compile Include="Services\ProfitStatistics\ProfitOrderItemPriceService.cs" />
    <Compile Include="Services\ProfitStatistics\ProfitOrderService.cs" />
    <Compile Include="Services\ProfitStatistics\ProfitOrderSyncTaskService.cs" />
    <Compile Include="Services\ProfitStatistics\ProfitStatisticsCloudMessageService.cs" />
    <Compile Include="Services\ProfitStatistics\ProfitStatisticsExportService.cs" />
    <Compile Include="Services\ProfitStatistics\ProfitStatisticsMsgProcessService.cs" />
    <Compile Include="Services\SettlementModule\SettlementBillQueryService.cs" />
    <Compile Include="Services\SettlementModule\TaskParamJsonService.cs" />
    <Compile Include="Services\ILockService.cs" />
    <Compile Include="Services\ShopLockService.cs" />
    <Compile Include="Services\SellerRemarkHistoryService.cs" />
    <Compile Include="Services\ShippingFeeTemplateService.cs" />
    <Compile Include="Services\DbConfigService.cs" />
    <Compile Include="Services\MessageQueue\PurchaseOrderStatusHandleMessageService.cs" />
    <Compile Include="Services\DeliveryModeChangeRecordService.cs" />
    <Compile Include="Services\OrderModule\ColdLogicOrderService.cs" />
    <Compile Include="Services\OrderModule\ColdOrderService.cs" />
    <Compile Include="Services\OrderModule\DefaultColdLogicOrderService.cs" />
    <Compile Include="Services\OrderModule\DefaultColdOrderService.cs" />
    <Compile Include="Services\OrderItemStatusService.cs" />
    <Compile Include="Services\OrderStatusService.cs" />
    <Compile Include="Services\PurchaseOrderDeliveryModeService.cs" />
    <Compile Include="Services\PrepayStatusChangeRecordService.cs" />
    <Compile Include="Services\PaymentStatementService.cs" />
    <Compile Include="Services\PurchaseOrderSendService.cs" />
    <Compile Include="Services\QingService.cs" />
    <Compile Include="Services\DistributorProductMappingService.cs" />
    <Compile Include="Services\DistributorProductSkuMappingService.cs" />
    <Compile Include="Services\FxAlibabaBuyerShopRelationService.cs" />
    <Compile Include="Services\LogicOrderPathFlowRecordService.cs" />
    <Compile Include="Services\ClearData\ClearDataChangeLogService.cs" />
    <Compile Include="Services\DataEventTrackingLog\UserAccountLogService.cs" />
    <Compile Include="Services\DataEventTrackingLog\DataDuplicationStatusHistoryLogService.cs" />
    <Compile Include="Services\DataEventTrackingLog\DataChangeRecordLogService.cs" />
    <Compile Include="Services\DataMigrateSameCloud\AfterSaleOrderDataMigrateService.cs" />
    <Compile Include="Services\DataMigrateSameCloud\BaseDataMigrateService.cs" />
    <Compile Include="Services\DataMigrateSameCloud\DataMigrateFactoryService.cs" />
    <Compile Include="Services\DataMigrateSameCloud\DataMigrateService.cs" />
    <Compile Include="Services\DataMigrateSameCloud\FxDataMigrateSubQueryService.cs" />
    <Compile Include="Services\DataMigrateSameCloud\FxDataMigrateSubTaskService.cs" />
    <Compile Include="Services\DataMigrateSameCloud\FxDataMigrateTaskService.cs" />
    <Compile Include="Services\DataMigrateSameCloud\LogicOrderDataMigrateService.cs" />
    <Compile Include="Services\DataMigrateSameCloud\LogicOrderItemDataMigrateService.cs" />
    <Compile Include="Services\DataMigrateSameCloud\OrderCheckRuleDataMigrateService.cs" />
    <Compile Include="Services\DataMigrateSameCloud\PathFlowDataMigrateService.cs" />
    <Compile Include="Services\DataMigrateSameCloud\ProductDataMigrateService.cs" />
    <Compile Include="Services\DataMigrateSameCloud\SendHistoryDataMigrateService.cs" />
    <Compile Include="Services\DataMigrateSameCloud\WaybillCodeDataMigrateService.cs" />
    <Compile Include="Services\DataMigrateForRepair\ClearMigrateDataService.cs" />
    <Compile Include="Services\CrossCloud\SyncCommonSettingsService.cs" />
    <Compile Include="Services\DataDuplication\PushMessageService.cs" />
    <Compile Include="Services\Compensate\CompensateBatchCollectionService.cs" />
    <Compile Include="Services\DataEventTrackingLog\LogicOrderInsertLogService.cs" />
    <Compile Include="Services\DataEventTrackingLog\InvokeApiLogService.cs" />
    <Compile Include="Services\DataEventTracking\DefaultDataEventTrackingService.cs" />
    <Compile Include="Services\DataEventTracking\LogicOrderInsertTrackingService.cs" />
    <Compile Include="Services\DataEventTracking\InvokeApiDataTrackingService.cs" />
    <Compile Include="Services\DataMigrateForRepair\BaseDataMigrateService.cs" />
    <Compile Include="Services\DataMigrateForRepair\DataMigrateFactoryService.cs" />
    <Compile Include="Services\DataMigrateForRepair\DataMigrateService.cs" />
    <Compile Include="Services\DataMigrateForRepair\LogicOrderDataMigrateService.cs" />
    <Compile Include="Services\DataMigrateForRepair\ProductDataMigrateService.cs" />
    <Compile Include="Services\DataMigrateForRepair\SendHistoryDataMigrateService.cs" />
    <Compile Include="Services\DataMigrateForRepair\WaybillCodeDataMigrateService.cs" />
    <Compile Include="Services\DataMigrate\FxDataMigrateSubQueryService.cs" />
    <Compile Include="Services\DataMigrate\LogicOrderItemDataMigrateService.cs" />
    <Compile Include="Services\DataMigrate\PrintHistoryMigratePathFlowService.cs" />
    <Compile Include="Services\DataMigrate\StatPathFlowService.cs" />
    <Compile Include="Services\DataMigrate\StatOrderService.cs" />
    <Compile Include="Services\DataMigrate\FxMigrateLockService.cs" />
    <Compile Include="Services\DataMigrate\TempFxDbConfigService.cs" />
    <Compile Include="Services\Compensate\CompensateBatchStorageService.cs" />
    <Compile Include="Services\Compensate\InvokeApiDataCollectionService.cs" />
    <Compile Include="Services\Compensate\InvokeApiDataStorageService.cs" />
    <Compile Include="Services\Compensate\SendHistoryCompensateService.cs" />
    <Compile Include="Services\Compensate\SendHistoryCompensateTaskService.cs" />
    <Compile Include="Services\DataEventTracking\AlibabaDataEventTrackingService.cs" />
    <Compile Include="Services\DataEventTracking\BaseDataEventTrackingService.cs" />
    <Compile Include="Services\DataEventTracking\DataEventTrackingFactoryService.cs" />
    <Compile Include="Services\ContrastTask\ContrastTaskItemService.cs" />
    <Compile Include="Services\ContrastTask\ContrastTaskExeService.cs" />
    <Compile Include="Services\ContrastTask\ContrastTaskService.cs" />
    <Compile Include="Services\DataEventTracking\JingDongDataEventTrackingService.cs" />
    <Compile Include="Services\DataEventTracking\PinDuoDuoDataEventTrackingService.cs" />
    <Compile Include="Services\Base\BaseDataDuplicationSyncService.cs" />
    <Compile Include="Services\Base\BaseDataMigrateTaskService.cs" />
    <Compile Include="Services\DataMigrate\AfterSaleOrderDataMigrateService.cs" />
    <Compile Include="Services\DataMigrate\BaseDataMigrateService.cs" />
    <Compile Include="Services\DataMigrate\DataMigrateFactoryService.cs" />
    <Compile Include="Services\DataMigrate\DataMigrateService.cs" />
    <Compile Include="Services\DataMigrate\FxDataMigrateSubTaskService.cs" />
    <Compile Include="Services\DataMigrate\FxDataMigrateTaskService.cs" />
    <Compile Include="Services\DataMigrate\LogicOrderDataMigrateService.cs" />
    <Compile Include="Services\DataMigrate\OrderCheckRuleDataMigrateService.cs" />
    <Compile Include="Services\DataMigrate\OrderDataMigrateService.cs" />
    <Compile Include="Services\DataMigrate\PathFlowDataMigrateService.cs" />
    <Compile Include="Services\DataMigrate\CommonSettingDataMigrateService.cs" />
    <Compile Include="Services\DataMigrate\ProductDataMigrateService.cs" />
    <Compile Include="Services\DataMigrate\ProductSettlementPriceDataMigrateService.cs" />
    <Compile Include="Services\DataMigrate\PrintHistoryDataMigrateService.cs" />
    <Compile Include="Services\DataMigrate\SendHistoryDataMigrateService.cs" />
    <Compile Include="Services\DataMigrate\SettlementProductSkuDataMigrateService.cs" />
    <Compile Include="Services\DataMigrate\WaybillCodeDataMigrateService.cs" />
    <Compile Include="Services\DuplicationCompensate\BaseDuplicationCompensateService.cs" />
    <Compile Include="Services\DuplicationCompensate\DuplicationCompensateFactoryService.cs" />
    <Compile Include="Services\DuplicationCompensate\DuplicationCompensateService.cs" />
    <Compile Include="Services\DuplicationCompensate\SettlementProductSkuDuplicationCompensateService.cs" />
    <Compile Include="Services\DuplicationCompensate\ProductSettlementPriceDuplicationCompensateService.cs" />
    <Compile Include="Services\FxDbConfigService.cs" />
    <Compile Include="Services\FxWeChatUserService.cs" />
    <Compile Include="Services\LogicOrderExtService.cs" />
    <Compile Include="Services\LoginLog\LoginLogService.cs" />
    <Compile Include="Services\OrderDeliverExceptionService.cs" />
    <Compile Include="Services\OrderExtraService.cs" />
    <Compile Include="Services\OrderModule\OrderAbnormalMessageService.cs" />
    <Compile Include="Services\OrderModule\OrderAbnormalPathFlowNodeService.cs" />
    <Compile Include="Services\OrderModule\OrderAbnormalService.cs" />
    <Compile Include="Services\PhShopService.cs" />
    <Compile Include="Services\PlatformAreaCodeInfoService.cs" />
    <Compile Include="Services\ProductSettlementPriceService.cs" />
    <Compile Include="Services\ProductSettlementRecordService.cs" />
    <Compile Include="Services\PurchaseOrderItemRelationService.cs" />
    <Compile Include="Services\QingService.cs" />
    <Compile Include="Services\PurchaseOrderModifyPriceRecordService.cs" />
    <Compile Include="Services\PurchaseOrderRelationService.cs" />
    <Compile Include="Services\ReceDbConfigService.cs" />
    <Compile Include="Services\InitFxDbConfigService.cs" />
    <Compile Include="Services\SendHistoryChildService.cs" />
    <Compile Include="Services\SendHistoryOrderService.cs" />
    <Compile Include="Services\SendHistoryReturnRecordService.cs" />
    <Compile Include="Services\SendHistoryReturn\SendHistoryReturnAuthConsumerService.cs" />
    <Compile Include="Services\SettingsService\BusinessSettingsService.cs" />
    <Compile Include="Services\SettlementInfoService.cs" />
    <Compile Include="Services\DataDuplication\AfterSaleOrderDuplicationService.cs" />
    <Compile Include="Services\DataDuplication\BaseDuplicationService.cs" />
    <Compile Include="Services\DataDuplication\DuplicationFactoryService.cs" />
    <Compile Include="Services\DataDuplication\LogicOrderDuplicationService.cs" />
    <Compile Include="Services\DataDuplication\OrderCheckDuplicationService.cs" />
    <Compile Include="Services\DataDuplication\OrderItemDuplicationService.cs" />
    <Compile Include="Services\DataDuplication\PathFlowDuplicationService.cs" />
    <Compile Include="Services\DataDuplication\PathFlowReferenceDuplicationService.cs" />
    <Compile Include="Services\DataDuplication\ProductDuplicationService.cs" />
    <Compile Include="Services\DataSyncStatus\DataSyncStatusService.cs" />
    <Compile Include="Services\EncryptionService.cs" />
    <Compile Include="Services\ExpressReachShareService.cs" />
    <Compile Include="Services\FenDanSystemNavService.cs" />
    <Compile Include="Services\BranchShareRelationOtherInfoService.cs" />
    <Compile Include="Services\FxUnBindTaskService.cs" />
    <Compile Include="Services\IAppService.cs" />
    <Compile Include="Services\LogicOrderItemService.cs" />
    <Compile Include="Services\ManualOrder\OrderCheckRuleService.cs" />
    <Compile Include="Services\ManualOrder\OrderCheckService.cs" />
    <Compile Include="Services\ProductNodeRelationService.cs" />
    <Compile Include="Services\ProductSkuFxService.cs" />
    <Compile Include="Services\QuickSearchService.cs" />
    <Compile Include="Services\ShopAuthHistoryService.cs" />
    <Compile Include="Services\Stat\ServiceAppOrderSettlementDetailService.cs" />
    <Compile Include="Services\Stat\StatSkuSettlementService.cs" />
    <Compile Include="Services\Stat\StatCategoryService.cs" />
    <Compile Include="Services\Stat\StatOrderV2Service.cs" />
    <Compile Include="Services\Stat\StatSendHistoryService.cs" />
    <Compile Include="Services\Stat\Stat1688V2Service.cs" />
    <Compile Include="Services\Stat\Stat1688Service.cs" />
    <Compile Include="Services\SubAccount\SysPermissionFxService.cs" />
    <Compile Include="Services\SupplierProduct\ListingTaskBusinessAbnormalService.cs" />
    <Compile Include="Services\SupplierProduct\ListingTaskCompensateService.cs" />
    <Compile Include="Services\SupplierProduct\SharePathFlowNodeService.cs" />
    <Compile Include="Services\SupplierProduct\ListingTaskRecordsService.cs" />
    <Compile Include="Services\SupplierProduct\SharePathFlowService.cs" />
    <Compile Include="Services\SupplierProduct\SupplierAddressService.cs" />
    <Compile Include="Services\SupplierProduct\SupplierProductBaseService.cs" />
    <Compile Include="Services\SupplierProduct\SupplierProductService.cs" />
    <Compile Include="Services\SupplierProduct\SupplierProductSkuService.cs" />
    <Compile Include="Services\SupplierProduct\UserSupplierStatusService.cs" />
    <Compile Include="Services\SyncDataInterface\SDIOrderService.cs" />
    <Compile Include="Services\MessageProcessLogService.cs" />
    <Compile Include="Services\SyncService\SyncMessageDeclarationService.cs" />
    <Compile Include="Services\SyncDataInterface\SDIDistributorProductMappingService.cs" />
    <Compile Include="Services\SyncDataInterface\SDISendHistoryReturnRecordService.cs" />
    <Compile Include="Services\SyncService\SyncMessageHandleService.cs" />
    <Compile Include="Services\TemplatePackageInfoService.cs" />
    <Compile Include="Services\CrossBorder\TkPrintProcessStateRecordService.cs" />
    <Compile Include="Services\CrossBorder\TkOrderPrintService.cs" />
    <Compile Include="Services\TkShipmentsInfoTranslateRecordService.cs" />
    <Compile Include="Services\Tools\DbIdentityIdMonitorService.cs" />
    <Compile Include="Services\Tools\DbTableMetaDataMySqlService.cs" />
    <Compile Include="Services\Tools\DbTableMetaDataService.cs" />
    <Compile Include="Services\Tools\InitializeLogicOrderItemFieldService.cs" />
    <Compile Include="Services\Tools\OrderSyncAnalysisAutoReasonAnalysisService.cs" />
    <Compile Include="Services\Tools\OrderSyncAnalysisAutoReasonMessageHandleService.cs" />
    <Compile Include="Services\Tools\OrderSyncAnalysisAutoReasonService.cs" />
    <Compile Include="Services\Tools\OrderSyncAnalysisMessageHandleService.cs" />
    <Compile Include="Services\Tools\OrderSyncAnalysisRealTimeService.cs" />
    <Compile Include="Services\Tools\OrderSyncAnalysisRecordStatusService.cs" />
    <Compile Include="Services\Tools\OrderSyncAnalysisService.cs" />
    <Compile Include="Services\Tools\OrderSyncAnalysisTriggerSyncMessageHandleService.cs" />
    <Compile Include="Services\Tools\OrderSyncAnalysisWeiXinTriggerSyncMessageHandleService.cs" />
    <Compile Include="Services\Tools\PurchaseOrderRelationCompensateMigrateToolService.cs" />
    <Compile Include="Services\Tools\InitOrderStatusService.cs" />
    <Compile Include="Services\Tools\SendHistoryAnalysisHandleService.cs" />
    <Compile Include="Services\Tools\SendHistoryAnalysisService.cs" />
    <Compile Include="Services\Tools\OrderCheckRuleCompensateService.cs" />
    <Compile Include="Services\Tools\OrderLifeCycleDetectionService.cs" />
    <Compile Include="Services\Tools\OrderLifeCycleToolReasonService.cs" />
    <Compile Include="Services\Tools\ServiceQueueMonitorToolService.cs" />
    <Compile Include="Services\Tools\TouTiaoPushDbService.cs" />
    <Compile Include="Services\UniversalModule\DataSyncStorageDeclarationService.cs" />
    <Compile Include="Services\UniversalModule\DataSyncStorageHandleService.cs" />
    <Compile Include="Services\UniversalModule\DataSyncStorageHealthTraceService.cs" />
    <Compile Include="Services\UniversalModule\DataSyncStorageMessageService.cs" />
    <Compile Include="Services\UniversalModule\FxMessageDataSyncStatusService.cs" />
    <Compile Include="Services\UpdateUniqueKeyService.cs" />
    <Compile Include="Services\RepairOrUpdateService.cs" />
    <Compile Include="Services\OrderPromiseService.cs" />
    <Compile Include="Services\EBillAccountExtensionService.cs" />
    <Compile Include="Services\OrderItemExtService.cs" />
    <Compile Include="Services\OrderTagService.cs" />
    <Compile Include="Services\ReceiverService.cs" />
    <Compile Include="Services\SendFailService.cs" />
    <Compile Include="Services\AsyncTaskService.cs" />
    <Compile Include="Services\AfterSaleEvidenceService.cs" />
    <Compile Include="Services\AfterSaleOrderItemService.cs" />
    <Compile Include="Services\Authrity\ActionAuthrityService.cs" />
    <Compile Include="Services\Authrity\AuthMessage.cs" />
    <Compile Include="Services\Authrity\AuthrityBLLService.cs" />
    <Compile Include="Services\Authrity\AuthrityFactory.cs" />
    <Compile Include="Services\Authrity\AuthrityService.cs" />
    <Compile Include="Services\Authrity\PageAuthrityService.cs" />
    <Compile Include="Services\FxServiceVersion\LimitedFunctionsService.cs" />
    <Compile Include="Services\FxServiceVersion\ServiceFunctionsService.cs" />
    <Compile Include="Services\FxServiceVersion\ServiceVersionService.cs" />
    <Compile Include="Services\FxServiceVersion\UserOrderBalanceUsedExceptionRecordService.cs" />
    <Compile Include="Services\FxServiceVersion\UserOrderCountBalanceService.cs" />
    <Compile Include="Services\FxServiceVersion\IUserOrderCount.cs" />
    <Compile Include="Services\FxServiceVersion\UserOrderLockRecordService.cs" />
    <Compile Include="Services\FxServiceVersion\UserOrderSendCountService.cs" />
    <Compile Include="Services\FxServiceVersion\UserOrderUsedRecordService.cs" />
    <Compile Include="Services\FxServiceVersion\UserServiceVersionMappingService.cs" />
    <Compile Include="Services\FxServiceVersion\UserSpecialFunctionMappingService.cs" />
    <Compile Include="Services\FxUnbindService.cs" />
    <Compile Include="Services\FxStatisticService.cs" />
    <Compile Include="Services\FxBuildExccelService.cs" />
    <Compile Include="Services\BuildExccelWithTempXMLService.cs" />
    <Compile Include="Services\CommonSettingExtensionService.cs" />
    <Compile Include="Services\FinancialSettlementService.cs" />
    <Compile Include="Services\FxPlatformEncryptService.cs" />
    <Compile Include="Services\ImportBatchHistoryService.cs" />
    <Compile Include="Services\ImportOrderBatchService.cs" />
    <Compile Include="Services\KuaiShouEncryptedReceiverInfoService.cs" />
    <Compile Include="Services\AfterSaleOrderService.cs" />
    <Compile Include="Services\ManualOrder\AssignLogicOrderFactory.cs" />
    <Compile Include="Services\ManualOrder\CheckOfflineFactory.cs" />
    <Compile Include="Services\ManualOrder\CheckOrderBase.cs" />
    <Compile Include="Services\ManualOrder\CheckOrderFactory.cs" />
    <Compile Include="Services\ManualOrder\CheckRuleService.cs" />
    <Compile Include="Services\ManualOrder\IAssignLogicOrder.cs" />
    <Compile Include="Services\ManualOrder\IObserveCheckOrder.cs" />
    <Compile Include="Services\ManualOrder\ISplitLogicOrder.cs" />
    <Compile Include="Services\ManualOrder\ManualAssignLogicOrder.cs" />
    <Compile Include="Services\ManualOrder\OfflineCheckOrderService.cs" />
    <Compile Include="Services\ManualOrder\PlatfomCheckOrderService.cs" />
    <Compile Include="Services\ManualOrder\BinarySplitLogicOrderService.cs" />
    <Compile Include="Services\ManualOrder\PrintSplitLogicOrderService.cs" />
    <Compile Include="Services\ManualOrder\SplistLogicOrderFactory.cs" />
    <Compile Include="Services\ManualOrder\TemplateCheckOrder.cs" />
    <Compile Include="Services\OrderSplitRecordService.cs" />
    <Compile Include="Services\PathFlowNodeService.cs" />
    <Compile Include="Services\PathFlowChangeLogService.cs" />
    <Compile Include="Services\PathFlowReferenceConfigService.cs" />
    <Compile Include="Services\PathFlowReferenceService.cs" />
    <Compile Include="Services\PathFlowService.cs" />
    <Compile Include="Services\FxUserAddressService.cs" />
    <Compile Include="Services\AsyncDeliveryService.cs" />
    <Compile Include="Services\PddLogisticsRecommendService.cs" />
    <Compile Include="Services\ProductFxServiceExtension.cs" />
    <Compile Include="Services\ProductSkuInfoFxService.cs" />
    <Compile Include="Services\ProductInfoFxService.cs" />
    <Compile Include="Services\ReceiverMaskDataService.cs" />
    <Compile Include="Services\SettlementBillService.cs" />
    <Compile Include="Services\SettlementProductSkuService.cs" />
    <Compile Include="Services\SupplierUserService.cs" />
    <Compile Include="Services\OrderFxService.cs" />
    <Compile Include="Services\LogicOrderService.cs" />
    <Compile Include="Services\ProductFxService.cs" />
    <Compile Include="Services\ShopExtensionService.cs" />
    <Compile Include="Services\FxUserShopService.cs" />
    <Compile Include="Services\SyncDataInterface\BaseSyncDataInterfaceService.cs" />
    <Compile Include="Services\SyncDataInterface\SDIWaybillCodeService.cs" />
    <Compile Include="Services\SyncDataInterface\SDIPathFlowService.cs" />
    <Compile Include="Services\SyncDataInterface\SDILogicOrderService.cs" />
    <Compile Include="Services\SyncDataInterface\SDIProductFxService.cs" />
    <Compile Include="Services\SyncDataInterface\SDIAfterSaleOrderService.cs" />
    <Compile Include="Services\SyncDataInterface\SyncDataInterfaceService.cs" />
    <Compile Include="Services\SyncQueryTaskService.cs" />
    <Compile Include="MongoDBService\LogForPlatformTypeDetailService.cs" />
    <Compile Include="MongoDBService\LogForStatisticByPlatformTypeService.cs" />
    <Compile Include="Services\JDAreaInfoService.cs" />
    <Compile Include="Services\ExportTaskService.cs" />
    <Compile Include="Services\BuildExccelService.cs" />
    <Compile Include="Services\PlatformRemarkService.cs" />
    <Compile Include="Services\ShareWaybillRemedyDataService.cs" />
    <Compile Include="Services\SubUserService.cs" />
    <Compile Include="Services\BranchShareExportExcelTaskService.cs" />
    <Compile Include="Services\BranchShareUsedStatisticService.cs" />
    <Compile Include="Services\ExpressImportMappingService.cs" />
    <Compile Include="Services\BranchShareRelationLogService.cs" />
    <Compile Include="Services\BranchShareRelationService.cs" />
    <Compile Include="Services\LinkAreaService.cs" />
    <Compile Include="Services\OpenPlatformAppService.cs" />
    <Compile Include="Services\NaHuoLabelService.cs" />
    <Compile Include="Services\ShareWaybillAccountCheckingRecordService.cs" />
    <Compile Include="Services\ShareBranchContactInfoService.cs" />
    <Compile Include="Services\ShareWaybillCodeRecordService.cs" />
    <Compile Include="Services\SyncStatusService.cs" />
    <Compile Include="Services\SyncTaskService.cs" />
    <Compile Include="Services\TouTiaoOrderService.cs" />
    <Compile Include="Services\UserActionRecordService.cs" />
    <Compile Include="Services\SubUserFxService.cs" />
    <Compile Include="Services\SubAccount\UserFxPostRelationService.cs" />
    <Compile Include="Services\UserFxService .cs" />
    <Compile Include="Services\FxWeChatQRCodeService.cs" />
    <Compile Include="Services\UserShopInfo\ChangeBindShopService.cs" />
    <Compile Include="Services\WaybillRealCpCodeService.cs" />
    <Compile Include="Services\WebCommon\WebAjaxResult.cs" />
    <Compile Include="Services\WebCommon\WebCommon.cs" />
    <Compile Include="Services\WxUserFxRelationService.cs" />
    <Compile Include="Services\WareHouseService.cs" />
    <Compile Include="Services\CommonQRCodeService.cs" />
    <Compile Include="PlatformService\WeiXin\WxVideoApiClient.cs" />
    <Compile Include="PlatformService\WeiXin\WxVideoPlatformService.cs" />
    <Compile Include="Services\WaybillCodeChildService.cs" />
    <Compile Include="Services\WaybillCodeOrderProductService.cs" />
    <Compile Include="Services\WaybillCodeUseRecordService.cs" />
    <Compile Include="Services\WxVisitingCardQRCodeService.cs" />
    <Compile Include="ShardRoute\DynamicLogFactory.cs" />
    <Compile Include="ShardRoute\DynamicPerformanceLogProxy.cs" />
    <Compile Include="SiteContextHandler.cs" />
    <Compile Include="SiteService\WebSiteService.cs" />
    <Compile Include="StorageService\AliyunOSSStorage.cs" />
    <Compile Include="StorageService\TouTiaoTosStorage.cs" />
    <Compile Include="StorageService\PddOSSStorage.cs" />
    <Compile Include="TokenBucket\Bucket.cs" />
    <Compile Include="TokenBucket\IBucket.cs" />
    <Compile Include="TokenBucket\IBucketManager.cs" />
    <Compile Include="TokenBucket\IMutex.cs" />
    <Compile Include="TokenBucket\RedisHelp.cs" />
    <Compile Include="TokenBucket\RedisMutex.cs" />
    <Compile Include="TokenBucket\RedisSingleBucket.cs" />
    <Compile Include="TokenBucket\RedisTwoBucket.cs" />
    <Compile Include="TokenBucket\TwoBucket.cs" />
    <Compile Include="TransferConfig.cs" />
    <Compile Include="LogForOperatorContext.cs" />
    <Compile Include="LogisticService\LinkLogisticService.cs" />
    <Compile Include="LogisticService\JdwjLogisticService.cs" />
    <Compile Include="LogisticService\LogisticCenterApiService.cs" />
    <Compile Include="LogisticService\PddLogisticService.cs" />
    <Compile Include="LogisticService\CaiNiaoLogisticService.cs" />
    <Compile Include="LogisticService\FakeLogisticService.cs" />
    <Compile Include="LogisticService\ILogisticService.cs" />
    <Compile Include="LogisticService\HTKYLogisticService.cs" />
    <Compile Include="LogisticService\ANELogisticService.cs" />
    <Compile Include="LogisticService\KuaiDiNiaoLogisticService.cs" />
    <Compile Include="LogisticService\PJLogisticService.cs" />
    <Compile Include="LogisticService\JDLogisticService.cs" />
    <Compile Include="LogisticService\FengQiaoLogisticService.cs" />
    <Compile Include="LogisticService\SFLogisticService.cs" />
    <Compile Include="PlatformService\AlibabaApiClient.cs" />
    <Compile Include="PlatformService\OfflinePlatformService.cs" />
    <Compile Include="PlatformService\JingDongApiClient.cs" />
    <Compile Include="PlatformService\JingDongPlatformService.cs" />
    <Compile Include="PlatformService\VipShopApiClient.cs" />
    <Compile Include="PlatformService\VipShopPlatformService.cs" />
    <Compile Include="PlatformService\WeiDianApiClient.cs" />
    <Compile Include="PlatformService\WeiMengApiClient.cs" />
    <Compile Include="PlatformService\WeiDianPlatformService.cs" />
    <Compile Include="PlatformService\SuningApiClient.cs" />
    <Compile Include="PlatformService\YouZanApiClient.cs" />
    <Compile Include="PlatformService\MoGuJieApiClient.cs" />
    <Compile Include="PlatformService\WeiMengPlatformService.cs" />
    <Compile Include="PlatformService\SuningPlatformService.cs" />
    <Compile Include="PlatformService\YouZanPlatformService.cs" />
    <Compile Include="PlatformService\XiaoDianApiClient.cs" />
    <Compile Include="PlatformService\MoGuJiePlatformService.cs" />
    <Compile Include="PlatformService\XiaoDianPlatformService.cs" />
    <Compile Include="PlatformService\PinduoduoApiClient.cs" />
    <Compile Include="PlatformService\PinduoduoPlatformService.cs" />
    <Compile Include="PlatformService\AlibabaPlatformService.cs" />
    <Compile Include="PlatformService\IPlatformService.cs" />
    <Compile Include="PlatformService\PlatformFactory.cs" />
    <Compile Include="PlatformService\SyncProductService.cs" />
    <Compile Include="PlatformService\SyncOrderService.cs" />
    <Compile Include="PlatformService\TaobaoApiClient.cs" />
    <Compile Include="PlatformService\TaobaoPlatformService.cs" />
    <Compile Include="ServicesExtension\MergerOrderService.cs" />
    <Compile Include="ServicesExtension\PolicyService.cs" />
    <Compile Include="Services\AdvService.cs" />
    <Compile Include="Services\SubAccount\PostFxService.cs" />
    <Compile Include="Services\CustomerVisitRecordService.cs" />
    <Compile Include="Services\GetOldSystemService.cs" />
    <Compile Include="Services\FengQiaoOrderDataService.cs" />
    <Compile Include="Services\LogisticsAppInfoService.cs" />
    <Compile Include="Services\LogisticTracesService.cs" />
    <Compile Include="Services\ModifyOrderPriceService.cs" />
    <Compile Include="Services\OrderLogisticInfoService.cs" />
    <Compile Include="Services\CustomerStaffService.cs" />
    <Compile Include="Services\WsXcxCommPrinterService.cs" />
    <Compile Include="Services\WxAppIdTokenService.cs" />
    <Compile Include="Services\WxBluetoothPrinterService.cs" />
    <Compile Include="Services\WxUserInfoService.cs" />
    <Compile Include="Services\WxQRcodeService.cs" />
    <Compile Include="Services\WxConcernService.cs" />
    <Compile Include="Services\AppOrderListService.cs" />
    <Compile Include="Services\EvaluateOrderService.cs" />
    <Compile Include="Services\LogisticAddServiceService.cs" />
    <Compile Include="Services\LogisticBusinessTypesService.cs" />
    <Compile Include="Services\CommentOrderService.cs" />
    <Compile Include="MongoDBService\LogForOperatorService.cs" />
    <Compile Include="Services\PrintTemplateExtendService.cs" />
    <Compile Include="Services\LogisticAccountInfoMappingsService.cs" />
    <Compile Include="Services\OrderSyncLogService.cs" />
    <Compile Include="Services\CaiNiaoAccountBranchService.cs" />
    <Compile Include="Services\MacAddressService.cs" />
    <Compile Include="Services\AreaCodeInfoService.cs" />
    <Compile Include="Services\CommonSettingService.cs" />
    <Compile Include="Services\CommService.cs" />
    <Compile Include="Services\BaseService.cs" />
    <Compile Include="Services\CaiNiaoAuthInfoService.cs" />
    <Compile Include="Services\CainiaoAuthOwnerService.cs" />
    <Compile Include="Services\CaiNiaoAuthRelationService.cs" />
    <Compile Include="Services\CustomColumnExcelService.cs" />
    <Compile Include="Services\CustomerColumnMappingService.cs" />
    <Compile Include="Services\ExpressCpCodeMappingService.cs" />
    <Compile Include="Services\ExpressCodeMappingService.cs" />
    <Compile Include="Services\ExpressCompanyService.cs" />
    <Compile Include="Services\LastCodeBuildService.cs" />
    <Compile Include="Services\NaHuoLabelTemplateService.cs" />
    <Compile Include="Services\NaHuoTemplateService.cs" />
    <Compile Include="Services\OpenSecrecySellerService.cs" />
    <Compile Include="Services\OrderCategoryService.cs" />
    <Compile Include="Services\LogisticPayTypesService.cs" />
    <Compile Include="Services\PurchaseConfigService.cs" />
    <Compile Include="Services\OrderFilterService.cs" />
    <Compile Include="Services\CustomerOrderItemService.cs" />
    <Compile Include="Services\OrderItemService.cs" />
    <Compile Include="Services\OrderModifiedService.cs" />
    <Compile Include="Services\CustomerOrderService.cs" />
    <Compile Include="Services\OrderService.cs" />
    <Compile Include="Services\PreordainService.cs" />
    <Compile Include="Services\PrintControlService.cs" />
    <Compile Include="Services\PrintHistoryService.cs" />
    <Compile Include="Services\PrintHistoryOrderService.cs" />
    <Compile Include="Services\PrintOrderProductService.cs" />
    <Compile Include="Services\PrintTemplateService.cs" />
    <Compile Include="Services\ProductService.cs" />
    <Compile Include="Services\ProductSkuAttributeService.cs" />
    <Compile Include="Services\ProductSkuService.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Services\ProductStatusService.cs" />
    <Compile Include="Services\PurchaseService.cs" />
    <Compile Include="Services\ReciverInfoService.cs" />
    <Compile Include="Services\ScanPrintService.cs" />
    <Compile Include="Services\ScanSendGoodsService.cs" />
    <Compile Include="Services\SellerInfoService.cs" />
    <Compile Include="Services\SendGoodTemplateService.cs" />
    <Compile Include="Services\SendHistoryService.cs" />
    <Compile Include="Services\SendOrderProductService.cs" />
    <Compile Include="Services\ShopRelationService.cs" />
    <Compile Include="Services\ShopService.cs" />
    <Compile Include="Services\KuaiDiNiaoLogisticTracesService.cs" />
    <Compile Include="Services\SkuAttributeService.cs" />
    <Compile Include="Services\StapleTemplateService.cs" />
    <Compile Include="Services\SynUserInfoService.cs" />
    <Compile Include="Services\SysConfigService.cs" />
    <Compile Include="Services\TemplateLogisticsSvsService.cs" />
    <Compile Include="Services\PrinterBindService.cs" />
    <Compile Include="Services\AgentService.cs" />
    <Compile Include="Services\UserInfoService.cs" />
    <Compile Include="Services\UserService.cs" />
    <Compile Include="Services\WaybillCodeCheckService.cs" />
    <Compile Include="Services\UserSiteInfoService.cs" />
    <Compile Include="Services\TemplateRelationAuthInfoService.cs" />
    <Compile Include="Services\WaybillCodeOrderService.cs" />
    <Compile Include="Services\WaybillCustomAreaService.cs" />
    <Compile Include="Services\WaybillCodeService.cs" />
    <Compile Include="Services\WxVisitingCardService.cs" />
    <Compile Include="Services\WxXiaDanRelationCardService.cs" />
    <Compile Include="SiteContext.cs" />
    <Compile Include="WaybillService\FengQiaoApiService.cs" />
    <Compile Include="WaybillService\JingDongApiService.cs" />
    <Compile Include="WaybillService\JTExpressApiService.cs" />
    <Compile Include="WaybillService\KsWaybillApiService.cs" />
    <Compile Include="WaybillService\TouTiaoWaybillApiService.cs" />
    <Compile Include="WaybillService\WuJieWaybillApiService.cs" />
    <Compile Include="WaybillService\PddWaybillApiService.cs" />
    <Compile Include="WaybillService\CloudCaiNiaoApiService.cs" />
    <Compile Include="WaybillService\TopCaiNiaoApiService.cs" />
    <Compile Include="WaybillService\WxVideoWaybillApiService.cs" />
    <Compile Include="WaybillService\XiaoHongShuWaybillApiService.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\AutoComment\DianGuanJiaApp.RabbitMQ\DianGuanJiaApp.RabbitMQ.csproj">
      <Project>{90dc55be-abba-4686-8119-a18e012f540e}</Project>
      <Name>DianGuanJiaApp.RabbitMQ</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Core\DianGuanJiaApp.Core.csproj">
      <Project>{93ca297d-8704-48d8-8748-67ef45304f88}</Project>
      <Name>DianGuanJiaApp.Core</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Data\DianGuanJiaApp.Data.csproj">
      <Project>{6c8b9657-fbaa-46c8-8433-1b2f908e8398}</Project>
      <Name>DianGuanJiaApp.Data</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.RabbitMqCore.Pool\DianGuanJiaApp.RabbitMqCore.Pool.csproj">
      <Project>{dd5c79ad-2a24-4553-95a6-36981eb6031f}</Project>
      <Name>DianGuanJiaApp.RabbitMqCore.Pool</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.SiteMessage\DianGuanJiaApp.SiteMessage.csproj">
      <Project>{776BDADB-85E8-4C5B-9B21-3C9235A36A79}</Project>
      <Name>DianGuanJiaApp.SiteMessage</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Trace.Services\DianGuanJiaApp.Trace.Services.csproj">
      <Project>{799efc5b-87d2-4fc1-a9d5-191fa59d0b02}</Project>
      <Name>DianGuanJiaApp.Trace.Services</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Trace.ViewModels\DianGuanJiaApp.Trace.ViewModels.csproj">
      <Project>{7EB493FB-B565-4C96-81F4-BCDF9393D1B8}</Project>
      <Name>DianGuanJiaApp.Trace.ViewModels</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Utility\DianGuanJiaApp.Utility.csproj">
      <Project>{a0ead1ef-8b94-4487-ad73-f981cb45e58f}</Project>
      <Name>DianGuanJiaApp.Utility</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.ViewModels\DianGuanJiaApp.ViewModels.csproj">
      <Project>{549A33CF-F7FB-49EF-B8BD-1AAE56317663}</Project>
      <Name>DianGuanJiaApp.ViewModels</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Warehouse\DianGuanJiaApp.Warehouse.csproj">
      <Project>{a5ad179b-04e3-44e4-a90c-6260199de89a}</Project>
      <Name>DianGuanJiaApp.Warehouse</Name>
    </ProjectReference>
    <ProjectReference Include="..\JdCloudSdk\JdCloudSdk.csproj">
      <Project>{7714246a-ffef-4af5-9cc2-8d125f7f5e9e}</Project>
      <Name>JdCloudSdk</Name>
    </ProjectReference>
    <ProjectReference Include="..\jos-net-open-api-sdk-2.0\jos-sdk-net.csproj">
      <Project>{cf7757d6-4f03-4bca-948d-d1e0b81b491c}</Project>
      <Name>jos-sdk-net</Name>
    </ProjectReference>
    <ProjectReference Include="..\kdapi\KDAPI.csproj">
      <Project>{8fcc5126-bda6-4141-983c-cfb7f3720fe3}</Project>
      <Name>KDAPI</Name>
    </ProjectReference>
    <ProjectReference Include="..\sdk-dotnet\sdk-dotnet.csproj">
      <Project>{ad728a85-85d9-4bc6-b7af-539d31ba0790}</Project>
      <Name>sdk-dotnet</Name>
    </ProjectReference>
    <ProjectReference Include="..\sf-fengqiao-sdk\sf-fengqiao-sdk.csproj">
      <Project>{24C1F4F0-D3DD-4BE1-BEBA-EFF47FEB7F3D}</Project>
      <Name>sf-fengqiao-sdk</Name>
    </ProjectReference>
    <ProjectReference Include="..\taobao-sdk-net-auto_1554290167233-20190926-source\TopSdk.csproj">
      <Project>{9c11cae5-5188-4e71-825b-68fc3135728a}</Project>
      <Name>TopSdk</Name>
    </ProjectReference>
    <ProjectReference Include="..\vipshop-sdk\vipshop-sdk.csproj">
      <Project>{2848621e-7b9f-4770-b444-e3913e1b0ddd}</Project>
      <Name>vipshop-sdk</Name>
    </ProjectReference>
    <ProjectReference Include="..\ych-sdk\ych-sdk.csproj">
      <Project>{aaf61c3f-5729-4e3c-bc8c-eea8067e0305}</Project>
      <Name>ych-sdk</Name>
    </ProjectReference>
    <ProjectReference Include="..\yz-sdk\yz-sdk.csproj">
      <Project>{2a0e43ad-8078-45b9-8c19-9f8115f2337f}</Project>
      <Name>yz-sdk</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="bin\Release\" />
    <Folder Include="PlatformService\Alibaba\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="bin\Debug\DianGuanJiaApp.Services.dll" />
    <Content Include="bin\Debug\DianGuanJiaApp.Services.pdb" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="..\packages\Microsoft.NETFramework.ReferenceAssemblies.net461.1.0.3\build\Microsoft.NETFramework.ReferenceAssemblies.net461.targets" Condition="Exists('..\packages\Microsoft.NETFramework.ReferenceAssemblies.net461.1.0.3\build\Microsoft.NETFramework.ReferenceAssemblies.net461.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>这台计算机上缺少此项目引用的 NuGet 程序包。使用“NuGet 程序包还原”可下载这些程序包。有关更多信息，请参见 http://go.microsoft.com/fwlink/?LinkID=322105。缺少的文件是 {0}。</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.NETFramework.ReferenceAssemblies.net461.1.0.3\build\Microsoft.NETFramework.ReferenceAssemblies.net461.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.NETFramework.ReferenceAssemblies.net461.1.0.3\build\Microsoft.NETFramework.ReferenceAssemblies.net461.targets'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>