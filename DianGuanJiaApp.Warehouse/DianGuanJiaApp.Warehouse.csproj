<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{A5AD179B-04E3-44E4-A90C-6260199DE89A}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>DianGuanJiaApp.Warehouse</RootNamespace>
    <AssemblyName>DianGuanJiaApp.Warehouse</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
    <Reference Include="Z.Dapper.Plus">
      <HintPath>..\packages\Build\Z.Dapper.Plus.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="EntityExtension\OrderStockIn.cs" />
    <Compile Include="EntityExtension\OrderStockOut.cs" />
    <Compile Include="EntityExtension\ProEntity\ProErrorMessage.cs" />
    <Compile Include="EntityExtension\ProEntity\TYPE_OrderStockOut_Item.cs" />
    <Compile Include="EntityExtension\ProEntity\TYPE_OrderStockOut_Order.cs" />
    <Compile Include="EntityExtension\ProEntity\TYPE_StockChangeDetailRecord.cs" />
    <Compile Include="EntityExtension\ProEntity\TYPE_StockIn_Item.cs" />
    <Compile Include="EntityExtension\SkuStockDetailList.cs" />
    <Compile Include="EntityExtension\StockIn.cs" />
    <Compile Include="EntityExtension\StockOrder.cs" />
    <Compile Include="EntityExtension\StockRecordList.cs" />
    <Compile Include="EntityExtension\WareHouse.cs" />
    <Compile Include="EntityExtension\WareHouseProduct.cs" />
    <Compile Include="EntityExtension\WareHouseSku.cs" />
    <Compile Include="EntityExtension\WareHouseSkuGetBodyCode.cs" />
    <Compile Include="EntityExtension\WareHouseProductSku.cs" />
    <Compile Include="Entity\CombinedProductChangeLog.cs" />
    <Compile Include="Entity\SkuStockDetail.cs" />
    <Compile Include="Entity\StockChangeDetailRecord.cs" />
    <Compile Include="Entity\StockChangeStat.cs" />
    <Compile Include="Entity\StockOrder.cs" />
    <Compile Include="Entity\TempProduct.cs" />
    <Compile Include="Entity\WareHouse.cs" />
    <Compile Include="Entity\WareHouseChildSkuFinal.cs" />
    <Compile Include="Entity\WareHouseChildSku.cs" />
    <Compile Include="Entity\WareHouseOwner.cs" />
    <Compile Include="Entity\WareHouseProduct.cs" />
    <Compile Include="Entity\WareHouseSku.cs" />
    <Compile Include="Entity\WareHouseSkuBindRelation.cs" />
    <Compile Include="Enum\StockChangeDetailRecordTypeEnum.cs" />
    <Compile Include="Enum\StockOrderStatusEnum.cs" />
    <Compile Include="Enum\StockOrderTypeEnum.cs" />
    <Compile Include="Enum\WareHouseErrorEnum.cs" />
    <Compile Include="Enum\WarehouseSkuStutasEnum.cs" />
    <Compile Include="Enum\SkuItemTypeEnum.cs" />
    <Compile Include="Enum\WarehouseStutasEnum.cs" />
    <Compile Include="Model\BaseWarehouseRespone.cs" />
    <Compile Include="Model\BaseWarehouseRequest.cs" />
    <Compile Include="Model\ParaModel\StatStockModel.cs" />
    <Compile Include="Model\ParaModel\ProductListModel.cs" />
    <Compile Include="Model\Request\ChangeLogLoadListRequest.cs" />
    <Compile Include="Model\Request\InventoryItemRequest.cs" />
    <Compile Include="Model\Request\InventoryOrderRequest.cs" />
    <Compile Include="Model\Request\InventoryStockInItemRequest.cs" />
    <Compile Include="Model\Request\InventoryStockOutItemRequest.cs" />
    <Compile Include="Model\Request\ProductCombinationSearchReqeust.cs" />
    <Compile Include="Model\Request\ProductInfoTempGetOneRequest.cs" />
    <Compile Include="Model\Request\ProductSkuCombinationAddRequest.cs" />
    <Compile Include="Model\Request\StockChangeDetailRequest.cs" />
    <Compile Include="Model\Request\ProductInfoTempRequest.cs" />
    <Compile Include="Model\Request\WarehouseDisableRequest.cs" />
    <Compile Include="Model\Request\WarehouseEnableRequest.cs" />
    <Compile Include="Model\Request\WarehouseEntryOrderConfirmRequest.cs" />
    <Compile Include="Model\Request\WarehouseCountRequest.cs" />
    <Compile Include="Model\Request\WarehouseGetRequest.cs" />
    <Compile Include="Model\Request\WarehouseOrderStockOutRecollbackRequest.cs" />
    <Compile Include="Model\Request\WarehouseOrderStockInRequest.cs" />
    <Compile Include="Model\Request\WarehouseProductMergerRequest.cs" />
    <Compile Include="Model\Request\WarehouseProductDeleteRequest.cs" />
    <Compile Include="Model\Request\WarehouseProductSkuContainChildGetRequest.cs" />
    <Compile Include="Model\Request\WarehouseProductSkuDeleteCheckRequest.cs" />
    <Compile Include="Model\Request\WarehouseProductUpdateStatusRequest.cs" />
    <Compile Include="Model\Request\WarehouseSkuDeleteRequest.cs" />
    <Compile Include="Model\Request\WarehouseSkuGetListRequest.cs" />
    <Compile Include="Model\Request\WarehouseSkuUpdateCostPriceRequest.cs" />
    <Compile Include="Model\Request\WarehouseSkuUpdatePreAlertCountRequest.cs" />
    <Compile Include="Model\Request\WarehouseSkuUpdatePreAlertCountByProductRequest.cs" />
    <Compile Include="Model\Request\WareHouseSkuBindRelationListRequest.cs" />
    <Compile Include="Model\Request\WarehouseListRequest.cs" />
    <Compile Include="Model\Request\WarehouseOrderReserverRequest.cs" />
    <Compile Include="Model\Request\WarehouseOrderReverserConfirmRequest.cs" />
    <Compile Include="Model\Request\WarehouseOrderStockOutRequest.cs" />
    <Compile Include="Model\Request\WarehouseProductListRequest.cs" />
    <Compile Include="Model\Request\WarehouseProductSkuListRequest.cs" />
    <Compile Include="Model\Request\WarehouseProductUpdateRequest.cs" />
    <Compile Include="Model\Request\WarehouseProductAddRequest.cs" />
    <Compile Include="Model\Request\WarehouseProductGetRequest.cs" />
    <Compile Include="Model\Request\WareHouseSkuBindRelationAddRequest.cs" />
    <Compile Include="Model\Request\WarehouseSkuGetOneRequest.cs" />
    <Compile Include="Model\Request\WarehouseSkuBindRequest.cs" />
    <Compile Include="Model\Request\WarehouseSkuUnbindRequest.cs" />
    <Compile Include="Model\Request\WarehouseEntryOrderCreateRequest.cs" />
    <Compile Include="Model\Request\WarehouseStockInRequest.cs" />
    <Compile Include="Model\Request\WarehouseStockListRequest.cs" />
    <Compile Include="Model\Request\WarehouseStockoutConfirmRequest.cs" />
    <Compile Include="Model\Request\WarehouseStockoutCreateRequest.cs" />
    <Compile Include="Model\Request\WarehouseStockOutRequest.cs" />
    <Compile Include="Model\Request\WarehouseStockChangeDetailRecordRequest .cs" />
    <Compile Include="Model\Request\WarehouseStockRecordListRequest.cs" />
    <Compile Include="Model\Request\WarehouseUpdateRequest.cs" />
    <Compile Include="Model\Request\WarehouseAddRequest.cs" />
    <Compile Include="Model\Request\WarehouseOwnerGetRequest.cs" />
    <Compile Include="Model\Request\WarehouseOwnerInitRequest.cs" />
    <Compile Include="Model\Response\ChangeLogLoadListResponse.cs" />
    <Compile Include="Model\Response\InventoryItemResponse.cs" />
    <Compile Include="Model\Response\InventoryOrderResponse.cs" />
    <Compile Include="Model\Response\InventoryReserverConfirmResponse.cs" />
    <Compile Include="Model\Response\InventoryStockOutItemResponse.cs" />
    <Compile Include="Model\Response\InventoryStockInItemResponse.cs" />
    <Compile Include="Model\Response\ProductCombinationSearchResponse.cs" />
    <Compile Include="Model\Response\ProductInfoTempGetOneResponse.cs" />
    <Compile Include="Model\Response\ProductSkuCombinationAddResponse.cs" />
    <Compile Include="Model\Response\SkuStockDetailResponse.cs" />
    <Compile Include="Model\Response\StockChangeDetailResponse.cs" />
    <Compile Include="Model\Response\StockRecordResponse.cs" />
    <Compile Include="Model\Response\ProductInfoTempResponse.cs" />
    <Compile Include="Model\Response\WarehouseDisableResponse.cs" />
    <Compile Include="Model\Response\WarehouseEnableResponse.cs" />
    <Compile Include="Model\Response\WarehouseEntryOrderConfirmResponse.cs" />
    <Compile Include="Model\Response\WarehouseEntryOrderCreateResponse.cs" />
    <Compile Include="Model\Response\WarehouseCountResponse.cs" />
    <Compile Include="Model\Response\WarehouseGetResponse.cs" />
    <Compile Include="Model\Response\WarehouseOrderStockInResponse.cs" />
    <Compile Include="Model\Response\WarehouseProductSkuContainChildResponse.cs" />
    <Compile Include="Model\Response\WarehouseProductMergerResponse.cs" />
    <Compile Include="Model\Response\WarehouseProductSkuDeleteCheckResponse.cs" />
    <Compile Include="Model\Response\WareHouseProductUpdateRespone.cs" />
    <Compile Include="Model\Response\WareHouseSkuDeleteResponse.cs" />
    <Compile Include="Model\Response\WareHouseProductDeleteResponse.cs" />
    <Compile Include="Model\Response\WarehouseSkuGetListResponse.cs" />
    <Compile Include="Model\Response\WarehouseSkuUpdateCostPriceResponse.cs" />
    <Compile Include="Model\Response\WarehouseSkuUpdatePreAlertCountResponse.cs" />
    <Compile Include="Model\Response\WarehouseSkuUpdatePreAlertCountByProductResponse.cs" />
    <Compile Include="Model\Response\WareHouseSkuBindRelationResponse.cs" />
    <Compile Include="Model\Response\WareHouseSkuBindRelationListResponse.cs" />
    <Compile Include="Model\Response\WarehouseListResponse.cs" />
    <Compile Include="Model\Response\WarehouseOrderReserverResponse.cs" />
    <Compile Include="Model\Response\WarehouseOrderReverserConfirmResponse.cs" />
    <Compile Include="Model\Response\WarehouseOrderStockOutResponse.cs" />
    <Compile Include="Model\Response\WarehouseProductListResponse.cs" />
    <Compile Include="Model\Response\WarehouseProductSkuListResponse.cs" />
    <Compile Include="Model\Response\WareHouseProductSkuResponse.cs" />
    <Compile Include="Model\Response\WarehouseProductSkuStockDetailResponse.cs" />
    <Compile Include="Model\Response\WarehouseProductUpdateResponse.cs" />
    <Compile Include="Model\Response\WarehouseProductAddResponse.cs" />
    <Compile Include="Model\Response\WarehouseProductGetResponse.cs" />
    <Compile Include="Model\Response\WarehouseResponse.cs" />
    <Compile Include="Model\Response\WareHouseSkuBindRelationAddResponse.cs" />
    <Compile Include="Model\Response\WarehouseSkuGetOneResponse.cs" />
    <Compile Include="Model\Response\WarehouseSkuBindResponse.cs" />
    <Compile Include="Model\Response\WareHouseSkuResponse.cs" />
    <Compile Include="Model\Response\WarehouseSkuUnbindResponse.cs" />
    <Compile Include="Model\Response\WarehouseStockInResponse.cs" />
    <Compile Include="Model\Response\WarehouseStockListResponse.cs" />
    <Compile Include="Model\Response\WarehouseStockoutConfirmResponse.cs" />
    <Compile Include="Model\Response\WarehouseStockoutCreateResponse.cs" />
    <Compile Include="Model\Response\WarehouseStockOutResponse.cs" />
    <Compile Include="Model\Response\WarehouseStockChangeDetailRecordResponse.cs" />
    <Compile Include="Model\Response\WarehouseStockRecordListResponse.cs" />
    <Compile Include="Model\Response\WarehouseUpdateResponse.cs" />
    <Compile Include="Model\Response\WarehouseAddResponse.cs" />
    <Compile Include="Model\Response\WarehouseOwnerGetResponse.cs" />
    <Compile Include="Model\Response\WarehouseOwnerInitResponse.cs" />
    <Compile Include="Model\StockOutModel.cs" />
    <Compile Include="Model\WarehouseChildSkuModel.cs" />
    <Compile Include="Model\WarehouseDescriptionModel.cs" />
    <Compile Include="Enum\WarehouseServiceTypeEnum.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Repository\CombinedProductChangeLogRepository.cs" />
    <Compile Include="Repository\SkuStockDetailRepository.cs" />
    <Compile Include="Repository\StockChangeStatRepository.cs" />
    <Compile Include="Repository\StockChangeDetailRecordRepository.cs" />
    <Compile Include="Repository\StockOrderRepository.cs" />
    <Compile Include="Repository\TempProductRepository.cs" />
    <Compile Include="Repository\WareHouseChildSkuFinalRepository.cs" />
    <Compile Include="Repository\WareHouseRepository.cs" />
    <Compile Include="Repository\WareHouseChildSkuRepository.cs" />
    <Compile Include="Repository\WareHouseProductRepository.cs" />
    <Compile Include="Repository\WareHouseSkuRepository.cs" />
    <Compile Include="Repository\WareHouseSkuBindRelationRepository.cs" />
    <Compile Include="Repository\WareHouseOwnerRepository.cs" />
    <Compile Include="Sdk\BaseRequest.cs" />
    <Compile Include="Sdk\Helpers\TimeStampHelper.cs" />
    <Compile Include="Sdk\IWarehouseClient.cs" />
    <Compile Include="Sdk\IWarehouseRequest.cs" />
    <Compile Include="Sdk\WarehouseClient.cs" />
    <Compile Include="ServicesExtension\WarehouseServiceRouter.cs" />
    <Compile Include="Services\BaseService.cs" />
    <Compile Include="Services\BaseWarehouseService.cs" />
    <Compile Include="Services\CombinedProductChangeLogService.cs" />
    <Compile Include="Services\DianGuanJiaWarehouseService.cs" />
    <Compile Include="Services\StockChangeStatService.cs" />
    <Compile Include="Services\StockChangeDetailRecordService.cs" />
    <Compile Include="Services\StockOrderService.cs" />
    <Compile Include="Services\WareHouseChildSkuFinalService.cs" />
    <Compile Include="Services\WareHouseService.cs" />
    <Compile Include="Services\WareHouseChildSkuService.cs" />
    <Compile Include="Services\WareHouseProductService.cs" />
    <Compile Include="Services\SkuStockDetailService.cs" />
    <Compile Include="Services\WareHouseSkuBindRelationService.cs" />
    <Compile Include="Services\WareHouseCommonService.cs" />
    <Compile Include="Services\WareHouseSkuService.cs" />
    <Compile Include="Services\WareHouseOwnerService.cs" />
    <Compile Include="Services\WarehouseServiceFactory.cs" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Test\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\DianGuanJiaApp.Data\DianGuanJiaApp.Data.csproj">
      <Project>{6c8b9657-fbaa-46c8-8433-1b2f908e8398}</Project>
      <Name>DianGuanJiaApp.Data</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Utility\DianGuanJiaApp.Utility.csproj">
      <Project>{a0ead1ef-8b94-4487-ad73-f981cb45e58f}</Project>
      <Name>DianGuanJiaApp.Utility</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>