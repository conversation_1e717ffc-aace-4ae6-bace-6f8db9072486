using DianGuanJiaApp.Data.Model.BaseProduct;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Services.BaseProduct;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;

namespace DianGuanJiaApp.Tests.Services
{
    [TestClass()]
    public class PtProductInfoService_Test
    {
        [TestInitialize]
        public void Init1()
        {
            var user = new UserFxService().Get(5);
            var site = new SiteContext(user);
        }

        [TestMethod()]
        public void TranPlatformSkuFromSelfDefineSku_Test()
        {
            PtProductInfoService ptProductInfoService = new PtProductInfoService();
            List<PtProductInfoSkuModel> ptSkus = new List<PtProductInfoSkuModel>();
            var m1 = new PtProductInfoSkuModel()
            {
                Attribute = new PtProductSkuAttributeModel(),
                SkuCode = DateTime.Now.Ticks.ToString(),
            };

            m1.Attribute.AttributeName1 = "规格";
            m1.Attribute.AttributeValue1 = "白色；XL";
            m1.Attribute.AttributeCode1 = "123";

            ptSkus.Add(m1);
            // var newptSkus = ptProductInfoService.TranPlatformSkuFromSelfDefineSku(ptSkus);
        }

        [TestMethod()]
        public void TranPlatformSkuFromSelfDefineSku_Test2()
        {
            PtProductInfoService ptProductInfoService = new PtProductInfoService();
            List<PtProductInfoSkuModel> ptSkus = new List<PtProductInfoSkuModel>();
            var m1 = new PtProductInfoSkuModel()
            {
                Attribute = new PtProductSkuAttributeModel(),
                SkuCode = DateTime.Now.Ticks.ToString(),
            };

            m1.Attribute.AttributeName1 = "规格1";
            m1.Attribute.AttributeValue1 = "白色";
            m1.Attribute.AttributeCode1 = "123";

            m1.Attribute.AttributeName2 = "规格2";
            m1.Attribute.AttributeValue2 = "L";
            m1.Attribute.AttributeCode2 = "456";

            ptSkus.Add(m1);
            // var newptSkus = ptProductInfoService.TranPlatformSkuFromSelfDefineSku(ptSkus);
        }
    }
}
