<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{2776072F-442E-4E00-9423-1B99CEACF757}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>DianGuanJiaApp.Tests</RootNamespace>
    <AssemblyName>DianGuanJiaApp.Tests</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <ProjectTypeGuids>{3AC096D0-A1C2-E12C-1390-A8335801FDAB};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="CodeProject.ObjectPool, Version=3.0.0.0, Culture=neutral, PublicKeyToken=2f204b7110a52060, processorArchitecture=MSIL">
      <HintPath>..\..\packages\CodeProject.ObjectPool.3.2.4\lib\net461\CodeProject.ObjectPool.dll</HintPath>
    </Reference>
    <Reference Include="AopSdk, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Build\AopSdk.dll</HintPath>
    </Reference>
    <Reference Include="BouncyCastle.Crypto, Version=1.9.0.0, Culture=neutral, PublicKeyToken=0e99375e54769942, processorArchitecture=MSIL">
      <HintPath>..\packages\Portable.BouncyCastle.1.9.0\lib\net40\BouncyCastle.Crypto.dll</HintPath>
    </Reference>
    <Reference Include="BouncyCastle.Cryptography, Version=*******, Culture=neutral, PublicKeyToken=072edcf4a5328938, processorArchitecture=MSIL">
      <HintPath>..\packages\BouncyCastle.Cryptography.2.2.1\lib\net461\BouncyCastle.Cryptography.dll</HintPath>
    </Reference>
    <Reference Include="CSRedisCore, Version=3.8.670.0, Culture=neutral, PublicKeyToken=9aa6a3079358d437, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\CSRedisCore.3.8.670\lib\net45\CSRedisCore.dll</HintPath>
    </Reference>
    <Reference Include="DnsClient, Version=*******, Culture=neutral, PublicKeyToken=4574bb5573c51424, processorArchitecture=MSIL">
      <HintPath>..\packages\DnsClient.1.0.7\lib\net45\DnsClient.dll</HintPath>
    </Reference>
    <Reference Include="ICSharpCode.SharpZipLib, Version=0.86.0.518, Culture=neutral, PublicKeyToken=1b03e6acf1164f73, processorArchitecture=MSIL">
      <HintPath>..\packages\SharpZipLib.0.86.0\lib\20\ICSharpCode.SharpZipLib.dll</HintPath>
    </Reference>
   
    <Reference Include="LOGSDK, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Build\LOGSDK.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.9.0.0\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.Diagnostics.Tracing.EventSource, Version=********, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Diagnostics.Tracing.EventSource.Redist.1.1.28\lib\net40\Microsoft.Diagnostics.Tracing.EventSource.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.9.0.0\lib\net462\Microsoft.Extensions.DependencyInjection.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.Abstractions.9.0.0\lib\net462\Microsoft.Extensions.DependencyInjection.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.9.0.0\lib\net462\Microsoft.Extensions.Logging.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.Abstractions.9.0.0\lib\net462\Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Options, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Options.9.0.0\lib\net462\Microsoft.Extensions.Options.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Primitives, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Primitives.9.0.0\lib\net462\Microsoft.Extensions.Primitives.dll</HintPath>
	 </Reference>  
    <Reference Include="Microsoft.VisualStudio.QualityTools.UnitTestFramework, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" />
    <Reference Include="MongoDB.Bson, Version=2.7.2.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\MongoDB.Bson.2.7.2\lib\net45\MongoDB.Bson.dll</HintPath>
    </Reference>
    <Reference Include="MongoDB.Driver, Version=2.7.2.0, Culture=neutral, PublicKeyToken=null" />
    <Reference Include="MongoDB.Driver.Core, Version=2.7.2.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\MongoDB.Driver.Core.2.7.2\lib\net45\MongoDB.Driver.Core.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NPOI, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.4.0\lib\net40\NPOI.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OOXML, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.4.0\lib\net40\NPOI.OOXML.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OpenXml4Net, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.4.0\lib\net40\NPOI.OpenXml4Net.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OpenXmlFormats, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.4.0\lib\net40\NPOI.OpenXmlFormats.dll</HintPath>
    </Reference>
    <Reference Include="Osp sdk">
      <HintPath>..\packages\Build\Osp sdk.dll</HintPath>
    </Reference>
    <Reference Include="Pipelines.Sockets.Unofficial, Version=*******, Culture=neutral, PublicKeyToken=42ea0a778e13fbe2, processorArchitecture=MSIL">
      <HintPath>..\packages\Pipelines.Sockets.Unofficial.2.2.2\lib\net461\Pipelines.Sockets.Unofficial.dll</HintPath>
    </Reference>
    <Reference Include="Polly, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Polly.5.9.0\lib\net45\Polly.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RabbitMQ.Client, Version=*******, Culture=neutral, PublicKeyToken=89e7d7c5feba84ce, processorArchitecture=MSIL">
      <HintPath>..\packages\RabbitMQ.Client.5.1.0\lib\net451\RabbitMQ.Client.dll</HintPath>
    </Reference>
    <Reference Include="StackExchange.Redis, Version=*******, Culture=neutral, PublicKeyToken=c219ff1ca8c2ce46, processorArchitecture=MSIL">
      <HintPath>..\packages\StackExchange.Redis.2.5.61\lib\net461\StackExchange.Redis.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel" />
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Configuration.ConfigurationManager, Version=4.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Configuration.ConfigurationManager.4.4.1\lib\net461\System.Configuration.ConfigurationManager.dll</HintPath>
    </Reference>
    <Reference Include="System.Configuration.Install" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.OracleClient" />
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.DiagnosticSource.9.0.0\lib\net462\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.PerformanceCounter, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.PerformanceCounter.5.0.0\lib\net461\System.Diagnostics.PerformanceCounter.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing" />
    <Reference Include="System.IO.Compression, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Compression.4.3.0\lib\net46\System.IO.Compression.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Pipelines, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Pipelines.5.0.2\lib\net461\System.IO.Pipelines.dll</HintPath>
    </Reference>
    <Reference Include="System.Management" />
    <Reference Include="System.Memory, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.InteropServices.RuntimeInformation.4.3.0\lib\net45\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Loader, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.Loader.4.3.0\lib\netstandard1.5\System.Runtime.Loader.dll</HintPath>
    </Reference>
    <Reference Include="System.Security" />
    <Reference Include="System.Security.AccessControl, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.AccessControl.4.7.0\lib\net461\System.Security.AccessControl.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Permissions, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Permissions.4.7.0\lib\net461\System.Security.Permissions.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Principal.Windows, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Principal.Windows.4.7.0\lib\net461\System.Security.Principal.Windows.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Text.Encoding.CodePages, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encoding.CodePages.4.4.0\lib\net461\System.Text.Encoding.CodePages.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encodings.Web, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.7.0.0\lib\netstandard2.0\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Json.7.0.1\lib\netstandard2.0\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Channels, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Channels.5.0.0\lib\net461\System.Threading.Channels.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions" />
    <Reference Include="System.ValueTuple, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net461\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Abstractions" />
    <Reference Include="System.Web.Helpers, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=5.2.7.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.7\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.7\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Routing" />
    <Reference Include="System.Web.WebPages, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="Microsoft.Web.Infrastructure, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.Web.Infrastructure.*******\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="Ubiety.Dns.Core, Version=2.2.1.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d, processorArchitecture=MSIL">
      <HintPath>..\packages\MySql.Data.8.0.30\lib\net452\Ubiety.Dns.Core.dll</HintPath>
    </Reference>
    <Reference Include="WindowsBase" />
    <Reference Include="Z.Dapper.Plus">
      <HintPath>..\packages\Build\Z.Dapper.Plus.dll</HintPath>
    </Reference>
    <Reference Include="ZstdNet, Version=1.4.5.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d, processorArchitecture=MSIL">
      <HintPath>..\packages\MySql.Data.8.0.30\lib\net452\ZstdNet.dll</HintPath>
    </Reference>
    <Reference Include="ZstdSharp, Version=0.7.1.0, Culture=neutral, PublicKeyToken=8d151af33a4ad5cf, processorArchitecture=MSIL">
      <HintPath>..\packages\ZstdSharp.Port.0.7.1\lib\net461\ZstdSharp.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Data\BaseRepositoryTest.cs" />
    <Compile Include="Data\DapperPurgeCacheTimerTests.cs" />
    <Compile Include="Data\DataChangeLogDbConfigRepositoryTests.cs" />
    <Compile Include="Data\FinancialSettlementRepositoryTest.cs" />
    <Compile Include="Data\SubUserFxRepositoryTest.cs" />
    <Compile Include="Data\UserFxPostRelationRepositoryTest.cs" />
    <Compile Include="Data\ProductSkuHistoryRepositoryTest.cs" />
    <Compile Include="Data\OrderItemExtRepositoryTest.cs" />
    <Compile Include="Data\FxDbConfigRepositoryTest.cs" />
    <Compile Include="Data\FxUserShopRepositoryTest.cs" />
    <Compile Include="Data\SupplierUserRepositoryTest.cs" />
    <Compile Include="FenDan\DuplicationColdStorageSwitchRepositoryTest.cs" />
    <Compile Include="Controllers\ProductControllerTest.cs" />
    <Compile Include="Data\PostFxRepositoryTest.cs" />
    <Compile Include="Data\DapperTestV2.cs" />
    <Compile Include="FenDan\SendHistoryRepositoryTest.cs" />
    <Compile Include="Data\BaseOfPtSkuRelationRepositoryTest.cs" />
    <Compile Include="Data\OrderItemRepositoryTest.cs" />
    <Compile Include="Controllers\PartnerControllerTest.cs" />
    <Compile Include="Data\BaseProductRepositoryTest.cs" />
    <Compile Include="FenDan\ListingTaskRecordsServiceTest.cs" />
    <Compile Include="PlatformService\BiliBiliPlatformServiceTest.cs" />
    <Compile Include="PlatformService\JingDongPurchasePlatformServiceTest.cs" />
    <Compile Include="PlatformService\SyncFxProductServiceTests.cs" />
    <Compile Include="PlatformService\HaoYouDuoPlatformTest.cs" />
    <Compile Include="Repository\BranchShareRelationRepositoryTests.cs" />
    <Compile Include="Repository\FxUserShopRepositoryTests.cs" />
    <Compile Include="Repository\PrintTemplateRepositoryTests.cs" />
    <Compile Include="Repository\SupplierUserRepositoryTests.cs" />
    <Compile Include="Repository\SyncStatusRepositoryTests.cs" />
    <Compile Include="Repository\UserFxRepositoryTests.cs" />
    <Compile Include="Services\BaseProductServiceTests.cs" />
    <Compile Include="Data\CainiaoAuthOwnerRepositoryTest.cs" />
    <Compile Include="Data\CommonSettingRepositoryTest.cs" />
    <Compile Include="Data\FxDbConfigTest.cs" />
    <Compile Include="Data\FxMessageTest.cs" />
    <Compile Include="Data\PrintHistoryDbConfigRepositoryTest.cs" />
    <Compile Include="Data\PrintTemplateRepositoryTest.cs" />
    <Compile Include="Data\QuickSearchRepositoryTest.cs" />
    <Compile Include="Data\SupperlierDbConfigTest.cs" />
    <Compile Include="Data\SupplierUserTest.cs" />
    <Compile Include="Data\WaybillCustomAreaRepositoryTest.cs" />
    <Compile Include="Helpers\LogHelperTests.cs" />
    <Compile Include="PlatformService\JuHaoMaiPlatformTest.cs" />
    <Compile Include="PlatformService\HeliangPlatformServiceTest.cs" />
    <Compile Include="SDK\JDTest.cs" />
    <Compile Include="Services\C2MSyncAfterOrderServiceTest.cs" />
    <Compile Include="Services\DataArchive\DataArchiveBySettingIllegalCharacterServiceTests.cs" />
    <Compile Include="Services\DataArchive\DataArchiveByCommonSettingClearServiceTests.cs" />
    <Compile Include="Services\ImgHelperServiceTest.cs" />
    <Compile Include="PlatformService\TaobaoMaiCaiPlatformServiceTest.cs" />
    <Compile Include="Repository\DbConfigRepositoryTests.cs" />
    <Compile Include="Repository\ShopRepositoryTests.cs" />
    <Compile Include="Services\AfterSaleOrderServiceTests.cs" />
    <Compile Include="PlatformService\TikTokPlatformServiceTest.cs" />
    <Compile Include="Services\BusinessCardServiceTest.cs" />
    <Compile Include="BusiniessTest\UnitTest1.cs" />
    <Compile Include="Alipay\AlipayTest.cs" />
    <Compile Include="Controllers\AuthControllerTest.cs" />
    <Compile Include="Controllers\NewOrderControllerTest.cs" />
    <Compile Include="Data\DataChangeLogTests.cs" />
    <Compile Include="Data\DataExtensionTest.cs" />
    <Compile Include="Data\ProductFxRepositoryTests.cs" />
    <Compile Include="Data\WaybillCodeRepositoryTest.cs" />
    <Compile Include="DingTalk\RobotClientTests.cs" />
    <Compile Include="Extension\StringExtensionTest.cs" />
    <Compile Include="Extension\BaseDataChangeLogRepositoryTests.cs" />
    <Compile Include="FeiShu\FeiShuRobotClientTests.cs" />
    <Compile Include="FenDan\ProductDbConfigRepositoryTest.cs" />
    <Compile Include="FenDan\BatchUpdateTest.cs" />
    <Compile Include="FenDan\WareHouseServiceTest.cs" />
    <Compile Include="FenDan\PurchaseOrderDeliveryModeServiceTest.cs" />
    <Compile Include="FenDan\DistributorProductMappingServiceTest.cs" />
    <Compile Include="FenDan\DbConfigRepositoryTest.cs" />
    <Compile Include="FenDan\FxUnbindServiceTests.cs" />
    <Compile Include="FenDan\PaymentStatementTest.cs" />
    <Compile Include="FenDan\QingTest.cs" />
    <Compile Include="FenDan\CheckOrderServiceTest.cs" />
    <Compile Include="FenDan\ShopServiceTest.cs" />
    <Compile Include="FenDan\PrintHistoryServiceTest.cs" />
    <Compile Include="FenDan\PrintHistoryControllerTest.cs" />
    <Compile Include="Controllers\WeixinControllerTest.cs" />
    <Compile Include="Controllers\CommonControllerTest.cs" />
    <Compile Include="Controllers\LogStatisticControllerTest.cs" />
    <Compile Include="Controllers\MessageControllerTest.cs" />
    <Compile Include="Controllers\OrderControllerTest.cs" />
    <Compile Include="Controllers\PddApiTest.cs" />
    <Compile Include="Data\DapperTest.cs" />
    <Compile Include="Data\RepositoryTest.cs" />
    <Compile Include="Data\SharedBranchDataModifyTest.cs" />
    <Compile Include="Data\DbPerformanceTest.cs" />
    <Compile Include="FenDan\MessageTest.cs" />
    <Compile Include="FenDan\FxDataMigrateTaskServiceTest.cs" />
    <Compile Include="FenDan\PhShopServiceTest.cs" />
    <Compile Include="FenDan\RepairTest.cs" />
    <Compile Include="FenDan\AliLogTest.cs" />
    <Compile Include="FenDan\SendFailTest.cs" />
    <Compile Include="FenDan\AfterSaleTest.cs" />
    <Compile Include="FenDan\ControllerTest.cs" />
    <Compile Include="FenDan\ProductFxServiceTest.cs" />
    <Compile Include="FenDan\OrderFxServiceTest.cs" />
    <Compile Include="FenDan\FxUnBindTaskTest.cs" />
    <Compile Include="FenDan\ColdLogicOrderTest.cs" />
    <Compile Include="Logistic\LogisticCenterTest.cs" />
    <Compile Include="Messages\MessageConsumerTests.cs" />
    <Compile Include="MongoDB11\LogisticsMogonRepository.cs" />
    <Compile Include="MongoDB11\OrderMongoRepositoryTest.cs" />
    <Compile Include="PlatformService\AlibabaC2MPlatformServiceTest.cs" />
    <Compile Include="PlatformService\AlibabaPlatformServiceWangShangPayTest.cs" />
    <Compile Include="PlatformService\BeiBeiPlatformServiceTest.cs" />
    <Compile Include="PlatformService\JingDongPlatformServiceTest2.cs" />
    <Compile Include="PlatformService\JingDongPlatformServiceTest.cs" />
    <Compile Include="PlatformService\DuXiaoDianPlatformServiceTest.cs" />
    <Compile Include="PlatformService\KuaiTuanTuanPlatformServiceTest.cs" />
    <Compile Include="PlatformService\OpenPlatformServiceTest.cs" />
    <Compile Include="PlatformService\CainiaoWaybillApiTest.cs" />
    <Compile Include="PlatformService\PinduoduoPlatformFFMServiceTest.cs" />
    <Compile Include="PlatformService\PinduoduoPlatformFdsServiceTest.cs" />
    <Compile Include="PlatformService\SyncFxOrderServiceTests.cs" />
    <Compile Include="PlatformService\XiaoHongShuLogisticsServiceTest.cs" />
    <Compile Include="PlatformService\WxVideoPlatfromServiceTest.cs" />
    <Compile Include="PlatformService\WxXiaoShangDianPlatfromServiceTest.cs" />
    <Compile Include="PlatformService\MoKuaiPlatformServiceTest.cs" />
    <Compile Include="PlatformService\TuanHaoHuoPlatformServiceTest.cs" />
    <Compile Include="PlatformService\YunJiPlatformServiceTest.cs" />
    <Compile Include="PlatformService\TaobaoPlatformServiceTest.cs" />
    <Compile Include="PlatformService\PinduoduoPlatformServiceTest.cs" />
    <Compile Include="PlatformService\AlibabaPlatformServiceTest.cs" />
    <Compile Include="PlatformService\KuaidiniaoApiTest.cs" />
    <Compile Include="PlatformService\PinduoduoWaybillApiTest.cs" />
    <Compile Include="PlatformService\FengQiaoServiceTest.cs" />
    <Compile Include="PlatformService\VipShopPlatformServiceTest.cs" />
    <Compile Include="PlatformService\SuningPlatformServiceTest.cs" />
    <Compile Include="PlatformService\MoGuJiePlatformServiceTest.cs" />
    <Compile Include="PlatformService\WuJieWaybillApiServerTest.cs" />
    <Compile Include="PlatformService\MengTuiPlatformServiceTest.cs" />
    <Compile Include="PlatformService\KuaiShouPlatformServiceTest.cs" />
    <Compile Include="PlatformService\XiaoHongShuPlatformServiceTest.cs" />
    <Compile Include="PlatformService\ZhiDianPlatformServiceTest.cs" />
    <Compile Include="PlatformService\WeiDianPlatformServiceTest.cs" />
    <Compile Include="PlatformService\UnitTest1.cs" />
    <Compile Include="PlatformService\WeiMengPlatformServiceTest.cs" />
    <Compile Include="PlatformService\YouZanPlatformServiceTest.cs" />
    <Compile Include="PlatformService\XiaoDianPlatformServiceTest.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Controllers\HomeControllerTest.cs" />
    <Compile Include="Redis\RedisTest.cs" />
    <Compile Include="SDK\PddTest.cs" />
    <Compile Include="SDK\YchTest.cs" />
    <Compile Include="Services\AfterSaleActionRecordServiceTest.cs" />
    <Compile Include="Services\ColdHotStorage\LogicOrderColdHotStorageServiceTests.cs" />
    <Compile Include="Services\CommonSettingServiceTests.cs" />
    <Compile Include="Services\ConfigDbMigrate\ConfigDbMigrateServiceTest.cs" />
    <Compile Include="Services\CrossCloud\SyncCommonSettingsServiceTests.cs" />
    <Compile Include="Services\DataArchive\DataArchiveByCleanServiceTests.cs" />
    <Compile Include="Services\DataArchive\DataArchiveByExpiredUserServiceTests.cs" />
    <Compile Include="Services\DataArchive\DataArchiveBySettlementBillClearServiceTests.cs" />
    <Compile Include="Services\DataArchive\DataArchiveBySettlementBillServiceTests.cs" />
    <Compile Include="Services\DataArchive\DataArchiveByTimeBackupDbServiceTests.cs" />
    <Compile Include="Services\DataArchive\DataArchiveServiceTests.cs" />
    <Compile Include="Services\DataArchive\DataArchiveWinServiceTests.cs" />
    <Compile Include="Services\DataEventTrackingLog\DefaultSettlementPriceHistoryLogServiceTests.cs" />
    <Compile Include="Services\DataEventTrackingLog\UserAccountLogServiceTests.cs" />
    <Compile Include="Services\DataEventTrackingLog\DataDuplicationStatusHistoryLogServiceTests.cs" />
    <Compile Include="Services\DataDuplication\PushMessageServiceTests.cs" />
    <Compile Include="Services\DataEventTracking\BusinessLogDataEventTrackingServiceTests.cs" />
    <Compile Include="Services\DataMigrateSameCloud\DataMigrateSameCloudServiceTests.cs" />
    <Compile Include="Services\DataMigrateSameCloud\LogicOrderDataMigrateSameCloudServiceTests.cs" />
    <Compile Include="Services\DataMigrateSameCloud\PathFlowDataMigrateSameCloudServiceTests.cs" />
    <Compile Include="Services\DataMigrateSameCloud\ProductDataMigrateSameCloudServiceTests.cs" />
    <Compile Include="Services\DataMigrateSameCloud\SendHistoryDataMigrateSameCloudServiceTests.cs" />
    <Compile Include="Services\DataMigrateSameCloud\WaybillCodeDataMigrateSameCloudServiceTests.cs" />
    <Compile Include="Services\DataMigrate\PrintHistoryMigratePathFlowServiceTests.cs" />
    <Compile Include="Services\DataMigrate\PathFlowDataMigrateServiceTests.cs" />
    <Compile Include="Services\DataMigrate\SetTipServiceTests.cs" />
    <Compile Include="Services\Compensate\CompensateBatchCollectionServiceTests.cs" />
    <Compile Include="Services\Compensate\CompensateBatchStorageServiceTests.cs" />
    <Compile Include="Services\Compensate\SendHistoryCompensateServiceTests.cs" />
    <Compile Include="Services\DataMigrate\LogicOrderDataMigrateServiceTests.cs" />
    <Compile Include="Services\DataMigrate\SettlementProductSkuDataMigrateServiceTests.cs" />
    <Compile Include="Services\DataMigrate\FxMigrateLockServiceTests.cs" />
    <Compile Include="Services\DataMigrate\WaybillCodeDataMigrateServiceTests.cs" />
    <Compile Include="Services\DataMigrate\SendHistoryDataMigrateServiceTests.cs" />
    <Compile Include="Services\Compensate\InvokeApiDataCollectionServiceTests.cs" />
    <Compile Include="Services\Compensate\InvokeApiDataStorageServiceTests.cs" />
    <Compile Include="Services\Compensate\SendHistoryCompensateTaskServiceTests.cs" />
    <Compile Include="Services\DataEventTracking\BaseDataEventTrackingServiceTests.cs" />
    <Compile Include="Services\ExportTaskServiceTests.cs" />
    <Compile Include="Services\ExpressCompanyServiceTests.cs" />
    <Compile Include="Services\ExpressCpCodeMappingServiceTests.cs" />
    <Compile Include="Services\FxDbConfigServiceTests.cs" />
    <Compile Include="Services\FxUserShopServiceTests.cs" />
    <Compile Include="Services\LogicOrderServiceTests.cs" />
    <Compile Include="Services\MessageQueue\OnlineSendMessageHandleServiceTests.cs" />
    <Compile Include="Services\MessageQueue\PurchaseOrderStatusHandleMessageServiceTests.cs" />
    <Compile Include="Services\OrderExtraServiceTest.cs" />
    <Compile Include="Services\OrderModule\ColdLogicOrderServiceTests.cs" />
    <Compile Include="Services\OrderModule\ColdOrderServiceTests.cs" />
    <Compile Include="Services\OrderModule\OrderAbnormalMessageServiceTests.cs" />
    <Compile Include="Services\OrderModule\OrderAbnormalPathFlowNodeServiceTests.cs" />
    <Compile Include="Services\OrderModule\OrderAbnormalServiceTests.cs" />
    <Compile Include="Services\OrderModule\OrderStatusServiceTest.cs" />
    <Compile Include="Services\OrderServiceTests.cs" />
    <Compile Include="Services\OrderItemStatusServiceTest.cs" />
    <Compile Include="Services\OrderStatusServiceTest.cs" />
    <Compile Include="Services\PrintHistoryServiceTest.cs" />
    <Compile Include="Services\ProductFxServiceTests.cs" />
    <Compile Include="Services\ProductSettlementPriceServiceTests.cs" />
    <Compile Include="Services\PtProductInfoService_Test.cs" />
    <Compile Include="Services\ProfitStatistics\CloudMessageBaseServiceTest.cs" />
    <Compile Include="Services\ProfitStatistics\ProfitOrderServiceTest.cs" />
    <Compile Include="Services\PurchaseOrderItemRelationServiceTests.cs" />
    <Compile Include="Services\PurchaseOrderRelationServiceTests.cs" />
    <Compile Include="Services\ChangeBindShopServiceTests.cs" />
    <Compile Include="Services\ReceDbConfigServiceTests.cs" />
    <Compile Include="Services\SendHistoryReturnRecordServiceTests.cs" />
    <Compile Include="Services\SendHistoryReturn\SendHistoryReturnAuthConsumerServiceTests.cs" />
    <Compile Include="Services\SendHistoryServiceTests.cs" />
    <Compile Include="Services\SettingsService\BusinessSettingsServiceTests.cs" />
    <Compile Include="Services\SettlementBillServiceTests.cs" />
    <Compile Include="Services\ShopAuthHistoryServiceTests.cs" />
    <Compile Include="Services\PathFlowReferenceServiceTests.cs" />
    <Compile Include="Services\SyncOrderServiceTest.cs" />
    <Compile Include="Services\SubAccount\PostFxServiceTest.cs" />
    <Compile Include="Services\SubAccount\SysPermissionFxServiceTest.cs" />
    <Compile Include="Services\SyncService\SyncMessageDeclarationServiceTest.cs" />
    <Compile Include="Services\TemplatePackageInfoServiceTests.cs" />
    <Compile Include="Services\Tools\DbIdentityIdMonitorServiceTests.cs" />
    <Compile Include="Services\Tools\OrderLifeCycleDetectionServiceTests.cs" />
    <Compile Include="Services\Tools\OrderSyncAnalysisRecordStatusServiceTests.cs" />
    <Compile Include="Services\Tools\OrderSyncAnalysisServiceTests.cs" />
    <Compile Include="Services\Tools\ServiceQueueMonitorToolServiceTests.cs" />
    <Compile Include="Services\TouTiaoMessageProcessorTest.cs" />
    <Compile Include="Services\DataDuplication\AfterSaleOrderDuplicationServiceTests.cs" />
    <Compile Include="Services\DataDuplication\LogicOrderDuplicationServiceTests.cs" />
    <Compile Include="Services\DataDuplication\PathFlowDuplicationServiceTests.cs" />
    <Compile Include="Services\DataDuplication\ProductDuplicationServiceTests.cs" />
    <Compile Include="Services\DataMigrate\DataMigrateServiceTests.cs" />
    <Compile Include="Services\DataMigrate\CommonSettingDataMigrateServiceTests.cs" />
    <Compile Include="Services\DataMigrate\PrintHistoryDataMigrateServiceTests.cs" />
    <Compile Include="Services\DataMigrate\ProductDataMigrateServiceTests.cs" />
    <Compile Include="Services\DataSyncStatus\DataSyncStatusServiceTests.cs" />
    <Compile Include="Services\DbPolicyServiceTest.cs" />
    <Compile Include="Services\ExcelExportTaskTest.cs" />
    <Compile Include="Services\NewServiceTest.cs" />
    <Compile Include="Services\CommonSettingServiceTest.cs" />
    <Compile Include="Services\OrderTagServiceTests.cs" />
    <Compile Include="Services\ServiceTest.cs" />
    <Compile Include="Services\ScanServiceTest.cs" />
    <Compile Include="Services\SyncDataInterface\SyncDataInterfaceServiceTests.cs" />
    <Compile Include="Services\ShopServiceTests.cs" />
    <Compile Include="Services\SyncStatusServiceTests.cs" />
    <Compile Include="Services\MessageProcessLogServiceTest.cs" />
    <Compile Include="Services\AlibabaFxOrderServiceTest.cs" />
    <Compile Include="Services\TouTiaoOrderServiceTest.cs" />
    <Compile Include="Services\UniversalModule\DataSyncStorageHandleServiceTests.cs" />
    <Compile Include="Services\CustomEncryptionServiceTest.cs" />
    <Compile Include="Services\SiteMessageServiceTest.cs" />
    <Compile Include="Services\UniversalModule\FxMessageDataSyncStatusServiceTests.cs" />
    <Compile Include="Services\BaseProductServiceTest.cs" />
    <Compile Include="Services\UserFxServiceTest.cs" />
    <Compile Include="ServiceVerison\ServiceVersionTest.cs" />
    <Compile Include="ShareWaybillCode\ShareWaybillCodeTest.cs" />
    <Compile Include="Codes.cs" />
    <Compile Include="Test2.cs" />
    <Compile Include="Test.cs" />
    <Compile Include="UtilTest\EntityUtilTest.cs" />
    <Compile Include="VolcanoLog\VolcanoLogClientTests.cs" />
    <Compile Include="WareHouse\WareHouseProductRepositoryTest.cs" />
    <Compile Include="WareHouse\StockChangeDetailRecordRepositoryTest.cs" />
    <Compile Include="WareHouse\SkuStockDetailRepositoryTest.cs" />
    <Compile Include="WaybillApiTest\DBKDExpressApiServiceTest.cs" />
    <Compile Include="WaybillApiTest\HTKYExpressApiServiceTest.cs" />
    <Compile Include="WaybillApiTest\JTExpressApiServiceTest.cs" />
    <Compile Include="WaybillApiTest\STOExpressApiServiceTest.cs" />
    <Compile Include="WaybillApiTest\LinkExpressApiServiceTest.cs" />
    <Compile Include="WaybillApiTest\TouTiaoExpressApiServiceTest.cs" />
    <Compile Include="WaybillApiTest\YTOExpressApiServiceTest.cs" />
    <Compile Include="WaybillApiTest\ZTOExpressApiServiceTest.cs" />
    <Compile Include="WaybillApiTest\UnitTest1.cs" />
    <Compile Include="WaybillApiTest\ZYKDExpressApiServiceTest.cs" />
    <Compile Include="Winform\ExportTaskAppTest.cs" />
    <Compile Include="Winform\TaobaoMessage.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="App.config" />
    <Content Include="ILLink\ILLink.Descriptors.LibraryBuild.xml" />
  </ItemGroup>
  <ItemGroup>
    <None Include="..\DianGuanJiaApp.ErpWeb\Config\AppSettings.config">
      <Link>Config\AppSettings.config</Link>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <SubType>Designer</SubType>
    </None>
    <None Include="..\DianGuanJiaApp.ErpWeb\Config\ConnectionStrings.config">
      <Link>Config\ConnectionStrings.config</Link>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\AutoComment\DianGuanJiaApp.RabbitMQ\DianGuanJiaApp.RabbitMQ.csproj">
      <Project>{90dc55be-abba-4686-8119-a18e012f540e}</Project>
      <Name>DianGuanJiaApp.RabbitMQ</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Core\DianGuanJiaApp.Core.csproj">
      <Project>{93CA297D-8704-48D8-8748-67EF45304F88}</Project>
      <Name>DianGuanJiaApp.Core</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Data\DianGuanJiaApp.Data.csproj">
      <Project>{6c8b9657-fbaa-46c8-8433-1b2f908e8398}</Project>
      <Name>DianGuanJiaApp.Data</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.ErpWeb\DianGuanJiaApp.ErpWeb.csproj">
      <Project>{8b8f2526-e5fa-4bbf-9bb0-7ea802f2eef7}</Project>
      <Name>DianGuanJiaApp.ErpWeb</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Message.Consumer\DianGuanJiaApp.Message.Consumer.csproj">
      <Project>{4ed3b78b-5437-4002-aae4-1faef5c19521}</Project>
      <Name>DianGuanJiaApp.Message.Consumer</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.PddFxMessage.Consumer\DianGuanJiaApp.PddFxMessage.Consumer.csproj">
      <Project>{7dad8151-0e02-4327-87b3-871bcaf330f6}</Project>
      <Name>DianGuanJiaApp.PddFxMessage.Consumer</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Portal\DianGuanJiaApp.Portal.csproj">
      <Project>{e4308453-7d50-4b74-b899-b01687363052}</Project>
      <Name>DianGuanJiaApp.Portal</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Services\DianGuanJiaApp.Services.csproj">
      <Project>{abee0a3a-3b86-4631-add0-3bdd17a0b1fc}</Project>
      <Name>DianGuanJiaApp.Services</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.SiteMessage.Api\DianGuanJiaApp.SiteMessage.Api.csproj">
      <Project>{60362575-b39d-4b39-b5c5-f01ee86fbbcf}</Project>
      <Name>DianGuanJiaApp.SiteMessage.Api</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.SiteMessage\DianGuanJiaApp.SiteMessage.csproj">
      <Project>{776BDADB-85E8-4C5B-9B21-3C9235A36A79}</Project>
      <Name>DianGuanJiaApp.SiteMessage</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Trace.Services\DianGuanJiaApp.Trace.Services.csproj">
      <Project>{799EFC5B-87D2-4FC1-A9D5-191FA59D0B02}</Project>
      <Name>DianGuanJiaApp.Trace.Services</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Trace.ViewModels\DianGuanJiaApp.Trace.ViewModels.csproj">
      <Project>{7eb493fb-b565-4c96-81f4-bcdf9393d1b8}</Project>
      <Name>DianGuanJiaApp.Trace.ViewModels</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Utility\DianGuanJiaApp.Utility.csproj">
      <Project>{a0ead1ef-8b94-4487-ad73-f981cb45e58f}</Project>
      <Name>DianGuanJiaApp.Utility</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.ViewModels\DianGuanJiaApp.ViewModels.csproj">
      <Project>{549A33CF-F7FB-49EF-B8BD-1AAE56317663}</Project>
      <Name>DianGuanJiaApp.ViewModels</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Warehouse\DianGuanJiaApp.Warehouse.csproj">
      <Project>{a5ad179b-04e3-44e4-a90c-6260199de89a}</Project>
      <Name>DianGuanJiaApp.Warehouse</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Web\DianGuanJiaApp.Web.csproj">
      <Project>{9eff6008-ab13-4524-97e5-efd416e72ce9}</Project>
      <Name>DianGuanJiaApp.Web</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.WindowsService\DianGuanJiaApp.WindowsService.csproj">
      <Project>{9efe38bf-56c8-424f-85c5-1bfe1dacaa6f}</Project>
      <Name>DianGuanJiaApp.WindowsService</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Winform.Base\DianGuanJiaApp.Winform.Base.csproj">
      <Project>{4af5c2a6-66f2-4f0f-a5b7-34a5895fb3c7}</Project>
      <Name>DianGuanJiaApp.Winform.Base</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Winform.DataMigrate\DianGuanJiaApp.Winform.DataMigrate.csproj">
      <Project>{0ef498a1-c25f-416e-a867-6b5eba725b6f}</Project>
      <Name>DianGuanJiaApp.Winform.DataMigrate</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Winform.ExportTaskApp\DianGuanJiaApp.Winform.ExportTaskApp.csproj">
      <Project>{223d7214-f0ae-4d8f-af50-879317cc4d10}</Project>
      <Name>DianGuanJiaApp.Winform.ExportTaskApp</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Winform.TaobaoMessage\DianGuanJiaApp.Winform.TaobaoMessage.csproj">
      <Project>{e31ee459-ed41-4f6e-9fc7-9d748d4d4422}</Project>
      <Name>DianGuanJiaApp.Winform.TaobaoMessage</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Winform\DianGuanJiaApp.Winform.SyncOrder.csproj">
      <Project>{3648dc20-4643-4b55-979a-3e7b91b8b5ff}</Project>
      <Name>DianGuanJiaApp.Winform.SyncOrder</Name>
    </ProjectReference>
    <ProjectReference Include="..\jos-net-open-api-sdk-2.0\jos-sdk-net.csproj">
      <Project>{cf7757d6-4f03-4bca-948d-d1e0b81b491c}</Project>
      <Name>jos-sdk-net</Name>
    </ProjectReference>
    <ProjectReference Include="..\sf-fengqiao-sdk\sf-fengqiao-sdk.csproj">
      <Project>{24C1F4F0-D3DD-4BE1-BEBA-EFF47FEB7F3D}</Project>
      <Name>sf-fengqiao-sdk</Name>
    </ProjectReference>
    <ProjectReference Include="..\taobao-sdk-net-auto_1554290167233-20190926-source\TopSdk.csproj">
      <Project>{9c11cae5-5188-4e71-825b-68fc3135728a}</Project>
      <Name>TopSdk</Name>
    </ProjectReference>
    <ProjectReference Include="..\vipshop-sdk\vipshop-sdk.csproj">
      <Project>{2848621e-7b9f-4770-b444-e3913e1b0ddd}</Project>
      <Name>vipshop-sdk</Name>
    </ProjectReference>
    <ProjectReference Include="..\ych-sdk\ych-sdk.csproj">
      <Project>{aaf61c3f-5729-4e3c-bc8c-eea8067e0305}</Project>
      <Name>ych-sdk</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <COMReference Include="IWshRuntimeLibrary">
      <Guid>{F935DC20-1CF0-11D0-ADB9-00C04FD58A0B}</Guid>
      <VersionMajor>1</VersionMajor>
      <VersionMinor>0</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>tlbimp</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </COMReference>
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Connected Services\" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Services\BaseProduct\" />
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>