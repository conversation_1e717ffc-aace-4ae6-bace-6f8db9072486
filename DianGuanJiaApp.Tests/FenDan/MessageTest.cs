using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;
using System.Diagnostics;
using System.Text;
using DianGuanJiaApp.Message.Consumer;
using DianGuanJiaApp.PddFxMessage.Consumer;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Data.Enum;

namespace DianGuanJiaApp.Tests.Data
{
    [TestClass]
    public class MessageTest
    {
        [TestInitialize]
        public void Init()
        {

        }
        /// <summary>
        /// 消息消费
        /// </summary>
        [TestMethod]
        public void ProcessFxMessageTest()
        {
            var msgs = new List<string>();
            //msgs.Add("{\"Key\":\"TouTiao-T-FX-4886392401558756913\",\"AfterSaleId\":\"A1687c2de1dda7845bf7f\",\"State\":\"200\",\"MID\":\"22321509\",\"TID\":\"5021386166686441381\",\"PID\":\"\",\"PT\":\"TouTiao\",\"ASS\":\"20\",\"CID\":\"5021386166686441381\"}");
            //msgs.Add("{\"Key\":\"TouTiao-T-FX-4886313601795237425\",\"AfterSaleId\":\"7051388125637050660\",\"State\":\"200\",\"MID\":\"V4463798\",\"TID\":\"4886313601795237425\",\"PID\":\"\",\"PT\":\"TouTiao\",\"ASS\":\"20\",\"CID\":\"5021386166686441381\"}");

            //msgs.Add("{\"Key\":\"TouTiao-T-FX-5044827066441878686\",\"AfterSaleId\":\"\",\"State\":\"102\",\"MID\":\"22321509\",\"TID\":\"5044827066441878686\",\"PID\":\"\",\"PT\":\"TouTiao\",\"ASS\":\"\",\"CID\":\"\"}");
            //msgs.Add("{\"Key\":\"1688-QT-3573363601878144402\",\"AfterSaleId\":\"\",\"State\":\"ORDER_ORDER_BUYER_REFUND_IN_SALES\",\"MID\":\"bncsm\",\"TID\":\"3573363601878144402\",\"PID\":\"\",\"PT\":\"Alibaba\",\"ASS\":\"\",\"CID\":\"\"}");

            msgs.Add("{\"PT\":\"Alibaba\",\"CID\":\"AlibabaFenFaDB\",\"PID\":\"8b4c8667efb328f1\",\"MID\":\"45\",\"AfterSaleType\":\"Virtual\",\"State\":\"SendHistoryReturnRecord\",\"ISC\":false}");

            //new DianGuanJiaApp.Message.Consumer.MessageConsumer().ProcessFxMessage(msgs, PlatformType.TouTiao.ToString());
            //new DianGuanJiaApp.Message.Consumer.MessageConsumer().ProcessFxMessage(msgs, true);

            new DianGuanJiaApp.Message.Consumer.MessageConsumer().ProcessFxSendHistoryReturnMessage(msgs);
        }
        /// <summary>
        /// 打单消息消费-->分单系统
        /// </summary>
        [TestMethod]
        public void ProcessMessageTest()
        {
            var msgs = new List<string>();
            msgs.Add("{\"Key\":\"1688-2527044699107924615\",\"State\":\"0\",\"MID\":\"b2b-22088764970542ec10\",\"TID\":\"2527044699107924615\",\"PID\":\"\",\"PT\":\"Alibaba\"}");

            new DianGuanJiaApp.Message.Consumer.MessageConsumer().ProcessMessage(msgs);
        }

        /// <summary>
        /// 打单消息消费-->分单系统 链路
        /// </summary>
        [TestMethod]
        public void ProcessMessageTraceTest()
        {
            var siteContext = new SiteContext(1610);
            var msgs = new List<string>();
            msgs.Add("{\"Key\":\"1688-2527044699107924615\",\"State\":\"0\",\"MID\":\"b2b-22088764970542ec10\",\"TID\":\"2527044699107924615\",\"PID\":\"\",\"PT\":\"Alibaba\",\"BatchId\":\"1234\"}");

            new DianGuanJiaApp.Message.Consumer.MessageConsumer().ProcessMessage(msgs);
        }




        /// <summary>
        /// 拼多多消息消费测试
        /// </summary>
        [TestMethod]
        public void PddProcessFxOrderMessageTest()
        {
            var msgs = new List<string>();
            msgs.Add("{\"id\":236507641669481,\"appId\":0,\"commandType\":\"Common\",\"time\":1648535187696,\"sendTime\":1648535185314,\"message\":{\"type\":\"pdd_chat_OrderPromise\",\"mallID\":411970,\"content\":\"{\\\"mall_id\\\":411970,\\\"action\\\":1,\\\"promise_id\\\":346731575,\\\"tid\\\":\\\"220329-186415850451917\\\"}\"}}");
            //new FxMessageConsumer().ProcessFxOrderMessage(msgs, PlatformType.Pinduoduo.ToString());
        }

        /// <summary>
        /// 分单系统-自动售后消息测试
        /// </summary>
        [TestMethod]
        public void ProcessFxAutoAfterSaleMessageTest()
        {
            var msgs = new List<string>();
            msgs.Add("{\"Key\":\"\",\"State\":\"0\",\"MID\":\"45\",\"TID\":\"b26aeed255bfba72\",\"PID\":\"\",\"PT\":\"\"}");
            msgs.Add("{\"Key\":\"\",\"State\":\"0\",\"MID\":\"45\",\"TID\":\"02dc2ef05a922bf0\",\"PID\":\"\",\"PT\":\"\"}");
            
            new DianGuanJiaApp.Message.Consumer.MessageConsumer().ProcessFxAutoAfterSaleMessage(msgs);
        }


        /// <summary>
        /// 分单系统-同步店铺类目
        /// </summary>
        [TestMethod]
        public void ProcessFxShopAuthMessageTest()
        {
            var msgs = new List<string>();
            msgs.Add("{\"ShopId\":1687,\"PlatformType\":\"TouTiao\",\"Time\":\"2024-07-17\"}");
            msgs.Add("{\"ShopId\":1681,\"PlatformType\":\"TouTiao\",\"Time\":\"2024-07-17\"}");

            new DianGuanJiaApp.Message.Consumer.MessageConsumer().ProcessFxShopAuthMessage(msgs);
        }
        
    }
}
