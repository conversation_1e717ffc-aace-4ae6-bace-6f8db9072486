<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{6C8B9657-FBAA-46C8-8433-1B2F908E8398}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>DianGuanJiaApp.Data</RootNamespace>
    <AssemblyName>DianGuanJiaApp.Data</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <LangVersion>6</LangVersion>
    <DocumentationFile>bin\Debug\DianGuanJiaApp.Data.xml</DocumentationFile>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <LangVersion>6</LangVersion>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="BouncyCastle.Crypto, Version=1.9.0.0, Culture=neutral, PublicKeyToken=0e99375e54769942, processorArchitecture=MSIL">
      <HintPath>..\packages\Portable.BouncyCastle.1.9.0\lib\net40\BouncyCastle.Crypto.dll</HintPath>
    </Reference>
    <Reference Include="BouncyCastle.Cryptography, Version=*******, Culture=neutral, PublicKeyToken=072edcf4a5328938, processorArchitecture=MSIL">
      <HintPath>..\packages\BouncyCastle.Cryptography.2.2.1\lib\net461\BouncyCastle.Cryptography.dll</HintPath>
    </Reference>
    <Reference Include="ClickHouse.Client, Version=7.10.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\ClickHouse.Client.7.10.1\lib\net462\ClickHouse.Client.dll</HintPath>
    </Reference>
    <Reference Include="CodeProject.ObjectPool, Version=3.0.0.0, Culture=neutral, PublicKeyToken=2f204b7110a52060, processorArchitecture=MSIL">
      <HintPath>..\packages\CodeProject.ObjectPool.3.2.4\lib\net45\CodeProject.ObjectPool.dll</HintPath>
    </Reference>
    <Reference Include="CSRedisCore, Version=3.8.670.0, Culture=neutral, PublicKeyToken=9aa6a3079358d437, processorArchitecture=MSIL">
      <HintPath>..\packages\CSRedisCore.3.8.670\lib\net45\CSRedisCore.dll</HintPath>
    </Reference>
    <Reference Include="DnsClient, Version=1.0.7.0, Culture=neutral, PublicKeyToken=4574bb5573c51424, processorArchitecture=MSIL">
      <HintPath>..\packages\DnsClient.1.0.7\lib\net45\DnsClient.dll</HintPath>
    </Reference>
    <Reference Include="Elasticsearch.Net, Version=7.0.0.0, Culture=neutral, PublicKeyToken=96c599bbe3e70f5d, processorArchitecture=MSIL">
      <HintPath>..\packages\Elasticsearch.Net.7.17.5\lib\net461\Elasticsearch.Net.dll</HintPath>
    </Reference>
    <Reference Include="Google.Protobuf, Version=3.25.1.0, Culture=neutral, PublicKeyToken=a7d26565bac4d604, processorArchitecture=MSIL">
      <HintPath>..\packages\Google.Protobuf.3.19.4\lib\net45\Google.Protobuf.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="K4os.Compression.LZ4, Version=1.3.8.0, Culture=neutral, PublicKeyToken=2186fa9121ef231d, processorArchitecture=MSIL">
      <HintPath>..\packages\K4os.Compression.LZ4.1.3.8\lib\net462\K4os.Compression.LZ4.dll</HintPath>
    </Reference>
    <Reference Include="K4os.Compression.LZ4.Streams, Version=1.2.6.0, Culture=neutral, PublicKeyToken=2186fa9121ef231d, processorArchitecture=MSIL">
      <HintPath>..\packages\K4os.Compression.LZ4.Streams.1.2.6\lib\net46\K4os.Compression.LZ4.Streams.dll</HintPath>
    </Reference>
    <Reference Include="K4os.Hash.xxHash, Version=1.0.6.0, Culture=neutral, PublicKeyToken=32cd54395057cec3, processorArchitecture=MSIL">
      <HintPath>..\packages\K4os.Hash.xxHash.1.0.6\lib\net46\K4os.Hash.xxHash.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Http.Features, Version=5.0.17.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNetCore.Http.Features.5.0.17\lib\net461\Microsoft.AspNetCore.Http.Features.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=9.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.9.0.0\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.Abstractions.8.0.0\lib\net462\Microsoft.Extensions.Configuration.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection, Version=9.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.9.0.0\lib\net462\Microsoft.Extensions.DependencyInjection.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection.Abstractions, Version=9.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.Abstractions.9.0.0\lib\net462\Microsoft.Extensions.DependencyInjection.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging, Version=9.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.9.0.0\lib\net462\Microsoft.Extensions.Logging.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging.Abstractions, Version=9.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.Abstractions.9.0.0\lib\net462\Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Options, Version=9.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Options.9.0.0\lib\net462\Microsoft.Extensions.Options.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Primitives, Version=9.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Primitives.9.0.0\lib\net462\Microsoft.Extensions.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IO.RecyclableMemoryStream, Version=3.0.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IO.RecyclableMemoryStream.3.0.1\lib\netstandard2.0\Microsoft.IO.RecyclableMemoryStream.dll</HintPath>
    </Reference>
    <Reference Include="MongoDB.Bson, Version=2.7.2.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\MongoDB.Bson.2.7.2\lib\net45\MongoDB.Bson.dll</HintPath>
    </Reference>
    <Reference Include="MongoDB.Driver, Version=2.7.2.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\MongoDB.Driver.2.7.2\lib\net45\MongoDB.Driver.dll</HintPath>
    </Reference>
    <Reference Include="MongoDB.Driver.Core, Version=2.7.2.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\MongoDB.Driver.Core.2.7.2\lib\net45\MongoDB.Driver.Core.dll</HintPath>
    </Reference>
    <Reference Include="MySql.Data, Version=8.0.30.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d, processorArchitecture=MSIL">
      <HintPath>..\packages\MySql.Data.8.0.30\lib\net452\MySql.Data.dll</HintPath>
    </Reference>
    <Reference Include="Nest, Version=7.0.0.0, Culture=neutral, PublicKeyToken=96c599bbe3e70f5d, processorArchitecture=MSIL">
      <HintPath>..\packages\NEST.7.17.5\lib\net461\Nest.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NodaTime, Version=3.2.1.0, Culture=neutral, PublicKeyToken=4226afe0d9b296d1, processorArchitecture=MSIL">
      <HintPath>..\packages\NodaTime.3.2.1\lib\netstandard2.0\NodaTime.dll</HintPath>
    </Reference>
    <Reference Include="OpenTelemetry">
      <HintPath>..\packages\Build\OpenTelemetryPackage\OpenTelemetry.dll</HintPath>
    </Reference>
    <Reference Include="OpenTelemetry.Api, Version=0.0.0.0, Culture=neutral, PublicKeyToken=7bd6737fe5b67e3c, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Build\OpenTelemetryPackage\OpenTelemetry.Api.dll</HintPath>
    </Reference>
    <Reference Include="Pipelines.Sockets.Unofficial, Version=*******, Culture=neutral, PublicKeyToken=42ea0a778e13fbe2, processorArchitecture=MSIL">
      <HintPath>..\packages\Pipelines.Sockets.Unofficial.2.2.2\lib\net461\Pipelines.Sockets.Unofficial.dll</HintPath>
    </Reference>
    <Reference Include="Polly, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Polly.5.9.0\lib\net45\Polly.dll</HintPath>
    </Reference>
    <Reference Include="StackExchange.Redis, Version=*******, Culture=neutral, PublicKeyToken=c219ff1ca8c2ce46, processorArchitecture=MSIL">
      <HintPath>..\packages\StackExchange.Redis.2.5.61\lib\net461\StackExchange.Redis.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" />
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Configuration.ConfigurationManager, Version=4.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Configuration.ConfigurationManager.4.4.1\lib\net461\System.Configuration.ConfigurationManager.dll</HintPath>
    </Reference>
    <Reference Include="System.Configuration.Install" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.OracleClient" />
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=9.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.DiagnosticSource.9.0.0\lib\net462\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.PerformanceCounter, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.PerformanceCounter.5.0.0\lib\net461\System.Diagnostics.PerformanceCounter.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing" />
    <Reference Include="System.IO.Compression, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Compression.4.3.0\lib\net46\System.IO.Compression.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Pipelines, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Pipelines.5.0.2\lib\net461\System.IO.Pipelines.dll</HintPath>
    </Reference>
    <Reference Include="System.Management" />
    <Reference Include="System.Memory, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net" />
    <Reference Include="System.Net.Http, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Http.4.3.4\lib\net46\System.Net.Http.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Caching" />
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.InteropServices.RuntimeInformation.4.3.0\lib\net45\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Loader, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.Loader.4.3.0\lib\netstandard1.5\System.Runtime.Loader.dll</HintPath>
    </Reference>
    <Reference Include="System.Security" />
    <Reference Include="System.Security.AccessControl, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.AccessControl.4.7.0\lib\net461\System.Security.AccessControl.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Algorithms.4.3.0\lib\net461\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Encoding.4.3.0\lib\net46\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Primitives.4.3.0\lib\net46\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.X509Certificates.4.3.0\lib\net461\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Permissions, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Permissions.4.7.0\lib\net461\System.Security.Permissions.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Principal.Windows, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Principal.Windows.4.7.0\lib\net461\System.Security.Principal.Windows.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Text.Encoding.CodePages, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encoding.CodePages.4.4.0\lib\net461\System.Text.Encoding.CodePages.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encodings.Web, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.8.0.0\lib\net462\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Json.8.0.5\lib\net462\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Channels, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Channels.5.0.0\lib\net461\System.Threading.Channels.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions" />
    <Reference Include="System.ValueTuple, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net461\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="Ubiety.Dns.Core, Version=2.2.1.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d, processorArchitecture=MSIL">
      <HintPath>..\packages\MySql.Data.8.0.30\lib\net452\Ubiety.Dns.Core.dll</HintPath>
    </Reference>
    <Reference Include="WindowsBase" />
    <Reference Include="Z.Dapper.Plus, Version=1.4.1.0, Culture=neutral, PublicKeyToken=59b66d028979105b, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Build\Z.Dapper.Plus.dll</HintPath>
    </Reference>
    <Reference Include="ZstdNet, Version=1.4.5.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d, processorArchitecture=MSIL">
      <HintPath>..\packages\MySql.Data.8.0.30\lib\net452\ZstdNet.dll</HintPath>
    </Reference>
    <Reference Include="ZstdSharp, Version=0.7.1.0, Culture=neutral, PublicKeyToken=8d151af33a4ad5cf, processorArchitecture=MSIL">
      <HintPath>..\packages\ZstdSharp.Port.0.7.1\lib\net461\ZstdSharp.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AliOpenEbillSendRecordRepository.cs" />
    <Compile Include="ClickHouseHelper.cs" />
    <Compile Include="DapperSource\CommandDefinition.cs" />
    <Compile Include="DapperSource\CommandFlags.cs" />
    <Compile Include="DapperSource\CustomPropertyTypeMap.cs" />
    <Compile Include="DapperSource\DataTableHandler.cs" />
    <Compile Include="DapperSource\DbString.cs" />
    <Compile Include="DapperSource\DefaultTypeMap.cs" />
    <Compile Include="DapperSource\DynamicParameters.CachedOutputSetters.cs" />
    <Compile Include="DapperSource\DynamicParameters.cs" />
    <Compile Include="DapperSource\DynamicParameters.ParamInfo.cs" />
    <Compile Include="DapperSource\ExplicitConstructorAttribute.cs" />
    <Compile Include="DapperSource\FeatureSupport.cs" />
    <Compile Include="DapperSource\SimpleMemberMap.cs" />
    <Compile Include="DapperSource\SqlDataRecordHandler.cs" />
    <Compile Include="DapperSource\SqlDataRecordListTVPParameter.cs" />
    <Compile Include="DapperSource\SqlMapper.Async.cs" />
    <Compile Include="DapperSource\SqlMapper.CacheInfo.cs" />
    <Compile Include="DapperSource\SqlMapper.cs" />
    <Compile Include="DapperSource\SqlMapper.DapperRow.cs" />
    <Compile Include="DapperSource\SqlMapper.DapperRowMetaObject.cs" />
    <Compile Include="DapperSource\SqlMapper.DapperTable.cs" />
    <Compile Include="DapperSource\SqlMapper.DeserializerState.cs" />
    <Compile Include="DapperSource\SqlMapper.DontMap.cs" />
    <Compile Include="DapperSource\SqlMapper.GridReader.Async.cs" />
    <Compile Include="DapperSource\SqlMapper.GridReader.cs" />
    <Compile Include="DapperSource\SqlMapper.ICustomQueryParameter.cs" />
    <Compile Include="DapperSource\SqlMapper.IDataReader.cs" />
    <Compile Include="DapperSource\SqlMapper.Identity.cs" />
    <Compile Include="DapperSource\SqlMapper.IDynamicParameters.cs" />
    <Compile Include="DapperSource\SqlMapper.IMemberMap.cs" />
    <Compile Include="DapperSource\SqlMapper.IParameterCallbacks.cs" />
    <Compile Include="DapperSource\SqlMapper.IParameterLookup.cs" />
    <Compile Include="DapperSource\SqlMapper.ITypeHandler.cs" />
    <Compile Include="DapperSource\SqlMapper.ITypeMap.cs" />
    <Compile Include="DapperSource\SqlMapper.Link.cs" />
    <Compile Include="DapperSource\SqlMapper.LiteralToken.cs" />
    <Compile Include="DapperSource\SqlMapper.Settings.cs" />
    <Compile Include="DapperSource\SqlMapper.TypeDeserializerCache.cs" />
    <Compile Include="DapperSource\SqlMapper.TypeHandler.cs" />
    <Compile Include="DapperSource\SqlMapper.TypeHandlerCache.cs" />
    <Compile Include="DapperSource\TableValuedParameter.cs" />
    <Compile Include="DapperSource\TypeExtensions.cs" />
    <Compile Include="DapperSource\UdtTypeHandler.cs" />
    <Compile Include="DapperSource\WrappedDataReader.cs" />
    <Compile Include="DapperSource\WrappedReader.cs" />
    <Compile Include="DapperSource\XmlHandlers.cs" />
    <Compile Include="Dapper\DapperConfigHelper.cs" />
    <Compile Include="Dapper\DapperPurgeCacheTimer.cs" />
    <Compile Include="Dapper\DbAccessUtility.cs" />
    <Compile Include="Dapper\DbApiAccessUtility.cs" />
    <Compile Include="Dapper\DbUtility.cs" />
    <Compile Include="Dapper\GenericListDataReader.cs" />
    <Compile Include="DataConstants.cs" />
    <Compile Include="DataConverter.cs" />
    <Compile Include="DbSchema.cs" />
    <Compile Include="EntityExtension\BaseProduct\BaseProductEntity.cs" />
    <Compile Include="EntityExtension\OrderAbnormalExtension.cs" />
    <Compile Include="EntityExtension\BaseProduct\BaseProductSku.cs" />
    <Compile Include="EntityExtension\CrossBroder\ClaimShopProductExtension.cs" />
    <Compile Include="EntityExtension\FxAlibabaBuyerShopRelationExtension.cs" />
    <Compile Include="EntityExtension\BranchShareUseShopExtension.cs" />
    <Compile Include="Entity\AfterSaleAddress.cs" />
    <Compile Include="Entity\AfterSaleAddressLog.cs" />
    <Compile Include="Entity\AfterSaleOrderExt.cs" />
    <Compile Include="Entity\AfterSaleOrderItemStockRecord.cs" />
    <Compile Include="EntityExtension\SupplierProduct\PtProductInfoExtension.cs" />
    <Compile Include="EntityExtension\SupplierProduct\ListingTaskRecordsExtension.cs" />
    <Compile Include="EntityExtension\Tools\OrderSyncAnalysisRecordStatusExtension.cs" />
    <Compile Include="Entity\BaseProduct\BaseOfSupplierProductSkuRelation.cs" />
    <Compile Include="Entity\BaseProduct\BaseProductAbnormal.cs" />
    <Compile Include="Entity\ApiCallLog.cs" />
    <Compile Include="Entity\BaseProduct\BaseProductRelationRecordExt.cs" />
    <Compile Include="Entity\BaseProduct\BaseProductRelationRecord.cs" />
    <Compile Include="Entity\BaseProduct\BaseProductSkuSupplierConfigExtension.cs" />
    <Compile Include="Entity\BaseProduct\GenerateBaseProductRecord.cs" />
    <Compile Include="Entity\CloudMessage.cs" />
    <Compile Include="Entity\ColdHotStorage\DbSpaceUsed.cs" />
    <Compile Include="Entity\DeliveryModeChangeLog.cs" />
    <Compile Include="Entity\MySqlMasterDb\MySqlDbConfig.cs" />
    <Compile Include="Entity\MySqlMasterDb\MySqlDbNameConfig.cs" />
    <Compile Include="Entity\MySqlMasterDb\MySqlDbServerConfig.cs" />
    <Compile Include="Entity\PlatformApp.cs" />
    <Compile Include="Entity\PlatformAppOrderRecord.cs" />
    <Compile Include="Entity\ProfitStatistics\PathFlowNode.cs" />
    <Compile Include="Entity\ProfitStatistics\ProfitLogicOrder.cs" />
    <Compile Include="Entity\ProfitStatistics\ProfitLogicOrderItem.cs" />
    <Compile Include="Entity\ProfitStatistics\ProfitOrderExtension.cs" />
    <Compile Include="Entity\ProfitStatistics\ProfitOrder.cs" />
    <Compile Include="Entity\ProfitStatistics\ProfitOrderItem.cs" />
    <Compile Include="Entity\ProfitStatistics\ProfitLogicOrderExtension.cs" />
    <Compile Include="Entity\ProfitStatistics\ProfitOrderItemPriceExtension.cs" />
    <Compile Include="Entity\ProfitStatistics\ProfitOrderItemPrice.cs" />
    <Compile Include="Entity\ProfitStatistics\ProfitOrderSyncTask.cs" />
    <Compile Include="Entity\ProfitStatistics\ProfitPathFlow.cs" />
    <Compile Include="Entity\ProfitStatistics\ProfitStatisticsCloudMessage.cs" />
    <Compile Include="Entity\ProfitStatistics\ProfitStatisticsDbConfig.cs" />
    <Compile Include="Entity\ProfitStatistics\ProfitStatisticsDbNameConfig.cs" />
    <Compile Include="Entity\ProfitStatistics\ProfitStatisticsDbServerConfig.cs" />
    <Compile Include="Entity\BaseProduct\PtProductInfo.cs" />
    <Compile Include="Entity\BaseProduct\PtProductInfoExt.cs" />
    <Compile Include="Entity\MemberLevel.cs" />
    <Compile Include="Entity\FxUserShopDefaultSupplier.cs" />
    <Compile Include="Entity\FreightTemplate\ExpShippingFeeTemplate.cs" />
    <Compile Include="Entity\FreightTemplate\ExpShippingFeeTemplateChangeRecord.cs" />
    <Compile Include="Entity\FreightTemplate\ExpShippingFeeTemplateOtherRule.cs" />
    <Compile Include="Entity\ProductPathFlowRefType.cs" />
    <Compile Include="Entity\ProductSkuHistory.cs" />
    <Compile Include="Entity\ReceiverOaid.cs" />
    <Compile Include="Entity\SettlementBillStat.cs" />
    <Compile Include="Entity\SettlementProductSkuName.cs" />
    <Compile Include="Entity\ShopVideo\PublishVideoTask.cs" />
    <Compile Include="Entity\ShopVideo\PublishVideoTaskExt.cs" />
    <Compile Include="Entity\ShopVideo\ShopVideoInfo.cs" />
    <Compile Include="Entity\ShopVideo\ShopVideoItem.cs" />
    <Compile Include="Entity\ShopVideo\VideoUploadRecord.cs" />
    <Compile Include="Entity\StatActive.cs" />
    <Compile Include="Entity\StatActiveDetail.cs" />
    <Compile Include="Entity\StatServiceAppOrder.cs" />
    <Compile Include="Entity\SubAccount\SysPermissionVersionRelation.cs" />
    <Compile Include="Entity\Collect\ClaimShopProduct.cs" />
    <Compile Include="Entity\Collect\ClaimShopProductExt.cs" />
    <Compile Include="Entity\Collect\ClaimProductRelation.cs" />
    <Compile Include="Entity\Collect\CollectBoxProduct.cs" />
    <Compile Include="Entity\Collect\CurrencyConvertRate .cs" />
    <Compile Include="Entity\GlobalCommodity\GlobalProduct.cs" />
    <Compile Include="Entity\GlobalCommodity\GlobalProductExt.cs" />
    <Compile Include="Entity\GlobalCommodity\GlobalProductSku.cs" />
    <Compile Include="Entity\SubAccount\SystemNavVersionRelation.cs" />
    <Compile Include="Entity\SubProductSettlementPrice.cs" />
    <Compile Include="Entity\SupplierProduct\ListingProduct\ListingTemplateGroup.cs" />
    <Compile Include="Entity\SupplierProduct\ListingProduct\ListingTemplateGroupItem.cs" />
    <Compile Include="Entity\SupplierProduct\ListingProduct\UserListingSetting.cs" />
    <Compile Include="Entity\PlatformCateShopRelation.cs" />
    <Compile Include="Entity\SupplierProduct\ListingTaskBusinessAbnormal.cs" />
    <Compile Include="Entity\SupplierProduct\ListingTaskRecordsExt.cs" />
    <Compile Include="Entity\SupplierProduct\SharePathFlowExtension.cs" />
    <Compile Include="Entity\SupplierProduct\ListingTaskRecords.cs" />
    <Compile Include="Entity\SupplierProduct\SupplierAddress.cs" />
    <Compile Include="Entity\SupplierProduct\SupProductWaybillRelation.cs" />
    <Compile Include="Entity\SupplierProduct\UserBrand.cs" />
    <Compile Include="Entity\SupplierProduct\SharePathFlow.cs" />
    <Compile Include="Entity\SupplierProduct\SharePathFlowNode.cs" />
    <Compile Include="Entity\SupplierProduct\SupplierProducSku.cs" />
    <Compile Include="Entity\SupplierProduct\SupplierProduct.cs" />
    <Compile Include="Entity\SupplierProduct\SupplierProductCateRelation.cs" />
    <Compile Include="Entity\SupplierProduct\UserSupplierStatus.cs" />
    <Compile Include="Entity\TkPrintProcessStateRecord.cs" />
    <Compile Include="Entity\TkShipmentsInfoTranslateRecord.cs" />
    <Compile Include="Entity\WaybillCodeRecycle.cs" />
    <Compile Include="Entity\Tools\OrderSyncAnalysisAutoReason.cs" />
    <Compile Include="Entity\Tools\OrderSyncAnalysisRecordStatus.cs" />
    <Compile Include="Enum\BaseProduct\BaseProductBindSupplierType.cs" />
    <Compile Include="Entity\ProfitStatistics\ProfitStatisticsLogicOrderItemExtension.cs" />
    <Compile Include="Enum\BaseProduct\BaseProductAbnormalType.cs" />
    <Compile Include="Enum\AfterSaleLogType.cs" />
    <Compile Include="Enum\MatchOaidType.cs" />
    <Compile Include="Enum\CombinedProductChangeType.cs" />
    <Compile Include="Enum\ShopVideoPublishStatus.cs" />
    <Compile Include="Enum\TouTiaoSendApiType.cs" />
    <Compile Include="FxModel\LogicOrderItemByAfterSaleModel.cs" />
    <Compile Include="FxModel\InstantRetail\InstantRetailWarehousesDto.cs" />
    <Compile Include="FxModel\MergeSubUserModel.cs" />
    <Compile Include="FxModel\SettlementBillQueryModelV2.cs" />
    <Compile Include="FxModel\UserFxModel.cs" />
    <Compile Include="Extension\RequiredAttribute.cs" />
    <Compile Include="FxModel\DataDuplicationLockSwitchConfigModel.cs" />
    <Compile Include="FxModel\ManyCodeSendConfigModel.cs" />
    <Compile Include="FxModel\FxColdHotMigrate\ColdHotFxMigrateTaskInitConfig.cs" />
    <Compile Include="FxModel\FxColdHotMigrate\ColdHotMigrateConfig.cs" />
    <Compile Include="IRepository\ICloudMessageRepository.cs" />
    <Compile Include="IRepository\IMySqlDbConfigRepository.cs" />
    <Compile Include="Enum\FinalDistributePriceCorrectRule.cs" />
    <Compile Include="Enum\SupplierProduct\ListingTaskBusinessAbnormalType.cs" />
    <Compile Include="Enum\TkGlobalProductStatusEnum.cs" />
    <Compile Include="FxModel\AfterSaleOrderStockMessageModel.cs" />
    <Compile Include="FxModel\CategoryProduct\CategoryPropAli.cs" />
    <Compile Include="FxModel\CategoryProduct\DyCascadeValueReqModel.cs" />
    <Compile Include="FxModel\CategoryProduct\GetBrandReqModel.cs" />
    <Compile Include="FxModel\CategoryProduct\GetCategoryByNameResModel.cs" />
    <Compile Include="FxModel\CategoryProduct\GetCategoryListReqModel.cs" />
    <Compile Include="FxModel\CategoryProduct\GetCategoryPropReqModel.cs" />
    <Compile Include="FxModel\GetOrderCountConditionsModel.cs" />
    <Compile Include="FxModel\CategoryProduct\ProductCateForecastReq.cs" />
    <Compile Include="FxModel\Listing\GetBatchListingReq.cs" />
    <Compile Include="FxModel\Listing\BatchListingSetReq.cs" />
    <Compile Include="Model\AfterSaleAddressModel.cs" />
    <Compile Include="Model\AfterSaleOperatResult.cs" />
    <Compile Include="Model\LocalAcCode.cs" />
    <Compile Include="Model\BaseProduct\BaseProcuctSkuBindSearch.cs" />
    <Compile Include="Model\BaseProduct\BaseProductSkuBindLineSearch.cs" />
    <Compile Include="Model\BaseProduct\BaseProductSkuBindSupplierPrice.cs" />
    <Compile Include="Model\BaseProduct\BatchBaseProductSkuUnbindRes.cs" />
    <Compile Include="Model\BaseProduct\BatchDeleteBaseProductRes.cs" />
    <Compile Include="Model\BaseProduct\BatchUnbindBaseProductSkuData.cs" />
    <Compile Include="Model\BaseProduct\DeleteBaseProductCheckModel.cs" />
    <Compile Include="Model\BaseProduct\DeleteBaseProductSettingModel.cs" />
    <Compile Include="Model\BiliBili\AfterSaleQueryList.cs" />
    <Compile Include="Model\BiliBili\CommodityItemList.cs" />
    <Compile Include="Model\BiliBili\IBiliBiliRequest.cs" />
    <Compile Include="Model\BiliBili\LogisticsAdd.cs" />
    <Compile Include="Model\BiliBili\OrderRemark.cs" />
    <Compile Include="Model\BiliBili\OrderSearchList.cs" />
    <Compile Include="Model\BiliBili\ShopInfoRequest.cs" />
    <Compile Include="Model\ConverDataToExcelModelForV2Params.cs" />
    <Compile Include="Model\CrossBorder\BulkCopyPublishGlobalProductsModel.cs" />
    <Compile Include="Model\CrossBorder\CategoryMetadata.cs" />
    <Compile Include="Model\CrossBorder\ClaimProductRelationModel.cs" />
    <Compile Include="Model\CrossBorder\ClaimProductSearchModel.cs" />
    <Compile Include="Model\CrossBorder\ClaimProductUpdateStatusDesModel.cs" />
    <Compile Include="Model\CrossBorder\ClaimPublishGlobalShopModel.cs" />
    <Compile Include="Model\CrossBorder\ClaimRelationPageResult.cs" />
    <Compile Include="Model\CrossBorder\ClaimShopOperateInModel.cs" />
    <Compile Include="Model\CrossBorder\CollectClaimProductModel.cs" />
    <Compile Include="Model\CrossBorder\CollectedAttributeModel.cs" />
    <Compile Include="Model\CrossBorder\GetBulkCopyGlobalParentShopsRes.cs" />
    <Compile Include="Model\CrossBorder\GetBulkCopyGlobalShopsRes.cs" />
    <Compile Include="Model\CrossBorder\GetGlobalShopsRes.cs" />
    <Compile Include="Model\CrossBorder\GetGlobalWarehouseObjADO.cs" />
    <Compile Include="Model\CrossBorder\GlobalInventeryReqADO.cs" />
    <Compile Include="Model\CrossBorder\GlobalProductCreateDesModel.cs" />
    <Compile Include="Model\CrossBorder\GlobalProductCreateModel.cs" />
    <Compile Include="Model\CrossBorder\GlobalProductDeleteModel.cs" />
    <Compile Include="Model\CrossBorder\GlobalProductEditModel.cs" />
    <Compile Include="Model\CrossBorder\GlobalProductInventorySkuModel.cs" />
    <Compile Include="Model\CrossBorder\GlobalProductRegionsJson.cs" />
    <Compile Include="Model\CrossBorder\GlobalProductRegionsRes.cs" />
    <Compile Include="Model\CrossBorder\GlobalProductSearchCategoryRuleModel.cs" />
    <Compile Include="Model\CrossBorder\GlobalProductSearchDetailModel.cs" />
    <Compile Include="Model\CrossBorder\GlobalProductSearchDetailRes.cs" />
    <Compile Include="Model\CrossBorder\GlobalProductSkuDeleteJsonModel.cs" />
    <Compile Include="Model\CrossBorder\GlobalProductSkuStockModel.cs" />
    <Compile Include="Model\CrossBorder\GlobalProductSyncModel.cs" />
    <Compile Include="Model\CrossBorder\GlobalProductUpdateInventoryModel.cs" />
    <Compile Include="Model\CrossBorder\GlobalProductUpdateModel.cs" />
    <Compile Include="Model\CrossBorder\GlobalShopResult.cs" />
    <Compile Include="Model\CrossBorder\GlobalProductSearchModel.cs" />
    <Compile Include="Model\CrossBorder\GlobalProductSearchRes.cs" />
    <Compile Include="Model\CrossBorder\GlobalProductSkuModel.cs" />
    <Compile Include="Model\CrossBorder\GlobalRecommendCategoriesRes.cs" />
    <Compile Include="Model\CrossBorder\ClaimShopEntryModel.cs" />
    <Compile Include="Model\CrossBorder\CrossBorderCurrencyExchangeRates.cs" />
    <Compile Include="Model\CrossBorder\ClaimProductInfoModel.cs" />
    <Compile Include="Model\CrossBorder\ListeningCreateGlobalProductModel.cs" />
    <Compile Include="Model\CrossBorder\ListeningSaveGlobalProductJsonModel.cs" />
    <Compile Include="Model\CrossBorder\ListeningUpdateClaimProductStatusModel.cs" />
    <Compile Include="Model\CrossBorder\TikTokShipContextModel.cs" />
    <Compile Include="Model\CrossBorder\PhPublishGlobalToShopModel.cs" />
    <Compile Include="Model\CrossBorder\PublishGlobalProductByTaskRecordModel.cs" />
    <Compile Include="Model\CrossBorder\PublishGlobalProductByTaskRecordShopModel.cs" />
    <Compile Include="Model\CrossBorder\PublishGlobalProductOnlyToMarketJsonModel.cs" />
    <Compile Include="Model\CrossBorder\PublishGlobalProductOnlyToMarketModel.cs" />
    <Compile Include="Model\CrossBorder\PublishGlobalProductRegionModel.cs" />
    <Compile Include="Model\CrossBorder\PublishGlobalShopsModel.cs" />
    <Compile Include="Model\CrossBorder\TikTokBrandModel.cs" />
    <Compile Include="Model\CrossBorder\TikTokGlobalCategory.cs" />
    <Compile Include="Model\CrossBorder\CollectProductInfoSkuModel.cs" />
    <Compile Include="Model\CrossBorder\ClooectProductBoxListModel.cs" />
    <Compile Include="Model\CrossBorder\CollectionsJSON.cs" />
    <Compile Include="Model\CrossBorder\CollectProductSearchModel.cs" />
    <Compile Include="FxModel\Listing\BrandInfo.cs" />
    <Compile Include="FxModel\Listing\BrandModel.cs" />
    <Compile Include="FxModel\Listing\JudgeTemplateReq.cs" />
    <Compile Include="FxModel\Listing\ListingProductStatusEnum.cs" />
    <Compile Include="FxModel\Listing\ListingSetting.cs" />
    <Compile Include="FxModel\Listing\ListingTaskRecordModel.cs" />
    <Compile Include="FxModel\Listing\ListingTaskRecordQuery.cs" />
    <Compile Include="FxModel\Listing\ListingTemplateGroupModel.cs" />
    <Compile Include="FxModel\Listing\SaveLisTempGroupItemModel.cs" />
    <Compile Include="FxModel\Listing\SaveLisTempGroupModel.cs" />
    <Compile Include="FxModel\Listing\ShopDeliveryTemplateReq.cs" />
    <Compile Include="FxModel\Listing\ShopDeliveryTemplateModel.cs" />
    <Compile Include="FxModel\Listing\UserListingSettingModel.cs" />
    <Compile Include="FxModel\RemarkSettingModel.cs" />
    <Compile Include="FxModel\SettlementExcelFreightModel.cs" />
    <Compile Include="FxModel\PrductSkuSimple.cs" />
    <Compile Include="FxModel\SupplierProduct\BusinessCardModel.cs" />
    <Compile Include="FxModel\SupplierProduct\CopySupplierProductModel.cs" />
    <Compile Include="FxModel\SupplierProduct\PuhuoRecordRequestModel.cs" />
    <Compile Include="FxModel\SupplierProduct\SaveBrandModel.cs" />
    <Compile Include="FxModel\SupplierProduct\StationInfoByAgentModel.cs" />
    <Compile Include="FxModel\SupplierProduct\StationInfoBySupplierModel.cs" />
    <Compile Include="FxModel\SupplierProduct\SupplierProductListModel.cs" />
    <Compile Include="FxModel\SupplierProduct\SupplierProductAddModel.cs" />
    <Compile Include="FxModel\SupplierProduct\SupplierProductQueryModel.cs" />
    <Compile Include="FxModel\SupplierProduct\SupplierProductUpdateModel.cs" />
    <Compile Include="FxModel\Tools\OrderSyncAnalysisAutoReasonMessageModel.cs" />
    <Compile Include="FxModel\Tools\OrderSyncAnalysisMessageModel.cs" />
    <Compile Include="FxModel\Tools\OrderSyncAnalysisNotSameReasonModel.cs" />
    <Compile Include="FxModel\Tools\OrderSyncAnalysisTriggerSyncMessageModel.cs" />
    <Compile Include="FxModel\Tools\OrderSyncAnalysisWeiXinTriggerSyncMessageModel.cs" />
    <Compile Include="FxModel\Tools\PushDbAuthCheckMessageModel.cs" />
    <Compile Include="MessageModel\ListingCompleteMessageModel.cs" />
    <Compile Include="MessageModel\ShopAuthMessageModel.cs" />
    <Compile Include="MessageModel\TouTiaoOrderSyncAnalysisMessageModel.cs" />
    <Compile Include="Model\AgentModel.cs" />
    <Compile Include="Model\AgentProductInfoPublicSetting.cs" />
    <Compile Include="Model\AntiCrawlerConfig.cs" />
    <Compile Include="Model\BaseProductRequestModel.cs" />
    <Compile Include="Entity\CooperateStatusRecord.cs" />
    <Compile Include="Entity\CooperateEvaluate.cs" />
    <Compile Include="Entity\DataArchive\ArchiveUser.cs" />
    <Compile Include="Entity\DataArchive\DataArchive.cs" />
    <Compile Include="Entity\DataArchive\DataArchiveHistory.cs" />
    <Compile Include="Entity\DownFileRelation.cs" />
    <Compile Include="Entity\ProductPathFlowRefType.cs" />
    <Compile Include="Entity\FxUserForeignShop.cs" />
    <Compile Include="Entity\IPAreaMapping.cs" />
    <Compile Include="Entity\PrintHistoryExt.cs" />
    <Compile Include="Entity\PrintHistoryOrderExt.cs" />
    <Compile Include="Entity\PrintHistoryRecordDbConfigEntitys.cs" />
    <Compile Include="Entity\OpenPlatform\OpenApiToken.cs" />
    <Compile Include="Entity\OpenPlatform\OpenPlatformApp.cs" />
    <Compile Include="Entity\OpenPlatform\OpenPlatformAppPermission.cs" />
    <Compile Include="Entity\OpenPlatform\OpenPlatformUser.cs" />
    <Compile Include="Entity\OpenPlatform\OutOrder.cs" />
    <Compile Include="Entity\PrintOrderProductExt.cs" />
    <Compile Include="Entity\PlatformCategory.cs" />
    <Compile Include="Entity\StatSkuSettlement.cs" />
    <Compile Include="Entity\ServiceAppOrderSettlementDetail.cs" />
    <Compile Include="Entity\HotProduct\HotProductCloudMessage.cs" />
    <Compile Include="Entity\HotProduct\HotProductDbConfig.cs" />
    <Compile Include="Entity\HotProduct\HotProductDbNameConfig.cs" />
    <Compile Include="Entity\HotProduct\HotProductDbServerConfig.cs" />
    <Compile Include="Entity\HotProduct\Product.cs" />
    <Compile Include="Entity\HotProduct\ProductSaleStatistics.cs" />
    <Compile Include="Entity\HotProduct\ProductSku.cs" />
    <Compile Include="Entity\HotProduct\ProductSkuSendDetail.cs" />
    <Compile Include="Entity\SkuForProductId.cs" />
    <Compile Include="Entity\ExportTaskExt.cs" />
    <Compile Include="Entity\BusinessCard.cs" />
    <Compile Include="Entity\FxUserForeignShop.cs" />
    <Compile Include="Entity\OrderSelfDelivery.cs" />
    <Compile Include="Entity\SettlementBillExt.cs" />
    <Compile Include="EntityExtension\PaymentStatementExtension.cs" />
    <Compile Include="Entity\AfterSaleOrderRelation.cs" />
    <Compile Include="Entity\AliOpenEbillSendRecord.cs" />
    <Compile Include="Entity\AfterSaleActionItemRecord.cs" />
    <Compile Include="Entity\AfterSaleActionRecord.cs" />
    <Compile Include="Entity\BaseProduct\BaseOfPtSkuRelation.cs" />
    <Compile Include="Entity\ApiCallLog.cs" />
    <Compile Include="Entity\BaseProduct\BaseProduct.cs" />
    <Compile Include="Entity\BaseProduct\BaseProductImage.cs" />
    <Compile Include="Entity\BaseProduct\BaseProductShortTitle.cs" />
    <Compile Include="Entity\BaseProduct\BaseProductSku.cs" />
    <Compile Include="Entity\BaseProduct\BaseProductSkuAttribute.cs" />
    <Compile Include="Entity\BaseProduct\BaseProductSkuShortTitle.cs" />
    <Compile Include="Entity\BaseProduct\BaseProductSkuSupplierConfig.cs" />
    <Compile Include="Entity\BaseProduct\MessageRecord.cs" />
    <Compile Include="Entity\BaseProduct\OssObject.cs" />
    <Compile Include="Entity\BaseProduct\ProductDbConfig.cs" />
    <Compile Include="Entity\BaseProduct\ProductDbNameConfig.cs" />
    <Compile Include="Entity\BaseProduct\ProductDbServerConfig.cs" />
    <Compile Include="Entity\BaseProduct\BaseUniqueIdCode.cs" />
    <Compile Include="Entity\DistributorProductSkuMappingRecord.cs" />
    <Compile Include="Entity\ImagesRelation.cs" />
    <Compile Include="Entity\MessageHistoryRecord.cs" />
    <Compile Include="Entity\SellerRemarkHistory.cs" />
    <Compile Include="Entity\ImagesRelation.cs" />
    <Compile Include="Entity\SettlementBillQuery.cs" />
    <Compile Include="Entity\ImagesRelation.cs" />
    <Compile Include="Entity\Stat1688FxUserId.cs" />
    <Compile Include="Entity\CommonSettingRecord.cs" />
    <Compile Include="Entity\ColdHotStorage\ColdLogicOrderState.cs" />
    <Compile Include="Entity\StatCategory.cs" />
    <Compile Include="Entity\StatSendHistory.cs" />
    <Compile Include="Entity\Stat1688Shop.cs" />
    <Compile Include="Entity\Stat1688V2.cs" />
    <Compile Include="Entity\PaymentStatement.cs" />
    <Compile Include="Entity\PaymentStatementOrder.cs" />
    <Compile Include="Entity\PaymentStatementRefund.cs" />
    <Compile Include="Entity\Stat1688.cs" />
    <Compile Include="Entity\FreightTemplate\ShippingFeeTemplateChangeRecord.cs" />
    <Compile Include="Entity\FreightTemplate\ShippingFeeTemplateExtension.cs" />
    <Compile Include="Entity\OrderItemStatus.cs" />
    <Compile Include="Entity\OrderStatus.cs" />
    <Compile Include="Entity\FreightTemplate\ShippingFeeTemplateOtherRule.cs" />
    <Compile Include="Entity\FreightTemplate\ShippingFeeTemplate.cs" />
    <Compile Include="Entity\PurchaseOrderDeliveryMode.cs" />
    <Compile Include="Entity\DistributorProductMapping.cs" />
    <Compile Include="Entity\DataMigrate\StatRelationSupplier.cs" />
    <Compile Include="Entity\DistributorProductSkuMapping.cs" />
    <Compile Include="Entity\FxAlibabaBuyerShopRelation.cs" />
    <Compile Include="Entity\LogicOrderPathFlowRecord.cs" />
    <Compile Include="Entity\BranchShareUseShop.cs" />
    <Compile Include="Entity\DataMigrate\FxDataMigrateSubQuery.cs" />
    <Compile Include="Entity\DataMigrate\FxMigrateLock.cs" />
    <Compile Include="Entity\DataMigrate\PrintHistoryMigratePathFlow.cs" />
    <Compile Include="Entity\DataMigrate\TempFxDbConfig.cs" />
    <Compile Include="Entity\DataMigrate\StatPathFlow.cs" />
    <Compile Include="Entity\DataMigrate\StatOrder.cs" />
    <Compile Include="Entity\Contrast\ContrastTaskExtension.cs" />
    <Compile Include="EntityExtension\FenDanSystemNavExtension.cs" />
    <Compile Include="EntityExtension\SendFailRequestExtension.cs" />
    <Compile Include="EntityExtension\SendFailExtension.cs" />
    <Compile Include="EntityExtension\AfterSaleOrderExtension.cs" />
    <Compile Include="EntityExtension\PathFlowReferenceExtension.cs" />
    <Compile Include="EntityExtension\PathFlowExtension.cs" />
    <Compile Include="EntityExtension\ProductInfoFxExtension.cs" />
    <Compile Include="EntityExtension\ProductSkuFxExtension.cs" />
    <Compile Include="EntityExtension\SendOrderProductExtension.cs" />
    <Compile Include="EntityExtension\SettlementBillExtension.cs" />
    <Compile Include="EntityExtension\SupplierUserExtension.cs" />
    <Compile Include="EntityExtension\FxUserShopExtension.cs" />
    <Compile Include="EntityExtension\OrderLogisticInfoExtension.cs" />
    <Compile Include="EntityExtension\BranchShareRelationLogExtension.cs" />
    <Compile Include="EntityExtension\BranchShareRelationExtension.cs" />
    <Compile Include="EntityExtension\CaiNiaoAuthInfoExtension.cs" />
    <Compile Include="EntityExtension\CommonSettingExtension.cs" />
    <Compile Include="EntityExtension\CustomerOrderExtension.cs" />
    <Compile Include="EntityExtension\CustomerVisitRecordExtension.cs" />
    <Compile Include="EntityExtension\ExpressCompanyExtension.cs" />
    <Compile Include="EntityExtension\ExpressCpCodeMappingExtension.cs" />
    <Compile Include="EntityExtension\ModifyOrderPriceExtension.cs" />
    <Compile Include="EntityExtension\NaHuoLabelExtension.cs" />
    <Compile Include="EntityExtension\PrintHistoryExtension.cs" />
    <Compile Include="EntityExtension\PrintTemplateExtension.cs" />
    <Compile Include="EntityExtension\AreaCodeInfo.cs" />
    <Compile Include="EntityExtension\ProductFxExtension.cs" />
    <Compile Include="EntityExtension\ShopRelationExtension.cs" />
    <Compile Include="EntityExtension\SyncStatusExtension.cs" />
    <Compile Include="EntityExtension\WxBluetoothPrinterExtension.cs" />
    <Compile Include="EntityExtension\UserExtension.cs" />
    <Compile Include="EntityExtension\WxUserInfoExtension.cs" />
    <Compile Include="EntityExtension\ProductSkuAttributeExtension.cs" />
    <Compile Include="EntityExtension\ProductExtension.cs" />
    <Compile Include="EntityExtension\OrderItemExtension.cs" />
    <Compile Include="EntityExtension\OrderExtension.cs" />
    <Compile Include="EntityExtension\PublishOffer.cs" />
    <Compile Include="EntityExtension\PrintHistoryOrderExtension.cs" />
    <Compile Include="EntityExtension\OldMemberRelation.cs" />
    <Compile Include="EntityExtension\SendGoodTemplateExtension.cs" />
    <Compile Include="EntityExtension\SendHistoryExtension.cs" />
    <Compile Include="EntityExtension\SendHistoryOrderExtension.cs" />
    <Compile Include="EntityExtension\LogForOperatorExtension.cs" />
    <Compile Include="EntityExtension\Int32Serializer.cs" />
    <Compile Include="EntityExtension\WaybillCodeOrderExtension.cs" />
    <Compile Include="EntityExtension\WaybillCodeExtension.cs" />
    <Compile Include="Entity\Advertisement.cs" />
    <Compile Include="Entity\MessageProcessLog.cs" />
    <Compile Include="Entity\Contrast\ContrastTaskItem.cs" />
    <Compile Include="Entity\Contrast\ContrastTask.cs" />
    <Compile Include="Entity\DataMigrate\FxDataMigrateSubTask.cs" />
    <Compile Include="Entity\DataMigrate\FxDataMigrateTask.cs" />
    <Compile Include="Entity\OrderDeliverException.cs" />
    <Compile Include="Entity\OrderExtra.cs" />
    <Compile Include="Entity\OrderModule\OrderAbnormal.cs" />
    <Compile Include="Entity\OrderModule\OrderAbnormalPathFlowNode.cs" />
    <Compile Include="Entity\PlatformAreaCodeInfo.cs" />
    <Compile Include="Entity\DeliveryModeChangeRecord.cs" />
    <Compile Include="Entity\PurchaseOrderItemRelation.cs" />
    <Compile Include="Entity\QingUserRole.cs" />
    <Compile Include="Entity\PurchaseOrderLogisticsInfo.cs" />
    <Compile Include="Entity\PurchaseOrderModifyPriceRecord.cs" />
    <Compile Include="Entity\PrepayStatusChangeRecord.cs" />
    <Compile Include="Entity\PurchaseOrderRelation.cs" />
    <Compile Include="Entity\SendHistoryReturnRecord.cs" />
    <Compile Include="Entity\SendHistoryReturnRecordBase.cs" />
    <Compile Include="Entity\SettlementInfo.cs" />
    <Compile Include="Entity\DataChangeLogEntity\DataChangeDbConfigModel.cs" />
    <Compile Include="Entity\DataChangeLogEntity\DataChangeLog.cs" />
    <Compile Include="Entity\DataChangeLogEntity\DataChangeLogEnums.cs" />
    <Compile Include="Entity\DataChangeLogEntity\DataChangeDbConfigEntitys.cs" />
    <Compile Include="Entity\DataSyncStatus\DataSyncStatus.cs" />
    <Compile Include="Entity\DataSyncStatus\DataSyncStatusHistory.cs" />
    <Compile Include="Entity\ExpressReachShare.cs" />
    <Compile Include="Entity\FenDanSystemNav.cs" />
    <Compile Include="Entity\BranchShareRelationOtherInfo.cs" />
    <Compile Include="Entity\FxUnBindTask.cs" />
    <Compile Include="Entity\QuickSearch.cs" />
    <Compile Include="Entity\PrintHistoryData.cs" />
    <Compile Include="Entity\PrintHistoryDbConfigEntitys.cs" />
    <Compile Include="Entity\ReceDbConfigEntitys.cs" />
    <Compile Include="Entity\Log\TokenBucketLog.cs" />
    <Compile Include="Entity\EBillAccountExtension.cs" />
    <Compile Include="Entity\Receiver.cs" />
    <Compile Include="Entity\OrderTags.cs" />
    <Compile Include="Entity\PerformanceLog.cs" />
    <Compile Include="Entity\SendHistoryRepeatRecord.cs" />
    <Compile Include="Entity\SendHistoryChild.cs" />
    <Compile Include="Entity\AsyncTask.cs" />
    <Compile Include="Entity\AfterSaleOrderItem.cs" />
    <Compile Include="Entity\AfterSaleEvidence.cs" />
    <Compile Include="Entity\AfterSaleOrder.cs" />
    <Compile Include="Entity\AsyncDelivery.cs" />
    <Compile Include="Entity\FxServiceVersion\LimitedFunctions.cs" />
    <Compile Include="Entity\FxServiceVersion\ServiceFunctions.cs" />
    <Compile Include="Entity\FxServiceVersion\ServiceVersion.cs" />
    <Compile Include="Entity\FxServiceVersion\ServiceVersionEnumTypes.cs" />
    <Compile Include="Entity\FxServiceVersion\UserOrderBalanceUsedExceptionRecord.cs" />
    <Compile Include="Entity\FxServiceVersion\UserOrderCountBalance.cs" />
    <Compile Include="Entity\FxServiceVersion\UserOrderLock.cs" />
    <Compile Include="Entity\FxServiceVersion\UserOrderLockRecord.cs" />
    <Compile Include="Entity\FxServiceVersion\UserOrderUsedRecord.cs" />
    <Compile Include="Entity\FxServiceVersion\UserServiceVersionMapping.cs" />
    <Compile Include="Entity\FxServiceVersion\UserSpecialFunctionMapping.cs" />
    <Compile Include="Entity\OrderPromise.cs" />
    <Compile Include="Entity\SendFailRequest.cs" />
    <Compile Include="Entity\KuaiShouEncryptedReceiverInfo.cs" />
    <Compile Include="Entity\SendFail.cs" />
    <Compile Include="Entity\ManualOrder\OrderCheckModel.cs" />
    <Compile Include="Entity\ManualOrder\OrderCheckRuleModel.cs" />
    <Compile Include="Entity\ManualOrder\OrderManualRecordModel.cs" />
    <Compile Include="Entity\OrderItemExt.cs" />
    <Compile Include="Entity\OrderItemStockRecord.cs" />
    <Compile Include="Entity\CommonSettingExtension.cs" />
    <Compile Include="Entity\OrderSplitRecord.cs" />
    <Compile Include="Entity\PathFlowChangeLog.cs" />
    <Compile Include="Entity\PathFlowReferenceConfig.cs" />
    <Compile Include="Entity\ProdcutSettlementPrice.cs" />
    <Compile Include="Entity\ProductSettlementRecord.cs" />
    <Compile Include="Entity\ProductSkuFxAndProduct.cs" />
    <Compile Include="Entity\ImportBatchHistory.cs" />
    <Compile Include="Entity\ImportOrderBatch.cs" />
    <Compile Include="Entity\ProductSkuInfoFx.cs" />
    <Compile Include="Entity\ProductInfoFx.cs" />
    <Compile Include="Entity\FxUserAddress.cs" />
    <Compile Include="Entity\FxUserShop.cs" />
    <Compile Include="Entity\LogicOrder.cs" />
    <Compile Include="Entity\LogicOrderItem.cs" />
    <Compile Include="Entity\LogisticTraceSubscribleLog.cs" />
    <Compile Include="Entity\Agent.cs" />
    <Compile Include="Entity\OptimisticLock.cs" />
    <Compile Include="Entity\PathFlowReference.cs" />
    <Compile Include="Entity\PathFlow.cs" />
    <Compile Include="Entity\PathFlowNode.cs" />
    <Compile Include="Entity\ProductFx.cs" />
    <Compile Include="Entity\ProductImage.cs" />
    <Compile Include="Entity\ProductNodeRelation.cs" />
    <Compile Include="Entity\ProductSkuFx.cs" />
    <Compile Include="Entity\ReceiverMaskData.cs" />
    <Compile Include="Entity\SendFailOrder.cs" />
    <Compile Include="Entity\SendFailOrderProduct.cs" />
    <Compile Include="Entity\SettlementBill.cs" />
    <Compile Include="Entity\SettlementBillRecord.cs" />
    <Compile Include="Entity\ObsoleteSettlementSku.cs" />
    <Compile Include="Entity\SettlementProductSku.cs" />
    <Compile Include="Entity\PhShop.cs" />
    <Compile Include="Entity\ShopAuthHistory.cs" />
    <Compile Include="Entity\ShopExtension.cs" />
    <Compile Include="Entity\SupplierProduct\UserSupplierStatus.cs" />
    <Compile Include="Entity\SubAccount\PostPermissionFx.cs" />
    <Compile Include="Entity\SubAccount\SysPermissionFx.cs" />
    <Compile Include="Entity\SubAccount\UserFxPostRelation.cs" />
    <Compile Include="Entity\SubAccount\PostFx.cs" />
    <Compile Include="Entity\SupplierUser.cs" />
    <Compile Include="Entity\SyncQueryTask.cs" />
    <Compile Include="Entity\BranchShareExportExcelTask.cs" />
    <Compile Include="Entity\BranchShareUsedStatistic.cs" />
    <Compile Include="Entity\ExportTask.cs" />
    <Compile Include="Entity\ShareWaybillRemedyData.cs" />
    <Compile Include="Entity\DbApiRequestLog.cs" />
    <Compile Include="Entity\SubUser.cs" />
    <Compile Include="Entity\DbConfigEntitys.cs" />
    <Compile Include="Entity\OrderQuantityInfo.cs" />
    <Compile Include="Entity\ExpressImportMapping.cs" />
    <Compile Include="Entity\BranchShareRelationLog.cs" />
    <Compile Include="Entity\NaHuoLabelTemplate.cs" />
    <Compile Include="Entity\NaHuoLabelSortingRules.cs" />
    <Compile Include="Entity\NaHuoLabelOrderItem.cs" />
    <Compile Include="Entity\NaHuoLabelOrder.cs" />
    <Compile Include="Entity\NaHuoLabelBatch.cs" />
    <Compile Include="Entity\ShareBranchContactInfo.cs" />
    <Compile Include="Entity\ShareWaybillAccountCheckingRecord.cs" />
    <Compile Include="Entity\ShareWaybillCodeRecord.cs" />
    <Compile Include="Entity\SyncOrderProcess.cs" />
    <Compile Include="Entity\AdvWhiteList.cs" />
    <Compile Include="Entity\AdvLog.cs" />
    <Compile Include="Entity\ServiceAppOrder.cs" />
    <Compile Include="Entity\PddServiceOrderPushLog.cs" />
    <Compile Include="Entity\CommentOrder.cs" />
    <Compile Include="Entity\CustomerStaff.cs" />
    <Compile Include="Entity\CustomerVisitRecord.cs" />
    <Compile Include="Entity\DatabaseConfig.cs" />
    <Compile Include="Entity\EvaluateOrder.cs" />
    <Compile Include="Entity\FengQiaoOrderData.cs" />
    <Compile Include="Entity\KuaiDiNiaoLogisticTraces.cs" />
    <Compile Include="Entity\LogisticAccountInfoMappings.cs" />
    <Compile Include="Entity\InvitedMigrateShop.cs" />
    <Compile Include="Entity\LogForOperator.cs" />
    <Compile Include="Entity\LogisticAddService.cs" />
    <Compile Include="Entity\LogisticBusinessTypes.cs" />
    <Compile Include="Entity\LogisticPayTypes.cs" />
    <Compile Include="Entity\LogisticsAppInfo.cs" />
    <Compile Include="Entity\LogisticTraces.cs" />
    <Compile Include="Entity\ModifyOrderPrice.cs" />
    <Compile Include="Entity\ModifyOrderPriceItem.cs" />
    <Compile Include="Entity\OrderLogisticInfo.cs" />
    <Compile Include="Entity\OrderSyncLog.cs" />
    <Compile Include="Entity\CaiNiaoAccountBranch.cs" />
    <Compile Include="Entity\MacAddress.cs" />
    <Compile Include="Entity\ExpressCpCodeMapping.cs" />
    <Compile Include="Entity\ExpressSend.cs" />
    <Compile Include="Entity\BranchShareRelation.cs" />
    <Compile Include="Entity\AreaCodeInfo.cs" />
    <Compile Include="EntityExtension\ShopExtension.cs" />
    <Compile Include="EntityExtension\SubOrderExtension.cs" />
    <Compile Include="Entity\BaseEntity.cs" />
    <Compile Include="Entity\CaiNiaoAuthInfo.cs" />
    <Compile Include="Entity\CainiaoAuthOwner.cs" />
    <Compile Include="Entity\CaiNiaoAuthRelation.cs" />
    <Compile Include="Entity\CommonSetting.cs" />
    <Compile Include="Entity\CustomColumnExcel.cs" />
    <Compile Include="Entity\CustomerColumnMapping.cs" />
    <Compile Include="Entity\CustomerOrder.cs" />
    <Compile Include="Entity\CustomerOrderItem.cs" />
    <Compile Include="Entity\OldSendGoodTemplate.cs" />
    <Compile Include="Entity\PrintTemplateExtend.cs" />
    <Compile Include="Entity\PlatformRemark.cs" />
    <Compile Include="Entity\SyncStatus.cs" />
    <Compile Include="Entity\SyncTask.cs" />
    <Compile Include="Entity\TaskParamJson.cs" />
    <Compile Include="Entity\TemplatePackageInfo.cs" />
    <Compile Include="Entity\Tools\DbTableMetaData.cs" />
    <Compile Include="Entity\Tools\OrderLifeCycleToolReason.cs" />
    <Compile Include="Entity\UniversalModule\FxMessageDataSyncStatus.cs" />
    <Compile Include="Entity\BusinessCardRemark.cs" />
    <Compile Include="Entity\UserFxLoginRegionRecord.cs" />
    <Compile Include="Entity\UserActionRecord.cs" />
    <Compile Include="Entity\SubUserFx.cs" />
    <Compile Include="Entity\WaybillRealCpCode.cs" />
    <Compile Include="Entity\WeChatQRCode.cs" />
    <Compile Include="Entity\WeChatUserInfo.cs" />
    <Compile Include="Entity\UserFx.cs" />
    <Compile Include="Entity\WxUserFxRelation.cs" />
    <Compile Include="Entity\WareHouseRelation.cs" />
    <Compile Include="Entity\WareHouseSkuBindRelation.cs" />
    <Compile Include="Entity\WaybillCodeOrderProduct.cs" />
    <Compile Include="Entity\WaybillCodeUseRecord.cs" />
    <Compile Include="Entity\WxAppIdToken.cs" />
    <Compile Include="Entity\OpenPlatformApp.cs" />
    <Compile Include="Entity\WxBluetoothPrinter.cs" />
    <Compile Include="Entity\UserVerificationCode.cs" />
    <Compile Include="Entity\UserShopRelation.cs" />
    <Compile Include="Entity\LinkArea.cs" />
    <Compile Include="Entity\CommonQRCode.cs" />
    <Compile Include="Entity\WxVisitingCardQRCode.cs" />
    <Compile Include="Entity\WxUserInfo.cs" />
    <Compile Include="Entity\WxQRcode.cs" />
    <Compile Include="Entity\WxConcern.cs" />
    <Compile Include="Entity\PurchaseConfig.cs" />
    <Compile Include="Entity\PrinterBind.cs" />
    <Compile Include="Entity\PrintHistoryOrder.cs" />
    <Compile Include="Entity\ProductAttribute.cs" />
    <Compile Include="Entity\ProductStatus.cs" />
    <Compile Include="Entity\ReciverInfo.cs" />
    <Compile Include="Entity\MigrateShop.cs" />
    <Compile Include="Entity\OldSupplier.cs" />
    <Compile Include="Entity\OldOfferTitle.cs" />
    <Compile Include="Entity\LoginAuthToken.cs" />
    <Compile Include="Entity\TemplateLogisticsServices.cs" />
    <Compile Include="Entity\ExpressCodeMapping.cs" />
    <Compile Include="Entity\ExpressCompany.cs" />
    <Compile Include="Entity\LastCodeBuild.cs" />
    <Compile Include="Entity\OpenSecrecySeller.cs" />
    <Compile Include="Entity\Order.cs" />
    <Compile Include="Entity\OrderCategory.cs" />
    <Compile Include="Entity\OrderFilter.cs" />
    <Compile Include="Entity\OrderItem.cs" />
    <Compile Include="Entity\OrderModified.cs" />
    <Compile Include="Entity\Preordain.cs" />
    <Compile Include="Entity\PrintControl.cs" />
    <Compile Include="Entity\PrintHistory.cs" />
    <Compile Include="Entity\PrintOrderProduct.cs" />
    <Compile Include="Entity\PrintTemplate.cs" />
    <Compile Include="Entity\Product.cs" />
    <Compile Include="Entity\ProductSku.cs" />
    <Compile Include="Entity\PurchaseHistory.cs" />
    <Compile Include="Entity\SellerInfo.cs" />
    <Compile Include="Entity\SendGoodTemplate.cs" />
    <Compile Include="Entity\SendHistory.cs" />
    <Compile Include="Entity\SendOrderProduct.cs" />
    <Compile Include="Entity\Shop.cs" />
    <Compile Include="Entity\ShopRelation.cs" />
    <Compile Include="Entity\SkuAttribute.cs" />
    <Compile Include="Entity\StapleTemplate.cs" />
    <Compile Include="Entity\Supplier.cs" />
    <Compile Include="Entity\SynUserInfo.cs" />
    <Compile Include="Entity\SysConfig.cs" />
    <Compile Include="Entity\AppOrderList.cs" />
    <Compile Include="Entity\UserInfo.cs" />
    <Compile Include="Entity\User.cs" />
    <Compile Include="Entity\WaybillCodeCheck.cs" />
    <Compile Include="Entity\UserSiteInfo.cs" />
    <Compile Include="Entity\WaybillCode.cs" />
    <Compile Include="Entity\TemplateRelationAuthInfo.cs" />
    <Compile Include="Entity\SendHistoryOrder.cs" />
    <Compile Include="Entity\WaybillCodeChild.cs" />
    <Compile Include="Entity\WaybillCustomArea.cs" />
    <Compile Include="Entity\WaybillCodeOrder.cs" />
    <Compile Include="Entity\WxVisitingCard.cs" />
    <Compile Include="Entity\WxXiaDanRelation.cs" />
    <Compile Include="Enum\SourceDbType.cs" />
    <Compile Include="Enum\OpenApiErrCode.cs" />
    <Compile Include="Enum\OpenPlatformStatus.cs" />
    <Compile Include="Enum\PlatformAppScene.cs" />
    <Compile Include="Enum\BusinessCardExtFlag.cs" />
    <Compile Include="Enum\ActionType.cs" />
    <Compile Include="Enum\ApplyPrepayPop.cs" />
    <Compile Include="Enum\SendHistoryOrderByFieldEnum.cs" />
    <Compile Include="Enum\HotProductStatisticsType.cs" />
    <Compile Include="Enum\ShopLockType.cs" />
    <Compile Include="Enum\IdentcodeEnum.cs" />
    <Compile Include="Enum\BaseProduct\BaseProductMsgType.cs" />
    <Compile Include="Enum\BaseProduct\SkuCodeType.cs" />
    <Compile Include="Enum\PayChannelEnum.cs" />
    <Compile Include="Enum\BusinessSettingKeys.cs" />
    <Compile Include="Enum\AbnormalOrderReasons.cs" />
    <Compile Include="Enum\AbnormalOrderTypes.cs" />
    <Compile Include="Enum\BusinessSettingTypes.cs" />
    <Compile Include="Enum\BusinessTypes.cs" />
    <Compile Include="Enum\DataDuplicationEnvironment.cs" />
    <Compile Include="Enum\ExchangeNames.cs" />
    <Compile Include="Enum\AbnormalOrderSources.cs" />
    <Compile Include="Enum\MessageSyncBusinessTypes.cs" />
    <Compile Include="Enum\MigrateType.cs" />
    <Compile Include="Enum\PlatformAppTypeEnum.cs" />
    <Compile Include="Enum\OrderTrackType.cs" />
    <Compile Include="Enum\AfterSaleEnum.cs" />
    <Compile Include="Enum\CacheFieldKeys.cs" />
    <Compile Include="Enum\CacheKeys.cs" />
    <Compile Include="Enum\CloudPlatformType.cs" />
    <Compile Include="Enum\DouyinEncryptType.cs" />
    <Compile Include="Enum\EncryptFromType.cs" />
    <Compile Include="Enum\AsyncDeliveryFlagType.cs" />
    <Compile Include="Enum\EventTrackingOperationType.cs" />
    <Compile Include="Enum\EventTrackingType.cs" />
    <Compile Include="Enum\ExportType.cs" />
    <Compile Include="Enum\FxEnum.cs" />
    <Compile Include="Enum\KuaiShouEncryptType.cs" />
    <Compile Include="Enum\LogStoreNames.cs" />
    <Compile Include="Enum\MessageResultType.cs" />
    <Compile Include="Enum\MigrateStatus.cs" />
    <Compile Include="Enum\MigrateTaskName.cs" />
    <Compile Include="Enum\OptimisticLockOperationType.cs" />
    <Compile Include="Enum\OrderChangedType.cs" />
    <Compile Include="Enum\ContrastTaskEnum.cs" />
    <Compile Include="Enum\OrderTag.cs" />
    <Compile Include="Enum\ProductStatusType.cs" />
    <Compile Include="Enum\QueueNames.cs" />
    <Compile Include="Enum\ReturnedStatus.cs" />
    <Compile Include="Enum\RobotUrls.cs" />
    <Compile Include="Enum\RoutingKeys.cs" />
    <Compile Include="Enum\SelectFieldName.cs" />
    <Compile Include="Enum\ServicePayStatusType.cs" />
    <Compile Include="Enum\OrderStatusType.cs" />
    <Compile Include="Enum\AppOrderProductCodeType.cs" />
    <Compile Include="Enum\DatabaseTypeEnum.cs" />
    <Compile Include="Enum\PlatformLogisticStatus.cs" />
    <Compile Include="Enum\PlatformType.cs" />
    <Compile Include="Enum\PrintTemplateType.cs" />
    <Compile Include="Enum\RefundStatusType.cs" />
    <Compile Include="Enum\ShopMigrateStatusType.cs" />
    <Compile Include="Enum\OrderTableType.cs" />
    <Compile Include="Enum\SiteMessageTypeEnum.cs" />
    <Compile Include="Enum\SyncBusinessTypes.cs" />
    <Compile Include="Enum\SyncMode.cs" />
    <Compile Include="Enum\Stat1688Enum.cs" />
    <Compile Include="Enum\StockChangeRecordType.cs" />
    <Compile Include="Enum\SyncStatusType.cs" />
    <Compile Include="Enum\SystemSettingKeys.cs" />
    <Compile Include="Enum\UpdateFieldName.cs" />
    <Compile Include="Enum\VolcanoLogProject.cs" />
    <Compile Include="Extension\CommonSettingRedisNewRepository.cs" />
    <Compile Include="Extension\CommonSettingRedisRepository.cs" />
    <Compile Include="Extension\CustomerConfigExt.cs" />
    <Compile Include="Extension\DataChangeLogExtension.cs" />
    <Compile Include="Extension\DbPolicyExtension.cs" />
    <Compile Include="Extension\QingServiceBase.cs" />
    <Compile Include="FxModel\ColdHotStorageResultModel.cs" />
    <Compile Include="FxModel\FxUserShopQueryModel.cs" />
    <Compile Include="FxModel\ApplyPrepayPopModel.cs" />
    <Compile Include="FxModel\DataSyncStorageMessageModel.cs" />
    <Compile Include="FxModel\DuplicationSwitchConfigModel.cs" />
    <Compile Include="FxModel\BusinessCardWithRemarkModel.cs" />
    <Compile Include="FxModel\AbnormalOrderRemindModel.cs" />
    <Compile Include="FxModel\ExportTranslateModel.cs" />
    <Compile Include="FxModel\FxUserNickMobileModel.cs" />
    <Compile Include="FxModel\FxUserSystemVersionModel.cs" />
    <Compile Include="FxModel\ImportSettlementPriceHandleMessageModel.cs" />
    <Compile Include="FxModel\LogicOrderAndColdHotTypeModel.cs" />
    <Compile Include="FxModel\LogisticsBillNoOrderItemModel.cs" />
    <Compile Include="FxModel\OperationOptimisticLockValueModel.cs" />
    <Compile Include="FxModel\PrintscreenPic.cs" />
    <Compile Include="FxModel\PurchaseOrderForWangShangPayModel.cs" />
    <Compile Include="FxModel\OrderAbnormalMessageModel.cs" />
    <Compile Include="FxModel\Common\PagingResultModel.cs" />
    <Compile Include="FxModel\Common\PagingQueryModel.cs" />
    <Compile Include="FxModel\SendHistoryBySettlementModel.cs" />
    <Compile Include="FxModel\ShippingFeeTemplateRule.cs" />
    <Compile Include="FxModel\OfflineOrderSwitchModel.cs" />
    <Compile Include="FxModel\PlatformOrderIdAndProductCountModel.cs" />
    <Compile Include="FxModel\PurchaseOrderStatusHandleMessageModel.cs" />
    <Compile Include="FxModel\ShopWithLogicOrderIdModel.cs" />
    <Compile Include="FxModel\LogicOrderSendMessageModel.cs" />
    <Compile Include="FxModel\ManualPayUrlWithLogicOrdersModel.cs" />
    <Compile Include="FxModel\PurchasePasswordFreePayResultModel.cs" />
    <Compile Include="FxModel\PurchaseOrderSendApiModel.cs" />
    <Compile Include="FxModel\PurchaseOrderSendDataModel.cs" />
    <Compile Include="FxModel\SetDefaultSettlementPriceModel.cs" />
    <Compile Include="FxModel\SystemSettingValueModel.cs" />
    <Compile Include="FxModel\UpdateRemarksAndFlagModel.cs" />
    <Compile Include="FxModel\AfterSaleOrderStockMessageModel.cs" />
    <Compile Include="FxModel\SubUserFxResModel.cs" />
    <Compile Include="FxModel\WxFxUserResponseModel.cs" />
    <Compile Include="FxModel\FxExportTaskModule.cs" />
    <Compile Include="FxModel\ExpressReachSettingModel.cs" />
    <Compile Include="FxModel\FxUserVersionInfoModel.cs" />
    <Compile Include="FxModel\GeneralizeIndexTotalModel.cs" />
    <Compile Include="FxModel\LogicMainOrderModel.cs" />
    <Compile Include="FxModel\LogicOrderChangeModel.cs" />
    <Compile Include="FxModel\LogicOrderNodeInfoModel.cs" />
    <Compile Include="FxModel\LogicOrderStock.cs" />
    <Compile Include="FxModel\ProductFxQueryModel.cs" />
    <Compile Include="FxModel\RepairOrUpdateModel.cs" />
    <Compile Include="FxModel\PathFlowChangeLogModel.cs" />
    <Compile Include="FxModel\PathFlowReferenceConfigModel.cs" />
    <Compile Include="FxModel\SendOrderHistoryModel.cs" />
    <Compile Include="FxModel\AgentShopModule.cs" />
    <Compile Include="FxModel\ServiceMessageCounterModel.cs" />
    <Compile Include="FxModel\ServiceMessageModel.cs" />
    <Compile Include="FxModel\ServiceProductMessageModel.cs" />
    <Compile Include="FxModel\SettlementBillModel.cs" />
    <Compile Include="FxModel\SupplierModule.cs" />
    <Compile Include="FxModel\ResendLogicOrderUpdateRequest.cs" />
    <Compile Include="FxModel\SettlementExcelModel.cs" />
    <Compile Include="FxModel\SettlementModel.cs" />
    <Compile Include="FxModel\SplitLogicOrderChangeModel.cs" />
    <Compile Include="FxModel\MergerLogicOrderChangeModel.cs" />
    <Compile Include="FxModel\LogicOrderItemChangeModel.cs" />
    <Compile Include="FxModel\BindSupplierRequestModel.cs" />
    <Compile Include="FxModel\LogicOrderFirstNodeInfoModel.cs" />
    <Compile Include="FxModel\OrderModel.cs" />
    <Compile Include="FxModel\ProductFxModule.cs" />
    <Compile Include="FxModel\ProductFxRequertModule.cs" />
    <Compile Include="FxModel\RepariSendHistory.cs" />
    <Compile Include="FxModel\UpdateLogicOrderSendStateModel.cs" />
    <Compile Include="FxModel\UpdatePrintStateModel.cs" />
    <Compile Include="FxModel\WaybillCodeLoadListModel.cs" />
    <Compile Include="FxModel\SystemDataModel.cs" />
    <Compile Include="IRepository\IBaseRepository.cs" />
    <Compile Include="IRepository\ILogicOrderRepository.cs" />
    <Compile Include="IRepository\IOrderRepository.cs" />
    <Compile Include="MessageModel\AfterSaleActionMessageModel.cs" />
    <Compile Include="MessageModel\OnlineSendMessageHandleResultModel.cs" />
    <Compile Include="MessageModel\OnlineSendMessageModel.cs" />
    <Compile Include="MessageModel\OrderMessageModel.cs" />
    <Compile Include="MessageModel\SendHistoryReturnAuthMessageModel.cs" />
    <Compile Include="Model\BatchSettlementPopupReq.cs" />
    <Compile Include="Model\BatchSettlementPopupModel.cs" />
    <Compile Include="Model\CrossBorder\TikTokInvoicesInModel.cs" />
    <Compile Include="Model\CrossBorder\TikTokUploadInvoiceInModel.cs" />
    <Compile Include="Model\CrossBorder\TkGlobalProductListRequest.cs" />
    <Compile Include="Model\BaseProduct\BaseProductResultModel.cs" />
    <Compile Include="Model\CombinedProductChangeLog\CombinedProductChangeLogReq.cs" />
    <Compile Include="Model\CombinedProductChangeLog\CombinedProductChangeLogRes.cs" />
    <Compile Include="Model\CrossBorder\WebhookInvoiceStatusChange.cs" />
    <Compile Include="Model\ExcelUploadConfig.cs" />
    <Compile Include="Model\FxAuthorizeModel.cs" />
    <Compile Include="MessageModel\SiteMessageContentModel.cs" />
    <Compile Include="MessageModel\SiteMessageModel.cs" />
    <Compile Include="Model\AbnormalOrderRequestModel.cs" />
    <Compile Include="FxModel\PurchaseOrderRelationModel.cs" />
    <Compile Include="Model\AlibabaFxModel\CreateRefundRequest.cs" />
    <Compile Include="Model\AfterSaleActionRecordModel.cs" />
    <Compile Include="Model\BaseProduct\CalculatePtProductInfoDraftModel.cs" />
    <Compile Include="Model\ExpressSustenanceModel.cs" />
    <Compile Include="Model\ExportTaskMessageModel.cs" />
    <Compile Include="Model\FreightTemplate\BaseFreightTemplateRequest.cs" />
    <Compile Include="Model\FreightTemplate\BaseFreightTemplateRespone.cs" />
    <Compile Include="Model\FreightTemplate\BaseRequest.cs" />
    <Compile Include="Model\FreightTemplate\ExpShippingFeeTemplateRule.cs" />
    <Compile Include="Model\FreightTemplate\IFreightTemplateRequest.cs" />
    <Compile Include="Model\FreightTemplate\Request\BatchAddRequest.cs" />
    <Compile Include="Model\FreightTemplate\Request\BatchDeleteRequest.cs" />
    <Compile Include="Model\FreightTemplate\Request\ChangeRecordListRequest.cs" />
    <Compile Include="Model\FreightTemplate\Request\ComputeModelRequest.cs" />
    <Compile Include="Model\FreightTemplate\Request\ExpListRequest.cs" />
    <Compile Include="Model\CrossBorder\CollectBoxProductRes.cs" />
    <Compile Include="Model\CrossBorder\TikTokGlobalProductCreate.cs" />
    <Compile Include="Model\CrossBorder\TikTokGlobalProductDetail.cs" />
    <Compile Include="Model\CrossBorder\TikTokGlobalProductEdit.cs" />
    <Compile Include="Model\CrossBorder\TikTokGlobalProductList.cs" />
    <Compile Include="Model\CrossBorder\TikTokProductFile.cs" />
    <Compile Include="Model\CrossBorder\TikTokProductImage.cs" />
    <Compile Include="Model\CrossBorder\TikTokPublishGlobalProduct.cs" />
    <Compile Include="Model\CrossBorder\UploadClaimProductFileModel.cs" />
    <Compile Include="Model\CrossBorder\UploadClaimProductImageModel.cs" />
    <Compile Include="Model\CrossBorder\UploadGlobalProductFileModel.cs" />
    <Compile Include="Model\CrossBorder\UploadGlobalProductImageModel.cs" />
    <Compile Include="Model\CrossBorder\CrossProductAttribute.cs" />
    <Compile Include="Model\FreightTemplate\Request\ListRequest.cs" />
    <Compile Include="Model\FreightTemplate\Request\ReckonModelRequest.cs" />
    <Compile Include="Model\FreightTemplate\Response\BatchAddResponse.cs" />
    <Compile Include="Model\FreightTemplate\Response\ChangeRecordListResponse.cs" />
    <Compile Include="Model\FreightTemplate\Response\ComputeModelResponse.cs" />
    <Compile Include="Model\FreightTemplate\Response\ExpListResponse.cs" />
    <Compile Include="Model\FreightTemplate\Response\ListResponse.cs" />
    <Compile Include="Model\FreightTemplate\Response\ReckonModelResponse.cs" />
    <Compile Include="Model\GetAfterSalesAddressesListReq.cs" />
    <Compile Include="Model\GetTkPrintProcessStateRecordsRequestModel.cs" />
    <Compile Include="Model\GetTkPrintProcessStateRecordsResModel.cs" />
    <Compile Include="Model\JingdongEbillRecipientAddressInfo.cs" />
    <Compile Include="Model\LogModel\CommonLogModel.cs" />
    <Compile Include="Model\HotProduct\HotProductCloudMessageModel.cs" />
    <Compile Include="Model\HotProduct\HotProductDbConfigModel.cs" />
    <Compile Include="Model\BaseProduct\BaseProductRelationModifyModel.cs" />
    <Compile Include="Model\CloudMessageQuery.cs" />
    <Compile Include="Model\BaseProduct\BaseProductSkuChildModel.cs" />
    <Compile Include="Model\BaseProduct\BpRelRecordModel.cs" />
    <Compile Include="Model\BaseProduct\BaseProductResModel.cs" />
    <Compile Include="Model\BaseProduct\BaseProductSearchRes.cs" />
    <Compile Include="Model\BaseProduct\BaseProductSearchSimpleRes.cs" />
    <Compile Include="Model\BaseProduct\BaseProductSearchSpuRes.cs" />
    <Compile Include="Model\BaseProduct\BaseProductSettingsModel.cs" />
    <Compile Include="Model\BaseProduct\BaseProductSkuBindSearchModelRes.cs" />
    <Compile Include="Model\BaseProduct\BaseProductSkuResModel.cs" />
    <Compile Include="Model\BaseProduct\BaseProductSkuSearchRes.cs" />
    <Compile Include="Model\BaseProduct\BaseProductSkuSimpleRes.cs" />
    <Compile Include="Model\BaseProduct\BaseProductSkuSimpleReq.cs" />
    <Compile Include="Model\LogModel\ServiceQueueMonitorLogModel.cs" />
    <Compile Include="Model\LogModel\SplitLogicOrderHistoryLogModel.cs" />
    <Compile Include="Model\LogModel\SplitLogicOrderPathFlowExLogModel.cs" />
    <Compile Include="Model\LogModel\SyncServiceTaskLogModel.cs" />
    <Compile Include="Model\MaxDegreeOfParallelismConfig.cs" />
    <Compile Include="Model\LogModel\SyncStatusLogModel.cs" />
    <Compile Include="Model\MergeOrderQueryModel.cs" />
    <Compile Include="Model\OpenPlatform\AgentListRespone.cs" />
    <Compile Include="Model\OpenPlatform\AllAfterSaleListRespone.cs" />
    <Compile Include="Model\OpenPlatform\AllOrderListRespone.cs" />
    <Compile Include="Model\OpenPlatform\QueryAgentListRequest.cs" />
    <Compile Include="Model\OpenPlatform\QuerySupplierWaybillListRequest.cs" />
    <Compile Include="Model\OpenPlatform\QueryWaybillListRequest.cs" />
    <Compile Include="Model\OpenPlatform\UploadBaseProductResponse.cs" />
    <Compile Include="Model\OpenPlatform\WaybillListRespone.cs" />
    <Compile Include="Model\OpenPlatform\BaseRequest.cs" />
    <Compile Include="Model\OpenPlatform\BindSupplierRequest.cs" />
    <Compile Include="Model\OpenPlatform\OpenPlatformMsgRequest.cs" />
    <Compile Include="Model\OpenPlatform\OrderListRespone.cs" />
    <Compile Include="Model\OpenPlatform\OutNoticeSettingModel.cs" />
    <Compile Include="Model\OpenPlatform\PartnerListRespone.cs" />
    <Compile Include="Model\OpenPlatform\PutLogicOrderRemarkRequest.cs" />
    <Compile Include="Model\OpenPlatform\QueryAllAfterSaleListRequest.cs" />
    <Compile Include="Model\OpenPlatform\QueryAllOrderListRequest.cs" />
    <Compile Include="Model\OpenPlatform\QueryOrderListRequest.cs" />
    <Compile Include="Model\OpenPlatform\QueryPartnerListRequest.cs" />
    <Compile Include="Model\OpenPlatform\QueryPartnerProductListRequest.cs" />
    <Compile Include="Model\OpenPlatform\QueryStockListRequest.cs" />
    <Compile Include="Model\OpenPlatform\ShopListRespone.cs" />
    <Compile Include="Model\OpenPlatform\StockListRespone.cs" />
    <Compile Include="Model\OpenPlatform\SupplierProductListRespone.cs" />
    <Compile Include="Model\OpenPlatform\UploadBaseProductRequest.cs" />
    <Compile Include="Model\OpenPlatform\UploadOrderRequest.cs" />
    <Compile Include="Model\OpenPlatform\UploadProductRequest.cs" />
    <Compile Include="Model\ImportSetSettlementPriceModel.cs" />
    <Compile Include="Model\FxOrder\LogicOrderHandSplitModel.cs" />
    <Compile Include="Model\OrderExcelUploadRequest.cs" />
    <Compile Include="Model\OtherPlatforms\HaoYouDuo\GoodsInfo.cs" />
    <Compile Include="Model\OtherPlatforms\HaoYouDuo\OrderInfo.cs" />
    <Compile Include="Model\OtherPlatforms\HaoYouDuo\OrderUpdate.cs" />
    <Compile Include="Model\OtherPlatforms\HaoYouDuo\ShopInfo.cs" />
    <Compile Include="Model\OtherPlatforms\HaoYouDuo\OrderInfo.cs" />
    <Compile Include="Model\OtherPlatforms\HaoYouDuo\OrderUpdate.cs" />
    <Compile Include="Model\OtherPlatforms\HaoYouDuo\ShopInfo.cs" />
    <Compile Include="Model\ServiceRunStatusModel.cs" />
    <Compile Include="Model\SendCheckResponseModel.cs" />
    <Compile Include="Model\OpenTelemetry\OtlpLogView.cs" />
    <Compile Include="Model\OpenTelemetry\QueryOtlpRequestForApiModel.cs" />
    <Compile Include="Model\OpenTelemetry\QueryOtlpRequestModel.cs" />
    <Compile Include="Model\OpenTelemetry\BackDetailExcelDataResponseModel.cs" />
    <Compile Include="Model\OpenTelemetry\BackSummaryExcelDataResponseModel.cs" />
    <Compile Include="Model\OpenTelemetry\OtlpResponseBaseModel.cs" />
    <Compile Include="Model\OpenTelemetry\DetailExcelDataResponseModel.cs" />
    <Compile Include="Model\OpenTelemetry\MonitoringConfigModel.cs" />
    <Compile Include="Model\OpenTelemetry\OpenTelemetryLogConfigModel.cs" />
    <Compile Include="Model\OpenTelemetry\SummaryExcelDataResponseModel.cs" />
    <Compile Include="Model\OpenTelemetry\OpenTelemetryConfigModel.cs" />
    <Compile Include="Model\OpenTelemetry\OpenTelemetryWebConfigModel.cs" />
    <Compile Include="Model\OpenTelemetry\ApiBasicDataResponseModel.cs" />
    <Compile Include="Model\ShopVideo\ManualSyncShopVideoModel.cs" />
    <Compile Include="Model\ShopVideo\ProductInfoModel.cs" />
    <Compile Include="Model\ShopVideo\ProductQueryModel.cs" />
    <Compile Include="Model\ShopVideo\PublishVideoTaskModel.cs" />
    <Compile Include="Model\PageRequest.cs" />
    <Compile Include="Model\ShopVideo\ShopModel.cs" />
    <Compile Include="Model\ShopVideo\ShopVideoQueryModel.cs" />
    <Compile Include="Model\ShopVideo\SyncResult.cs" />
    <Compile Include="Model\ShopVideo\TouTiaoPublishBeforeInfo.cs" />
    <Compile Include="Model\ShopVideo\UploadImageModel.cs" />
    <Compile Include="Model\ShopVideo\VideoUploadAuthModel.cs" />
    <Compile Include="Model\ShopVideo\VideoUploadRecordModel.cs" />
    <Compile Include="Model\TaobaoPushDbOrderInfo.cs" />
    <Compile Include="Model\ReprintRequestModel.cs" />
    <Compile Include="Model\ShopDefaultSupplierListRequestModel.cs" />
    <Compile Include="Model\ShopDefaultSupplierListResponseModel.cs" />
    <Compile Include="Model\OrderRemarkSetting.cs" />
    <Compile Include="Model\ProductSkuHistory\ProductSkuHistoryMessageModel.cs" />
    <Compile Include="Model\ReminderSettingModel.cs" />
    <Compile Include="Model\ProductSkuHistory\ProductSkuHistoryQuery.cs" />
    <Compile Include="Model\ProductSkuHistory\SetShortTitleOrPriceModel.cs" />
    <Compile Include="Model\Stat\StatActive.cs" />
    <Compile Include="Model\Stat\StatSendHistoryCount.cs" />
    <Compile Include="Model\TKCombinablePackageModel.cs" />
    <Compile Include="Model\TkTrackingModel.cs" />
    <Compile Include="Model\TkOrderPrintRequestModel.cs" />
    <Compile Include="Model\OrderCheckRuleMessageModel.cs" />
    <Compile Include="Model\OrderCheckRuleMessageModel.cs" />
    <Compile Include="Model\BaseProduct\BaseSkuSyncToPtSkuModel.cs" />
    <Compile Include="Model\MySqlDbConfigModel.cs" />
    <Compile Include="Model\BaseProduct\BindCheckBaseProductModel.cs" />
    <Compile Include="Model\BaseProduct\BindCheckBaseProductRes.cs" />
    <Compile Include="Model\BaseProduct\BpRelRecordSearchModel.cs" />
    <Compile Include="Model\BaseProduct\BpSkuSimpleConditionReq.cs" />
    <Compile Include="Model\BaseProduct\CategoryCheckAuth.cs" />
    <Compile Include="Model\BaseProduct\CommonKeyModel.cs" />
    <Compile Include="Model\BaseProduct\EditBaseProductSkuRelation.cs" />
    <Compile Include="Model\BaseProduct\EditSupplierProductSkuRelation.cs" />
    <Compile Include="Model\BaseProduct\PlateProductSearch.cs" />
    <Compile Include="Model\BaseProduct\PlateProductSearchRes.cs" />
    <Compile Include="Model\BaseProduct\PlatformInfo.cs" />
    <Compile Include="Model\BaseProduct\PtInfoDraftReqModel.cs" />
    <Compile Include="Model\BaseProduct\PtInfoDraftResModel.cs" />
    <Compile Include="Model\BaseProduct\PtInfoReqModel.cs" />
    <Compile Include="Model\BaseProduct\PtProductInfoModel.cs" />
    <Compile Include="Model\BaseProduct\TranAttributeAndSkuModel.cs" />
    <Compile Include="Model\BaseProduct\PtProductInfoSkuModel.cs" />
    <Compile Include="Model\BaseProduct\SyncPtProductInfoModel.cs" />
    <Compile Include="Model\BaseProduct\TranSourceSkuModel.cs" />
    <Compile Include="Model\BaseProduct\UpdatePtProductInfoDraftModel.cs" />
    <Compile Include="Model\BaseProduct\UpdatePtProductInfoDraftStockCountModel.cs" />
    <Compile Include="Model\BaseProduct\UpdatePtProductInfoDraftTitleModel.cs" />
    <Compile Include="Model\CommonDbModel.cs" />
    <Compile Include="Model\DyCategoryPropInfoV2Model.cs" />
    <Compile Include="Model\GetPurchaseQueryListRes.cs" />
    <Compile Include="Model\GetRecommendCategoryResult.cs" />
    <Compile Include="Model\ImageModel.cs" />
    <Compile Include="Model\LastLevelCateModel.cs" />
    <Compile Include="Model\ListingCategory.cs" />
    <Compile Include="Model\PlatformCategoryNavigation.cs" />
    <Compile Include="Model\ListingProductInDyModel.cs" />
    <Compile Include="Model\ListingDeliveryTemplate.cs" />
    <Compile Include="Model\MemberLevelViewModel.cs" />
    <Compile Include="Model\MaterialModel.cs" />
    <Compile Include="Model\OrderRequestSimpleModel.cs" />
    <Compile Include="Model\BaseProduct\BaseProductDetailReqModel.cs" />
    <Compile Include="Model\BaseProduct\BaseProductAbnormalModel.cs" />
    <Compile Include="Model\BaseProduct\BaseProductJson.cs" />
    <Compile Include="Model\BaseProduct\BaseProductSkuModifyModel.cs" />
    <Compile Include="Model\BaseProduct\AttributeTypeModel.cs" />
    <Compile Include="Model\BaseProduct\BaseProductReqModel.cs" />
    <Compile Include="Model\BaseProduct\BaseProductSkuAddModel.cs" />
    <Compile Include="Model\BaseProduct\BaseProductSkuUnbindModel.cs" />
    <Compile Include="Model\BaseProduct\BaseSkuRequestModel.cs" />
    <Compile Include="Model\BaseProduct\SyncPtSkuModel.cs" />
    <Compile Include="Model\OrderCheckRuleMessageModel.cs" />
    <Compile Include="Model\CloudMessageModel.cs" />
    <Compile Include="Model\OrderRequestSimpleModel.cs" />
    <Compile Include="Model\PageResultApiModel.cs" />
    <Compile Include="Model\OtherPlatforms\Heliang\OrderDeliveryRequest.cs" />
    <Compile Include="Model\OtherPlatforms\Heliang\QueryAfterSalesListRequest.cs" />
    <Compile Include="Model\OtherPlatforms\Heliang\QueryExpressRequest.cs" />
    <Compile Include="Model\OtherPlatforms\Heliang\QueryGoodsListRequest.cs" />
    <Compile Include="Model\OtherPlatforms\Heliang\QueryQrderListRequest.cs" />
    <Compile Include="Model\OtherPlatforms\IOtherPlatformsRequest.cs" />
    <Compile Include="Model\OtherPlatforms\IOtherPlatformsRequest.cs" />
    <Compile Include="Model\OtherPlatforms\juhaomai\CallbackInfo.cs" />
    <Compile Include="Model\OtherPlatforms\juhaomai\CallbackSet.cs" />
    <Compile Include="Model\OtherPlatforms\juhaomai\ExpressList.cs" />
    <Compile Include="Model\OtherPlatforms\juhaomai\GoodsInfo.cs" />
    <Compile Include="Model\OtherPlatforms\juhaomai\OrderInfo.cs" />
    <Compile Include="Model\OtherPlatforms\juhaomai\OrderUpdate.cs" />
    <Compile Include="Model\OtherPlatforms\juhaomai\ShopInfo.cs" />
    <Compile Include="Model\Platform\DdpTraceInfoResponseModel.cs" />
    <Compile Include="Model\BaseProduct\BaseProductSkuBindModel.cs" />
    <Compile Include="Model\BaseProduct\BaseProductSkuUnbindModel.cs" />
    <Compile Include="Model\OrderCheckRuleTaskModel.cs" />
    <Compile Include="Model\BaseProduct\BaseProductSkuBindSearchModel.cs" />
    <Compile Include="Model\BaseProduct\MessageRecordModel.cs" />
    <Compile Include="Model\BaseProduct\OssObjectModel.cs" />
    <Compile Include="Model\BaseProduct\BaseProductSearchModel.cs" />
    <Compile Include="Model\BaseProduct\ProductDbConfigModel.cs" />
    <Compile Include="Model\BaseProduct\BaseProductModel.cs" />
    <Compile Include="Model\OwnCodeWithUserIdModel.cs" />
    <Compile Include="Model\BaseProduct\SupplierSettlementPriceModels.cs" />
    <Compile Include="Model\ProductAuditListResult.cs" />
    <Compile Include="Model\ProfitStatistics\ProfitOrderSyncModel.cs" />
    <Compile Include="Model\ProfitStatistics\ProfitReportModel.cs" />
    <Compile Include="Model\ProfitStatistics\ProfitStatisticsDbConfigModel.cs" />
    <Compile Include="Model\ProfitStatistics\ProfitStatisticsModel.cs" />
    <Compile Include="Model\ProfitStatistics\ProfitOrderMessageModel.cs" />
    <Compile Include="Model\ProfitStatistics\ProfitStatisticsTotalResponseModel.cs" />
    <Compile Include="Model\ProfitStatistics\TouTiaoComissionModel.cs" />
    <Compile Include="Model\PurchaseOrderDeliveryModeModel.cs" />
    <Compile Include="Model\AliPaycreditResultModel.cs" />
    <Compile Include="Model\PaymentStatementsModel.cs" />
    <Compile Include="Model\RuleModel.cs" />
    <Compile Include="Model\RegistSubAccountModel.cs" />
    <Compile Include="Model\ScanQrCodeReqModel.cs" />
    <Compile Include="Model\SendHistoryReturnRecordModel.cs" />
    <Compile Include="Model\AjaxResultModel.cs" />
    <Compile Include="Model\AlibabaBuyerOrderLogisticsTraceInfo.cs" />
    <Compile Include="Model\AlibabaChoicenessProductTag.cs" />
    <Compile Include="Model\AliEbillOpenStatus.cs" />
    <Compile Include="Model\AliSendOpenEbillStatusLog.cs" />
    <Compile Include="Model\AlibabaCreateBuyerOrderRequestModel.cs" />
    <Compile Include="Model\AlibabaFxModel\Preview1688FxOrderCreateModel.cs" />
    <Compile Include="Model\AlibabaLogisticCompanyModel.cs" />
    <Compile Include="Model\AlibabaPlatformReturnModel.cs" />
    <Compile Include="Model\AliBuyerOrderPayChannelModel.cs" />
    <Compile Include="Model\AliParseAddressResponseModel.cs" />
    <Compile Include="Model\DistributorProductMappingModel.cs" />
    <Compile Include="Model\DistributorProductSkuMappingModel.cs" />
    <Compile Include="Model\DuplicationPushTaskLogModel.cs" />
    <Compile Include="Model\ExportTaskSearchModel.cs" />
    <Compile Include="Model\ExcelSheetHeaderModel.cs" />
    <Compile Include="Model\FxUserWithDbNameIdModel.cs" />
    <Compile Include="Model\IdAndTotalCount.cs" />
    <Compile Include="Model\Compensate\CompensateBatchBaseModel.cs" />
    <Compile Include="Model\Compensate\CompensateLogModel.cs" />
    <Compile Include="Model\InvokeApiDataLogModel.cs" />
    <Compile Include="Model\KeyValueModel.cs" />
    <Compile Include="Model\LogModel\BusinessLogModel.cs" />
    <Compile Include="Model\LogModel\UserAccountLogModel.cs" />
    <Compile Include="Model\ProductSettlementPriceQuery.cs" />
    <Compile Include="Model\OrderDisplaySetting.cs" />
    <Compile Include="Model\QueryPageModel.cs" />
    <Compile Include="Model\QueryMapProductModel.cs" />
    <Compile Include="Model\QueryDistributionProductModel.cs" />
    <Compile Include="Model\SearchDataEventTracingModel.cs" />
    <Compile Include="Model\SendHistoryAggregationModel.cs" />
    <Compile Include="Model\Compensate\CompensateBatchHistoryModel.cs" />
    <Compile Include="Model\Compensate\CompensateBatchMetaDataModel.cs" />
    <Compile Include="Model\Compensate\CompensateBatchModel.cs" />
    <Compile Include="Model\Compensate\InvokeApiDataModel.cs" />
    <Compile Include="Model\Compensate\SendHistoryCompensateMessageModel.cs" />
    <Compile Include="Model\DataEventTrackingModel.cs" />
    <Compile Include="Model\SelectOrderItem.cs" />
    <Compile Include="Model\AccountViewModel.cs" />
    <Compile Include="Model\AfterSaleOrderAggregationModel.cs" />
    <Compile Include="Model\DataSyncInterfaceDuplicationLogModel.cs" />
    <Compile Include="Model\PathFlowPlatformTypeModel.cs" />
    <Compile Include="Model\FxUserWithShopIdModel.cs" />
    <Compile Include="Model\LogicOrderAggregationModel.cs" />
    <Compile Include="Model\PathFlowAggregationModel.cs" />
    <Compile Include="Model\ProductAggregationModel.cs" />
    <Compile Include="Model\Set1688ProductShortTitleModel.cs" />
    <Compile Include="Model\SettlementBillStatusResult.cs" />
    <Compile Include="Model\SettlementProductModel.cs" />
    <Compile Include="Model\AfterSaleWithPathFlowCodeModel.cs" />
    <Compile Include="Model\DateTimeRangeModel.cs" />
    <Compile Include="Model\FxDbConfigModel.cs" />
    <Compile Include="Model\DuplicationConditionModel.cs" />
    <Compile Include="Model\DuplicationMessageModel.cs" />
    <Compile Include="Model\FxUserPlatformOrderIdModel.cs" />
    <Compile Include="Model\IdAndCodeModel.cs" />
    <Compile Include="Model\LogicOrderPathFlowModel.cs" />
    <Compile Include="Model\PathFlowFxUserIdNodeModel.cs" />
    <Compile Include="Model\ReturnedModel.cs" />
    <Compile Include="Model\ReceDbConfigModel.cs" />
    <Compile Include="Model\ShopAppModel.cs" />
    <Compile Include="Model\Stat\ServiceAppOrderSettlementDetail.cs" />
    <Compile Include="Model\Stat\StatSkuSettlement.cs" />
    <Compile Include="Model\Stat\StatCategory.cs" />
    <Compile Include="Model\Stat\QueryNeedStatFxUserId.cs" />
    <Compile Include="Model\Stat\StatModel.cs" />
    <Compile Include="Model\Stat\AgentFor1688Model.cs" />
    <Compile Include="Model\SupplierProduct\BatchListingTaskModel.cs" />
    <Compile Include="Model\SubAccountSearchModel.cs" />
    <Compile Include="Model\SupplierProduct\BatchListingTaskRes.cs" />
    <Compile Include="Model\SupplierProduct\ListingTaskBusinessModel.cs" />
    <Compile Include="Model\SupplierProduct\ListingTaskRecordsModel.cs" />
    <Compile Include="Model\SupplierProduct\ListingTaskBusinessAbnormalModel.cs" />
    <Compile Include="Model\SupplierProduct\SupplierProductQuery.cs" />
    <Compile Include="Model\SyncOrderTimeChunkLogModel.cs" />
    <Compile Include="Model\ExpressSustenanceInfoModel.cs" />
    <Compile Include="Model\TemplateSearchModel.cs" />
    <Compile Include="Model\TimeRange.cs" />
    <Compile Include="Model\Tools\BaseProductUtils.cs" />
    <Compile Include="Model\Tools\DbConfigSimpleModel.cs" />
    <Compile Include="Model\Tools\DbMetaDataModel.cs" />
    <Compile Include="Model\Tools\SendHistoryAnalysisModel.cs" />
    <Compile Include="Model\Tools\SendHistoryAnalysisResultModel.cs" />
    <Compile Include="Model\Tools\OrderCheckRuleValidationModel.cs" />
    <Compile Include="Model\Tools\OrderLifeCycleOrderSendNodeModel.cs" />
    <Compile Include="Model\Tools\OrderLifeCycleAfterSaleOrderNodeModel.cs" />
    <Compile Include="Model\Tools\OrderLifeCycleDetectionRequestModel.cs" />
    <Compile Include="Model\Tools\OrderLifeCycleDetectionResultModel.cs" />
    <Compile Include="Model\Tools\OrderLifeCycleLogicOrderNodeModel.cs" />
    <Compile Include="Model\Tools\OrderLifeCycleOrderNodeModel.cs" />
    <Compile Include="Model\Tools\OrderLifeCycleOrderPrintNodeModel.cs" />
    <Compile Include="Model\TouTiaoAddressAutoConfirmSetting.cs" />
    <Compile Include="Model\TouTiaoPushDbInfo.cs" />
    <Compile Include="Model\TouTiaoPushDbOrderInfo.cs" />
    <Compile Include="Model\LogicOrderIdModel.cs" />
    <Compile Include="Model\Trace\LogisticsTraceConfigModel.cs" />
    <Compile Include="Model\Trace\TraceBatchLogModel.cs" />
    <Compile Include="Model\Trace\TraceDataLogModel.cs" />
    <Compile Include="Model\WxVideoCreateEbillOrderRequest.cs" />
    <Compile Include="Model\VirtualExpressModel.cs" />
    <Compile Include="Model\XiaoHongShuCreateEbillOrderRequest.cs" />
    <Compile Include="Repository\AfterSaleAddressLogRepository.cs" />
    <Compile Include="Repository\AfterSaleAddressRepository.cs" />
    <Compile Include="Repository\AfterSaleOrderExtRepository.cs" />
    <Compile Include="Repository\ClickHouse\OtlpRepository.cs" />
    <Compile Include="Repository\ClickHouse\OtlpSignozRepository.cs" />
    <Compile Include="Repository\DataMigrate\ConfigDbBaseRepository.cs" />
    <Compile Include="Repository\DataMigrate\ConfigDbMigrateRepository.cs" />
    <Compile Include="Repository\DeliveryModeChangeLogRepository.cs" />
    <Compile Include="Repository\ElasticSearch\OtlpRepository.cs" />
    <Compile Include="Repository\BaseProduct\BaseProductRelationRecordRepository.cs" />
    <Compile Include="Repository\BaseProduct\GenerateBaseProductRecordRepository.cs" />
    <Compile Include="Repository\IPAreaMappingRepository.cs" />
    <Compile Include="Repository\AfterSaleOrderItemStockRecordRepository.cs" />
    <Compile Include="Repository\DataArchive\ArchiveUserRepository.cs" />
    <Compile Include="Repository\DataArchive\DataArchiveHistoryRepository.cs" />
    <Compile Include="Repository\DataArchive\DataArchiveRepository.cs" />
    <Compile Include="Extension\RequiredAttribute.cs" />
    <Compile Include="FxModel\DataDuplicationLockSwitchConfigModel.cs" />
    <Compile Include="Entity\DataArchive\ArchiveUser.cs" />
    <Compile Include="Entity\DataArchive\DataArchive.cs" />
    <Compile Include="Entity\DataArchive\DataArchiveHistory.cs" />
    <Compile Include="Model\BatchSettlementPopupReq.cs" />
    <Compile Include="Model\BatchSettlementPopupModel.cs" />
    <Compile Include="Model\OpenPlatform\BindSupplierRequest.cs" />
    <Compile Include="Model\OpenPlatform\QueryPartnerProductListRequest.cs" />
    <Compile Include="Model\OpenPlatform\SupplierProductListRespone.cs" />
    <Compile Include="Model\OtherPlatforms\IOtherPlatformsRequest.cs" />
    <Compile Include="Model\OtherPlatforms\juhaomai\CallbackInfo.cs" />
    <Compile Include="Model\OtherPlatforms\juhaomai\CallbackSet.cs" />
    <Compile Include="Model\OtherPlatforms\juhaomai\ExpressList.cs" />
    <Compile Include="Model\OtherPlatforms\juhaomai\GoodsInfo.cs" />
    <Compile Include="Model\OtherPlatforms\juhaomai\OrderInfo.cs" />
    <Compile Include="Model\OtherPlatforms\juhaomai\OrderUpdate.cs" />
    <Compile Include="Model\OtherPlatforms\juhaomai\ShopInfo.cs" />
    <Compile Include="Repository\DataMigrate\ConfigDbBaseRepository.cs" />
    <Compile Include="Repository\DataMigrate\ConfigDbMigrateRepository.cs" />
    <Compile Include="Repository\ClickHouse\OtlpRepository.cs" />
    <Compile Include="Repository\DataArchive\ArchiveUserRepository.cs" />
    <Compile Include="Repository\DataArchive\DataArchiveHistoryRepository.cs" />
    <Compile Include="Repository\DataArchive\DataArchiveRepository.cs" />
    <Compile Include="Repository\Collect\ClaimShopProductRepository.cs" />
    <Compile Include="Repository\Collect\CollectProductBaseRepository.cs" />
    <Compile Include="Repository\Collect\ClaimProductRelationRepository.cs" />
    <Compile Include="Repository\Collect\CollectBoxRepository.cs" />
    <Compile Include="Repository\Collect\CurrencyConvertRateRepository.cs" />
    <Compile Include="Repository\FxUserForeignShopRepository.cs" />
    <Compile Include="Repository\PrintHistoryRecordDbNameConfigRepository.cs" />
    <Compile Include="Repository\GreyCompatible\ProfitStatisticsMsgRepository.cs" />
    <Compile Include="Repository\OpenPlatform\OpenApiBaseRepository.cs" />
    <Compile Include="Repository\OpenPlatform\OpenApiTokenRepository.cs" />
    <Compile Include="Repository\OpenPlatform\OpenPlatformAppPermissionRepository.cs" />
    <Compile Include="Repository\OpenPlatform\OpenPlatformAppRepository.cs" />
    <Compile Include="Repository\OpenPlatform\OpenPlatformUserRepository.cs" />
    <Compile Include="Repository\OpenPlatform\OutOrderRepository.cs" />
    <Compile Include="Repository\ReceiverOaidRepository.cs" />
    <Compile Include="Repository\SettlementBillStatRepository.cs" />
    <Compile Include="Repository\ShopVideo\PublishVideoTaskExtRepository.cs" />
    <Compile Include="Repository\ShopVideo\PublishVideoTaskRepository.cs" />
    <Compile Include="Repository\ShopVideo\ShopVideoBaseRepository.cs" />
    <Compile Include="Repository\ShopVideo\ShopVideoItemRepository.cs" />
    <Compile Include="Repository\ShopVideo\ShopVideoRepository.cs" />
    <Compile Include="Repository\ShopVideo\VideoUploadRecordRepository.cs" />
    <Compile Include="Repository\Stat\StatActiveDetailRepository.cs" />
    <Compile Include="Repository\Stat\StatActiveRepository.cs" />
    <Compile Include="Repository\Stat\StatSkuSettlementRepository.cs" />
    <Compile Include="Repository\Stat\StatServiceAppOrderRepository.cs" />
    <Compile Include="Repository\BaseProduct\PtProductInfoRepository.cs" />
    <Compile Include="Repository\BaseProduct\BaseOfSupplierSkuRelation.cs" />
    <Compile Include="Entity\PlatformCategory.cs" />
    <Compile Include="Entity\PlatformCategoryProp.cs" />
    <Compile Include="Entity\PlatformCategoryPublishRule.cs" />
    <Compile Include="Repository\BaseProduct\BaseProductShortTitleRepository.cs" />
    <Compile Include="Model\TkSplitPackageModel.cs" />
    <Compile Include="Repository\FxUserShopDefaultSupplierRepository.cs" />
    <Compile Include="Repository\ProductSkuHistoryRepository.cs" />
    <Compile Include="Repository\SettlementProductSkuNameRepository.cs" />
    <Compile Include="Repository\GlobalProduct\GlobalProductExtRepository.cs" />
    <Compile Include="Repository\GlobalProduct\GlobalProductRepository.cs" />
    <Compile Include="Repository\GlobalProduct\GlobalProductSkuRepository.cs" />
    <Compile Include="Repository\HotProduct\HotProductCloudMessageRepository.cs" />
    <Compile Include="Repository\HotProduct\HotProductMasterDbRepository.cs" />
    <Compile Include="Repository\HotProduct\HotProductBaseRepository.cs" />
    <Compile Include="Repository\HotProduct\HotProductDbConfigRepository.cs" />
    <Compile Include="Repository\HotProduct\ProductRepository.cs" />
    <Compile Include="Repository\HotProduct\ProductSaleStatisticsRepository.cs" />
    <Compile Include="Repository\HotProduct\ProductSkuRepository.cs" />
    <Compile Include="Repository\HotProduct\ProductSkuSendDetailRepository.cs" />
    <Compile Include="Repository\SkuForProductIdRepository.cs" />
    <Compile Include="Repository\BaseProduct\BaseProductSkuShortTitleRepository.cs" />
    <Compile Include="Repository\BaseProduct\BaseProductSkuSupplierConfigRepository.cs" />
    <Compile Include="Repository\BaseProduct\BaseProductAbnormalRepository.cs" />
    <Compile Include="Repository\CooperateStatusRecordRepository.cs" />
    <Compile Include="Repository\CooperateEvaluateRepository.cs" />
    <Compile Include="Repository\BusinessCardRemarkRepository.cs" />
    <Compile Include="RepositoryFactories\LogicOrderRepositoryFactory.cs" />
    <Compile Include="RepositoryFactories\OrderRepositoryFactory.cs" />
    <Compile Include="Repository\AfterSaleActionItemRecordRepository.cs" />
    <Compile Include="Repository\AfterSaleActionRecordRepository.cs" />
    <Compile Include="Repository\BaseProduct\BaseProductImageRepository.cs" />
    <Compile Include="Repository\AfterSaleOrderRelationRepository.cs" />
    <Compile Include="Repository\BaseProduct\BaseProductMasterDbRepository.cs" />
    <Compile Include="Repository\BaseProduct\BaseProductBaseRepository.cs" />
    <Compile Include="Repository\BaseProduct\BaseOfPtSkuRelationRepository.cs" />
    <Compile Include="Repository\BaseProduct\BaseProductSkuAttributeRepository.cs" />
    <Compile Include="Repository\BaseProduct\BaseProductSkuRepository.cs" />
    <Compile Include="Repository\BaseProduct\MessageRecordRepository.cs" />
    <Compile Include="Repository\BaseProduct\OssObjectRepository.cs" />
    <Compile Include="Repository\BaseProduct\ProductDbConfigRepository.cs" />
    <Compile Include="Repository\BaseProduct\BaseProductRepository.cs" />
    <Compile Include="Repository\BranchShareRelationUseHistoryRepository.cs" />
    <Compile Include="Repository\BusinessCardRepository.cs" />
    <Compile Include="Repository\MemberLevelRepository.cs" />
    <Compile Include="Repository\DownFileRelationRepository.cs" />
    <Compile Include="Repository\ColdHotStorage\ColdLogicOrderStateRepository.cs" />
    <Compile Include="Repository\DataMigrate\StatRelationSupplierRepository.cs" />
    <Compile Include="Repository\CommonSettingRecordRepository.cs" />
    <Compile Include="Repository\DistributorProductRepository.cs" />
    <Compile Include="Repository\DistributorProductMappingRepository.cs" />
    <Compile Include="Repository\DistributorProductSkuMappingRecordRepository.cs" />
    <Compile Include="Repository\DistributorProductSkuMappingRepository.cs" />
    <Compile Include="Repository\SubProductSettlementPriceRepository.cs" />
    <Compile Include="Repository\MySqlBaseRepository.cs" />
    <Compile Include="Repository\MySqlMasterDbRepository.cs" />
    <Compile Include="Repository\SupplierProduct\ListingProduct\ListingTemplateGroupItemRepository.cs" />
    <Compile Include="Repository\ImagesRelationRepository.cs" />
    <Compile Include="Repository\SupplierProduct\ListingProduct\ListingTemplateGroupRepository.cs" />
    <Compile Include="Repository\SupplierProduct\ListingProduct\UserListingSettingRepository.cs" />
    <Compile Include="Repository\OptimisticLockRepository.cs" />
    <Compile Include="Repository\OrderSelfDeliveryRepository.cs" />
    <Compile Include="Repository\PlatformCategoryPropRepository.cs" />
    <Compile Include="Repository\PlatformCategorySupplierRepository.cs" />
    <Compile Include="Repository\PlatformCategoryRepository.cs" />
    <Compile Include="Repository\ProfitStatistics\PathFlowNodeRepository.cs" />
    <Compile Include="Repository\ProfitStatistics\ProfitOrderItemPriceRepository.cs" />
    <Compile Include="Repository\ProfitStatistics\ProfitLogicOrderItemRepository.cs" />
    <Compile Include="Repository\ProfitStatistics\ProfitOrderItemRepository.cs" />
    <Compile Include="Repository\ProfitStatistics\ProfitLogicOrderRepository.cs" />
    <Compile Include="Repository\ProfitStatistics\ProfitOrderRepository.cs" />
    <Compile Include="Repository\ProfitStatistics\ProfitPathFlowRepository.cs" />
    <Compile Include="Repository\ProfitStatistics\ProfitStatisticsBaseRepository.cs" />
    <Compile Include="Repository\ProfitStatistics\ProfitOrderSyncTaskRepository.cs" />
    <Compile Include="Repository\ProfitStatistics\ProfitStatisticsCloudMessageRepository.cs" />
    <Compile Include="Repository\ProfitStatistics\ProfitStatisticsDbConfigRepository.cs" />
    <Compile Include="Repository\SettlementModule\TaskParamJsonRepository.cs" />
    <Compile Include="Repository\MessageHistoryRecordsRepository.cs" />
    <Compile Include="Repository\SettlementModule\SettlementBillQueryRepository.cs" />
    <Compile Include="Repository\SellerRemarkHistoryRepository.cs" />
    <Compile Include="Repository\ShippingFeeTemplateRepository.cs" />
    <Compile Include="Repository\FxAlibabaBuyerShopRelationRepository.cs" />
    <Compile Include="Repository\LogicOrderPathFlowRecordRepository.cs" />
    <Compile Include="Repository\Cache\FxCaching.cs" />
    <Compile Include="Repository\Cache\FxCachingConfig.cs" />
    <Compile Include="Model\WaybillCodeAggregationModel.cs" />
    <Compile Include="Repository\Cache\FxCachingSetting.cs" />
    <Compile Include="Repository\DataMigrate\FxDataMigrateSubQueryRepository.cs" />
    <Compile Include="Repository\DataMigrate\FxMigrateLockRepository.cs" />
    <Compile Include="Repository\DataMigrate\PrintHistoryMigratePathFlowRepository.cs" />
    <Compile Include="Repository\DataMigrate\TempFxDbConfigRepository.cs" />
    <Compile Include="Repository\DataMigrate\StatPathFlowRepository.cs" />
    <Compile Include="Repository\DataMigrate\StatOrderRepository.cs" />
    <Compile Include="Repository\Contrast\ContrastTaskItemRepository.cs" />
    <Compile Include="Repository\Contrast\ContrastTaskExeRepository.cs" />
    <Compile Include="Repository\Contrast\ContrastTaskRepository.cs" />
    <Compile Include="Model\SourceFxUserIdGroupDbConfigModel.cs" />
    <Compile Include="Model\SupplierFxUserIdModel.cs" />
    <Compile Include="Repository\DataMigrate\FxDataMigrateSubTaskRepository.cs" />
    <Compile Include="Repository\DataMigrate\FxDataMigrateTaskRepository.cs" />
    <Compile Include="Repository\FxWeChatUserRepository.cs" />
    <Compile Include="Repository\MessageProcessLogMongoDbRepository.cs" />
    <Compile Include="Repository\OrderDeliverExceptionRepository.cs" />
    <Compile Include="Repository\OrderExtraRepository.cs" />
    <Compile Include="Repository\OrderModule\DefaultColdLogicOrderRepository.cs" />
    <Compile Include="Repository\OrderModule\DefaultColdOrderRepository.cs" />
    <Compile Include="Repository\OrderModule\OrderAbnormalPathFlowNodeRepository.cs" />
    <Compile Include="Repository\OrderModule\OrderAbnormalRepository.cs" />
    <Compile Include="Repository\OrderStatusRepository.cs" />
    <Compile Include="Repository\OrderItemStatusRepository.cs" />
    <Compile Include="Repository\PlatformAreaCodeInfoRepository.cs" />
    <Compile Include="Repository\PhShopRepository.cs" />
    <Compile Include="Repository\DeliveryModeChangeRecordRepository.cs" />
    <Compile Include="Repository\PaymentStatementRepository.cs" />
    <Compile Include="Repository\ProductSettlementPriceRepository.cs" />
    <Compile Include="Repository\ProductSettlementRecordRepository.cs" />
    <Compile Include="Repository\Stat\Stat1688FxUserIdRepository.cs" />
    <Compile Include="Repository\Stat\ServiceAppOrderSettlementDetailRepository.cs" />
    <Compile Include="Repository\Stat\StatCategoryRepository.cs" />
    <Compile Include="Repository\Stat\StatSendHistoryRepository.cs" />
    <Compile Include="Repository\Stat\Stat1688V2Repository.cs" />
    <Compile Include="Repository\Settings\DuplicationColdStorageSwitchRepository.cs" />
    <Compile Include="Repository\Stat\Stat1688Repository.cs" />
    <Compile Include="Repository\PurchaseOrderItemRelationRepository.cs" />
    <Compile Include="Repository\PurchaseOrderModifyPriceRecordRepository.cs" />
    <Compile Include="Repository\PurchaseOrderDeliveryModeRepository.cs" />
    <Compile Include="Repository\PurchaseOrderRelationRepository.cs" />
    <Compile Include="Repository\SendHistoryReturnRecordRepository.cs" />
    <Compile Include="Repository\SettlementInfoRepository.cs" />
    <Compile Include="Repository\DataChangeLogRepository.cs" />
    <Compile Include="Repository\DataSyncStatus\DataSyncStatusHistoryRepository.cs" />
    <Compile Include="Repository\DataSyncStatus\DataSyncStatusRepository.cs" />
    <Compile Include="Repository\DataChangeLogDbConfigRepository.cs" />
    <Compile Include="Repository\FxDbConfigRepository.cs" />
    <Compile Include="Repository\ExpressReachShareRepository.cs" />
    <Compile Include="Repository\ProductNodeRelationRepository.cs" />
    <Compile Include="Repository\FxUnBindTaskRepository.cs" />
    <Compile Include="Repository\QuickSearchRepository.cs" />
    <Compile Include="Repository\PrintHistoryDataRepository.cs" />
    <Compile Include="Repository\PrintHistoryDbConfigRepository.cs" />
    <Compile Include="Repository\RepairOrUpdateRepository.cs" />
    <Compile Include="Repository\ReceDbConfigRepository.cs" />
    <Compile Include="Repository\Log\TokenBucketLogRepository.cs" />
    <Compile Include="Model\BatchUpdateModel.cs" />
    <Compile Include="Model\GetEbillOrderRequest.cs" />
    <Compile Include="Model\GetEbillOrderResponse.cs" />
    <Compile Include="Model\SendFailModel.cs" />
    <Compile Include="Model\AsyncTaskModel.cs" />
    <Compile Include="Model\AddPreordainRequestModel.cs" />
    <Compile Include="EntityExtension\OldMemberToken.cs" />
    <Compile Include="Model\AfterSaleModel.cs" />
    <Compile Include="Model\AsyncDeliveryModel.cs" />
    <Compile Include="Model\ApiRequestModel.cs" />
    <Compile Include="Model\AppRestartInfo.cs" />
    <Compile Include="Model\MessageModel.cs" />
    <Compile Include="Model\OperatorBlockSetting.cs" />
    <Compile Include="Model\LogicOrderRequestModel.cs" />
    <Compile Include="Model\PddDecrptModel.cs" />
    <Compile Include="Model\AppendLogPageResultModel.cs" />
    <Compile Include="Model\PddRecommendLogisticsModel.cs" />
    <Compile Include="Model\PinduoduoAddressModel.cs" />
    <Compile Include="Model\SendHistoryResultModel.cs" />
    <Compile Include="Model\SupplierUserNameModel.cs" />
    <Compile Include="Model\TouTiaoAddressModel.cs" />
    <Compile Include="Model\TouTiaoMessageModel.cs" />
    <Compile Include="Model\KuaiShouMessageModel.cs" />
    <Compile Include="Model\ExpressApiRequestModel.cs" />
    <Compile Include="Model\LogForOperatorStasticModel.cs" />
    <Compile Include="Model\BranchSharedViewModel.cs" />
    <Compile Include="Model\DbApiRequestModel.cs" />
    <Compile Include="Model\LogisticQueryPageResultModel.cs" />
    <Compile Include="Model\PreordainPageResultModel.cs" />
    <Compile Include="Model\BranchShareRelationModel.cs" />
    <Compile Include="Model\JTExpressApiModels.cs" />
    <Compile Include="Model\PddMessageWrapper.cs" />
    <Compile Include="Model\pdp_tb_trade.cs" />
    <Compile Include="Model\SeachWaybillCodeFromViewModel.cs" />
    <Compile Include="Model\ShopSynInfoModel.cs" />
    <Compile Include="Model\AdvShowingModel.cs" />
    <Compile Include="Model\AuthRedirectModel.cs" />
    <Compile Include="Model\AccountShopRequestModel.cs" />
    <Compile Include="Model\DbConfigModel.cs" />
    <Compile Include="Model\FunctionSettingModel.cs" />
    <Compile Include="Model\HotOrderSyncParameter.cs" />
    <Compile Include="Model\OrderItemSelectKeyModel.cs" />
    <Compile Include="Model\OrderMergerParameterModel.cs" />
    <Compile Include="Model\PddRefundOrder.cs" />
    <Compile Include="Model\PurchasesReturnModel.cs" />
    <Compile Include="Model\ScanProductPrintViewModel.cs" />
    <Compile Include="Model\ServiceAppModel.cs" />
    <Compile Include="Model\PayModel.cs" />
    <Compile Include="Model\CommentOrderModel.cs" />
    <Compile Include="Model\CustomerApiResult.cs" />
    <Compile Include="Model\JDKuaidiModel.cs" />
    <Compile Include="Model\LogisticCodePushRequest.cs" />
    <Compile Include="Model\LogisticCodeQueryRequest.cs" />
    <Compile Include="Model\LogisticCodeQueryResponse.cs" />
    <Compile Include="Model\LogisticQueryListRequestModel.cs" />
    <Compile Include="Model\OrderLogisticInfoModel.cs" />
    <Compile Include="Model\ProductWeightModel.cs" />
    <Compile Include="Model\SearchAdvModel.cs" />
    <Compile Include="Model\CommonWsXcxModel.cs" />
    <Compile Include="Model\CommonXcxModel.cs" />
    <Compile Include="Model\GetPrintHistListRequestModel.cs" />
    <Compile Include="Model\KDNiaoApiRequestModels.cs" />
    <Compile Include="Model\LogConfigModel.cs" />
    <Compile Include="Model\LogForOperatorConfigModel.cs" />
    <Compile Include="Model\LogForOperatorReturnModel.cs" />
    <Compile Include="Model\OrderCacheConfigModel.cs" />
    <Compile Include="Model\ProductAttrModel.cs" />
    <Compile Include="Model\PlatformSettingModel.cs" />
    <Compile Include="Model\SiteContextConfig.cs" />
    <Compile Include="Model\SubUserModel.cs" />
    <Compile Include="Model\TaobaoPushDbSetting.cs" />
    <Compile Include="Model\SystemSetting.cs" />
    <Compile Include="Model\TaobaoAuthMemberToken.cs" />
    <Compile Include="Model\AlibabaMemberToken.cs" />
    <Compile Include="Model\AlibabaMessageModel.cs" />
    <Compile Include="Model\BranchAddress.cs" />
    <Compile Include="Enum\ShopSyncStatusType.cs" />
    <Compile Include="Model\CainiaoPrintDatatModel.cs" />
    <Compile Include="Model\CheckAddressReachableRequestModel.cs" />
    <Compile Include="Model\CommonModel.cs" />
    <Compile Include="Model\CustomerColumnMappingModel.cs" />
    <Compile Include="Model\CustomerOrderExcelImportModel.cs" />
    <Compile Include="Model\DeliverySendOrderModel.cs" />
    <Compile Include="Model\DeliverySendOrderResultModel.cs" />
    <Compile Include="Model\ExpressPrintDataModel.cs" />
    <Compile Include="Model\ExpressPrintRequestModel.cs" />
    <Compile Include="Model\GetWaybillCodeModel.cs" />
    <Compile Include="Model\LoginAuthCookieModel.cs" />
    <Compile Include="Model\OnlineSendRequestModel.cs" />
    <Compile Include="Model\OrderRequestModel.cs" />
    <Compile Include="Model\PrintHisotoryCallbackRequestModel.cs" />
    <Compile Include="Model\PrintNaHuoLabelTemplateRequestModel.cs" />
    <Compile Include="Model\PrintSendGoodTemplateRequestModel.cs" />
    <Compile Include="Model\PreordainListRequestModel.cs" />
    <Compile Include="Model\PreordainViewModel.cs" />
    <Compile Include="Model\ProductCategory.cs" />
    <Compile Include="Model\SaveOfferModule.cs" />
    <Compile Include="Model\SwitchShopViewModel.cs" />
    <Compile Include="Model\TouTiaoModifyReceiverMessageModel.cs" />
    <Compile Include="Model\UpdatePriceRequestModel.cs" />
    <Compile Include="Model\UpdateReceiverModel.cs" />
    <Compile Include="Model\UserFunctionAvailableCountCheckResultModel.cs" />
    <Compile Include="Model\UserLoginModel.cs" />
    <Compile Include="Model\UserRegisterModel.cs" />
    <Compile Include="Model\UserShopResponseModel.cs" />
    <Compile Include="Model\WaybillBigShotModel.cs" />
    <Compile Include="Model\WaybillCodeRequestModel.cs" />
    <Compile Include="Model\SendHistoryRequestModel.cs" />
    <Compile Include="Model\ProvinceModel.cs" />
    <Compile Include="Model\PrintHistoryRequestModel.cs" />
    <Compile Include="Model\PurchaseItemsModel.cs" />
    <Compile Include="Model\OrderSearchModel.cs" />
    <Compile Include="Model\OrderSelectKeyModel.cs" />
    <Compile Include="Model\PagedResultModel.cs" />
    <Compile Include="Model\SetOfferModule.cs" />
    <Compile Include="Model\SyncOrderParametersModel.cs" />
    <Compile Include="Model\TemplateListModel.cs" />
    <Compile Include="Model\ThreeAreaInfo.cs" />
    <Compile Include="Model\WayBillAuthConfig.cs" />
    <Compile Include="Model\WaybillCodeViewModel.cs" />
    <Compile Include="Model\WaybillStockModel.cs" />
    <Compile Include="Model\WlbWayBillIGetData.cs" />
    <Compile Include="Model\NaHuoLabelOrderModel.cs" />
    <Compile Include="Model\YouZanTradeQueryResponse.cs" />
    <Compile Include="Model\YunJiSendModel.cs" />
    <Compile Include="MongoRepository\CollectionNameAttribute.cs" />
    <Compile Include="MongoRepository\ConnectionNameAttribute.cs" />
    <Compile Include="MongoRepository\LogForPlatformTypeExceptionDetailRepository.cs" />
    <Compile Include="MongoRepository\LogisticCodesRepository.cs" />
    <Compile Include="MongoRepository\LogisticsAppInfoRepository.cs" />
    <Compile Include="MongoRepository\LogForPlatformTypeDetailRepository.cs" />
    <Compile Include="MongoRepository\TracesMessageRepository.cs" />
    <Compile Include="MongoRepository\LogStatisticByPlatformTypeRepository.cs" />
    <Compile Include="MongoRepository\LogForTimeOutRepository.cs" />
    <Compile Include="MongoRepository\LogStatisticRepository.cs" />
    <Compile Include="MongoRepository\LogOperatorStatisticRepository.cs" />
    <Compile Include="MongoRepository\LogOperatorsRepository.cs" />
    <Compile Include="MongoRepository\LogisticTracesRepository.cs" />
    <Compile Include="MongoRepository\MongoDatabase.cs" />
    <Compile Include="MongoRepository\MongoRepository.cs" />
    <Compile Include="MongoRepository\OrderMongoRepository.cs" />
    <Compile Include="MongoRepository\ProductTmpMongoRepository.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Dapper\SimpleCRUD.cs" />
    <Compile Include="Dapper\SimpleCRUDAsync.cs" />
    <Compile Include="Repository\BranchShareRelationOtherInfoRepository.cs" />
    <Compile Include="Repository\EBillAccountExtensionRepository.cs" />
    <Compile Include="Repository\FenDanSystemNavRepository.cs" />
    <Compile Include="Repository\OrderTagsRepository.cs" />
    <Compile Include="Repository\OrderPromiseRepository.cs" />
    <Compile Include="Repository\PerformanceLogRepository.cs" />
    <Compile Include="Repository\ReceiverRepository.cs" />
    <Compile Include="Repository\SendFailRequestRepository.cs" />
    <Compile Include="Repository\SendFailRepository.cs" />
    <Compile Include="Repository\AsyncTaskRepository.cs" />
    <Compile Include="Repository\AfterSaleEvidenceRepository.cs" />
    <Compile Include="Repository\AfterSaleOrderItemRepository.cs" />
    <Compile Include="Repository\AfterSaleOrderRepository.cs" />
    <Compile Include="Repository\AsyncDeliveryRepository.cs" />
    <Compile Include="Repository\FxServiceVersion\LimitedFunctionsRepository.cs" />
    <Compile Include="Repository\FxServiceVersion\ServiceFunctionsRepository.cs" />
    <Compile Include="Repository\FxServiceVersion\ServiceVersionRepository.cs" />
    <Compile Include="Repository\FxServiceVersion\UserOrderBalanceUsedExceptionRecordRepository.cs" />
    <Compile Include="Repository\FxServiceVersion\UserOrderCountBalanceRepository.cs" />
    <Compile Include="Repository\FxServiceVersion\UserOrderLockRecordRepository.cs" />
    <Compile Include="Repository\FxServiceVersion\UserOrderLockRepositor.cs" />
    <Compile Include="Repository\FxServiceVersion\UserOrderUsedRecordRepository.cs" />
    <Compile Include="Repository\FxServiceVersion\UserServiceVersionMappingRepository.cs" />
    <Compile Include="Repository\FxServiceVersion\UserSpecialFunctionMappingRepository.cs" />
    <Compile Include="Repository\FxUnbindRepository.cs" />
    <Compile Include="Repository\CommonSettingExtensionRepository.cs" />
    <Compile Include="Repository\FinancialSettlementRepository.cs" />
    <Compile Include="Repository\ImportBatchHistoryRepository.cs" />
    <Compile Include="Repository\ImportOrderBatchRepository.cs" />
    <Compile Include="Repository\KuaiShouEncryptedReceiverInfoRepository.cs" />
    <Compile Include="Repository\ManualOrder\OrderCheckRepository.cs" />
    <Compile Include="Repository\ManualOrder\OrderCheckRuleRepository.cs" />
    <Compile Include="Repository\ManualOrder\OrderManualRecordRepository.cs" />
    <Compile Include="Repository\OrderItemExtRepository.cs" />
    <Compile Include="Repository\OrderSplitRecordRepository.cs" />
    <Compile Include="Repository\PathFlowNodeRepository.cs" />
    <Compile Include="Repository\PathFlowChangeLogRepository.cs" />
    <Compile Include="Repository\PathFlowReferenceConfigRepository.cs" />
    <Compile Include="Repository\PathFlowReferenceRepository.cs" />
    <Compile Include="Repository\ReceiverMaskDataRepository.cs" />
    <Compile Include="Repository\SendHistoryChildRepository.cs" />
    <Compile Include="Repository\SettlementBillFSDBRepository.cs" />
    <Compile Include="Repository\SettlementBillRepository.cs" />
    <Compile Include="Repository\SettlementProductSkuRepository.cs" />
    <Compile Include="Repository\FxWeChatQRCodeRepository.cs" />
    <Compile Include="Repository\ShopAuthHistoryRepository.cs" />
    <Compile Include="Repository\PrepayStatusChangeRecordRepository.cs" />
    <Compile Include="Repository\SupplierProduct\SupplierAddressRepository.cs" />
    <Compile Include="Repository\TemplatePackageInfoRepository.cs" />
    <Compile Include="Repository\SubAccount\UserFxPostRelationRepository.cs" />
    <Compile Include="Repository\SubAccount\SysPermissionFxRepository.cs" />
    <Compile Include="Repository\SubAccount\PostPermissionFxRepository.cs" />
    <Compile Include="Repository\SubAccount\PostFxRepository.cs" />
    <Compile Include="Repository\SupplierProduct\ListingTaskRecordsRepository.cs" />
    <Compile Include="Repository\SupplierProduct\ListingTaskBusinessAbnormalRepository.cs" />
    <Compile Include="Repository\SupplierProduct\SupProductWaybillRelationRepository.cs" />
    <Compile Include="Repository\SupplierProduct\UserBrandRepository.cs" />
    <Compile Include="Repository\SupplierProduct\SharePathFlowNodeRepository.cs" />
    <Compile Include="Repository\SupplierProduct\SharePathFlowRepository.cs" />
    <Compile Include="Repository\SupplierProduct\SupplierProductBaseRepository.cs" />
    <Compile Include="Repository\SupplierProduct\SupplierProductCategoryRepository.cs" />
    <Compile Include="Repository\SupplierProduct\SupplierProductRepository.cs" />
    <Compile Include="Repository\SupplierProduct\SupplierProductSkuRepository.cs" />
    <Compile Include="Repository\SupplierProduct\UserSupplierStatusRepository.cs" />
    <Compile Include="Repository\SupplierProduct\SupplierAddressRepository.cs" />
    <Compile Include="Repository\TkPrintProcessStateRecordRepository.cs" />
    <Compile Include="Repository\TkShipmentsInfoTranslateRecordRepository.cs" />
    <Compile Include="Repository\Tools\DbTableMetaDataRepository.cs" />
    <Compile Include="Repository\Tools\OrderLifeCycleToolReasonRepository.cs" />
    <Compile Include="Repository\Tools\OrderSyncAnalysisAutoReasonRepository.cs" />
    <Compile Include="Repository\Tools\OrderSyncAnalysisRecordStatusRepository.cs" />
    <Compile Include="Repository\UniversalModule\FxMessageDataSyncStatusRepository.cs" />
    <Compile Include="Repository\UserActionRecordRepository.cs" />
    <Compile Include="Repository\WaybillCodeRecycleRepository.cs" />
    <Compile Include="Repository\UserFxLoginRegionRecordRepository.cs" />
    <Compile Include="Repository\SubUserFxRepository.cs" />
    <Compile Include="Repository\WaybillRealCpCodeRepository.cs" />
    <Compile Include="Repository\UpdateOrderByProcedure.cs" />
    <Compile Include="Repository\WxUserFxRelationRepository.cs" />
    <Compile Include="Repository\MessageProcessLogRepository.cs" />
    <Compile Include="Repository\WareHouseSkuBindRelationRepository.cs" />
    <Compile Include="Repository\ProductSkuInfoFxRepository.cs" />
    <Compile Include="Repository\ProductInfoFxRepository.cs" />
    <Compile Include="Repository\PathFlowRepository.cs" />
    <Compile Include="Repository\ProductSkuFxRepository.cs" />
    <Compile Include="Repository\ShopExtensionRepository.cs" />
    <Compile Include="Repository\SupplierUserRepository.cs" />
    <Compile Include="Repository\FxUserShopRepository.cs" />
    <Compile Include="Repository\LogicOrderItemRepository.cs" />
    <Compile Include="Repository\LogicOrderRepository.cs" />
    <Compile Include="Repository\OrderFxRepository.cs" />
    <Compile Include="Repository\ProductFxRepository.cs" />
    <Compile Include="Repository\SyncQueryTaskRepository.cs" />
    <Compile Include="Repository\JDAreaInfoRepository.cs" />
    <Compile Include="Repository\BranchShareExportExcelTaskRepository.cs" />
    <Compile Include="Repository\ExportTaskRepository.cs" />
    <Compile Include="Repository\ShareWaybillRemedyDataRepository.cs" />
    <Compile Include="Repository\SubUserRepository.cs" />
    <Compile Include="Repository\DbConfigRepository.cs" />
    <Compile Include="Repository\BranchShareUsedStatisticRepository.cs" />
    <Compile Include="Repository\ExpressImportMappingRepository.cs" />
    <Compile Include="Repository\BranchShareRelationLogRepository.cs" />
    <Compile Include="Repository\OuterDbAcessRepository.cs" />
    <Compile Include="Repository\ShareWaybillAccountCheckingRecordRepository.cs" />
    <Compile Include="Repository\ShareBranchContactInfoRepository.cs" />
    <Compile Include="Repository\ShareWaybillCodeRecordRepository.cs" />
    <Compile Include="Repository\BranchShareRelationRepository.cs" />
    <Compile Include="Repository\LinkAreaRepository.cs" />
    <Compile Include="Repository\NaHuoLabelBatchRepository.cs" />
    <Compile Include="Repository\NaHuoLabelSortingRulesRepository.cs" />
    <Compile Include="Repository\NaHuoLabelTemplateRepository.cs" />
    <Compile Include="Repository\NaHuoLabelOrderItemRepository.cs" />
    <Compile Include="Repository\NaHuoLabelOrderRepository.cs" />
    <Compile Include="Repository\PddServiceOrderPushLogRepository.cs" />
    <Compile Include="Repository\CustomerVisitRecordRepository.cs" />
    <Compile Include="Repository\GetOldSystemRepository.cs" />
    <Compile Include="Repository\AgentRepository.cs" />
    <Compile Include="Repository\AppOrderListRepository.cs" />
    <Compile Include="Repository\EvaluateOrderRepository.cs" />
    <Compile Include="Repository\IOrderRepository.cs" />
    <Compile Include="Repository\LogisticAddServiceRepository.cs" />
    <Compile Include="Repository\LogisticBusinessTypesRepository.cs" />
    <Compile Include="Repository\CommentOrderRepository.cs" />
    <Compile Include="Repository\LogForOperatorRepository.cs" />
    <Compile Include="Repository\LogisticAccountInfoMappingsRepository.cs" />
    <Compile Include="Repository\FengQiaoOrderDataRepository.cs" />
    <Compile Include="Repository\ModifyOrderPriceRepository.cs" />
    <Compile Include="Repository\OrderLogisticInfoRepository.cs" />
    <Compile Include="Repository\PrintTemplateExtendRepository.cs" />
    <Compile Include="Repository\OrderSyncLogRepository.cs" />
    <Compile Include="Repository\CaiNiaoAccountBranchRepository.cs" />
    <Compile Include="Repository\MacAddressRepository.cs" />
    <Compile Include="Repository\AreaCodeInfoRepository.cs" />
    <Compile Include="Repository\BaseRepository.cs" />
    <Compile Include="Repository\CaiNiaoAuthInfoRepository.cs" />
    <Compile Include="Repository\CainiaoAuthOwnerRepository.cs" />
    <Compile Include="Repository\CaiNiaoAuthRelationRepository.cs" />
    <Compile Include="Repository\CommonSettingRepository.cs" />
    <Compile Include="Repository\CustomColumnExcelRepository.cs" />
    <Compile Include="Repository\AdvRepository.cs" />
    <Compile Include="Repository\CustomerColumnMappingRepository.cs" />
    <Compile Include="Repository\ExpressCpCodeMappingRepository.cs" />
    <Compile Include="Repository\ExpressCodeMappingRepository.cs" />
    <Compile Include="Repository\ExpressCompanyRepository.cs" />
    <Compile Include="Repository\LastCodeBuildRepository.cs" />
    <Compile Include="Repository\OpenSecrecySellerRepository.cs" />
    <Compile Include="Repository\OrderCategoryRepository.cs" />
    <Compile Include="Repository\LogisticPayTypesRepository.cs" />
    <Compile Include="Repository\ProductSkuRepository.cs" />
    <Compile Include="Repository\PurchaseConfigRepository.cs" />
    <Compile Include="Repository\OrderFilterRepository.cs" />
    <Compile Include="Repository\CustomerOrderItemRepository.cs" />
    <Compile Include="Repository\OrderItemRepository.cs" />
    <Compile Include="Repository\OrderModifiedRepository.cs" />
    <Compile Include="Repository\CustomerOrderRepository.cs" />
    <Compile Include="Repository\OrderRepository.cs" />
    <Compile Include="Repository\PreordainRepository.cs" />
    <Compile Include="Repository\PrintControlRepository.cs" />
    <Compile Include="Repository\PrintHistoryRepository.cs" />
    <Compile Include="Repository\PrintHistoryOrderRepository.cs" />
    <Compile Include="Repository\PrintOrderProductRepository.cs" />
    <Compile Include="Repository\PrintTemplateRepository.cs" />
    <Compile Include="Repository\ProductRepository.cs" />
    <Compile Include="Repository\ProductSkuAttributeRepository.cs" />
    <Compile Include="Repository\ProductStatusRepository.cs" />
    <Compile Include="Repository\KuaiDiNiaoLogisticTracesRepository.cs" />
    <Compile Include="Repository\PurchaseRepository.cs" />
    <Compile Include="Repository\ReciverInfoRepository.cs" />
    <Compile Include="Repository\SellerInfoRepository.cs" />
    <Compile Include="Repository\SendGoodTemplateRepository.cs" />
    <Compile Include="Repository\SendHistoryRepository.cs" />
    <Compile Include="Repository\SendOrderProductRepository.cs" />
    <Compile Include="Repository\SendHistoryOrderRepository.cs" />
    <Compile Include="Repository\ShopRelationRepository.cs" />
    <Compile Include="Repository\ShopRepository.cs" />
    <Compile Include="Repository\SkuAttributeRepository.cs" />
    <Compile Include="Repository\StapleTemplateRepository.cs" />
    <Compile Include="Repository\SyncStatusRepository.cs" />
    <Compile Include="Repository\SyncTaskRepository.cs" />
    <Compile Include="Repository\SynUserInfoRepository.cs" />
    <Compile Include="Repository\SysConfigRepository.cs" />
    <Compile Include="Repository\PrinterBindRepository.cs" />
    <Compile Include="Repository\CustomerStaffRepository.cs" />
    <Compile Include="Repository\OpenPlatformAppRepository.cs" />
    <Compile Include="Repository\PlatformRemarkRepository.cs" />
    <Compile Include="Repository\FxUserAddressRepository.cs" />
    <Compile Include="Repository\UserFxRepository.cs" />
    <Compile Include="Repository\UserVerificationCodeRepository.cs" />
    <Compile Include="Repository\WareHouseRepository.cs" />
    <Compile Include="Repository\WaybillCodeOrderProductRepository.cs" />
    <Compile Include="Repository\WaybillCodeUseRecordRepository.cs" />
    <Compile Include="Repository\WaybillCodeChildRepository.cs" />
    <Compile Include="Repository\WxAppIdTokenRepository.cs" />
    <Compile Include="Repository\WxBluetoothPrinterRepository.cs" />
    <Compile Include="Repository\CommonQRCodeRepository.cs" />
    <Compile Include="Repository\WxVisitingCardQRCodeRepository.cs" />
    <Compile Include="Repository\WxUserInfoRepository.cs" />
    <Compile Include="Repository\UserInfoRepository.cs" />
    <Compile Include="Repository\WxQRcodeRepository.cs" />
    <Compile Include="Repository\WxConcernRepository.cs" />
    <Compile Include="Repository\UserRepository.cs" />
    <Compile Include="Repository\WaybillCodeCheckRepository.cs" />
    <Compile Include="Repository\UserSiteInfoRepository.cs" />
    <Compile Include="Repository\TemplateRelationAuthInfoRepository.cs" />
    <Compile Include="Repository\WaybillCodeOrderRepository.cs" />
    <Compile Include="Repository\TemplateLogisticsSvsRepository.cs" />
    <Compile Include="Repository\WaybillCustomAreaRepository.cs" />
    <Compile Include="Repository\WaybillCodeRepository.cs" />
    <Compile Include="Repository\WxVisitingRepository.cs" />
    <Compile Include="Repository\WxXiaDanRelationRepository.cs" />
    <Compile Include="BaseSiteContext.cs" />
    <Compile Include="SqlOptimizationHandler.cs" />
    <Compile Include="TKPackageShippingModel.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\AutoComment\DianGuanJiaApp.RabbitMQ\DianGuanJiaApp.RabbitMQ.csproj">
      <Project>{90dc55be-abba-4686-8119-a18e012f540e}</Project>
      <Name>DianGuanJiaApp.RabbitMQ</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Core\DianGuanJiaApp.Core.csproj">
      <Project>{93ca297d-8704-48d8-8748-67ef45304f88}</Project>
      <Name>DianGuanJiaApp.Core</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Trace.ViewModels\DianGuanJiaApp.Trace.ViewModels.csproj">
      <Project>{7eb493fb-b565-4c96-81f4-bcdf9393d1b8}</Project>
      <Name>DianGuanJiaApp.Trace.ViewModels</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Utility\DianGuanJiaApp.Utility.csproj">
      <Project>{a0ead1ef-8b94-4487-ad73-f981cb45e58f}</Project>
      <Name>DianGuanJiaApp.Utility</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.ViewModels\DianGuanJiaApp.ViewModels.csproj">
      <Project>{549a33cf-f7fb-49ef-b8bd-1aae56317663}</Project>
      <Name>DianGuanJiaApp.ViewModels</Name>
    </ProjectReference>
    <ProjectReference Include="..\jos-net-open-api-sdk-2.0\jos-sdk-net.csproj">
      <Project>{cf7757d6-4f03-4bca-948d-d1e0b81b491c}</Project>
      <Name>jos-sdk-net</Name>
    </ProjectReference>
    <ProjectReference Include="..\ych-sdk\ych-sdk.csproj">
      <Project>{aaf61c3f-5729-4e3c-bc8c-eea8067e0305}</Project>
      <Name>ych-sdk</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="ILLink\ILLink.Descriptors.LibraryBuild.xml" />
    <Content Include="SQL\AfterSaleOrderItem表冗余PathFlowCode字段.sql" />
    <Content Include="SQL\P_SendOrderProduct冗余字段.sql" />
    <Content Include="SQL\OrderStatus和OrderItemStatus建表语句.sql" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>