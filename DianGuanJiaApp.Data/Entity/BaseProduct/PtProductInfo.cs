using Dapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.Entity.BaseProduct
{
    /// <summary>
    /// 商品平台资料
    /// </summary>
    [Table("PtProductInfo")]
    public partial class PtProductInfo : BaseEntity
    {
        [Key]
        public long Id { get; set; }
        /// <summary>
        /// 所属平台，如：TouTiao\KuaiShou
        /// </summary>
        public string PlatformType { get; set; }
        /// <summary>
        /// 用户自己的基础商品Uid，来源是自己的货盘或商品库才有值，来源厂家此值为0
        /// </summary>
        public long BaseProductUid { get; set; }
        /// <summary>
        /// 唯一Code：Guid().ToShortMd5()
        /// </summary>
        public string UniqueCode { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int FxUserId { get; set; }
        /// <summary>
        /// 所属店铺Id，默认0=平台通用资料,大于0=各店铺专属
        /// </summary>
        public int ShopId { get; set; }
        /// <summary>
        /// 创建来源，可选值如下：
        /// SelfBaseProduct=自己的基础商品
        /// SelfSupplierProduct = 自己的货盘
        /// SupplierProduct=厂家货盘
        /// SupplierPtProduct = 厂家平台资料
        /// </summary>
        public string CreateFrom { get; set; }
        /// <summary>
        /// 来源用户：可能是自己或厂家
        /// </summary>
        public int FromFxUserId { get; set; }
        /// <summary>
        /// 数据源关联的BaseProductUid，来源自己时此值等于BaseProductUid
        /// </summary>
        public long FromBaseProductUid { get; set; }
        /// <summary>
        /// 数据源关联的货盘Uid，来源货盘商品时有值，可能是自己或厂家的货盘商品Uid
        /// </summary>
        public long? FromSupplierProductUid { get; set; }
        /// <summary>
        /// 商家编码
        /// </summary>
        public string SpuCode { get; set; }
        /// <summary>
        /// 商品标题
        /// </summary>
        public string Subject { get; set; }
        /// <summary>
        /// 最后一级类目Id
        /// </summary>
        public string CategoryId { get; set; }
        /// <summary>
        /// 运费模板Id，0表示包邮
        /// </summary>
        public long FreightId { get; set; }
        /// <summary>
        /// 状态：false 无需同步、true 待同步
        /// 现用于当基础资料规格层数有变化时，此值为true
        /// </summary>
        public bool IsWaitSyncBaseProduct { get; set; }
        /// <summary>
        /// 创建时间，默认=当前时间
        /// </summary>
        public DateTime CreateTime { get; set; }
        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }
        /// <summary>
        /// 状态，默认1正常，0已删除，-1草稿(铺货任务)
        /// </summary>
        public int Status { get; set; }
        /// <summary>
        /// 是否公开：是否允许分销使用平台资料
        /// </summary>
        public bool IsPublic { get; set; }
        /// <summary>
        /// 根路径节点的用户ID
        /// </summary>
        public int? RootNodeFxUserId {get;set;}
        /// <summary>
        /// 分享路径流Code，跟随基础商品
        /// </summary>
        public string SharePathCode { get; set; }
        /// <summary>
        /// 节点深度，跟随基础商品
        /// </summary>
        public int PathNodeDeep { get; set; }

        /// <summary>
        /// 需完善
        /// </summary>
        public bool NeedUpdate { get; set; } = false;
    }
}
