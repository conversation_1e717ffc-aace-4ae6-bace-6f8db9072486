using Dapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.Entity.BaseProduct
{
    /// <summary>
    /// 商品平台资料扩展表
    /// </summary>
    [Table("PtProductInfoExt")]
    public class PtProductInfoExt : BaseEntity
    {
        /// <summary>
        /// 
        /// </summary>
        [Key]
        public long Id { get; set; }
        /// <summary>
        /// 主表的UniqueCode
        /// </summary>
        public string PtProductUniqueCode { get; set; }
        /// <summary>
        /// 类目集Json List<CateItem>().ToJson()=>CateItem:Id,Name,ParentId,Level
        /// </summary>
        public string CategoryJson { get; set; }
        /// <summary>
        /// List-PtProductInfoImageModel.ToJson()=>PtProductInfoImageModel:ObjectId,FullUrl,IsMain
        /// </summary>
        public string MainImageJson { get; set; }
        /// <summary>
        /// 详细描述；图片列表，多张逗号隔开
        /// </summary>
        public string Description { get; set; }
        /// <summary>
        /// 类目的属性，JSON格式
        /// </summary>
        public string CategoryAttributes { get; set; }
        /// <summary>
        /// List<PtProductInfoSkuModel>().ToJson()
        /// </summary>
        public string SkuJson { get; set; }
        /// <summary>
        /// 平台属性类型
        /// </summary>
        public string AttributeTypesJson { get; set; }
        /// <summary>
        /// 创建时间，默认=当前时间
        /// </summary>
        public DateTime CreateTime { get; set; }
        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 铺货设置：多品商品维度设置，单品NULL
        /// </summary>
        public string SettingJson { get; set; }

        /// <summary>
        /// 类目推荐Id
        /// </summary>
        public string RecommendIds { get; set; }
    }
}
