using Dapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.FxModel.Listing
{
    /// <summary>
    /// 铺货设置
    /// </summary>
    public class ListingSetting
    {
        public int Id { get; set; }
        /// <summary>
        /// 是否默认
        /// </summary>
        public bool IsDefault { get; set; }
        /// <summary>
        /// 平台
        /// </summary>
        public string PlatformType { get; set; }

        public ListingSettingValue ListingSettingValue { get; set; }

        public BatchListingSettingValue BatchSettingValue { get; set; }
        
        ///// <summary>
        ///// 铺货运费模板分组Code
        ///// </summary>
        //public string ListingLogisticsTemplateGroupCode { get; set; }

        /// <summary>
        /// 售后客服手机号
        /// </summary>
        public string ServiceMobile { get; set; }
    }
    /// <summary>
    /// 值
    /// </summary>
    public class ListingSettingValue
    {
        /// <summary>
        /// 完成铺货的商品状态 1-立即上架、2-放入仓库、3-放入草稿箱
        /// </summary>
        public ProductStatus ProductStatus { get; set; }
        /// <summary>
        /// 发货
        /// </summary>
        public DeliverySetting Delivery { get; set; }
        /// <summary>
        /// 履约
        /// </summary>
        public HonourAgreementSetting HonourAgreement { get; set; }
        /// <summary>
        /// 库存计数 1 下单减库存、2 付款减库存
        /// </summary>
        public WarehouseCalcRule WarehouseCalcRule { get; set; }
    }

    /// <summary>
    /// 批量铺货设置
    /// </summary>
    public class BatchListingSettingValue: ListingSettingValue
    {
        /// <summary>
        /// 价格设置的顺序  ByDouyinPrice,ByPurChasePrice,ByDistributorPrice
        /// </summary>
        public string PriceSetting { get; set; }

        /// <summary>
        /// 取抖音资料的商品售价
        /// </summary>
        public bool ByDouyinPrice { get; set; }

        #region 取商品的采购价
        /// <summary>
        /// 取商品的采购价
        /// </summary>
        public bool ByPurChasePrice { get; set; }

        /// <summary>
        /// 取商品的采购价-百分比(正整数)    20%
        /// </summary>
        public string ByPurChasePrice_Percent { get; set; }

        /// <summary>
        /// 取商品的采购价-加价
        /// </summary>
        public decimal ByPurChasePrice_Num { get; set; }
        #endregion

        #region 取商品的分销价
        /// <summary>
        /// 取商品的分销价
        /// </summary>
        public bool ByDistributorPrice { get; set; }

        /// <summary>
        /// 取商品的分销价-百分比(正整数)    20%  
        /// </summary>
        public string ByDistributorPrice_Percent { get; set; }

        /// <summary>
        /// 取商品的分销价-加价
        /// </summary>
        public decimal ByDistributorPrice_Num { get; set; }
        #endregion

        /// <summary>
        /// 角分处理 "" ：保留角和分。有值：统一改为
        /// </summary>
        public string MinuteOfArc { get; set; }

        /// <summary>
        /// 库存规则 "" ：和源库存保持一致。有值：统一改为
        /// </summary>
        public string Repertory { get; set; }


        public void SetDefault()
        {
            PriceSetting = "ByDouyinPrice,ByPurChasePrice,ByDistributorPrice";
            ByDouyinPrice = true;
            ByPurChasePrice = true;
            ByDistributorPrice = true;
            MinuteOfArc = string.Empty; // 角分
            Repertory = string.Empty; // 库存规则
            Delivery = new DeliverySetting() { DeliveryMode = DeliveryMode.SpotGoods, DeliveryTime = DeliveryTime.FortyEightHour }; // 发货
            HonourAgreement = new HonourAgreementSetting() { AftersalesPolicy = AftersalesPolicy.SevenDaysWithoutReason, ExpressTemplateGroupCode = "" }; // 履约
            WarehouseCalcRule = WarehouseCalcRule.PlaceOrder; // 库存计数
        }
    }

    /// <summary>
    /// 发货
    /// </summary>
    public class DeliverySetting
    {
        /// <summary>
        /// 发货模式 1-现货发货模式
        /// </summary>
        public DeliveryMode DeliveryMode { get; set; }
        /// <summary>
        /// 发货时间 1-当日发、2-次日发、3-48小时内发货
        /// </summary>
        public DeliveryTime DeliveryTime { get; set; }
    }
    /// <summary>
    /// 履约
    /// 根据类目查询规则，判断物流退货规则是否必填。如果必填，则展示字段。查询可支持的类型，默认选择7天无理由
    /// </summary>
    public class HonourAgreementSetting
    {
        /// <summary>
        /// 运费模板（即运费模板类型）
        /// </summary>
        public string ExpressTemplateGroupCode { get; set; }
        /// <summary>
        /// 售后政策 1 代表7天无理由退货
        /// </summary>
        public AftersalesPolicy AftersalesPolicy { get; set; }
    }

    #region 相关的枚举
    /// <summary>
    /// 铺货商品状态
    /// </summary>
    public enum ProductStatus
    {
        /// <summary>
        /// 立即上架
        /// </summary>
        Publish = 1,
        /// <summary>
        /// 放入仓库
        /// </summary>
        Warehouse = 2,
        /// <summary>
        /// 放入草稿箱
        /// </summary>
        Drafts = 3
    }
    /// <summary>
    /// 库存计算规则
    /// </summary>
    public enum WarehouseCalcRule
    {
        /// <summary>
        /// 下单减库存
        /// </summary>
        PlaceOrder = 1,
        /// <summary>
        /// 付款减库存
        /// </summary>
        Payment = 2
    }
    /// <summary>
    /// 发货模式
    /// </summary>
    public enum DeliveryMode
    {
        /// <summary>
        /// 现货发货模式
        /// </summary>
        SpotGoods = 1
    }
    /// <summary>
    /// 发货时间
    /// </summary>
    public enum DeliveryTime
    {
        /// <summary>
        /// 当天发
        /// </summary>
        SameDay = 1,
        /// <summary>
        /// 次日发
        /// </summary>
        NextDay = 2,
        /// <summary>
        /// 48小时内
        /// </summary>
        FortyEightHour = 3
    }
    /// <summary>
    /// 售后政策
    /// </summary>
    public enum AftersalesPolicy
    {
        /// <summary>
        /// 七天无理由
        /// </summary>
        SevenDaysWithoutReason = 1
    }
    #endregion
}
