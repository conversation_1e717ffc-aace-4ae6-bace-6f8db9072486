using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace DianGuanJiaApp.Data.FxModel.Listing
{
    /// <summary>
    /// 铺货记录查询Model
    /// </summary>
    public class ListingTaskRecordQuery
    {
        /// <summary>
        /// 查询模型
        /// </summary>
        public ListingTaskRecordQuery()
        {
            PageIndex = 1;
            PageSize = 10;
        }

        /// <summary>
        /// 页码
        /// </summary>
        public int PageIndex { get; set; }
        /// <summary>
        /// 页大小
        /// </summary>
        public int PageSize { get; set; }
        /// <summary>
        /// 状态 Created待铺货，Doing正在铺货，Error失败，Success成功
        /// </summary>
        public List<string> Status { get; set; }

        /// <summary>
        /// 目标店铺
        /// </summary>
        public int TargetShopId { get; set; }

        /// <summary>
        /// 商品名称
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 用户Id
        /// </summary>
        public int FxUserId { get; set; }
        
        /// <summary>
        /// 任务Status
        /// </summary>
        public string TaskStatus { get; set; }
        
        /// <summary>
        /// 任务Status映射
        /// </summary>
        [JsonIgnore]
        public static readonly Dictionary<string, List<string>> StatusDic = new Dictionary<string, List<string>>
        {
            {"Doing", new List<string> { "Doing", "WaitRetryLocked", "WaitRetry", "WaitRetryDoing", "Locked", "Created" } },
            {"Success", new List<string> { "Success", "SuccessWaitCallBack", "SuccessReject", "SuccessInDrafts", "PartSuccess", "PartError" } },
            {"Error", new List<string> { "Error" } }
        };
    }
}
