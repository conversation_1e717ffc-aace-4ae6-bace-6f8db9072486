using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace DianGuanJiaApp.Data.FxModel.CategoryProduct
{
    public class GetBrandReqModel :IValidatableObject
    {
        /// <summary>
        /// 当前店铺Id
        /// 0: 平台资料编辑场景。会自动寻找ShopId的
        /// </summary>
        public int ShopId { get; set; }

        /// <summary>
        /// 类目Id
        /// </summary>
        [Required(ErrorMessage = "类目Id 不能为空")]
        public string CateId { get; set; }

        /// <summary>
        /// 当前平台
        /// </summary>
        public string PlatformType { get; set; }

        public int IsCache { get; set; }

        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            if (ShopId < 0)
            {
                yield return new ValidationResult("ShopId 输入错误，请重新输入");
            }
        }
    }
}
