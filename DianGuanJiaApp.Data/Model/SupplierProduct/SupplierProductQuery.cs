using System.Collections.Generic;

namespace DianGuanJiaApp.Data.Model.SupplierProduct
{
    /// <summary>
    /// 分页查询模型
    /// </summary>
    public class SupplierProductQuery
    {
        /// <summary>
        /// 查询模型
        /// </summary>
        public SupplierProductQuery()
        {
            PageIndex = 1;
            PageSize = 10;
        }

        /// <summary>
        /// 页码
        /// </summary>
        public int PageIndex { get; set; }

        /// <summary>
        /// 页大小
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// 字段
        /// </summary>
        public List<string> Fields { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public int FxUserId { get; set; }

        /// <summary>
        /// 标记，1-我的小站 2-厂商小站 3-选品铺货
        /// </summary>
        public int Tag { get; set; }

        /// <summary>
        /// 厂家Id，当Tag=2时必填，Tag=3时选填
        /// </summary>
        public List<int> SupplierFxUserId { get; set; }

        /// <summary>
        /// 排序字段 上新时间PublicTime 供货价DistributePrice 发货量ShipmentsCount 退货率 RefundRate
        /// </summary>
        public string OrderByField { set; get; }

        /// <summary>
        /// 是否排序
        /// </summary>
        public bool IsOrderDesc { set; get; }

        /// <summary>
        /// 商品名称
        /// </summary>
        public string ProductName { set; get; }

        /// <summary>
        /// 查看上架或下架商品，true-上架 false-下架 null-全部
        /// </summary>
        public bool? IsPublic { set; get; } = true;

        /// <summary>
        /// 商品类目，仅Tag = 3时填写
        /// </summary>
        public List<int> CategoryId { get; set; }

        /// <summary>
        /// 面单，仅Tag = 3时填写
        /// </summary>
        public List<int> ExpressBillId { get; set; }

        /// <summary>
        /// 是否初始化判断数量
        /// </summary>
        public bool IsInit { get; set; } = false;
        
        /// <summary>
        /// 商品类型，默认空，空为小站商品，按平台类型记录
        /// </summary>
        public string ProductType { get; set; }
    }
}