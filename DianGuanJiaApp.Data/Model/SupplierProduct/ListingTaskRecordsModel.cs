using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Entity.BaseProduct;
using DianGuanJiaApp.Data.FxModel.Listing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DianGuanJiaApp.Data.Model
{
    /// <summary>
    /// 查询模型
    /// </summary>
    public class ListingTaskRecordsQuery
    {
        /// <summary>
        /// 查询模型
        /// </summary>
        public ListingTaskRecordsQuery()
        {
            PageIndex = 1;
            PageSize = 10;
        }

        /// <summary>
        /// 页码
        /// </summary>
        public int PageIndex { get; set; }
        /// <summary>
        /// 页大小
        /// </summary>
        public int PageSize { get; set; }
        /// <summary>
        /// 字段
        /// </summary>
        public List<string> Fields { get; set; }
        /// <summary>
        /// 用户ID
        /// </summary>
        public int FxUserId { get; set; }

        /// <summary>
        /// 铺货任务Code
        /// </summary>
        public string ListingTaskCode { get; set; }

        /// <summary>
        /// 是否要取扩展表数据
        /// </summary>
        public bool IsNeedJoinExt { get; set; }
    }

    /// <summary>
    /// 通过商品详情接口查询数据
    /// </summary>
    public class GetProductFromApiModel
    {
        /// <summary>
        /// 商品Id
        /// </summary>
        public string ProductId { get; set; }
        /// <summary>
        /// 平台类型
        /// </summary>
        public string PlatformType { get; set; }
        /// <summary>
        /// 店铺Id
        /// </summary>
        public int ShopId { get; set; }
        /// <summary>
        /// 所属用户
        /// </summary>
        public int FxUserId { get; set; }
    }


    /// <summary>
    /// 处理绑定厂家&路径流数据
    /// </summary>
    public class BindSupplierForListingModel
    {
        /// <summary>
        /// 路径流
        /// </summary>
        public List<PathFlow> PathFlows { get; set; }
        /// <summary>
        /// 路径关系
        /// </summary>
        public List<PathFlowReference> PathFlowReferences { get; set; }
        /// <summary>
        /// 所属用户
        /// </summary>
        public int FxUserId { get; set; }
    }



    #region 铺货任务相关Model
    /// <summary>
    /// 铺货任务-店铺铺货设置
    /// </summary>
    public class ShopListingConfigSaveRequest
    {
        /// <summary>
        /// 构造时初始默认值
        /// </summary>
        public ShopListingConfigSaveRequest()
        {
            IsReserveDecimal = true;
        }

        /// <summary>
        /// 店铺id
        /// </summary>
        public int ShopId { get; set; }

        /// <summary>
        /// 平台
        /// </summary>
        public string PlatformType { get; set; }

        /// <summary>
        /// 是否智能匹配类目
        /// </summary>
        public bool IsAutoMappingCate { get; set; }

        /// <summary>
        /// 选择的商品类目
        /// 1-n级，按顺序排列
        /// </summary>
        public List<CateItem> CateList { get; set; }

        /// <summary>
        /// 实际售价百分比
        /// </summary>
        public decimal SalePricePercent { get; set; }

        /// <summary>
        /// 建议售价百分比
        /// </summary>
        public decimal SuggestSalePrice { get; set; }

        /// <summary>
        /// 加售价，单位元
        /// </summary>
        public decimal AddSalePrice { get; set; }

        /// <summary>
        /// 建议加售价，单位元
        /// </summary>
        public decimal AddSuggestSalePrice { get; set; }

        /// <summary>
        /// 是否保留小数，不保留则四舍五入小数
        /// </summary>
        public bool IsReserveDecimal { get; set; }


        /// <summary>
        /// 库存计数设置，两个值：CreateTime\PayTime
        /// </summary>
        public string StockCountTime { get; set; }

        /// <summary>
        /// 运费模板
        /// </summary>
        public string PostageId { get; set; }

        /// <summary>
        /// 运费模板,名称
        /// </summary>
        public string PostageName { get; set; }


        /// <summary>
        /// 物流模板Id
        /// </summary>
        public string LogisticsId { get; set; }

        /// <summary>
        /// 物流模板,名称
        /// </summary>
        public string LogisticsName { get; set; }

        /// <summary>
        /// true=保存并提交，false=仅保存
        /// </summary>
        public bool IsDirectUpload { get; set; }

        /// <summary>
        /// 预售商品承诺发货时间：[4,90] 建议用下拉框选择【不预售-值为0】【4】...【90】
        /// </summary>
        public int TheDayOfDeliverGoodsTime { get; set; }

        /// <summary>
        /// 非预售商品承诺发货时间,单选：【不承若-值为-1】，【24小时】、【48小时】、【72小时】。
        /// 预售和非预售发货时间，只能二选一。选其中一个，另外一个变成不选择.
        /// 抖店：支持传入9999 、1、 2 （分别表示当日发、次日发、48小时发）
        /// </summary>
        public int PromiseDeliveryTime { get; set; }

        /// <summary>
        /// 时效，格式：数组
        /// 目前抖音那边用到
        /// </summary>
        public List<SpecsItem> TimeLinessList { get; set; }

        /// <summary>
        /// 是否默认设置，默认true
        /// </summary>
        public bool IsDefault { get; set; }

        /// <summary>
        /// 售后客服手机号，抖店铺货必填
        /// </summary>
        public string ServiceMobile { get; set; }

        /// <summary>
        /// 是否限购
        /// </summary>
        public bool IsLimitBuy { get; set; }

        /// <summary>
        /// 每个用户限购 数量
        /// </summary>
        public int BuyerLimitNum { get; set; }


        /// <summary>
        /// 每个用户每单限购 数量，抖店铺货参数
        /// </summary>
        public int OrderMaxBuyerNum { get; set; }


        /// <summary>
        /// 每个用户每单最少购买 数量，抖店铺货参数
        /// </summary>
        public int OrderMinBuyerNum { get; set; }

        /// <summary>
        /// 是否支持假一赔十
        /// </summary>
        public bool IsFolt { get; set; }

        /// <summary>
        /// 是否7天无理由退换货，true-支持，false-不支持
        /// </summary>
        public bool IsRefundable { get; set; }

        /// <summary>
        /// 拼多多使用固定售价比例
        /// </summary>
        public bool UseFixedPercentPriceConfig { get; set; }

        /// <summary>
        /// 快团团发布商品后开团时间（单位小时）
        /// </summary>
        public int GroupStartTime { get; set; }

        /// <summary>
        /// 快团团开团结束时间（单位天）
        /// </summary>
        public int GroupEndTime { get; set; }

        /// <summary>
        /// 必填规格
        /// </summary>
        public string SkuRequireProp { get; set; }

        /// <summary>
        /// 自动合并规格
        /// </summary>
        public string SkuAutoMerge { get; set; }

        /// <summary>
        /// 自动填充规格
        /// </summary>
        public string SkuAutoFillProp { get; set; }

        /// <summary>
        /// 自动填充规格值
        /// </summary>
        public string SkuAutoFillPropValue { get; set; }

        /// <summary>
        /// 是否迁移视频
        /// </summary>
        public string IsMigrationVideo { get; set; }

        /// <summary>
        /// 过滤素材审核失败图片(0 表示关闭过滤，其他表示开启)
        /// </summary>
        public string FilterMaterial { get; set; }

        /// <summary>
        /// 淘宝铺货时，出现素材保护中，丢弃图片（0 关闭，其他表示开启）
        /// </summary>
        public string MaterialProtectFilter { get; set; }

        /// <summary>
        /// 检查是否已经铺货(1 表示开启，其他表示)
        /// </summary>
        public string CheckIsListing { get; set; }

        /// <summary>
        /// 坏了包退
        /// </summary>
        public string IfreshRotRefund { get; set; }
        /// <summary>
        /// (破损包退)
        /// </summary>
        public string BrokenRefund { get; set; }

        /// <summary>
        /// (过敏包退)
        /// </summary>
        public string AllergyRefund { get; set; }


        /// <summary>
        /// 三包-售后天数
        /// </summary>
        public int ThreeGuaranteesByDuration { get; set; }

        /// <summary>
        /// 三包-服务类型
        /// </summary>
        public int ThreeGuaranteesByServiceType { get; set; }


        /// <summary>
        /// 新增版本
        /// 现用于pdd临时修改如果修改完成则不需要重复去读取默认配置
        /// </summary>
        public int Version { get; set; }

        /// <summary>
        /// 抖店start_sale_type:0=立即上架，1=放入仓库
        /// </summary>
        public int StartSaleType { get; set; }
    }

    /// <summary>
    /// 类目项
    /// </summary>
    public class CateItem
    {

        /// <summary>
        /// 类目CateId
        /// </summary>
        public string CateId { get; set; }

        /// <summary>
        /// 类目名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 父级类目Id
        /// </summary>
        public string ParentId { get; set; }

        /// <summary>
        /// 类目层数
        /// </summary>
        public int Level { get; set; }
        
    }


    /// <summary>
    /// 规则项
    /// </summary>
    public class SpecsItem
    {

        /// <summary>
        /// 是否预售时效
        /// </summary>
        public bool IsPresellSpec { get; set; }

        /// <summary>
        /// 时效(单位：天)
        /// </summary>
        public int Days { get; set; }

        /// <summary>
        /// 时效库存百分比
        /// </summary>
        public int StockPercentum { get; set; }
    }


    /// <summary>
    /// 铺货模型
    /// </summary>
    public class PlatformListingModel
    {
        /// <summary>
        /// 目标所在云平台
        /// </summary>
        public string TargetPlatform { get; set; }

        /// <summary>
        /// 目标平台，铺货到哪个平台
        /// </summary>
        public string TargetPlatformType { get; set; }

        /// <summary>
        /// 铺货目标店铺Id
        /// </summary>
        public int TargetShopId { get; set; }

        /// <summary>
        /// 第一级类目
        /// </summary>
        public CateItem FirstCate { get; set; }

        /// <summary>
        /// 最后一级类目
        /// </summary>
        public CateItem LastCate { get; set; }

        /// <summary>
        /// 平台资料
        /// </summary>
        public PtProductInfo PtProductInfo { get; set; }

        /// <summary>
        /// 铺货设置
        /// </summary>
        public ShopListingConfigSaveRequest ListingConfig { get; set; }

        /// <summary>
        /// 传一个Id或者code，视频号需要用这个来生成唯一码，所以需要传任务code或者是id之类的，配合skuid生成唯一码
        /// </summary>
        public string TaskCode { get; set; }

        /// <summary>
        /// 拼多多平台独有
        /// </summary>
        public long MaxWeight { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string DefaultCateId { get; set; }

        /// <summary>
        /// 抖音独有
        /// </summary>
        public string TaskParam { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool IsUseOtherSizeInfo { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int ExcuteTimes { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string BuyerShopUid { get; set; }
    }
    #endregion

    /// <summary>
    /// 铺货任务状态查询
    /// </summary>
    public class ListingTaskStatusQuery
    {
        /// <summary>
        /// 批次号
        /// </summary>
        public string BatchNo { get; set; }

        /// <summary>
        /// 任务Code（重新铺货时用）优先级高于BatchNo
        /// </summary>
        public string TaskCode { get; set; }

        /// <summary>
        /// 用户Id
        /// </summary>
        public int FxUserId { get; set; }
    }

    /// <summary>
    /// 铺货任务状态统计
    /// </summary>
    public class ListingTaskStatusStat
    {
        /// <summary>
        /// 批次号
        /// </summary>
        public string BatchNo { get; set; }

        /// <summary>
        /// 任务Code
        /// </summary>
        public string TaskCode { get; set; }

        /// <summary>
        /// 总任务数
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 待执行的数量
        /// </summary>
        public int WaitCount { get; set; }

        /// <summary>
        /// 执行中的数量
        /// </summary>
        public int DoingCount { get; set; }

        /// <summary>
        /// 铺货失败的数量
        /// </summary>
        public int FailCount { get; set; }

        /// <summary>
        /// 铺货成功的数量
        /// </summary>
        public int SuccessCount { get; set; }
    }
}