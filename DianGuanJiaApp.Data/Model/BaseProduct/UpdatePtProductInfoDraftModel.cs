using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.Model.BaseProduct
{
    /// <summary>
    /// 铺货草稿价格修改
    /// </summary>
    public class UpdatePtProductInfoDraftModel
    {
        /// <summary>
        /// 唯一Code：Guid().ToShortMd5()
        /// </summary>
        public string UniqueCode { get; set; }

        /// <summary>
        /// 价格类型：0 售价、1 采购价、2 分销价
        /// </summary>
        public int PriceType { get; set; }

        /// <summary>
        /// 价格修改方式：0 统一修改、1 公式修改
        /// </summary>
        public int PriceUpdateType { get; set; }

        /// <summary>
        /// 价格百分比（公式修改）
        /// </summary>
        public double? PricePercentage { get; set; }

        /// <summary>
        /// 价格增量（公式修改）
        /// </summary>
        public Decimal? PriceIncrement { get; set; }

        /// <summary>
        /// 价格统一值（统一修改）
        /// </summary>
        public Decimal? PriceCommon { get; set; }

        /// <summary>
        /// 价格单位：0 保留角分 1 统一修改
        /// </summary>
        public int PriceUnitType { get; set; }

        /// <summary>
        /// 价格角分自定义(两位数)
        /// </summary>
        public int? PriceCornerFen { get; set; }
    }
}
