using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.Model.BaseProduct
{
    /// <summary>
    /// 
    /// </summary>
    public class PtInfoReqModel
    {
        /// <summary>
        /// 基础商品uid/货盘Uid
        /// </summary>
        public long ProductUid { get; set; }

        /// <summary>
        /// 平台类型
        /// </summary>
        public string PlatformType { get; set; }

        /// <summary>
        /// 0: 根据基础商品铺货、1：根据平台资料（需要拿其基础资料的Uid）、2：根据自己货盘、3：根据厂商货盘 4：铺货任务
        /// </summary>
        public int FromType { get; set; }

        /// <summary>
        /// 用户id 如果是从厂商铺货，则为厂商的FxUserId，其余默认为自身的FxUserId
        /// </summary>
        public int FxUserId { get; set; }

        /// <summary>
        /// 平台资料code 根据FromType来
        /// </summary>
        public string FromCode { get; set; }


        public string Token { get; set; }   

        /// <summary>
        /// 铺货选择店铺
        /// </summary>
        public List<int>ShopIds { get; set; }
    }
}
