
namespace DianGuanJiaApp.Data.Model.BaseProduct
{
    /// <summary>
    /// 平台资料Sku
    /// </summary>
    public class PtProductInfoSkuModel
    {
        /// <summary>
        /// 数据源关联的BaseProductSkuUid (long类型在前端有丢失精度的风险)
        /// </summary>
        public string FromBaseProductSkuUid { get; set; }
        /// <summary>
        /// 数据源关联的货盘Sku.Uid (long类型在前端有丢失精度的风险)
        /// </summary>
        public string FromSupplierProductSkuUid { get; set; }
        /// <summary>
        /// 规格编码
        /// </summary>
        public string SkuCode { get; set; }
        /// <summary>
        /// 标题
        /// </summary>
        public string Subject { get; set; }
        /// <summary>
        /// 属性Json，如：[{"k":"颜色","v":"白色"},{"k":"尺寸","v":"XL"},{"k":"材质","v":"棉质"}]
        /// </summary>
        public string Attributes { get; set; }
        /// <summary>
        /// 分销价，单位元
        /// </summary>
        public decimal DistributePrice { get; set; }
        /// <summary>
        /// 销售价，单位元
        /// </summary>
        public decimal SalePrice { get; set; }

        /// <summary>
        /// 采购价格，单位元
        /// </summary>
        public decimal SettlePrice { get; set; }
        
        /// <summary>
        /// 库存
        /// </summary>
        public int StockCount { get; set; }
        /// <summary>
        /// 图片对应的ObjectId
        /// </summary>
        public string ImageObjectId { get; set; }
        /// <summary>
        /// 图片完整路径
        /// </summary>
        public string ImageUrl { get; set; }
        /// <summary>
        /// 其他内容Json
        /// </summary>
        public string OtherJson { get; set; }
        /// <summary>
        /// 根路径节点的用户ID
        /// </summary>
        public int? RootNodeFxUserId { get; set; }
        /// <summary>
        /// 分享路径流Code，跟随基础商品
        /// </summary>
        public string SharePathCode { get; set; }
        /// <summary>
        /// 节点深度，跟随基础商品
        /// </summary>
        public int PathNodeDeep { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public PtProductSkuAttributeModel Attribute { get; set; }

        /// <summary>
        /// 是否是默认填充的规格 true 表示默认填充的，不能编辑（自定义转平台资料规格时用到）
        /// </summary>
        public bool IsDefaultPadding { get; set; }

        /// <summary>
        /// 是否来源复制
        /// </summary>
        public bool IsCopy { get; set; } = false;
    }
}