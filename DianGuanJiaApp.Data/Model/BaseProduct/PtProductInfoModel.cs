using DianGuanJiaApp.Data.FxModel.Listing;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.Model.BaseProduct
{
    /// <summary>
    /// 同步到货盘的消息模型
    /// </summary>
    public class PtProductInfoMessage
    {
        /// <summary>
        /// 所属用户
        /// </summary>
        public int FxUserId { get; set; }

        /// <summary>
        /// 平台资料唯一Code
        /// </summary>
        public List<string> UniqueCodes { get; set; }

        /// <summary>
        /// 所属平台，如：TouTiao、KuaiShou
        /// </summary>
        public string PlatformType { get; set; }
    }
    
    /// <summary>
    /// 平台资料模型
    /// </summary>
    public class PtProductInfoModel
    {
        /// <summary>
        /// 唯一编码
        /// </summary>
        public string UniqueCode { get; set; }
        /// <summary>
        /// 标题
        /// </summary>
        public string Subject { get; set; }
        /// <summary>
        /// 平台类型
        /// </summary>
        public string PlatfromType { get; set; }
        /// <summary>
        /// 用户自己的基础商品ID
        /// </summary>
        public string BaseProductUid { get; set; }
        /// <summary>
        /// 创建来源，可选值如下：
        /// SelfBaseProduct=自己的基础商品
        /// SelfSupplierProduct = 自己的货盘
        /// SupplierProduct=厂家货盘
        /// SupplierPtProduct = 厂家平台资料
        /// Copy=厂家货盘复制
        /// </summary>
        public string CreateFrom { get; set; }
        /// <summary>
        /// 来源用户：上级厂家FxUserId或自己
        /// </summary>
        public int FromFxUserId { get; set; }
        /// <summary>
        /// 数据源关联的BaseProductUid(long类型在前端有丢失精度的风险)
        /// </summary>
        public string FromBaseProductUid { get; set; }
        /// <summary>
        /// 数据源关联的货盘Uid(long类型在前端有丢失精度的风险)
        /// </summary>
        public string FromSupplierProductUid { get; set; }
        /// <summary>
        /// 商家编码
        /// </summary>
        public string SpuCode { get; set; }
        /// <summary>
        /// 平台类目ID
        /// </summary>
        public string CategoryId { get; set; }
        /// <summary>
        /// 主图
        /// </summary>
        public List<PtProductInfoImageModel> ProductImages { get; set; }
        /// <summary>
        /// 详情图
        /// </summary>
        public List<string> DescriptionStr { get; set; }
        /// <summary>
        /// 类目信息
        /// </summary>
        public List<PtCategoryInfo> CategoryInfoList { get; set; }
        /// <summary>
        /// 平台属性类型（Sku值的说明）
        /// </summary>
        public List<PtAttributeTypeModel> AttributeTypes { get; set; }
        /// <summary>
        /// Sku
        /// </summary>
        public List<PtProductInfoSkuModel> ProductInfoSkus { get; set; }
        /// <summary>
        /// 平台属性信息
        /// </summary>
        public string CategoryAttribute { get; set; }
        /// <summary>
        /// 是否公开
        /// </summary>
        public bool IsPublic { get; set; }
        /// <summary>
        /// 根路径节点的用户ID
        /// </summary>
        public int? RootNodeFxUserId { get; set; }
        /// <summary>
        /// 分享路径流Code，跟随基础商品
        /// </summary>
        public string SharePathCode { get; set; }
        /// <summary>
        /// 节点深度，跟随基础商品
        /// </summary>
        public int PathNodeDeep { get; set; }

        /// <summary>
        /// 所属用户
        /// </summary>
        public int FxUserId { get; set; }

        /// <summary>
        /// 本次铺货的店铺列表
        /// </summary>
        public List<int> ListingShopIds { get; set; }

        /// <summary>
        /// 铺货设置-前端赋值
        /// </summary>
        public ListingSetting UserListingSetting { get; set; }

        /// <summary>
        /// 铺货任务-铺货设置-后端处理
        /// </summary>
        public ShopListingConfigSaveRequest ListingConfig { get; set; }

        /// <summary>
        /// 查询来源:0: 根据基础商品Uid、1：根据平台资料code、2：根据自己货盘、3：根据厂商货盘 4：铺货任务
        /// </summary>
        public int FromType { get; set; }

        /// <summary>
        /// 查询来源编码：重新铺货任务code
        /// </summary>
        public string FromCode { get; set; }

        /// <summary>
        /// 状态：false 无需同步、true 待同步
        /// </summary>
        public bool IsWaitSyncBaseProduct { get; set; }

        /// <summary>
        /// 规格销售属性（根据类目来）
        /// </summary>
        public string SellPropertyJson { get; set; }

        /// <summary>
        /// 导航样式查询
        /// </summary>
        public List<PlatformCategoryNavRes> NavCateValues { get; set; } = new List<PlatformCategoryNavRes>();

        /// <summary>
        /// 平台规格模式=0（默认）、自定义规格模式=1
        /// </summary>
        public int SkuModeType { get; set; }

        /// <summary>
        /// 是否需完善
        /// </summary>
        public bool NeedUpdate { get; set; } = false;


        /// <summary>
        /// 智能推荐类目是否展示
        /// </summary>
        public bool IsInit { get; set; } = false;

        /// <summary>
        /// 智能推荐类目
        /// </summary>
        //public List<PtCategoryInfo> AutoCategoryInfos { get; set; }
        //public List<CategoryResult> AutoCategoryInfos { get; set; }

        /// <summary>
        /// 类目预测（2025-06新增）
        /// </summary>
        public ProductRecommendCategory RecommendCategory { get; set; }

        /// <summary>
        /// 是否同步到平台资料（铺自己商品）
        /// </summary>
        public bool? IsSyncPtProductInfo { get; set; }

        /// <summary>
        /// 是显示否同步到平台资料按钮
        /// </summary>
        public bool? IsSyncPtProductInfoButton { get; set; }

        /// <summary>
        /// 无类目发布权限店铺数量
        /// </summary>
        public int ShopCount { get; set; }

        /// <summary>
        /// 无类目发布权限店铺数量
        /// </summary>
        public string ShopNames { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int ShopId { get; set; }
    }

    /// <summary>
    /// 平台商品主图更新模型
    /// </summary>
    public class PtProductInfoImageModel
    {
        /// <summary>
        /// 商品图片ID
        /// </summary>
        public long ImageObjectId { get; set; }

        /// <summary>
        /// 图片地址
        /// </summary>
        public string ImageUrl { get; set; }

        /// <summary>
        /// 是否主图
        /// </summary>
        public bool IsMain { get; set; }
    }

    public class ProductRecommendCategory
    {
        public ProductRecommendCategory() {}

        public ProductRecommendCategory(string recommendIds)
        {
            RecommendIds = recommendIds;
            Results = new List<CategoryResult>();
        }

        /// <summary>
        /// 类目预测Id
        /// </summary>
        public string RecommendIds { get; set; }

        /// <summary>
        /// 类目预测结果
        /// </summary>
        public List<CategoryResult> Results { get; set; }
    }


    public class CategoryResult
    {
        /// <summary>
        /// 预测类目项
        /// </summary>
        public List<PtCategoryInfo> List { get; set; } = new List<PtCategoryInfo>();

        /// <summary>
        /// 类目资质状态，0有资质；1资质过期；2无资质
        /// </summary>
        public int QualificationStatus { get; set; }
    }

    /// <summary>
    /// 类目信息
    /// </summary>
    public class PtCategoryInfo
    {
        /// <summary>
        /// 来源类目接口CateId
        /// </summary>
        public string CateId { get; set; }

        /// <summary>
        /// 来源类目接口ParentId
        /// </summary>
        public string ParentId { get; set; }

        /// <summary>
        /// 来源类目接口Level
        /// </summary>
        public int Level { get; set; }

        /// <summary>
        /// 类目名称
        /// </summary>
        public string Name { get; set; }
    }
    /// <summary>
    /// 平台属性类型
    /// </summary>
    public class PtAttributeTypeModel
    {
        /// <summary>
        /// 属性名称
        /// </summary>
        public string AttributeName { get; set; }

        /// <summary>
        /// 是否支持备注
        /// </summary>
        public bool SupportRemark { get; set; }
        /// <summary>
        /// 是否支持自定义
        /// </summary>
        public bool SupportDiy { get; set; }
        /// <summary>
        /// 是否必填
        /// </summary>
        public bool IsRequired { get; set; }

        /// <summary>
        /// 销售属性id
        /// </summary>
        public string SellPropertyId { get; set; }

        /// <summary>
        /// 属性值
        /// </summary>
        public List<PtAttributeValueModel> AttributeValues { get; set; }

    }
    /// <summary>
    /// 平台属性类型值
    /// </summary>
    public class PtAttributeValueModel
    {
        /// <summary>
        /// 图片的对应属性序号，可选值：0，1、2、3，0表示没有图片，1对应AttributeValue1，以此类推
        /// </summary>
        public int? ImgAttributeValueNo { get; set; }
        /// <summary>
        /// 图片的对象ID，来源OssObject.Id
        /// </summary>
        public long? ImageObjectId { get; set; }
        public string Value { get; set; }
        public string ValueUrl { get; set; }

        public string ValueUrlKey { get; set; }

        /// <summary>
        /// 是否必填提示
        /// </summary>
        public bool IsRequiredTip { get; set; }

        /// <summary>
        /// 销售属性值列表
        /// </summary>
        public List<SellPropertyValueModel> SellPropertyValues { get; set; }

        /// <summary>
        /// 所有值合在一起，供铺货任务使用，从SellPropertyValues解析过来
        /// </summary>
        public string ValueString 
        { 
            get
            {
                if (SellPropertyValues == null || SellPropertyValues.Any(x => x != null) == false)
                    return Value;

                SellPropertyValues = SellPropertyValues.Where(x => x != null).ToList();

                var strList = new List<string>();
                SellPropertyValues.ForEach(x => {
                    var strVm = string.Empty;
                    if (x.value_modules != null)
                        strVm = string.Join(",", x.value_modules.Select(a => a.values.ToString2() + a.unit.ToString2()).ToList());

                    strList.Add($"{x.values};{x.remark};{strVm}");
                });

                return string.Join("||", strList);
            }  
        }

        public string UniCode { get; set; }

        /// <summary>
        /// 对应的基础资料sku规格值
        /// </summary>
        public string OldValue { get; set; }
    }

    /// <summary>
    /// 销售属性值
    /// </summary>
    public class SellPropertyValueModel
    {
        /// <summary>
        /// 销售属性id
        /// </summary>
        public string sell_property_id { get; set; }
        /// <summary>
        /// 销售属性名称
        /// </summary>
        public string sell_property_name { get; set; }

        /// <summary>
        /// 值对应的id
        /// </summary>
        public string sell_property_value_id { get; set; }
        /// <summary>
        /// 值的名称
        /// </summary>
        public string sell_property_value_name { get; set; }

        /// <summary>
        /// 完整属性值
        /// </summary>
        public string property_values { get; set; }
        /// <summary>
        /// 完整的值
        /// </summary>
        public string values { get; set; }

        /// <summary>
        /// 备注的值
        /// </summary>
        public string remark { get; set; }
        /// <summary>
        /// 扩展1
        /// </summary>
        public string ext1 { get; set; }
        /// <summary>
        /// 扩展2
        /// </summary>
        public string ext2 { get; set; }
        /// <summary>
        /// 度量衡值列表
        /// </summary>
        public List<value_module> value_modules { get; set; }

        public bool support_diy { get; set; }
        public bool support_remark { get; set; }

        public string value_display_style { get; set; }
    }

    /// <summary>
    /// 度量衡值Model
    /// </summary>
    public class value_module
    {
        /// <summary>
        /// 模板id
        /// </summary>
        public string module_id { get; set; }
        /// <summary>
        /// 完整的值
        /// </summary>
        public string values { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        public string unit { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class PtProductSkuAttributeModel
    {
        /// <summary>
        /// 图片的对应属性序号，可选值：0，1、2、3，0表示没有图片，1对应AttributeValue1，以此类推
        /// </summary>
        public int ImgAttributeValueNo { get; set; }

        /// <summary>
        /// 属性名称1
        /// </summary>
        public string AttributeName1 { get; set; }

        /// <summary>
        /// 属性名称2，属性1不为空值时，属性2才可能有值
        /// </summary>
        public string AttributeName2 { get; set; }

        /// <summary>
        /// 属性名称3，属性2不为空值时，属性3才可能有值
        /// </summary>
        public string AttributeName3 { get; set; }

        /// <summary>
        /// 属性值1
        /// </summary>
        public string AttributeValue1 { get; set; }

        /// <summary>
        /// 属性值2，属性1不为空值时，属性2才可能有值
        /// </summary>
        public string AttributeValue2 { get; set; }

        /// <summary>
        /// 属性值3，属性2不为空值时，属性3才可能有值
        /// </summary>
        public string AttributeValue3 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ValueUrl { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string ValueUrlKey { get; set; }

        /// <summary>
        /// 图片的对象ID，来源OssObject.Id
        /// </summary>
        public long? ImageObjectId { get; set; }

        public string AttributeCode1 { get; set; }

        public string AttributeCode2 { get; set; }

        public string AttributeCode3 { get; set; }

        /// <summary>
        /// 转换方法，将JSON转为BaseProductSkuAttributeModel类型
        /// </summary>
        /// <param name="json"></param>
        /// <returns></returns>
        public static PtProductSkuAttributeModel FromJson(string json)
        {
            var attributeModel = new PtProductSkuAttributeModel();

            try
            {
                var attributes = JsonConvert.DeserializeObject<List<Dictionary<string, string>>>(json);

                if (attributes != null && attributes.Count > 0)
                {
                    var attribute1 = attributes.FirstOrDefault(a => a.ContainsKey("k") && a.ContainsKey("v"));
                    if (attribute1 != null)
                    {
                        attributeModel.AttributeName1 = attribute1["k"];
                        attributeModel.AttributeValue1 = attribute1["v"];

                        string v;
                        if (attribute1.TryGetValue("c", out v))
                            attributeModel.AttributeCode1 = v;
                    }

                    var attribute2 = attributes.Skip(1).FirstOrDefault(a => a.ContainsKey("k") && a.ContainsKey("v"));
                    if (attribute2 != null)
                    {
                        attributeModel.AttributeName2 = attribute2["k"];
                        attributeModel.AttributeValue2 = attribute2["v"];

                        string v;
                        if (attribute2.TryGetValue("c", out v))
                            attributeModel.AttributeCode2 = v;
                    }

                    var attribute3 = attributes.Skip(2).FirstOrDefault(a => a.ContainsKey("k") && a.ContainsKey("v"));
                    if (attribute3 != null)
                    {
                        attributeModel.AttributeName3 = attribute3["k"];
                        attributeModel.AttributeValue3 = attribute3["v"];

                        string v;
                        if (attribute3.TryGetValue("c", out v))
                            attributeModel.AttributeCode3 = v;
                    }
                }
            }
            catch (JsonException ex)
            {
                Log.WriteError($"平台商品Sku属性转换出错{ex.Message}");
            }
            return attributeModel;
        }

        /// <summary>
        /// 将类型转为Json字符串
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public static string FromClass(PtProductSkuAttributeModel model)
        {
            var attributes = new List<Dictionary<string, string>>();
            if (model == null) return attributes.ToJson();

            var attribute = new Dictionary<string, string>
            {
                { "k", model.AttributeName1 },
                { "v", model.AttributeValue1 },
                { "c", model.AttributeCode1 },
            };
            attributes.Add(attribute);

            if (!string.IsNullOrEmpty(model.AttributeName2))
            {
                attribute = new Dictionary<string, string>
                {
                    { "k", model.AttributeName2 },
                    { "v", model.AttributeValue2 },
                    { "c", model.AttributeCode2 }
                };
                attributes.Add(attribute);
            }

            if (!string.IsNullOrEmpty(model.AttributeName3))
            {
                attribute = new Dictionary<string, string>
                {
                    { "k", model.AttributeName3 },
                    { "v", model.AttributeValue3 },
                    { "c", model.AttributeCode3 }
                };
                attributes.Add(attribute);
            }

            return attributes.ToJson();
        }
    }
}
