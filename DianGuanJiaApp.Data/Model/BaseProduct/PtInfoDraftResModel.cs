using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.Model.BaseProduct
{
    /// <summary>
    /// 铺货草稿列表
    /// </summary>
    public class PtInfoDraftResModel
    {
        /// <summary>
        /// 已完善资料
        /// </summary>
        public List<PtInfoDraftItem> NeedUpdateY { get; set; }

        /// <summary>
        /// 未完善资料
        /// </summary>
        public List<PtInfoDraftItem> NeedUpdateN { get; set; }

        /// <summary>
        /// 已完善数量
        /// </summary>
        public int NeedUpdateCountY { get; set; }

        /// <summary>
        /// 未完善数量
        /// </summary>
        public int NeedUpdateCountN { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class PtInfoDraftItem
    {
        /// <summary>
        /// 唯一Code：Guid().ToShortMd5()
        /// </summary>
        public string UniqueCode { get; set; }

        /// <summary>
        /// 商品标题
        /// </summary>
        public string Subject { get; set; }

        /// <summary>
        /// 商品标题
        /// </summary>
        public string SubjectLength { get; set; }

        /// <summary>
        /// 商品主图
        /// </summary>
        public string MainImageUrl { get; set; }

        /// <summary>
        /// 类目名称
        /// </summary>
        public string CategoryName { get; set; }

        /// <summary>
        /// 类目末级id
        /// </summary>
        public string CategoryId{ get; set; }

        /// <summary>
        /// 分销价，单位元
        /// </summary>
        public string DistributePrice { get; set; }

        /// <summary>
        /// 销售价，单位元
        /// </summary>
        public string SalePrice { get; set; }

        /// <summary>
        /// 销售价是否为0
        /// </summary>
        public bool IsZeroForSalePrice { get; set; }=false;

        /// <summary>
        /// 采购价格，单位元
        /// </summary>
        public string SettlePrice { get; set; }

        /// <summary>
        /// 库存数量
        /// </summary>
        public int StockCount { get; set; }

        /// <summary>
        /// 需完善 true 未完善、false 已完善 
        /// </summary>
        public bool NeedUpdate { get; set; }

        /// <summary>
        /// 无类目发布权限店铺数量
        /// </summary>
        public int ShopCount { get; set; }

        /// <summary>
        /// 无类目发布权限店铺名称拼接
        /// </summary>
        public string ShopNames { get; set; }
    }
}
