using System;
using System.Collections.Generic;
using System.Linq;
using Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Dapper;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.FxModel;

namespace DianGuanJiaApp.Data.Repository
{
    public class FxUserShopRepository : BaseRepository<FxUserShop>
    {
        public FxUserShopRepository() : base(CustomerConfig.ConfigureDbConnectionString)
        {
        }

        public List<FxUserShop> GetShopsByFxUserIds(List<int> fxUserIds, List<int> shopIds = null, List<string> fields = null, bool rmVirtualShop = false)
        {
            var result = new List<FxUserShop>();
            if (fxUserIds == null || !fxUserIds.Any())
                return result;

            var shopIdJoinSql = string.Empty;
            var fxUserIdJoinSql = string.Empty;
            var param = new DynamicParameters();
            if (fxUserIds != null && fxUserIds.Count > 5)
            {
                fxUserIdJoinSql = $" INNER JOIN FunStringToIntTable(@FxUserIds,',') t ON f.FxUserId = t.Item";
                param.Add("FxUserIds", string.Join(",", fxUserIds));
            }

            if (shopIds != null && shopIds.Count > 5)
            {
                shopIdJoinSql = $" INNER JOIN FunStringToIntTable(@ShopIds,',') t2 ON f.FxUserId = t2.Item";
                param.Add("ShopIds", string.Join(",", shopIds));
            }

            var fieldStr = fields == null || !fields.Any() ? "*" : fields.ToStringWithSplit(",");
            var db = this.DbConnection;
            if (shopIdJoinSql.IsNotNullOrEmpty() || fxUserIdJoinSql.IsNotNullOrEmpty())
            {
                var whereSql = rmVirtualShop ? " AND PlatformType NOT IN('System','Virtual')" : " AND PlatformType NOT IN('System')";
                var sql = $"SELECT {fieldStr} FROM dbo.P_FxUserShop f WITH(NOLOCK) {fxUserIdJoinSql} {shopIdJoinSql} WHERE 1=1 {whereSql}";
                result = db.Query<FxUserShop>(sql, param).ToList();
            }
            else
            {
                var whereSql = $" WHERE FxUserId IN({fxUserIds.ToStringWithSplit(",")})";
                whereSql += rmVirtualShop ? " AND PlatformType NOT IN('System','Virtual')" : " AND PlatformType NOT IN('System')";
                if (shopIds != null && shopIds.Any())
                    whereSql += $" AND ShopId IN({shopIds.ToStringWithSplit(",")})";

                var sql = $"SELECT {fieldStr} FROM dbo.P_FxUserShop WITH(NOLOCK) {whereSql}";
                result = db.Query<FxUserShop>(sql).ToList();
            }
            return result;
        }

        public List<FxUserShop> GetShopsByFxUserId(int fxUserId, bool rmVirtualShop = false)
        {
            var fields = new List<string> { "Id", "ShopId", "NickName", "FxUserId", "PlatformType", "AuthTime" };
            var result = GetShopsByFxUserIds(new List<int> { fxUserId }, fields: fields, rmVirtualShop: rmVirtualShop);
            return result;

        }



        public Dictionary<int, string> GetDictionaryShopsByFxUserId(int fxUserId, bool rmVirtualShop = false)
        {
            var result = GetShopsByFxUserId(fxUserId, rmVirtualShop);
            return result?.ToDictionary(x => x.ShopId, x => x.NickName);
        }

        public List<FxUserShop> GetUserIdByShopId(List<int> shopIds, string strField = "*")
        {
            if (shopIds == null || shopIds.Any() == false)
                return new List<FxUserShop>();
            var db = this.DbConnection;
            //var sql = $"SELECT {strField} FROM dbo.P_FxUserShop WITH(NOLOCK) WHERE ShopId IN({string.Join(",", shopIds)}) ";
            var sql = $"SELECT {strField} FROM dbo.P_FxUserShop WITH(NOLOCK) WHERE ShopId IN @ShopIds";
            if (shopIds.Count > 1000)
            {
                List<FxUserShop> result = new List<FxUserShop>();
                for (int i = 0; i < Math.Ceiling(shopIds.Count / 1000.00); i++)
                {
                    var ids = shopIds.Skip(i * 1000).Take(1000);
                    var tempresult = db.Query<FxUserShop>(sql, new { ShopIds = ids }).ToList();
                    if (tempresult != null && tempresult.Count > 0)
                    {
                        result.AddRange(tempresult);
                    }
                }
                return result;
            }
            else
            {
                return db.Query<FxUserShop>(sql, new { ShopIds = shopIds }).ToList();
            }
        }

        public List<FxUserShop> GetFxUserIdMapping(List<int> fxUserIds)
        {
            if (fxUserIds == null || fxUserIds.Any() == false)
                return new List<FxUserShop>();
            var db = this.DbConnection;
            var sql = $"SELECT FxUserId,ShopId FROM dbo.P_FxUserShop WITH(NOLOCK) WHERE FxUserId IN({string.Join(",", fxUserIds)}) AND PlatformType='System'";
            if (fxUserIds.Count > 1000)
            {
                List<FxUserShop> result = new List<FxUserShop>();
                for (int i = 0; i < Math.Ceiling(fxUserIds.Count / 1000.00); i++)
                {
                    var ids = fxUserIds.Skip(i * 1000).Take(1000);
                    var tempresult = db.Query<FxUserShop>(sql, new { fxUserIds = ids }).ToList();
                    if (tempresult != null && tempresult.Count > 0)
                    {
                        result.AddRange(tempresult);
                    }
                }
                return result;
            }
            else
            {
                return db.Query<FxUserShop>(sql, new { fxUserIds }).ToList();
            }
        }

        public Tuple<int, List<FxUserShop>> GetListOfFinancialSettlement(FxUserShopQueryModel _reqModel)
        {
            var fxUserId = _reqModel.FxUserId;
            var strWhereSql = " AND PlatformType !='System'";
            var shopIds = new List<int>();
            if (_reqModel.ShopId > 0)
                strWhereSql += " AND ShopId = @shopId";
            if (_reqModel.ShopIds.IsNotNullOrEmpty() && _reqModel.ShopId <= 0)
            {
                strWhereSql += " AND ShopId IN @shopIds";
                shopIds = _reqModel.ShopIds.Split(',').Select(int.Parse).ToList();
            }
            if (_reqModel.Status > 0)
                strWhereSql += " AND Status = @status";
            if (!string.IsNullOrEmpty(_reqModel.PlatformType))
                strWhereSql += " AND PlatformType = @platformType";
            //用户名搜索
            if (!string.IsNullOrEmpty(_reqModel.NickName))
                strWhereSql += $" AND NickName like @nickName";
            var strOrderBySql = string.Empty;
            //有传入授权到期时间则按照传入时间,否则创建时间
            if (_reqModel.AuthTimeOrderBy == 1)
                strOrderBySql += " ORDER BY CASE WHEN PlatformType = 'Virtual' THEN DATEADD(yy,100,GETDATE()) ELSE AuthTime end asc ";
            else if (_reqModel.AuthTimeOrderBy == 2)
                strOrderBySql += " ORDER BY CASE WHEN PlatformType = 'Virtual' THEN DATEADD(yy,-100,GETDATE()) ELSE AuthTime end desc ";
            else
                strOrderBySql += " ORDER BY CreateTime DESC";

            var strPageSql = string.Empty;
            if (_reqModel.PageIndex > 0 && _reqModel.PageSize > 0)
                strPageSql += $" OFFSET {(_reqModel.PageIndex - 1) * _reqModel.PageSize} ROWS FETCH NEXT {_reqModel.PageSize} ROWS ONLY";

            var querySql = $@"
--查询总数
            SELECT COUNT(*) FROM dbo.P_FxUserShop WITH(NOLOCK) WHERE FxUserId = @userId
{strWhereSql}
;
            --查询绑定店铺
            SELECT * FROM dbo.P_FxUserShop WITH(NOLOCK) WHERE FxUserId = @userId
{strWhereSql}
{strOrderBySql}
{strPageSql}
";
            var multiple = this.DbConnection.QueryMultiple(querySql, new
            {
                userId = fxUserId,
                shopId = _reqModel.ShopId,
                status = _reqModel.Status,
                platformType = _reqModel.PlatformType,
                nickName = "%" + _reqModel.NickName + "%",
                shopIds
            });
            var total = multiple.Read<int>().FirstOrDefault();
            var result = multiple.Read<FxUserShop>().ToList();
            Log.Debug(() => $"sql:{querySql},result:{result.ToJson()}", "loadShop.log");
            return Tuple.Create(total, result);
        }

        public Tuple<int, List<FxUserShop>> GetList(FxUserShopQueryModel _reqModel)
        {
            return GetFxUserShopSyncStatus(_reqModel);
            string str = string.Empty;
            var sql = @"SELECT * FROM dbo.P_FxUserShop WITH(NOLOCK) WHERE FxUserId = @userId AND PlatformType !='System'";
            var sql_count = "SELECT COUNT(*) FROM dbo.P_FxUserShop WITH(NOLOCK) WHERE FxUserId = @userId AND PlatformType !='System' ";
            var sql_stuatus = "SELECT * FROM dbo.P_SyncStatus WITH(NOLOCK) WHERE FxUserId = @userId AND Source='FenDanSystem'";
            var shopIds = new List<int>();
            if (_reqModel.ShopId > 0)
            {
                sql_count += " AND ShopId = @shopId";
                sql += " AND ShopId = @shopId";
                sql_stuatus += " AND ShopId = @shopId";
            }
            if (_reqModel.ShopIds.IsNotNullOrEmpty() && _reqModel.ShopId <= 0)
            {
                sql_count += " AND ShopId IN @shopIds";
                sql += " AND ShopId IN @shopIds";
                sql_stuatus += " AND ShopId IN @shopIds";
                shopIds = _reqModel.ShopIds.Split(',').Select(int.Parse).ToList();
            }
            if (_reqModel.Status > 0)
            {
                sql_count += " AND Status = @status";
                sql += " AND Status = @status";
            }
            if (!string.IsNullOrEmpty(_reqModel.PlatformType))
            {
                sql_count += " AND PlatformType = @platformType";
                sql += " AND PlatformType = @platformType";
            }
            if (!string.IsNullOrEmpty(_reqModel.NickName))//用户名搜索
            {
                sql_count += $" AND NickName like @nickName";
                sql += $" AND NickName like @nickName";
                str = _reqModel.NickName.Insert(0, "%");
                str = str.Insert(str.Length, "%");
            }
            //有传入授权到期时间则按照传入时间,否则创建时间
            if (_reqModel.AuthTimeOrderBy == 1)
            {
                sql += " ORDER BY CASE WHEN PlatformType = 'Virtual' THEN DATEADD(yy,100,GETDATE()) ELSE AuthTime end asc ";
            }
            else if (_reqModel.AuthTimeOrderBy == 2)
            {
                sql += " ORDER BY CASE WHEN PlatformType = 'Virtual' THEN DATEADD(yy,-100,GETDATE()) ELSE AuthTime end desc ";
            }
            else
            {
                sql += " ORDER BY CreateTime DESC";
            }
            if (_reqModel.PageIndex > 0 && _reqModel.PageSize > 0)
                sql += $" OFFSET {(_reqModel.PageIndex - 1) * _reqModel.PageSize} ROWS FETCH NEXT {_reqModel.PageSize} ROWS ONLY";

            sql = $@"{sql};{sql_stuatus};{sql_count}";


            var multiple = this.DbConnection.QueryMultiple(sql, new
            {
                userId = _reqModel.FxUserId,
                shopId = _reqModel.ShopId,
                status = _reqModel.Status,
                platformType = _reqModel.PlatformType,
                nickName = str,
                shopIds
            });
            var result = multiple.Read<FxUserShop>().ToList();
            var status = multiple.Read<SyncStatus>().ToList();
            var total = multiple.Read<int>().FirstOrDefault();

            // 拼多多店铺需要通过API重新查
            //增加条件：QueryPageType=1且非拼多多云环境或者非抖店云环境
            //Log.Debug($"CloudPlatformType={CustomerConfig.CloudPlatformType}，_reqModel.FxUserId={_reqModel.FxUserId}，result：{result.ToJson()}，status={status.ToJson()}");
            if (_reqModel.QueryPageType == 1)
            {
                var newSyncStatus = new List<SyncStatus>();
                var pddSyncStatuLst = new List<SyncStatus>();
                var toutiaoSyncStatuLst = new List<SyncStatus>();

                if (CustomerConfig.CloudPlatformType != CloudPlatformType.Pinduoduo.ToString())
                {
                    var pddCloundPts = new List<string> { PlatformType.Pinduoduo.ToString(), PlatformType.KuaiTuanTuan.ToString() };
                    var pddFxShops = result.Where(x => pddCloundPts.Contains(x.PlatformType)).ToList();
                    if (pddFxShops.Any())
                    {
                        var sids = pddFxShops.Select(x => x.ShopId).Distinct().ToList();
                        try
                        {
                            //Log.Debug($"查询拼多多店铺【{string.Join(",", sids)}】同步信息");
                            sql_stuatus = $"SELECT * FROM dbo.P_SyncStatus WITH(NOLOCK) WHERE FxUserId={_reqModel.FxUserId} AND ShopId IN({string.Join(",", sids)}) AND Source='FenDanSystem'";
                            var pddConfigDb = DbApiAccessUtility.GetPddConfigureDb();
                            pddSyncStatuLst = pddConfigDb.Query(sql_stuatus).ToList<SyncStatus>();
                        }
                        catch (Exception ex)
                        {
                            Log.WriteError($"获取拼多多平台店铺【{string.Join(",", sids)}】同步信息失败：{ex}");
                        }
                    }
                }
                if (CustomerConfig.CloudPlatformType != CloudPlatformType.TouTiao.ToString())
                {
                    var toutiaoFxShops = result.Where(x => x.PlatformType == PlatformType.TouTiao.ToString()).ToList();
                    if (toutiaoFxShops.Any())
                    {
                        // 判断账号是否在抖店云
                        var fxDbConfig = new FxDbConfigRepository().GetByFxUserId(_reqModel.FxUserId, PlatformType.TouTiao.ToString());
                        //Log.Debug($"当前账号是否是抖店云分区：{(fxDbConfig == null ? "null" : fxDbConfig.FromFxDbConfig.ToString2())}");
                        if (fxDbConfig != null && fxDbConfig.FromFxDbConfig == 1)
                        {
                            var sids = toutiaoFxShops.Select(x => x.ShopId).Distinct().ToList();
                            try
                            {
                                //Log.Debug($"查询抖店店铺【{string.Join(",", sids)}】同步信息");
                                sql_stuatus = $"SELECT * FROM dbo.P_SyncStatus WITH(NOLOCK) WHERE FxUserId={_reqModel.FxUserId} AND ShopId IN({string.Join(",", sids)}) AND Source='FenDanSystem'";
                                var toutiaoConfigDb = DbApiAccessUtility.GetTouTiaoConfigureDb();
                                toutiaoSyncStatuLst = toutiaoConfigDb.Query(sql_stuatus).ToList<SyncStatus>();
                            }
                            catch (Exception ex)
                            {
                                Log.WriteError($"获取抖店云平台店铺【{string.Join(",", sids)}】同步信息失败：{ex}");
                            }
                        }
                    }
                }

                foreach (var s in status)
                {
                    var newStatus = pddSyncStatuLst.FirstOrDefault(x => x.FxUserId == s.FxUserId && x.ShopId == s.ShopId && x.SyncType == s.SyncType && x.Source == s.Source);
                    if (newStatus == null)
                        newStatus = toutiaoSyncStatuLst.FirstOrDefault(x => x.FxUserId == s.FxUserId && x.ShopId == s.ShopId && x.SyncType == s.SyncType && x.Source == s.Source);
                    if (newStatus != null)
                        newSyncStatus.Add(newStatus);
                    else
                        newSyncStatus.Add(s);
                }
                if (newSyncStatus.Any())
                    status = newSyncStatus;
            }

            var newResult = new List<FxUserShop>();
            result.ForEach(s =>
            {
                var cuurStatus = status.FirstOrDefault(t => t.FxUserId == s.FxUserId && t.ShopId == s.ShopId && t.SyncType == ShopSyncType.Order);
                if (cuurStatus != null)
                {
                    s.LastSyncMessage = cuurStatus.LastSyncMessage;
                    //if (cuurStatus.LastSyncMessage?.Contains($"【{s.NickName}】授权过期") == true
                    //|| cuurStatus.LastSyncMessage?.Contains($"【{s.NickName}】授权已过期") == true
                    //|| cuurStatus.LastSyncMessage?.Contains("授权过期") == true
                    //|| cuurStatus.LastSyncMessage?.Contains("授权已过期") == true)
                    //{
                    //    if (s.Status != Enum.FxUserShopStatus.AuthExpired)
                    //    {
                    //        s.LastSyncMessage = cuurStatus.LastSyncMessage;
                    //        s.Status = Enum.FxUserShopStatus.AuthExpired;
                    //        UpdateShopStatusById(s.Id, s.Status);
                    //        //缓存性能优化:直接清除缓存
                    //        FxCaching.RefeshCache(FxCachingType.FxShopSelf, s.FxUserId);
                    //        //FxCaching.RefeshShopCaching(s.FxUserId);
                    //    }
                    //}
                    //else
                    //{
                    //    if (s.Status == FxUserShopStatus.AuthExpired)
                    //    {
                    //        s.Status = FxUserShopStatus.Binded;
                    //        UpdateShopStatusById(s.Id, s.Status);
                    //        //缓存性能优化:直接清除缓存
                    //        FxCaching.RefeshCache(FxCachingType.FxShopSelf, s.FxUserId);
                    //        //FxCaching.RefeshShopCaching(s.FxUserId);
                    //    }
                    //}
                }
                //经过同步表确认状态后,和搜索状态不同的就不输出了-反之无搜索转态或者搜索转态和最新状态相同的则输出
                if (_reqModel.Status <= 0 || _reqModel.Status == s.Status)
                {
                    //设置解绑初始值，申请解绑
                    s.UnBindTaskState = -1;

                    newResult.Add(s);
                }
            });

            return Tuple.Create(total, newResult);
        }

        /// <summary>
        /// 查询当前系统帮的店铺及同步任务
        /// </summary>
        /// <param name="_reqModel"></param>
        /// <returns></returns>
        private Tuple<int, List<FxUserShop>> GetFxUserShopSyncStatus(FxUserShopQueryModel _reqModel)
        {
            var fxUserId = _reqModel.FxUserId;
            var strWhereSql = " AND PlatformType !='System'";
            var shopIds = new List<int>();
            var platformTypes = new List<string>();
            if (_reqModel.ShopId > 0)
                strWhereSql += " AND ShopId = @shopId";
            if (_reqModel.ShopIds.IsNotNullOrEmpty() && _reqModel.ShopId <= 0)
            {
                strWhereSql += " AND ShopId IN @shopIds";
                shopIds = _reqModel.ShopIds.Split(',').Select(int.Parse).ToList();
            }
            if (_reqModel.Status > 0)
                strWhereSql += " AND Status = @status";
            if (!string.IsNullOrEmpty(_reqModel.PlatformType))
                strWhereSql += " AND PlatformType = @platformType";

            if (!string.IsNullOrEmpty(_reqModel.PlatformTypes) && string.IsNullOrEmpty(_reqModel.PlatformType))
            {
                strWhereSql += $" AND PlatformType {(_reqModel.IsPlatformTypesNotIn ? "not":"")} in @platformTypes";

                platformTypes = _reqModel.PlatformTypes.Split(',').ToList();
            }

            //用户名搜索
            if (!string.IsNullOrEmpty(_reqModel.NickName))
                strWhereSql += $" AND NickName like @nickName";
            var strOrderBySql = string.Empty;
            //有传入授权到期时间则按照传入时间,否则创建时间
            if (_reqModel.AuthTimeOrderBy == 1)
                strOrderBySql += " ORDER BY CASE WHEN PlatformType = 'Virtual' THEN DATEADD(yy,100,GETDATE()) ELSE AuthTime end asc ";
            else if (_reqModel.AuthTimeOrderBy == 2)
                strOrderBySql += " ORDER BY CASE WHEN PlatformType = 'Virtual' THEN DATEADD(yy,-100,GETDATE()) ELSE AuthTime end desc ";
            else
                strOrderBySql += " ORDER BY CreateTime DESC";

            var strPageSql = string.Empty;
            if (_reqModel.PageIndex > 0 && _reqModel.PageSize > 0)
                strPageSql += $" OFFSET {(_reqModel.PageIndex - 1) * _reqModel.PageSize} ROWS FETCH NEXT {_reqModel.PageSize} ROWS ONLY";

            var querySql = $@"
--查询总数
            SELECT COUNT(*) FROM dbo.P_FxUserShop WITH(NOLOCK) WHERE FxUserId = @userId
{strWhereSql}
;
            --查询绑定店铺
            SELECT * FROM dbo.P_FxUserShop WITH(NOLOCK) WHERE FxUserId = @userId
{strWhereSql}
{strOrderBySql}
{strPageSql}
";
            Log.Debug(() => querySql, "ShopListQuery.log");
            var multiple = this.DbConnection.QueryMultiple(querySql, new
            {
                userId = fxUserId,
                shopId = _reqModel.ShopId,
                status = _reqModel.Status,
                platformType = _reqModel.PlatformType,
                nickName = "%" + _reqModel.NickName + "%",
                shopIds,
                platformTypes
            });
            var total = multiple.Read<int>().FirstOrDefault();
            var result = multiple.Read<FxUserShop>().ToList();

            var syncStatusList = new List<SyncStatus>();
            //查询任务
            // 拼多多店铺需要通过API重新查
            //增加条件：QueryPageType=1且非拼多多云环境或者非抖店云环境
            //Log.Debug($"CloudPlatformType={CustomerConfig.CloudPlatformType}，_reqModel.FxUserId={_reqModel.FxUserId}，result：{result.ToJson()}，status={status.ToJson()}");
            if (_reqModel.QueryPageType == 1)
            {
                // 判断账号是否在抖店云
                //var fxDbConfig = new FxDbConfigRepository().GetByFxUserId(fxUserId, PlatformType.TouTiao.ToString());
                //按店铺所属云平台分组
                var shopGroups = result.GroupBy(f =>
                {
                    if (CustomerConfig.FxPinduoduoCloudPlatformTypes.Contains(f.PlatformType))
                        return CloudPlatformType.Pinduoduo.ToString();
                    if (CustomerConfig.FxDouDianCloudPlatformTypes.Contains(f.PlatformType))
                    {
                        //if (fxDbConfig != null && fxDbConfig.FromFxDbConfig == 1)
                        //    return CloudPlatformType.TouTiao.ToString();
                        //return CloudPlatformType.Alibaba.ToString();
                        return CloudPlatformType.TouTiao.ToString();
                    }
                    if (CustomerConfig.FxJingDongCloudPlatformTypes.Contains(f.PlatformType))
                        return CloudPlatformType.Jingdong.ToString();
                    return CloudPlatformType.Alibaba.ToString();
                });

                var currentSiteCloundPt = CustomerConfig.CloudPlatformType; //当前站点云平台
                //根据云平台去查配置库
                foreach (var item in shopGroups)
                {
                    var queryStatusSql = $"SELECT FxUserId,ShopId,SyncType,StartSyncTime,LastSyncTime,LastSyncMessage FROM dbo.P_SyncStatus WITH(NOLOCK) WHERE FxUserId = {fxUserId} {(_reqModel.ShopId > 0 ? $"AND ShopId = {_reqModel.ShopId}" : "")} AND Source='FenDanSystem'";
                    if (item.Key == currentSiteCloundPt || CustomerConfig.IsLocalDbDebug)   //相同云和本地调试不需要跨云查询，直链查询即可
                    {
                        var db = DbConnection;
                        var syncStatus = db.Query<SyncStatus>(queryStatusSql);
                        syncStatusList.AddRange(syncStatus);
                        Log.Debug($"查询本地的数据：{syncStatus.Select(x => new { x.FxUserId, x.ShopId, x.SyncType, x.StartSyncTime, x.LastSyncTime, x.LastSyncMessage }).ToJson()}", "dbApiQuerySyncStatus.txt");
                    }
                    else if (item.Key == CloudPlatformType.Pinduoduo.ToString())
                    {
                        try
                        {
                            var dbApi = DbApiAccessUtility.GetPddConfigureDb();
                            var syncStatus = dbApi.Query<SyncStatus>(queryStatusSql);
                            syncStatusList.AddRange(syncStatus);
                            Log.Debug($"查询拼多多的数据：{syncStatus.Select(x => new { x.FxUserId, x.ShopId, x.SyncType, x.StartSyncTime, x.LastSyncTime, x.LastSyncMessage }).ToJson()}", "dbApiQuerySyncStatus.txt");
                        }
                        catch (Exception ex)
                        {
                            Log.Debug($"查询拼多多的数据异常：{ex.Message}", "dbApiQuerySyncStatus.txt");
                        }
                    }
                    else if (item.Key == CloudPlatformType.TouTiao.ToString())
                    {
                        try
                        {
                            var dbApi = DbApiAccessUtility.GetTouTiaoConfigureDb();
                            var syncStatus = dbApi.Query<SyncStatus>(queryStatusSql);
                            syncStatusList.AddRange(syncStatus);
                            Log.Debug($"查询抖店的数据：{syncStatus.Select(x => new { x.FxUserId, x.ShopId, x.SyncType, x.StartSyncTime, x.LastSyncTime, x.LastSyncMessage }).ToJson()}", "dbApiQuerySyncStatus.txt");
                        }
                        catch (Exception ex)
                        {
                            Log.Debug($"查询抖店的数据异常：{ex.Message}", "dbApiQuerySyncStatus.txt");
                        }

                    }
                    else
                    {
                        try
                        {
                            var dbApi = DbApiAccessUtility.GetConfigureDb();
                        var syncStatus = dbApi.Query<SyncStatus>(queryStatusSql);
                        syncStatusList.AddRange(syncStatus);
                        Log.Debug($"查询阿里的数据：{syncStatus.Select(x => new { x.FxUserId, x.ShopId, x.SyncType, x.StartSyncTime, x.LastSyncTime, x.LastSyncMessage }).ToJson()}", "dbApiQuerySyncStatus.txt");
                        }
                        catch (Exception ex)
                        {
                            Log.Debug($"查询阿里的数据异常：{ex.Message}", "dbApiQuerySyncStatus.txt");
                        }
                    }
                }
            }

            var newResult = new List<FxUserShop>();
            result.ForEach(s =>
            {
                s.OldStatus = s.Status;

                var syncStatus = syncStatusList?.OrderByDescending(o => o.StartSyncTime)?.FirstOrDefault(p => p.FxUserId == s.FxUserId && p.ShopId == s.ShopId && p.SyncType == ShopSyncType.Order);
                s.LastSyncMessage = syncStatus?.LastSyncMessage;
                if (s.IsAuthExpired)
                {
                    if (s.Status != Enum.FxUserShopStatus.AuthExpired)
                    {
                        s.Status = Enum.FxUserShopStatus.AuthExpired;
                    }
                }
                else
                {
                    if (s.Status == FxUserShopStatus.AuthExpired)
                    {
                        s.Status = FxUserShopStatus.Binded;
                    }
                }
                var syncProductStatus = syncStatusList?.OrderByDescending(o => o.StartSyncTime)?.FirstOrDefault(p => p.FxUserId == s.FxUserId && p.ShopId == s.ShopId && p.SyncType == ShopSyncType.Product);
                s.LastSyncProductMessage = syncProductStatus?.LastSyncMessage;
                if (s.IsProductAuthExpired)
                {
                    if (s.SyncProductStatus != Enum.FxUserShopStatus.AuthExpired)
                    {
                        s.SyncProductStatus = Enum.FxUserShopStatus.AuthExpired;
                    }
                }
                else
                {
                    s.SyncProductStatus = FxUserShopStatus.Binded;
                }

                //经过同步表确认状态后,和搜索状态不同的就不输出了-反之无搜索转态或者搜索转态和最新状态相同的则输出
                if (_reqModel.Status <= 0 || _reqModel.Status == s.Status)
                {
                    //设置解绑初始值，申请解绑
                    s.UnBindTaskState = -1;
                    newResult.Add(s);
                }
            });
            return Tuple.Create(total, newResult);
        }

        public List<FxUserShop> GetList(int fxUserId = 0, int shopId = 0,List<int> shopIds=null)
        {
            var sql = "SELECT t1.*,t2.NickName AS FxUserName,t2.Mobile AS FxUserMobile FROM dbo.P_FxUserShop t1 WITH(NOLOCK) INNER JOIN dbo.P_UserFx t2 WITH(NOLOCK) ON t1.FxUserId = t2.Id";
            if (fxUserId > 0)
            {
                sql += " AND t1.FxUserId = @fxUserId";
            }
            if (shopId > 0)
            {
                sql += " AND t1.ShopId = @shopId"; 
            }
            if (shopId == 0 && shopIds != null)
            {
                sql += " AND t1.ShopId in @shopIds"; 
            }

            var result = this.DbConnection.Query<FxUserShop>(sql, new { fxUserId, shopId , shopIds }).ToList();
            return result;
        }

        public FxUserShop GetFxUserShop(string shopId,string platformType)
        {
            var sql = @"SELECT TOP 1 t1.*,t2.NickName AS FxUserName,t2.Mobile AS FxUserMobile 
FROM dbo.P_FxUserShop t1 WITH(NOLOCK) 
INNER JOIN dbo.P_UserFx t2 WITH(NOLOCK) ON t1.FxUserId = t2.Id 
INNER JOIN dbo.P_Shop t3 WITH(NOLOCK)  ON t1.ShopId = t3.Id
WHERE t1.PlatformType = @platformType AND t3.ShopId = @shopId";
            return DbConnection.QueryFirstOrDefault<FxUserShop>(sql,new { platformType, shopId });
        }

        /// <summary>
        /// 根据平台订单店铺Id，获取关联此店铺的商家的虚拟店铺Id
        /// </summary>
        /// <param name="orderShopId"></param>
        /// <returns></returns>
        public FxUserShop GetFxUserSystemShop(int orderShopId)
        {
            var sql = $@"SELECT TOP 1 t3.ShopId,t3.FxUserId FROM dbo.P_FxUserShop t1 WITH(NOLOCK) 
                         INNER JOIN dbo.P_UserFx t2 WITH(NOLOCK) ON t1.FxUserId = t2.Id 
                         INNER JOIN dbo.P_FxUserShop t3 WITH(NOLOCK) on t3.FxUserId =t2.Id
                         WHERE t3.PlatformType='System' AND t1.ShopId={orderShopId}";
            return DbConnection.QueryFirstOrDefault<FxUserShop>(sql);

        }

        public FxUserShop GetFxUserShop(FxUserShop _newUser)
        {
            var sql = "SELECT * FROM dbo.P_FxUserShop WITH(NOLOCK) WHERE FxUserId = @userId AND ShopId = @shopId AND PlatformType = @pt";
            var data = DbConnection.QueryFirstOrDefault<FxUserShop>(sql, new
            {
                userId = _newUser.FxUserId,
                shopId = _newUser.ShopId,
                pt = _newUser.PlatformType
            });
            return data;
        }

        /// <summary>
        /// 获取系统店铺
        /// </summary>
        /// <param name="fxIds"></param>
        /// <returns></returns>
        public List<FxUserShop> GetFxUserShopIds(List<int> fxIds)
        {
            //var fxUserIds = string.Join(",", fxIds);
            //判空处理一下
            if (fxIds == null || !fxIds.Any())
            {
                return new List<FxUserShop>();
            }
            //是否开启缓存
            if (new CommonSettingRepository().IsEnabledSystemShopCache)
            {
                return GetSystemFxUserShopsByFxUserIdsWithCache(fxIds);
            }
            //未开启缓存
            const string sqlByIn = @"SELECT ShopId,FxUserId,NickName,PlatformType FROM dbo.P_FxUserShop fx WITH(NOLOCK) 
                            WHERE PlatformType ='System' AND fx.FxUserId IN @Codes";
            const string sqlByTableFun = @"SELECT ShopId,FxUserId,NickName,PlatformType FROM dbo.P_FxUserShop fx WITH(NOLOCK) 
                            INNER JOIN dbo.FunStringToIntTable(@Codes,',')	t ON fx.FxUserId=t.item
                            WHERE PlatformType ='System'";
            //var result = DbConnection.Query<FxUserShop>(sql, new
            //{
            //    Codes = fxUserIds
            //}).ToList();
            return SqlOptimizationHandler.QueryEntities(fxIds, sqlByIn, sqlByTableFun,
                (sql, param) => DbConnection.Query<FxUserShop>(sql, param).ToList());
        }

        public List<int> GetShopIdByFxUserId(int fxUserId)
        {
            var result = new List<int>();
            if (fxUserId <= 0)
                return result;
            var db = this.DbConnection;
            var sql = "SELECT ShopId FROM dbo.P_FxUserShop WITH(NOLOCK) WHERE FxUserId = @userId AND PlatformType !='System'";
            result = db.Query<int>(sql, new { userId = fxUserId }).ToList();
            return result;
        }

        public void UpdateShopStatus(int fxUserId, int shopId, int status)
        {
            var sql = "UPDATE dbo.P_FxUserShop SET [Status] = @status WHERE FxUserId = @userId AND ShopId = @shopId";
            var resutl = DbConnection.Execute(sql, new
            {
                status = status,
                userId = fxUserId,
                shopId = shopId
            });
            //缓存性能优化:直接清除缓存
            FxCaching.RefeshCache(FxCachingType.FxShopSelf, fxUserId);
        }
        public void UpdateShopStatusById(int id, Enum.FxUserShopStatus status)
        {
            var sql = "UPDATE dbo.P_FxUserShop SET [Status] = @status WHERE Id = @id";
            var resutl = DbConnection.Execute(sql, new
            {
                status = status,
                id = id
            });
        }

        public void UpdateNickName(Dictionary<int, string> shopIdNickNameDict)
        {
            var sql = "UPDATE P_FxUserShop SET nickname=@nickName WHERE ShopId=@id";
            foreach (var dict in shopIdNickNameDict)
            {
                var result = DbConnection.Execute(sql, new
                {
                    nickName = dict.Value,
                    id = dict.Key
                });
                //缓存性能优化:直接清除缓存
                FxCaching.RefeshShopCaching(dict.Key);
            }
        }

        public List<FxUserShop> GetExpireShopByFxUserId(int fxUserId, List<int> skipIds = null, int findShopId=0)
        {
            var sql = @"SELECT * FROM dbo.P_FxUserShop WITH(NOLOCK) WHERE FxUserId = @userId AND PlatformType !='System' AND PlatformType !='Virtual'";
            if (skipIds != null && skipIds.Any())
            {
                sql += " AND ShopId NOT IN@notIds ";
            }
            if (findShopId>0)
            {
                sql += " AND ShopId = @findShopId ";
            }

            var list = DbConnection.Query<FxUserShop>(sql, new
            {
                userId = fxUserId,
                notIds = skipIds,
                findShopId = findShopId
            }).ToList();

            // 判断账号是否在抖店云
            //var fxDbConfig = new FxDbConfigRepository().GetByFxUserId(fxUserId, PlatformType.TouTiao.ToString());
            //按店铺所属云平台分组
            var shopGroups = list.GroupBy(f =>
            {
                if (f.PlatformType == PlatformType.Pinduoduo.ToString() || f.PlatformType == PlatformType.KuaiTuanTuan.ToString())
                    return CloudPlatformType.Pinduoduo.ToString();
                else if (f.PlatformType == PlatformType.TouTiao.ToString())
                {
                    //if (fxDbConfig != null && fxDbConfig.FromFxDbConfig == 1)
                    //    return CloudPlatformType.TouTiao.ToString();
                    //return CloudPlatformType.Alibaba.ToString();
                    return CloudPlatformType.TouTiao.ToString();
                }
                else
                    return CloudPlatformType.Alibaba.ToString();
            });

            var syncStatusList = new List<SyncStatus>();
            var currentSiteCloundPt = CustomerConfig.CloudPlatformType; //当前站点云平台
            //根据云平台去查配置库
            foreach (var item in shopGroups)
            {
                var newsql = "SELECT FxUserId,ShopId,SyncType,StartSyncTime,LastSyncTime,LastSyncMessage FROM dbo.P_SyncStatus WITH(NOLOCK) WHERE FxUserId = @fxUserId AND ShopId IN@sids  AND Source='FenDanSystem'";
                if (item.Key == currentSiteCloundPt || CustomerConfig.IsLocalDbDebug)
                {
                    var db = DbConnection;
                    var syncStatus = db.Query<SyncStatus>(newsql, new { sids = item.ToList().Select(f => f.ShopId), fxUserId = fxUserId });
                    syncStatusList.AddRange(syncStatus);
                    Log.Debug($"查询本地的数据：{syncStatus.Select(x => new { x.FxUserId, x.ShopId, x.SyncType, x.StartSyncTime, x.LastSyncTime, x.LastSyncMessage }).ToJson()}", "dbApiQuerySyncStatus.txt");
                }
                //else if (currentSiteCloundPt == CloudPlatformType.Pinduoduo.ToString())
                //{
                //    var db = DbUtility.GetConfigureConnection();
                //    var syncStatus = db.Query<SyncStatus>(newsql, new { sids = item.ToList().Select(f => f.ShopId), fxUserId = fxUserId });
                //    syncStatusList.AddRange(syncStatus);
                //}
                //else if (currentSiteCloundPt == CloudPlatformType.TouTiao.ToString())
                //{
                //    var db = DbUtility.GetConfigureConnection();
                //    var syncStatus = db.Query<SyncStatus>(newsql, new { sids = item.ToList().Select(f => f.ShopId), fxUserId = fxUserId });
                //    syncStatusList.AddRange(syncStatus);
                //}
                else if (item.Key == CloudPlatformType.Pinduoduo.ToString())
                {
                    var dbApi = DbApiAccessUtility.GetPddConfigureDb();
                    var syncStatus = dbApi.Query<SyncStatus>(newsql, new { sids = item.ToList().Select(f => f.ShopId), fxUserId = fxUserId });
                    syncStatusList.AddRange(syncStatus);
                    Log.Debug($"查询拼多多的数据：{syncStatus.Select(x => new { x.FxUserId, x.ShopId, x.SyncType, x.StartSyncTime, x.LastSyncTime, x.LastSyncMessage }).ToJson()}", "dbApiQuerySyncStatus.txt");
                }
                else if (item.Key == CloudPlatformType.TouTiao.ToString())
                {
                    var dbApi = DbApiAccessUtility.GetTouTiaoConfigureDb();
                    var syncStatus = dbApi.Query<SyncStatus>(newsql, new { sids = item.ToList().Select(f => f.ShopId), fxUserId = fxUserId });
                    syncStatusList.AddRange(syncStatus);

                    Log.Debug($"查询抖店的数据：{syncStatus.Select(x => new { x.FxUserId, x.ShopId, x.SyncType, x.StartSyncTime, x.LastSyncTime, x.LastSyncMessage }).ToJson()}", "dbApiQuerySyncStatus.txt");
                }
                else
                {
                    var dbApi = DbApiAccessUtility.GetConfigureDb();
                    var syncStatus = dbApi.Query<SyncStatus>(newsql, new { sids = item.ToList().Select(f => f.ShopId), fxUserId = fxUserId });
                    syncStatusList.AddRange(syncStatus);
                    Log.Debug($"查询阿里的数据：{syncStatus.Select(x => new { x.FxUserId, x.ShopId, x.SyncType, x.StartSyncTime, x.LastSyncTime, x.LastSyncMessage }).ToJson()}", "dbApiQuerySyncStatus.txt");
                }
            }

            list.ForEach(f =>
            {
                var syncStatus = syncStatusList?.OrderByDescending(o => o.StartSyncTime)?.FirstOrDefault(p => p.FxUserId == f.FxUserId && p.ShopId == f.ShopId && p.SyncType == ShopSyncType.Order);
                f.LastSyncMessage = syncStatus?.LastSyncMessage;

                if (f.IsAuthExpired)
                {
                    if (f.Status != Enum.FxUserShopStatus.AuthExpired)
                    {
                        f.Status = Enum.FxUserShopStatus.AuthExpired;
                    }
                }
                else
                {
                    if (f.Status == FxUserShopStatus.AuthExpired)
                    {
                        f.Status = FxUserShopStatus.Binded;
                    }
                }
            });

            //异步修改数据库消息
            //try
            //{
            //    var _syncStatusRepository = new SyncStatusRepository();
            //    syncStatusList.ForEach(s =>
            //    {
            //        _syncStatusRepository.UpdateLastSyncMessage(s.FxUserId, s.ShopId, (int)s.SyncType, s.LastSyncMessage);
            //    });
            //}
            //catch (Exception e)
            //{
            //    Log.WriteError("同步更新状态(SyncStatus表)错误消息失败:" + e);
            //}
            return list;
        }

        public void UpdateAuthTime(int shopId, DateTime newAuthTime)
        {
            DbConnection.Execute("UPDATE dbo.P_FxUserShop SET AuthTime = @newTime WHERE ShopId = @id", new
            {
                id = shopId,
                newTime = newAuthTime
            });
            //缓存性能优化:直接清除缓存
            FxCaching.RefeshShopCaching(shopId);
        }
        public void UpdateListingOwnerServiceTime(int shopId, DateTime time)
        {
            DbConnection.Execute("UPDATE dbo.ListingOwnerShop SET ServiceEndTime = @endTime,UpdateTime=GETDATE() WHERE ShopId = @id", new { id = shopId, endTime = time });
        }

        public void UpdateAuthTimeOrStatus(List<FxUserShop> uplist)
        {
            var strSql = string.Empty;
            foreach (var item in uplist)
            {
                int status = (int)item.Status;
                var authTime = item.AuthTime;
                var setSql = string.Empty;
                if (status > 0)
                {
                    setSql = $"Status = {status}";
                }
                if (authTime.Year > 1)
                {
                    setSql = $"AuthTime = '{authTime}'";
                }
                if (status > 0 && authTime.Year > 1)
                {
                    setSql = $"Status = {status},AuthTime = '{authTime}'";
                }
                strSql += $"UPDATE dbo.P_FxUserShop SET {setSql} WHERE Id = {item.Id};";
                //缓存性能优化:直接清除缓存--再次使用时刷新
                FxCaching.RefeshCache(FxCachingType.FxShopSelf, item.FxUserId);
            }
            DbConnection.Execute(strSql);
        }

        public List<Shop> GetBindShops(int fxId)
        {
            var sql = "SELECT t2.* FROM dbo.P_FxUserShop t1 WITH(NOLOCK) INNER JOIN dbo.P_Shop t2 WITH(NOLOCK) ON t1.ShopId = t2.Id WHERE t1.FxUserId = @fxId";
            return DbConnection.Query<Shop>(sql, new { fxId }).ToList();
        }

        /// <summary>
        /// 已绑定的店铺（指定用户+指定店铺Id）
        /// </summary>
        /// <param name="fxId"></param>
        /// <param name="sids"></param>
        /// <returns></returns>
        public List<Shop> GetBindShops(int fxId, List<int> sids)
        {
            var sql = "SELECT t2.* FROM dbo.P_FxUserShop t1 WITH(NOLOCK) INNER JOIN dbo.P_Shop t2 WITH(NOLOCK) ON t1.ShopId = t2.Id WHERE t1.FxUserId = @fxId AND t2.Id IN @sids";
            return DbConnection.Query<Shop>(sql, new { fxId, sids }).ToList();
        }

        public List<FxUserShop> GetBindFxShops(List<int> sids)
        {
            return GetBindFxShopsNew(sids);
            var result = new List<FxUserShop>(sids.Count);
            if (sids == null || sids.Count == 0)
                return result;

            const string sql = "SELECT * FROM dbo.P_FxUserShop t1 WITH(NOLOCK) INNER JOIN dbo.P_Shop t2 WITH(NOLOCK) ON t1.ShopId = t2.Id WHERE t1.ShopId IN@sids";

            using (var db = DbConnection)
            {
                db.Open();
                const int batchSize = 1000;
                for (int i = 0; i < sids.Count; i+=batchSize)
                {
                    var batchIds = sids.Skip(i).Take(batchSize).ToList();
                    result.AddRange(db.Query<FxUserShop, Shop, FxUserShop>(sql, (fs, s) =>
                    {
                        fs.ShopInfo = s;
                        return fs;
                    },new{sids = batchIds}));
                }
            }

            return result;
        }
        
        /// <summary>
        /// 已绑定的店铺（指定用户+指定店铺Id）
        /// </summary>
        /// <param name="sids"></param>
        /// <returns></returns>
        public List<FxUserShop> GetBindFxShopsNew(List<int> sids)
        {
            if (sids == null || !sids.Any()) return new List<FxUserShop>();

            sids = sids.Distinct().ToList();

            // 1. 查询FxUserShop
            const string fxUserShopSql = "SELECT * FROM dbo.P_FxUserShop WITH(NOLOCK) INNER JOIN FunStringToIntTable(@shopIds, ',') t ON ShopId = t.item";

            var fxUserShops = DbConnection.Query<FxUserShop>(fxUserShopSql,
                    new { shopIds = string.Join(",", sids) })
                .ToList();

            if (!fxUserShops.Any()) return new List<FxUserShop>();

            // 2. 查询Shop
            const string shopSql = "SELECT * FROM dbo.P_Shop WITH(NOLOCK) INNER JOIN FunStringToIntTable(@shopIds, ',') t ON Id = t.item";
            var shops = DbConnection.Query<Shop>(shopSql, 
                    new { shopIds = string.Join(",", sids) })
                .GroupBy(x => x.Id).ToDictionary(x => x.Key, x => x.First());

            // 3. 合并数据
            foreach (var fxUserShop in fxUserShops)
            {
                Shop shop;
                if (shops.TryGetValue(fxUserShop.ShopId, out shop))
                {
                    fxUserShop.ShopInfo = shop;
                }
            }

            return fxUserShops;
        }

        public List<Shop> GetSystemShops(List<int> fxIds)
        {
            if (fxIds == null || fxIds.Any() == false)
                return new List<Shop>();

            var sql = $"SELECT t2.*,t1.FxUserId AS FxUserIds FROM dbo.P_FxUserShop t1 WITH(NOLOCK) INNER JOIN dbo.P_Shop t2 WITH(NOLOCK) ON t1.ShopId = t2.Id WHERE t1.FxUserId IN@fxIds AND t1.PlatformType='System'";
            return BatchQuery<Shop>(fxIds, "fxIds", sql, null);

            //return DbConnection.Query<Shop>(sql).ToList();
        }

        public UserFunctionAvailableCountCheckResultModel CheckFxUserIsHaveAvaliableCount(int fxUserId, ServiceVersionTypeEnum serviceVersionType)
        {
            var model = new UserFunctionAvailableCountCheckResultModel
            {
                FxUserId = fxUserId,
                AvailableCount = 1,
                ExisitCount = 0,
            };
            try
            {
                var db = DbConnection;
                var cmd = db.CreateCommand() as System.Data.SqlClient.SqlCommand;
                cmd.CommandType = System.Data.CommandType.StoredProcedure;
                cmd.CommandText = "dbo.sp_GetFxUserFunctionAvailableCount";
                cmd.Parameters.AddWithValue("@fxUserId", fxUserId);
                cmd.Parameters.AddWithValue("@versionType", serviceVersionType.ToInt());
                int result = 0;
                using (cmd)
                {
                    if (cmd.Connection.State != System.Data.ConnectionState.Open)
                        cmd.Connection.Open();
                    result = cmd.ExecuteScalar().ToInt();
                }
                var compareCount = 0;
                if (serviceVersionType == ServiceVersionTypeEnum.ShopUnit)
                {
                    //查询用户已有的店铺数量
                    compareCount = db.ExecuteScalar("SELECT COUNT(1) FROM dbo.P_FxUserShop u WITH(NOLOCK) INNER JOIN dbo.P_Shop s WITH(NOLOCK) ON u.ShopId = s.Id WHERE FxUserId=@fxUserId AND s.PlatformType NOT IN('Virtual','System')", new { fxUserId })?.ToInt() ?? 0;
                }
                else if (serviceVersionType == ServiceVersionTypeEnum.SellerUnit)
                {
                    //查询用户已有的商家数量
                    compareCount = db.ExecuteScalar("SELECT COUNT(1) FROM dbo.P_SupplierUser s WITH(NOLOCK) WHERE s.SupplierFxUserId=@fxUserId AND (s.SupplierType IS NULL OR s.SupplierType!='Virtual') AND (s.Status IN(1,2) OR s.AcceptTime IS NOT NULL)", new { fxUserId })?.ToInt() ?? 0;
                }
                else if (serviceVersionType == ServiceVersionTypeEnum.FactoryUnit)
                {
                    //查询用户已有的供应商数量
                    compareCount = db.ExecuteScalar("SELECT COUNT(1) FROM dbo.P_SupplierUser s WITH(NOLOCK) WHERE s.FxUserId=@fxUserId AND (s.SupplierType IS NULL OR s.SupplierType!='Virtual')  AND (s.Status IN(1,2) OR s.AcceptTime IS NOT NULL)", new { fxUserId })?.ToInt() ?? 0;
                }
                model.AvailableCount = result;
                model.ExisitCount = compareCount;
            }
            catch (Exception ex)
            {
                Log.WriteError($"检查分单用户是否有可用的数量时发生错误，用户ID:{fxUserId}，serviceVersionType:{serviceVersionType}，错误详情：{ex}");
            }
            return model;
        }

        /// <summary>
        /// 查询指定平台类型的商铺，只返回Id,ShopId,FxUserId
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <param name="platformType"></param>
        /// <param name="shopIds">指定ShopIds</param>
        /// <returns></returns>
        public List<FxUserShop> GetShopListByPlatformType(List<int> fxUserIds, string platformType, List<int> shopIds)
        {
            var sql =
                "SELECT Id, ShopId, FxUserId, NickName, CreateTime FROM dbo.P_FxUserShop WITH(NOLOCK) WHERE FxUserId IN @FxUserIds AND PlatformType = @PlatformType";
            if (shopIds != null && shopIds.Any())
                sql += $" AND ShopId IN ({string.Join(",", shopIds)})";
            return DbConnection.Query<FxUserShop>(sql, new { FxUserIds = fxUserIds, PlatformType = platformType })
                .ToList();
        }

        /// <summary>
        /// 查询店铺是否迁移至抖店云了
        /// </summary>
        /// <param name="shopIds">指定ShopIds</param>
        /// <returns></returns>
        public List<int> GetMigratedToTouTiaoCloudShopIds(List<int> shopIds)
        {
            var sql = @"SELECT us.ShopId
FROM dbo.P_FxUserShop us (NOLOCK)
    INNER JOIN dbo.FxDbConfig dc (NOLOCK)
        ON us.FxUserId = dc.FxUserId
           AND dc.FromFxDbConfig = 1
           AND us.PlatformType = 'TouTiao'
WHERE us.ShopId IN @shopIds;";

            return BatchQuery<int>(shopIds, "shopIds", sql, null);
            //return DbConnection.Query<int>(sql, new { shopIds }).ToList();
        }


        /// <summary>
        /// 获取用户所属店铺信息
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="platformType"></param>
        /// <returns></returns>
        public List<FxUserWithShopIdModel> GetFxUserWithShopIds(int fxUserId, string platformType)
        {
            //平台过滤
            var platformTypeWhere = string.Empty;
            if (!string.IsNullOrWhiteSpace(platformType))
            {
                switch (platformType.ToLower())
                {
                    case "alibaba":
                        platformTypeWhere =
                            " AND us.PlatformType NOT IN('System','Pinduoduo','KuaiTuanTuan','Jingdong','TouTiao')";
                        break;
                    case "pinduoduo":
                        platformTypeWhere =
                            " AND us.PlatformType IN('Pinduoduo','KuaiTuanTuan')";
                        break;
                    case "jingdong":
                        platformTypeWhere =
                            " AND us.PlatformType IN('Jingdong')";
                        break;
                    case "toutiao":
                        platformTypeWhere =
                            " AND us.PlatformType IN('TouTiao')";
                        break;
                }
            }
            var sql =
                $@"SELECT DISTINCT us.FxUserId,us.ShopId FROM P_FxUserShop us(NOLOCK) 
                  INNER JOIN P_Shop s(NOLOCK) ON us.ShopId = s.Id 
                  WHERE us.FxUserId = @FxUserId{platformTypeWhere}";
            return DbConnection.Query<FxUserWithShopIdModel>(sql, new { FxUserId = fxUserId }).ToList();
        }

        /// <summary>
        /// 是否有指定平台类型的店铺
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="platformTypes"></param>
        /// <returns></returns>
        public bool IsHasShop(int fxUserId, List<string> platformTypes)
        {
            var sql = "SELECT TOP 1 1 FROM dbo.P_FxUserShop WITH(NOLOCK) WHERE FxUserId=@fxUserId AND PlatformType IN @platformTypes";
            var result = DbConnection.QueryFirstOrDefault<bool>(sql, new { fxUserId, platformTypes });
            return result;
        }

        /// <summary>
        /// 用户订购普通应用的情况
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public List<ShopSubscribeAppView> GetSubscribeNormalAppShops(ShopSubscribeAppQuery model)
        {
            var shopWhereSql = " AND fus.FxUserId =@fxUserId ";
            if (model.ShopIds != null && model.ShopIds.Any())
                shopWhereSql = " AND s.Id IN @shopIds";

            //已订购普通应用的店铺
            var sql = $@"SELECT DISTINCT s.Id,s.ShopName,s.NickName,s.PlatformType,1 AS IsSubscribeNormalApp,fus.FxUserId AS SourceFxUserId FROM P_FxUserShop fus WITH(NOLOCK) 
INNER JOIN P_Shop s WITH(NOLOCK) ON s.Id=fus.ShopId
LEFT JOIN P_ShopExtension se WITH(NOLOCK) ON s.Id=se.ShopId
WHERE fus.PlatformType IN @platformTypes {shopWhereSql}
AND (se.Id IS NULL OR se.AppKey NOT IN @fxListingAppKeys)";
            var db = DbConnection;
            var subscribeNormalAppShops = db.Query<ShopSubscribeAppView>(sql, new { fxUserId = model.FxUserId, platformTypes=model.SupportPlatformTypes, fxListingAppKeys = CustomerConfig.FxListingAppKeys, shopIds = model.ShopIds }).ToList();
            var subscribeNormalAppShopIds = subscribeNormalAppShops?.Select(a => a.Id).ToList();
            var whereSql = string.Empty;
            var joinSql = string.Empty;
            if (subscribeNormalAppShopIds != null && subscribeNormalAppShopIds.Any())
            {
                joinSql = " LEFT JOIN dbo.FunStringToIntTable(@ids,',') t ON t.item = s.Id";
                whereSql = " AND t.item IS NULL ";
            }
            else
                subscribeNormalAppShops = new List<ShopSubscribeAppView>();

            //未订购普通应用的店铺
            sql = $@"SELECT s.Id,s.NickName,s.ShopName,s.PlatformType,0 AS IsSubscribeNormalApp,fus.FxUserId AS SourceFxUserId FROM P_FxUserShop fus WITH(NOLOCK)
INNER JOIN P_Shop s WITH(NOLOCK) ON s.Id=fus.ShopId
{joinSql}
WHERE fus.PlatformType IN @platformTypes {shopWhereSql} {whereSql} ";
            var notSubscribeNormalAppShops = db.Query<ShopSubscribeAppView>(sql, new { fxUserId = model.FxUserId, platformTypes = model.SupportPlatformTypes, ids = string.Join(",", subscribeNormalAppShopIds), shopIds = model.ShopIds }).ToList();

            if (notSubscribeNormalAppShops != null && notSubscribeNormalAppShops.Any())
                subscribeNormalAppShops.AddRange(notSubscribeNormalAppShops);

            return subscribeNormalAppShops;
        }

        ///// <summary>
        ///// 是否有指定平台类型的店铺(通过系统店铺id判断)
        ///// </summary>
        ///// <param name="fxUserId"></param>
        ///// <param name="platformTypes"></param>
        ///// <returns></returns>
        //public bool IsHasShopBySystemShopId(int systemShopId, List<string> platformTypes)
        //{
        //    var sql = $@"SELECT TOP 1 1 FROM   dbo.P_FxUserShop fp  WITH(NOLOCK) WHERE fp.PlatformType IN @platformTypes
        //            AND EXISTS 
        //            (
        //             SELECT 1 FROM dbo.P_FxUserShop up WITH(NOLOCK) 
        //             WHERE up.PlatformType = 'System' 
        //             AND up.ShopId = @systemShopId 
        //             AND fp.FxUserId = up.FxUserId
        //            )";
        //    var result = DbConnection.QueryFirstOrDefault<bool>(sql, new { systemShopId, platformTypes });
        //    return result;
        //}

        /// <summary>
        /// 通过单号分享关系Id获取ToId对应的用户
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public List<FxUserWithDbNameIdModel> GetFxUserIdByBranchShareRelationId(List<int> ids)
        {
            var sql = $@"SELECT fus.FxUserId,fdc.SystemShopId,fdc.DbNameConfigId,fdc.DbCloudPlatform FROM [P_FxUserShop] fus WITH(NOLOCK)
INNER JOIN P_BranchShareRelation bsr WITH(NOLOCK) ON bsr.ToId=fus.ShopId
LEFT JOIN FxDbConfig fdc WITH(NOLOCK) ON fdc.FxUserId=fus.FxUserId
WHERE bsr.Id IN @ShareIds";
            //WHERE bsr.Id IN({string.Join(",", ids)})";
            //return DbConnection.Query<int>(sql).Distinct().ToList();
            var sqlParameters = new DynamicParameters();
            sqlParameters.Add("@ShareIds", ids);
            return ExcuteFuncByCloundPlatform<FxUserWithDbNameIdModel>(sql, sqlParameters).ToList();
        }

        /// <summary>
        /// 通过SystemShopId获取对应的用户
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public List<FxUserWithDbNameIdModel> GetFxUserIdBySystemShopId(List<int> ids)
        {
            var sql = $@"SELECT fus.FxUserId,fdc.SystemShopId,fdc.DbNameConfigId,fdc.DbCloudPlatform FROM [P_FxUserShop] fus WITH(NOLOCK)
LEFT JOIN FxDbConfig fdc WITH(NOLOCK) ON fdc.FxUserId=fus.FxUserId
WHERE fus.PlatformType='System' AND fus.ShopId IN @ids";
            return ExcuteFuncByCloundPlatform<FxUserWithDbNameIdModel>(sql, new { ids = ids }).ToList();
        }

        /// <summary>
        /// 检查是否有绑定toutiao店铺
        /// </summary>
        /// <param name="fxUserId">用户ID</param>
        /// <returns></returns>
        public bool CheckIsAnyTouTiaoShop(int fxUserId)
        {
            var sql = $"SELECT TOP 1 1 FROM dbo.P_FxUserShop WITH(NOLOCK) WHERE FxUserId=@fxUserId AND PlatformType = 'TouTiao'";
            return DbConnection.QueryFirstOrDefault<int>(sql, new { fxUserId }) == 1;
        }

        /// <summary>
        /// 查询绑定信息
        /// </summary>
        /// <param name="platformType"></param>
        /// <param name="nickNames"></param>
        /// <returns></returns>
        public List<FxUserShop> GetFxUserShops(string platformType, List<string> nickNames, string fields = "*")
        {
            var _db = DbConnection;
            var sql = $"SELECT {fields} FROM dbo.P_FxUserShop (NOLOCK) WHERE PlatformType = @platformType AND NickName IN@nickNames";
            var fxUserShops = _db.Query<FxUserShop>(sql, new { platformType, nickNames });
            return fxUserShops.ToList();
        }

        public void UpdateFxUserShopInfo(List<FxUserShop> fxUserShops)
        {
            if (fxUserShops.Any())
            {
                var sql = "UPDATE dbo.P_FxUserShop SET NickName=@nickName,AuthTime=@authTime,Status=@status WHERE ShopId = @shopId";
                foreach (var newInfo in fxUserShops)
                {
                    var result = DbConnection.Execute(sql, new
                    {
                        nickName = newInfo.NickName,
                        authTime = newInfo.AuthTime,
                        status = newInfo.Status,
                        shopId = newInfo.ShopId
                    });
                    //缓存性能优化:直接清除缓存
                    FxCaching.RefeshShopCaching(newInfo.ShopId);
                }
            }
        }

        /// <summary>
        /// 获取系统用户店铺关系 按用户ID列表 并缓存
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <returns></returns>
        public List<FxUserShop> GetSystemFxUserShopsByFxUserIdsWithCache(List<int> fxUserIds)
        {
            //判空处理
            if (fxUserIds == null || !fxUserIds.Any())
            {
                return new List<FxUserShop>();
            }

            //查询
            var sql =
                "SELECT us.Id,us.FxUserId,us.ShopId,us.NickName,us.PlatformType FROM P_FxUserShop us WITH(NOLOCK) WHERE us.FxUserId IN @Ids AND us.PlatformType = 'System'";
            //是否表值函数参数
            var isTableValueFunctionParam = fxUserIds.Count > 3;
            if (isTableValueFunctionParam)
            {
                sql =
                    "SELECT us.Id,us.FxUserId,us.ShopId,us.NickName,us.PlatformType FROM P_FxUserShop us WITH(NOLOCK) INNER JOIN dbo.FunStringToIntTable(@Ids,',') t ON t.item = us.FxUserId WHERE us.PlatformType = 'System'";
            }

            //先取缓存，缓存中没有则再查询数据库
            return DbConnection.QueryWithSingleCache<FxUserShop, int>(sql, fxUserIds, appendCacheKey: "System",
                keyFieldName: "FxUserId",
                isTableValueFunctionParam: isTableValueFunctionParam, cacheExpireSeconds: 900);
        }

        /// <summary>
        /// 查询用户店铺关系，按用户ID，平台类型（可选）
        /// </summary>
        /// <param name="fxUserId">用户ID</param>
        /// <param name="platformType">可选</param>
        /// <param name="selectFields">可选</param>
        /// <returns></returns>
        public List<FxUserShop> GetListByFxUserIdAndPlatformType(int fxUserId, string platformType = null,
            List<string> selectFields = null)
        {
            var select = "*";
            if (selectFields != null && selectFields.Any())
            {
                select = string.Join(",", selectFields);
            }
            //其他查询条件
            var whereSql = " AND PlatformType <> @PlatformType";
            //查询参数
            var param = new DynamicParameters();
            if (string.IsNullOrWhiteSpace(platformType) == false)
            {
                whereSql = " AND PlatformType = @PlatformType";
                param.Add("@PlatformType", platformType);
            }
            else
            {
                param.Add("@PlatformType", "System");
            }
            param.Add("@FxUserId", fxUserId);
            //查询脚本
            var sql = $"SELECT {select} FROM dbo.P_FxUserShop WITH(NOLOCK) WHERE FxUserId = @FxUserId {whereSql}";
            //执行SQL
            return DbConnection.Query<FxUserShop>(sql, param).ToList();
        }
    }
}
