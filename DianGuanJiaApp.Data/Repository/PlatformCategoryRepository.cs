using Dapper;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.FxModel.CategoryProduct;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Repository.SupplierProduct;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace DianGuanJiaApp.Data.Repository
{
    /// <summary>
    ///  类目表：<see cref="PlatformCategory"/> 
    ///  类目属性表：<see cref="PlatformCategoryProp"/>
    ///  类目属性规则：<see cref="PlatformCategoryPublishRule"/> 
    ///  写入位置在这个仓储：<see cref="PlatformCategorySupplierRepository"/> 
    /// </summary>
    public class PlatformCategoryRepository : SupplierProductBaseRepository<PlatformCategory>
    {
        /// <summary>
        /// 获取类目信息
        /// </summary>
        public List<PlatformCategory> GetCategoryList(GetCategoryListReqModel req)
        {
            var sql = string.Empty;
            if (req.ShopId > 0)
            {
                // 店铺级
                sql = @"select t1.* from PlatformCategory t1 INNER JOIN PlatformCateShopRelation t2 ON t1.CateId = t2.CateId WHERE
t2.ShopId=@ShopId and t1.PlatformType = @PlatformType AND t1.status !='deleted' ";
            }
            else
            {
                // 找出 PlatformCategory 表中的末级类目Id，并且没有与 PlatformCateShopRelation 产生关联的数据(就是系统级数据)
                //  sql = @"SELECT t1.* FROM PlatformCategory t1 LEFT JOIN PlatformCateShopRelation t2 ON t1.CateId = t2.CateId WHERE t1.PlatformType = @PlatformType and t1.status !='deleted'
                //AND t2.CateId IS NULL ";
                sql = "SELECT t1.* FROM PlatformCategory AS t1 WHERE t1.PlatformType= @PlatformType AND t1.status != 'deleted' ";
            }

            if (string.IsNullOrWhiteSpace(req.Pid)) sql += " and t1.ParentId =0 ";
            else sql += " and t1.ParentId = @Pid ";

            return DbConnection.Query<PlatformCategory>(sql, new { req.Pid, req.PlatformType, req.ShopId }).ToList();
        }

        public List<PlatformCategory> GetPlatformCategoriesByShopId(int shopId, string shopPt)
        {
            var sql = string.Empty;

            if (shopId > 0)
            {
                // 没有走索引
                //sql = $@"SELECT t1.* FROM PlatformCategory AS t1  
                //        INNER JOIN PlatformCateShopRelation AS t2  ON t1.Code= t2.RelationCode
                //        WHERE t2.ShopId= @shopId AND t1.PlatformType= @shopPt AND  t1.status !='deleted' ";

                sql = @"SELECT t1.* FROM PlatformCategory t1 where
CateId in (select CateId from PlatformCateShopRelation where ShopId= @shopId)
aND t1.PlatformType= @shopPt AND  t1.status !='deleted' ";
            }
            else
                sql = $@"SELECT t1.* FROM PlatformCategory AS t1  
                        WHERE t1.PlatformType= @shopPt AND  t1.status !='deleted' ";

            var categories = DbConnection.Query<PlatformCategory>(sql, new { shopId, shopPt });
            return categories?.ToList();
        }

        public List<string> GetCategoryProp(string platformType, string endCateId)
        {
            var sql = @"select Content from platformcategoryprop where PlatformType  = @platformType and CateId = @endCateId ";

            var list = DbConnection.Query<string>(sql, new { platformType, endCateId });
            return list?.ToList();
        }
        public List<PlatformCategoryProp> GetCategoryPropByCateId(string platformType, string endCateId)
        {
            var sql = @"select * from platformcategoryprop where PlatformType  = @platformType and CateId = @endCateId ";

            var list = DbConnection.Query<PlatformCategoryProp>(sql, new { platformType, endCateId });
            return list?.ToList();
        }
        /// <summary>
        /// 获取类目所属的店铺Id
        /// </summary>
        /// <param name="cateId"></param>
        /// <returns></returns>
        public List<int> GetShopIdByCateId(string cateId)
        {
            var sql = $"select ShopId from platformcateshoprelation where CateId =@cateId";

            return DbConnection.Query<int>(sql, new { cateId }).Distinct().ToList();
        }
        public string GetPlatformCategoryPublishRule(string platformType, string endCateId)
        {
            var sql = @"select Content from PlatformCategoryPublishRule where PlatformType=@platformType and CateId=@endCateId ";

            return DbConnection.Query<string>(sql, new { platformType, endCateId }).FirstOrDefault();
        }

        public PlatformCategory GetPlatformCategory(string platformType, string endCateId)
        {
            var sql = @"select * from PlatformCategory where PlatformType=@platformType and CateId=@endCateId ";

            return DbConnection.Query<PlatformCategory>(sql, new { platformType, endCateId }).FirstOrDefault();
        }

        public int GetCurrentShop(string cateId, string platformType)
        {
            var sql = @"select ShopId from platformcateshoprelation where CateId = @cateId and relationcode = 
(select code from platformcategory where PlatformType = @platformType and CateId = @cateId limit 1) order by id desc limit 1";

            return DbConnection.QueryFirstOrDefault<int>(sql, new { cateId, platformType });
        }

        public StringBuilder GetCategoryDataComparison()
        {
            StringBuilder builder = new StringBuilder();
            var sql = @"select COUNT(1) from platformcategory where PlatformType = 'TouTiao' and IsParent = 0
union all
select COUNT(1) from PlatformCategoryPublishRule where PlatformType = 'TouTiao';";

            var result = DbConnection.Query<int>(sql).ToList();
            if (result.Count >= 2)
            {
                builder.Append($"头条末级类目：{result[0]}个；");
                builder.Append($"头条类目规则总量：{result[1]}个");
            }

            return builder;
        }

        public List<PlatformCategoryNavigation> GetNavigationCateProp(string platformType, string propertyId)
        {
            var edb = DbConnection;
            var colllist = edb.Query<PlatformCategoryNavigation>("select * from PlatformCategoryNavigation where propertyId = @propertyId and platformType=@platformType ", new { propertyId, platformType });
            return colllist.ToList();
        }
        public List<PlatformCategory> GetCategoryAndRelationList(GetCategoryListReqModel req)
        {
            var sql = string.Empty;
            if (req.ShopId > 0)
            {
                // 店铺级
                sql = @"select t1.*,t2.LicenseStatus from PlatformCategory t1 INNER JOIN PlatformCateShopRelation t2 ON t1.CateId = t2.CateId WHERE
t2.ShopId=@ShopId and t1.PlatformType = @PlatformType AND t1.status !='deleted' ";
            }
            else
            {
                sql = "SELECT t1.* FROM PlatformCategory AS t1 WHERE t1.PlatformType= @PlatformType AND t1.status != 'deleted' ";
            }

            if (string.IsNullOrWhiteSpace(req.Pid)) sql += " and t1.ParentId =0 ";
            else sql += " and t1.ParentId = @Pid ";

            return DbConnection.Query<PlatformCategory>(sql, new { req.Pid, req.PlatformType, req.ShopId }).ToList();
        }
    }
}
