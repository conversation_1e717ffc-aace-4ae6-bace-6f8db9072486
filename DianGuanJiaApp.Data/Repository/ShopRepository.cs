using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using Dapper;
using DianGuanJiaApp.Data.Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.FxModel;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using Nest;

namespace DianGuanJiaApp.Data.Repository
{

    public partial class ShopRepository : BaseRepository<Entity.Shop>
    {
        /// <summary>
        /// 场景
        /// </summary>
        private string _scene = string.Empty;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="scene">场景：listing表示铺货场景，all表示全部，其他值表示常规场景</param>
        public ShopRepository(string scene = "") : base(CustomerConfig.ConfigureDbConnectionString)
        {
            _scene = scene;
        }
        public ShopRepository() : base(CustomerConfig.ConfigureDbConnectionString)
        {
        }

        public bool IsEnabledGlobalDataCacheKey
        {
            get
            {
                return new CommonSettingRepository().IsEnabledGlobalDataCache();
            }
        }

        public List<Shop> GetAllShops()
        {
            return this.DbConnection.Query<Shop>(@"
SELECT [Id],[NickName],[ShopName],[ShopId],[AccessToken],[RefreshToken],[AuthTime],[CreateTime],[ExpireTime],
[PlatformType],[LastSyncTime],[FullSyncCompleteTime],[FullSyncStatus],[Status],[LastSyncMessage],[LastRefreshTokenTime],
[RefundOrderSyncStatus],[RefundOrderSyncStartTime]
  FROM [dbo].[P_Shop] WITH(NOLOCK)").ToList();

        }

        /// <summary>
        /// 设置店铺的增量同步状态
        /// </summary>
        /// <param name="shopIds">店铺ID</param>
        /// <param name="syncStatus">同步状态</param>
        /// <param name="synCompleteTime">同步截止时间</param>
        public void SetShopStatus(List<int> shopIds, Data.Enum.ShopSyncStatusType syncStatus, DateTime? synCompleteTime = null, string lastSyncMessage = "", bool isUpdateFullSyncStatus = false)
        {
            var db = this.DbConnection;
            if (syncStatus == Enum.ShopSyncStatusType.Syncing)
                db.ExecuteWithNoSyncToOtherConfigDb("UPDATE P_Shop SET LastSyncStatus=@syncStatus,StartSyncTime=GetDate() WHERE Id IN @shopIds", new { syncStatus = syncStatus.ToString(), shopIds });
            else
            {
                if (syncStatus == Enum.ShopSyncStatusType.Finished)
                {
                    if (synCompleteTime == null)
                        throw new ArgumentException("设置店铺为同步完成时，必须提供完成时间", "synCompleteTime");
                    else
                        db.ExecuteWithNoSyncToOtherConfigDb($"UPDATE P_Shop SET LastSyncStatus=@syncStatus,LastSyncTime=@lastSyncTime,LastSyncMessage=@lastSyncMessage {(isUpdateFullSyncStatus ? ",FullSyncStatus='Complete'" : "")} WHERE Id IN @shopIds", new { syncStatus = syncStatus.ToString(), shopIds, lastSyncTime = synCompleteTime.Value, lastSyncMessage });
                }
                else if (syncStatus == Enum.ShopSyncStatusType.Error)
                {
                    db.ExecuteWithNoSyncToOtherConfigDb("UPDATE P_Shop SET LastSyncStatus=@syncStatus,LastSyncMessage=@lastSyncMessage WHERE Id IN @shopIds", new { syncStatus = syncStatus.ToString(), shopIds, lastSyncMessage });
                }
            }
        }

        /// <summary>
        /// 设置店铺的热数据增量同步状态
        /// </summary>
        /// <param name="shopIds">店铺ID</param>
        /// <param name="syncStatus">同步状态</param>
        /// <param name="synCompleteTime">同步截止时间</param>
        public void SetShopHotOrderSyncStatus(List<int> shopIds, Data.Enum.ShopSyncStatusType syncStatus, DateTime? synCompleteTime = null, string lastSyncMessage = "", bool isUpdateFullSyncStatus = false, bool isFullSync = false)
        {
            var db = this.DbConnection;
            if (syncStatus == Enum.ShopSyncStatusType.Syncing)
            {
                if (isFullSync)
                    db.Execute("UPDATE P_HotDataSyncStatus SET FullSyncStatus=@syncStatus,StartFullSyncTime=GetDate() WHERE ShopId IN @shopIds", new { syncStatus = syncStatus.ToString(), shopIds });
                else
                    db.Execute("UPDATE P_HotDataSyncStatus SET LastSyncStatus=@syncStatus,StartSyncTime=GetDate() WHERE ShopId IN @shopIds", new { syncStatus = syncStatus.ToString(), shopIds });
            }
            else
            {
                if (syncStatus == Enum.ShopSyncStatusType.Finished)
                {
                    if (synCompleteTime == null)
                        throw new ArgumentException("设置店铺为同步完成时，必须提供完成时间", "synCompleteTime");
                    else
                    {
                        if (isFullSync)
                            db.Execute($"UPDATE P_HotDataSyncStatus SET FullSyncStatus=@syncStatus,FullSyncCompleteTime=@lastSyncTime,LastSyncMessage=@lastSyncMessage {(isUpdateFullSyncStatus ? ",FullSyncStatus='Complete'" : "")} WHERE ShopId IN @shopIds", new { syncStatus = syncStatus.ToString(), shopIds, lastSyncTime = synCompleteTime.Value, lastSyncMessage });
                        else
                            db.Execute($"UPDATE P_HotDataSyncStatus SET LastSyncStatus=@syncStatus,LastSyncTime=@lastSyncTime,LastSyncMessage=@lastSyncMessage {(isUpdateFullSyncStatus ? ",FullSyncStatus='Complete'" : "")} WHERE ShopId IN @shopIds", new { syncStatus = syncStatus.ToString(), shopIds, lastSyncTime = synCompleteTime.Value, lastSyncMessage });
                    }
                }
                else if (syncStatus == Enum.ShopSyncStatusType.Error)
                {
                    if (isFullSync)
                        db.Execute("UPDATE P_HotDataSyncStatus SET FullSyncStatus=@syncStatus,LastSyncMessage=@lastSyncMessage WHERE ShopId IN @shopIds", new { syncStatus = syncStatus.ToString(), shopIds, lastSyncMessage });
                    else
                        db.Execute("UPDATE P_HotDataSyncStatus SET LastSyncStatus=@syncStatus,LastSyncMessage=@lastSyncMessage WHERE ShopId IN @shopIds", new { syncStatus = syncStatus.ToString(), shopIds, lastSyncMessage });
                }
            }
        }

        public HotDataSyncStatus CreateHotDataSyncStatus(int shopId)
        {
            var db = this.DbConnection;
            var model = db.QueryFirstOrDefault<HotDataSyncStatus>($"select top 1 * from P_HotDataSyncStatus WITH(NOLOCK) where ShopId={shopId}");
            if (model == null)
            {
                model = new HotDataSyncStatus
                {
                    ShopId = shopId
                };
                var id = db.Insert(model);
                model.Id = id.Value;
            }
            return model;
        }

        /// <summary>
        /// 设置店铺的退款订单增量同步 状态
        /// </summary>
        /// <param name="shopIds">店铺ID</param>
        /// <param name="syncStatus">同步状态</param>
        /// <param name="synCompleteTime">同步截止时间</param>
        public void SetShopRefundOrderSyncStatus(List<int> shopIds, Data.Enum.ShopSyncStatusType syncStatus, DateTime? startTime = null, string lastSyncMessage = "")
        {
            var db = this.DbConnection;
            if (syncStatus == Enum.ShopSyncStatusType.Syncing)
                db.Execute("UPDATE P_Shop SET RefundOrderSyncStatus=@syncStatus,RefundOrderSyncStartExecTime=GetDate() WHERE Id IN @shopIds", new { syncStatus = syncStatus.ToString(), shopIds });
            else
            {
                if (syncStatus == Enum.ShopSyncStatusType.Finished)
                {
                    db.Execute($"UPDATE P_Shop SET RefundOrderSyncStatus=@syncStatus,RefundOrderSyncStartTime=@lastSyncTime,RefundOrderLastMessage=@lastSyncMessage WHERE Id IN @shopIds", new { syncStatus = syncStatus.ToString(), shopIds, lastSyncTime = startTime, lastSyncMessage });
                }
                else if (syncStatus == Enum.ShopSyncStatusType.Error)
                {
                    db.Execute("UPDATE P_Shop SET RefundOrderSyncStatus=@syncStatus,RefundOrderLastMessage=@lastSyncMessage WHERE Id IN @shopIds", new { syncStatus = syncStatus.ToString(), shopIds, lastSyncMessage });
                }
            }
        }

        public bool UpdateShopOnlyCode(int shopId, string onlyCode)
        {

            var sql = "UPDATE P_Shop SET OnlyCode =@OnlyCode WHERE Id = @ShopId;";
            var isOK = DbConnection.Execute(sql, new { OnlyCode = onlyCode, ShopId = shopId }) > 0;
            //缓存性能优化:删除原来缓存，实时更新
            FxCaching.RefeshShopCaching(shopId);
            return isOK;
        }

        public Shop UpdateShopShareCode(int shopId, string shareCode)
        {
            var sql = $"UPDATE P_Shop SET shareCode =@shareCode WHERE Id = @ShopId;";
            DbConnection.Execute(sql, new { shareCode, ShopId = shopId });
            sql = $"SELECT * FROM P_Shop {WithNoLockSql} WHERE Id = @ShopId";
            var shop = DbConnection.Query<Shop>(sql, new { ShopId = shopId }).FirstOrDefault();
            //缓存性能优化:删除原来缓存，实时更新
            FxCaching.RefeshShopCaching(shopId);
            return shop;
        }

        public void UpdateAccessToken(int shopId, string accessToken, string refreshToken = "", DateTime? time = null)
        {
            var tempSql = "";
            if (!refreshToken.IsNullOrEmpty())
            {
                tempSql = $",RefreshToken=@refreshToken";
            }
            if (time != null)
            {
                tempSql += $",ExpireTime='{(time ?? DateTime.Now.AddDays(3)).ToString("yyyy-MM-dd HH:mm:ss")}'";
            }
            var sql = $"UPDATE P_Shop SET LastSyncMessage='',AccessToken =@accessToken,LastRefreshTokenTime=GETDATE() {tempSql} WHERE Id = @shopId;";
            DbConnection.Execute(sql, new { accessToken, refreshToken, shopId });
            //缓存性能优化:删除原来缓存，实时更新
            FxCaching.RefeshShopCaching(shopId);
        }

        public void UpdateLastSyncMessage(int shopId, string lastSyncMessage)
        {
            var sql = $"UPDATE P_Shop SET LastSyncMessage =@lastSyncMessage WHERE Id = @ShopId;";
            //缓存性能优化:不需要实时更新
            DbConnection.Execute(sql, new { lastSyncMessage, ShopId = shopId });
        }
        public void UpdateOrderSyncStatusLastSyncMessage(int shopId, string lastSyncMessage)
        {
            var sql = $"UPDATE dbo.P_SyncStatus SET LastSyncMessage=@lastSyncMessage WHERE ShopId= @ShopId AND SyncType=1 AND Source='FenDanSystem';";
            DbConnection.Execute(sql, new { lastSyncMessage, ShopId = shopId });
        }

        public void UpdateUid(int shopId, string uid)
        {
            var sql = $"UPDATE P_Shop SET Uid =@uid WHERE Id = @ShopId;";
            DbConnection.Execute(sql, new { uid, ShopId = shopId });
            //缓存性能优化:删除原来缓存，实时更新
            FxCaching.RefeshShopCaching(shopId);
        }

        //public Shop GetByAccessToken(int shopId,string accessToken,string refreshToken)
        //{
        //    if (shopId <= 0 || string.IsNullOrEmpty(accessToken) || string.IsNullOrEmpty(refreshToken))
        //        return null;
        //    var sql = " select * from P_Shop where (AccessToken=@accessToken AND Id!=@shopId) OR (RefreshToken=@refreshToken AND Id!=@shopId)";
        //    var shop = DbConnection.Query<Shop>(sql,new {accessToken,shopId,refreshToken }).FirstOrDefault();
        //    return shop;
        //}

        //public void AddSameAccessTokenLog(string type,Shop shop,Shop sameShop)
        //{
        //    var updateFrom = "【更新店铺授权信息时】";
        //    if (type == "UpdateAccessToken")
        //        updateFrom = "【更新AccessToken信息时】";
        //    var msg = $"警告：{updateFrom}发现相同店铺授权信息，当前店铺：{shop?.ToJson()},数据库中相同的店铺：{sameShop?.ToJson()}";
        //    var orderSyncLog = new OrderSyncLog {
        //        CreateTime = DateTime.Now,
        //        IsError = true,
        //        Message = msg,
        //        ShopId = shop.Id,
        //    };
        //    DbConnection.Insert(orderSyncLog);
        //    Log.WriteWarning(msg);

        //}

        public void UpdateExpireTime(int shopId, DateTime time)
        {
            var sql = "UPDATE dbo.P_Shop SET ExpireTime=@time WHERE Id=@shopId";
            DbConnection.Execute(sql, new { time, shopId });
            //缓存性能优化:删除原来缓存，实时更新
            FxCaching.RefeshShopCaching(shopId);
        }
        public void UpdateExpiredTimeAndSystemVersion(int shopId, DateTime time, string systemVersion)
        {
            var sql = "UPDATE dbo.P_Shop SET ExpireTime=@time,SystemVersion=@systemVersion WHERE Id=@shopId";
            DbConnection.Execute(sql, new { time, shopId, systemVersion });
            //缓存性能优化:删除原来缓存，实时更新
            FxCaching.RefeshShopCaching(shopId);
        }
        public bool UpdateLastSyncTime(int shopId, DateTime time)
        {
            var sql = "UPDATE dbo.P_Shop SET LastSyncTime=@time WHERE Id=@shopId AND LastSyncStatus!='Syncing' AND LastSyncTime>@time";
            var result = DbConnection.Execute(sql, new { time, shopId });
            //缓存性能优化:无需更新
            return result == 1;
        }

        public bool UpdateLastSyncTimeByShopId(int shopId, DateTime time)
        {
            var sql = "UPDATE dbo.P_Shop SET LastSyncTime=@time WHERE Id=@shopId";
            return DbConnection.Execute(sql, new { time, shopId }) > 0;
        }


        public void UpdateSystemVersion(int shopId, string systemVersion)
        {
            var sql = "UPDATE dbo.P_Shop SET SystemVersion=@systemVersion WHERE Id=@shopId";
            DbConnection.Execute(sql, new { shopId, systemVersion });
            //缓存性能优化:删除原来缓存，实时更新
            FxCaching.RefeshSystemShopCaching(shopId);
        }
        public bool UpdateShopVersion(int shopId, string version)
        {
            var sql = "UPDATE dbo.P_Shop SET Version=@version WHERE Id=@shopId";
            var result = DbConnection.Execute(sql, new { version, shopId });
            //缓存性能优化:删除原来缓存，实时更新
            FxCaching.RefeshShopCaching(shopId);
            return result == 1;
        }

        public bool UpdateShopVersion(List<int> shopIds, string version)
        {
            var sql = "UPDATE dbo.P_Shop SET Version=@version WHERE Id IN@shopIds";
            var result = DbConnection.Execute(sql, new { version, shopIds });
            //缓存性能优化:无处理
            return result == 1;
        }


        public void UpdateShop(int id, Dictionary<string, string> fieldVals)
        {
            if (fieldVals == null || !fieldVals.Keys.Any())
            {
                throw new Exception("店铺更新字段不能为空");
            }

            var setFieldValSql = string.Empty;
            var parameters = new DynamicParameters();
            parameters.Add("Id", id);
            foreach (var key in fieldVals.Keys)
            {
                setFieldValSql += $"{key}=@{key},";
                parameters.Add(key, fieldVals[key]);
            }
            var sql = $"UPDATE dbo.P_Shop SET {setFieldValSql.TrimEnd(",")} WHERE Id=@Id";
            DbConnection.Execute(sql, parameters);
            //缓存性能优化:删除原来缓存，实时更新
            FxCaching.RefeshShopCaching(id);
        }

        public void UpdateAccessTokenAndExpireTime(int shopId, string accessToken, DateTime time)
        {
            var sql = "UPDATE P_Shop SET LastSyncMessage='',AccessToken =@accessToken,LastRefreshTokenTime=GETDATE(),ExpireTime=@time WHERE Id = @shopId;";
            DbConnection.Execute(sql, new { accessToken, shopId, time });
            //缓存性能优化:删除原来缓存，实时更新
            FxCaching.RefeshShopCaching(shopId);
        }

        public void UpdateLoginToken(LoginAuthToken model)
        {
            if (model != null)
            {
                DbConnection.Update(model);
                //缓存性能优化:刷新token缓存
                FxCaching.RefeshCache(FxCachingType.UserToken, model.Id.ToString(), model);
            }
        }
        public LoginAuthToken CreateToken(int shopId, string sign, bool isQuickLink = false, bool isFromParent = false, int userId = 0, int fromId = 0, int subUserId = 0, string ip = "")
        {
            return CreateToken(new LoginAuthToken
            {
                ShopId = shopId,
                Sign = sign,
                IsQuickLink = isQuickLink,
                IsFromParent = isFromParent,
                UserId = userId,
                FromId = fromId,
                SubUserId = subUserId,
                IP = ip
            });
        }

        /// <summary>
        /// 创建分销系统的Token
        /// </summary>
        /// <param name="shopId"></param>
        /// <param name="sign"></param>
        /// <param name="isQuickLink"></param>
        /// <param name="isFromParent"></param>
        /// <param name="fromId"></param>
        /// <param name="subUserId"></param>
        /// <param name="ip"></param>
        /// <returns></returns>
        public LoginAuthToken CreateFxToken(int fxUserId, string sign, int shopId = 0, bool isQuickLink = false, bool isFromParent = false, int fromId = 0, int subUserId = 0, string ip = "")
        {
            //查找当前电脑当前店铺有没有没有过期的token
            var db = DbConnection;
            var expiredTime = DateTime.Now.AddDays(15);
            if (isQuickLink)
                expiredTime = DateTime.Now.AddMonths(1);
            var extWhere = "";

            if (shopId > 0)
                extWhere += $" AND ShopId={shopId} ";
            else
                extWhere += $" AND (ShopId IS NULL OR ShopId=0)";

            if (subUserId > 0)
                extWhere += $" AND SubUserId={subUserId} ";
            else
                extWhere += $" AND (SubUserId IS NULL OR SubUserId=0)";

            if (fromId > 0)
                extWhere += $" AND FromId={fromId} ";
            else
                extWhere += $" AND (FromId IS NULL OR FromId=0)";

            if (string.IsNullOrEmpty(ip))
                extWhere += $" AND IP='{ip}'";

            var model =
                db.GetList<LoginAuthToken>($@" where FxUserId=@fxUserId
AND (Sign=@sign OR Sign IS NULL) 
AND (IsExpired IS NULL OR IsExpired=0) 
AND ExpiredTime>GETDATE() 
AND IsQuickLink=@isQuickLink 
AND IsFromParent=@isFromParent {extWhere}  Order BY CreateTime DESC ", new { fxUserId = fxUserId, sign = sign, isQuickLink = isQuickLink, isFromParent = isFromParent })?.FirstOrDefault();
            if (model == null)
            {
                model = new LoginAuthToken()
                {
                    Sign = sign,
                    CreateTime = DateTime.Now,
                    ExpiredTime = expiredTime,
                    ShopId = shopId,
                    IsExpired = false,
                    IsQuickLink = isQuickLink,
                    IsFromParent = isFromParent,
                    FxUserId = fxUserId,
                    SubUserId = subUserId,
                    FromId = fromId,
                    IP = ip
                };
                model.Id = db.Insert(model) ?? 0;

                //缓存性能优化:刷新token缓存
                FxCaching.RefeshCache(FxCachingType.UserToken, model.Id.ToString(), model);
            }
            else
            {
                model.ExpiredTime = expiredTime;
                model.IsExpired = false;
                db.Update(model);
                //缓存性能优化:刷新token缓存
                FxCaching.RefeshCache(FxCachingType.UserToken, model.Id.ToString(), model);
            }
            return model;
        }


        /// <summary>
        /// 更新分销系统的Token
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="sign"></param>
        /// <param name="shopId"></param>
        /// <param name="tokenId"></param>
        /// <returns></returns>
        public LoginAuthToken UpdateFxToken(LoginAuthToken token)
        {
            var db = DbConnection;
            var expiredTime = DateTime.Now.AddDays(15);
            if (token.IsQuickLink)
                expiredTime = DateTime.Now.AddMonths(1);
            var model =
                db.GetList<LoginAuthToken>($@" where FxUserId=@fxUserId
AND (Sign=@sign) 
AND IsQuickLink=@isQuickLink
AND Id=@id 
AND IsFromParent=@isFromParent Order BY CreateTime DESC ", new { 
                    id = token.Id,
                    fxUserId = token.FxUserId, 
                    sign = token.Sign, 
                    isQuickLink = token.IsQuickLink,
                    isFromParent = token.IsFromParent })?.FirstOrDefault();
            if (model != null)
            {
                model.ExpiredTime = expiredTime;
                model.IsExpired = false;
                db.Update(model);
                //缓存性能优化:刷新token缓存
                FxCaching.RefeshCache(FxCachingType.UserToken, model.Id.ToString(), model);
            }
            return model;
        }

        /// <summary>
        /// 作废分销系统当前登录token 以外的其他token
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="sign"></param>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public bool ExpiredFxToken(int fxUserId,int subfxUserId, string sign, int shopId,int tokenId)
        {
            var db = DbConnection;
            var models = new List<LoginAuthToken>();
            if (subfxUserId > 0)
            {
                models = db.GetList<LoginAuthToken>($@" where FxUserId=@fxUserId
                AND (ShopId=@shopId)
                AND (IsExpired IS NULL OR IsExpired=0)
                AND SubUserId=@subUserId",
                new { fxUserId = fxUserId, shopId = shopId, subUserId= subfxUserId }).ToList();
            }
            else
            {
                models = db.GetList<LoginAuthToken>($@" where FxUserId=@fxUserId
                AND (ShopId=@shopId)
                AND (IsExpired IS NULL OR IsExpired=0)
                AND (SubUserId IS NULL OR SubUserId=0)",
                new { fxUserId = fxUserId, shopId = shopId }).ToList();
            }
           
            if (models.Count > 0)
            {
                var currents = models.Where(p => p.Sign == sign && p.Id == tokenId).OrderByDescending(p => p.CreateTime).ToList();
                var currentIds = currents.Select(p => p.Id).Distinct().ToList();
                var others = models.Where(p => !currentIds.Contains(p.Id)).ToList();
                if (others.Count > 0)
                {
                    others.ForEach(item =>
                    {
                        item.IsExpired = true;
                        item.ExpiredTime = DateTime.Now.AddDays(-1);
                    });
                    db.BatchUpdateByNotUpdateFields(others, new List<string> { "Id" });
                    // others.ForEach(item => { FxCaching.RefeshCache(FxCachingType.UserToken, item.Id.ToString(), item); });
                    others.ForEach(item => { FxCaching.RefeshCache(FxCachingType.UserToken, item.Id.ToString(), item); });
                }
            }
            return true;
        }

        /// <summary>
        /// 作废分销系统用户登录令牌
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="subfxUserId"></param>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public bool ExpiredFxTokenAll(int fxUserId, int subfxUserId, int shopId)
        {
            var db = DbConnection;
            var models = new List<LoginAuthToken>();
            using (db)
            {
                if (db.State == ConnectionState.Closed)
                    db.Open();

                if (subfxUserId > 0)
                {
                    // 子账号
                    models = db.GetList<LoginAuthToken>($@" where FxUserId=@fxUserId
                AND (ShopId=@shopId)
                AND (IsExpired IS NULL OR IsExpired=0)
                AND SubUserId=@subUserId",
                    new { fxUserId = fxUserId, shopId = shopId, subUserId = subfxUserId }).ToList();
                }
                else
                {
                    // 主账号
                    models = db.GetList<LoginAuthToken>($@" where FxUserId=@fxUserId
                AND (ShopId=@shopId)
                AND (IsExpired IS NULL OR IsExpired=0)
                AND (SubUserId IS NULL OR SubUserId=0)",
                    new { fxUserId = fxUserId, shopId = shopId }).ToList();
                }
                if (models.Count > 0)
                {
                    models.ForEach(item =>
                    {
                        item.IsExpired = true;
                        item.ExpiredTime = DateTime.Now.AddDays(-1);
                    });
                    // db.BatchUpdateByNotUpdateFields(models, new List<string> { "Id" });
                    // 分批更新处理
                    var batchSize = 8;
                    var page = Math.Ceiling(models.Count * 1.0 / batchSize);
                    for (int i = 0; i < page; i++)
                    {
                        var data = models.Skip(i * batchSize).Take(batchSize).ToList();
                        db.BatchUpdateByNotUpdateFields(data,
                            new List<string> { "Id" },
                            new List<string> { "Id", "ShopId", "Sign", "CreateTime", "IsQuickLink", "IsFromParent", "UserId", "FxUserId", "PlatformType", "SubUserId", "SupplierUserId", "FromId", "IP" });
                    }
                    models.ForEach(item => { FxCaching.RefeshCache(FxCachingType.UserToken, item.Id.ToString(), item); });
                }
            }
            return true;
        }


        public LoginAuthToken CreateToken(LoginAuthToken token)
        {
            //查找当前电脑当前店铺有没有没有过期的token
            var db = DbConnection;
            var expiredTime = DateTime.Now.AddDays(15);
            if (token.IsQuickLink)
                expiredTime = DateTime.Now.AddMonths(1);
            var extWhere = "";
            if (token.UserId > 0)
                extWhere += $" AND UserId={token.UserId} ";
            else
                extWhere += $" AND (UserId IS NULL OR UserId=0)";

            if (token.SubUserId > 0)
                extWhere += $" AND SubUserId={token.SubUserId} ";
            else
                extWhere += $" AND (SubUserId IS NULL OR SubUserId=0)";

            if (token.FromId > 0)
                extWhere += $" AND FromId={token.FromId} ";
            else
                extWhere += $" AND (FromId IS NULL OR FromId=0)";
            if (string.IsNullOrEmpty(token.IP) && (string.IsNullOrEmpty(Convert.ToString(token.UserId)) || token.UserId == 0))
                extWhere += $" AND IP='{token.IP}'";
            var model = db.GetList<LoginAuthToken>($" where ShopId=@sid AND (Sign=@sign OR Sign IS NULL) AND (IsExpired IS NULL OR IsExpired=0) AND ExpiredTime>GETDATE() AND IsQuickLink=@isQuickLink AND IsFromParent=@isFromParent {extWhere}  Order BY CreateTime DESC ", new { sid = token.ShopId, token.Sign, token.IsQuickLink, token.IsFromParent })?.FirstOrDefault();
            if (model == null)
            {
                model = new LoginAuthToken()
                {
                    Sign = token.Sign,
                    CreateTime = DateTime.Now,
                    ExpiredTime = expiredTime,
                    ShopId = token.ShopId,
                    IsExpired = false,
                    IsQuickLink = token.IsQuickLink,
                    IsFromParent = token.IsFromParent,
                    UserId = token.UserId,
                    SubUserId = token.SubUserId,
                    FromId = token.FromId,
                    IP = token.IP
                };
                model.Id = db.Insert(model) ?? 0;
                //缓存性能优化:刷新token缓存
                FxCaching.RefeshCache(FxCachingType.UserToken, model.Id.ToString(), model);

            }
            return model;
        }

        public LoginAuthToken GetToken(int id)
        {
            //缓存性能优化:token加入缓存
            return FxCaching.GetCache(FxCachingType.UserToken, id.ToString(), () => DbConnection.Get<LoginAuthToken>(id));
        }

        public LoginAuthToken GetTokenByFxUserId(int fxUserId)
        {
            var token = DbConnection.QueryFirstOrDefault<LoginAuthToken>($"SELECT TOP 1 * FROM dbo.P_LoginAuthToken WITH(NOLOCK) where FxUserId=@FxUserId ORDER BY Id DESC", new { FxUserId = fxUserId });
            return token;
        }

        public List<LoginAuthToken> GetTokenByFxUserIds(List<int> fxUserIds)
        {
            if (fxUserIds.IsNullOrEmptyList())
                return new List<LoginAuthToken>();
            return DbConnection.Query<LoginAuthToken>($"SELECT * FROM dbo.P_LoginAuthToken WITH(NOLOCK) WHERE FxUserId IN@fxUserIds AND IsExpired = 0", new { fxUserIds }).ToList();
        }

        /// <summary>
        /// 查询用户店铺关系 按用户ID 指定返回字段
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="selectFields"></param>
        /// <returns></returns>
        public FxUserShop GetFxUserShopByFxUserId(int fxUserId, string selectFields = "*")
        {
            var fxUserShop = DbConnection.QueryFirstOrDefault<FxUserShop>(
                $"SELECT {selectFields} FROM dbo.P_FxUserShop WITH(NOLOCK) WHERE FxUserId=@FxUserId AND PlatformType='System'",
                new { FxUserId = fxUserId });
            return fxUserShop;
        }


        public LoginAuthToken GetTokenByShopId(int shopId)
        {
            var token = DbConnection.Query<LoginAuthToken>("SELECT top 1 * FROM P_LoginAuthToken WITH(NOLOCK) WHERE shopId=@shopId order by createTime desc", new { shopId = shopId });
            return token.FirstOrDefault();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="pt"></param>
        /// <param name="mid"></param>
        /// <returns></returns>
        public InvitedMigrateShop GetInvitedMigrateShop(string pt, string mid)
        {
            var token = DbConnection.Query<InvitedMigrateShop>("SELECT top 1 * FROM P_InvitedMigrateShop WITH(NOLOCK) WHERE MemberId=@mid AND PlatformType=@pt", new { pt, mid });
            return token.FirstOrDefault();
        }

        public MigrateShop UpdateMigrateShop(MigrateShop model)
        {
            DbConnection.Update(model);
            return model;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="pt"></param>
        /// <param name="mid"></param>
        /// <returns></returns>
        public InvitedMigrateShop UpdateInvitedMigrateShop(InvitedMigrateShop model)
        {
            DbConnection.Update(model);
            return model;
        }

        public List<Shop> Query(string sql, object para)
        {
            return DbConnection.Query<Shop>(sql, para)?.ToList();
        }

        public List<Shop> GetByUserIds(List<int> userIds, bool isAll = false)
        {
            if (isAll)
            {
                var shops = DbConnection.Query<Shop>("SELECT s.* FROM dbo.P_Shop s WITH(NOLOCK) INNER JOIN P_UserShopRelation usr WITH(NOLOCK) ON s.Id = usr.ShopId WHERE usr.UserId IN @userIds", new { userIds }).ToList();
                return shops;
            }
            else
            {
                var shops = DbConnection.Query<Shop>("SELECT s.* FROM dbo.P_Shop s WITH(NOLOCK) INNER JOIN P_UserShopRelation usr WITH(NOLOCK) ON s.Id = usr.ShopId WHERE usr.IsDeleted=0 and usr.UserId IN @userIds", new { userIds }).ToList();
                return shops;
            }

        }

        public List<Shop> GetByUserIdByShouDan(int userId, bool isAll)
        {
            if (isAll)
                return DbConnection.Query<Shop>("SELECT s.* FROM dbo.P_Shop s WITH(NOLOCK)  INNER JOIN P_WxXiaDanRelation usr WITH(NOLOCK) ON s.Id = usr.CourierShopId WHERE usr.UserId=@userId", new { userId }).ToList();
            else
                return DbConnection.Query<Shop>("SELECT s.* FROM dbo.P_Shop s WITH(NOLOCK)  INNER JOIN P_WxXiaDanRelation usr WITH(NOLOCK) ON s.Id = usr.CourierShopId WHERE usr.UserId=@userId AND usr.IsDeleted !=1 ", new { userId }).ToList();

        }

        public Shop GetUserShop(int shopId, int userId)
        {
            var shop = DbConnection.Query<Shop>("SELECT top 1 s.* FROM dbo.P_Shop s WITH(NOLOCK) INNER JOIN P_UserShopRelation usr WITH(NOLOCK) ON s.Id = usr.ShopId WHERE usr.UserId=@userId AND usr.ShopId=@shopId", new { userId, shopId }).FirstOrDefault();
            return shop;
        }

        public Shop GetUserfxShop(int shopId, int userfxId)
        {
            var shop = DbConnection.Query<Shop>("SELECT top 1 s.* FROM dbo.P_Shop s WITH(NOLOCK) INNER JOIN P_UserShopRelation usr WITH(NOLOCK) ON s.Id = usr.ShopId WHERE usr.FxUserId=@userfxId AND usr.ShopId=@shopId", new { userfxId, shopId }).FirstOrDefault();
            return shop;
        }

        public Shop GetFxSystemShopByFxId(int userfxId)
        {
            return GetFxSystemShopByFxIdNew(userfxId);
            var shop = DbConnection.Query<Shop>("SELECT top 1 s.* FROM dbo.P_Shop s WITH(NOLOCK) INNER JOIN P_FxUserShop t2 WITH(NOLOCK) ON s.Id = t2.ShopId WHERE t2.PlatformType='System' AND t2.FxUserId=@userfxId"
                 , new { userfxId }).FirstOrDefault();
            return shop;
        }
        
        public Shop GetFxSystemShopByFxIdNew(int userfxId)
        {
            var fxUserShop = DbConnection.QueryFirstOrDefault<FxUserShop>(
                "SELECT TOP 1 ShopId FROM dbo.P_FxUserShop WITH(NOLOCK) WHERE PlatformType='System' AND FxUserId=@userfxId",
                new { userfxId });
    
            if (fxUserShop == null)
                return null;
        
            var shop = DbConnection.QueryFirstOrDefault<Shop>(
                "SELECT * FROM dbo.P_Shop WITH(NOLOCK) WHERE Id=@shopId",
                new { shopId = fxUserShop.ShopId });
        
            return shop;
        }

        /// <summary>
        /// 需要额外取P_FxUserShop字段用此方法，其他情况用GetFxSystemShopByFxId
        /// </summary>
        /// <param name="userfxIds"></param>
        /// <param name="fields">P_FxUserShop别名fus，P_Shop别名s</param>
        /// <returns></returns>
        public List<Shop> GetFxSystemShopByFxIdV1(List<int> userfxIds, string fields)
        {
            if (string.IsNullOrWhiteSpace(fields))
                fields = "s.*";

            var shops = DbConnection.Query<Shop>($@"
SELECT {fields} FROM dbo.P_Shop s WITH(NOLOCK) 
INNER JOIN P_FxUserShop fus WITH(NOLOCK) ON s.Id = fus.ShopId 
WHERE fus.PlatformType='System' AND fus.FxUserId IN @userfxIds"
                     , new { userfxIds }).ToList();
            return shops;
        }

        public List<Shop> GetFxSystemShopByFxId(List<int> userfxIds, string fields)
        {
            return GetFxSystemShopByFxIdNew(userfxIds, fields);
            if (string.IsNullOrWhiteSpace(fields))
                fields = "s.*";

            var shops = DbConnection.Query<Shop>($@"
SELECT {fields} FROM dbo.P_Shop s WITH(NOLOCK) 
INNER JOIN P_FxUserShop fus WITH(NOLOCK) ON s.Id = fus.ShopId 
WHERE fus.PlatformType='System' AND fus.FxUserId IN @userfxIds"
                     , new { userfxIds }).ToList();
            return shops;
        }
        
        public List<Shop> GetFxSystemShopByFxIdNew(List<int> userfxIds, string fields)
        {
            if (string.IsNullOrWhiteSpace(fields))
                fields = "*";
        
            var shopIds = DbConnection.Query<int>(
                @"SELECT ShopId FROM dbo.P_FxUserShop fus WITH(NOLOCK)
INNER JOIN dbo.FunStringToIntTable(@userfxIds, ',') as si ON si.item = FxUserId WHERE PlatformType='System'",
                new { userfxIds = string.Join(",", userfxIds) }).ToList();
        
            if (!shopIds.Any())
                return new List<Shop>();

            var shops = DbConnection.Query<Shop>(
                $"SELECT {fields} FROM dbo.P_Shop s WITH(NOLOCK) INNER JOIN dbo.FunStringToIntTable(@shopIds, ',') as si ON si.item = Id",
                new { shopIds = string.Join(",", shopIds) }).ToList();
        
            return shops;
        }

        /// <summary>
        /// 通过店铺LoginTokenId获取数据
        /// </summary>
        /// <param name="loginTokenId"></param>
        /// <returns></returns>
        public FxUserShop GetFxUserShopByLoginTokenId(int loginTokenId)
        {
            var sql = $@"SELECT TOP 1 t2.* FROM dbo.P_LoginAuthToken lat WITH(NOLOCK)
 INNER JOIN P_FxUserShop fus WITH(NOLOCK) ON fus.ShopId=lat.ShopId
 INNER JOIN P_FxUserShop t2 WITH(NOLOCK) ON fus.FxUserId = t2.FxUserId AND t2.PlatformType='System'
 WHERE lat.Id={loginTokenId}";
            return DbConnection.Query<FxUserShop>(sql).FirstOrDefault();
        }

        public List<Shop> GetByUserIds(int userId, int pageSize, int pageIndex, string platformType, string shopName, out int totalCount)
        {
            if (pageIndex <= 0)
                pageIndex = 1;
            if (pageSize <= 0)
                pageSize = 10;
            var offset = pageSize * (pageIndex - 1);
            var whereSql = " ";
            DynamicParameters prs = new DynamicParameters();
            if (!string.IsNullOrEmpty(platformType))
            {
                whereSql += " AND PlatformType=@pt";
                prs.Add("pt", platformType);
            }
            if (!string.IsNullOrEmpty(shopName))
            {
                var like = $"%{shopName}%";
                whereSql += " AND (ShopName like @sn OR NickName like @sn)";
                prs.Add("sn", like);
            }
            prs.Add("userId", userId);
            totalCount = DbConnection.ExecuteScalar($"SELECT Count(1) FROM dbo.P_Shop s WITH(NOLOCK) INNER JOIN P_UserShopRelation usr WITH(NOLOCK) ON s.Id = usr.ShopId WHERE usr.UserId=@userId {whereSql}", prs).ToInt();
            var shops = DbConnection.Query<Shop>($"SELECT s.* FROM dbo.P_Shop s WITH(NOLOCK) INNER JOIN P_UserShopRelation usr WITH(NOLOCK) ON s.Id = usr.ShopId WHERE usr.UserId=@userId {whereSql} Order By Id desc OFFSET {offset} ROWS FETCH NEXT {pageSize} ROW ONLY ", prs).ToList();
            return shops;
        }

        public Shop UpdateShopNickName(int userId, int shopId, string name)
        {
            if (string.IsNullOrEmpty(name))
                return null;
            var shop = DbConnection.Query<Shop>($"SELECT s.* FROM dbo.P_Shop s WITH(NOLOCK) INNER JOIN P_UserShopRelation usr WITH(NOLOCK) ON s.Id = usr.ShopId WHERE usr.UserId=@userId AND s.Id=@sid", new { sid = shopId, userId = userId }).ToList()?.FirstOrDefault();
            if (shop == null)
                throw new LogicException("您要修改的店铺不存在或已被您删除");
            shop.NickName = name;
            DbConnection.ExecuteScalar(" UPDATE P_SHOP Set NickName=@NickName where Id = @shopId ", new { shop.NickName, shopId });
            //缓存性能优化:昵称不需要实时更新
            return shop;
        }

        public void UpdateShopAppInfo(int shopId, string appKey, string appSecret)
        {
            DbConnection.ExecuteScalar(" UPDATE P_SHOP Set AccessToken=@appKey,RefreshToken=@appSecret where Id = @shopId ", new { appKey, appSecret, shopId });
            //缓存性能优化:删除原来缓存，实时更新
            FxCaching.RefeshShopCaching(shopId);
        }

        public List<DatabaseConfig> GetDatabaseConfigs(List<int> shopIds)
        {
            return DbConnection.Query<DatabaseConfig>("select * from P_DatabaseConfig WITH(NOLOCK) where ShopID IN @sids", new { sids = shopIds }).ToList();
        }

        public List<Shop> GetShopByIds(List<int> ids)
        {
            if (ids == null || !ids.Any())
                return new List<Shop>();
            return GetShopByIds(ids, "*");
        }


        public List<Shop> GetShopByIds(List<int> ids, string fields)
        {
            if (ids == null || ids.Any() == false)
            {
                return new List<Shop>();
            }
            ids = ids.Distinct().ToList();
            if (string.IsNullOrWhiteSpace(fields)) fields = "*";
            var sql = $@"SELECT {fields} FROM P_Shop s WITH(NOLOCK) INNER JOIN FunStringToIntTable(@ids,',') t ON s.Id=t.item";
            var shops = DbConnection.Query<Shop>(sql, new { ids = string.Join(",",ids) }).ToList();
            return shops;
        }

        public List<Shop> GetShopByMemberId(List<string> memberIds, string platformType)
        {
            var shops = DbConnection.Query<Shop>("SELECT * FROM P_Shop WITH(NOLOCK) WHERE PlatformType = @platformType AND ShopId IN@memberIds", new { platformType = platformType, memberIds = memberIds }).ToList();
            return shops;
        }

        public List<Shop> GetShopByVenderId(List<string> VenderIds, string platformType)
        {
            var shops = DbConnection.Query<Shop>("SELECT * FROM P_Shop WITH(NOLOCK) WHERE PlatformType = @platformType AND VenderId IN@VenderIds", new { platformType = platformType, VenderIds = VenderIds }).ToList();
            return shops;
        }

        public void AddJdRequestLog(jos_sdk_net.JdApiLogModel model)
        {
            var jd = Enum.PlatformType.Jingdong.ToString();
            if (string.Equals(CustomerConfig.Platform, jd, StringComparison.OrdinalIgnoreCase) == false)
                return;
            var db = Dapper.DbUtility.GetConnection(CustomerConfig.NewJdPrintDBConnectionString);
            if (db == null)
                return;
            try
            {
                db.Insert(model);
            }
            catch
            {

            }
        }
        /// <summary>
        /// 查询店铺当前的服务
        /// </summary>
        /// <param name="shopId"></param>
        /// <param name="pt">平台类型</param>
        /// <returns></returns>
        public AppOrderList GetCurrentAppService(string shopId, string pt)
        {
            var sql = "SELECT TOP 1 * FROM dbo.AppOrderList WITH(NOLOCK) WHERE MemberId=@shopId AND PlatformType=@pt AND BizStatus IN('S','1','B','TRADE_SUCCESS','IN_SERVICE') ORDER BY GmtServiceEnd DESC";
            var app = DbConnection.Query<AppOrderList>(sql, new { shopId, pt })?.FirstOrDefault();
            return app;
        }

        public void AddOrderQuantityInfo(OrderQuantityInfo model)
        {
            DbConnection.Insert(model);
        }

        public List<OrderQuantityInfo> GetOrderQuantityInfo(List<Shop> shops)
        {
            if (shops == null || !shops.Any())
                return null;
            var shop = shops.FirstOrDefault();
            var rs = new List<OrderQuantityInfo>();
            var conn = CustomerConfig.GetConnectionString(shop.PlatformType);
            if (string.IsNullOrEmpty(conn))
                return null;
            var db = Dapper.DbUtility.GetConnection(conn);
            var thisMonth = DateTime.Now.ToString("yyyy-MM-01");
            var nextMonth = DateTime.Now.AddMonths(1).ToString("yyyy-MM-01");
            var lastMonth = DateTime.Now.AddMonths(-1).ToString("yyyy-MM-01");
            var lastLastMonth = DateTime.Now.AddMonths(-2).ToString("yyyy-MM-01");
            var thisMonthWhere = $" AND CreateTime>'{thisMonth}' AND CreateTime<'{nextMonth}'";
            var lastMonthWhere = $" AND CreateTime>'{lastMonth}' AND CreateTime<'{thisMonth}'";
            var lastlastMonthWhere = $" AND CreateTime>'{lastLastMonth}' AND CreateTime<'{lastMonth}'";
            if (db != null && !string.IsNullOrEmpty(db.ConnectionString))
            {
                shops.ForEach(cur =>
                {
                    var sql = $"SELECT COUNT(1) FROM dbo.P_Order WITH(NOLOCK) WHERE ShopId={cur.Id};";
                    sql += $"SELECT COUNT(1) FROM dbo.P_Order WITH(NOLOCK) WHERE ShopId={cur.Id} {thisMonthWhere};";
                    sql += $"SELECT COUNT(1) FROM dbo.P_Order WITH(NOLOCK) WHERE ShopId={cur.Id} {lastMonthWhere};";
                    sql += $"SELECT COUNT(1) FROM dbo.P_Order WITH(NOLOCK) WHERE ShopId={cur.Id} {lastlastMonthWhere};";
                    sql += $"SELECT MAX(CreateTime) FROM dbo.P_Order WITH(NOLOCK) WHERE ShopId={cur.Id} ;";
                    sql += $"SELECT MIN(CreateTime) FROM dbo.P_Order WITH(NOLOCK) WHERE ShopId={cur.Id} ;";
                    var mutil = db.QueryMultiple(sql);
                    var info = new OrderQuantityInfo();
                    info.TotalCount = mutil.ReadSingle<int>();
                    info.ThisMonthCount = mutil.ReadSingle<int>();
                    info.LastMonthCount = mutil.ReadSingle<int>();
                    info.LastLastMonthCount = mutil.ReadSingle<int>();
                    info.NewestTime = mutil.ReadSingle<DateTime?>();
                    info.OldestTime = mutil.ReadSingle<DateTime?>();
                    info.ShopId = cur.Id;
                    if (info.NewestTime != null && info.OldestTime != null)
                    {
                        var days = (info.NewestTime - info.OldestTime).Value.TotalDays;
                        if (days > 1)
                            info.DayCount = (int)(info.TotalCount / days);
                    }
                    info.CreateTime = DateTime.Now;
                    try
                    {
                        AddOrderQuantityInfo(info);
                    }
                    catch (Exception ex)
                    {

                    }
                    rs.Add(info);
                });
            }
            return rs;
        }


        public List<Shop> GetRelationShopsByShopIds(List<int> shopIds)
        {
            var shopIdStr = string.Join(",", shopIds);
            var sql = $@"SELECT * FROM dbo.P_Shop WITH(NOLOCK) WHERE id IN({shopIdStr}) OR id IN(
							SELECT RelatedShopId FROM dbo.P_ShopRelation WITH(NOLOCK) WHERE ShopId IN({shopIdStr}) 
							UNION ALL
							SELECT RelatedShopId FROM dbo.P_ShopRelation WITH(NOLOCK) WHERE ShopId IN(SELECT ShopId FROM dbo.P_ShopRelation WHERE RelatedShopId IN({shopIdStr}) )
						)
";
            var shops = DbConnection.Query<Shop>(sql).ToList();
            return shops;
        }

        public int Execute(string sql, object obj)
        {
            return DbConnection.ExecuteScalar<int>(sql, obj);
        }
        
        /// <summary>
        /// 查询店铺，并获取其数据库配置
        /// </summary>
        /// <param name="shopIds">店铺平台ID</param>
        /// <param name="pt">平台类型</param>
        /// <param name="cloudPlatformType">云平台类型，不填写默认为阿里云</param>
        /// <returns></returns>
        public List<Shop> GetShopWithDbConfigOld(List<string> shopIds, string pt, string cloudPlatformType = "")
        {
            if (shopIds == null || shopIds.Any() == false)
                return null;
            var db = DbConnection;
            var cptCondition = "";
            if (string.IsNullOrEmpty(cloudPlatformType) == false)
                cptCondition = $" AND ds.Location='{cloudPlatformType}'";
            var sql = $@"
SELECT *
FROM P_Shop s WITH (NOLOCK)
    LEFT JOIN P_DbConfig d WITH(NOLOCK)
        ON s.Id = d.ShopId
    LEFT JOIN P_DbNameConfig dn WITH(NOLOCK)
        ON dn.Id = d.DbNameConfigId
    LEFT JOIN P_DbServerConfig ds WITH(NOLOCK)
        ON dn.DbServerConfigId = ds.Id
WHERE s.ShopId IN@shopIds AND s.PlatformType=@pt {cptCondition};";
            var model = db.Query<Shop, DbConfig, DbNameConfig, DbServerConfig, Shop>(sql, (shop, config, dbname, server) =>
            {
                if (server != null && dbname != null && config != null)
                {
                    var temp = new DbConfigModel { DbServer = server, DbNameConfig = dbname, DbConfig = config };
                    shop.DbConfig = temp;
                }
                return shop;
            }, new { shopIds, pt }, splitOn: "Id,Id,Id,Id").ToList();
            return model;
        }

        /// <summary>
        /// 查询店铺，并获取其数据库配置
        /// </summary>
        /// <param name="shopIds">店铺平台ID</param>
        /// <param name="pt">平台类型</param>
        /// <param name="cloudPlatformType">云平台类型，不填写默认为阿里云</param>
        /// <returns></returns>
        public List<Shop> GetShopWithDbConfig(List<string> shopIds, string pt, string cloudPlatformType = "")
        {
            if (shopIds == null || shopIds.Any() == false)
                return null;
            var db = DbConnection;
            if (!IsEnabledGlobalDataCacheKey)
            {
                var cptCondition = "";
                if (string.IsNullOrEmpty(cloudPlatformType) == false)
                    cptCondition = $" AND ds.Location='{cloudPlatformType}'";
                var sql = $@"
SELECT *
FROM P_Shop s WITH (NOLOCK)
    LEFT JOIN P_DbConfig d WITH(NOLOCK)
        ON s.Id = d.ShopId
    LEFT JOIN P_DbNameConfig dn WITH(NOLOCK)
        ON dn.Id = d.DbNameConfigId
    LEFT JOIN P_DbServerConfig ds WITH(NOLOCK)
        ON dn.DbServerConfigId = ds.Id
WHERE s.ShopId IN@shopIds AND s.PlatformType=@pt {cptCondition};";
                var model = db.Query<Shop, DbConfig, DbNameConfig, DbServerConfig, Shop>(sql, (shop, config, dbname, server) =>
                {
                    if (server != null && dbname != null && config != null)
                    {
                        var temp = new DbConfigModel { DbServer = server, DbNameConfig = dbname, DbConfig = config };
                        shop.DbConfig = temp;
                    }
                    return shop;
                }, new { shopIds, pt }, splitOn: "Id,Id,Id,Id").ToList();
                return model;
            }
            else
            {
                var pageSize = 2000;
                var repoisty = new DbConfigRepository();

                // shops_dbconfigs
                var shops = new List<Shop>();
                var dbConfigs = new List<DbConfig>();
                var shopPageCount = Math.Ceiling(shopIds.Count * 1.0 / pageSize);
                var shopSql = $@"SELECT * FROM P_Shop s WITH(NOLOCK) LEFT JOIN P_DbConfig d WITH (NOLOCK) ON s.Id =d.ShopId WHERE s.ShopId IN @shopIds AND s.PlatformType=@pt";
                for (var i = 0; i < shopPageCount; i++)
                {
                    // 分批
                    var shopIdsTemp = shopIds.Skip(i * pageSize).Take(pageSize).ToList();
                    var shopSqlParmeters = new DynamicParameters();
                    shopSqlParmeters.Add("@shopIds", shopIdsTemp);
                    shopSqlParmeters.Add("@pt", pt);
                    var shopsTemp = db.Query<Shop, DbConfig, Shop>(shopSql, (shop, config) =>
                    {
                        if (config != null)
                        {
                            var temp = new DbConfigModel {DbConfig = config };
                            shop.DbConfig = temp;
                        }
                        return shop;
                    }, shopSqlParmeters, splitOn: "Id,Id").ToList();


                    if (shopsTemp != null && shopsTemp.Any())
                        shops.AddRange(shopsTemp);
                }

                if (shops == null || shops.Any() == false)
                {
                    return null;
                }
                
                dbConfigs = shops.Where(p => p.DbConfig?.DbConfig != null).Select(p => p.DbConfig?.DbConfig).ToList() ?? new List<DbConfig>();
                shops.ForEach(shop => shop.DbConfig = null);

                // dbNameConfigs
                var dbNameConfigIds = dbConfigs.Where(p=>p.DbNameConfigId.HasValue) .Select(p => p.DbNameConfigId.Value).Distinct().ToList();
                var dbNameConfigs = new List<DbNameConfig>();
                var dbNamePageCount = Math.Ceiling(dbNameConfigIds.Count * 1.0 / pageSize);
                for (var i = 0; i < dbNamePageCount; i++)
                {
                    // 分批
                    var dbNameConfigIdsTemp = dbNameConfigIds.Skip(i * pageSize).Take(pageSize).ToList();
                    var dbNameConfigstemp = repoisty.GetDbNameConfigsByIdsWithCache(dbNameConfigIdsTemp);
                    if (dbNameConfigstemp != null && dbNameConfigstemp.Any())
                        dbNameConfigs.AddRange(dbNameConfigstemp);
                }

                // dbServerConfigs
                var dbServerConfigIds = dbNameConfigs.Select(p=>p.DbServerConfigId).Distinct().ToList();
                var dbServerConfigs = repoisty.GetDbServerConfigsWithCache();
                if (dbServerConfigs == null)
                {
                    dbServerConfigs = new List<DbServerConfig>();
                }
                if (dbServerConfigIds.Any())
                {
                    dbServerConfigs = dbServerConfigs.Where(p => dbServerConfigIds.Contains(p.Id)).ToList();
                }
                if (string.IsNullOrEmpty(cloudPlatformType) == false)
                {
                    dbServerConfigs = dbServerConfigs.Where(p=>p.Location== cloudPlatformType).ToList();
                }

                var datas = from s in shops 
                        join d in dbConfigs on s?.Id equals d?.ShopId into dbConfigsGroup
                        from d in dbConfigsGroup.DefaultIfEmpty()
                        join dn in dbNameConfigs on d?.DbNameConfigId equals dn?.Id into dbNameConfigsGroup
                        from dn in dbNameConfigsGroup.DefaultIfEmpty()
                        join ds in dbServerConfigs on dn?.DbServerConfigId equals ds?.Id into dbServerConfigsGroup
                        from ds in dbServerConfigsGroup.DefaultIfEmpty()
                        select new ShopDbConfigModel
                        {
                            Shop = s,
                            DbConfigModel = ds != null && db != null && d != null ? new DbConfigModel { DbServer = ds, DbNameConfig = dn, DbConfig = d}: null
                        };

                var shopsRes = new List<Shop>();
                datas = datas.ToList();
                if (datas != null && datas.Any())
                {
                    foreach (var data in datas)
                    {
                        var shop = data?.Shop;
                        var configs = data?.DbConfigModel;
                        if (shop != null)
                        {
                            var tempShop = CommUtls.DeepClone(shop);
                            tempShop.DbConfig = configs;
                            shopsRes.Add(tempShop);
                        }
                    }
                }
                return shopsRes;
            }
        }


        public Shop GetShopWithDbConfig(int shopId)
        {
            if (shopId <= 0)
                return null;
            var db = DbConnection;
            var sql = $@"
SELECT top 1 *
FROM P_Shop s WITH (NOLOCK)
    LEFT JOIN P_DbConfig d WITH(NOLOCK)
        ON s.Id = d.ShopId 
    LEFT JOIN P_DbNameConfig dn WITH(NOLOCK)
        ON dn.Id = d.DbNameConfigId
    LEFT JOIN P_DbServerConfig ds WITH(NOLOCK)
        ON dn.DbServerConfigId = ds.Id
WHERE s.Id=@shopId;";
            var model = db.Query<Shop, DbConfig, DbNameConfig, DbServerConfig, Shop>(sql, (shop, config, dbname, server) =>
             {
                 var temp = new DbConfigModel { DbServer = server, DbNameConfig = dbname, DbConfig = config };
                 shop.DbConfig = temp;
                 return shop;
             }, new { shopId }, splitOn: "Id,Id,Id,Id").FirstOrDefault();
            return model;
        }

        public void TryToCreateDbConfig(int shopId, int dbNameConfigId)
        {
            var db = DbConnection;
            var oldDbConfig = db.QueryFirstOrDefault<DbConfig>($"SELECT * FROM dbo.P_DbConfig {WithNoLockSql} WHERE ShopId=@shopId", new { shopId });
            if (oldDbConfig == null)
            {
                db.Insert(new DbConfig
                {
                    ShopId = shopId,
                    DbNameConfigId = dbNameConfigId,
                    CreateTime = DateTime.Now,
                    IsDisabled = false,
                    IsHotTableEnabled = false,
                    UserId = 0
                });
            }
        }


        public List<DbConfig> GetDbConfigList(List<int> list, string platformType)
        {
            if (platformType.ToLower() == "pinduoduo" || platformType.ToLower() == "pdd")
            {
                var db = DianGuanJiaApp.Data.Dapper.DbApiAccessUtility.GetPddConfigureDb();
                var listDb = db.Query<DbConfig>("SELECT * FROM dbo.P_DbConfig WITH(NOLOCK) WHERE ShopId IN@ShopIds", new { ShopIds = list }).ToList();

                return listDb;
            }
            else
            {
                var db = DbConnection;
                var listDb = db.Query<DbConfig>("SELECT * FROM dbo.P_DbConfig WITH(NOLOCK) WHERE ShopId IN@ShopIds", new { ShopIds = list }).ToList();

                return listDb;
            }

        }

        public List<DataMigrateTask> GetDataMigrateTaskList(int shopId, string platformType)
        {
            if (platformType.ToLower() == "pinduoduo" || platformType.ToLower() == "pdd")
            {
                var db = DianGuanJiaApp.Data.Dapper.DbApiAccessUtility.GetPddConfigureDb();
                var listDb = db.Query<DataMigrateTask>("SELECT * FROM dbo.P_DataMigrateTask WITH(NOLOCK) WHERE ShopId =@ShopId", new { ShopId = shopId }).ToList();

                return listDb;
            }
            else
            {
                var db = DbConnection;
                var listDb = db.Query<DataMigrateTask>("SELECT * FROM dbo.P_DataMigrateTask WITH(NOLOCK) WHERE ShopId =@ShopId", new { ShopId = shopId }).ToList();

                return listDb;
            }

        }

        public string SaveOneDataMigrateTask(int shopId, int sourceDbNameConfigId, int targetDbNameConfigId, string platformType)
        {
            var sql = $"INSERT INTO dbo.P_DataMigrateTask(SourceDbNameConfigId, TargetDbNameConfigId,ShopId)VALUES({sourceDbNameConfigId},{targetDbNameConfigId},{shopId})";

            if (platformType.ToLower() == "pinduoduo" || platformType.ToLower() == "pdd")
            {
                var db = DianGuanJiaApp.Data.Dapper.DbApiAccessUtility.GetPddConfigureDb();

                var tid = db.ExecuteScalar(sql);

                return tid;
            }
            else
            {
                var db = DbConnection;
                var task = new DataMigrateTask
                {
                    SourceDbNameConfigId = sourceDbNameConfigId,
                    TargetDbNameConfigId = targetDbNameConfigId,
                    ShopId = shopId,
                    CreateTime = DateTime.Now,
                    HopeMigrateTime = null
                };
                var tid = db.Insert(task);

                return Convert.ToString(tid);
            }

        }





        public List<Shop> GetShopByLastSyncTime(string datetime)
        {
            return DbConnection.Query<Shop>($"SELECT * FROM P_Shop WITH(NOLOCK) WHERE LastSyncTime>'{datetime}'").ToList();
        }

        public OperatorBlockSetting GetOperatorBlockSetting(int shopId)
        {
            return DbConnection.QueryFirstOrDefault<OperatorBlockSetting>($"SELECT top 1 * FROM OperatorBlockSetting WITH(NOLOCK) WHERE ShopId='{shopId}'");
        }

        public void AddRefreshTokenLog(RefreshTokenLog model)
        {
            DbConnection.Insert(model);
        }
        /// <summary>
        /// 记录物流轨迹订阅错误数据日志到配置库
        /// </summary>
        /// <param name="logs"></param>
        public void WriteLogisticTraceSubscribleLog(List<LogisticTraceSubscribleLog> logs, string actionType)
        {
            if (logs == null || logs.Any() == false)
                return;
            logs.ForEach(x =>
            {
                x.ActionType = actionType;
            });
            try
            {
                //var db = DbConnection;
                //db.BulkInsert(logs);
                base.BulkWrite(logs, "LogisticTraceSubscribleLog");
            }
            catch (Exception ex)
            {
                Log.WriteError($"记录物流轨迹订阅错误数据日志到配置库时发生错误：{ex}");
            }
        }

        public void WriteLogisticsConsumerLog(string json, string errorMessage, string actionType)
        {
            var log = new LogisticTraceSubscribleLog
            {
                Request = json,
                CreateTime = DateTime.Now,
                Status = "Error",
                ErrorMessage = errorMessage,
                ActionType = actionType
            };
            WriteLogisticTraceSubscribleLog(new List<LogisticTraceSubscribleLog> { log }, actionType);
        }

        public List<Shop> GetShopsByPlatformType(PlatformType platformType, List<string> fields = null)
        {
            var fieldStr = fields == null ? "*" : string.Join(",", fields);
            var sql = $@"SELECT {fieldStr} FROM P_Shop WITH(NOLOCK) WHERE PlatformType='{platformType.ToString()}'";
            var shops = DbConnection.Query<Shop>(sql).ToList();
            return shops;
        }

        public int GetPddCommonSetting(string key)
        {
            var db = DbApiAccessUtility.GetPddConfigureDb();
            var defaultDbNameConfigId = db.Query<CommonSetting>(key)?.FirstOrDefault()?.Value?.ToInt() ?? 0;
            return defaultDbNameConfigId;
        }

        public void TryToCreatePddCloudDbConfig(int shopId, int dbNameConfigId, string cloudPlatformType)
        {
            var db = DbApiAccessUtility.GetPddConfigureDb();
            var oldDbConfig = db.Query<DbConfig>("SELECT * FROM dbo.P_DbConfig WITH(NOLOCK) WHERE ShopId=@shopId AND DbCloudPlatform=@cloudPlatformType", new { shopId, cloudPlatformType })?.FirstOrDefault();
            if (oldDbConfig == null)
            {
                db.ExecuteScalar($"INSERT INTO dbo.P_DbConfig(UserId,ShopId,DbNameConfigId,CreateTime,ModifyTime) VALUES(0,{shopId},{dbNameConfigId}, GETDATE(),GETDATE())");
            }
        }

        public void TryToCreateCloudDbConfig(int shopId, int dbNameConfigId, string cloudPlatformType, int fxUserId)
        {
            var db = DbConnection;
            var oldDbConfig = db.QueryFirstOrDefault<DbConfig>("SELECT * FROM dbo.P_DbConfig WITH(NOLOCK) WHERE ShopId=@shopId AND DbCloudPlatform=@cloudPlatformType", new { shopId, cloudPlatformType });
            if (oldDbConfig == null)
            {
                db.Insert(new DbConfig
                {
                    ShopId = shopId,
                    DbNameConfigId = dbNameConfigId,
                    CreateTime = DateTime.Now,
                    IsDisabled = false,
                    IsHotTableEnabled = false,
                    DbCloudPlatform = cloudPlatformType,
                    UserId = fxUserId,
                });
            }
            if (cloudPlatformType == CustomerConfig.CloudPlatformType) new DbConfigRepository().DeleteCache(shopId, cloudPlatformType);
        }

        /// <summary>
        /// 获取店铺和状态信息
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public Shop GetShopWithSyncStatus(int fxUserId, int shopId)
        {
            //SQL
            var sql = $@"SELECT * FROM P_Shop p {WithNoLockSql} 
                        INNER JOIN P_SyncStatus sc {WithNoLockSql}
                        ON p.Id = sc.ShopId
                        WHERE p.Id = @ShopId AND sc.FxUserId = @FxUserId AND sc.Source = 'FenDanSystem'";
            //查询
            var shopDict = new Dictionary<int, Shop>();
            DbConnection.Query<Shop, SyncStatus, Shop>(sql, (shop, syncStatus) =>
            {
                Shop shopModel;
                if (shopDict.TryGetValue(shop.Id, out shopModel))
                {
                    if (!shopModel.SyncStatusList.Any(f => f.Id == syncStatus.Id))
                        shopModel.SyncStatusList.Add(syncStatus);
                }
                else
                {
                    shop.SyncStatusList = new List<SyncStatus>();
                    shop.SyncStatusList.Add(syncStatus);
                    shopDict.Add(shop.Id, shop);
                }

                return shop;
            }, new
            {
                FxUserId = fxUserId,
                ShopId = shopId
            });
            //返回
            return shopDict.Values.FirstOrDefault();
        }

        /// <summary>
        /// 获取店铺
        /// </summary>
        /// <param name="fxUserId">用户ID</param>
        /// <param name="shopId">店铺ID</param>
        /// <param name="syncType">同步类型</param>
        /// <param name="isIncludeSyncStatusData">是否包含同步状态信息（默认值：包含，且同步状态没有，则店铺信息无法NULL）</param>
        /// <returns></returns>
        public Shop GetShopByFxUserWithShopId(int fxUserId, int shopId, int? syncType = 1,
            bool isIncludeSyncStatusData = true)
        {
            var db = DbConnection;
            List<SyncStatus> syncStatus = null;
            //需要包含同步状态才查询
            if (isIncludeSyncStatusData)
            {
                //需要包含同步状态，必须要有值
                if (syncType.HasValue == false)
                {
                    throw new LogicException("店铺包含同步状态信息，同步类型必须有值");
                }

                //获取同步状态
                const string sql =
                    "SELECT * FROM P_SyncStatus WITH(NOLOCK) WHERE FxUserId = @FxUserId AND ShopId = @ShopId AND SyncType = @SyncType AND Source = 'FenDanSystem'";
                syncStatus =
                    db.Query<SyncStatus>(sql, new { FxUserId = fxUserId, ShopId = shopId, SyncType = syncType })
                        .ToList();
                if (syncStatus.Any() == false)
                {
                    return null;
                }
            }

            //获取店铺信息
            const string sqlByShop = "SELECT * FROM P_Shop WITH(NOLOCK) WHERE Id = @Id";
            var shop = db.QueryFirstOrDefault<Shop>(sqlByShop, new { Id = shopId });
            if (shop == null)
            {
                return null;
            }

            //附加用户ID
            shop.FxUserIds = fxUserId;
            //附加同步状态信息
            if (syncStatus != null && syncStatus.Any())
            {
                if (syncStatus.Count > 1)
                {
                    shop.SyncStatusList = new List<SyncStatus>
                    {
                        syncStatus.OrderByDescending(m => m.LastSyncTime2)
                            .First()
                    };
                }
                else
                {
                    shop.SyncStatusList = syncStatus;
                }
            }
            //返回
            return shop;
        }

        
        /// <summary>
        /// 获取店铺和状态信息
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public Shop GetShopWithSyncStatusNew(int fxUserId, int shopId)
        {
            var shopSql = $@"SELECT * FROM P_Shop p WITH(NOLOCK) WHERE p.Id = @ShopId";
            var shopSqlPara = new DynamicParameters();
            var pyncStatusSql = $@"SELECT * FROM P_SyncStatus sc WITH(NOLOCK) WHERE sc.FxUserId = @FxUserId AND sc.Source = 'FenDanSystem' AND sc.ShopId =@ShopId";
            var pyncStatusSqlPara = new DynamicParameters();
            shopSqlPara.Add("@ShopId", shopId);
            pyncStatusSqlPara.Add("@FxUserId", fxUserId);
            pyncStatusSqlPara.Add("@ShopId", shopId);
            var shops = DbConnection.Query<Shop>(shopSql, shopSqlPara).ToList();
            if (shops == null)
            {
                return null;
            }
            var pynStatus = DbConnection.Query<SyncStatus>(pyncStatusSql, pyncStatusSqlPara).ToList();
            var datas = pynStatus.Join(shops, p => p.ShopId, s => s.Id, (p, s) => new ShopSyncStatus { Shop = s, SyncStatus = p }).ToList();
            var shopDict = new Dictionary<int, Shop>();
            foreach (var data in datas)
            {
                var shop = data.Shop;
                var syncStatus = data.SyncStatus;
                Shop shopModel;
                if (shopDict.TryGetValue(shop.Id, out shopModel))
                {
                    if (!shopModel.SyncStatusList.Any(f => f.Id == syncStatus.Id))
                        shopModel.SyncStatusList.Add(syncStatus);
                }
                else
                {
                    shop.SyncStatusList = new List<SyncStatus>();
                    shop.SyncStatusList.Add(syncStatus);
                    shopDict.Add(shop.Id, shop);
                }
            }
            return shopDict.Values.FirstOrDefault();
        }


        public List<Shop> GetShopAndSyncStatusById(IEnumerable<int> agentIds, IEnumerable<int> ids, ShopSyncType syncType)
        {
            var type = (int)syncType;
            //组装id串
            var shopIds = "0";
            ids = ids?.Distinct().ToList();
            agentIds = agentIds?.Distinct().ToList();
            if (ids != null && ids.Any())
            {
                shopIds = string.Join(",", ids.ToList());
            }

            var aids = "0";
            if (agentIds != null && agentIds.Any())
            {
                aids = string.Join(",", agentIds.ToList());
            }

            // 先查询店铺
            const string shopSql = @"SELECT * FROM dbo.P_Shop WITH(NOLOCK) 
                    INNER JOIN dbo.FunStringToIntTable(@shopIds, ',') as si ON si.item = Id";
            var shops = DbConnection.Query<Shop>(shopSql, new { shopIds }).ToList();

            if (!shops.Any())
                return new List<Shop>();

            // 再查询同步状态
            var syncStatusSql = @"SELECT * FROM dbo.P_SyncStatus ps WITH(NOLOCK) 
                          INNER JOIN dbo.FunStringToIntTable(@shopIds, ',') as si ON si.item = ps.ShopId";
    
            if (!string.IsNullOrEmpty(aids) && aids != "0")
            {
                syncStatusSql += " INNER JOIN dbo.FunStringToIntTable(@aids, ',') as ai ON ai.item = ps.FxUserId";
            }
            var whereSql = $@" WHERE ps.SyncType =@type";
            syncStatusSql = syncStatusSql + whereSql;

            var syncStatuses = DbConnection.Query<SyncStatus>(syncStatusSql, new { shopIds, aids, type}).ToList();

            var shopDict = new Dictionary<int, Shop>();
            foreach (var shop in shops)
            {
                shop.SyncStatusList = new List<SyncStatus>();
                shopDict.Add(shop.Id, shop);
            }

            foreach (var status in syncStatuses)
            {
                Shop shopModel;
                if (shopDict.TryGetValue(status.ShopId, out shopModel))
                {
                    if (shopModel.SyncStatusList.All(f => f.Id != status.Id))
                    {
                        shopModel.SyncStatusList.Add(status);
                    }
                }
            }

            return shopDict.Values.ToList();
        }


        /// <summary>
        /// 获取店铺信息 按用户ID列表，店铺ID列表（拆分查询）
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <param name="shopIds"></param>
        /// <param name="syncType"></param>
        /// <param name="isIncludeSyncStatusData"></param>
        /// <returns></returns>
        public List<Shop> GetShopsByFxUserWithShopIds(List<int> fxUserIds, List<int> shopIds, int? syncType = 1,
            bool isIncludeSyncStatusData = true)
        {
            //判空处理
            if (fxUserIds == null || !fxUserIds.Any() || shopIds == null || !shopIds.Any())
            {
                return new List<Shop>();
            }
            var db = DbConnection;
            List<SyncStatus> syncStatus = null;
            //是否需要包含状态信息
            if (isIncludeSyncStatusData)
            {
                //需要包含同步状态，必须要有值
                if (syncType.HasValue == false)
                {
                    throw new LogicException("店铺包含同步状态信息，同步类型必须有值");
                }
                //获取同步状态
                var syncStatusRepository = new SyncStatusRepository();
                syncStatus =
                    syncStatusRepository.GetListByFxUserIdsAndShopIdsAndSyncType(fxUserIds, shopIds, syncType.Value);
                if (syncStatus.Any() == false)
                {
                    return new List<Shop>();
                }
                //非订单同步，如果发现店铺授权过期，则取一次订单授权
                var syncStatusByShopExpired = syncStatus.Where(m => m.ShopIsExpired).ToList();
                if (syncType.Value != 1 && syncStatusByShopExpired.Any())
                {
                    var fxUserIdsByOrder = syncStatusByShopExpired.Select(m => m.FxUserId).Distinct().ToList();
                    var shopIdsByOrder = syncStatusByShopExpired.Select(m => m.ShopId).Distinct().ToList();
                    var syncStatusByOrder =
                        syncStatusRepository.GetListByFxUserIdsAndShopIdsAndSyncType(fxUserIdsByOrder, shopIdsByOrder,
                            1, "Id,FxUserId,ShopId,SyncType,LastSyncMessage");
                    if (syncStatusByOrder != null && syncStatusByOrder.Any())
                    {
                        syncStatusByShopExpired.ForEach(status =>
                        {
                            var orderStatus = syncStatusByOrder.FirstOrDefault(m =>
                                m.FxUserId == status.FxUserId && m.ShopId == status.ShopId);
                            if (orderStatus != null)
                            {
                                status.LastSyncMessage = orderStatus.LastSyncMessage;
                            }
                        });
                    }
                }
            }

            //获取店铺信息
            const string sqlByShop = @"SELECT s.* FROM P_Shop s WITH(NOLOCK) 
                                       INNER JOIN dbo.FunStringToIntTable(@ShopIds, ',') AS  si ON si.item = s.Id";
            var shops = db.Query<Shop>(sqlByShop, new { ShopIds =  string.Join(",", shopIds) }).ToList();
            if (shops.Any() == false)
            {
                return new List<Shop>();
            }
            //附加同步状态信息
            if (syncStatus != null && syncStatus.Any())
            {
                shops.ForEach(shop =>
                {
                    var status = syncStatus.Where(m => m.ShopId == shop.Id).ToList();
                    if (status.Any() == false)
                    {
                        return;
                    }

                    if (status.Count > 1)
                    {
                        var state = status.OrderByDescending(m => m.LastSyncTime2)
                            .First();
                        shop.SyncStatusList = new List<SyncStatus>
                        {
                            state
                        };
                        shop.FxUserIds = state.FxUserId;
                    }
                    else
                    {
                        shop.SyncStatusList = status;
                        shop.FxUserIds = status.First().FxUserId;
                    }
                });
            }
            //返回
            return shops;
        }
        /// <summary>
        /// 获取分享系统店铺全量同步信息
        /// </summary>
        /// <param name="agentId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public IEnumerable<Shop> GetFxOrderSyncedList(IEnumerable<string> pt_in, IEnumerable<string> pt_not_in, SyncTaskType taskType, int topCount = 100, int shopId = 0)
        {
            var ptCondition = "";
            if (pt_in != null && pt_in.Any())
                ptCondition += " t1.PlatformType IN @in_pt AND ";
            if (pt_not_in != null && pt_not_in.Any())
                ptCondition += " t1.PlatformType NOT IN @not_in_pt AND ";

            var sql = $@"SELECT top {topCount} * FROM dbo.P_Shop AS t1 WITH(NOLOCK) 
INNER JOIN dbo.P_SyncTask AS t2 WITH(NOLOCK) ON t1.Id = t2.ShopId
INNER JOIN dbo.P_SyncStatus AS t3 WITH(NOLOCK) ON t1.Id = t3.ShopId
LEFT JOIN dbo.P_ShopExtension AS t4 WITH(NOLOCK) ON t1.Id = t4.ShopId
WHERE {ptCondition} t2.TaskType =@taskType AND t3.SyncType=@taskType AND t2.Source='FenDanSystem' AND t3.Source='FenDanSystem'  
AND t3.FullSyncCompleteTime IS NULL AND ( t3.FullSyncStatus IS NULL OR t3.FullSyncStatus = 'Pending' OR ( t3.FullSyncStatus = 'Error' AND t3.FullSyncErrorTimes < 5 ) )";
            if (shopId > 0)//指定店铺
                sql = $@"SELECT top {topCount} * FROM dbo.P_Shop AS t1 WITH(NOLOCK) 
INNER JOIN dbo.P_SyncTask AS t2 WITH(NOLOCK) ON t1.Id = t2.ShopId
INNER JOIN dbo.P_SyncStatus AS t3 WITH(NOLOCK) ON t1.Id = t3.ShopId
LEFT JOIN dbo.P_ShopExtension AS t4 WITH(NOLOCK) ON t1.Id = t4.ShopId
WHERE t2.TaskType =@taskType AND t3.SyncType=@taskType 
AND t1.Id={shopId} AND t2.Source='FenDanSystem' AND t3.Source='FenDanSystem'";

            var shopDict = new Dictionary<int, Shop>();
            DbConnection.Query<Shop, SyncTask, SyncStatus, ShopExtension, Shop>(sql, (shop, syncTask, syncStatus, shopExtension) =>
             {
                 Shop shopModel;
                 if (shopDict.TryGetValue(shop.Id, out shopModel))
                 {
                     if (syncTask != null)
                         shopModel.SyncTaskList.Add(syncTask);

                     if (syncStatus != null)
                         shopModel.SyncStatusList.Add(syncStatus);

                     if (shopExtension != null)
                         shopModel.ShopExtension = shopExtension;
                 }
                 else
                 {
                     shop.SyncTaskList = new List<SyncTask>();
                     if (syncTask != null)
                         shop.SyncTaskList.Add(syncTask);

                     shop.SyncStatusList = new List<SyncStatus>();
                     if (syncStatus != null)
                         shop.SyncStatusList.Add(syncStatus);

                     if (shopExtension != null)
                         shop.ShopExtension = shopExtension;

                     shopDict.Add(shop.Id, shop);
                 }
                 return shop;
             }, new
             {
                 in_pt = pt_in,
                 not_in_pt = pt_not_in,
                 taskType = taskType
             });

            var syncStatusRepo = new SyncStatusRepository();
            var list = new List<Shop>();
            var sids = shopDict.Values.Select(f => f.Id);
            //由于全量同步中有同步产品，所以需要把产品同步状态控制查询出来
            var syncProductStatusList = syncStatusRepo.Get("WHERE ShopId in @sids AND SyncType=2", new { sids });
            shopDict.Values.ToList().ForEach(f =>
            {
                var productSyncStatus = syncProductStatusList.FirstOrDefault(p => p.ShopId == f.Id);
                if (productSyncStatus != null)
                {
                    f.SyncStatusList.Add(productSyncStatus);
                }
                list.Add(f);
            });
            return GetShopByFxShopExtension(list);
        }



        /// <summary>
        /// 根据商家和店铺平台加载关联的店铺
        /// </summary>
        /// <param name="agentId"></param>
        /// <param name="pt"></param>
        /// <returns></returns>
        public IEnumerable<Shop> GetShopsByAgentAndShopPt(int agentId, List<string> pts)
        {
            var sql = @"SELECT t2.*,t3.* FROM P_AgentShop AS t1 WITH(NOLOCK) 
INNER JOIN P_Shop AS t2 WITH(NOLOCK) on t1.ShopId=t2.Id 
INNER JOIN P_SyncStatus as t3 WITH(NOLOCK) on t2.Id=t3.ShopId
WHERE t1.AgentId=@agentId and t2.PlatformType in@pts and t1.Status = @status AND t3.Source='FenDanSystem'";
            var shopDict = new Dictionary<int, Shop>();
            DbConnection.Query<Shop, SyncStatus, Shop>(sql, (shop, syncStatus) =>
            {
                Shop shopModel;
                if (shopDict.TryGetValue(shop.Id, out shopModel))
                {
                    if (syncStatus != null)
                        shopModel.SyncStatusList.Add(syncStatus);
                }
                else
                {
                    shop.SyncStatusList = new List<SyncStatus>();
                    if (syncStatus != null)
                        shop.SyncStatusList.Add(syncStatus);

                    shopDict.Add(shop.Id, shop);
                }
                return shop;
            }, new { agentId, pts, status = (int)FxUserShopStatus.Binded });
            return shopDict.Values;
        }

        /// <summary>
        /// 根据站点所在云平台获取商家的店铺信息
        /// </summary>
        /// <param name="agentId">商家id</param>
        /// <param name="pt_in"></param>
        /// <param name="pt_not_in"></param>
        /// <returns></returns>
        public IEnumerable<Shop> GetFxShopByAgentId(int agentId, IEnumerable<string> pt_in, IEnumerable<string> pt_not_in)
        {
            var ptCondition = "";
            if (pt_in != null && pt_in.Any())
                ptCondition += " t1.PlatformType IN @in_pt AND ";
            if (pt_not_in != null && pt_not_in.Any())
                ptCondition += " t1.PlatformType NOT IN @not_in_pt AND ";

            var sql = $@"SELECT t1.*,t3.*,t4.* FROM dbo.P_Shop AS t1 WITH(NOLOCK) 
INNER JOIN dbo.P_AgentShop AS t2 WITH(NOLOCK) ON t1.Id = t2.ShopId
INNER JOIN P_SyncStatus as t3 WITH(NOLOCK) on t1.Id = t3.ShopId
INNER JOIN P_SyncTask t4 WITH(NOLOCK) on t1.Id = t4.shopId
WHERE {ptCondition} t2.AgentId=@aid AND t3.AgentId = @aid AND t4.AgentId = @aid AND t2.Status != 2 AND t3.Source='FenDanSystem' AND t4.Source='FenDanSystem';";

            Dictionary<int, Shop> dict = new Dictionary<int, Shop>();
            var syncTaskDict = new Dictionary<int, SyncTask>();
            var syncStatusDict = new Dictionary<int, SyncStatus>();
            DbConnection.Query<Shop, SyncStatus, SyncTask, Shop>(sql, (shop, syncStatus, syncTask) =>
            {
                Shop model;
                if (dict.TryGetValue(shop.Id, out model))
                {
                    model.SyncTaskList.Add(syncTask);
                    model.SyncStatusList.Add(syncStatus);
                }
                else
                {
                    shop.SyncTaskList = new List<SyncTask>();
                    if (syncTask != null && syncTaskDict.ContainsKey(syncTask.Id) == false)
                    {
                        shop.SyncTaskList.Add(syncTask);
                        syncTaskDict.Add(syncTask.Id, syncTask);
                    }

                    shop.SyncStatusList = new List<SyncStatus>();
                    if (syncStatus != null && syncStatusDict.ContainsKey(syncStatus.Id) == false)
                    {
                        shop.SyncStatusList.Add(syncStatus);
                        syncStatusDict.Add(syncStatus.Id, syncStatus);
                    }

                    dict.Add(shop.Id, shop);
                }
                return model;
            }, new
            {
                in_pt = pt_in,
                not_in_pt = pt_not_in,
                aid = agentId
            });

            return dict.Values.ToList();
        }


        /// <summary>
        /// 根据店铺id加载店铺信息
        /// </summary>
        /// 
        /// <returns></returns>
        public IEnumerable<Shop> GetShopsAndSyncStatusById(List<int> ids)
        {
            var sql = @"SELECT * FROM P_Shop AS t1 WITH(NOLOCK) 
INNER JOIN P_SyncStatus as t2 WITH(NOLOCK) on t1.Id=t2.ShopId
WHERE t1.Id IN @sids AND t2.Source='FenDanSystem'";
            var shopDict = new Dictionary<int, Shop>();
            DbConnection.Query<Shop, SyncStatus, Shop>(sql, (shop, syncStatus) =>
            {
                Shop shopModel;
                if (shopDict.TryGetValue(shop.Id, out shopModel))
                {
                    if (syncStatus != null)
                        shopModel.SyncStatusList.Add(syncStatus);
                }
                else
                {
                    shop.SyncStatusList = new List<SyncStatus>();
                    if (syncStatus != null)
                        shop.SyncStatusList.Add(syncStatus);

                    shopDict.Add(shop.Id, shop);
                }
                return shop;
            }, new { sids = ids });
            return shopDict.Values;
        }

        public Shop GetShopAndShopExtension(int sId)
        {
//            var sql = @"SELECT * FROM P_Shop AS t1 WITH(NOLOCK) 
//LEFT JOIN P_ShopExtension as t2 WITH(NOLOCK) on t1.Id=t2.ShopId --AND (t2.Source IS NULL OR t2.Source = '' OR t2.Source='FendanSystem')
//WHERE t1.Id = @id";
//            var shops = DbConnection.Query<Shop, ShopExtension, Shop>(sql, (shop, shopExtension) =>
//             {
//                 Shop _shop = shop;
//                 _shop.ShopExtension = shopExtension;
//                 return _shop;
//             }, new { id = sId });

            var shopSql = $@"SELECT * FROM P_Shop AS t1 WITH(NOLOCK) WHERE t1.Id = @id";
            var shopSqlParam = new { id = sId };
            var shopExtenSql = $@"SELECT * FROM P_ShopExtension AS t2 WITH(NOLOCK) WHERE t2.ShopId = @id";

            var shops = DbConnection.Query<Shop>(shopSql, shopSqlParam).ToList();
            var shopExtension = DbConnection.Query<ShopExtension>(shopExtenSql, shopSqlParam).ToList();

            // 处理
            var list = from t1 in shops
                       join t2 in shopExtension on t1.Id equals t2.ShopId into shop_extentsion
                       from t2 in shop_extentsion.DefaultIfEmpty()
                       select new ShopTemp
                       {
                           Shop = t1,
                           ShopExtension = t2
                       };
            var testList = list.ToList();
            var newShops = new List<Shop>();
            foreach (var item in list)
            {
                var shop = CommUtls.DeepClone(item.Shop);
                var shope = item.ShopExtension == null ? null : CommUtls.DeepClone(item.ShopExtension);
                if (shop != null)
                {
                    shop.ShopExtension = shope;
                }
                newShops.Add(shop);
            }
            //shops = shopExtension.Join(shops, e => e.ShopId, s => s.Id,(e, s) => {  s.ShopExtension = e; return s; }).ToList();

            return GetShopByFxShopExtension(newShops.ToList()).FirstOrDefault();
        }

        public Shop GetShopAndShopExtension(int sId, string appKey)
        {
            var sql = @"SELECT * FROM P_Shop AS t1 WITH(NOLOCK) 
LEFT JOIN P_ShopExtension as t2 WITH(NOLOCK) on t1.Id=t2.ShopId AND t2.AppKey=@appKey
WHERE t1.Id = @id";
            var shops = DbConnection.Query<Shop, ShopExtension, Shop>(sql, (shop, shopExtension) =>
            {
                Shop _shop = shop;
                _shop.ShopExtension = shopExtension;
                return _shop;
            }, new { id = sId, appKey });
            return shops?.FirstOrDefault();
        }

        public List<Shop> GetShopsAndShopExtension(List<int> sIds)
        {
            var shops = new List<Shop>();
            var groupSize = 1000;
            var count = Math.Ceiling(sIds.Count * 1.0 / groupSize);
            for (int i = 0; i < count; i++)
            {
                var tmpSids = sIds.Skip(i * groupSize).Take(groupSize).ToList();
                var sql = @"SELECT * FROM P_Shop AS t1 WITH(NOLOCK) 
LEFT JOIN P_ShopExtension as t2 WITH(NOLOCK) on t1.Id=t2.ShopId --AND (t2.Source IS NULL OR t2.Source = '' OR t2.Source='FendanSystem')
INNER JOIN dbo.FunStringToIntTable(@sids,',') t ON t1.Id = t.item";
                var result = DbConnection.Query<Shop, ShopExtension, Shop>(sql, (shop, shopExtension) =>
                {
                    Shop _shop = shop;
                    _shop.ShopExtension = shopExtension;
                    return _shop;
                }, new { sids = string.Join(",", tmpSids) });
                if (result.Any())
                    shops.AddRange(result);
            }

            return GetShopByFxShopExtension(shops.ToList());
        }

        public bool ShopExistsZhuKeSystem(int sid)
        {
            var sql = $@" SELECT 1 FROM dbo.ListingOwnerShop WITH(NOLOCK) WHERE ShopId={sid}";
            var count = DbConnection.Query<int>(sql);
            return count.Any();
        }

        /// <summary>
        /// 获取拼多多店铺信息
        /// </summary>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public List<Shop> GetPddShopsAndShopExtension(int pageIndex = 1, int pageSize = 500)
        {

            var sql = $@"SELECT t1.*,t2.*
FROM P_Shop t1 WITH(NOLOCK) 
INNER JOIN P_FxUserShop us WITH(NOLOCK) ON us.ShopId=t1.Id
INNER JOIN P_ShopExtension t2 WITH(NOLOCK) ON t1.Id=t2.ShopId --AND (t2.Source IS NULL OR t2.Source = '' OR t2.Source='FendanSystem')
WHERE t1.PlatformType='Pinduoduo'
ORDER BY t1.Id
    OFFSET {(pageIndex - 1) * pageSize} ROWS FETCH NEXT {pageSize} ROWS ONLY";
            var shops = DbConnection.Query<Shop, ShopExtension, Shop>(sql, (shop, shopExtension) =>
            {
                Shop _shop = shop;
                _shop.ShopExtension = shopExtension;
                return _shop;
            });

            return GetShopByFxShopExtension(shops.ToList());
        }

        /// <summary>
        /// 获取指定Ids拼多多店铺信息
        /// </summary>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public List<Shop> GetPddShopsAndShopExtensionByIds(List<int> shopIds)
        {

            var sql = $@"SELECT t1.*,t2.*
FROM P_Shop t1 WITH(NOLOCK) 
INNER JOIN P_FxUserShop us WITH(NOLOCK) ON us.ShopId=t1.Id
INNER JOIN P_ShopExtension t2 WITH(NOLOCK) ON t1.Id=t2.ShopId --AND (t2.Source IS NULL OR t2.Source = '' OR t2.Source='FendanSystem')
WHERE t1.PlatformType='Pinduoduo' AND t1.Id IN @shopIds";
            var shops = DbConnection.Query<Shop, ShopExtension, Shop>(sql, (shop, shopExtension) =>
            {
                Shop _shop = shop;
                _shop.ShopExtension = shopExtension;
                return _shop;
            }, new { shopIds = shopIds });

            return GetShopByFxShopExtension(shops.ToList());
        }


        public Shop GetNewestShopWithShopExtension(string pt)
        {
            var sql = $@"
SELECT TOP 1 * FROM dbo.P_Shop s WITH(NOLOCK) INNER JOIN P_ShopExtension se WITH(NOLOCK) ON se.ShopId=s.Id  AND (se.Source IS NULL OR se.Source = '' OR se.Source='FendanSystem')
WHERE s.PlatformType=@pt
ORDER BY se.Id DESC";
            var shops = DbConnection.Query<Shop, ShopExtension, Shop>(sql, (s, se) =>
            {
                if (s != null && se != null)
                    s.ShopExtension = se;
                return s;
            }, new { pt });
            return GetShopByFxShopExtension(shops.ToList()).FirstOrDefault();
        }

        /// <summary>
        /// 只获取自己的店铺列表,不包括系统店铺
        /// </summary>
        /// <param name="fxuserid"></param>
        /// <param name="platformType"></param>
        /// <returns></returns>
        public List<Shop> GetShopSelf(int fxuserid, string platformType = "")
        {
            return GetSelfShopsByFxUserIdAndPlatformType(fxuserid, platformType);
            
            // var whereSql = " AND fus.PlatformType <> 'System'";
            // var parameters = new DynamicParameters();
            // parameters.Add("FxUserId", fxuserid);
            // if (!string.IsNullOrEmpty(platformType))
            // {
            //     whereSql = " AND fus.PlatformType=@platformType";
            //     parameters.Add("platformType", platformType);
            // }
            //
            // List<Shop> result = new List<Shop>();
            // string sql = $@" SELECT shop.* FROM dbo.P_FxUserShop AS fus WITH(NOLOCK)
            //                 INNER JOIN dbo.P_Shop AS shop  WITH(NOLOCK) ON fus.ShopId = shop.Id
            //                 WHERE fus.FxUserId = @FxUserId {whereSql} --AND fus.Status = 1  ";
            //
            // using (var connection = this.DbConnection)
            // {
            //     result = connection.Query<Shop>(sql, parameters).ToList();
            // }
            // return result;
        }

        /// <summary>
        /// 只获取自己的店铺列表(不包括系统店铺)+缓存性能优化
        /// </summary>
        public List<Shop> GetShopSelf(int fxuserid)
        {
            //Func<int,List<Shop>> funGetShopSelf = (id) => GetShopSelf(id, ""); 
            return FxCaching.GetCache(FxCachingType.FxShopSelf, fxuserid.ToString(),
                () => GetSelfShopsByFxUserIdAndPlatformType(fxuserid));
        }

        public List<Shop> GetShopBySourceType(List<int> sids, List<string> appKeys)
        {
            return GetShopBySourceTypeNew(sids, appKeys);
            if (sids == null || !sids.Any())
                return new List<Shop>();

            var sql = $@"SELECT s.*,se.*
                            FROM dbo.P_Shop s WITH(NOLOCK)
                            LEFT JOIN dbo.P_ShopExtension se WITH(NOLOCK) ON s.Id = se.ShopId
                            WHERE s.Id IN({string.Join(",", sids)}) AND (se.Id IS NULL OR se.AppKey IN ('{string.Join("','", appKeys)}'))";
            var shops = DbConnection.Query<Shop, ShopExtension, Shop>(sql, (s, se) =>
            {
                s.ShopExtension = se;
                return s;
            }).ToList();

            return shops;
        }
        
        public List<Shop> GetShopBySourceTypeNew(List<int> sids, List<string> appKeys)
        {
            if (sids == null || !sids.Any())
                return new List<Shop>();
            var sidsStr = string.Join(",", sids);

            // 先获取店铺信息
            const string shopSql = @"SELECT * FROM dbo.P_Shop WITH(NOLOCK) 
INNER JOIN dbo.FunStringToIntTable(@sids, ',') as si ON si.item = Id";
            var shops = DbConnection.Query<Shop>(shopSql, new { sids = sidsStr }).ToList();

            if (!shops.Any())
                return shops;

            // 再获取扩展信息
            const string extensionSql = @"SELECT * FROM dbo.P_ShopExtension WITH(NOLOCK)
INNER JOIN dbo.FunStringToIntTable(@sids, ',') as si ON si.item = ShopId
INNER JOIN dbo.FunStringToTableV2(@appKeys, ',') as ak ON ak.item = AppKey";
            var extensions = DbConnection
                .Query<ShopExtension>(extensionSql, new { sids = sidsStr, appKeys = string.Join(",", appKeys) }).ToList();

            // 存在一对多的关系
            foreach (var extension in extensions)
            {
                var shop = shops.FirstOrDefault(x => x.Id == extension.ShopId);
                if (shop != null && shop.ShopExtension == null) shop.ShopExtension = extension;
                if (shop?.ShopExtension == null) continue;
                
                var temp = CommUtls.DeepClone(shop);
                temp.ShopExtension = extension;
                shops.Add(temp);
            }

            return shops;
        }

        /// <summary>
        /// 查询店铺P_SyncStatus,P_SyncTask信息
        /// </summary>
        /// <param name="model"></param>
        public List<CheckShopSyncInfoResult> QueryShopSyncInfo(CheckShopSyncInfoModel model)
        {
            return QueryShopSyncInfoNew(model);
            var strCondition = "";
            var strShopCondition = "";
            var parameters = new DynamicParameters();
            parameters.Add("ids",string.Join(",", model.ShopIds));
            parameters.Add("TaskType", model.TaskType);
            var joinFxUserId = string.Empty;
            if (model.InPlatfromTypes != null && model.InPlatfromTypes.Any())
            {
                strCondition += " AND t1.PlatformType IN @PlatformTypes";
                strShopCondition += " AND PlatformType IN @PlatformTypes";
                parameters.Add("PlatformTypes", model.InPlatfromTypes);
            }

            if (model.AgentIds != null && model.AgentIds.Any())
            {
                joinFxUserId = " INNER JOIN dbo.FunStringToIntTable (@fxUserIds,',') t3 ON t3.item = t2.FxUserId ";
                parameters.Add("fxUserIds",string.Join(",", model.AgentIds));
            }

            var sql = $@"SELECT ShopId,FxUserId,'shop' SyncType FROM dbo.P_FxUserShop AS t2 WITH(NOLOCK)
{joinFxUserId}
WHERE 1=1 {strShopCondition}
;
SELECT t2.ShopId,t2.FxUserId,'status' SyncType FROM dbo.P_Shop AS t1 WITH(NOLOCK) 
INNER JOIN dbo.P_SyncStatus AS t2 WITH(NOLOCK) ON t1.Id = t2.ShopId 
INNER JOIN dbo.FunStringToIntTable (@ids,',') t ON t.item = t1.Id
{joinFxUserId}
WHERE t2.SyncType=@TaskType AND t2.Source='FenDanSystem' {strCondition}
;
SELECT t2.ShopId,t2.FxUserId,'task' SyncType FROM dbo.P_Shop AS t1 WITH(NOLOCK) 
INNER JOIN dbo.P_SyncTask AS t2 WITH(NOLOCK) ON t1.Id = t2.ShopId 
INNER JOIN dbo.FunStringToIntTable (@ids,',') t ON t.item = t1.Id
{joinFxUserId}
WHERE t2.TaskType=@TaskType {strCondition} AND t2.Source='FenDanSystem'";


            var result = new List<CheckShopSyncInfoResult>();
            var grid = DbConnection.QueryMultiple(sql, param: parameters);
            result.AddRange(grid.Read<CheckShopSyncInfoResult>().ToList());
            result.AddRange(grid.Read<CheckShopSyncInfoResult>().ToList());
            result.AddRange(grid.Read<CheckShopSyncInfoResult>().ToList());

            return result;
        }

        /// <summary>
        /// 原QueryShopSyncInfo方法，进行SQL拆分
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public List<CheckShopSyncInfoResult> QueryShopSyncInfoNew(CheckShopSyncInfoModel model)
        {
            var result = new List<CheckShopSyncInfoResult>();
            var joinFxUserId = string.Empty;
            var whereSql = string.Empty;
            var parameters = new DynamicParameters();
            parameters.Add("TaskType", model.TaskType);
            parameters.Add("ids", string.Join(",", model.ShopIds));

            if (model.InPlatfromTypes?.Any() == true)
            {
                whereSql = " WHERE PlatformType IN @PlatformTypes";
                parameters.Add("PlatformTypes", model.InPlatfromTypes);
            }

            if (model.AgentIds?.Any() == true)
            {
                joinFxUserId = " INNER JOIN dbo.FunStringToIntTable (@fxUserIds,',') t3 ON t3.item = FxUserId ";
                parameters.Add("fxUserIds",string.Join(",", model.AgentIds));
            }

            // 先验证店铺存在于P_Shop
            var validShopSql = $@"SELECT Id FROM dbo.P_Shop WITH(NOLOCK) 
INNER JOIN dbo.FunStringToIntTable (@ids,',') t ON t.item = Id {whereSql}";

            // 查询 P_FxUserShop
            var shopSql = $"SELECT ShopId, FxUserId FROM dbo.P_FxUserShop WITH(NOLOCK) {joinFxUserId} {whereSql} ";

            var validShopIds = DbConnection.Query<int>(validShopSql, parameters).ToList();
            if (!validShopIds.Any())
                return result;
            parameters.Add("validShopIds", string.Join(",", validShopIds));

            var shopResults = DbConnection.Query<CheckShopSyncInfoResult>(shopSql, parameters).ToList();
            shopResults.ForEach(x => x.SyncType = "shop");
            result.AddRange(shopResults);

            // 查询 P_SyncStatus
            var statusSql = $@"SELECT ShopId, FxUserId FROM dbo.P_SyncStatus WITH(NOLOCK) 
INNER JOIN dbo.FunStringToIntTable (@validShopIds,',') t ON t.item = ShopId 
{joinFxUserId}
WHERE SyncType = @TaskType AND Source = 'FenDanSystem'";
            var statusResults = DbConnection.Query<CheckShopSyncInfoResult>(statusSql, parameters).ToList();
            statusResults.ForEach(x => x.SyncType = "status");
            result.AddRange(statusResults);

            // 查询 P_SyncTask
            var taskSql = $@"SELECT ShopId, FxUserId FROM dbo.P_SyncTask WITH(NOLOCK) 
INNER JOIN dbo.FunStringToIntTable (@validShopIds,',') t ON t.item = ShopId 
{joinFxUserId} 
WHERE TaskType = @TaskType AND Source = 'FenDanSystem'";
            var taskResults = DbConnection.Query<CheckShopSyncInfoResult>(taskSql, parameters).ToList();
            taskResults.ForEach(x => x.SyncType = "task");
            result.AddRange(taskResults);

            return result;
        }
        
        public int ClearFxLoginHistory(string mobile)
        {
            //var sql = $"DELETE FROM P_LoginAuthToken WHERE ShopId IN(SELECT Id FROM P_Shop WITH(NOLOCK) WHERE PlatformType='System' AND ShopId='{mobile}')";
            var sql = $"UPDATE P_LoginAuthToken SET ShopId=-ABS(ShopId),FxUserId=(CASE WHEN FxUserId IS NULL THEN NULL ELSE -ABS(FxUserId) END) WHERE ShopId IN(SELECT Id FROM P_Shop WITH(NOLOCK) WHERE PlatformType='System' AND ShopId=@ShopId)";
            var result = DbConnection.Execute(sql, new { ShopId = mobile });

            //缓存性能优化:直接清除缓存--再次使用时刷新
            var user = new UserFxRepository().GetByMobile(mobile);
            FxCaching.RefeshCache(FxCachingType.UserToken, user == null ? 0 : user.Id);
            return result;
        }

        public int ClearFxLoginHistory2(string mobile)
        {
            int result = 0;
            try
            {
                using (var db = DbConnection)
                {
                    var loginAuthTokenArr = DbConnection.Query<LoginAuthToken>(
                        $"SELECT * FROM P_LoginAuthToken WITH(NOLOCK) WHERE ShopId IN(SELECT Id FROM P_Shop WITH(NOLOCK) WHERE PlatformType='System' AND ShopId=@ShopId)",
                        new { ShopId = mobile });

                    List<int> idList = loginAuthTokenArr.Select(a => a.Id).ToList();
                    if (!idList.Any()) return 0;

                    var usql = $"UPDATE P_LoginAuthToken SET IsExpired=1, ExpiredTime=@ExpiredTime, ShopId=-ABS(ShopId),FxUserId=(CASE WHEN FxUserId IS NULL THEN NULL ELSE -ABS(FxUserId) END) WHERE Id in @ids";
                    var batchSize = 10;
                    var page = Math.Ceiling(idList.Count * 1.0 / batchSize);
                    for (int i = 0; i < page; i++)
                    {
                        var data = idList.Skip(i * batchSize).Take(batchSize).ToList();
                        result += db.Execute(usql, new { ids = data, ExpiredTime = DateTime.Now.AddDays(-1) });
                    }

                    if (result > 0)
                    {
                        foreach (var loginAuthToken in loginAuthTokenArr)
                        {
                            loginAuthToken.FxUserId = -1 * loginAuthToken.FxUserId;
                            loginAuthToken.ShopId = -1 * loginAuthToken.ShopId;
                            loginAuthToken.IsExpired = true;
                            loginAuthToken.ExpiredTime = DateTime.Now.AddDays(-1);
                            FxCaching.RefeshCache(FxCachingType.UserToken, loginAuthToken.Id.ToString(), loginAuthToken);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"清楚登录痕迹报错：{ex.Message} 堆栈：{ex.StackTrace}");
            }

            return result;
        }

        public Shop GetShopAndShopExtension(string ptshopId, string platformType, string appKey = "")
        {
            var sql = @"SELECT * FROM P_Shop AS t1 WITH(NOLOCK) 
LEFT JOIN P_ShopExtension as t2 WITH(NOLOCK) on t1.Id=t2.ShopId --AND (t2.Source IS NULL OR t2.Source = '' OR t2.Source='FendanSystem')
WHERE t1.ShopId = @ptId AND t1.PlatformType = @pt";
            var shops = DbConnection.Query<Shop, ShopExtension, Shop>(sql, (shop, shopExtension) =>
            {
                Shop _shop = shop;
                _shop.ShopExtension = shopExtension;
                return _shop;
            }, new { ptId = ptshopId, pt = platformType });
            return GetShopByFxShopExtension(shops.ToList(), appKey).FirstOrDefault();
        }

        public Shop GetShopAndShopExtension(int id, string platformType, string appKey = "")
        {
            var sql = @"SELECT * FROM P_Shop AS t1 WITH(NOLOCK) 
LEFT JOIN P_ShopExtension as t2 WITH(NOLOCK) on t1.Id=t2.ShopId --AND (t2.Source IS NULL OR t2.Source = '' OR t2.Source='FendanSystem')
WHERE t1.Id = @id AND t1.PlatformType = @pt";
            var shops = DbConnection.Query<Shop, ShopExtension, Shop>(sql, (shop, shopExtension) =>
            {
                Shop _shop = shop;
                _shop.ShopExtension = shopExtension;
                return _shop;
            }, new { id, pt = platformType });
            return GetShopByFxShopExtension(shops.ToList(), appKey).FirstOrDefault();
        }

        /// <summary>
        /// 只获取分单的授权信息
        /// 匹配shop和shopextension
        /// 常规匹配:多条shopextension匹配最后授权的
        /// </summary>
        /// <param name="shopList"></param>
        /// <param name="appKey"></param>
        /// <param name="isProcessPddShopExtension">是否处理拼多多授权扩展，默认true</param>
        /// <returns></returns>
        public List<Shop> GetShopByFxShopExtension(List<Shop> shopList, string appKey = "", bool isProcessPddShopExtension = true)
        {
            return GetShopByFxShopExtensionNew(shopList, appKey, isProcessPddShopExtension);

            #region 旧
            //if (shopList == null || shopList.Count == 0)
            //    return shopList;
            //// 查询头条店铺订购记录
            //var serviceOrders = new List<ServiceAppOrder>();
            //var appOrderLists = new List<AppOrderList>();
            //var toutiaoShops = shopList.Where(x => x.PlatformType == PlatformType.TouTiao.ToString()).ToList();
            //var alibabShops = shopList.Where(x => x.PlatformType == PlatformType.Alibaba.ToString()).ToList();
            //var kuaishouShops = shopList.Where(x => x.PlatformType == PlatformType.KuaiShou.ToString()).ToList();
            //var userRepository = new UserRepository();
            //if (toutiaoShops.Any())
            //{
            //    var shopIds = toutiaoShops.Select(x => x.Id).Distinct().ToList();
            //    var ptShopsIds = toutiaoShops.Select(x => x.ShopId).Distinct().ToList();
            //    var apps = userRepository.GetLastEndOrdersByShopIds(shopIds, ptShopsIds);
            //    serviceOrders.AddRange(apps);
            //}
            //if (alibabShops.Any())
            //{
            //    var ptShopsIds = alibabShops.Select(x => x.ShopId).ToList();
            //    var apps = userRepository.GetLastEndOrdersByPtShopsIds(ptShopsIds, PlatformType.Alibaba.ToString());
            //    serviceOrders.AddRange(apps);

            //    var oldApps = userRepository.GetLastEndAppOrderListByPtShopsIds(ptShopsIds, PlatformType.Alibaba.ToString());
            //    appOrderLists.AddRange(oldApps);
            //}
            //bool isOpenNewCompatibleLogic = false;
            //if (kuaishouShops.Any())
            //{
            //    var shopIds = kuaishouShops.Select(x => x.Id).Distinct().ToList();
            //    var ptUids = kuaishouShops.Select(x => x.Uid).Distinct().ToList();
            //    var apps = userRepository.GetLastEndOrdersWhere(shopIds, ptUids, PlatformType.KuaiShou.ToString());
            //    serviceOrders.AddRange(apps);

            //    isOpenNewCompatibleLogic = new CommonSettingRepository().Get("/System/FenDan/KuaiShou/OpenNewCompatibleLogic", 0)?.Value == "true";
            //}

            //var dicshop = new Dictionary<string, Shop>();
            //var groups = shopList.GroupBy(x => x.Id).ToList();
            //foreach (var g in groups)
            //{
            //    var first = g.FirstOrDefault();
            //    ShopExtension shopEx = null;
            //    if (appKey.IsNotNullOrEmpty())
            //    {
            //        first.ShopExtension = g.FirstOrDefault(x => x.ShopExtension != null && CustomerConfig.FxSystemAppKeyDict.ContainsKey(x.ShopExtension.AppKey) && x.ShopExtension.AppKey == appKey)?.ShopExtension;
            //        if (dicshop.ContainsKey(first.ShopId) == false)
            //            dicshop.Add(first.ShopId, first);
            //        continue;
            //    }

            //    // 头条有2个分单应用，需要确认是否取最新的应用
            //    if (first.PlatformType == PlatformType.TouTiao.ToString())
            //    {
            //        // 分单取订购时间最长的应用
            //        var serviceOrder = serviceOrders.Where(x => x.ServiceAppId != CustomerConfig.TouTiaoAppKey && x.ShopId == first.Id).OrderByDescending(x => x.ServiceEnd).FirstOrDefault();
            //        if (serviceOrder != null)
            //            shopEx = g.FirstOrDefault(x => x.ShopExtension != null && x.ShopExtension.AppKey == serviceOrder.ServiceAppId)?.ShopExtension;
            //        if (shopEx == null)
            //        {
            //            shopEx = g.FirstOrDefault(x => x.ShopExtension != null && x.ShopExtension.AppKey == CustomerConfig.TouTiaoFxNewAppKey)?.ShopExtension;
            //            // 如果没有订购新应用，则取之前的铺货代发应用
            //            if (shopEx == null)
            //                shopEx = g.Where(x => x.ShopExtension != null && CustomerConfig.FxSystemAppKeyDict.ContainsKey(x.ShopExtension.AppKey)).OrderByDescending(x => x.ShopExtension.LastRefreshTokenTime).FirstOrDefault()?.ShopExtension;
            //        }
            //    }
            //    else if (first.PlatformType == PlatformType.Alibaba.ToString())
            //    {
            //        // 分单取订购时间最长的应用
            //        var appOrder = serviceOrders.Where(x => x.PlatformShopId == first.ShopId && x.PlatformType == first.PlatformType).OrderByDescending(x => x.ServiceEnd).FirstOrDefault();
            //        if (appOrder != null)
            //        {
            //            shopEx = g.FirstOrDefault(x => x.ShopExtension != null && x.ShopExtension.AppKey == appOrder.ServiceAppId)?.ShopExtension;

            //            //情况1.轻应用还有时间的话就优先使用轻应用
            //            //情况2.轻应用到期了，打单应用未到期就使用打单应用
            //            //情况3.两个应用都到期了，优先使用轻应用信息
            //            if (appOrder?.ServiceEnd < DateTime.Now)
            //            {
            //                if (appOrderLists != null && appOrderLists.Any())
            //                {
            //                    var oldAppOrder = appOrderLists.Where(x => x.MemberId == first.ShopId && x.AppKey == CustomerConfig.AlibabaAppKey).OrderByDescending(x => x.GmtServiceEnd).FirstOrDefault();
            //                    if (oldAppOrder != null && oldAppOrder.GmtServiceEnd > DateTime.Now)
            //                    {
            //                        shopEx = null;
            //                    }
            //                }
            //            }
            //        }
            //    }
            //    else if (first.PlatformType == PlatformType.KuaiShou.ToString())
            //    {
            //        var oldShopEx = g.Where(x => x.ShopExtension != null).FirstOrDefault(x => x.ShopExtension.AppKey == CustomerConfig.KuaiShouFxAppKey)?.ShopExtension;
            //        var newShopEx = g.Where(x => x.ShopExtension != null).FirstOrDefault(x => x.ShopExtension.AppKey == CustomerConfig.KuaiShouNewFxAppKey)?.ShopExtension;
            //        //【快手】

            //        if (newShopEx?.ExpireTime == null && oldShopEx?.ExpireTime == null)
            //        {
            //            #region 依赖订购记录
            //            ////只有一条记录
            //            //if (g.Where(x => x.ShopExtension != null).Count() <= 1)
            //            //{
            //            //    shopEx = g.Where(x => x.ShopExtension != null).FirstOrDefault()?.ShopExtension;
            //            //}
            //            //else
            //            //{

            //            //}

            //            //---方案①---
            //            //情况1.新应用还有时间的话就优先使用新应用
            //            //情况2.新应用到期了，旧应用未到期就使用旧应用
            //            //---方案②---
            //            //多个应用情况下，应用的时间长就用那个逻辑

            //            //情况3.两个应用都到期了，优先使用新应用信息
            //            //情况4.没有订购记录的情况，优先查询是否有新应用授权，没有就使用旧应用授权

            //            var oldAppOrder = serviceOrders.FirstOrDefault(x => (x.PlatformShopId == first.Uid || x.ShopId == first.Id) && x.ServiceAppId == CustomerConfig.KuaiShouFxAppKey);
            //            var newAppOrder = serviceOrders.FirstOrDefault(x => (x.PlatformShopId == first.Uid || x.ShopId == first.Id) && x.ServiceAppId == CustomerConfig.KuaiShouNewFxAppKey);

            //            ServiceAppOrder appOrder = null;
            //            if (isOpenNewCompatibleLogic)
            //            {
            //                if (newAppOrder != null && newAppOrder.ServiceEnd > DateTime.Now)
            //                    appOrder = newAppOrder;
            //                if (appOrder == null && oldAppOrder != null && oldAppOrder.ServiceEnd > DateTime.Now)
            //                    appOrder = oldAppOrder;
            //            }
            //            else
            //            {
            //                //按产品说法走，以最长时间为准
            //                if (newAppOrder != null && oldAppOrder != null)
            //                {
            //                    if (newAppOrder.ServiceEnd > oldAppOrder.ServiceEnd)
            //                        appOrder = newAppOrder;
            //                    else
            //                        appOrder = oldAppOrder;
            //                }
            //                else if (newAppOrder != null)
            //                {
            //                    appOrder = newAppOrder;
            //                }
            //                else if (oldAppOrder != null)
            //                {
            //                    appOrder = oldAppOrder;
            //                }
            //            }

            //            if (appOrder != null)
            //                shopEx = g.Where(x => x.ShopExtension != null).FirstOrDefault(x => x.ShopExtension.AppKey == appOrder.ServiceAppId)?.ShopExtension;

            //            //if (shopEx == null)
            //            //{
            //            //    var oldShopEx = g.Where(x => x.ShopExtension != null).FirstOrDefault(x => x.ShopExtension.AppKey == CustomerConfig.KuaiShouFxAppKey)?.ShopExtension;
            //            //    var newShopEx = g.Where(x => x.ShopExtension != null).FirstOrDefault(x => x.ShopExtension.AppKey == CustomerConfig.KuaiShouNewFxAppKey)?.ShopExtension;
            //            //    if (newShopEx != null)
            //            //        shopEx = newShopEx;
            //            //    if (shopEx == null)
            //            //        shopEx = oldShopEx;
            //            //}
            //            #endregion
            //        }
            //        else
            //        {
            //            if (isOpenNewCompatibleLogic)
            //            {
            //                if (newShopEx != null && newShopEx.ExpireTime > DateTime.Now)
            //                    shopEx = newShopEx;
            //                if (shopEx == null && oldShopEx != null && oldShopEx.ExpireTime > DateTime.Now)
            //                    shopEx = oldShopEx;
            //            }
            //            else
            //            {
            //                //按产品说法走，以最长时间为准
            //                if (newShopEx != null && oldShopEx != null)
            //                {
            //                    if (newShopEx.ExpireTime > oldShopEx.ExpireTime)
            //                        shopEx = newShopEx;
            //                    else
            //                        shopEx = oldShopEx;
            //                }
            //                else if (newShopEx != null)
            //                {
            //                    shopEx = newShopEx;
            //                }
            //                else if (oldShopEx != null)
            //                {
            //                    shopEx = oldShopEx;
            //                }
            //            }
            //        }

            //        if (shopEx == null)
            //        {
            //            if (newShopEx != null)
            //                shopEx = newShopEx;
            //            if (shopEx == null)
            //                shopEx = oldShopEx;
            //        }
            //    }
            //    else if (first.PlatformType == PlatformType.AlibabaZhuKe.ToString())
            //    {
            //        // 主客授权 只获取阿里主客
            //        shopEx = g.FirstOrDefault(x => x.ShopExtension != null && x.ShopExtension.AppKey == CustomerConfig.AlibabaZkphAppKey)?.ShopExtension;
            //    }
            //    else
            //    {
            //        shopEx = g.Where(x => x.ShopExtension != null && CustomerConfig.FxSystemAppKeyDict.ContainsKey(x.ShopExtension.AppKey)).FirstOrDefault(x => x.ShopExtension.AppKey == CustomerConfig.KuaiShouFxAppKey || x.ShopExtension.AppKey == CustomerConfig.PddFxAppKey)?.ShopExtension;
            //        if (shopEx == null)
            //            shopEx = g.Where(x => x.ShopExtension != null && CustomerConfig.FxSystemAppKeyDict.ContainsKey(x.ShopExtension.AppKey)).OrderByDescending(x => x.ShopExtension.LastRefreshTokenTime).FirstOrDefault()?.ShopExtension;
            //    }
            //    first.ShopExtension = shopEx;
            //    if (dicshop.ContainsKey(first.ShopId) == false)
            //        dicshop.Add(first.ShopId, first);
            //}
            //var result = dicshop.Values.ToList();
            //return result; 
            #endregion
        }

        public List<Shop> GetShopByFxShopExtensionNew(List<Shop> shopList, string appKey = "", bool isProcessPddShopExtension = true)
        {
            if (shopList == null || shopList.Count == 0)
                return shopList;

            if (_scene == PlatformAppScene.listing)
            {
                //铺货场景：只取铺货应用相关的店铺
                shopList = shopList.Where(s => s.ShopExtension != null && CustomerConfig.FxListingAppKeys.Contains(s.ShopExtension.AppKey)).ToList();
            }
            else if (_scene != PlatformAppScene.all)
            {
                //常规场景：过滤铺货应用相关的店铺
                shopList = shopList.Where(s => s.ShopExtension == null || (s.ShopExtension != null && CustomerConfig.FxListingAppKeys.Contains(s.ShopExtension.AppKey) == false)).ToList();
            }


            // 查询头条店铺订购记录
            var serviceOrders = new List<ServiceAppOrder>();
            var appOrderLists = new List<AppOrderList>();
            var toutiaoShops = shopList.Where(x => x.PlatformType == PlatformType.TouTiao.ToString() || x.PlatformType == PlatformType.TouTiaoSaleShop.ToString()).ToList();
            var alibabShops = shopList.Where(x => x.PlatformType == PlatformType.Alibaba.ToString()).ToList();
            var kuaishouShops = shopList.Where(x => x.PlatformType == PlatformType.KuaiShou.ToString()).ToList();
            var userRepository = new UserRepository();
            if (toutiaoShops.Any() && _scene != PlatformAppScene.listing)
            {
                var shopIds = toutiaoShops.Select(x => x.Id).Distinct().ToList();
                var ptShopsIds = toutiaoShops.Select(x => x.ShopId).Distinct().ToList();
                var apps = userRepository.GetLastEndOrdersByShopIds(shopIds, ptShopsIds);
                serviceOrders.AddRange(apps);
            }
            if (alibabShops.Any())
            {
                //var ptShopsIds = alibabShops.Select(x => x.ShopId).ToList();
                //var apps = userRepository.GetLastEndOrdersByPtShopsIds(ptShopsIds, PlatformType.Alibaba.ToString());
                //serviceOrders.AddRange(apps);

                //var oldApps = userRepository.GetLastEndAppOrderListByPtShopsIds(ptShopsIds, PlatformType.Alibaba.ToString());
                //appOrderLists.AddRange(oldApps);
            }
            bool isOpenNewCompatibleLogic = false;
            if (kuaishouShops.Any())
            {
                var shopIds = kuaishouShops.Select(x => x.Id).Distinct().ToList();
                var ptUids = kuaishouShops.Select(x => x.Uid).Distinct().ToList();
                var apps = userRepository.GetLastEndOrdersWhere(shopIds, ptUids, PlatformType.KuaiShou.ToString());
                serviceOrders.AddRange(apps);

                isOpenNewCompatibleLogic = new CommonSettingRepository().Get("/System/FenDan/KuaiShou/OpenNewCompatibleLogic", 0)?.Value == "true";
            }

            var dicshop = new Dictionary<string, Shop>();
            var groups = shopList.GroupBy(x => x.Id).ToList();
            foreach (var g in groups)
            {
                var first = g.FirstOrDefault();
                Shop needfulShop = null; //正确需要的授权和店铺信息
                if (appKey.IsNotNullOrEmpty())
                {
                    first.ShopExtension = g.FirstOrDefault(x => x.ShopExtension != null && CustomerConfig.FxSystemAppKeyDict.ContainsKey(x.ShopExtension.AppKey) && x.ShopExtension.AppKey == appKey)?.ShopExtension;
                    if (dicshop.ContainsKey(first.PlatformType + first.ShopId) == false)
                        dicshop.Add(first.PlatformType + first.ShopId, first);
                    continue;
                }

                // 头条有2个分单应用，需要确认是否取最新的应用
                //增加分单铺货应用兼容 2024.07.08
                if (first.PlatformType == PlatformType.TouTiao.ToString())
                {
                    if (_scene == PlatformAppScene.listing)
                    {
                        needfulShop = g.FirstOrDefault(x => x.ShopExtension != null && x.ShopExtension.AppKey == CustomerConfig.TouTiaoFxListingAppKey);
                    }
                    else
                    {
                        // 分单取订购时间最长的应用
                        var serviceOrder = serviceOrders.Where(x => x.ServiceAppId != CustomerConfig.TouTiaoAppKey && x.ServiceAppId != CustomerConfig.TouTiaoFxListingAppKey && x.ShopId == first.Id).OrderByDescending(x => x.ServiceEnd).FirstOrDefault();
                        if (serviceOrder != null)
                            needfulShop = g.FirstOrDefault(x => x.ShopExtension != null && x.ShopExtension.AppKey == serviceOrder.ServiceAppId);
                        if (needfulShop?.ShopExtension == null)
                        {
                            needfulShop = g.FirstOrDefault(x => x.ShopExtension != null && x.ShopExtension.AppKey == CustomerConfig.TouTiaoFxNewAppKey);
                            // 如果没有订购新应用，则取之前的铺货代发应用
                            if (needfulShop?.ShopExtension == null)
                                needfulShop = g.Where(x => x.ShopExtension != null && CustomerConfig.FxSystemAppKeyDict.ContainsKey(x.ShopExtension.AppKey)).OrderByDescending(x => x.ShopExtension.LastRefreshTokenTime).FirstOrDefault();
                        }
                    }
                }
                else if (first.PlatformType == PlatformType.Alibaba.ToString())
                {
                    #region 依赖订购记录
                    // 分单取订购时间最长的应用
                    //var appOrder = serviceOrders.Where(x => x.PlatformShopId == first.ShopId && x.PlatformType == first.PlatformType).OrderByDescending(x => x.ServiceEnd).FirstOrDefault();
                    //if (appOrder != null)
                    //{
                    //    shopEx = g.FirstOrDefault(x => x.ShopExtension != null && x.ShopExtension.AppKey == appOrder.ServiceAppId)?.ShopExtension;

                    //    //情况1.轻应用还有时间的话就优先使用轻应用
                    //    //情况2.轻应用到期了，打单应用未到期就使用打单应用
                    //    //情况3.两个应用都到期了，优先使用轻应用信息
                    //    if (appOrder?.ServiceEnd < DateTime.Now)
                    //    {
                    //        if (appOrderLists != null && appOrderLists.Any())
                    //        {
                    //            var oldAppOrder = appOrderLists.Where(x => x.MemberId == first.ShopId && x.AppKey == CustomerConfig.AlibabaAppKey).OrderByDescending(x => x.GmtServiceEnd).FirstOrDefault();
                    //            if (oldAppOrder != null && oldAppOrder.GmtServiceEnd > DateTime.Now)
                    //            {
                    //                shopEx = null;
                    //            }
                    //        }
                    //    }
                    //} 
                    #endregion

                    needfulShop = g.FirstOrDefault(x => x.ShopExtension != null && x.ShopExtension.AppKey == CustomerConfig.AlibabaQingAppKey);
                    if (needfulShop?.ShopExtension != null && needfulShop?.ShopExtension?.ExpireTime < DateTime.Now)
                    {
                        needfulShop.ShopExtension = null;
                    }
                }
                else if (first.PlatformType == PlatformType.KuaiShou.ToString())
                {
                    var oldShopEx = g.Where(x => x.ShopExtension != null).FirstOrDefault(x => x.ShopExtension.AppKey == CustomerConfig.KuaiShouFxAppKey);
                    var newShopEx = g.Where(x => x.ShopExtension != null).FirstOrDefault(x => x.ShopExtension.AppKey == CustomerConfig.KuaiShouNewFxAppKey);
                    //【快手】
                    //1. ShopExtension?.ExpireTime 其中一个没值，就依赖订购记录
                    //2. 都有值就按ExpireTime 优先选择时间长/新应用
                    //3. ShopExtension，新旧都没授权那就只能用P_shop的授权信息为准
                    if ((oldShopEx != null && newShopEx != null) && (newShopEx?.ShopExtension?.ExpireTime == null || oldShopEx?.ShopExtension?.ExpireTime == null))
                    {
                        #region 依赖订购记录
                        ////只有一条记录
                        //if (g.Where(x => x.ShopExtension != null).Count() <= 1)
                        //{
                        //    shopEx = g.Where(x => x.ShopExtension != null).FirstOrDefault()?.ShopExtension;
                        //}
                        //else
                        //{

                        //}

                        //---方案①---
                        //情况1.新应用还有时间的话就优先使用新应用
                        //情况2.新应用到期了，旧应用未到期就使用旧应用
                        //---方案②---
                        //多个应用情况下，应用的时间长就用那个逻辑

                        //情况3.两个应用都到期了，优先使用新应用信息
                        //情况4.没有订购记录的情况，优先查询是否有新应用授权，没有就使用旧应用授权

                        var oldAppOrder = serviceOrders.FirstOrDefault(x => (x.PlatformShopId == first.Uid || x.ShopId == first.Id) && x.ServiceAppId == CustomerConfig.KuaiShouFxAppKey);
                        var newAppOrder = serviceOrders.FirstOrDefault(x => (x.PlatformShopId == first.Uid || x.ShopId == first.Id) && x.ServiceAppId == CustomerConfig.KuaiShouNewFxAppKey);

                        ServiceAppOrder appOrder = null;
                        if (isOpenNewCompatibleLogic)
                        {
                            if (newAppOrder != null && newAppOrder.ServiceEnd > DateTime.Now)
                                appOrder = newAppOrder;
                            if (appOrder == null && oldAppOrder != null && oldAppOrder.ServiceEnd > DateTime.Now)
                                appOrder = oldAppOrder;
                        }
                        else
                        {
                            //按产品说法走，以最长时间为准
                            if (newAppOrder != null && oldAppOrder != null)
                            {
                                if (newAppOrder.ServiceEnd > oldAppOrder.ServiceEnd)
                                    appOrder = newAppOrder;
                                else
                                    appOrder = oldAppOrder;
                            }
                            else if (newAppOrder != null)
                            {
                                appOrder = newAppOrder;
                            }
                            else if (oldAppOrder != null)
                            {
                                appOrder = oldAppOrder;
                            }
                        }

                        if (appOrder != null)
                        {
                            needfulShop = g.Where(x => x.ShopExtension != null).FirstOrDefault(x => x.ShopExtension.AppKey == appOrder.ServiceAppId);
                            if (needfulShop != null)
                                needfulShop.ShopExtension.ExpireTime = appOrder.ServiceEnd.Value;
                        }

                        //if (shopEx == null)
                        //{
                        //    var oldShopEx = g.Where(x => x.ShopExtension != null).FirstOrDefault(x => x.ShopExtension.AppKey == CustomerConfig.KuaiShouFxAppKey)?.ShopExtension;
                        //    var newShopEx = g.Where(x => x.ShopExtension != null).FirstOrDefault(x => x.ShopExtension.AppKey == CustomerConfig.KuaiShouNewFxAppKey)?.ShopExtension;
                        //    if (newShopEx != null)
                        //        shopEx = newShopEx;
                        //    if (shopEx == null)
                        //        shopEx = oldShopEx;
                        //}
                        #endregion
                    }
                    else
                    {
                        if (isOpenNewCompatibleLogic)
                        {
                            //新应用有效就优先
                            if (newShopEx?.ShopExtension != null && newShopEx?.ShopExtension?.ExpireTime > DateTime.Now)
                                needfulShop = newShopEx;
                            if (needfulShop == null && needfulShop?.ShopExtension != null && oldShopEx?.ShopExtension?.ExpireTime > DateTime.Now)
                                needfulShop = oldShopEx;
                        }
                        else
                        {
                            //按产品说法走，以最长时间为准
                            if (newShopEx?.ShopExtension != null && oldShopEx?.ShopExtension != null)
                            {
                                if (newShopEx.ShopExtension.ExpireTime != null && oldShopEx.ShopExtension.ExpireTime != null)
                                {
                                    if (newShopEx.ShopExtension.ExpireTime > oldShopEx.ShopExtension.ExpireTime)
                                        needfulShop = newShopEx;
                                    else
                                        needfulShop = oldShopEx;
                                }
                                else
                                {
                                    //过期时间其中有为空的话按刷新授权时间最近的为准
                                    if (newShopEx.ShopExtension.LastRefreshExpireTime > oldShopEx.ShopExtension.LastRefreshExpireTime)
                                        needfulShop = newShopEx;
                                    else
                                        needfulShop = oldShopEx;
                                }
                            }
                            else if (newShopEx?.ShopExtension != null)
                            {
                                needfulShop = newShopEx;
                            }
                            else if (oldShopEx?.ShopExtension != null)
                            {
                                needfulShop = oldShopEx;
                            }
                        }
                    }

                    if (needfulShop == null)
                    {
                        if (newShopEx != null)
                            needfulShop = newShopEx;
                        if (needfulShop == null)
                            needfulShop = oldShopEx;
                    }
                }
                else if (first.PlatformType == PlatformType.AlibabaZhuKe.ToString())
                {
                    // 主客授权 只获取阿里主客
                    needfulShop = g.FirstOrDefault(x => x.ShopExtension != null && x.ShopExtension.AppKey == CustomerConfig.AlibabaZkphAppKey);
                }
                else if (first.PlatformType == PlatformType.Pinduoduo.ToString())
                {
                    //优先取分单应用授权
                    needfulShop = g.FirstOrDefault(x => x.ShopExtension != null && x.ShopExtension.AppKey == CustomerConfig.PddFxAppKey);
                    //没取到分单应用，就取第一个（这种情况说明只有打单应用）
                    if (needfulShop == null)
                        needfulShop = g.FirstOrDefault();
                    //取到了分单应用，且该店铺有使用打单应用，需要根据订购到期时间判断，优先使用订购时间较长的应用
                    else if (needfulShop.SystemVersion.ToString2() == "ForFxSystem" && isProcessPddShopExtension)
                    {
                        //兼容拼多多打单应用-过期时间大的为准 2024.06.18
                        //if (needfulShop.ShopExtension.ExpireTime != null && needfulShop.ExpireTime != null && needfulShop.ShopExtension.ExpireTime.Value < needfulShop.ExpireTime.Value)
                        //{
                        //    needfulShop.ShopExtension = null;
                        //    needfulShop.AppKey = "";
                        //    needfulShop.AppSecret = "";
                        //}

                        //情况1.分单还有时间的话就优先使用分单
                        //情况2.分单到期了，打单应用未到期就使用打单应用
                        //情况3.两个应用都到期了，优先使用分单
                        if (needfulShop.ShopExtension.ExpireTime != null && needfulShop.ExpireTime != null)
                        {
                            if (needfulShop.ShopExtension.ExpireTime.Value <= DateTime.Now && needfulShop.ExpireTime.Value > DateTime.Now)
                            {
                                needfulShop.ShopExtension = null;
                                needfulShop.AppKey = "";
                                needfulShop.AppSecret = "";
                            }
						}
                    }
                }
                else if (first.PlatformType == PlatformType.Jingdong.ToString())
                {
                    //优先取分单应用授权
                    needfulShop = g.FirstOrDefault(x => x.ShopExtension != null && x.ShopExtension.AppKey == CustomerConfig.JingDongFxAppKey);
                    //没取到分单应用，就取第一个（这种情况说明只授权打单应用）
                    if (needfulShop == null)
                        needfulShop = g.FirstOrDefault();
                    //取到了分单应用，且该店铺又使用了打单应用授权过，需要根据订购到期时间判断，优先使用订购时间较长的应用
                    else if (string.IsNullOrEmpty(needfulShop.SystemVersion))
                    {
                        //if (needfulShop.ShopExtension.ExpireTime != null && needfulShop.ExpireTime != null && needfulShop.ShopExtension.ExpireTime.Value < needfulShop.ExpireTime.Value)
                        //{
                        //    var fxExpireTime = needfulShop.ShopExtension.ExpireTime;
                        //    //使用打单应用授权信息
                        //    needfulShop.ShopExtension = new ShopExtension()
                        //    {
                        //        ShopId = needfulShop.Id,
                        //        AppKey = CustomerConfig.JingDongAppKey,
                        //        AppSecret = CustomerConfig.JingDongAppSecret,
                        //        AccessToken = needfulShop.AccessToken,
                        //        RefreshToken = needfulShop.RefreshToken,
                        //        LastRefreshTokenTime = needfulShop.LastRefreshTokenTime,
                        //        ExpireTime = needfulShop.ExpireTime
                        //    }; 
                        //    needfulShop.AppKey = CustomerConfig.JingDongAppKey;
                        //    needfulShop.AppSecret = CustomerConfig.JingDongAppSecret;
                        //    needfulShop.ExpireTime = fxExpireTime;
                        //}

                        //情况1.分单还有时间的话就优先使用分单
                        //情况2.分单到期了，打单应用未到期就使用打单应用
                        //情况3.两个应用都到期了，优先使用分单
                        if (needfulShop.ShopExtension.ExpireTime != null && needfulShop.ExpireTime != null)
                        {
                            if (needfulShop.ShopExtension.ExpireTime.Value <= DateTime.Now && needfulShop.ExpireTime.Value > DateTime.Now)
                            {
                                var fxExpireTime = needfulShop.ShopExtension.ExpireTime;
								//使用打单应用授权信息
								needfulShop.ShopExtension = new ShopExtension()
								{
									ShopId = needfulShop.Id,
									AppKey = CustomerConfig.JingDongAppKey,
									AppSecret = CustomerConfig.JingDongAppSecret,
									AccessToken = needfulShop.AccessToken,
									RefreshToken = needfulShop.RefreshToken,
									LastRefreshTokenTime = needfulShop.LastRefreshTokenTime,
									ExpireTime = needfulShop.ExpireTime
								};
								needfulShop.AppKey = CustomerConfig.JingDongAppKey;
								needfulShop.AppSecret = CustomerConfig.JingDongAppSecret;
								needfulShop.ExpireTime = fxExpireTime;
							}
                        }

                    }
                }
                else if (first.PlatformType == PlatformType.WxVideo.ToString())
                {
                    var oldShopEx = g.Where(x => x.ShopExtension != null).FirstOrDefault(x => x.ShopExtension.AppKey == CustomerConfig.Fx_WxComponentNewAppId);
                    var newShopEx = g.Where(x => x.ShopExtension != null).FirstOrDefault(x => x.ShopExtension.AppKey == CustomerConfig.Fx_WxShopNewAppId); 
                    
                    // 多应用情况下，以最长时间为准
                    if (newShopEx?.ShopExtension != null && oldShopEx?.ShopExtension != null)
                    {
                        if(newShopEx.ShopExtension.ExpireTime != null && oldShopEx.ShopExtension.ExpireTime != null)
                        {
                            needfulShop = newShopEx.ShopExtension.ExpireTime > oldShopEx.ShopExtension.ExpireTime
                                ? newShopEx
                                : oldShopEx;
                        }
                        else
                        {
                            //过期时间其中有为空的话按刷新授权时间最近的为准
                            needfulShop = newShopEx.ShopExtension.LastRefreshExpireTime > oldShopEx.ShopExtension.LastRefreshExpireTime 
                                ? newShopEx
                                : oldShopEx;
                        }
                    }
                    else if (newShopEx?.ShopExtension != null)
                    {
                        needfulShop = newShopEx;
                    }
                    else if (oldShopEx?.ShopExtension != null)
                    {
                        needfulShop = oldShopEx;
                    }
                }
                else
                {
                    needfulShop = g.Where(x => x.ShopExtension != null && CustomerConfig.FxSystemAppKeyDict.ContainsKey(x.ShopExtension.AppKey)).FirstOrDefault(x => x.ShopExtension.AppKey == CustomerConfig.KuaiShouFxAppKey || x.ShopExtension.AppKey == CustomerConfig.PddFxAppKey);
                    if (needfulShop == null)
                        needfulShop = g.Where(x => x.ShopExtension != null && CustomerConfig.FxSystemAppKeyDict.ContainsKey(x.ShopExtension.AppKey)).OrderByDescending(x => x.ShopExtension.LastRefreshTokenTime).FirstOrDefault();
                }
                if (needfulShop == null)
                    needfulShop = first;
                if (dicshop.ContainsKey(needfulShop.PlatformType + needfulShop.ShopId) == false)
                    dicshop.Add(needfulShop.PlatformType + needfulShop.ShopId, needfulShop);
            }
            var result = dicshop.Values.ToList();
            return result;
        }

        /// <summary>
        /// 返回Shop和对应所有的ShopExtension
        /// </summary>
        /// <param name="shopid"></param>
        /// <returns></returns>
        public Tuple<Shop, List<ShopExtension>> GetShopAndAllShopExtension(int shopid)
        {
            string sql = @" Select * From P_Shop WITH(NOLOCK)  Where id = @sid
                            Select * From P_ShopExtension WITH(NOLOCK) Where ShopId = @sid ";
            var grid = DbConnection.QueryMultiple(sql, new { sid = shopid });
            var shops = grid.Read<Shop>().FirstOrDefault();
            var extentions = grid.Read<ShopExtension>()?.ToList();
            return new Tuple<Shop, List<ShopExtension>>(shops, extentions);
        }

        /// <summary>
        /// 获取平台应用有效订购记录（付款）
        /// </summary>
        /// <param name="shopid"></param>
        /// <param name="appkey"></param>
        /// <returns></returns>
        public List<ServiceAppOrder> GetShopValidAppService(int shopid, string appkey)
        {
            string sql = @" Select * From ServiceAppOrder With(NoLock) 
                            Where ShopId = @sid And ServiceAppId = @appid And PayStatus = 'TRADE_SUCCESS'";
            return DbConnection.Query<ServiceAppOrder>(sql, new { sid = shopid, appid = appkey })?.ToList();
        }

        public List<int> GetFxSystemShopIdByFxId(List<int> userfxIds)
        {
            var shopids = DbConnection.Query<int>("SELECT s.Id FROM dbo.P_Shop s WITH(NOLOCK) INNER JOIN P_FxUserShop t2 WITH(NOLOCK) ON s.Id = t2.ShopId WHERE t2.PlatformType='System' AND t2.FxUserId in@userfxId", new { userfxId = userfxIds })?.ToList();
            return shopids;
        }

        /// <summary>
        /// 通过ShopId获取FxUserId
        /// </summary>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public int GetFxUserIdByShopId(int shopId,bool useCache = true)
        {
            //缓存性能优化:通过店铺ID获得用户id
            return useCache
                ? FxCaching.GetCache(FxCachingType.FxUserIdByShopId, shopId.ToString(),
                    () => GetMyFxUserIdByShopId(shopId))
                : GetMyFxUserIdByShopId(shopId);
        }

        /// <summary>
        /// 通过主客铺户店铺id得到FxuserID
        /// </summary>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public int GetFxUserIdByZhuKeShopId(int shopId)
        {
            var fxUserId = DbConnection.Query<int>($@" select TOP 1 ls.OwnerShopId FROM ListingOwnerShop ls WITH(NOLOCK) WHERE ls.ShopId =@shopId", new { shopId = shopId }).FirstOrDefault();
            return fxUserId;
        }

        private int GetMyFxUserIdByShopId(int shopId)
        {
            var fxUserId = DbConnection.Query<int>("SELECT TOP 1 FxUserId FROM P_FxUserShop WITH(NOLOCK) WHERE ShopId=@shopId;", new { shopId = shopId }).FirstOrDefault();
            return fxUserId;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="maxId"></param>
        /// <param name="topNum"></param>
        /// <returns></returns>
        public List<PhShop> GetListByMaxId(int maxId, int topNum)
        {
            var list = DbConnection.Query<PhShop>($"SELECT TOP {topNum} Id AS ShopId,ShopName,PlatformType FROM P_Shop WITH(NOLOCK) WHERE Id>@maxId ORDER BY Id ASC;", new { maxId = maxId }).ToList();
            return list;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="shopIds"></param>
        /// <returns></returns>
        public List<Shop> GetListByShopIds(List<int> shopIds)
        {
            var list = DbConnection
                .Query<Shop>($"SELECT Id,PlatformType,NickName,ShopName FROM P_Shop WITH(NOLOCK) WHERE Id IN @shopIds;",
                    new { shopIds = shopIds }).ToList();
            return list;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="shopIds"></param>
        /// <returns></returns>
        public List<Shop> GetFxShopListByShopIds(List<int> shopIds)
        {
            var list = DbConnection
                .Query<Shop>($"SELECT a.Id,a.NickName,b.NickName ShopName,b.FxUserId AS FxUserIds FROM P_Shop AS a WITH(NOLOCK) INNER JOIN P_FxUserShop AS b WITH(NOLOCK) ON a.Id = b.ShopId WHERE a.Id IN @shopIds;",
                    new { shopIds = shopIds }).ToList();
            return list;
        }

        /// <summary>
        /// 跨境店铺关系表
        /// </summary>
        /// <param name="shopIds"></param>
        /// <returns></returns>
        public List<Shop> GetCrossBorderFxShopListByShopIds(List<int> shopIds)
        {
            var list = DbConnection
                .Query<Shop>($"SELECT a.Id,a.NickName,a.ShopName,b.FxUserId AS FxUserIds FROM P_Shop AS a WITH(NOLOCK) INNER JOIN P_FxUserForeignShop AS b WITH(NOLOCK) ON a.Id = b.ShopId WHERE a.Id IN @shopIds;",
                    new { shopIds = shopIds }).ToList();
            return list;
        }

        /// <summary>
        /// 跨境查全球店铺Id
        /// </summary>
        /// <param name="shopIds"></param>
        /// <returns></returns>
        public Shop GetGlobalShop(string shopId)
        {
            var shop = DbConnection
                .QueryFirstOrDefault<Shop>($"SELECT * FROM P_Shop AS s WITH(NOLOCK) WHERE s.ShopId =@ShopId;",
                    new { ShopId = shopId });
            return shop;
        }



        /// <summary>
        /// 查出用户Id+店铺Id
        /// </summary>
        /// <param name="pageSize"></param>
        /// <param name="pageIndex"></param>
        /// <param name="platformType"></param>
        /// <returns></returns>
        public List<FxUserIdAndShopId> GetFxUserIdAndShopId(int pageSize, int pageIndex, string platformType, int maxId)
        {
            var sql = $@"SELECT fus.FxUserId,s.Id AS ShopId FROM P_FxUserShop fus WITH(NOLOCK) 
INNER JOIN P_Shop s WITH(NOLOCK) ON s.Id =fus.ShopId
WHERE s.PlatformType ='{platformType}' AND s.Id>{maxId}
ORDER BY s.Id OFFSET {pageSize * (pageIndex - 1)} ROWS FETCH NEXT {pageSize} ROWS ONLY";
            return DbConnection.Query<FxUserIdAndShopId>(sql).ToList();
        }

        /// <summary>
        /// 查出用户Id+所在源库Id（表WaitMigrateTouTiaoShop）
        /// </summary>
        /// <param name="pageSize"></param>
        /// <param name="pageIndex"></param>
        /// <param name="maxId"></param>
        /// <returns></returns>
        public List<FxUserIdAndShopId> GetFxUserIdInWaitMigrateTouTiaoShop(int pageSize, int pageIndex, int maxId)
        {
            var sql = $@"SELECT FxUserId,SourceDbNameConfigId AS ShopId FROM(
SELECT FxUserId,MAX(SourceDbNameConfigId) AS SourceDbNameConfigId FROM WaitMigrateTouTiaoShop WITH(NOLOCK) WHERE FxUserId>{maxId} GROUP BY FxUserId
) AS t
ORDER BY FxUserId ASC OFFSET {pageSize * (pageIndex - 1)} ROWS FETCH NEXT {pageSize} ROWS ONLY";
            return DbConnection.Query<FxUserIdAndShopId>(sql).ToList();
        }

        public new int Add(Shop shop)
        {
            var result = base.Add(shop);
            //缓存性能优化:直接清除缓存--再次使用时刷新
            FxCaching.RefeshShopCaching(shop.Id);

            return result;
        }

        /// <summary>
        ///  查询是否开通应用的店铺
        /// </summary>
        /// <returns></returns>
        public bool GetOpenAppShops(int fxUserId, string platformType, List<string> appkeys)
        {
            var parameters = new DynamicParameters();

            string sql = $@" SELECT TOP 1 1 FROM dbo.P_FxUserShop us WITH(NOLOCK)
                            INNER JOIN dbo.P_ShopExtension se WITH(NOLOCK) ON se.ShopId = us.ShopId AND se.Source = 'FenDanSystem' AND se.AppKey in @AppKey
                            WHERE  us.FxUserId = @FxUserId AND us.PlatformType = @PlatformType  " ;

            parameters.Add("FxUserId", fxUserId);
            parameters.Add("PlatformType", platformType);
            parameters.Add("AppKey", appkeys);
            var isOpen = DbConnection.QueryFirstOrDefault<bool>(sql, parameters);
            return isOpen;
        }


        /// <summary>
        ///  查询是否开通老应用
        /// </summary>
        /// <returns></returns>
        public bool GetIsAppOrderList(int fxUserId, string platformType, string appkeys)
        {
            //var parameters = new DynamicParameters();

            //string sql = $@" SELECT TOP 1 1  from AppOrderList ao WITH(NOLOCK) 
            //                INNER JOIN  dbo.P_Shop sp WITH(NOLOCK) ON ao.MemberId = sp.ShopId
            //                INNER JOIN dbo.P_FxUserShop us WITH(NOLOCK)  ON sp.Id = us.ShopId
            //                WHERE ao.AppKey = @AppKey 
            //                AND ao.PlatformType = @PlatformType 
            //                AND us.FxUserId = @FxUserId  ";

            //parameters.Add("FxUserId", fxUserId);
            //parameters.Add("PlatformType", platformType);
            //parameters.Add("AppKey", appkeys);
            //var isOpen = DbConnection.QueryFirstOrDefault<bool>(sql, parameters);
            //return isOpen;

            var sqlfxUserShop = $@"SELECT us.ShopId FROM dbo.P_FxUserShop us WITH(NOLOCK) WHERE  us.FxUserId = @FxUserId";
            var sqlfxUserShopParameters = new DynamicParameters();
            sqlfxUserShopParameters.Add("FxUserId", fxUserId);
            var shopIds = DbConnection.Query<int>(sqlfxUserShop,sqlfxUserShopParameters).ToList();
            if (!shopIds.Any())
                return false;

            // 使用SqlOptimizationHandler处理shopIds参数过多的情况
            const string sqlShopByIn = "SELECT sp.ShopId FROM dbo.P_Shop sp WITH(NOLOCK) WHERE sp.Id IN @shopIds";
            const string sqlShopByTableFun = @"SELECT sp.ShopId FROM dbo.P_Shop sp WITH(NOLOCK) 
                                             INNER JOIN FunStringToIntTable(@shopIds,',') AS t ON t.item = sp.Id";
            var memberIds = SqlOptimizationHandler.QueryEntities(shopIds, sqlShopByIn, sqlShopByTableFun,
                (sql, param) => DbConnection.Query<string>(sql, param).ToList(), "shopIds");

            if (!memberIds.Any())
                return false;

            // 使用SqlOptimizationHandler处理memberIds参数过多的情况
            const string sqlAppOrderListByIn = @"SELECT TOP 1 1 FROM AppOrderList ao WITH(NOLOCK) 
                                               WHERE ao.AppKey = @AppKey AND ao.PlatformType = @PlatformType AND ao.MemberId IN @memberIds";
            const string sqlAppOrderListByTableFun = @"SELECT TOP 1 1 FROM AppOrderList ao WITH(NOLOCK) 
                                                     INNER JOIN FunStringToTable(@memberIds,',') AS t ON t.item = ao.MemberId
                                                     WHERE ao.AppKey = @AppKey AND ao.PlatformType = @PlatformType";

            var result = SqlOptimizationHandler.QueryEntities(memberIds, sqlAppOrderListByIn, sqlAppOrderListByTableFun,
                (sql, param) =>
                {
                    param.Add("AppKey", appkeys);
                    param.Add("PlatformType", platformType);
                    return DbConnection.QueryFirstOrDefault<bool>(sql, param) ? new List<bool> { true } : new List<bool>();
                }, "memberIds");

            return result.Any();
        }

        /// <summary>
        ///  查询是否有对应的应用店铺授权
        /// </summary>
        /// <returns></returns>
        public bool GetAppShopAuth(int fxUserId, int ShopId, List<string> appkeys)
        {
            var parameters = new DynamicParameters();

            string sql = $@" SELECT TOP 1 1 FROM dbo.P_FxUserShop us WITH(NOLOCK)
                            INNER JOIN dbo.P_ShopExtension se WITH(NOLOCK) ON se.ShopId = us.ShopId AND se.Source = 'FenDanSystem' AND se.AppKey in @AppKey
                            WHERE  us.FxUserId = @FxUserId AND us.ShopId = @ShopId ";

            parameters.Add("@FxUserId", fxUserId);
            parameters.Add("@ShopId", ShopId);
            parameters.Add("@AppKey", appkeys);
            var isOpen = DbConnection.QueryFirstOrDefault<bool>(sql, parameters);
            return isOpen;
        }
        /// <summary>
        /// 获取阿里店铺开通电子面单情况
        /// </summary>
        /// <param name="loginFxShopId"></param>
        /// <param name="platformType"></param>
        /// <returns></returns>
        public List<AliEbillOpenStatus> GetAliOpenEbillInfo(int loginFxShopId, string platformType)
        {
          return ExecGetAliOpenEbillInfo(loginFxShopId, platformType, "Fx_GetAliOpenEbillInfoV2");
        }

        private List<AliEbillOpenStatus> ExecGetAliOpenEbillInfo(int loginFxShopId, string platformType,string ProcedureName)
        {
            var db = DbConnection;
            var parameters = new DynamicParameters();
            parameters.Add("loginFxShopId", loginFxShopId);
            parameters.Add("platformType", platformType);
            var rst = db.Query<AliEbillOpenStatus>($"dbo.[{ProcedureName}]", parameters, commandType: CommandType.StoredProcedure);
            return rst.ToList();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="fxUserId">分单用户ID</param>
        /// <returns></returns>
        public List<Shop> GetAlibabaQingShopsByFxUserId(int fxUserId)
        {
            string sql = $@" SELECT sp.*,se.* from dbo.P_Shop sp WITH(NOLOCK) 
                            INNER JOIN dbo.P_FxUserShop us WITH(NOLOCK) ON us.ShopId = sp.id AND us.PlatformType = 'Alibaba'
                            INNER JOIN dbo.P_ShopExtension se WITH(NOLOCK) ON se.ShopId = us.ShopId AND se.Source = 'FenDanSystem'
                            WHERE se.AppKey = @AppKey AND us.FxUserId=@fxUserId";
            var shop = DbConnection.Query<Shop, ShopExtension, Shop>(sql, (s, se) => { s.ShopExtension = se; s.AppKey = se.AppKey; s.AppSecret = se.AppSecret; return s; }, new { AppKey = CustomerConfig.AlibabaQingAppKey, fxUserId }).ToList();
            return shop;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="fxUserId">分单用户ID</param>
        /// <returns></returns>
        public Shop GetAlibabaDefalutQingShopsByFxUserId(int fxUserId)
        {
            string sql = $@"SELECT sp.*,se.* from dbo.P_Shop sp WITH(NOLOCK) 
                            INNER JOIN dbo.P_FxUserShop us WITH(NOLOCK) ON us.ShopId = sp.id AND us.PlatformType = 'Alibaba'
							INNER JOIN dbo.P_FxUserShop syus WITH(NOLOCK) ON syus.FxUserId = us.FxUserId AND syus.PlatformType='System'
							INNER JOIN dbo.P_CommonSetting cs WITH(NOLOCK) ON cs.[Key]='/ErpWeb/1688Supply/Supplier/1688Shop' AND cs.ShopId = syus.ShopId
							INNER JOIN dbo.P_ShopExtension se WITH(NOLOCK) ON se.ShopId = us.ShopId AND se.Source = 'FenDanSystem' AND se.ShopId = cs.Value
                            WHERE se.AppKey = @AppKey AND us.FxUserId=@fxUserId";
            var shop = DbConnection.Query<Shop, ShopExtension, Shop>(sql, (s, se) => { s.ShopExtension = se; s.AppKey = se.AppKey; s.AppSecret = se.AppSecret; return s; }, new { AppKey = CustomerConfig.AlibabaQingAppKey, fxUserId })?.FirstOrDefault();
            return shop;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="shopId">轻应用店铺ID</param>
        /// <returns></returns>
        public List<Shop> GetAlibabaQingShopsByShopId(int shopId)
        {
            string sql = $@" SELECT sp.*,se.* from dbo.P_Shop sp WITH(NOLOCK) 
                            INNER JOIN dbo.P_FxUserShop us WITH(NOLOCK) ON us.ShopId = sp.id AND us.PlatformType = 'Alibaba'
                            INNER JOIN dbo.P_ShopExtension se WITH(NOLOCK) ON se.ShopId = us.ShopId AND se.Source = 'FenDanSystem'
                            WHERE se.AppKey = @AppKey AND us.ShopId=@shopId";
            var shop = DbConnection.Query<Shop, ShopExtension, Shop>(sql, (s, se) => { s.ShopExtension = se; s.AppKey = se.AppKey; s.AppSecret = se.AppSecret; return s; }, new { AppKey = CustomerConfig.AlibabaQingAppKey, shopId }).ToList();
            return shop;
        }

        /// <summary>
        /// 获取所有分单系统用户1688收单店铺信息
        /// </summary>
        /// <param name="fxUserId">分单用户ID</param>
        /// <returns></returns>
        public List<Shop> GetAllAlibabaDefalutQingShops()
        {
            string sql = $@"SELECT sp.*,se.*,syus.* from dbo.P_Shop sp WITH(NOLOCK) 
                            INNER JOIN dbo.P_FxUserShop us WITH(NOLOCK) ON us.ShopId = sp.id AND us.PlatformType = 'Alibaba'
							INNER JOIN dbo.P_FxUserShop syus WITH(NOLOCK) ON syus.FxUserId = us.FxUserId AND syus.PlatformType='System'
							INNER JOIN dbo.P_CommonSetting cs WITH(NOLOCK) ON cs.[Key]='/ErpWeb/1688Supply/Supplier/1688Shop' AND cs.ShopId = syus.ShopId
							INNER JOIN dbo.P_ShopExtension se WITH(NOLOCK) ON se.ShopId = us.ShopId AND se.Source = 'FenDanSystem' AND se.ShopId = cs.Value
                            WHERE se.AppKey = @AppKey";
            var shops = DbConnection.Query<Shop, ShopExtension, FxUserShop, Shop>(sql, (s, se, fus) => { s.FxUserIds = fus.FxUserId; s.ShopExtension = se; s.AppKey = se.AppKey; s.AppSecret = se.AppSecret; return s; }, new { AppKey = CustomerConfig.AlibabaQingAppKey }).ToList();
            return shops;
        }

        /// <summary>
        /// 获取用户版本信息
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <returns></returns>
        public List<FxUserSystemVersionModel> GetFxUserSystemVersions(List<int> fxUserIds)
        {
            //判空处理
            if (fxUserIds == null || !fxUserIds.Any())
            {
                return new List<FxUserSystemVersionModel>();
            }
            //查询
            const string sqlByIn = @"SELECT fs.FxUserId,fs.ShopId,s.[Version] FROM [P_Shop](NOLOCK) s 
                        INNER JOIN [P_FxUserShop](NOLOCK) fs ON s.Id = fs.ShopId 
                        WHERE fs.PlatformType = 'System' AND fs.FxUserId IN @Codes";
            const string sqlByTableFun = @"SELECT fs.FxUserId,fs.ShopId,s.[Version] FROM [P_Shop](NOLOCK) s 
                        INNER JOIN [P_FxUserShop](NOLOCK) fs ON s.Id = fs.ShopId 
                        INNER JOIN FunStringToIntTable(@Codes,',') AS t ON t.item = fs.FxUserId
                        WHERE fs.PlatformType = 'System'";
            //查询
            //var models = DbConnection.Query<FxUserSystemVersionModel>(sql, new { Codes = string.Join(",", fxUserIds) })
            //    .ToList();
            var models = SqlOptimizationHandler.QueryEntities(fxUserIds, sqlByIn, sqlByTableFun,
                (sql, param) => DbConnection.Query<FxUserSystemVersionModel>(sql, param).ToList());
            return models;
        }

        /// <summary>
        /// 获取用户版本信息
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <returns></returns>
        public List<FxUserSystemVersionModel> GetFxUserSystemVersionsOld(List<int> fxUserIds)
        {
            //判空处理
            if (fxUserIds == null || !fxUserIds.Any())
            {
                return new List<FxUserSystemVersionModel>();
            }
            var shops = new List<Shop>();
            // 分页处理
            var pageSize = 2000;
            var pageCount = Math.Ceiling(fxUserIds.Count * 1.0 / pageSize);
            for (int i = 0; i < pageCount; i++)
            {
                var tempIds = fxUserIds.Skip(i * pageSize).Take(pageSize).ToList();
                var tempShops = GetShopWithCache(tempIds);
                if (tempShops != null && tempShops.Any())
                    shops.AddRange(tempShops);
            }
            if(shops == null && !shops.Any())
            {
                return new List<FxUserSystemVersionModel>();
            }
            var results = shops.Where(p => p.PlatformType == "System").Select(p=>new FxUserSystemVersionModel
            {
                FxUserId = p.FxUserIds,
                ShopId = p.Id,
                Version = p.Version,
            }).ToList();

            return results;
        }

        /// <summary>
        /// 获取店铺信息及多应用授权&校验授权
        /// </summary>
        /// <param name="sIds"></param>
        /// <param name="tryCheckAuthFunc">校验授权回调方法</param>
        /// <param name="isProcessPddShopExtension">是否处理拼多多授权扩展，默认true</param>
        /// <returns></returns>
        public List<Shop> GetShopsAndShopExtensionFunc(List<int> sIds, Func<IEnumerable<Shop>, bool> tryCheckAuthFunc = null, bool isProcessPddShopExtension = true)
        {
            // 先查询店铺
            const string shopSql = "SELECT * FROM P_Shop WITH(NOLOCK) WHERE Id IN @ids";
            var shops = DbConnection.Query<Shop>(shopSql, new { ids = sIds }).ToList();

            if (!shops.Any()) return shops;

            // 再查询店铺授权
            const string extensionSql = "SELECT * FROM P_ShopExtension WITH(NOLOCK) WHERE ShopId IN @ids";
            var shopExtensions = DbConnection.Query<ShopExtension>(extensionSql, new { ids = sIds }).ToList();

            // 存在一对多的关系
            foreach (var extension in shopExtensions)
            {
                var shop = shops.FirstOrDefault(x => x.Id == extension.ShopId);
                if (shop != null && shop.ShopExtension == null) shop.ShopExtension = extension;
                if (shop?.ShopExtension == null) continue;
                
                var temp = CommUtls.DeepClone(shop);
                temp.ShopExtension = extension;
                shops.Add(temp);
            }

            //1688还可以使用打单系统的授权,需要补充一条ShopExtension为空的数据
            if (shops.Any(s => s.PlatformType == PlatformType.Alibaba.ToString()))
            {
                //分组一下，数据查询出来只有一条数据，需要校验一下只有一条数据的时候再往里面写入一个ShopExtension为空的记录
                var appendShops = new List<Shop>();
                var shopsGroupBy = shops.GroupBy(x => x.Id).ToList();
                shopsGroupBy.ForEach(f =>
                {
                    if (f.Any(a => a.ShopExtension == null) == false)
                    {
                        var appendShop = f.FirstOrDefault();
                        if (appendShops.Any(i => i.Id == appendShop.Id) == false)
                        {
                            var newAppendShop = appendShop.ToJson().ToObject<Shop>();
                            newAppendShop.ShopExtension = null;
                            appendShops.Add(newAppendShop);
                        }
                    }
                });
                if (appendShops.Any())
                    shops.AddRange(appendShops);
            }

            //是否先更新服务过期时间
            if (tryCheckAuthFunc != null)
            {
                tryCheckAuthFunc(shops);
            }
            return GetShopByFxShopExtension(shops.ToList(), isProcessPddShopExtension: isProcessPddShopExtension);
        }

        /// <summary>
        /// 修改店铺名称
        /// </summary>
        /// <param name="shopId"></param>
        /// <param name="shopName"></param>
        public void UpdateShopName(int shopId, string shopName)
        {
            var sql = @"
UPDATE dbo.P_Shop
SET NickName = @shopName,
    ShopName = @shopName
WHERE Id = @shopId
";
            DbConnection.Execute(sql, new { shopName, shopId });
        }
        
        public List<Shop> GetOpenPlatformShops(int fxUserId)
        {
            var sql = @"SELECT s.id,s.NickName,s.ShopId,s.CreateTime
FROM dbo.P_FxUserShop fus WITH(NOLOCK) 
INNER JOIN dbo.P_Shop s WITH(NOLOCK) ON s.Id =fus.ShopId
WHERE s.PlatformType ='OwnShop' AND fus.FxUserId = @fxUserId AND fus.[Status] = 1 ";
            return DbConnection.Query<Shop>(sql, new { fxUserId }).ToList();
        }

        /// <summary>
        /// 获取店铺信息
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="shopIds"></param>
        /// <param name="platformType"></param>
        /// <returns></returns>
        public List<Shop> GetShops(int fxUserId, List<int> shopIds ,string platformType)
        {
            if (shopIds.IsNullOrEmptyList())
                return null;
            var parameters = new DynamicParameters();
            parameters.Add("fxUserId", fxUserId);
            parameters.Add("platformType", platformType);
            var sql = string.Empty;
            if (shopIds.Count == 1)
            {
                parameters.Add("shopId", shopIds.First());
                sql = @"SELECT s.*
FROM dbo.P_FxUserShop fus WITH(NOLOCK) 
INNER JOIN dbo.P_Shop s WITH(NOLOCK) ON s.Id =fus.ShopId and s.Id = @shopId
WHERE s.PlatformType =@platformType AND fus.FxUserId = @fxUserId AND fus.[Status] = 1 ";
            }
            else
            {
                parameters.Add("shopIds", shopIds);
                sql = @"SELECT s.*
FROM dbo.P_FxUserShop fus WITH(NOLOCK) 
INNER JOIN dbo.P_Shop s WITH(NOLOCK) ON s.Id =fus.ShopId and s.Id in @shopIds
WHERE s.PlatformType =@platformType AND fus.FxUserId = @fxUserId AND fus.[Status] = 1 ";
            }
            return DbConnection.Query<Shop>(sql, parameters).ToList();
        }

        public List<Shop> GetShopNotExpTokenTouTiao(string appkey)
        {
            // 跨云查询，拿到抖店店铺的token是最新
            var sql = $@"SELECT top 15 
t2.AppKey,
t2.AppSecret,
t2.AccessToken,
t2.RefreshToken,
t2.LastRefreshTokenTime,
t1.Id,
t1.PlatformType,
t1.NickName,
t1.ShopName,
t1.ShopId
FROM p_shop as t1  with(nolock) 
INNER JOIN P_ShopExtension as t2 with(nolock) on t1.Id = t2.ShopId
INNER JOIN [P_FxUserShop] t3 WITH(NOLOCK) ON t3.ShopId=t1.Id
INNER JOIN P_SyncStatus t4 WITH(NOLOCK) ON t4.ShopId=t1.Id AND t4.[SyncType] =1 AND t4.FxUserId=t3.FxUserId
where t1.PlatformType = 'TouTiao'  and t2.ExpireTime > GETDATE()+1 and t2.ExpireTime < GETDATE() +365 
AND t3.[AuthTime] > GETDATE()
AND t2.[AppKey] = @appkey  AND t4.[LastSyncStatus] ='Finished'
AND t4.[LastSyncTime] >DATEADD(hh,-12,GETDATE())";

            var db = DbApiAccessUtility.GetTouTiaoConfigureDb();
            return db.Query<Shop>(sql, new { appkey });
        }

        public List<Shop> GetShopNotExpTokenTouTiaoByFxUser(string appkey, int fxUserId, bool isdd)
        {
            var sql = $@"SELECT top 10
t2.AppKey,
t2.AppSecret,
t2.AccessToken,
t2.RefreshToken,
t2.LastRefreshTokenTime,
t1.Id,
t1.PlatformType,
t1.NickName,
t1.ShopName,
t1.ShopId
FROM p_shop as t1  with(nolock) 
INNER JOIN P_ShopExtension as t2 with(nolock) on t1.Id = t2.ShopId
INNER JOIN [P_FxUserShop] t3 WITH(NOLOCK) ON t3.ShopId=t1.Id
INNER JOIN P_SyncStatus t4 WITH(NOLOCK) ON t4.ShopId=t1.Id AND t4.[SyncType] =1 AND t4.FxUserId=t3.FxUserId
where t1.PlatformType = 'TouTiao' and t3.FxUserId = @fxUserId and t2.ExpireTime > GETDATE()+1 and t2.ExpireTime < GETDATE() +365  AND t3.[AuthTime] > GETDATE()
AND t2.[AppKey] = @appkey  AND t4.[LastSyncStatus] ='Finished'
AND t4.[LastSyncTime] >DATEADD(hh,-12,GETDATE());
";
            var ds = new { fxUserId, appkey };
            if (isdd)
            {
                return DbConnection.Query<Shop>(sql, ds).ToList();
            }
            else
            {
                var db = DbApiAccessUtility.GetTouTiaoConfigureDb(); // 抖店服务器拿到的token，经过反复验证才是最新可用
                // return db.Query<Shop>(sql, new ApiSqlParamModel(ds)); // 会报错，参数化导致的报错
                return db.Query<Shop>(sql, ds);
            }
        }

        public List<Shop> GetTouTiaoShopByIds(params int[] ids)
        {
            if (ids == null || ids.Any() == false) return new List<Shop>();

            var sql = @"select t2.AppKey,t2.AppSecret,t2.AccessToken,t2.RefreshToken,t2.LastRefreshTokenTime,t1.Id,t1.PlatformType,t1.NickName,t1.ShopName,t1.ShopId from P_Shop t1 WITH(NOLOCK) 
left join P_ShopExtension t2 WITH(NOLOCK) on t1.id = t2.shopid WHERE t1.id in @ids ";

            var db = DbApiAccessUtility.GetTouTiaoConfigureDb();
            var shops = db.Query<Shop>(sql, new { ids = ids.ToList() }).ToList();
            return shops;
        }

        /// <summary>
        /// 主店铺下的所有子店铺
        /// </summary>
        /// <param name="parentShopId"></param>
        /// <param name="platformType"></param>
        /// <returns></returns>
        public List<Shop> GetListByParentShopId(string parentShopId, string platformType)
        {
            var sql = $@"SELECT s.* FROM P_Shop s WITH(NOLOCK) WHERE ParentShopId=@parentShopId and PlatFormType=@platformType";
            var shops = DbConnection.Query<Shop>(sql, new { parentShopId, platformType })?.OrderByDescending(x => x.Id)?.ToList();
            return shops;
        }
        
        /// <summary>
        /// 更新用户版本
        /// </summary>
        /// <param name="version"></param>
        /// <param name="userIds"></param>
        public void UpdateVersion(int? version, List<int> userIds)
        {
            // 更新 SQL 语句
            const string sql = @"UPDATE P_Shop SET [Version]=@version WHERE Id IN(
        SELECT ShopId FROM P_FxUserShop WITH(NOLOCK) WHERE FxUserId IN @fxUserIds AND PlatformType='System')";
            
            // 执行更新
            DbConnection.Execute(sql, new { version, fxUserIds = userIds });
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <returns></returns>
        public List<Shop> GetShopWithCache(List<int> fxUserIds)
        {
            //判空处理
            if (fxUserIds == null || !fxUserIds.Any())
            {
                return new List<Shop>();
            }
            List<Shop> shops = new List<Shop>();
            var dbServer = this.DbConnection;
            var dbSql = $@"SELECT 
    s.*,
	usr.FxUserId AS FxUserIds 
FROM
	dbo.P_Shop s WITH ( NOLOCK )
	INNER JOIN dbo.P_FxUserShop usr WITH ( NOLOCK ) ON s.Id = usr.ShopId 
WHERE
	usr.FxUserId IN @fxUserId";
            shops = dbServer.Query<Shop>(dbSql, new { fxUserId = fxUserIds }).ToList();
            //if (!IsEnabledGlobalDataCacheKey)
            //    shops = dbServer.Query<Shop>(dbSql, new { fxUserId = fxUserIds }).ToList();
            //else
            //{
            //    shops = dbServer.QueryWithMultipleCache<Shop, int>(dbSql,
            //    keyFieldName: "FxUserIds",
            //    keyFieldValue: fxUserIds,
            //    parameterName: "fxUserId",
            //    filterNotMapped: false); ;
            //}
            if (shops == null)
            {
                shops = new List<Shop>();
            }
            return shops;
        }
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="ShopId"></param>
        /// <returns></returns>
        public Shop GetCrossBorderShops(int fxUserId, int shopId)
        {
            var parameters = new DynamicParameters();

            string sql = $@" SELECT TOP 1 t1.* FROM dbo.P_Shop t1 WITH(NOLOCK) INNER JOIN dbo.P_FxUserForeignShop t2 WITH(NOLOCK) 
                             ON  t1.ShopId = t2.ParentShopId AND t2.Status='1'
                            WHERE t1.Id=@shopId AND t2.FxUserId=@fxUserId";

            parameters.Add("fxUserId", fxUserId);
            parameters.Add("shopId", shopId);
            var shop = DbConnection.Query<Shop>(sql, new { fxUserId, shopId })?.First();
            return shop;
        }
        
          /// <summary>
        /// 获取店铺信息，按店铺ID列表（拆分查询）
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="platformType"></param>
        /// <param name="isAppendShopExtension"></param>
        /// <returns></returns>
        public List<Shop> GetListByIds(List<int> ids, string platformType = null, bool isAppendShopExtension = true)
        {
            //判空处理
            if (ids == null || !ids.Any())
            {
                return new List<Shop>();
            }
            //是否
            var sqlWhere = string.Empty;
            if (!string.IsNullOrWhiteSpace(platformType))
            {
                sqlWhere = $" AND s.PlatformType = '{platformType}'";
            }
            //SQL脚本
            var sqlByIn = $"SELECT s.* FROM P_Shop s WITH(NOLOCK) WHERE s.Id IN @Codes {sqlWhere}";
            var sqlByTableFun =
                $"SELECT s.* FROM P_Shop s WITH(NOLOCK) INNER JOIN dbo.FunStringToIntTable(@Codes,',') t ON s.Id = t.item {sqlWhere}";
            //查询店铺信息
            var shops = SqlOptimizationHandler.QueryEntities(ids, sqlByIn, sqlByTableFun,
                (sql, param) => DbConnection.Query<Shop>(sql, param).ToList());
            if (shops == null || !shops.Any())
            {
                return new List<Shop>();
            }
            //是否需要附加店铺扩展信息
            if (!isAppendShopExtension)
            {
                return shops;
            }
            ids = shops.Select(m => m.Id).Distinct().ToList();
            //查询店铺扩展信息
            const string sqlShopExtensionByIn =
                "SELECT se.* FROM P_ShopExtension se WITH(NOLOCK) WHERE se.ShopId IN @Codes";
            const string sqlShopExtensionByTableFun =
                "SELECT se.* FROM P_ShopExtension se WITH(NOLOCK) INNER JOIN dbo.FunStringToIntTable(@Codes,',') t ON se.ShopId = t.item";
            //查询店铺扩展信息
            var shopExtensions = SqlOptimizationHandler.QueryEntities(ids, sqlShopExtensionByIn,
                sqlShopExtensionByTableFun,
                (sql, param) => DbConnection.Query<ShopExtension>(sql, param).ToList());
            if (shopExtensions == null || !shopExtensions.Any())
            {
                return shops;
            }

            //左联
            var models = shops.GroupJoin(shopExtensions, s => s.Id, se => se.ShopId, (s, se) => new { s, se })
                .SelectMany(sse => sse.se.DefaultIfEmpty(), (sse, se) =>
                {
                    sse.s.ShopExtension = se;
                    return sse.s;
                }).ToList();
            return models;
        }

        /// <summary>
        /// 查询用户自己所有店铺，排除系统店铺
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="platformType"></param>
        public List<Shop> GetSelfShopsByFxUserIdAndPlatformType(int fxUserId, string platformType = null)
        {
            //查询店铺关系
            var selectFields = new List<string> { "FxUserId", "ShopId", "PlatformType" };
            var fxUserShops =
                new FxUserShopRepository().GetListByFxUserIdAndPlatformType(fxUserId, platformType, selectFields);
            if (fxUserShops == null || !fxUserShops.Any())
            {
                return new List<Shop>();
            }
            //查询店铺信息
            var shopIds = fxUserShops.Select(m => m.ShopId).Distinct().ToList();
            var shops = GetListByIds(shopIds, isAppendShopExtension: false);
            return shops;
        }

        /// <summary>
        /// 获取平台店铺信息，并缓存
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public List<Shop> GetPlatformShopInfosByIdsWithCache(List<int> ids)
        {
            //判空处理
            if (ids == null || !ids.Any())
            {
                return new List<Shop>();
            }

            //查询店铺名称
            var sql =
                "SELECT s.Id,s.NickName,s.ShopName,s.ShopId,s.PlatformType FROM P_Shop s WITH(NOLOCK) WHERE s.Id IN@Ids";
            //是否表值函数查询
            var isTableValueFunctionParam = false;
            if (ids.Count > SqlOptimizationHandler.SqlUseTableFunParamCount)
            {
                isTableValueFunctionParam = true;
                sql = @"SELECT s.Id,s.NickName,s.ShopName,s.ShopId,s.PlatformType FROM P_Shop s WITH(NOLOCK) 
                                           INNER JOIN dbo.FunStringToIntTable(@Ids,',') t ON s.Id=t.item";
            }

            //查询并缓存
            return DbConnection.QueryWithSingleCache<Shop, int>(sql, ids: ids, appendCacheKey: "PlatformShopInfo",
                cacheExpireSeconds: 300, isTableValueFunctionParam: isTableValueFunctionParam);
        }

        /// <summary>
        /// 根据平台获取有效的店，目前仅淘工厂使用，去除ShopExtension判断
        /// </summary>
        /// <param name="platformType"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public List<Shop> GetValidByPlatform(string platformType, int pageIndex = 1, int pageSize = 500)
        {
            const string sql = @"SELECT s.Id, fus.FxUserId AS FxUserIds
                FROM P_Shop s WITH(NOLOCK)
                INNER JOIN P_FxUserShop fus WITH(NOLOCK) ON fus.ShopId=s.Id
                WHERE s.PlatformType = @platformType 
                AND s.ExpireTime > GETDATE()";

            var parameters = new DynamicParameters();
            parameters.Add("platformType", platformType);

            var allShops = new List<Shop>();
            var currentPage = 1;

            while (true)
            {
                const string pageSql = sql + "ORDER BY s.Id OFFSET @offset ROWS FETCH NEXT @pageSize ROWS ONLY";
                parameters.Add("offset", (currentPage - 1) * pageSize);
                parameters.Add("pageSize", pageSize);

                var pageShops = DbConnection.Query<Shop>(pageSql, parameters).ToList();
                if (!pageShops.Any())
                    break;

                allShops.AddRange(pageShops);
                currentPage++;
            }

            return allShops;
        }
    }
}
