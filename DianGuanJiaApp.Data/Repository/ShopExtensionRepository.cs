using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DianGuanJiaApp.Data.Repository;
using Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Data.Enum;
using System.Collections.Concurrent;

namespace DianGuanJiaApp.Data.Repository
{
    public class ShopExtensionRepository : BaseRepository<Entity.ShopExtension>
    {
        /// <summary>
        /// 场景
        /// </summary>
        private string _scene = string.Empty;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="scene">场景：listing表示铺货场景，all表示全部，其他值表示常规场景</param>
        public ShopExtensionRepository(string scene = "") : base(Utility.CustomerConfig.ConfigureDbConnectionString)
        {
            _scene = scene;
        }

        public List<ShopExtension> GetRawShopExtensionByShopIds(List<int> sids)
        {
            if (sids == null || sids.Any() == false)
                return new List<ShopExtension>();

            var sql = $"SELECT * FROM P_ShopExtension WITH(NOLOCK) WHERE ShopId IN({string.Join(",", sids)})";
            var result = DbConnection.Query<ShopExtension>(sql)?.ToList();
            if (_scene == PlatformAppScene.listing)
                return result?.Where(a => CustomerConfig.FxListingAppKeys.Contains(a.AppKey)).ToList();
            else if (_scene != PlatformAppScene.all)
                return result?.Where(a => CustomerConfig.FxListingAppKeys.Contains(a.AppKey) == false).ToList();

            return result;
        }

        public List<ShopExtension> GetShopExtensionByShopIds(List<int> sids)
        {
            if (sids == null || sids.Any() == false)
                return new List<ShopExtension>();

            var sql = $"SELECT * FROM P_ShopExtension WITH(NOLOCK) WHERE ShopId IN({string.Join(",", sids)})"; 
            return GetShopByFxShopExtension(DbConnection.Query<ShopExtension>(sql)?.ToList());
        }

        public void UpdateAccessToken(int id, int shopId, string accessToken, string refreshToken = "")
        {
            var tempSql = "";
            if (!refreshToken.IsNullOrEmpty())
            {
                tempSql = $",RefreshToken=@refreshToken";
            }
            var sql = $@"UPDATE P_ShopExtension SET AccessToken =@accessToken,LastRefreshTokenTime=GETDATE() {tempSql} WHERE Id = {id};
UPDATE dbo.P_SyncStatus SET LastSyncMessage='' WHERE ShopId= {shopId} AND SyncType=1 AND Source='FenDanSystem';";
            DbConnection.Execute(sql, new { accessToken, refreshToken });
        }

        public void UpdateAccessToken(string appkey, int shopId, string accessToken, string refreshToken = "")
        {
            var tempSql = "";
            if (!refreshToken.IsNullOrEmpty())
            {
                tempSql = $",RefreshToken=@refreshToken";
            }
            var sql = $@"UPDATE P_ShopExtension SET AccessToken =@accessToken,LastRefreshTokenTime=GETDATE() {tempSql} WHERE ShopId = @sid And AppKey = @key;
UPDATE dbo.P_SyncStatus SET LastSyncMessage='' WHERE ShopId= @sid AND SyncType=1 AND Source='FenDanSystem';";
            DbConnection.Execute(sql, new { accessToken = accessToken, refreshToken = refreshToken, key = appkey, sid = shopId });
        }


        ///// <summary>
        ///// 只获取分单的授权信息
        ///// </summary>
        ///// <param name="shopList"></param>
        ///// <returns></returns>
        //public List<ShopExtension> GetShopByFxShopExtension(List<ShopExtension> shopExtensionList)
        //{
        //    //这里需要取出来指定过的appkey和相同shopid时LastRefreshTokenTime最新的一个来
        //    if (shopExtensionList == null || shopExtensionList.Count <= 0) return new List<ShopExtension>();
        //    Dictionary<int, ShopExtension> dicshop = new Dictionary<int, ShopExtension>();
        //    List<ShopExtension> result = new List<ShopExtension>();
        //    foreach (var shopex in shopExtensionList)
        //    {
        //        if (!dicshop.ContainsKey(shopex.ShopId))
        //        {
        //            if (CustomerConfig.FxSystemAppKeyDict.ContainsKey(shopex.AppKey))
        //            {
        //                dicshop.Add(shopex.ShopId, shopex);
        //            }
        //        }
        //        else
        //        {
        //            //如果快手既有分销又有智速打单则使用分销
        //            if (shopex.AppKey == CustomerConfig.KuaiShouFxAppKey || shopex.AppKey == CustomerConfig.PddFxAppKey)
        //            {
        //                dicshop[shopex.ShopId] = shopex;
        //                continue;
        //            }
        //            //如果LastRefreshTokenTime都为null取id较大的
        //            //两个都不为空则取时间大的
        //            //有一个为空取不为空的
        //            if (shopex.LastRefreshTokenTime == null && dicshop[shopex.ShopId].LastRefreshTokenTime == null)
        //            {
        //                if(shopex.Id > dicshop[shopex.ShopId].Id)
        //                {
        //                    dicshop[shopex.ShopId] = shopex;
        //                }
        //            }
        //            else if(shopex.LastRefreshTokenTime != null && dicshop[shopex.ShopId].LastRefreshTokenTime != null)
        //            {
        //                if(shopex.LastRefreshTokenTime > dicshop[shopex.ShopId].LastRefreshTokenTime)
        //                {
        //                    dicshop[shopex.ShopId] = shopex;
        //                }
        //            }
        //            else
        //            {
        //                if(shopex.LastRefreshTokenTime != null)
        //                {
        //                    dicshop[shopex.ShopId] = shopex;
        //                }
        //            }
        //        }
        //    }
        //    foreach (var shopex in dicshop)
        //    {
        //        result.Add(shopex.Value);
        //    }
        //    return result;
        //    //return shopExtensionList?.Where(x => CustomerConfig.FxSystemAppKeyDict.ContainsKey(x.AppKey))
        //    //    ?.GroupBy(x => x.ShopId)
        //    //    ?.Select(x => x.FirstOrDefault())
        //    //    ?.ToList();
        //}

        public List<ShopExtension> GetShopByFxShopExtension(List<ShopExtension> shopExtensionList)
        {
            if (shopExtensionList == null || shopExtensionList.Count == 0)
                return shopExtensionList;

            if (_scene == PlatformAppScene.listing)
            {
                //铺货场景：只取铺货应用相关的店铺
                shopExtensionList = shopExtensionList.Where(s => CustomerConfig.FxListingAppKeys.Contains(s.AppKey)).ToList();
            }
            else if (_scene != PlatformAppScene.all)
            {
                //常规场景：过滤铺货应用相关的店铺
                shopExtensionList = shopExtensionList.Where(s => CustomerConfig.FxListingAppKeys.Contains(s.AppKey) == false).ToList();
            }

            var shopRepository = new ShopRepository();
            var appOrderLists = new List<AppOrderList>();
            var sids = shopExtensionList.Select(x => x.ShopId).Distinct().ToList();
            var shopList = shopRepository.GetShopByIds(sids);
            // 查询头条店铺订购记录
            var serviceOrders = new List<ServiceAppOrder>();
            var toutiaoShops = shopList.Where(x => x.PlatformType == PlatformType.TouTiao.ToString()).ToList();
            var alibabShops = shopList.Where(x => x.PlatformType == PlatformType.Alibaba.ToString()).ToList();
            var kuaishouShops = shopList.Where(x => x.PlatformType == PlatformType.KuaiShou.ToString()).ToList();
            var pddShops = shopList.Where(x => x.PlatformType == PlatformType.Pinduoduo.ToString()).ToList();
            var jdShops = shopList.Where(x => x.PlatformType == PlatformType.Jingdong.ToString()).ToList();
            var userRepository = new UserRepository();
            if (toutiaoShops.Any())
            {
                var shopIds = toutiaoShops.Select(x => x.Id).Distinct().ToList();
                var ptShopsIds = toutiaoShops.Select(x => x.ShopId).ToList();
                var apps = userRepository.GetLastEndOrdersByShopIds(shopIds, ptShopsIds);
                serviceOrders.AddRange(apps);
            }
            if (alibabShops.Any())
            {
                //var ptShopsIds = alibabShops.Select(x => x.ShopId).ToList();
                //var apps = userRepository.GetLastEndOrdersByPtShopsIds(ptShopsIds, PlatformType.Alibaba.ToString());
                //serviceOrders.AddRange(apps);

                //var oldApps = userRepository.GetLastEndAppOrderListByPtShopsIds(ptShopsIds, PlatformType.Alibaba.ToString());
                //appOrderLists.AddRange(oldApps);
            }
            bool isOpenNewCompatibleLogic = false;
            if (kuaishouShops.Any())
            {
                var shopIds = kuaishouShops.Select(x => x.Id).Distinct().ToList();
                var ptUids = kuaishouShops.Select(x => x.Uid).Distinct().ToList();
                var apps = userRepository.GetLastEndOrdersWhere(shopIds, ptUids, PlatformType.KuaiShou.ToString());
                serviceOrders.AddRange(apps);

                isOpenNewCompatibleLogic = new CommonSettingRepository().Get("/System/FenDan/KuaiShou/OpenNewCompatibleLogic", 0)?.Value == "true";
            }

            var dicshopEx = new Dictionary<int, ShopExtension>();
            var groups = shopExtensionList.GroupBy(x => x.ShopId).ToList();
            foreach (var g in groups)
            {
                var first = shopList.FirstOrDefault(x => x.Id == g.Key);
                if (first == null)
                    continue;

                ShopExtension shopEx = null;
                // 头条有2个分单应用，需要确认是否取最新的应用
                //增加分单铺货应用兼容 2024.07.08
                if (first.PlatformType == PlatformType.TouTiao.ToString())
                {
                    if (_scene == PlatformAppScene.listing)
                    {
                        shopEx = g.FirstOrDefault(x => x.AppKey == CustomerConfig.TouTiaoFxListingAppKey);
                    }
                    else
                    {
                        // 分单取订购时间最长的应用
                        var serviceOrder = serviceOrders.Where(x => x.ServiceAppId != CustomerConfig.TouTiaoAppKey && x.ServiceAppId != CustomerConfig.TouTiaoFxListingAppKey && x.ShopId == first.Id).OrderByDescending(x => x.ServiceEnd).FirstOrDefault();
                        if (serviceOrder != null)
                            shopEx = g.FirstOrDefault(x => x.AppKey == serviceOrder.ServiceAppId);
                        if (shopEx == null)
                        {
                            shopEx = g.FirstOrDefault(x => x.AppKey == CustomerConfig.TouTiaoFxNewAppKey);
                            // 如果没有订购新应用，则取之前的铺货代发应用
                            if (shopEx == null)
                                shopEx = g.Where(x => CustomerConfig.FxSystemAppKeyDict.ContainsKey(x.AppKey)).OrderByDescending(x => x.LastRefreshTokenTime).FirstOrDefault();
                        }
                    }
                }
                else if (first.PlatformType == PlatformType.Alibaba.ToString())
                {
                    #region 依赖订购记录
                    // 分单取订购时间最长的应用
                    //var appOrder = serviceOrders.Where(x => x.PlatformShopId == first.ShopId && x.PlatformType == first.PlatformType).OrderByDescending(x => x.ServiceEnd).FirstOrDefault();
                    //if (appOrder != null)
                    //{
                    //    shopEx = g.FirstOrDefault(x => x.AppKey == appOrder.ServiceAppId);

                    //    //情况1.轻应用还有时间的话就优先使用轻应用
                    //    //情况2.轻应用到期了，打单应用未到期就使用打单应用
                    //    //情况3.两个应用都到期了，优先使用轻应用信息
                    //    if (appOrder?.ServiceEnd < DateTime.Now)
                    //    {
                    //        if (appOrderLists != null && appOrderLists.Any())
                    //        {
                    //            var oldAppOrder = appOrderLists.Where(x => x.MemberId == first.ShopId && x.AppKey == CustomerConfig.AlibabaAppKey).OrderByDescending(x => x.GmtServiceEnd).FirstOrDefault();
                    //            if (oldAppOrder != null && oldAppOrder.GmtServiceEnd > DateTime.Now)
                    //            {
                    //                shopEx = null;
                    //            }
                    //        }
                    //    }
                    //} 
                    #endregion

                    shopEx = g.FirstOrDefault(x => x.AppKey == CustomerConfig.AlibabaQingAppKey);
                    if(shopEx != null && shopEx.ExpireTime < DateTime.Now)
                    {
                        var oldShop = alibabShops.FirstOrDefault(x => x.Id == shopEx.ShopId);
                        if(oldShop != null && oldShop.ExpireTime > DateTime.Now)
                        {
                            shopEx = null;
                        }
                    }
                }
                else if (first.PlatformType == PlatformType.KuaiShou.ToString())
                {
                    var oldShopEx = g.FirstOrDefault(x => x.AppKey == CustomerConfig.KuaiShouFxAppKey);
                    var newShopEx = g.FirstOrDefault(x => x.AppKey == CustomerConfig.KuaiShouNewFxAppKey);
                    //【快手】
                    //1. ShopExtension?.ExpireTime 其中一个没值，就依赖订购记录
                    //2. 都有值就按ExpireTime 优先选择时间长/新应用
                    //3. ShopExtension，新旧都没授权那就只能用P_shop的授权信息为准
                    if ((oldShopEx != null && newShopEx != null) && (newShopEx?.ExpireTime == null || oldShopEx?.ExpireTime == null))
                    {
                        #region 依赖订购记录
                        ////只有一条记录
                        //if (g.Count() <= 1)
                        //{
                        //    shopEx = g.FirstOrDefault();
                        //}
                        //else
                        //{

                        //}

                        //---方案①---
                        //情况1.新应用还有时间的话就优先使用新应用
                        //情况2.新应用到期了，旧应用未到期就使用旧应用
                        //---方案②---
                        //多个应用情况下，应用的时间长就用那个逻辑

                        //情况3.两个应用都到期了，优先使用新应用信息
                        //情况4.没有订购记录的情况，优先查询是否有新应用授权，没有就使用旧应用授权

                        var oldAppOrder = serviceOrders.FirstOrDefault(x => (x.PlatformShopId == first.Uid || x.ShopId == first.Id) && x.ServiceAppId == CustomerConfig.KuaiShouFxAppKey);
                        var newAppOrder = serviceOrders.FirstOrDefault(x => (x.PlatformShopId == first.Uid || x.ShopId == first.Id) && x.ServiceAppId == CustomerConfig.KuaiShouNewFxAppKey);

                        ServiceAppOrder appOrder = null;
                        if (isOpenNewCompatibleLogic)
                        {
                            if (newAppOrder != null && newAppOrder.ServiceEnd > DateTime.Now)
                                appOrder = newAppOrder;
                            if (appOrder == null && oldAppOrder != null && oldAppOrder.ServiceEnd > DateTime.Now)
                                appOrder = oldAppOrder;
                        }
                        else
                        {
                            //按产品说法走，以最长时间为准
                            if (newAppOrder != null && oldAppOrder != null)
                            {
                                if (newAppOrder.ServiceEnd > oldAppOrder.ServiceEnd)
                                    appOrder = newAppOrder;
                                else
                                    appOrder = oldAppOrder;
                            }
                            else if (newAppOrder != null)
                            {
                                appOrder = newAppOrder;
                            }
                            else if (oldAppOrder != null)
                            {
                                appOrder = oldAppOrder;
                            }
                        }

                        if (appOrder != null)
                        {
                            shopEx = g.FirstOrDefault(x => x.AppKey == appOrder.ServiceAppId);
                            if (shopEx != null)
                                shopEx.ExpireTime = appOrder.ServiceEnd.Value;
                        }

                        //if (shopEx == null)
                        //{
                        //    var oldShopEx = g.FirstOrDefault(x => x.AppKey == CustomerConfig.KuaiShouFxAppKey);
                        //    var newShopEx = g.FirstOrDefault(x => x.AppKey == CustomerConfig.KuaiShouNewFxAppKey);
                        //    if (newShopEx != null)
                        //        shopEx = newShopEx;
                        //    if (shopEx == null)
                        //        shopEx = oldShopEx;
                        //}
                        #endregion
                    }
                    else
                    {
                        if (isOpenNewCompatibleLogic)
                        {
                            //新应用有效就优先
                            if (newShopEx != null && newShopEx.ExpireTime > DateTime.Now)
                                shopEx = newShopEx;
                            if (shopEx == null && oldShopEx != null && oldShopEx.ExpireTime > DateTime.Now)
                                shopEx = oldShopEx;
                        }
                        else
                        {
                            //按产品说法走，以最长时间为准
                            if (newShopEx != null && oldShopEx != null)
                            {
                                if(newShopEx.ExpireTime != null && oldShopEx.ExpireTime != null)
                                {
                                    if (newShopEx.ExpireTime > oldShopEx.ExpireTime)
                                        shopEx = newShopEx;
                                    else
                                        shopEx = oldShopEx;
                                }
                                else
                                {
                                    //过期时间其中有为空的话按刷新授权时间最近的为准
                                    if (newShopEx.LastRefreshExpireTime > oldShopEx.LastRefreshExpireTime)
                                        shopEx = newShopEx;
                                    else
                                        shopEx = oldShopEx;
                                }
                            }
                            else if (newShopEx != null)
                            {
                                shopEx = newShopEx;
                            }
                            else if (oldShopEx != null)
                            {
                                shopEx = oldShopEx;
                            }
                        }
                    }

                    if (shopEx == null)
                    {
                        if (newShopEx != null)
                            shopEx = newShopEx;
                        if (shopEx == null)
                            shopEx = oldShopEx;
                    }
                }
                else if (first.PlatformType == PlatformType.AlibabaZhuKe.ToString())
                {
                    // 主客授权 只获取阿里主客
                    shopEx = g.FirstOrDefault(x => x.AppKey == CustomerConfig.AlibabaZkphAppKey);
                }
                else if (first.PlatformType == PlatformType.Pinduoduo.ToString())
                {
                    //优先取分单应用授权
                    shopEx = g.FirstOrDefault(x => x.AppKey == CustomerConfig.PddFxAppKey);
                    //没取到分单应用，就取第一个（这种情况说明只有打单应用）continue退出后不加入到结果，说明用打单应用
                    if (shopEx == null)
                        continue;
                    var pddShop = pddShops.FirstOrDefault(x => x.Id == shopEx.ShopId);
                    //取到了分单应用，且该店铺有使用打单应用，需要根据订购到期时间判断，优先使用订购时间较长的应用
                    if (pddShop.SystemVersion == "ForFxSystem")
                    {
                        //兼容拼多多打单应用-过期时间大的为准 2024.06.18
                        if (shopEx.ExpireTime != null && pddShop.ExpireTime != null && shopEx.ExpireTime.Value < pddShop.ExpireTime.Value)
                            continue;
                    }
                }
                else if (first.PlatformType == PlatformType.Jingdong.ToString())
                {
                    //优先取分单应用授权
                    shopEx = g.FirstOrDefault(x => x.AppKey == CustomerConfig.JingDongFxAppKey);
                    //没取到分单应用，就取第一个（这种情况说明只授权打单应用）
                    if (shopEx == null)
                        continue;
                    //取到了分单应用，且该店铺又使用了打单应用授权过，需要根据订购到期时间判断，优先使用订购时间较长的应用
                    var jdShop = jdShops.FirstOrDefault(x => x.Id == shopEx.ShopId);
                    if (string.IsNullOrEmpty(jdShop.SystemVersion))
                    {
                        if (shopEx.ExpireTime != null && jdShop.ExpireTime != null && shopEx.ExpireTime.Value < jdShop.ExpireTime.Value)
                            continue;
                    }
                }
                else if (first.PlatformType == PlatformType.OwnShop.ToString())
                {
                    shopEx = g.FirstOrDefault();
                }                
                else if (first.PlatformType == PlatformType.Other_Heliang.ToString() || first.PlatformType == PlatformType.Other_JuHaoMai.ToString() || first.PlatformType == PlatformType.Other_HaoYouDuo.ToString())
                {
                    shopEx = g.FirstOrDefault();
                }
                else if (first.PlatformType == PlatformType.WxVideo.ToString())
                {
                    var oldShopEx = g.FirstOrDefault(x => x.AppKey == CustomerConfig.Fx_WxComponentNewAppId);
                    var newShopEx = g.FirstOrDefault(x => x.AppKey == CustomerConfig.Fx_WxShopNewAppId); 
                    
                    // 多应用情况下，以最长时间为准
                    if (newShopEx != null && oldShopEx != null)
                    {
                        if(newShopEx.ExpireTime != null && oldShopEx.ExpireTime != null)
                        {
                            shopEx = newShopEx.ExpireTime > oldShopEx.ExpireTime
                                ? newShopEx
                                : oldShopEx;
                        }
                        else
                        {
                            //过期时间其中有为空的话按刷新授权时间最近的为准
                            shopEx = newShopEx.LastRefreshExpireTime > oldShopEx.LastRefreshExpireTime 
                                ? newShopEx
                                : oldShopEx;
                        }
                    }
                    else if (newShopEx != null)
                    {
                        shopEx = newShopEx;
                    }
                    else if (oldShopEx != null)
                    {
                        shopEx = oldShopEx;
                    }
                }
                else
                {
                    shopEx = g.Where(x => CustomerConfig.FxSystemAppKeyDict.ContainsKey(x.AppKey)).FirstOrDefault(x => x.AppKey == CustomerConfig.KuaiShouFxAppKey || x.AppKey == CustomerConfig.PddFxAppKey);
                    if (shopEx == null)
                        shopEx = g.Where(x => CustomerConfig.FxSystemAppKeyDict.ContainsKey(x.AppKey)).OrderByDescending(x => x.LastRefreshTokenTime).FirstOrDefault();
                }
                if (shopEx != null && dicshopEx.ContainsKey(g.Key) == false)
                    dicshopEx.Add(g.Key, shopEx);
            }
            var result = dicshopEx.Values.ToList();
            return result;
        }

        public List<dynamic> GetSubscriperByAppKeys(List<string> appkeys)
        {
            string sql = $@" SELECT shopex.ShopId,usershop2.FxUserId as FxUserId,dbserver.ConnectionString as DbConnectionStr,dbname.DbName as DbName
                            FROM dbo.P_ShopExtension AS shopex {WithNoLockSql}
                            INNER JOIN P_FxUserShop AS usershop {WithNoLockSql} ON shopex.ShopId = usershop.ShopId
                            INNER JOIN dbo.P_FxUserShop AS usershop2 {WithNoLockSql} ON usershop.FxUserId = usershop2.FxUserId
                            INNER JOIN dbo.P_DbConfig AS dbconfig {WithNoLockSql} ON dbconfig.ShopId = usershop2.ShopId AND dbconfig.DbCloudPlatform = 'Alibaba'
                            INNER JOIN dbo.P_DbNameConfig AS dbname {WithNoLockSql} ON dbconfig.DbNameConfigId = dbname.Id
                            INNER JOIN dbo.P_DbServerConfig AS dbserver {WithNoLockSql} ON dbname.DbServerConfigId = dbserver.Id
                            WHERE shopex.AppKey in @key AND usershop2.PlatformType = 'system' ";
            return DbConnection.Query<dynamic>(sql, new { key = appkeys })?.ToList();
        }


        public List<ShopExtension> GetShopAppKey(List<int> sids)
        {
            if (sids == null || sids.Any() == false)
                return new List<ShopExtension>();
            var result = new List<ShopExtension>();
            
            sids.ForEach(m =>
            {
                // 从缓存中获取，仅缓存个别字段
                var model = GetByShopIdWithCache(m);
                if (model != null && model.Any())
                    result.AddRange(model);
            });
            
            return result;
            var sql = $"SELECT Id,ShopId,AppKey FROM P_ShopExtension WITH(NOLOCK) WHERE ShopId IN @Sids;";
            return DbConnection.Query<ShopExtension>(sql, new { Sids = sids }).ToList();
        }

        /// <summary>
        /// 仅缓存个别不易改动的字段
        /// </summary>
        /// <param name="sid"></param>
        /// <returns></returns>
        public List<ShopExtension> GetByShopIdWithCache(int sid)
        {
            const string sql = "SELECT Id,ShopId,AppKey FROM P_ShopExtension WITH(NOLOCK) WHERE ShopId=@sid";
            var models = DbConnection.QueryWithCache<ShopExtension>(sql, new { sid }, sid.ToString());
            return models;
        }

        public void UpdateExpireTime(string appKey, int shopId, DateTime newTime)
        {
            var sql = $@"UPDATE dbo.P_ShopExtension SET ExpireTime = @newTime,LastRefreshExpireTime=GETDATE() WHERE AppKey = @appKey AND ShopId = @shopId";
            DbConnection.Execute(sql, new { appKey, shopId, newTime });
        }

        public int UpdateExpireTimeById(List<ShopExtension> shopExtensions)
        {
            var pageSize = 50;
            var pageCount = decimal.Ceiling(shopExtensions.Count() / (decimal)pageSize);
            var rows = new List<List<ShopExtension>>();
            for (int i = 0; i < pageCount; i++)
            {
                var list = shopExtensions.Skip(i * pageSize).Take(pageSize);
                if (list?.Count() == 0)
                    break;
                rows.Add(list.ToList());
            }
            var count = new ConcurrentBag<int>();

            Console.WriteLine($"分页执行，总页数：{pageCount}，每页：{pageSize}条数据执行...");
            Parallel.ForEach(rows, new ParallelOptions { MaxDegreeOfParallelism = 10 }, (models) =>
            {
                var sql = "";
                models.ForEach(m =>
                {
                    sql += $"UPDATE dbo.P_ShopExtension SET ExpireTime = '{m.ExpireTime?.ToString("yyyy-MM-dd HH:mm:ss")}',LastRefreshExpireTime=GETDATE() WHERE Id ={m.Id};";
                });
                var row = DbConnection.Execute(sql);
                if (row > 0)
                {
                    count.Add(row);
                    Console.WriteLine($"执行成功：{models.Select(s => s.Id).ToJson()}...");
                }
            });
            return count.Sum();
        }

        public void UpdateAppSecret(int shopId,string appKey,string appSecret)
        {
            var sql = $@"UPDATE dbo.P_ShopExtension SET AppSecret = @appSecret WHERE AppKey = @appKey AND ShopId = @shopId";
            DbConnection.Execute(sql, new { shopId,appKey, appSecret });
        }

        /// <summary>
        /// 软删除，更新shopId = -shopId 
        /// </summary>
        /// <param name="shopId"></param>
        /// <param name="appKey"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public int DeleteByShopId(int shopId, string appKey)
        {
            const string sql = @"UPDATE P_ShopExtension
                SET ShopId = -ShopId
                WHERE Id = (SELECT TOP 1 Id FROM P_ShopExtension WITH(NOLOCK) WHERE AppKey = @appKey AND ShopId = @shopId ORDER BY Id DESC)";
            return DbConnection.Execute(sql, new { shopId, appKey });
        }

        /// <summary>
        /// 恢复删除，更新shopId = -shopId
        /// </summary>
        /// <param name="shopId"></param>
        /// <param name="appKey"></param>
        /// <returns></returns>
        public int RecoveryByShopId(int shopId, string appKey)
        {
            const string sql = @"UPDATE P_ShopExtension
                SET ShopId = ABS(ShopId)
                WHERE Id = (SELECT TOP 1 Id FROM P_ShopExtension WITH(NOLOCK) WHERE AppKey = @app and ShopId = -@shopId ORDER BY Id DESC)";
            return DbConnection.Execute(sql, new { shopId, appKey });
        }
    }
}
