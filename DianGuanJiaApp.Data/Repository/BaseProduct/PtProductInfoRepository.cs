using System;
using System.Collections.Generic;
using System.Linq;
using Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Entity.BaseProduct;
using Z.Dapper.Plus;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility;
using System.Data;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Model.BaseProduct;
using DianGuanJiaApp.Data.Entity.SupplierProduct;
using DianGuanJiaApp.Data.Enum;
using MySql.Data.MySqlClient;

namespace DianGuanJiaApp.Data.Repository.BaseProduct
{
    /// <summary>
    /// PtProductInfo 相关数据层操作
    /// </summary>
    public partial class PtProductInfoRepository : BaseProductBaseRepository<PtProductInfo>
    {
        private string _connectionString = string.Empty;

        /// <summary>
        /// 通过BaseSiteContext.CurrentNoThrow.CurrentFxUserId获取使用的数据库
        /// </summary>
        public PtProductInfoRepository()
        {
        }

        /// <summary>
        /// 指定fxUserId
        /// </summary>
        /// <param name="fxUserId"></param>
        public PtProductInfoRepository(int fxUserId) : base(fxUserId)
        {
        }

        /// <summary>
        /// 指定连接字符串
        /// </summary>
        /// <param name="connectionString"></param>
        /// <param name="isUseMySQL"></param>
        public PtProductInfoRepository(string connectionString, bool isUseMySQL = true) : base(connectionString, isUseMySQL)
        {
            _connectionString = connectionString;
        }

        /// <summary>
        /// 基础商品库及货盘库都有的表，用此构造函数
        /// </summary>
        /// <param name="dbFlag">supplierproduct=货盘，business=普通业务库，其他值=基础商品库</param>
        /// <param name="fxUserId">dbFlag为基础商品库时有效，是否指定用户，为0时未指定，用当前登录用户Id</param>
        public PtProductInfoRepository(string dbFlag, int fxUserId = 0) : base(dbFlag, fxUserId)
        {
        }


        /// <summary>
        /// 添加（含扩展数据）
        /// </summary>
        /// <param name="model"></param>
        public new void Add(PtProductInfo model)
        {
            if (model == null)
                return;

            var db = DbConnection;
            if (model.Ext != null)
            {
                if (db is MySqlConnection)
                    db.InsertMysqlWithLongId(model.Ext);
                else
                    db.Insert(model.Ext);
            }
            if (db is MySqlConnection)
                db.InsertMysqlWithLongId(model);
            else
                db.Insert(model);
        }
        /// <summary>
        /// 更新（含扩展数据）
        /// </summary>
        /// <param name="model"></param>
        public new void Update(PtProductInfo model)
        {
            if (model == null)
                return;

            var db = DbConnection;
            if (model.Ext != null)
            {
                if (db is MySqlConnection)
                    db.UpdateMysql(model.Ext);
                else
                    db.Update(model.Ext);
            }
            if (db is MySqlConnection)
                db.UpdateMysql(model);
            else
                db.Update(model);
        }
        /// <summary>
        /// 批量添加（含扩展数据）
        /// </summary>
        /// <param name="models"></param>
        public void BatchAdd(List<PtProductInfo> models)
        {
            if (models == null || models.Any() == false)
                return;

            var db = DbConnection;
            var exts = models.Where(a => a.Ext != null).Select(a => a.Ext).ToList();

            if(exts != null && exts.Any())
                db.BulkInsert(exts);

            db.BulkInsert(models);

        }

        /// <summary>
        /// 批量添加（含扩展数据）
        /// </summary>
        /// <param name="models"></param>
        public void BatchUpdate(List<PtProductInfo> models)
        {
            if (models == null || models.Any() == false)
                return;

            var db = DbConnection;
            var exts = models.Where(a => a.Ext != null).Select(a => a.Ext).ToList();

            if (exts != null && exts.Any())
                db.BulkUpdate(exts);

            db.BulkUpdate(models);
        }

        /// <summary>
        /// 获取单个（含扩展表数据）
        /// </summary>
        /// <param name="code">UniqueCode</param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public PtProductInfo GetByCode(string code, int fxUserId = 0)
        {
            var sqlTemplate = $@"SELECT * FROM PtProductInfo t1 WITH(NOLOCK) 
INNER JOIN PtProductInfoExt ext WITH(NOLOCK) ON ext.PtProductUniqueCode=t1.UniqueCode WHERE t1.UniqueCode=@code";
            if (fxUserId > 0)
                sqlTemplate += " AND t1.FxUserId=@fxUserId";
            var sql = TranSql(sqlTemplate);
            var model = DbConnection.Query<PtProductInfo, PtProductInfoExt, PtProductInfo>(sql, (info, ext) =>
            {
                info.Ext = ext;
                return info;
            }, param: new { code, fxUserId }).FirstOrDefault();
            return model;
        }

        /// <summary>
        /// 是否存在平台资料
        /// </summary>
        /// <param name="uid"></param>
        /// <param name="fxUserId"></param>
        /// <param name="platformType">指定平台</param>
        /// <param name="shopId">指定店铺Id，0表示公共</param>
        /// <returns></returns>
        public bool IsExistByUid(long uid, int fxUserId, string platformType, int shopId = 0)
        {
            var sql = "SELECT TOP 1 1 FROM PtProductInfo WITH(NOLOCK) WHERE BaseProductUid = @uid AND FxUserId=@fxUserId AND PlatformType=@platformType AND ShopId=@shopId AND Status=1";
            if (IsUseMySQL)
                sql = "SELECT 1 FROM PtProductInfo WHERE BaseProductUid = @uid AND FxUserId=@fxUserId AND PlatformType=@platformType AND ShopId=@shopId AND Status=1 LIMIT 1";

            var result = DbConnection.Query<int>(sql, new { uid, fxUserId, platformType, shopId }).FirstOrDefault().ToInt();
            return result >= 1;
        }

        /// <summary>
        /// 根据uid获取信息
        /// </summary>
        /// <param name="uid"></param>
        /// <returns></returns>
        public List<PtProductInfo> GetByProductUid(long uid, int fxUserId)
        {
            var sqlTemplate = $@"SELECT * FROM PtProductInfo WHERE BaseProductUid=@BaseProductUid AND FxUserId=@FxUserId AND Status=1 AND ShopId=0";
            var sql = TranSql(sqlTemplate);
            var list = DbConnection.Query<PtProductInfo>(sql, new { BaseProductUid= uid, FxUserId= fxUserId }).ToList();
            return list;
        }

        /// <summary>
        /// 根据uid获取信息
        /// </summary>
        /// <param name="uids"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public List<PtProductInfo> GetByProductUids(List<long> uids, int fxUserId)
        {
            var sqlTemplate = $@"SELECT * FROM PtProductInfo WHERE BaseProductUid IN @BaseProductUid AND FxUserId=@FxUserId AND Status=1 AND ShopId=0";
            var sql = TranSql(sqlTemplate);
            try
            {
                var dy = new DynamicParameters();
                dy.Add("@BaseProductUid", uids);
                dy.Add("@FxUserId", fxUserId);
                var aa = GetRealSql(sql, dy);
                var list = DbConnection.Query<PtProductInfo>(sql, dy).ToList();
                return list;
            }
            catch (Exception ex)
            {

                throw ex;
            }
            
        }
        /// <summary>
        /// 根据uid获取信息（包含扩展）
        /// </summary>
        /// <param name="productUid"></param>
        /// <param name="fxUserId"></param>
        /// <param name="platformType"></param>
        /// <param name="shopId">指定店铺Id，0表示公共</param>
        /// <returns></returns>
        public PtProductInfo GetByProductUid(long productUid, int fxUserId, string platformType, int shopId = 0)
        {
            var sqlTemplate = $@"SELECT * FROM PtProductInfo t1 WITH(NOLOCK) 
INNER JOIN PtProductInfoExt ext WITH(NOLOCK) ON ext.PtProductUniqueCode=t1.UniqueCode WHERE t1.BaseProductUid=@BaseProductUid AND t1.PlatformType=@PlatformType AND t1.Status=1 AND ShopId=@ShopId";
            if (fxUserId > 0)
                sqlTemplate += " AND t1.FxUserId=@fxUserId";
            var sql = TranSql(sqlTemplate);
            var model = DbConnection.Query<PtProductInfo, PtProductInfoExt, PtProductInfo>(sql, (info, ext) =>
            {
                info.Ext = ext;
                return info;
            }, param: new { BaseProductUid = productUid, FxUserId = fxUserId, PlatformType = platformType, ShopId = shopId }).FirstOrDefault();
            //var sqlTemplate = $@"SELECT * FROM PtProductInfo with(nolock) WHERE BaseProductUid=@BaseProductUid AND FxUserId=@FxUserId AND PlatformType=@PlatformType AND Status=1 ";
            //var sql = TranSql(sqlTemplate);
            return model;
        }
        /// <summary>
        /// 根据SupplierProductUid获取信息（包含扩展）（货盘的平台资料）
        /// </summary>
        /// <param name="shopId">指定店铺Id，0表示公共</param>
        /// <returns></returns>
        public PtProductInfo GetBySupplierProductUid(long supplierProductUid, int fxUserId, int fromFxUserId, string platformType, int shopId = 0)
        {
            var sqlTemplate = $@"SELECT * FROM PtProductInfo t1 WITH(NOLOCK) 
                                INNER JOIN PtProductInfoExt ext WITH(NOLOCK) ON ext.PtProductUniqueCode=t1.UniqueCode 
                                WHERE t1.FromSupplierProductUid=@FromSupplierProductUid 
                                AND t1.FromFxUserId=@FromFxUserId 
                                AND t1.PlatformType=@PlatformType 
                                AND t1.Status=1 
                                AND ShopId=@ShopId";
            if (fxUserId > 0)
                sqlTemplate += " AND t1.FxUserId=@fxUserId";
            var sql = TranSql(sqlTemplate);
            var model = DbConnection.Query<PtProductInfo, PtProductInfoExt, PtProductInfo>(sql, (info, ext) =>
            {
                info.Ext = ext;
                return info;
            }, param: new { FromSupplierProductUid = supplierProductUid, FxUserId = fxUserId,FromFxUserId = fromFxUserId, PlatformType = platformType, ShopId = shopId }).FirstOrDefault();
            //var sqlTemplate = $@"SELECT * FROM PtProductInfo with(nolock) WHERE BaseProductUid=@BaseProductUid AND FxUserId=@FxUserId AND PlatformType=@PlatformType AND Status=1 ";
            //var sql = TranSql(sqlTemplate);
            return model;
        }

        /// <summary>
        /// 获取指定平台资料信息
        /// </summary>
        /// <param name="baseProductUid"></param>
        /// <param name="platformType"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public PtProductInfo GetDefaultPtInfos(long baseProductUid,string platformType, int fxUserId)
        {
            var sqlTemplate = $@"SELECT * FROM
	PtProductInfo t1 WITH(NOLOCK)
	INNER JOIN PtProductInfoExt ext WITH(NOLOCK) ON ext.PtProductUniqueCode = t1.UniqueCode 
WHERE
	t1.BaseProductUid = @baseProductUid
	AND t1.FxUserId = @fxUserId 
    AND t1.PlatformType = @platformType 
    AND t1.ShopId = 0 
	AND t1.STATUS =1";
            var sql = TranSql(sqlTemplate);
            var model = DbConnection.Query<PtProductInfo, PtProductInfoExt, PtProductInfo>(sql, (info, ext) =>
            {
                info.Ext = ext;
                return info;
            }, param: new { baseProductUid, fxUserId, platformType}).FirstOrDefault();
            return model;
        }

        /// <summary>
        /// 根据基础商品Uid获取平台资料
        /// </summary>
        /// <param name="baseProductUidsDic">Key：基础商品ProUid，Value：货盘商品ProUid</param>
        /// <returns></returns>
        public Dictionary<long, List<PtProductInfo>> GetListByBaseProductUids(Dictionary<long, long> baseProductUidsDic)
        {
            const string sql = @"SELECT *
            FROM PtProductInfo t1
            INNER JOIN PtProductInfoExt ext ON ext.PtProductUniqueCode = t1.UniqueCode
            WHERE
              t1.BaseProductUid IN @BaseProductUids
              AND t1.Status = 1
              AND t1.IsPublic = 1";
            var list = DbConnection.Query<PtProductInfo, PtProductInfoExt, PtProductInfo>(sql, (info, ext) =>
            {
                info.Ext = ext;
                long supplierProductUid;
                baseProductUidsDic.TryGetValue(info.BaseProductUid,out supplierProductUid);
                if (supplierProductUid != default(long)) info.FromSupplierProductUid = supplierProductUid;
                return info;
            }, param: new { BaseProductUids = baseProductUidsDic.Keys.ToList() }).ToList();
            
            return list.GroupBy(x => x.BaseProductUid).ToDictionary(x => x.Key, x => x.ToList());
        }

        /*
        public List<string> GetPtProductUniqueCode(int fxUserId, long fromSupplierProductUid = 0, long fromBaseProductUid = 0)
        {
            var sqlTemplate = $@"SELECT UniqueCode from PtProductInfo A where A.FxUserId=@fxUserId AND A.Status<>0 ";

            string sqlwhere = string.Empty;
            if (fromSupplierProductUid > 0)
                sqlwhere += " and A.FromSupplierProductUid=@fromSupplierProductUid";

            if (fromBaseProductUid > 0)
                sqlwhere += " and A.FromBaseProductUid=@fromBaseProductUid";

            var sql = TranSql(sqlTemplate + sqlwhere);

            var dy = new DynamicParameters();
            dy.Add("@fxUserId", fxUserId);
            dy.Add("@fromSupplierProductUid", fromSupplierProductUid);
            dy.Add("@fromBaseProductUid", fromBaseProductUid);

            var aa = GetRealSql(sql, dy);
            var list = DbConnection.Query<string>(sql, dy).ToList();
            return list;
        }
        */

        public List<string> GetPtProductUniqueCode2(int fxUserId, int shopId, long fromSupplierProductUid = 0, long fromBaseProductUid = 0, bool islog = false)
        {
            var sqlTemplate = $@"SELECT UniqueCode from PtProductInfo A where A.FxUserId=@fxUserId  and A.shopId = @shopId AND A.Status<>0 ";

            string sqlwhere = string.Empty;
            if (fromSupplierProductUid > 0 && fromBaseProductUid > 0)
            {
                sqlwhere += " and (A.FromSupplierProductUid=@fromSupplierProductUid or A.FromBaseProductUid=@fromBaseProductUid)";
            }
            else
            {
                if (fromSupplierProductUid > 0)
                    sqlwhere += " and A.FromSupplierProductUid=@fromSupplierProductUid";

                if (fromBaseProductUid > 0)
                    sqlwhere += " and A.FromBaseProductUid=@fromBaseProductUid";
            }

            var sql = TranSql(sqlTemplate + sqlwhere);

            var dy = new DynamicParameters();
            dy.Add("@fxUserId", fxUserId);
            dy.Add("@fromSupplierProductUid", fromSupplierProductUid);
            dy.Add("@fromBaseProductUid", fromBaseProductUid);
            dy.Add("@shopId", shopId);

            var aa = GetRealSql(sql, dy);
            if (islog) Log.WriteLine($"查找UniqueCode：{aa}", "CheckRepeatPtListing.txt");
            var list = DbConnection.Query<string>(sql, dy).ToList();
            return list;
        }

        /// <summary>
        /// 铺货草稿获取
        /// </summary>
        /// <param name="PtProductUniqueCodes"></param>
        /// <param name="fxUserId"></param>
        /// <param name="platformType"></param>
        /// <returns></returns>
        public List<PtProductInfo> GetPtInfoDrafts(List<string> PtProductUniqueCodes, int fxUserId, string platformType)
        {
            var count = PtProductUniqueCodes.Count;
            int batchSize = 500;
            var pageCount = Math.Ceiling(count * 1.0 / batchSize);
            var res = new List<PtProductInfo>();
            for (int i = 0; i < pageCount; i++)
            {
                var codes = PtProductUniqueCodes.Skip(batchSize * i).Take(batchSize).Distinct().ToList();
                var sql = $@"SELECT * FROM PtProductInfo t1 WITH(NOLOCK) 
                                INNER JOIN PtProductInfoExt ext WITH(NOLOCK) ON ext.PtProductUniqueCode=t1.UniqueCode 
                                WHERE t1.UniqueCode IN @codes
                                AND t1.PlatformType=@platformType 
                                AND t1.Status=1 
                                AND t1.FxUserId=@fxUserId
                                AND ShopId=-1";
                if (IsUseMySQL) sql = TranSql(sql);
                var sqlParam = new DynamicParameters();
                sqlParam.Add("@codes", codes);
                sqlParam.Add("@platformType", platformType);
                sqlParam.Add("@fxUserId", fxUserId);
                var list = DbConnection.Query<PtProductInfo, PtProductInfoExt, PtProductInfo>(sql, (info, ext) =>
                {
                    info.Ext = ext;
                    return info;
                }, sqlParam).ToList();

                res.AddRange(list);
            }
            return res;
        }

        /// <summary>
        /// 获取平台资料 FromBaseProductUid 
        /// </summary>
        /// <param name="fuid">FromBaseProductUid</param>
        /// <param name="fxUserId"></param>
        /// <param name="platformType">指定平台</param>
        /// <returns></returns>
        public PtProductInfo GetPtInfoDrafts(long fuid, int fxUserId, string platformType)
        {
            var sqlTemplate = $@"SELECT * FROM PtProductInfo t1 WITH(NOLOCK)
	                                INNER JOIN PtProductInfoExt ext WITH(NOLOCK) ON ext.PtProductUniqueCode = t1.UniqueCode 
                                WHERE FromBaseProductUid = @fuid AND FxUserId=@fxUserId AND PlatformType=@platformType AND ShopId=-1 AND Status=1";

            var sql = TranSql(sqlTemplate);
            var result = DbConnection.Query<PtProductInfo, PtProductInfoExt, PtProductInfo>(sql, (info, ext) =>
            {
                info.Ext = ext;
                return info;
            }, param: new { fuid, fxUserId, platformType }).FirstOrDefault();
            return result;
        }
    }
}
