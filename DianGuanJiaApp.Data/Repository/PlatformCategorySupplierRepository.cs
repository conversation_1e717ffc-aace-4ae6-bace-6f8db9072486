using Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Repository.SupplierProduct;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.Net;
using MySql.Data.MySqlClient;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Z.Dapper.Plus;
using static Dapper.SqlMapper;

namespace DianGuanJiaApp.Data.Repository
{
    public partial class PlatformCategorySupplierRepository : SupplierProductBaseRepository<PlatformCategory>
    {
        public void SaveCategoryProp2(string cateId, List<PlatformCategoryProp> apiList, string platformType)
        {
            if (!apiList.Any()) return;

            var db = DbConnection;

            var s = "select * from PlatformCategoryProp where PlatformType  = @platformType and CateId = @cateId ";
            var dbList = db.Query<PlatformCategoryProp>(s, new { platformType, cateId }).ToList();
            if (apiList.Count < dbList.Count)
            {
                // ������ɾ�����ӿ���ȡ��������С��Db��˵���������Ѿ���ƽ̨��ɾ����
                var apiListIds = apiList.Select(a => a.Code);
                var dbListIds = dbList.Select(a => a.Code);

                var difference = dbListIds.Except(apiListIds).ToList();
                if (difference.Any())
                {
                    var codes = difference;   // string.Join("','", difference);
                    db.Execute("delete from PlatformCategoryProp where Code in @codes ", new { codes }, commandTimeout: 100);
                }
            }

            foreach (var item in apiList)
            {
                var dbModel = dbList.FirstOrDefault(a => a.Code == item.Code);
                if (dbModel == null)
                {
                    db.BulkInsert(item);
                }
                else
                {
                    item.Id = dbModel.Id;
                    item.UpdateTime = DateTime.Now;
                    item.CreateTime = dbModel.CreateTime;// ����ʱ���þɵ�
                    db.BulkUpdate(item);
                }
            }
        }

        public void SaveCategoryPublishRule3(PlatformCategoryPublishRule item)
        {
            var edb = DbConnection;
            var dbModel = edb.QueryFirstOrDefault<PlatformCategoryPublishRule>("select * from PlatformCategoryPublishRule where Code = @Code limit 1", new { item.Code });
            if (dbModel == null)
            {
                edb.BulkInsert(item);
            }
            else
            {
                item.Id = dbModel.Id;
                item.UpdateTime = DateTime.Now;
                item.CreateTime = dbModel.CreateTime; // ����ʱ���þɵ�
                edb.BulkUpdate(item);
            }
        }

        public List<PlatformCategory> GetShopCates(Shop shop)
        {
            return DbConnection.Query<PlatformCategory>(@"select * from PlatformCategory WHERE  PlatformType  = @PlatformType and CateId in 
(select CateId from platformcateshoprelation where ShopId = @Id)", new { shop.PlatformType, shop.Id }).ToList();
        }

        public List<PlatformCategory> GetShopCatesByPlatform(string platformType)
        {
            return DbConnection.Query<PlatformCategory>(@"select * from PlatformCategory WHERE  PlatformType  = @PlatformType and Status!='deleted' ", new { platformType }).ToList();
        }



        public void BulkDelete(List<PlatformCategory> list)
        {
            if (list == null || list.Any() == false)
                return;
            var pageSize = 500;
            var pageCount = decimal.Ceiling(list.Count / (decimal)pageSize);
            var db = this.DbConnection;
            for (int i = 0; i < pageCount; i++)
            {
                var tempList = list.Skip(i * pageSize).Take(pageSize);
                if (tempList?.Count() == 0)
                    break;
                // var codes = string.Join(",", tempList.Select(f => f.Id).Distinct());
                var codes = tempList.Select(f => f.Id).Distinct().ToList();
                var sql = "DELETE FROM PlatformCategory WHERE ID IN @codes ";
                var res = db.Execute(sql, new { codes });
            }
        }

        public new void BulkUpdate(List<PlatformCategory> addlist)
        {
            var db = this.DbConnection;
            db.BulkUpdate(addlist);
        }

        public void BulkAdd(List<PlatformCategory> addlist)
        {
            var db = this.DbConnection;
            db.BulkInsert(addlist);
        }

        public void SaveCateShopRelation(int shopId, List<PlatformCateShopRelation> list)
        {
            var sql = "DELETE FROM PlatformCateShopRelation WHERE ShopId =@shopId "; // ��������range
            var db = this.DbConnection;
            db.Execute(sql, new { shopId }); //��ɾ��
            db.BulkInsert(list); //�ٱ���
        }

        /// <summary>
        /// ��ȡ������Ŀ������
        /// </summary>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public int GetCateShopRelationsNumber(int shopId)
        {
            var sql = "SELECT count(1) Number FROM PlatformCateShopRelation WHERE ShopId = @shopId ";
            var result = DbConnection.Query<int>(sql, new { shopId }).FirstOrDefault();
            return result;
        }
    
        public void GetModelRowParser(Action<PlatformCategoryPublishRule> action)
        {
            try
            {
                var sqlstr = "SELECT * Number FROM PlatformCategoryPublishRule";
                using (var reader = DbConnection.ExecuteReader(sqlstr))
                {
                    var rowParser = reader.GetRowParser<PlatformCategoryPublishRule>();
                    while (reader.Read())
                    {
                        PlatformCategoryPublishRule model = rowParser(reader);
                        action(model);
                    }
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public void SaveNavigationCateProp(PlatformCategoryNavigation item)
        {
            var edb = DbConnection;
            var dbModel = edb.QueryFirstOrDefault<PlatformCategoryNavigation>("select * from PlatformCategoryNavigation where Code = @Code limit 1", new { item.Code });
            if (dbModel == null)
            {
                edb.BulkInsert(item);
            }
            else
            {
                item.Id = dbModel.Id;
                item.UpdateTime = DateTime.Now;
                item.CreateTime = dbModel.CreateTime; // ����ʱ���þɵ�
                edb.BulkUpdate(item);
            }
        }

        public List<PlatformCategoryPublishRule> GetList()
        {
            var sqlstr = "SELECT * FROM PlatformCategoryPublishRule";
            return DbConnection.Query<PlatformCategoryPublishRule>(sqlstr).ToList();
        }

        /// <summary>
        /// ��ѯTk�ķ�������
        /// </summary>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public PlatformCategoryPublishRule GetTkCategoryRule(string CateId)
        {
            var sql = "SELECT * FROM platformcategorypublishrule WHERE PlatformType = 'TikTok' AND CateId=@CateId ";
            var result = DbConnection.Query<PlatformCategoryPublishRule>(sql, new { CateId = CateId }).FirstOrDefault();
            return result;
        }

    }
}