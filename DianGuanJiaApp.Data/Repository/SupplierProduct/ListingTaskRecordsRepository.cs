using System;
using System.Collections.Generic;
using System.Linq;
using Dapper;
using DianGuanJiaApp.Data.Entity.BaseProduct;
using DianGuanJiaApp.Data.Entity.Collect;
using DianGuanJiaApp.Data.Entity.SupplierProduct;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.FxModel.Listing;
using DianGuanJiaApp.Data.FxModel.SupplierProduct;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Model.CrossBorder;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.Other;
using MySql.Data.MySqlClient;
using Nest;
using Z.Dapper.Plus;

namespace DianGuanJiaApp.Data.Repository.SupplierProduct
{
    /// <summary>
    /// 铺货任务仓储层
    /// </summary>
    public class ListingTaskRecordsRepository : SupplierProductBaseRepository<ListingTaskRecords>
    {
        /// <summary>
        /// 默认获取的数据库连接
        /// </summary>
        public ListingTaskRecordsRepository()
        {
        }

        /// <summary>
        /// 根据连接字符串和数据库类型获取数据库连接
        /// </summary>
        /// <param name="connectionString"></param>
        /// <param name="isUseMySql"></param>
        public ListingTaskRecordsRepository(string connectionString, bool isUseMySql) : base(connectionString, isUseMySql)
        {
        }

        /// <summary>
        /// 添加（含扩展数据）
        /// </summary>
        /// <param name="model"></param>
        public new void Add(ListingTaskRecords model)
        {
            if (model == null)
                return;

            var db = DbConnection;
            if (model.Ext != null)
            {
                if (db is MySqlConnection)
                    db.InsertMysqlWithLongId(model.Ext);
                else
                    db.Insert(model.Ext);
            }
            if (db is MySqlConnection)
                db.InsertMysqlWithLongId(model);
            else
                db.Insert(model);
        }

        /// <summary>
        /// 批量添加（含扩展数据）
        /// </summary>
        /// <param name="models"></param>
        public void BatchAdd(List<ListingTaskRecords> models)
        {
            if (models == null || models.Any() == false)
                return;

            var db = DbConnection;
            var exts = models.Where(a => a.Ext != null).Select(a => a.Ext).ToList();

            if (exts != null && exts.Any())
                db.BulkInsert(exts);

            db.BulkInsert(models);
        }

        /// <summary>
        /// 批量更新（含扩展数据）
        /// </summary>
        /// <param name="models"></param>
        public void BatchUpdate(List<ListingTaskRecords> models)
        {
            if (models == null || models.Any() == false)
                return;

            var db = DbConnection;
            var exts = models.Where(a => a.Ext != null).Select(a => a.Ext).ToList();

            if (exts != null && exts.Any())
                db.BulkUpdate(exts);

            db.BulkUpdate(models);
        }

        /// <summary>
        /// 获取单个（含扩展表数据）
        /// </summary>
        /// <param name="code"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public ListingTaskRecords GetByCode(string code, int fxUserId = 0)
        {
            var sqlTemplate = $@"SELECT * FROM ListingTaskRecords t1 WITH(NOLOCK) 
INNER JOIN ListingTaskRecordsExt ext WITH(NOLOCK) ON ext.ListingTaskCode=t1.ListingTaskCode WHERE t1.ListingTaskCode=@code";
            if (fxUserId > 0)
                sqlTemplate += " AND t1.FxUserId=@fxUserId";
            var sql = TranSql(sqlTemplate);
            var model = DbConnection.Query<ListingTaskRecords, ListingTaskRecordsExt, ListingTaskRecords>(sql, (task, ext) =>
            {
                task.Ext = ext;
                return task;
            }, param: new { code, fxUserId }).FirstOrDefault();
            return model;
        }

        /// <summary>
        /// 获取单个（含扩展表数据）的组合数据,目前就Tk用这个
        /// </summary>
        /// <param name="code"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public ListingTaskRecords GetTkRecordTotalInfoByCode(string code, int fxUserId = 0)
        {
            var sqlTemplate = $@"SELECT * FROM ListingTaskRecords t1 WITH(NOLOCK) 
INNER JOIN ListingTaskRecordsExt ext WITH(NOLOCK) ON ext.ListingTaskCode=t1.ListingTaskCode WHERE t1.ListingTaskCode=@code";
            if (fxUserId > 0)
                sqlTemplate += " AND t1.FxUserId=@fxUserId";
            var sql = TranSql(sqlTemplate);
            var model = DbConnection.Query<ListingTaskRecords, ListingTaskRecordsExt, ListingTaskRecords>(sql, (task, ext) =>
            {
                task.Ext = ext;
                task.PackJson = ext.PackJson;
                return task;
            }, param: new { code, fxUserId }).FirstOrDefault();
            return model;
        }

        /// <summary>
        /// 获取列表（含扩展表数据）
        /// </summary>
        /// <param name="codes"></param>
        /// <param name="selectFields"></param>
        /// <param name="whereFieldName"></param>
        /// <returns></returns>
        public List<ListingTaskRecords> GetList(List<string> codes, string selectFields = "*",
            string whereFieldName = "t1.ListingTaskCode")
        {
            var sqlTemplate = $@"SELECT {(string.IsNullOrWhiteSpace(selectFields) ? "*" : selectFields)} FROM ListingTaskRecords t1 WITH(NOLOCK) 
INNER JOIN ListingTaskRecordsExt ext WITH(NOLOCK) ON ext.ListingTaskCode=t1.ListingTaskCode WHERE {whereFieldName} IN @codes";
            var sql = TranSql(sqlTemplate);
            return DbConnection.Query<ListingTaskRecords, ListingTaskRecordsExt, ListingTaskRecords>(sql, (task, ext) =>
            {
                task.Ext = ext;
                return task;
            }, param: new { codes }).ToList();
        }

        /// <summary>
        /// 分页查询
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public Tuple<int, List<ListingTaskRecords>> GetPageList(ListingTaskRecordsQuery query)
        {
            var strFields = "*";
            if (query.Fields != null && query.Fields.Any())
                strFields = string.Join(",", query.Fields);

            var whereStr = $" FxUserId=@FxUserId ";
            var parameters = new DynamicParameters();
            parameters.Add("FxUserId", query.FxUserId);

            var extJoinStr = string.Empty;
            var extField = string.Empty;
            if (query.IsNeedJoinExt)
            {
                extJoinStr = " INNER JOIN ListingTaskRecordsExt ext WITH(NOLOCK) ON ext.ListingTaskCode=t1.ListingTaskCode";

                if (strFields != "*" && strFields.IndexOf("ext.") < 0)
                    strFields += ",ext.*";
            }

            if (!string.IsNullOrEmpty(query.ListingTaskCode))
            {
                whereStr += " AND t1.ListingTaskCode=@ListingTaskCode";
                parameters.Add("ListingTaskCode", query.ListingTaskCode);
            }

            #region 拼SQL

            var sql = $@"SELECT COUNT(1) FROM ListingTaskRecords t1 WITH(NOLOCK) WHERE {whereStr} ; 
SELECT {strFields} FROM ListingTaskRecords t1 WITH(NOLOCK) {extJoinStr} WHERE {whereStr} ORDER BY t1.Id DESC
OFFSET {(query.PageIndex - 1) * query.PageSize} ROWS FETCH NEXT {query.PageSize} ROWS ONLY
";
            if (IsUseMySql)
            {
                extJoinStr = TranSql(extJoinStr);
                sql = $@"SELECT COUNT(1) FROM ListingTaskRecords t1 WHERE {whereStr} ; 
SELECT {strFields} FROM ListingTaskRecords t1 {extJoinStr} WHERE {whereStr} ORDER BY t1.Id DESC
limit {(query.PageIndex - 1) * query.PageSize},{query.PageSize}
";
            }

            #endregion

            #region 记录执行的SQL语句
            if (CustomerConfig.IsDebug)
            {
                WriteSqlToLog(sql, parameters, "GetPageListSql.txt", "ListingTaskRecords", "ListingTaskRecords");
            }
            #endregion

            var db = this.DbConnection;
            var grid = db.QueryMultiple(sql, param: parameters);
            var totalCount = grid.Read<int>().FirstOrDefault();
            var result = grid.Read<ListingTaskRecords, ListingTaskRecordsExt, ListingTaskRecords>((task, ext) =>
            {
                task.Ext = ext;
                return task;
            }).ToList();

            return Tuple.Create(totalCount, result);
        }

        /// <summary>
        /// 分页查询，兼容了跨境部分
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public Tuple<int, List<ListingTaskRecords>> GetRecordPageList(ListingTaskRecordQuery query)
        {
            var strFields = "*";

            var parameters = new DynamicParameters();
            parameters.Add("FxUserId", query.FxUserId);
            var whereStr = " t1.FxUserId=@FxUserId ";
            if (query.TargetShopId > 0)
            {
                whereStr += " AND t1.TargetShopId = @TargetShopId";
                parameters.Add("TargetShopId", query.TargetShopId);
            }
            if (query.Status != null && query.Status.Any())
            {
                if (query.Status.FirstOrDefault().Equals("Error"))
                {
                    whereStr += " AND t1.Status IN ('Error', 'Error_Repeat')";
                }
                else
                {
                    whereStr += " AND t1.Status IN @Status";
                    parameters.Add("Status", query.Status);
                }
            }
            if (!string.IsNullOrWhiteSpace(query.ProductName))
            {
                whereStr += " AND t1.ProductName LIKE @ProductName";
                parameters.Add("ProductName", $"%{query.ProductName}%");
            }

            var extJoinStr = "INNER JOIN ListingTaskRecordsExt t2 ON t2.ListingTaskCode = t1.ListingTaskCode";
            var extField = ",t2.Id,t2.PackJson";

            #region 拼SQL

            var sql = $@"SELECT COUNT(1) FROM ListingTaskRecords t1 WITH(NOLOCK) {extJoinStr} WHERE {whereStr} ; 
SELECT {strFields + extField} FROM ListingTaskRecords t1 WITH(NOLOCK) {extJoinStr} WHERE {whereStr} ORDER BY t1.Id DESC
OFFSET {(query.PageIndex - 1) * query.PageSize} ROWS FETCH NEXT {query.PageSize} ROWS ONLY
";
            if (IsUseMySql)
            {
                extJoinStr = TranSql(extJoinStr);
                sql = $@"SELECT COUNT(1) FROM ListingTaskRecords t1 {extJoinStr} WHERE {whereStr} ; 
SELECT {strFields + extField} FROM ListingTaskRecords t1 {extJoinStr} WHERE {whereStr} ORDER BY t1.Id DESC
limit {(query.PageIndex - 1) * query.PageSize},{query.PageSize}
";
            }

            #endregion

            #region 记录执行的SQL语句
            if (CustomerConfig.IsDebug)
            {
                WriteSqlToLog(sql, parameters, "GetRecordPageList.txt", "ListingTaskRecords", "ListingTaskRecords");
            }
            #endregion

            var db = DbConnection;
            using (var grid = db.QueryMultiple(sql, parameters))
            {
                var totalCount = grid.Read<int>().FirstOrDefault();
                var result = grid.Read<ListingTaskRecords, ListingTaskRecordsExt, ListingTaskRecords>((task, ext) =>
                {
                    // 非跨境的判断是否自营商品
                    if (!task.TargetPlatformType.Equals("TikTok"))
                    {
                        task.FxUserId = task.FromFxUserId == task.FxUserId ? task.FxUserId : task.FromFxUserId;
                    }

                    return task;
                }, "Id").ToList();

                return Tuple.Create(totalCount, result);
            }
        }


        /// <summary>
        /// 更新以下字段：BusinessStatus、ProcessBusinessTime、NextProcessBusinessTime、ErrorMessage
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public int UpdateBusinessStatus(ListingTaskRecords model)
        {
            var sql = $"UPDATE ListingTaskRecords SET BusinessStatus=@status,ProcessBusinessTime=@time,NextProcessBusinessTime=@nextTime,ErrorMessage=@message WHERE ListingTaskCode=@code";
            return DbConnection.Execute(sql, new { code = model.ListingTaskCode, status = model.BusinessStatus, time = model.ProcessBusinessTime, nextTime = model.NextProcessBusinessTime, message = model.ErrorMessage });
        }

        /// <summary>
        /// 更新以下字段：BusinessProcessResult
        /// </summary>
        /// <param name="code"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public int UpdateExtBusinessProcessResult(string code, string message)
        {
            var sql = $"UPDATE ListingTaskRecordsExt SET BusinessProcessResult=@message WHERE ListingTaskCode=@code";
            return DbConnection.Execute(sql, new { code, message });
        }

        /// <summary>
        /// 获取铺货异常任务
        /// </summary>
        /// <returns></returns>
        public List<ListingTaskRecords> GetCompensateTasks(int retryTime)
        {
            var now = DateTime.Now;
            var nowStr = now.ToString("yyyy-MM-dd HH:mm:ss");
            var pageCommandText = $" offset 0 rows fetch next 100 row only ";
            if (IsUseMySql)
            {
                pageCommandText = $" limit 0,100";
            }
            var sql = $@"SELECT * FROM listingtaskrecords WITH(NOLOCK) WHERE IsDeleted=0 
AND Status='Success' 
AND FinishedTime IS NOT NULL
AND (
(BusinessStatus=0 AND DATE_ADD(FinishedTime,INTERVAL 10 MINUTE)<'{nowStr}') OR
(BusinessStatus=1 AND DATE_ADD(ProcessBusinessTime,INTERVAL 30 MINUTE)<'{nowStr}') OR
(BusinessStatus=-1 AND NextProcessBusinessTime IS NOT NULL  AND NextProcessBusinessTime<'{nowStr}' AND BusinessRetryCount<@retryTime )
)ORDER BY NextProcessBusinessTime ASC {pageCommandText}";
            sql = TranSql(sql);
            var parameters = new DynamicParameters();
            parameters.Add("@retryTime", retryTime);
            var aa = GetRealSql(sql, parameters);
            var res = DbConnection.Query<ListingTaskRecords>(sql, parameters).ToList();
            return res;
        }

        /// <summary>
        /// 更新铺货异常任务
        /// </summary>
        /// <param name="tasks"></param>
        /// <param name="interval"></param>
        /// <returns></returns>
        public bool UpdateCompensateTasks(List<ListingTaskRecords> tasks, int interval)
        {
            foreach (var task in tasks)
            {
                task.BusinessRetryCount = task.BusinessRetryCount + 1;
                task.BusinessStatus = -1;
                task.ProcessBusinessTime = DateTime.Now;
                task.NextProcessBusinessTime = DateTime.Now.AddMinutes(interval);
                //if (task.NextProcessBusinessTime.HasValue)
                //    task.NextProcessBusinessTime = task.NextProcessBusinessTime.Value.AddMinutes(interval);
                //else
                //    task.NextProcessBusinessTime = DateTime.Now.AddMinutes(interval);
            }
            var updateFields = new List<string> { "BusinessRetryCount", "BusinessStatus", "ProcessBusinessTime", "NextProcessBusinessTime" };
            var updateData = tasks;
            var a = BulkUpdateByParam(updateData, updateFields);
            return true;
        }

        /// <summary>
        /// 获取非跨境的已存在的任务店铺Id
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public List<int> GetExistShopIds(int fxUserId)
        {
            var sql = "SELECT DISTINCT TargetShopId FROM ListingTaskRecords WHERE FxUserId=@FxUserId AND (TargetPlatformType IS NOT NULL AND TargetPlatformType != 'TikTok')";
            var res = DbConnection.Query<int>(sql, new { FxUserId = fxUserId }).ToList();
            return res;
        }

        /// <summary>
        /// 获取跨境的已存在的任务店铺Id
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public List<int> GetForeignExistShopIds(int fxUserId)
        {
            var sql = "SELECT DISTINCT TargetShopId FROM ListingTaskRecords WHERE FxUserId=@FxUserId AND TargetPlatformType ='TikTok'";
            var res = DbConnection.Query<int>(sql, new { FxUserId = fxUserId }).ToList();
            return res;
        }


        /// <summary>
        /// 查询任务状态
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public List<ListingTaskRecords> GetTaskStatus(ListingTaskStatusQuery model)
        {
            var sql = "SELECT Id,ListingTaskCode,BatchNo,FxUserId,Status,BusinessStatus FROM ListingTaskRecords WITH(NOLOCK) WHERE FxUserId=@FxUserId AND BatchNo=@BatchNo";
            if (string.IsNullOrEmpty(model.TaskCode) == false)
                sql = "SELECT Id,ListingTaskCode,BatchNo,FxUserId,Status,BusinessStatus FROM ListingTaskRecords WITH(NOLOCK) WHERE FxUserId=@FxUserId AND ListingTaskCode=@TaskCode";
            sql = TranSql(sql);
            var result = DbConnection.Query<ListingTaskRecords>(sql, model).ToList();
            return result;
        }

        /// <summary>
        /// 根据来源找到铺货记录
        /// </summary>
        /// <param name="shopId"></param>
        /// <param name="fxUserId"></param>
        /// <param name="fromSupplierProductUid"></param>
        /// <param name="fromBaseProductUid"></param>
        /// <returns></returns>
        public List<PtProductInfo> GetByProductModel(int shopId, int fxUserId, long fromSupplierProductUid, long fromBaseProductUid)
        {
            // var sqlTemplate = $@"SELECT * FROM PtProductInfo WHERE FxUserId=@fxUserId and ShopId=@shopId AND Status<>0 ";

            var sqlTemplate = $@"SELECT A.* FROM PtProductInfo A
left  join ListingTaskRecords B on A.UniqueCode = B.PtProductUniqueCode
WHERE A.FxUserId=@fxUserId AND A.Status<>0 and B.ShopId=@shopId and B.Status not in ('Error','Error_Repeat') ";

            string sqlwhere = string.Empty;
            if (fromSupplierProductUid > 0)
                sqlwhere += " and A.FromSupplierProductUid=@fromSupplierProductUid";

            if (fromBaseProductUid > 0)
                sqlwhere += " and A.FromBaseProductUid=@fromBaseProductUid";

            var sql = TranSql(sqlTemplate + sqlwhere);

            var dy = new DynamicParameters();
            dy.Add("@fxUserId", fxUserId);
            dy.Add("@fromSupplierProductUid", fromSupplierProductUid);
            dy.Add("@fromBaseProductUid", fromBaseProductUid);
            dy.Add("@shopId", shopId);

            var aa = GetRealSql(sql, dy);
            var list = DbConnection.Query<PtProductInfo>(sql, dy).ToList();
            return list;
        }

        public int UpdateListingProductStatus(ListingTaskRecords model)
        {
            var sql = $"UPDATE ListingTaskRecords set ListingProductStatus = @ListingProductStatus WHERE ListingTaskCode=@code";
            return DbConnection.Execute(sql, new { code = model.ListingTaskCode, model.ListingProductStatus });
        }

        /// <summary>
        /// 根据来源找到铺货记录
        /// </summary>
        /// <returns></returns>
        public bool ExistListingTaskRecords(int shopId, List<string> ptProductUniqueCode, bool islog = false)
        {
            if (!ptProductUniqueCode.Any()) return false;
            var sql = $@"SELECT count(1) from ListingTaskRecords B where B.PtProductUniqueCode in @ptProductUniqueCode and B.ShopId = @shopId and B.Status not in ('Error','Error_Repeat')";

            var dy = new DynamicParameters();
            dy.Add("@ptProductUniqueCode", ptProductUniqueCode);
            dy.Add("@shopId", shopId);

            var aa = GetRealSql(sql, dy);

            if (islog) Log.WriteLine($"【或盘库】查找铺货日志：{aa}", "CheckRepeatPtListing.txt");
            var result = DbConnection.QueryFirstOrDefault<int>(sql, dy);
            return result > 0;
        }
    }
}