namespace DianGuanJiaApp.Data.Enum
{
    public struct CacheKeys
    {
        /// <summary>
        /// winform 业务程序 心跳Key
        /// </summary>
        public const string WinFormHeartBeatHSetKey = "DianGuanJiaApp:Winforms";

        #region 监控平台(2025-03)

        /// <summary>
        /// 服务运行总Key
        /// </summary>
        public const string ServiceRunStatusKey = "DianGuanJia:FenDan:ServiceRunStatusHS";

        /// <summary>
        /// 服务开关
        /// </summary>
        public const string ServiceSwitchKey = "DianGuanJia:FenDan:{HostName}:{ServiceName}:Switch";

        #endregion

        #region 增量同步相关键(可配置环境，需要多加一个冒号)

        /// <summary>
        /// 增量同步键
        /// </summary>
        public const string SyncQueueKey = "DianGuanJia:FenXiao{Environment}:{PlatformType}:{SyncBusinessType}";

        /// <summary>
        /// 订单增量同步计数器键
        /// </summary>
        public const string SyncCounterKey =
            "DianGuanJia:FenXiao{Environment}:{PlatformType}:{SyncBusinessType}:Counter";

        /// <summary>
        /// 订单增量同步状态
        /// </summary>
        public const string SyncStatusKey =
            "DianGuanJia:FenXiao{Environment}:{PlatformType}:{SyncBusinessType}:Status:{ShopId}";

        /// <summary>
        /// 订单增量同步服务开关
        /// </summary>
        public const string SyncSwitchKey =
            "DianGuanJia:FenXiao{Environment}:{SyncBusinessType}Switch:{HostName}:{ServiceNo}";

        /// <summary>
        /// 订单增量同步服务并发数
        /// </summary>
        public const string SyncConcurrencyKey =
            "DianGuanJia:FenXiao{Environment}:{SyncBusinessType}:{HostName}:Concurrency";

        /// <summary>
        /// 处理中计数器
        /// </summary>
        public const string SyncProcessingCounterKey =
            "DianGuanJia:FenXiao{Environment}:{PlatformType}:{SyncBusinessType}:ProcessingCounter";

        /// <summary>
        /// 订单处理中任务
        /// </summary>
        public const string SyncProcessingKey =
            "DianGuanJia:FenXiao{Environment}:{PlatformType}:{SyncBusinessType}:Processing";

        /// <summary>
        /// 订单增量同步服务开关
        /// </summary>
        public const string SyncSwitchNewKey =
            "DianGuanJia:FenXiao{Environment}:{SyncBusinessType}:Switch:{HostName}";

        /// <summary>
        /// 订单增量同步服务并发数
        /// </summary>
        public const string SyncConcurrencyNewKey = "DianGuanJia:FenXiao{Environment}:{SyncBusinessType}:Concurrency";

        /// <summary>
        /// 订单增量同步服务并发数
        /// </summary>
        public const string SyncAutoRestartLockKey =
            "DianGuanJia:FenXiao{Environment}:{SyncBusinessType}:{HostName}:AutoRestartLock";

        /// <summary>
        /// 同步任务队列锁
        /// </summary>
        public const string SyncQueueLockKey = "DianGuanJia:FenXiao{Environment}:{SyncBusinessType}:SyncQueueLock";

        #endregion

        #region 路径流复制副本键

        public const string PathFlowDuplicationQueueKey = "DianGuanJia:FenXiao:PathFlowDuplication:Queue";

        public const string PathFlowDuplicationCounterKey = "DianGuanJia:FenXiao:PathFlowDuplication:Counter";

        public const string PathFlowDuplicationSwitchKey = "DianGuanJia:FenXiao:PathFlowDuplication:Switch:{HostName}";

        public const string PathFlowDuplicationLockKey = "DianGuanJia:FenXiao:PathFlowDuplication:Lock";

        public const string PathFlowDuplicationConcurrencyKey =
            "DianGuanJia:FenXiao:PathFlowDuplication:Concurrency";

        #endregion

        #region 路径流引用复制副本键

        public const string PathFlowReferenceDuplicationQueueKey =
            "DianGuanJia:FenXiao:PathFlowReferenceDuplication:Queue";

        public const string PathFlowReferenceDuplicationCounterKey =
            "DianGuanJia:FenXiao:PathFlowReferenceDuplication:Counter";

        public const string PathFlowReferenceDuplicationSwitchKey =
            "DianGuanJia:FenXiao:PathFlowReferenceDuplication:Switch:{HostName}";

        public const string PathFlowReferenceDuplicationLockKey =
            "DianGuanJia:FenXiao:PathFlowReferenceDuplication:Lock";

        public const string PathFlowReferenceDuplicationConcurrencyKey =
            "DianGuanJia:FenXiao:PathFlowReferenceDuplication:Concurrency";

        #endregion

        #region 商品复制副本键

        public const string ProductDuplicationQueueKey = "DianGuanJia:FenXiao:ProductDuplication:Queue";

        public const string ProductDuplicationCounterKey = "DianGuanJia:FenXiao:ProductDuplication:Counter";

        public const string ProductDuplicationSwitchKey = "DianGuanJia:FenXiao:ProductDuplication:Switch:{HostName}";

        public const string ProductDuplicationLockKey = "DianGuanJia:FenXiao:ProductDuplication:Lock";

        public const string ProductDuplicationConcurrencyKey =
            "DianGuanJia:FenXiao:ProductDuplication:Concurrency";

        #endregion

        #region 逻辑单复制副本键

        public const string LogicOrderDuplicationQueueKey = "DianGuanJia:FenXiao:LogicOrderDuplication:Queue";

        public const string LogicOrderDuplicationCounterKey = "DianGuanJia:FenXiao:LogicOrderDuplication:Counter";

        public const string LogicOrderDuplicationSwitchKey =
            "DianGuanJia:FenXiao:LogicOrderDuplication:Switch:{HostName}";

        public const string LogicOrderDuplicationLockKey = "DianGuanJia:FenXiao:LogicOrderDuplication:Lock";

        #endregion

        #region 售后单复制副本键

        public const string AfterSaleOrderDuplicationQueueKey = "DianGuanJia:FenXiao:AfterSaleOrderDuplication:Queue";

        public const string AfterSaleOrderDuplicationCounterKey =
            "DianGuanJia:FenXiao:AfterSaleOrderDuplication:Counter";

        public const string AfterSaleOrderDuplicationSwitchKey =
            "DianGuanJia:FenXiao:AfterSaleOrderDuplication:Switch:{HostName}";

        public const string AfterSaleOrderDuplicationLockKey = "DianGuanJia:FenXiao:AfterSaleOrderDuplication:Lock";

        public const string AfterSaleOrderDuplicationConcurrencyKey =
            "DianGuanJia:FenXiao:AfterSaleOrderDuplication:Concurrency";

        #endregion

        #region 订单项复制副本键

        public const string OrderItemDuplicationQueueKey = "DianGuanJia:FenXiao:OrderItemDuplication:Queue";

        public const string OrderItemDuplicationCounterKey = "DianGuanJia:FenXiao:OrderItemDuplication:Counter";

        public const string OrderItemDuplicationSwitchKey =
            "DianGuanJia:FenXiao:OrderItemDuplication:Switch:{HostName}";

        public const string OrderItemDuplicationLockKey = "DianGuanJia:FenXiao:OrderItemDuplication:Lock";

        public const string OrderItemDuplicationConcurrencyKey =
            "DianGuanJia:FenXiao:OrderItemDuplication:Concurrency";

        #endregion

        #region 订单审核复制副本键

        public const string OrderCheckDuplicationQueueKey = "DianGuanJia:FenXiao:OrderCheckDuplication:Queue";

        public const string OrderCheckDuplicationCounterKey = "DianGuanJia:FenXiao:OrderCheckDuplication:Counter";

        public const string OrderCheckDuplicationSwitchKey =
            "DianGuanJia:FenXiao:OrderCheckDuplication:Switch:{HostName}";

        public const string OrderCheckDuplicationLockKey = "DianGuanJia:FenXiao:OrderCheckDuplication:Lock";

        public const string OrderCheckDuplicationConcurrencyKey =
            "DianGuanJia:FenXiao:OrderCheckDuplication:Concurrency";

        #endregion

        #region 采购单关系复制副本键

        public const string PurchaseOrderRelationDuplicationQueueKey =
            "DianGuanJia:FenXiao:PurchaseOrderRelationDuplication:Queue";

        public const string PurchaseOrderRelationDuplicationCounterKey =
            "DianGuanJia:FenXiao:PurchaseOrderRelationDuplication:Counter";

        public const string PurchaseOrderRelationDuplicationSwitchKey =
            "DianGuanJia:FenXiao:PurchaseOrderRelationDuplication:Switch:{HostName}";

        public const string PurchaseOrderRelationDuplicationLockKey =
            "DianGuanJia:FenXiao:PurchaseOrderRelationDuplication:Lock";

        #endregion

        #region 调用API日志开关

        public const string InvokeApiDataLogSwitch = "DianGuanJia:FenXiao:InvokeApiDataLog:Switch:{PlatformType}";
        /// <summary>
        /// 请求API日志总是开启开关
        /// </summary>
        public const string InvokeApiDataLogAlwaysSwitch = "DianGuanJia:FenXiao:InvokeApiDataLog:AlwaysSwitch:{PlatformType}";

        #endregion

        #region 调用订单发货API日志开关

        public const string OrderSendInvokeApiDataSwitch =
            "DianGuanJia:FenXiao:OrderSendInvokeApiDataLog:Switch:{PlatformType}";

		#endregion

		#region 调用订单取号API日志开关
		public const string OrderWaybillCodeInvokeApiDataSwitch = "DianGuanJia:FenXiao:OrderWaybillCodeInvokeApiDataLog:Switch:{PlatformType}";
		#endregion

		#region 同步订单时间分块日志开关

		public const string SyncOrderTimeChunkLogSwitch =
            "DianGuanJia:FenXiao:SyncOrderTimeChunkLog:Switch:{PlatformType}";

        #endregion

        #region 复制副本推送任务开关

        public const string DataDuplicationPushTaskSwitch = "DianGuanJia:FenXiao:DataDuplicationPushTask:Switch";

        #endregion

        #region 逻辑单写入日志开关

        public const string LogicOrderInsertLogSwitch =
            "DianGuanJia:FenXiao:LogicOrderInsertLog:Switch:{OperationType}";

        #endregion

        #region 采购单发货

        /// <summary>
        /// 采购单发货队列
        /// </summary>
        public const string PurchaseOrderSendQueueKey = "DianGuanJia:FenXiao:PurchaseOrderSend:Queue";

        /// <summary>
        /// 采购单发货服务开关
        /// </summary>
        public const string PurchaseOrderSendSwitchKey =
            "DianGuanJia:FenXiao:PurchaseOrderSend:{HostName}:{ServiceNo}:Switch";

        /// <summary>
        /// 采购单发货服务并发数
        /// </summary>
        public const string PurchaseOrderSendConcurrencyKey =
            "DianGuanJia:FenXiao:PurchaseOrderSend:{HostName}:{ServiceNo}:Concurrency";

        /// <summary>
        /// 处理中
        /// </summary>
        public const string PurchaseOrderSendProcessingCounterKey =
            "DianGuanJia:FenXiao:PurchaseOrderSend:{HostName}:{ServiceNo}:ProcessingCounter";

        /// <summary>
        /// 成功数
        /// </summary>
        public const string PurchaseOrderSendSuccessCounterKey =
            "DianGuanJia:FenXiao:PurchaseOrderSend:{HostName}:{ServiceNo}:SuccessCounter";

        /// <summary>
        /// 失败数
        /// </summary>
        public const string PurchaseOrderSendFaildCounterKey =
            "DianGuanJia:FenXiao:PurchaseOrderSend:{HostName}:{ServiceNo}:FaildCounter";

        #endregion

        #region 业务日志开关

        /// <summary>
        /// 业务日志开关
        /// </summary>
        public const string BusinessLogSwitch = "DianGuanJia:FenXiao:BusinessLog:Switch:{BusinessType}";

        #endregion

        #region 同步链路开关

        /// <summary>
        /// 链路批次日志开关
        /// </summary>
        public const string TraceBatchLogSwitch = "DianGuanJia:FenXiao:TraceLog:Switch:TraceBatch";

        /// <summary>
        /// 链路监控数据日志开关
        /// </summary>
        public const string TraceDataLogSwitch = "DianGuanJia:FenXiao:TraceLog:Switch:TraceData";

        /// <summary>
        /// 链路批次日志开关
        /// </summary>
        public const string TraceBatchLogSwitchKey = "DianGuanJia:FenXiao:TraceLog:Switch:TraceBatch:{PlatformType}";

        /// <summary>
        /// 链路监控数据日志开关
        /// </summary>
        public const string TraceDataLogSwitchKey = "DianGuanJia:FenXiao:TraceLog:Switch:TraceData:{PlatformType}";

        #endregion

        #region 1688创建采购单锁键

        public const string AlibabaCreatePurchaseOrderLockKey = "DianGuanJia:FenXiao:CreatePurchaseOrder:Lock:{Id}";

        #endregion

        #region 订单审核推送服务队列键

        public const string OrderCheckRuleToServiceQueueKey =
            "DianGuanJia:FenXiao:OrderCheckRuleToServiceQueueKey:Queue";

        #endregion

        #region 基于消息数据同步键

        /// <summary>
        /// 基于消息数据同步处理情况汇总（Redis 哈希数据类型）
        /// </summary>
        public const string MessageDataSyncStorageCounterKey =
            "DianGuanJia:FenXiao{Environment}:{PlatformType}:MessageDataSync:{BusinessType}:Counter";

        /// <summary>
        /// 基于消息数据同步处理中任务（Redis 哈希数据类型）
        /// </summary>
        public const string MessageDataSyncStorageProcessingKey =
            "DianGuanJia:FenXiao{Environment}:{PlatformType}:MessageDataSync:{BusinessType}:Processing";

        /// <summary>
        /// 基于消息数据同步最大剩余数键
        /// </summary>
        public const string MessageDataSyncStorageMaxRemaindersKey =
            "DianGuanJia:FenXiao{Environment}:{PlatformType}:MessageDataSync:{BusinessType}:MaxRemainders";

        #endregion

        #region 发货相关

        /// <summary>
        /// 发货请求ID标识
        /// </summary>
        public const string OnlineSendRequestIdKey = "DianGuanJia.FenXiao.OnlineSend.RequestIds:{RequestId}";


        /// <summary>
        /// 发货订单异步任务Key
        /// </summary>
        public const string FendanAsyncOSRequestKey = "Fendan:AsyncOSRequest:RequestId:{RequestId}";

        #endregion

        #region 是否开启紧急解绑隐藏店铺订单键

        public const string IsUnbindHideShopOrderSwitch = "DianGuanJia:FenXiao:IsUnbindHideShopOrder:Switch";

        /// <summary>
        /// 发货保存发货历史锁（SendHistoryCode）
        /// </summary>
        public const string OnlineSendSaveSendHistoryLockKey =
            "DianGuanJia.FenXiao.OnlineSend.SaveSendHistory.Lock:{Code}";

        #endregion


        #region 跨境功能 配置Key

        /// <summary>
        /// 跨境功能开启全局开关
        /// </summary>
        public const string CrossBorderGlobalEnabledKey = "DianGuanJia:FenXiao:CrossBorder:Enabled:Global";

        /// <summary>
        /// 跨境功能开启白名单 shopid:-1
        /// </summary>
        public const string CrossBorderEnabledBySysSidKey = "DianGuanJia:FenXiao:CrossBorder:Enabled:SysSid";

        #endregion

        public const string ClearDataChangeLogKey = "DianGuanJia:FenXiao:ClearDataChangeLog";

        #region 乐观锁控制

        /// <summary>
        /// 乐观锁转换开关键
        /// </summary>
        public const string OptimisticLockModeSwitchKey =
            "DianGuanJia:FenDan:{CloudPlatformType}:OptimisticLock:{OptimisticLockOperationType}:ModeSwitch";

        /// <summary>
        /// 乐观锁键
        /// </summary>
        public const string OptimisticLockKey =
            "DianGuanJia:FenDan:{CloudPlatformType}:OptimisticLock:{OptimisticLockOperationType}:{OperationId}";

        #endregion

        /// <summary>
        /// 超过更新笔数使用临时表
        /// </summary>
        public const string UseTempTableOutUpdateCountKey = "DianGuanJia:FenDan:UseTempTableOutUpdateCount";

        #region 基础商品Sku自动绑定相关

        /// <summary>
        /// 自动关联绑定基础商品Sku
        /// </summary>
        public const string BaseProductAutoRelationBindKey =
            "DianGuanJia:FenXiao:BaseProductAutoRelationBind:{FxUserId}";

        /// <summary>
        /// 自动关联绑定基础商品Sku任务进度
        /// </summary>
        public const string BaseProductAutoRelationTaskProgressKey =
            "DianGuanJia:FenXiao:BaseProductAutoRelationTaskProgress:{FxUserId}";

        #endregion

        #region 导出任务相关键

        /// <summary>
        /// 导出任务队列计数键
        /// </summary>
        public const string ExportTaskQueueCountKey = "DianGuanJia:FenXiao:ExportTaskQueue:Count:{version}";

        /// <summary>
        /// 对账任务队列计数键
        /// </summary>
        public const string SettlementTaskQueueCountKey = "DianGuanJia:FenXiao:SettlementTaskQueue:Count:{version}";

        /// <summary>
        /// 导出任务查询键
        /// </summary>
        public const string ExportTaskQueryKey = "FxExportTask:{ExportType}:{FxUserId}";

        #endregion

        #region 开放平台缓存配置Key

        /// <summary>
        /// 开放平台应用缓存配置
        /// </summary>
        public const string OpenPlatformAppCacheKey = "DianGuanJia:FenXiao:OpenPlatform:App:{appkey}";

        /// <summary>
        /// 开放平台权限缓存配置
        /// </summary>
        public const string OpenPlatformPermissionCacheKey = "DianGuanJia:FenXiao:OpenPlatform:Permission:{uniqueCode}";

        /// <summary>
        /// 开放平台token缓存配置
        /// </summary>
        public const string OpenPlatformTokenCacheKey = "DianGuanJia:FenXiao:OpenPlatform:Token:{token}";

        /// <summary>
        /// 开放平台api限流缓存
        /// </summary>
        public const string OpenPlatformApiRateLimitCacheKey = "DianGuanJia:FenXiao:OpenPlatform:RateLimit:{appKey}";

        #endregion

        /// <summary>
        /// 抖店品牌Key
        /// </summary>
        public const string DdShopBrandKey = "Fendan:GetShopBrand:{Sid}:{Cid}";

        /// <summary>
        /// 创建平台资料锁的Key
        /// {Id}={ptProductInfo.BaseProductUid}/{ptProductInfo.FxUserId}/{ptProductInfo.PlatformType}/{ptProductInfo.ShopId}
        /// </summary>
        public const string CreatePtProductInfoLockKey = "DianGuanJia:FenXiao:CreatePtProductInfo:Lock:{Id}";

        /// <summary>
        /// 使用新订单导出方法
        /// </summary>
        public const string UseNewOrderExportMethodKey = "DianGuanJia:FenXiao:UseNewOrderExportMethod";

        /// <summary>
        /// 基础商品SKu基础字段
        /// </summary>
        public const string BaseProductSkuSimple = "Fendan:BaseProductSkuSimple:{Uid}";

        /// <summary>
        /// 店铺类目发布权限校验Key
        /// </summary>
        public const string ShopCateAuth = "Fendan:CateAuthCheck:{Sid}:{Cid}";

        #region 站内信

        /// <summary>
        ///站内信推送队列
        /// </summary>
        public const string SiteMessageSendQueueKey = "DianGuanJia:FenXiao:SiteMessageSend:Queue";

        /// <summary>
        /// 站内信服务开关
        /// </summary>
        public const string SiteMessageSendSwitchKey = "DianGuanJia:FenXiao:SiteMessageSend:{HostName}:Switch";

        /// <summary>
        /// 站内信并发数
        /// </summary>
        public const string SiteMessageSendConcurrencyKey =
            "DianGuanJia:FenXiao:SiteMessageSend:{HostName}:Concurrency";

        /// <summary>
        /// 处理中
        /// </summary>
        public const string SiteMessageSendProcessingCounterKey =
            "DianGuanJia:FenXiao:SiteMessageSend:{HostName}:ProcessingCounter";

        /// <summary>
        /// 成功数
        /// </summary>
        public const string SiteMessageSendSuccessCounterKey =
            "DianGuanJia:FenXiao:SiteMessageSend:{HostName}:SuccessCounter";

        /// <summary>
        /// 失败数
        /// </summary>
        public const string SiteMessageSendFaildCounterKey =
            "DianGuanJia:FenXiao:SiteMessageSend:{HostName}:FaildCounter";

        #endregion


        #region 订单同步分析

        /// <summary>
        /// 订单同步分析锁
        /// </summary>
        public const string OrderSyncAnalysisLockKey = "DianGuanJia:FenDan:OrderSyncAnalysis:Lock";

        /// <summary>
        /// 订单同步分析队列
        /// </summary>
        public const string OrderSyncAnalysisQueueKey = "DianGuanJia:FenDan:OrderSyncAnalysis:Queue";

        /// <summary>
        /// 订单同步分析并发数控制
        /// </summary>
        public const string OrderSyncAnalysisConcurrencyKey = "DianGuanJia:FenDan:OrderSyncAnalysis:Concurrency";

        /// <summary>
        /// 订单同步分析推送任务锁（按日期，过期时间7小时）
        /// </summary>
        public const string OrderSyncAnalysisPushTaskLock = "DianGuanJia:FenDan:OrderSyncAnalysis:PushTask:Lock";

        /// <summary>
        /// 订单同步分析通知锁（按日期，过期时间4小时）
        /// </summary>
        public const string OrderSyncAnalysisNoticeLock = "DianGuanJia:FenDan:OrderSyncAnalysis:Notice:Lock";

        /// <summary>
        /// 订单同步分析最后同步分钟（默认30分钟）
        /// </summary>
        public const string OrderSyncAnalysisOnlyCompensateLastSyncMinutes =
            "DianGuanJia:FenDan:OrderSyncAnalysis:OnlyCompensateLastSyncMinutes";

        #endregion

        #region 订单同步分析后触发同步

        /// <summary>
        /// 订单同步分析后触发同步锁
        /// </summary>
        public const string OrderSyncAnalysisTriggerSyncLockKey =
            "DianGuanJia:FenDan:OrderSyncAnalysis:TriggerSync:Lock";

        /// <summary>
        /// 订单同步分析后触发同步队列
        /// </summary>
        public const string OrderSyncAnalysisTriggerSyncQueueKey =
            "DianGuanJia:FenDan:OrderSyncAnalysis:TriggerSync:Queue";

        /// <summary>
        /// 触发同步任务的并发数
        /// </summary>
        public const string OrderSyncAnalysisTriggerSyncConcurrencyKey =
            "DianGuanJia:FenDan:OrderSyncAnalysis:TriggerSync:Concurrency";

        /// <summary>
        /// 触发同步任务最大剩余数任务数
        /// </summary>
        public const string OrderSyncAnalysisTriggerSyncMaxRemainderTasksKey =
            "DianGuanJia:FenDan:OrderSyncAnalysis:TriggerSync:MaxRemainderTasks";

        #endregion

        #region 订单同步分析自动原因分析

        /// <summary>
        /// 订单同步分析自动原因分析锁
        /// </summary>
        public const string OrderSyncAnalysisAutoReasonLockKey =
            "DianGuanJia:FenDan:OrderSyncAnalysis:AutoReasonAnalysis:Lock";

        /// <summary>
        /// 订单同步分析自动原因分析队列
        /// </summary>
        public const string OrderSyncAnalysisAutoReasonQueueKey =
            "DianGuanJia:FenDan:OrderSyncAnalysis:AutoReasonAnalysis:Queue";

        /// <summary>
        /// 订单同步分析自动原因分析并发数控制
        /// </summary>
        public const string OrderSyncAnalysisAutoReasonConcurrencyKey =
            "DianGuanJia:FenDan:OrderSyncAnalysis:AutoReasonAnalysis:Concurrency";

        #endregion

        #region 推送库授权检查

        /// <summary>
        /// 推送库授权检查队列（后续可以将 Taobao 缓存平台 {PlatformType}）
        /// </summary>
        public const string PushDbAuthCheckQueueKey = "DianGuanJia:FenXiao:PushDbAuthCheck:{PlatformType}:Queue";

        /// <summary>
        /// 推送库检查
        /// </summary>
        public const string PushDbAuthCheckLockKey = "DianGuanJia:FenXiao:PushDbAuthCheck:{PlatformType}:Lock";

        #endregion

        #region 微信触发订单同步分析

        /// <summary>
        /// 微信触发同步订单同步分析锁
        /// </summary>
        public const string OrderSyncAnalysisWeiXinTriggerSyncLockKey =
            "DianGuanJia:FenDan:OrderSyncAnalysis:WeiXinTriggerSync:Lock";

        /// <summary>
        /// 微信触发同步订单同步分析队列
        /// </summary>
        public const string OrderSyncAnalysisWeiXinTriggerSyncQueueKey =
            "DianGuanJia:FenDan:OrderSyncAnalysis:WeiXinTriggerSync:Queue";

        #endregion

        #region 数据归档相关


        /// <summary>
        /// 永久清理
        /// </summary>
        public const string DataArchiveByCleanKey = "DianGuanJia:FenXiao:DataArchive:ByClean:{BusinessName}";

        /// <summary>
        /// 按时间归档
        /// </summary>
        public const string DataArchiveByTimeBackupDbKey =
            "DianGuanJia:FenXiao:DataArchive:ByTimeBackupDb:{BusinessName}";

        /// <summary>
        /// 按对账归档
        /// </summary>
        public const string DataArchiveBySettlementBillKey = "DianGuanJia:FenXiao:DataArchive:BySettlementBill";

        /// <summary>
        /// 按对账归档清理
        /// </summary>
        public const string DataArchiveBySettlementBillClearKey =
            "DianGuanJia:FenXiao:DataArchive:BySettlementBillClear";

        /// <summary>
        /// 按僵尸用户归档
        /// </summary>
        public const string DataArchiveByExpiredUserKey = "DianGuanJia:FenXiao:DataArchive:ByExpiredUser";

        /// <summary>
        /// 配置库库数据归档
        /// </summary>
        public const string ConfigDbDataArchiveLockKey = "DianGuanJia:FenXiao:DataArchive:ConfigDbDataArchive:Lock";

        /// <summary>
        /// 配置项清理
        /// </summary>
        public const string DataArchiveByCommonSettingClearKey =
            "DianGuanJia:FenXiao:DataArchive:CommonSettingClear:{BusinessName}";

        /// <summary>
        /// CommonSetting 非法字符串
        /// </summary>
        public const string CommonSettingKeyIllegalCharactersKey = "DianGuanJia:FenXiao:CommonSetting:KeyIllegalCharacters";
        
        /// <summary>
        /// CommonSetting 非法字符归档键
        /// </summary>
        public const string DataArchiveBySettingIllegalCharacterKey = "DianGuanJia:FenXiao:DataArchive:SettingIllegalCharacter";

        #endregion

        #region 监控检测最大剩余数

        /// <summary>
        /// 监控检测最大检测数
        /// </summary>
        public const string SyncServiceHealthMonitorMaxRemaindersKey =
            "DianGuanJia:FenXiao{Environment}:HealthMonitor:SyncService:MaxRemainders";

        #endregion

        #region 乐观锁日志开关

        /// <summary>
        /// 乐观锁日志开关
        /// </summary>
        public const string OptimisticLockLogSwitch = "DianGuanJia:FenXiao:OptimisticLockLog:Switch";

        #endregion

        #region 同步服务任务日志开关

        /// <summary>
        /// 同步服务任务日志开关
        /// </summary>
        public const string SyncServiceTaskLogSwitch = "DianGuanJia:FenXiao:SyncServiceTaskLog:Switch";

        #endregion

        /// <summary>
        /// 聚好麦同步订单分页等待时间（单位毫秒）
        /// </summary>
        public const string JuHaoMaiSyncOrderPageWaitTime = "DianGuanJia:FenXiao:JuHaoMaiSyncOrderPageWaitTime";

        #region 更换手机号

        /// <summary>
        /// 旧手机号获取验证码次数（有效期24小时）
        /// 注意子账号的 fxUserId 有一个前缀【sub】
        /// 例：DianGuanJia:FenXiao:ReplaceMobile:OldValidCode:sub_1_13800138000
        /// </summary>
        public const string ReplaceMobileOldValidCodeKey =
            "DianGuanJia:FenXiao:ReplaceMobile:OldValidCode:{fxUserId}_{mobile}";

        /// <summary>
        /// 新手机号获取验证码次数（有效期24小时）
        /// 注意子账号的 fxUserId 有一个前缀【sub】
        /// 例：例：DianGuanJia:FenXiao:ReplaceMobile:NewValidCode:sub_1_13800138000
        /// </summary>
        public const string ReplaceMobileNewValidCodeKey =
            "DianGuanJia:FenXiao:ReplaceMobile:NewValidCode:{fxUserId}_{mobile}";

        /// <summary>
        /// 上次更换手机号的时间，注意子账号的 fxUserId 有一个前缀【sub】
        /// 例：DianGuanJia:FenXiao:ReplaceMobile:Date:sub_1
        /// </summary>
        public const string ReplaceMobileDateKey = "DianGuanJia:FenXiao:ReplaceMobile:Date:{fxUserId}";

        /// <summary>
        /// 开放平台api-ip限流缓存
        /// </summary>
        public const string OpenPlatformApiIpRateLimitCacheKey = "DianGuanJia:FenXiao:OpenPlatform:IpRateLimit:{ip}";

        #endregion
        
         #region 京东供销POP融合

        /// <summary>
        /// 全局启用POP融合逻辑
        /// </summary>
        public const string JingDongPOPFusionEnabledGlobal = "DianGuanJia:FenXiao:JingDongPOPFusion:Enabled:Global";

        /// <summary>
        /// 京东供销平台接口切换到POP接口的时间节点
        /// </summary>
        public const string JingDongPurchaseApiSwitchDate = "DianGuanJia:FenXiao:JingDongPurchaseApi:Switch:Date";
        
        #endregion
        
        /// <summary>
        /// 快手是否开启新的更新最后同步时间键（默认:关闭），二级缓存15分钟
        /// </summary>
        public const string IsEnabledKuaiShouNewUpdateLastSyncTimeKey =
            "DianGuanJia:FenXiao:KuaiShou:IsEnabledNewUpdateLastSyncTime";

        /// <summary>
        /// 快手订单同步并发数键（默认值：1，大于1则开启并发），需要二级缓存
        /// </summary>
        public const string KuaiShouOrderSyncConcurrencyKey = "DianGuanJia:FenXiao:KuaiShou:OrderSyncConcurrency";

        /// <summary>
        /// 快手是否开启新PING键（默认：开启），需要二级缓存
        /// </summary>
        public const string KuaiShouIsEnabledNewPingKey = "DianGuanJia:FenXiao:KuaiShou:IsEnabledNewPing";

        /// <summary>
        /// 配置项仅存储在Redis键列表(数据)，需要二级缓存（15分钟）
        /// </summary>
        public const string CommonSettingOnlyRedisKeysKey =
            "DianGuanJia:FenDan:CommonSetting:OnlyInRedisKeys";

        /// <summary>
        /// 配置项保存在Redis是否永久有效，需要二级缓存（2分钟）
        /// </summary>
        public const string CommonSettingForeverEffectiveRedisKeysKey =
            "DianGuanJia:FenDan:CommonSetting:OnlyInRedisKeys:ForeverEffective";

        /// <summary>
        /// 同步状态历史日志开关
        /// </summary>
        public const string SyncStatusHistoryLogSwitchKey = "DianGuanJia:FenXiao:SyncStatusHistoryLog:Switch";

        /// <summary>
        /// 复制副本和同步复制副本数据日志开关
        /// </summary>
        public const string DataSyncDuplicationLogSwitchKey = "DianGuanJia:FenXiao:DataSyncDuplicationLog:Switch";

        /// <summary>
        /// 复制副本服务日志开关
        /// </summary>
        public const string DataDuplicationLogSwitchKey = "DianGuanJia:FenXiao:DataDuplicationLog:Switch";

        /// <summary>
        /// 冷热异步消息分离日志开关
        /// </summary>
        public const string MessageDataSyncStorageLogSwitchKey = "DianGuanJia:FenXiao:MessageDataSyncStorageLog:Switch";

        /// <summary>
        /// 异常日志开关
        /// </summary>
        public const string ExceptionLogSwitchKey = "DianGuanJia:FenXiao:ExceptionLog:Switch";

        /// <summary>
        /// 发货历史回流记录日志开关
        /// </summary>
        public const string SendHistoryReturnRecordLogSwitchKey =
            "DianGuanJia:FenXiao:SendHistoryReturnRecordLog:Switch";

        /// <summary>
        /// 是否开启执行Dapper清理缓存（默认值：开启）
        /// </summary>
        public const string IsEnabledDapperPurgeCacheSwitchKey = "DianGuanJia:FenXiao:IsEnabledDapperPurgeCache:Switch";

        /// <summary>
        /// Dapper清理缓存记录（暂时每天仅支持一次清理）
        /// </summary>
        public const string DapperPurgeCacheRecordKey =
            "DianGuanJia:FenXiao:DapperPurgeCacheRecord:{HostName}:{ProcessId}";

        /// <summary>
        /// 复制副本服务自动重启锁键
        /// DianGuanJia:FenXiao{Environment}:DataDuplication:{HostName}:AutoRestartLock
        /// </summary>
        public const string DataDuplicationAutoRestartLockKey =
            "DianGuanJia:FenXiao{Environment}:DataDuplication:{HostName}:AutoRestartLock";

        /// <summary>
        /// 是否忽略其他异常PING（默认：开启）
        /// </summary>
        public const string PingIsIgnoreOtherExceptionKey = "DianGuanJia:FenXiao:PingIsIgnoreOtherException:Switch";

        /// <summary>
        /// 头条PING API信息
        /// </summary>
        public const string TouTiaoPingApiInfosKey = "DianGuanJia:FenXiao:TouTiao:PingApiInfos";

        /// <summary>
        /// 服务队列监控日志开关
        /// </summary>
        public const string ServiceQueueMonitorLogSwitch = "DianGuanJia:FenXiao:ServiceQueueMonitorLog:Switch";

        /// <summary>
        /// 服务队列监控心跳时间，秒为单位（默认值：15秒）
        /// </summary>
        public const string ServiceQueueMonitorHeartbeatTimeKey =
            "DianGuanJia:FenXiao:ServiceQueue:MonitorHeartbeatTime";

        /// <summary>
        /// 副本历史日志开关
        /// </summary>
        public const string DataDuplicationStatusHistoryLogSwitch =
            "DianGuanJia:FenXiao:DataDuplicationStatusHistoryLog:Switch";  
            
        /// <summary>
        /// 短视频最后同步时间
        /// </summary>
        public const string ShopVideoLastSyncTime = "DianGuanJia:FenXiao:ShopVideoLastSyncTime:{fxUserId}";

        /// <summary>
        /// 短视频自动同步频率全局限制(分钟)
        /// </summary>
        public const string ShopVideoAutoSyncLimitRateGlobal = "DianGuanJia:FenXiao:ShopVideoAutoSyncLimitRateGlobal";

        /// <summary>
        /// 短视频发布服务的运行间隔（毫秒）
        /// </summary>
        public const string ShopVideoWinServiceRunInterval = "DianGuanJia:FenXiao:ShopVideoWinServiceRunInterval";

        /// <summary>
        /// 短视频店铺是否可挂车的检查结果
        /// </summary>
        public const string ShopVideoShopCanBindResult = "DianGuanJia:FenXiao:ShopVideoShopCanBindResult:{fxUserId}";
        
        #region CommonSetting系统配置，是否使用二级缓存键

        /// <summary>
        /// CommonSetting系统配置，是否使用二级缓存键（默认开启）
        /// </summary>
        public const string IsUseSecondCacheSwitchKeyByCommonSetting = "DianGuanJia:FenXiao:CommonSetting:IsUseSecondCacheSwitch";

        #endregion
        
         #region 防爬虫配置相关

        /// <summary>
        /// 系统配置缓存键
        /// </summary>
        public const string SystemConfigCacheKey = "/System/AntiCrawler/Config";

        /// <summary>
        /// 用户白名单缓存键
        /// </summary>
        public const string UserWhiteListCacheKey = "/System/AntiCrawler/UserWhiteList";

        /// <summary>
        /// Redis键前缀 - 用户每日请求计数，{0}=fxUserId, {1}=date
        /// </summary>
        public const string RedisKeyDailyRequest = "DianGuanJia:FenXiao:AntiCrawler:DailyRequest:{0}:{1}"; 
        
        /// <summary>
        /// Redis键前缀 - 用户等级请求，{0}=fxUserId
        /// </summary>
        public const string RedisKeyLevelRequest = "DianGuanJia:FenXiao:AntiCrawler:Level:{0}"; 

        /// <summary>
        /// Redis键前缀 - 用户每分钟请求计数，{0}=fxUserId，{1}=date
        /// </summary>
        public const string RedisKeyMinuteRequest = "DianGuanJia:FenXiao:AntiCrawler:MinuteRequest:{0}:{1}"; 
        
        /// <summary>
        /// Redis键前缀 - 验证码锁定，{0}=fxUserId, {1}=type
        /// </summary>
        public const string RedisKeyValidCodeLock = "DianGuanJia:FenXiao:AntiCrawler:ValidCodeLock:{0}:{1}"; 
        
        /// <summary>
        /// Redis键前缀 - 用户自定义请求限制，{0}=fxUserId, {1}=date
        /// </summary>
        public const string RedisKeyCustomLimit = "DianGuanJia:FenXiao:AntiCrawler:CustomLimit:{0}:{1}"; 
        
        /// <summary>
        /// Redis键前缀 - 用户是否存在需验证未处理，{0}=fxUserId
        /// </summary>
        public const string RedisKeyValidExist = "DianGuanJia:FenXiao:AntiCrawler:ValidExist:{0}";
        
        /// <summary>
        /// Redis键前缀 - 阿里云验证码，{0}=fxUserId 或 IP
        /// </summary>
        public const string RedisKeyCaptcha = "DianGuanJia:FenXiao:ValidateCode:Captcha:{0}";

        /// <summary>
        /// Redis键 - 被阻止的用户列表
        /// </summary>
        public const string RedisKeyBlocked = "DianGuanJia:FenXiao:AntiCrawler:BlockedUsers";

        #endregion


        #region 订单列表
        /// <summary>
        /// 订单列表最大订单查询记录数
        /// </summary>
        public const string MaxQueryRecordCountKey = "DianGuanJia:FenXiao:OrderList:MaxQueryRecordCount";
        #endregion
        
        /// <summary>
        /// 拆单路径异常日志开关
        /// </summary>
        public const string SplitLogicOrderPathFlowExLogSwitch = "DianGuanJia:FenXiao:SplitLogicOrderPathFlowExLog:Switch";
        
        /// <summary>
        /// 拆单历史日志日志开关
        /// </summary>
        public const string SplitLogicOrderHistoryLogSwitch = "DianGuanJia:FenXiao:SplitLogicOrderHistoryLog:Switch";
    }
}