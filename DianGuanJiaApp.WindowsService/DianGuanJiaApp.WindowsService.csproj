<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{9EFE38BF-56C8-424F-85C5-1BFE1DACAA6F}</ProjectGuid>
    <OutputType>Exe</OutputType>
    <RootNamespace>DianGuanJiaApp.WindowsService</RootNamespace>
    <AssemblyName>DianGuanJiaApp.WindowsService</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="CSRedisCore, Version=3.8.670.0, Culture=neutral, PublicKeyToken=9aa6a3079358d437, processorArchitecture=MSIL">
      <HintPath>..\packages\CSRedisCore.3.8.670\lib\net45\CSRedisCore.dll</HintPath>
    </Reference>
    <Reference Include="Elasticsearch.Net, Version=7.0.0.0, Culture=neutral, PublicKeyToken=96c599bbe3e70f5d, processorArchitecture=MSIL">
      <HintPath>..\packages\Elasticsearch.Net.7.17.5\lib\net461\Elasticsearch.Net.dll</HintPath>
    </Reference>
    <Reference Include="Nest, Version=7.0.0.0, Culture=neutral, PublicKeyToken=96c599bbe3e70f5d, processorArchitecture=MSIL">
      <HintPath>..\packages\NEST.7.17.5\lib\net461\Nest.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Configuration.Install" />
    <Reference Include="System.Core" />
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=9.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.DiagnosticSource.9.0.0\lib\net462\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory, Version=4.0.1.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http, Version=4.1.1.3, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Http.4.3.4\lib\net46\System.Net.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.4.1.2\lib\net462\System.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.InteropServices.RuntimeInformation.4.3.0\lib\net45\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Algorithms.4.3.0\lib\net461\System.Security.Cryptography.Algorithms.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Encoding.4.3.0\lib\net46\System.Security.Cryptography.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Primitives.4.3.0\lib\net46\System.Security.Cryptography.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.X509Certificates.4.3.0\lib\net461\System.Security.Cryptography.X509Certificates.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net461\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="Topshelf, Version=4.3.0.0, Culture=neutral, PublicKeyToken=b800c4cfcdeea87b, processorArchitecture=MSIL">
      <HintPath>..\packages\Topshelf.4.3.0\lib\net452\Topshelf.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Service\AfterSaleFixedTimeSyncService.cs" />
    <Compile Include="Service\AfterSaleIncrementSyncService.cs" />
    <Compile Include="Service\BaseFixedTimeSyncService.cs" />
    <Compile Include="Service\BaseService.cs" />
    <Compile Include="Service\BaseServiceV2.cs" />
    <Compile Include="Service\C2MSyncAfterOrderService.cs" />
    <Compile Include="Service\CompensateBaseProductptRelationsService.cs" />
    <Compile Include="Service\DataDuplicationServiceAutoRestartWinService.cs" />
    <Compile Include="Service\CompensateBaseProductptRelations_ReverseService.cs" />
    <Compile Include="Service\OpenTelemetryOtlpExportWinService.cs" />
    <Compile Include="Service\StatActiveUserCountWinServices.cs" />
    <Compile Include="Service\ServiceQueueMonitorWinService.cs" />
    <Compile Include="Service\ShopVideoPublishService.cs" />
    <Compile Include="Service\StatSendHistoryOrderCountWinServices.cs" />
    <Compile Include="Service\TaskRecoveryService.cs" />
    <Compile Include="Service\StockChangeStatWinService.cs" />
    <Compile Include="Service\CheckShopPushDbAuthWinService.cs" />
    <Compile Include="Service\ClearDataChangeLogWinService.cs" />
    <Compile Include="Service\CompensateCloudMessageBaseService.cs" />
    <Compile Include="Service\ClearDataSyncStatusWinService.cs" />
    <Compile Include="Service\CompensateBaseProductMessageService.cs" />
    <Compile Include="Service\CompensateHotProductCategoryService.cs" />
    <Compile Include="Service\CompensatePurchaseOrderAndSkuMappingService.cs" />
    <Compile Include="Service\ConfigDbDataArchiveWinService.cs" />
    <Compile Include="Service\DataDuplicationCompensateFromSyncStatusPushMessageService.cs" />
    <Compile Include="Service\DataDuplicationPrintHistoryService.cs" />
    <Compile Include="Service\DataDuplicationCompensatePushMessageService.cs" />
    <Compile Include="Service\ImportSettlementPriceWinService.cs" />
    <Compile Include="Service\CompensatePushSendReturnRecordToMessageService.cs" />
    <Compile Include="Service\FixedTimeHotProductService.cs" />
    <Compile Include="Service\HealthMonitorByPlatformWinService.cs" />
    <Compile Include="Service\OrderCheckRuleService.cs" />
    <Compile Include="Service\OnlineSendMessageWinService.cs" />
    <Compile Include="Service\ListingTaskService.cs" />
    <Compile Include="Service\OrderSyncAnalysisAutoReasonWinService.cs" />
    <Compile Include="Service\OrderSyncAnalysisLastSyncTimeUpdateWinService.cs" />
    <Compile Include="Service\OrderSyncAnalysisNoticeWinService.cs" />
    <Compile Include="Service\OrderSyncAnalysisPushTaskWinService.cs" />
    <Compile Include="Service\OrderSyncAnalysisRealTimeByWeiXinWinService.cs" />
    <Compile Include="Service\OrderSyncAnalysisSecondWinService.cs" />
    <Compile Include="Service\OrderSyncAnalysisTriggerSyncWinService.cs" />
    <Compile Include="Service\OrderSyncAnalysisWinService.cs" />
    <Compile Include="Service\PaymentStatementCompensateService.cs" />
    <Compile Include="Service\DataSyncStorageCompensationTaskWinService.cs" />
    <Compile Include="Service\DataSyncStorageConsumerWinService.cs" />
    <Compile Include="Service\DataSyncStorageHealthMonitorWinService.cs" />
    <Compile Include="Service\ProfitOrderSyncService.cs" />
    <Compile Include="Service\PurchaseOrderDataMonitorService.cs" />
    <Compile Include="Service\OrderIncrementSyncByShopService.cs" />
    <Compile Include="Service\PurchaseOrderStatusHandleMessageWinService.cs" />
    <Compile Include="Service\SendHistoryReturnAuthPushTaskWinService.cs" />
    <Compile Include="Service\SendHistoryReturnRecordMonitorService.cs" />
    <Compile Include="Service\DataDuplicationPushMessageService.cs" />
    <Compile Include="Service\BaseWinService.cs" />
    <Compile Include="Service\CompensateBatchStorageWinService.cs" />
    <Compile Include="Service\DataDuplicationCompensationService.cs" />
    <Compile Include="Service\DataDuplicationMonitorService.cs" />
    <Compile Include="Service\DataDuplicationService.cs" />
    <Compile Include="Service\DbIdentityIdMonitorWinService.cs" />
    <Compile Include="Service\FixedTimeSyncService.cs" />
    <Compile Include="Service\InvokeApiDataStorageWinService.cs" />
    <Compile Include="Service\NewBaseService.cs" />
    <Compile Include="Service\PurchaseOrderSendMsgConsumerService.cs" />
    <Compile Include="Service\OrderAbnormalMessageWinService.cs" />
    <Compile Include="Service\OrderIncrementSyncService.cs" />
    <Compile Include="Service\HealthMonitorService.cs" />
    <Compile Include="Service\ProductIncrementSyncService.cs" />
    <Compile Include="Service\SendHistoryCompensateMonitorWinService.cs" />
    <Compile Include="Service\SendHistoryCompensateTaskWinService.cs" />
    <Compile Include="Service\SendHistoryCompensateWinService.cs" />
    <Compile Include="Service\ServiceRunMonitorService.cs" />
    <Compile Include="Service\SiteMessageClearService.cs" />
    <Compile Include="Service\CrossBoderExchangeRateSyncService.cs" />
    <Compile Include="Service\SiteMessageCustomerService.cs" />
    <Compile Include="Service\SyncServiceAutoRestartWinService.cs" />
    <Compile Include="Service\TikTokOrderActiveSyncWinService.cs" />
    <Compile Include="Service\TikTokOrderCanceWinService.cs" />
    <Compile Include="Service\TikTokOrderInvoiceStatusWinService.cs" />
    <Compile Include="ViewModel\ConfigView.cs" />
    <Compile Include="ViewModel\RunLogView.cs" />
    <Compile Include="ViewModel\PurchaseOrderSendLogView.cs" />
    <Compile Include="ViewModel\SiteMessageLogView.cs" />
    <Compile Include="ViewModel\SyncLogView.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
    <None Include="BAT-BaseProduct\install_基础商品补偿关联关系服务.bat" />
    <None Include="BAT-BaseProduct\install_基础商品补偿关联关系服务_反向.bat" />
    <None Include="BAT-BaseProduct\install_补偿推送基础商品消息服务.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT-BaseProduct\uninstall_基础商品补偿关联关系服务.bat" />
    <None Include="BAT-BaseProduct\uninstall_补偿推送基础商品消息服务_反向.bat" />
    <None Include="BAT-ProfitStatistcs\install_按任务同步利润订单服务.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT-ProfitStatistcs\install_补偿推送利润统计消息服务.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT-ProfitStatistcs\uninstall_按任务同步利润订单服务服务.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT-ProfitStatistcs\uninstall_补偿推送利润统计消息服务.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT-ShopVideo\install_定时短视频发布服务.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT-ShopVideo\uninstall_定时短视频发布服务.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT-库存\install_定时更新库存统计服务.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT-库存\uninstall_定时更新库存统计服务.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT-HotProduct\install_定时统计爆品分析数据服务.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT-HotProduct\install_补偿商品类目信息服务.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT-HotProduct\uninstall_定时统计爆品分析数据服务.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT-HotProduct\uninstall_补偿商品类目信息服务.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT1688\install_1688补偿采购单数据及Sku映射数据服务.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT1688\install_1688补偿推送回流触发消息服务.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT-BaseProduct\uninstall_补偿推送基础商品消息服务.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT1688\uninstall_1688补偿采购单数据及Sku映射数据服务.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT1688\uninstall_1688补偿推送回流触发消息服务.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT2\install_1688_purchase_order_status.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT2\install_1688_auth_push_return_task.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT2\install_monitor_platform.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT2\install_monitor_platform_gray.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT2\install_onlinesend_message_01.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT2\stop_all.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT2\start_all.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT2\install_health_monitor.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT2\install_sendhistory_compensate.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT2\install_sendhistory_compensate_task.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT2\install_invoke_api_data_storage.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT2\install_compensate_batch_storage.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT2\uninstall_1688_purchase_order_status.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT2\uninstall_monitor_platform.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT2\uninstall_onlinesend_message_01.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT2\uninstall_all_compensate_storage.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT2\uninstall_1688_auth_push_return_task.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT2\uninstall_all_sendhistory_compensate.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT3备库\install_dataduplication.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT3备库\install_dataduplication_monitor.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT3备库\install_打印记录备用库补偿服务.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT3备库\install_推送补偿触发消息.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT3备库\install_推送触发消息.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT3备库\uninstall_dataduplication.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT3备库\uninstall_dataduplication_monitor.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT3备库\uninstall_打印记录备用库补偿服务.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT3备库\uninstall_推送补偿触发消息.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT3备库\uninstall_推送触发消息.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BATTools\install_订单同步分析二次服务.bat" />
    <None Include="BATTools\install_订单同步分析二次服务_02.bat" />
    <None Include="BATTools\install_订单同步分析更新最后同步时间服务.bat" />
    <None Include="BATTools\install_订单同步分析通知服务.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BATTools\install_订单同步分析服务.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BATTools\install_消息数据同步存储服务_11.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BATTools\install_订单同步分析任务推送服务.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BATTools\install_站内信定期清除.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BATTools\install_站内信.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BATTools\install_消息数据同步存储补偿服务.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BATTools\install_消息数据同步存储健康监控服务.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BATTools\install_消息数据同步存储服务.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BATTools\install_dbidentity_monitor.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BATTools\install_syncservice_autorestart.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BATTools\uninstall_消息数据同步存储服务11.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BATTools\install_站内信v2.bat" />
    <None Include="BATTools\uninstall_站内信定期清除.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BATTools\uninstall_站内信.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BATTools\uninstall_消息数据同步存储补偿服务.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BATTools\uninstall_消息数据同步存储服务.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BATTools\uninstall_dbidentity_monitor.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BATTools\uninstall_站内信v2.bat" />
    <None Include="BAT\install_aftersalesync_gray.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_aftersalesync.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_statactiveusercount.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_queryopentelemetryotlp.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_statsendhistoryordercount.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_dataduplication_05_06.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_dataduplication_07_08.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_abnormalorder_01.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_checkshoppushdbauth.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_定时增量同步售后单.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_monitor_gray.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_ordersync_byshop_11.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_ordersync_byshop_gray.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_ordersync_byshop.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_purchaseordersend.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_ordersync_gray_11.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_ordersync_gray.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_dataduplication_11.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_dataduplication.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_1688采购单数据完整性监控服务.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_货币汇率定时同步服务.bat" />
    <None Include="BAT\install_跨境TikTok主动同步订单服务.bat">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_跨境TikTok发票状态同步服务.bat" />
    <None Include="BAT\install_铺货任务补偿.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_跨境平台按店铺订单同步服务.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_充值退款申请补偿.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_充值退款补偿.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_批量导入结算价触发消息.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_据SyncStatus补偿触发消息.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_推送补偿触发消息.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_回流数据监控.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_充值结果补偿.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_推送触发消息-00-02.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_推送触发消息-03-04.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_cleardatasyncstatus.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_清理变更日志.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_推送触发消息.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_dataduplication_compensate.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_dataduplication_monitor.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_fxiedsync.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_ordersync_11.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_productsync_gray.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_productsync.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_ordersync_09_10.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_ordersync_07_08.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_servicerun_monitor.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_monitor.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_ordersync_05_06.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_ordersync.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\install_订单审核推送服务.bat" />
    <None Include="BAT\uninstall_aftersalesync_gray.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_statactiveusercount.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_statsendhistoryordercount.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_queryopentelemetryotlp.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_dataduplication_05_06.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_dataduplication_07_08.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_abnormalorder_01.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_dataduplication_11.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_dataduplication.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_aftersalesync.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_checkshoppushdbauth.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_定时增量同步售后单.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_ordersync_byshop.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_purchaseordersend.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_跨境TikTok主动同步订单服务.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_跨境TikTok发票状态同步服务.bat" />
    <None Include="BAT\uninstall_跨境TikTok取消订单拆合单服务.bat" />
    <None Include="BAT\uninstall_跨境平台按店铺订单同步服务.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_铺货任务补偿.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_充值退款申请补偿.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_批量导入结算价触发消息.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_据SyncStatus补偿触发消息.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_推送补偿触发消息.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_1688采购单数据完整性监控服务.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_充值结果补偿.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_充值退款补偿.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_推送触发消息-00-02.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_推送触发消息-03-04.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_回流数据监控.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_cleardatasyncstatus.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_清理变更日志.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_推送触发消息.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_dataduplication_compensate.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_dataduplication_monitor.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_fxiedsync.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_monitor_gray.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_ordersync_gray_11.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_ordersync_gray.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_ordersync_11.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_productsync_gray.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_productsync.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_ordersync_09_10.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_ordersync_07_08.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_servicerun_monitor.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_monitor.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_ordersync.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT\uninstall_ordersync_05_06.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT测试\install_dataduplication.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT测试\install_dataduplication_monitor.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT测试\uninstall_dataduplication.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT测试\uninstall_dataduplication_monitor.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT灰度\install_dataduplication.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT灰度\install_dataduplication_monitor.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT灰度\install_跨境TikTok按店铺订单同步服务.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT灰度\uninstall_dataduplication.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT灰度\uninstall_dataduplication_monitor.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="BAT灰度\uninstall_跨境TikTok按店铺订单同步服务.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="Config\AppSettings.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="Config\ConnectionStrings.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\AutoComment\DianGuanJiaApp.RabbitMQ\DianGuanJiaApp.RabbitMQ.csproj">
      <Project>{90dc55be-abba-4686-8119-a18e012f540e}</Project>
      <Name>DianGuanJiaApp.RabbitMQ</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Core\DianGuanJiaApp.Core.csproj">
      <Project>{93CA297D-8704-48D8-8748-67EF45304F88}</Project>
      <Name>DianGuanJiaApp.Core</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Data\DianGuanJiaApp.Data.csproj">
      <Project>{6c8b9657-fbaa-46c8-8433-1b2f908e8398}</Project>
      <Name>DianGuanJiaApp.Data</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Es.Log.Client\DianGuanJiaApp.Es.Log.Client.csproj">
      <Project>{DDF084DD-E7E0-4E17-A13A-33778DE7B431}</Project>
      <Name>DianGuanJiaApp.Es.Log.Client</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Services\DianGuanJiaApp.Services.csproj">
      <Project>{abee0a3a-3b86-4631-add0-3bdd17a0b1fc}</Project>
      <Name>DianGuanJiaApp.Services</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.SiteMessage\DianGuanJiaApp.SiteMessage.csproj">
      <Project>{776bdadb-85e8-4c5b-9b21-3c9235a36a79}</Project>
      <Name>DianGuanJiaApp.SiteMessage</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Trace.Services\DianGuanJiaApp.Trace.Services.csproj">
      <Project>{799EFC5B-87D2-4FC1-A9D5-191FA59D0B02}</Project>
      <Name>DianGuanJiaApp.Trace.Services</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Trace.ViewModels\DianGuanJiaApp.Trace.ViewModels.csproj">
      <Project>{7EB493FB-B565-4C96-81F4-BCDF9393D1B8}</Project>
      <Name>DianGuanJiaApp.Trace.ViewModels</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Utility\DianGuanJiaApp.Utility.csproj">
      <Project>{a0ead1ef-8b94-4487-ad73-f981cb45e58f}</Project>
      <Name>DianGuanJiaApp.Utility</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.ViewModels\DianGuanJiaApp.ViewModels.csproj">
      <Project>{549A33CF-F7FB-49EF-B8BD-1AAE56317663}</Project>
      <Name>DianGuanJiaApp.ViewModels</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Warehouse\DianGuanJiaApp.Warehouse.csproj">
      <Project>{a5ad179b-04e3-44e4-a90c-6260199de89a}</Project>
      <Name>DianGuanJiaApp.Warehouse</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Web\DianGuanJiaApp.Web.csproj">
      <Project>{9EFF6008-AB13-4524-97E5-EFD416E72CE9}</Project>
      <Name>DianGuanJiaApp.Web</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="BATTools\install_service_queue_monitor.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="BATTools\install_service_queue_monitor_gray.bat">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="BATTools\install_订单同步分析即时微信小程序服务.bat" />
    <Content Include="BATTools\install_订单同步分析自动原因分析服务.bat" />
    <Content Include="BATTools\install_订单同步分析触发同步服务.bat" />
    <Content Include="BAT\install_淘工厂每日售后同步服务.bat" />
    <Content Include="BAT\uninstall_淘工厂每日售后同步服务.bat" />
    <Content Include="BAT\uninstall_订单审核推送服务.bat" />
    <Content Include="BAT\install_导出任务获取补偿服务.bat" />
    <Content Include="BAT\uninstall_导出任务获取补偿服务.bat" />
    <Content Include="ILLink\ILLink.Descriptors.LibraryBuild.xml" />
    <Content Include="BAT2\install_configdb_autoarchive.bat" />
    <Content Include="BAT2\uninstall_configdb_autoarchive.bat" />
    <Content Include="安装说明.txt" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.6.1">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.6.1 %28x86 和 x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Connected Services\" />
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>