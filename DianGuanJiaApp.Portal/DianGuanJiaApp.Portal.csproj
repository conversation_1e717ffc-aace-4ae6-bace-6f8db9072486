<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0\build\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props" Condition="Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0\build\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{E4308453-7D50-4B74-B899-B01687363052}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>DianGuanJiaApp.Portal</RootNamespace>
    <AssemblyName>DianGuanJiaApp.Portal</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <MvcBuildViews>false</MvcBuildViews>
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress />
    <IISExpressSSLPort>44305</IISExpressSSLPort>
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="mscorlib" />
    <Reference Include="Newtonsoft.Json, Version=********, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http, Version=4.1.1.3, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Http.4.3.4\lib\net46\System.Net.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Algorithms.4.3.0\lib\net461\System.Security.Cryptography.Algorithms.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Encoding.4.3.0\lib\net46\System.Security.Cryptography.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Primitives.4.3.0\lib\net46\System.Security.Cryptography.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.X509Certificates.4.3.0\lib\net461\System.Security.Cryptography.X509Certificates.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Abstractions" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Routing" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="Microsoft.Web.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.Web.Infrastructure.1.0.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.WebRequest">
    </Reference>
    <Reference Include="System.Web.Helpers, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=5.2.7.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.7\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Optimization">
      <HintPath>..\packages\Microsoft.AspNet.Web.Optimization.1.1.3\lib\net40\System.Web.Optimization.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.7\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="WebGrease">
      <Private>True</Private>
      <HintPath>..\packages\WebGrease.1.6.0\lib\WebGrease.dll</HintPath>
    </Reference>
    <Reference Include="Antlr3.Runtime">
      <Private>True</Private>
      <HintPath>..\packages\Antlr.3.5.0.2\lib\Antlr3.Runtime.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform">
      <HintPath>..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0\lib\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="App_Start\BundleConfig.cs" />
    <Compile Include="App_Start\FilterConfig.cs" />
    <Compile Include="App_Start\RouteConfig.cs" />
    <Compile Include="App_Start\UserAccountLogFilter.cs" />
    <Compile Include="Controllers\BasePortalController.cs" />
    <Compile Include="Controllers\FxQingAccountController.cs" />
    <Compile Include="Controllers\FFAccountController.cs" />
    <Compile Include="Controllers\SubAccountController.cs" />
    <Compile Include="Controllers\MyhomeController.cs" />
    <Compile Include="Controllers\AccountController.cs" />
    <Compile Include="Controllers\ServiceController.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Content\css\accountIndex.css" />
    <Content Include="Content\css\common.css" />
    <Content Include="Content\css\FFaccountIndex.css" />
    <Content Include="Content\css\Myhome.css" />
    <Content Include="Content\css\fxQingAccount\Invite.css" />
    <Content Include="Content\css\fxQingAccount\setBase.css" />
    <Content Include="Content\css\setBase.css" />
    <Content Include="Content\css\toutiao.css" />
    <Content Include="Content\Images\allicons.png" />
    <Content Include="Content\Images\all_parther600.png" />
    <Content Include="Content\Images\ddlogin_03.png" />
    <Content Include="Content\Images\ddlogin_07.png" />
    <Content Include="Content\Images\fd-2023-5-4-8.png" />
    <Content Include="Content\Images\fd-2023-5-4_03.png" />
    <Content Include="Content\Images\fd-2023-5-4_06.png" />
    <Content Include="Content\Images\genavigation.png" />
    <Content Include="Content\Images\headerActiveBackground.png" />
    <Content Include="Content\Images\login.png" />
    <Content Include="Content\Images\icon.png" />
    <Content Include="Content\Images\loginBackground_icon.png" />
    <Content Include="Content\Images\logo.png" />
    <Content Include="Content\Images\logo03.png" />
    <Content Include="Content\Images\newfenfaUI_03.png" />
    <Content Include="Content\Images\newfenfaUI_06.png" />
    <Content Include="Content\Images\newfenfaUI_07.png" />
    <Content Include="Content\Images\newfenfaUI_10.png" />
    <Content Include="Content\Images\newfenfaUI_11.png" />
    <Content Include="Content\Images\orderPic.png" />
    <Content Include="Content\Images\pcLoginBackground.png" />
    <Content Include="Content\Images\pcLoginBackground02.png" />
    <Content Include="Content\Images\produst01.png" />
    <Content Include="Content\Images\produst02.png" />
    <Content Include="Content\Images\produst03.png" />
    <Content Include="Content\Images\produst04.png" />
    <Content Include="Content\Images\puhuo-icon-1.png" />
    <Content Include="Content\Images\remark_icon.png" />
    <Content Include="Content\Images\searchss.png" />
    <Content Include="Content\Images\SRHeader-02.png" />
    <Content Include="Content\Images\SRHeader-03.png" />
    <Content Include="Content\Images\wanwanicon.png" />
    <Content Include="favicon.ico" />
    <Content Include="Global.asax" />
    <Content Include="Scripts\ffaccount\index.js" />
    <Content Include="Scripts\jquery-1.10.2.min.js" />
    <Content Include="Scripts\jquery-1.12.4.min.js" />
    <Content Include="Scripts\jsrender\jsrender.js" />
    <Content Include="Scripts\jsrender\jsrender.min.js" />
    <Content Include="Scripts\layer\layer.js" />
    <Content Include="Scripts\layer\mobile\layer.js" />
    <Content Include="Scripts\layer\mobile\need\layer.css" />
    <Content Include="Scripts\layer\theme\default\icon-ext.png" />
    <Content Include="Scripts\layer\theme\default\icon.png" />
    <Content Include="Scripts\layer\theme\default\layer.css" />
    <Content Include="Scripts\layer\theme\default\loading-0.gif" />
    <Content Include="Scripts\layer\theme\default\loading-1.gif" />
    <Content Include="Scripts\layer\theme\default\loading-2.gif" />
    <Content Include="Scripts\layui\css\layui.css" />
    <Content Include="Scripts\layui\css\layui.mobile.css" />
    <Content Include="Scripts\layui\css\modules\code.css" />
    <Content Include="Scripts\layui\css\modules\laydate\default\laydate.css" />
    <Content Include="Scripts\layui\css\modules\layer\default\icon-ext.png" />
    <Content Include="Scripts\layui\css\modules\layer\default\icon.png" />
    <Content Include="Scripts\layui\css\modules\layer\default\layer.css" />
    <Content Include="Scripts\layui\css\modules\layer\default\loading-0.gif" />
    <Content Include="Scripts\layui\css\modules\layer\default\loading-1.gif" />
    <Content Include="Scripts\layui\css\modules\layer\default\loading-2.gif" />
    <Content Include="Scripts\layui\font\iconfont.svg" />
    <Content Include="Scripts\layui\images\face\0.gif" />
    <Content Include="Scripts\layui\images\face\1.gif" />
    <Content Include="Scripts\layui\images\face\10.gif" />
    <Content Include="Scripts\layui\images\face\11.gif" />
    <Content Include="Scripts\layui\images\face\12.gif" />
    <Content Include="Scripts\layui\images\face\13.gif" />
    <Content Include="Scripts\layui\images\face\14.gif" />
    <Content Include="Scripts\layui\images\face\15.gif" />
    <Content Include="Scripts\layui\images\face\16.gif" />
    <Content Include="Scripts\layui\images\face\17.gif" />
    <Content Include="Scripts\layui\images\face\18.gif" />
    <Content Include="Scripts\layui\images\face\19.gif" />
    <Content Include="Scripts\layui\images\face\2.gif" />
    <Content Include="Scripts\layui\images\face\20.gif" />
    <Content Include="Scripts\layui\images\face\21.gif" />
    <Content Include="Scripts\layui\images\face\22.gif" />
    <Content Include="Scripts\layui\images\face\23.gif" />
    <Content Include="Scripts\layui\images\face\24.gif" />
    <Content Include="Scripts\layui\images\face\25.gif" />
    <Content Include="Scripts\layui\images\face\26.gif" />
    <Content Include="Scripts\layui\images\face\27.gif" />
    <Content Include="Scripts\layui\images\face\28.gif" />
    <Content Include="Scripts\layui\images\face\29.gif" />
    <Content Include="Scripts\layui\images\face\3.gif" />
    <Content Include="Scripts\layui\images\face\30.gif" />
    <Content Include="Scripts\layui\images\face\31.gif" />
    <Content Include="Scripts\layui\images\face\32.gif" />
    <Content Include="Scripts\layui\images\face\33.gif" />
    <Content Include="Scripts\layui\images\face\34.gif" />
    <Content Include="Scripts\layui\images\face\35.gif" />
    <Content Include="Scripts\layui\images\face\36.gif" />
    <Content Include="Scripts\layui\images\face\37.gif" />
    <Content Include="Scripts\layui\images\face\38.gif" />
    <Content Include="Scripts\layui\images\face\39.gif" />
    <Content Include="Scripts\layui\images\face\4.gif" />
    <Content Include="Scripts\layui\images\face\40.gif" />
    <Content Include="Scripts\layui\images\face\41.gif" />
    <Content Include="Scripts\layui\images\face\42.gif" />
    <Content Include="Scripts\layui\images\face\43.gif" />
    <Content Include="Scripts\layui\images\face\44.gif" />
    <Content Include="Scripts\layui\images\face\45.gif" />
    <Content Include="Scripts\layui\images\face\46.gif" />
    <Content Include="Scripts\layui\images\face\47.gif" />
    <Content Include="Scripts\layui\images\face\48.gif" />
    <Content Include="Scripts\layui\images\face\49.gif" />
    <Content Include="Scripts\layui\images\face\5.gif" />
    <Content Include="Scripts\layui\images\face\50.gif" />
    <Content Include="Scripts\layui\images\face\51.gif" />
    <Content Include="Scripts\layui\images\face\52.gif" />
    <Content Include="Scripts\layui\images\face\53.gif" />
    <Content Include="Scripts\layui\images\face\54.gif" />
    <Content Include="Scripts\layui\images\face\55.gif" />
    <Content Include="Scripts\layui\images\face\56.gif" />
    <Content Include="Scripts\layui\images\face\57.gif" />
    <Content Include="Scripts\layui\images\face\58.gif" />
    <Content Include="Scripts\layui\images\face\59.gif" />
    <Content Include="Scripts\layui\images\face\6.gif" />
    <Content Include="Scripts\layui\images\face\60.gif" />
    <Content Include="Scripts\layui\images\face\61.gif" />
    <Content Include="Scripts\layui\images\face\62.gif" />
    <Content Include="Scripts\layui\images\face\63.gif" />
    <Content Include="Scripts\layui\images\face\64.gif" />
    <Content Include="Scripts\layui\images\face\65.gif" />
    <Content Include="Scripts\layui\images\face\66.gif" />
    <Content Include="Scripts\layui\images\face\67.gif" />
    <Content Include="Scripts\layui\images\face\68.gif" />
    <Content Include="Scripts\layui\images\face\69.gif" />
    <Content Include="Scripts\layui\images\face\7.gif" />
    <Content Include="Scripts\layui\images\face\70.gif" />
    <Content Include="Scripts\layui\images\face\71.gif" />
    <Content Include="Scripts\layui\images\face\8.gif" />
    <Content Include="Scripts\layui\images\face\9.gif" />
    <Content Include="Scripts\layui\layui.all.js" />
    <Content Include="Scripts\layui\layui.js" />
    <Content Include="Scripts\layui\lay\modules\carousel.js" />
    <Content Include="Scripts\layui\lay\modules\code.js" />
    <Content Include="Scripts\layui\lay\modules\colorpicker.js" />
    <Content Include="Scripts\layui\lay\modules\element.js" />
    <Content Include="Scripts\layui\lay\modules\flow.js" />
    <Content Include="Scripts\layui\lay\modules\form.js" />
    <Content Include="Scripts\layui\lay\modules\jquery.js" />
    <Content Include="Scripts\layui\lay\modules\laydate.js" />
    <Content Include="Scripts\layui\lay\modules\layedit.js" />
    <Content Include="Scripts\layui\lay\modules\layer.js" />
    <Content Include="Scripts\layui\lay\modules\laypage.js" />
    <Content Include="Scripts\layui\lay\modules\laytpl.js" />
    <Content Include="Scripts\layui\lay\modules\mobile.js" />
    <Content Include="Scripts\layui\lay\modules\rate.js" />
    <Content Include="Scripts\layui\lay\modules\slider.js" />
    <Content Include="Scripts\layui\lay\modules\table.js" />
    <Content Include="Scripts\layui\lay\modules\tree.js" />
    <Content Include="Scripts\layui\lay\modules\upload.js" />
    <Content Include="Scripts\layui\lay\modules\util.js" />
    <Content Include="Scripts\myHomeCommon.js" />
    <Content Include="Scripts\Scripts\base64.js" />
    <Content Include="Scripts\Scripts\bootstrap.js" />
    <Content Include="Scripts\Scripts\bootstrap.min.js" />
    <None Include="Scripts\Scripts\jquery-1.10.2.intellisense.js" />
    <Content Include="Scripts\Scripts\crypto-js.js" />
    <Content Include="Scripts\Scripts\jquery-1.10.2.js" />
    <Content Include="Scripts\Scripts\jquery-1.10.2.min.js" />
    <Content Include="Scripts\Scripts\jsencrypt.min.js" />
    <Content Include="Scripts\Scripts\modernizr-2.6.2.js" />
    <Content Include="Scripts\wuku.js" />
    <Content Include="Web.config" />
    <Content Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Views\Web.config" />
    <Content Include="Views\_ViewStart.cshtml" />
    <Content Include="Views\Shared\Error.cshtml" />
    <Content Include="Views\Shared\_Layout.cshtml" />
    <Content Include="Views\Account\Index.cshtml" />
    <Content Include="Views\Myhome\TouTiao.cshtml" />
    <Content Include="Views\SubAccount\Index.cshtml" />
    <Content Include="Views\Account\Login_wd.cshtml" />
    <Content Include="Views\Shared\_EmptyLayout.cshtml" />
    <Content Include="Views\FFAccount\Index.cshtml" />
    <Content Include="Views\Service\FxMessage.cshtml" />
    <Content Include="Views\FFAccount\ScanQRCodeBindUser.cshtml" />
    <Content Include="Views\FxQingAccount\MobilePage.cshtml" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <None Include="packages.config" />
    <Content Include="Views\Shared\500.cshtml" />
    <None Include="Properties\PublishProfiles\Portal.pubxml" />
    <Content Include="Scripts\layui\font\iconfont.eot" />
    <Content Include="Scripts\layui\font\iconfont.ttf" />
    <Content Include="Scripts\layui\font\iconfont.woff" />
    <Content Include="Views\Shared\_LayoutCom.cshtml" />
    <Content Include="Views\Myhome\Indexs.cshtml" />
    <Content Include="Views\Myhome\Index.cshtml" />
    <Content Include="Views\Service\Index.cshtml" />
    <Content Include="Views\Service\Message.cshtml" />
    <Content Include="Scripts\Scripts\jquery-1.10.2.min.map" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\DianGuanJiaApp.Data\DianGuanJiaApp.Data.csproj">
      <Project>{6c8b9657-fbaa-46c8-8433-1b2f908e8398}</Project>
      <Name>DianGuanJiaApp.Data</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Services\DianGuanJiaApp.Services.csproj">
      <Project>{abee0a3a-3b86-4631-add0-3bdd17a0b1fc}</Project>
      <Name>DianGuanJiaApp.Services</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Utility\DianGuanJiaApp.Utility.csproj">
      <Project>{a0ead1ef-8b94-4487-ad73-f981cb45e58f}</Project>
      <Name>DianGuanJiaApp.Utility</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Web\DianGuanJiaApp.Web.csproj">
      <Project>{9eff6008-ab13-4524-97e5-efd416e72ce9}</Project>
      <Name>DianGuanJiaApp.Web</Name>
    </ProjectReference>
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <Target Name="MvcBuildViews" AfterTargets="AfterBuild" Condition="'$(MvcBuildViews)'=='true'">
    <AspNetCompiler VirtualPath="temp" PhysicalPath="$(WebProjectOutputDir)" />
  </Target>
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>3372</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:44306/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>这台计算机上缺少此项目引用的 NuGet 程序包。使用“NuGet 程序包还原”可下载这些程序包。有关更多信息，请参见 http://go.microsoft.com/fwlink/?LinkID=322105。缺少的文件是 {0}。</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0\build\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0\build\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target> -->
</Project>